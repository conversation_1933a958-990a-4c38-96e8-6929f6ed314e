/**
 * Training Monitor Hook for ClassyWeb ML Platform Phase 3
 * 
 * This hook provides WebSocket-based real-time training monitoring
 * with comprehensive progress tracking and metrics collection.
 */

import { useState, useEffect, useRef, useCallback } from 'react';
import { getWebSocketUrl } from '../services/classificationEngineService';

// Types and Interfaces
export interface TrainingProgress {
  session_id: string;
  current_epoch: number;
  total_epochs: number;
  current_step: number;
  total_steps: number;
  progress_percentage: number;
  stage: TrainingStage;
  estimated_time_remaining?: number;
  elapsed_time: number;
}

export interface TrainingMetrics {
  loss: number;
  accuracy?: number;
  precision?: number;
  recall?: number;
  f1_score?: number;
  validation_loss?: number;
  validation_accuracy?: number;
  learning_rate?: number;
  batch_size?: number;
  epoch_time?: number;
}

export interface SystemMetrics {
  cpu_percent: number;
  memory_mb: number;
  memory_percent: number;
  gpu_utilization?: number;
  gpu_memory_used?: number;
  gpu_memory_total?: number;
  temperature?: number;
  timestamp: string;
}

export interface TrainingEvent {
  type: TrainingEventType;
  timestamp: string;
  data: any;
  message?: string;
}

export enum TrainingStage {
  INITIALIZATION = 'initialization',
  DATA_LOADING = 'data_loading',
  PREPROCESSING = 'preprocessing',
  TRAINING = 'training',
  VALIDATION = 'validation',
  OPTIMIZATION = 'optimization',
  EVALUATION = 'evaluation',
  COMPLETED = 'completed',
  FAILED = 'failed',
  STOPPED = 'stopped'
}

export enum TrainingEventType {
  PROGRESS_UPDATE = 'progress_update',
  METRICS_UPDATE = 'metrics_update',
  SYSTEM_METRICS = 'system_metrics',
  STAGE_CHANGE = 'stage_change',
  ERROR = 'error',
  WARNING = 'warning',
  INFO = 'info',
  CHECKPOINT_SAVED = 'checkpoint_saved',
  EARLY_STOPPING = 'early_stopping',
  HYPERPARAMETER_UPDATE = 'hyperparameter_update'
}

export interface UseTrainingMonitorOptions {
  autoConnect?: boolean;
  reconnectAttempts?: number;
  reconnectInterval?: number;
  heartbeatInterval?: number;
  onConnect?: () => void;
  onDisconnect?: () => void;
  onError?: (error: Error) => void;
  onProgress?: (progress: TrainingProgress) => void;
  onMetrics?: (metrics: TrainingMetrics) => void;
  onSystemMetrics?: (metrics: SystemMetrics) => void;
  onEvent?: (event: TrainingEvent) => void;
}

export interface UseTrainingMonitorReturn {
  // Connection state
  isConnected: boolean;
  isConnecting: boolean;
  error: Error | null;
  
  // Training data
  progress: TrainingProgress | null;
  metrics: TrainingMetrics | null;
  systemMetrics: SystemMetrics | null;
  events: TrainingEvent[];
  
  // Control functions
  connect: () => void;
  disconnect: () => void;
  sendMessage: (message: any) => void;
  clearEvents: () => void;
  
  // Computed values
  isTraining: boolean;
  isCompleted: boolean;
  isFailed: boolean;
  progressPercentage: number;
  estimatedTimeRemaining: number | null;
}

/**
 * Hook for monitoring training progress via WebSocket
 */
export const useTrainingMonitor = (
  sessionId: string,
  options: UseTrainingMonitorOptions = {}
): UseTrainingMonitorReturn => {
  const {
    autoConnect = true,
    reconnectAttempts = 5,
    reconnectInterval = 3000,
    heartbeatInterval = 30000,
    onConnect,
    onDisconnect,
    onError,
    onProgress,
    onMetrics,
    onSystemMetrics,
    onEvent
  } = options;

  // State
  const [isConnected, setIsConnected] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [progress, setProgress] = useState<TrainingProgress | null>(null);
  const [metrics, setMetrics] = useState<TrainingMetrics | null>(null);
  const [systemMetrics, setSystemMetrics] = useState<SystemMetrics | null>(null);
  const [events, setEvents] = useState<TrainingEvent[]>([]);

  // Refs
  const wsRef = useRef<WebSocket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const heartbeatIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const reconnectAttemptsRef = useRef(0);
  const mountedRef = useRef(true);

  // Computed values
  const isTraining = progress?.stage === TrainingStage.TRAINING;
  const isCompleted = progress?.stage === TrainingStage.COMPLETED;
  const isFailed = progress?.stage === TrainingStage.FAILED;
  const progressPercentage = progress?.progress_percentage || 0;
  const estimatedTimeRemaining = progress?.estimated_time_remaining || null;

  // Clear reconnect timeout
  const clearReconnectTimeout = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }
  }, []);

  // Clear heartbeat interval
  const clearHeartbeatInterval = useCallback(() => {
    if (heartbeatIntervalRef.current) {
      clearInterval(heartbeatIntervalRef.current);
      heartbeatIntervalRef.current = null;
    }
  }, []);

  // Send heartbeat
  const sendHeartbeat = useCallback(() => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      wsRef.current.send(JSON.stringify({
        type: 'ping',
        timestamp: Date.now()
      }));
    }
  }, []);

  // Send message
  const sendMessage = useCallback((message: any) => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      wsRef.current.send(JSON.stringify(message));
    } else {
      console.warn('WebSocket is not connected. Message not sent:', message);
    }
  }, []);

  // Handle WebSocket message
  const handleMessage = useCallback((event: MessageEvent) => {
    if (!mountedRef.current) return;

    try {
      const data = JSON.parse(event.data);
      const trainingEvent: TrainingEvent = {
        type: data.type,
        timestamp: data.timestamp || new Date().toISOString(),
        data: data.data || data,
        message: data.message
      };

      // Add event to history
      setEvents(prev => [...prev.slice(-99), trainingEvent]); // Keep last 100 events

      // Handle specific event types
      switch (data.type) {
        case TrainingEventType.PROGRESS_UPDATE:
          const newProgress = data.data as TrainingProgress;
          setProgress(newProgress);
          onProgress?.(newProgress);
          break;

        case TrainingEventType.METRICS_UPDATE:
          const newMetrics = data.data as TrainingMetrics;
          setMetrics(newMetrics);
          onMetrics?.(newMetrics);
          break;

        case TrainingEventType.SYSTEM_METRICS:
          const newSystemMetrics = data.data as SystemMetrics;
          setSystemMetrics(newSystemMetrics);
          onSystemMetrics?.(newSystemMetrics);
          break;

        case TrainingEventType.ERROR:
          const errorMsg = data.message || 'Training error occurred';
          const trainingError = new Error(errorMsg);
          setError(trainingError);
          onError?.(trainingError);
          break;

        case TrainingEventType.STAGE_CHANGE:
          if (progress) {
            setProgress(prev => prev ? { ...prev, stage: data.data.stage } : null);
          }
          break;

        default:
          // Handle other event types
          break;
      }

      onEvent?.(trainingEvent);
    } catch (err) {
      console.error('Failed to parse WebSocket message:', err);
    }
  }, [progress, onProgress, onMetrics, onSystemMetrics, onError, onEvent]);

  // Connect to WebSocket
  const connect = useCallback(() => {
    if (!sessionId || wsRef.current?.readyState === WebSocket.OPEN || isConnecting) {
      return;
    }

    setIsConnecting(true);
    setError(null);
    clearReconnectTimeout();

    try {
      const wsUrl = getWebSocketUrl(sessionId);
      wsRef.current = new WebSocket(wsUrl);

      wsRef.current.onopen = () => {
        if (!mountedRef.current) return;
        
        setIsConnected(true);
        setIsConnecting(false);
        setError(null);
        reconnectAttemptsRef.current = 0;

        // Start heartbeat
        clearHeartbeatInterval();
        heartbeatIntervalRef.current = setInterval(sendHeartbeat, heartbeatInterval);

        console.log('Training monitor WebSocket connected:', sessionId);
        onConnect?.();
      };

      wsRef.current.onmessage = handleMessage;

      wsRef.current.onclose = (event) => {
        if (!mountedRef.current) return;
        
        setIsConnected(false);
        setIsConnecting(false);
        clearHeartbeatInterval();

        console.log('Training monitor WebSocket closed:', event.code, event.reason);
        onDisconnect?.();

        // Attempt reconnection if not a clean close and we haven't exceeded attempts
        if (event.code !== 1000 && reconnectAttemptsRef.current < reconnectAttempts) {
          reconnectAttemptsRef.current++;
          console.log(`Attempting to reconnect (${reconnectAttemptsRef.current}/${reconnectAttempts})...`);
          
          reconnectTimeoutRef.current = setTimeout(() => {
            if (mountedRef.current) {
              connect();
            }
          }, reconnectInterval);
        }
      };

      wsRef.current.onerror = (event) => {
        if (!mountedRef.current) return;
        
        const wsError = new Error('WebSocket connection error');
        setError(wsError);
        setIsConnecting(false);
        
        console.error('Training monitor WebSocket error:', event);
        onError?.(wsError);
      };

    } catch (err) {
      setIsConnecting(false);
      const connectError = err instanceof Error ? err : new Error('Failed to connect to WebSocket');
      setError(connectError);
      onError?.(connectError);
    }
  }, [sessionId, isConnecting, reconnectAttempts, reconnectInterval, heartbeatInterval, 
      handleMessage, sendHeartbeat, clearReconnectTimeout, clearHeartbeatInterval, 
      onConnect, onDisconnect, onError]);

  // Disconnect from WebSocket
  const disconnect = useCallback(() => {
    clearReconnectTimeout();
    clearHeartbeatInterval();
    
    if (wsRef.current) {
      wsRef.current.close(1000, 'Manual disconnect');
      wsRef.current = null;
    }
    
    setIsConnected(false);
    setIsConnecting(false);
  }, [clearReconnectTimeout, clearHeartbeatInterval]);

  // Clear events
  const clearEvents = useCallback(() => {
    setEvents([]);
  }, []);

  // Auto-connect on mount
  useEffect(() => {
    if (autoConnect && sessionId) {
      connect();
    }

    return () => {
      mountedRef.current = false;
      disconnect();
    };
  }, [sessionId, autoConnect, connect, disconnect]);

  return {
    // Connection state
    isConnected,
    isConnecting,
    error,
    
    // Training data
    progress,
    metrics,
    systemMetrics,
    events,
    
    // Control functions
    connect,
    disconnect,
    sendMessage,
    clearEvents,
    
    // Computed values
    isTraining,
    isCompleted,
    isFailed,
    progressPercentage,
    estimatedTimeRemaining
  };
};
