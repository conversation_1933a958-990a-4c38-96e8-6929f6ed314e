import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import {
  Lightbulb,
  CheckCircle2,
  ArrowRight,
  Target,
  Brain,
  Zap,
  Settings,
  BarChart3,
  Users,
  Sparkles,
  Database,
  Upload,
  X
} from "lucide-react";
import { SmartDataAnalysis, getWorkflowRecommendations } from "@/services/dataAnalysisApi";
import { LLMConfigurationPanel, LLMConfiguration } from "./LLMConfigurationPanel";
import { UnifiedFileUploadZone } from "./UnifiedFileUploadZone";
import { UploadedFile, FileUploadProgress, uploadFile } from "@/services/fileUploadApi";
import { unifiedDataManager, DataPurpose } from "@/services/unifiedDataManager";
import { useToast } from "@/hooks/use-toast";

interface EnhancedRecommendationEngineProps {
  analysis: SmartDataAnalysis;
  onRecommendationSelect: (recommendation: any) => void;
  onCustomize?: () => void;
  initialTextColumns?: string[];
  initialLabelColumns?: string[];
  onConfigurationStatusChange?: (isComplete: boolean) => void;
}

export const EnhancedRecommendationEngine = ({
  analysis,
  onRecommendationSelect,
  onCustomize,
  initialTextColumns = [],
  initialLabelColumns = [],
  onConfigurationStatusChange
}: EnhancedRecommendationEngineProps) => {
  const { toast } = useToast();
  // Map detected structure to classification type
  const getInitialClassificationType = () => {
    // First try to get from suggestions
    const suggestionType = analysis.suggestions[0]?.suggested_config?.classification_type;
    if (suggestionType) return suggestionType;

    // Otherwise map from detected structure
    switch (analysis.detected_structure) {
      case 'hierarchical':
        return 'hierarchical';
      case 'flat':
        return 'multi-class'; // Default for flat structures
      case 'mixed':
        return 'multi-label'; // Mixed structures often need multi-label
      default:
        return 'multi-class'; // Safe default
    }
  };

  const [selectedClassificationType, setSelectedClassificationType] = useState<string>(
    getInitialClassificationType()
  );
  const [selectedMethod, setSelectedMethod] = useState<'custom' | 'llm'>('custom');
  const [showLLMConfig, setShowLLMConfig] = useState(false);
  const [llmConfig, setLLMConfig] = useState<LLMConfiguration | null>(null);
  const [isConfigurationComplete, setIsConfigurationComplete] = useState(false);
  const [workflowRecommendation, setWorkflowRecommendation] = useState<{
    recommended_workflow: string;
    reasoning: string;
    configuration_hints: string[];
  } | null>(null);
  const [isLoadingRecommendations, setIsLoadingRecommendations] = useState(true);

  // Data configuration state
  const [selectedTextColumns, setSelectedTextColumns] = useState<string[]>(() => {
    // Use initial values from workflow if available
    if (initialTextColumns.length > 0) {
      return initialTextColumns;
    }
    // Try to get from analysis preview first, then fallback to detected text columns
    if (analysis.preview.text_column) {
      return [analysis.preview.text_column];
    }
    // Fallback to first detected text column
    const textColumns = Object.keys(analysis.column_analysis).filter(
      col => analysis.column_analysis[col].is_potential_text
    );
    return textColumns.length > 0 ? [textColumns[0]] : [];
  });

  const [selectedLabelColumns, setSelectedLabelColumns] = useState<string[]>(() => {
    // Use initial values from workflow if available
    if (initialLabelColumns.length > 0) {
      return initialLabelColumns;
    }
    // Try to get from analysis preview first, then fallback to detected label columns
    if (analysis.preview.label_columns && analysis.preview.label_columns.length > 0) {
      return analysis.preview.label_columns;
    }
    // Fallback to detected label columns
    const labelColumns = Object.keys(analysis.column_analysis).filter(
      col => analysis.column_analysis[col].is_potential_label
    );
    return labelColumns;
  });

  // Training data upload state for custom training
  const [trainingFile, setTrainingFile] = useState<File | null>(null);
  const [trainingFileInfo, setTrainingFileInfo] = useState<UploadedFile | null>(null);
  const [isUploadingTraining, setIsUploadingTraining] = useState(false);
  const [trainingUploadProgress, setTrainingUploadProgress] = useState(0);
  const [trainingUploadError, setTrainingUploadError] = useState<string | null>(null);

  // Fetch workflow recommendations from backend
  useEffect(() => {
    const fetchWorkflowRecommendations = async () => {
      try {
        setIsLoadingRecommendations(true);
        const recommendations = await getWorkflowRecommendations(analysis);
        setWorkflowRecommendation(recommendations);

        // Update selected method based on recommendation
        if (recommendations.recommended_workflow === 'LLM') {
          setSelectedMethod('llm');
        } else {
          setSelectedMethod('custom');
        }

        // Don't auto-select recommendation - let user explicitly choose
      } catch (error) {
        console.error('Failed to get workflow recommendations:', error);
        toast({
          title: "Recommendation Error",
          description: "Failed to get AI recommendations. Using default settings.",
          variant: "destructive"
        });
      } finally {
        setIsLoadingRecommendations(false);
      }
    };

    fetchWorkflowRecommendations();
  }, [analysis, toast]);

  // Notify parent of configuration status changes
  useEffect(() => {
    if (onConfigurationStatusChange) {
      onConfigurationStatusChange(isConfigurationComplete);
    }
  }, [isConfigurationComplete, onConfigurationStatusChange]);

  // Note: Recommendation updates are handled in handleAcceptRecommendation
  // to avoid automatic step advancement

  const getClassificationTypeInfo = (type: string) => {
    switch (type) {
      case 'binary':
        return {
          title: 'Binary Classification',
          description: 'Perfect for yes/no, positive/negative, or two-category problems',
          icon: Target,
          color: 'ml-primary',
          examples: ['Spam detection', 'Sentiment analysis', 'Medical diagnosis']
        };
      case 'multi-class':
        return {
          title: 'Multi-class Classification',
          description: 'Ideal for categorizing into multiple distinct categories',
          icon: BarChart3,
          color: 'ml-secondary',
          examples: ['Topic classification', 'Product categorization', 'Language detection']
        };
      case 'multi-label':
        return {
          title: 'Multi-label Classification',
          description: 'When items can belong to multiple categories simultaneously',
          icon: Settings,
          color: 'ml-accent',
          examples: ['Tag assignment', 'Genre classification', 'Skill categorization']
        };
      case 'hierarchical':
        return {
          title: 'Hierarchical Classification',
          description: 'For nested category structures with parent-child relationships',
          icon: Brain,
          color: 'ml-primary',
          examples: ['Product taxonomy', 'Academic subjects', 'Geographic classification']
        };
      default:
        return {
          title: 'Flat Classification',
          description: 'Standard classification for simple category structures',
          icon: Target,
          color: 'ml-secondary',
          examples: ['Basic categorization', 'Simple labeling', 'Standard classification']
        };
    }
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return 'text-ml-success';
    if (confidence >= 0.6) return 'text-ml-warning';
    return 'text-ml-error';
  };

  const getConfidenceLabel = (confidence: number) => {
    if (confidence >= 0.8) return 'High Confidence';
    if (confidence >= 0.6) return 'Medium Confidence';
    return 'Low Confidence';
  };

  // Training data upload handlers
  const handleTrainingFileUpload = async (file: File) => {
    setTrainingFile(file);
    setTrainingUploadError(null);
    setIsUploadingTraining(true);
    setTrainingUploadProgress(0);

    try {
      const uploadedFileInfo = await uploadFile(file, (progress: FileUploadProgress) => {
        setTrainingUploadProgress(progress.percentage);
      });

      setTrainingFileInfo(uploadedFileInfo);
      setIsUploadingTraining(false);

      toast({
        title: "Training data uploaded successfully",
        description: `${uploadedFileInfo.filename} (${uploadedFileInfo.num_rows.toLocaleString()} rows) is ready for training`,
      });
    } catch (error: any) {
      setTrainingUploadError(error.message || 'Failed to upload training file');
      setIsUploadingTraining(false);

      toast({
        title: "Training data upload failed",
        description: error.message || 'Failed to upload training file. Please try again.',
        variant: "destructive",
      });
    }
  };

  const handleTrainingFileRemove = () => {
    setTrainingFile(null);
    setTrainingFileInfo(null);
    setTrainingUploadError(null);
    setTrainingUploadProgress(0);

    toast({
      title: "Training data removed",
      description: "You can upload a new training file",
    });
  };

  // Column management helpers
  const availableColumns = Object.keys(analysis.column_analysis);

  const addTextColumn = (column: string) => {
    if (!selectedTextColumns.includes(column)) {
      setSelectedTextColumns([...selectedTextColumns, column]);
    }
  };

  const removeTextColumn = (column: string) => {
    setSelectedTextColumns(selectedTextColumns.filter(col => col !== column));
  };

  const addLabelColumn = (column: string) => {
    if (!selectedLabelColumns.includes(column)) {
      setSelectedLabelColumns([...selectedLabelColumns, column]);
    }
  };

  const removeLabelColumn = (column: string) => {
    setSelectedLabelColumns(selectedLabelColumns.filter(col => col !== column));
  };

  const getColumnPriority = (col: string, isText: boolean) => {
    const columnInfo = analysis.column_analysis[col];
    if (isText && columnInfo.is_potential_text) return 0;
    if (!isText && columnInfo.is_potential_label) return 0;
    return 1;
  };

  const handleClassificationTypeChange = (type: string) => {
    setSelectedClassificationType(type);
    // Reset method selection when type changes
    setSelectedMethod('custom');
    setShowLLMConfig(false);
    setIsConfigurationComplete(false);
  };

  const handleMethodChange = (method: 'custom' | 'llm') => {
    setSelectedMethod(method);
    if (method === 'llm') {
      // Only show LLM config if it hasn't been configured yet
      if (!llmConfig || !llmConfig.provider || !llmConfig.model) {
        setShowLLMConfig(true);
        setIsConfigurationComplete(false);
      } else {
        // LLM is already configured
        setIsConfigurationComplete(true);
      }
    } else {
      setShowLLMConfig(false);
      // Custom training is ready immediately
      setIsConfigurationComplete(true);
    }
  };

  const handleLLMConfigurationChange = (config: LLMConfiguration) => {
    setLLMConfig(config);
  };

  const handleApplyLLMConfig = (config: LLMConfiguration) => {
    setLLMConfig(config);
    setShowLLMConfig(false);
    setIsConfigurationComplete(true);

    // Don't create recommendation here - let user explicitly accept it
    // This ensures all column selections are properly included
    toast({
      title: "LLM Configuration Saved",
      description: `${config.provider} ${config.model} configured. Click "Accept Recommendation" to proceed.`,
    });
  };

  const handleAcceptRecommendation = () => {
    // Validate data configuration
    if (selectedTextColumns.length === 0) {
      toast({
        title: "Missing text columns",
        description: "Please select at least one text column for classification",
        variant: "destructive"
      });
      return;
    }

    if (selectedLabelColumns.length === 0) {
      toast({
        title: "Missing label columns",
        description: "Please select at least one label column for classification",
        variant: "destructive"
      });
      return;
    }

    // For custom training, require training data upload
    if (selectedMethod === 'custom' && !trainingFileInfo) {
      toast({
        title: "Missing training data",
        description: "Please upload training data for custom model training",
        variant: "destructive"
      });
      return;
    }

    // For LLM inference, validate LLM configuration
    if (selectedMethod === 'llm' && (!llmConfig || !llmConfig.provider || !llmConfig.model)) {
      toast({
        title: "Missing LLM configuration",
        description: "Please configure LLM settings for inference",
        variant: "destructive"
      });
      return;
    }

    const currentSuggestion = analysis.suggestions.find(
      s => s.suggested_config?.classification_type === selectedClassificationType
    ) || analysis.suggestions[0];

    // Create recommendation with current configuration
    const recommendation = {
      suggested_config: {
        classification_type: selectedClassificationType,
        approach: selectedMethod === 'llm' ? 'llm_inference' : 'custom_training',
        text_column: selectedTextColumns[0],
        label_columns: selectedLabelColumns,
        training_file_id: trainingFileInfo?.file_id,
        llm_config: llmConfig,
        hierarchy_levels: analysis.preview.label_columns // For hierarchical classification
      },
      confidence: analysis.confidence,
      reasoning: workflowRecommendation?.reasoning || `${selectedMethod === 'llm' ? 'LLM' : 'Custom'} ${selectedClassificationType} classification configured`
    };

    onRecommendationSelect(recommendation);
  };

  const currentClassificationInfo = getClassificationTypeInfo(selectedClassificationType);
  const currentSuggestion = analysis.suggestions.find(
    s => s.suggested_config?.classification_type === selectedClassificationType
  ) || analysis.suggestions[0];
  const Icon = currentClassificationInfo.icon;

  // Show loading state while fetching recommendations
  if (isLoadingRecommendations) {
    return (
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 rounded-lg bg-ml-accent/10 flex items-center justify-center">
                <Lightbulb className="w-5 h-5 text-ml-accent animate-pulse" />
              </div>
              <div>
                <CardTitle>AI Recommendations</CardTitle>
                <CardDescription>
                  Analyzing your data to generate personalized recommendations...
                </CardDescription>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-ml-accent"></div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 rounded-lg bg-ml-accent/10 flex items-center justify-center">
              <Lightbulb className="w-5 h-5 text-ml-accent" />
            </div>
            <div>
              <CardTitle>AI Recommendations</CardTitle>
              <CardDescription>
                Configure your classification approach based on our analysis
              </CardDescription>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Main Configuration */}
      <Card className="border-2 border-ml-secondary/20 bg-gradient-to-br from-ml-secondary/5 to-transparent">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className={`w-12 h-12 rounded-lg bg-${currentClassificationInfo.color}/10 flex items-center justify-center`}>
                <Icon className={`w-6 h-6 text-${currentClassificationInfo.color}`} />
              </div>
              <div>
                <CardTitle className="flex items-center gap-2">
                  {currentClassificationInfo.title}
                  {selectedClassificationType === analysis.suggestions[0]?.suggested_config?.classification_type && (
                    <Badge className="bg-ml-success/10 text-ml-success">Recommended</Badge>
                  )}
                </CardTitle>
                <CardDescription>
                  {workflowRecommendation?.reasoning || currentClassificationInfo.description}
                </CardDescription>
              </div>
            </div>
            <div className="text-right">
              <div className={`text-sm font-medium ${getConfidenceColor(analysis.confidence)}`}>
                {getConfidenceLabel(analysis.confidence)}
              </div>
              <div className="text-xs text-muted-foreground">
                {Math.round(analysis.confidence * 100)}% match
              </div>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="configure" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="configure">Configure</TabsTrigger>
              <TabsTrigger value="alternatives">Alternatives</TabsTrigger>
            </TabsList>
            
            <TabsContent value="configure" className="space-y-6 mt-6">
              {/* Classification Type Selection */}
              <div className="space-y-3">
                <h4 className="font-semibold flex items-center gap-2">
                  <Target className="w-4 h-4 text-primary" />
                  Classification Type
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                  {['binary', 'multi-class', 'multi-label', 'hierarchical', 'flat'].map((type) => {
                    const typeInfo = getClassificationTypeInfo(type);
                    const TypeIcon = typeInfo.icon;
                    const isSelected = selectedClassificationType === type;
                    const isRecommended = type === analysis.suggestions[0]?.suggested_config?.classification_type;
                    
                    return (
                      <Card
                        key={type}
                        className={`cursor-pointer transition-all ${
                          isSelected ? 'border-2 border-primary bg-primary/5' : 'hover:border-primary/20'
                        }`}
                        onClick={() => handleClassificationTypeChange(type)}
                      >
                        <CardContent className="p-3">
                          <div className="flex items-center gap-2">
                            <TypeIcon className={`w-4 h-4 text-${typeInfo.color}`} />
                            <div className="flex-1">
                              <h5 className="font-medium text-xs">{typeInfo.title}</h5>
                              {isRecommended && (
                                <Badge className="bg-ml-success/10 text-ml-success text-xs">Recommended</Badge>
                              )}
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    );
                  })}
                </div>
              </div>

              {/* AI Recommendations */}
              {workflowRecommendation && workflowRecommendation.configuration_hints.length > 0 && (
                <div className="space-y-3">
                  <h4 className="font-semibold flex items-center gap-2">
                    <Sparkles className="w-4 h-4 text-ml-accent" />
                    AI Recommendations
                  </h4>
                  <div className="bg-ml-accent/5 border border-ml-accent/20 rounded-lg p-4">
                    <ul className="space-y-2">
                      {workflowRecommendation.configuration_hints.map((hint, index) => (
                        <li key={index} className="flex items-start gap-2 text-sm">
                          <CheckCircle2 className="w-4 h-4 text-ml-accent mt-0.5 flex-shrink-0" />
                          <span>{hint}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              )}

              {/* Training Method Selection */}
              <div className="space-y-3">
                <h4 className="font-semibold flex items-center gap-2">
                  <Zap className="w-4 h-4 text-primary" />
                  Training Method
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Card
                    className={`cursor-pointer transition-all ${
                      selectedMethod === 'custom' ? 'border-2 border-primary bg-primary/5' : 'hover:border-primary/20'
                    }`}
                    onClick={() => handleMethodChange('custom')}
                  >
                    <CardContent className="p-4">
                      <div className="flex items-center gap-3">
                        <Brain className="w-6 h-6 text-ml-primary" />
                        <div>
                          <h5 className="font-medium">Custom Training</h5>
                          <p className="text-xs text-muted-foreground">Train specialized model</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card
                    className={`cursor-pointer transition-all ${
                      selectedMethod === 'llm' ? 'border-2 border-primary bg-primary/5' : 'hover:border-primary/20'
                    }`}
                    onClick={() => handleMethodChange('llm')}
                  >
                    <CardContent className="p-4">
                      <div className="flex items-center gap-3">
                        <Sparkles className="w-6 h-6 text-ml-accent" />
                        <div>
                          <h5 className="font-medium">LLM Inference</h5>
                          <p className="text-xs text-muted-foreground">Use language models</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                {/* LLM Configuration Status */}
                {selectedMethod === 'llm' && llmConfig && !showLLMConfig && (
                  <div className="mt-4 p-4 bg-ml-success/10 border border-ml-success/20 rounded-lg">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <CheckCircle2 className="w-4 h-4 text-ml-success" />
                        <span className="text-sm font-medium">LLM Configured</span>
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setShowLLMConfig(true)}
                      >
                        <Settings className="w-4 h-4 mr-2" />
                        Reconfigure
                      </Button>
                    </div>
                    <p className="text-xs text-muted-foreground mt-1">
                      {llmConfig.provider} - {llmConfig.model}
                    </p>
                  </div>
                )}
              </div>

              {/* Data Configuration */}
              <div className="space-y-3">
                <h4 className="font-semibold flex items-center gap-2">
                  <Database className="w-4 h-4 text-primary" />
                  Data Configuration
                </h4>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* Text Columns */}
                  <div className="space-y-2">
                    <Label htmlFor="text-columns">Text Columns</Label>

                    {/* Selected text columns */}
                    {selectedTextColumns.length > 0 && (
                      <div className="flex flex-wrap gap-2 mb-2">
                        {selectedTextColumns.map(col => (
                          <Badge key={col} variant="secondary" className="flex items-center gap-1">
                            {col}
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-4 w-4 p-0 hover:bg-destructive hover:text-destructive-foreground"
                              onClick={() => removeTextColumn(col)}
                            >
                              <X className="h-3 w-3" />
                            </Button>
                          </Badge>
                        ))}
                      </div>
                    )}

                    {/* Add text column dropdown */}
                    <Select value="" onValueChange={addTextColumn}>
                      <SelectTrigger>
                        <SelectValue placeholder="Add text column" />
                      </SelectTrigger>
                      <SelectContent>
                        {availableColumns
                          .filter(col => !selectedTextColumns.includes(col))
                          .sort((a, b) => getColumnPriority(a, true) - getColumnPriority(b, true))
                          .map(col => {
                            const columnInfo = analysis.column_analysis[col];
                            const isRecommended = columnInfo.is_potential_text;

                            return (
                              <SelectItem key={col} value={col}>
                                <div className="flex items-center gap-2">
                                  <span>{col}</span>
                                  {isRecommended && (
                                    <Badge variant="default" className="text-xs bg-ml-primary/10 text-ml-primary">
                                      Recommended
                                    </Badge>
                                  )}
                                  <Badge variant="secondary" className="text-xs">
                                    {columnInfo.unique_count} unique
                                  </Badge>
                                </div>
                              </SelectItem>
                            );
                          })}
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Label Columns */}
                  <div className="space-y-2">
                    <Label htmlFor="label-columns">Label Columns</Label>

                    {/* Selected label columns */}
                    {selectedLabelColumns.length > 0 && (
                      <div className="flex flex-wrap gap-2 mb-2">
                        {selectedLabelColumns.map(col => (
                          <Badge key={col} variant="secondary" className="flex items-center gap-1">
                            {col}
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-4 w-4 p-0 hover:bg-destructive hover:text-destructive-foreground"
                              onClick={() => removeLabelColumn(col)}
                            >
                              <X className="h-3 w-3" />
                            </Button>
                          </Badge>
                        ))}
                      </div>
                    )}

                    {/* Add label column dropdown */}
                    <Select value="" onValueChange={addLabelColumn}>
                      <SelectTrigger>
                        <SelectValue placeholder="Add label column" />
                      </SelectTrigger>
                      <SelectContent>
                        {availableColumns
                          .filter(col => !selectedLabelColumns.includes(col))
                          .sort((a, b) => getColumnPriority(a, false) - getColumnPriority(b, false))
                          .map(col => {
                            const columnInfo = analysis.column_analysis[col];
                            const isRecommended = columnInfo.is_potential_label;

                            return (
                              <SelectItem key={col} value={col}>
                                <div className="flex items-center gap-2">
                                  <span>{col}</span>
                                  {isRecommended && (
                                    <Badge variant="default" className="text-xs bg-ml-primary/10 text-ml-primary">
                                      Recommended
                                    </Badge>
                                  )}
                                  <Badge variant="secondary" className="text-xs">
                                    {columnInfo.unique_count} classes
                                  </Badge>
                                </div>
                              </SelectItem>
                            );
                          })}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>

              {/* Custom Training Data Upload */}
              {selectedMethod === 'custom' && (
                <div className="space-y-3">
                  <h4 className="font-semibold flex items-center gap-2">
                    <Upload className="w-4 h-4 text-primary" />
                    Training Data
                  </h4>

                  <div className="p-4 bg-muted/50 rounded-lg">
                    <h5 className="font-semibold mb-2">Training Data Requirements:</h5>
                    <ul className="text-sm text-muted-foreground space-y-1">
                      <li>• Must contain both text and labels</li>
                      <li>• Should have the same structure as your sample data</li>
                      <li>• Larger datasets typically produce better models</li>
                      <li>• You can reuse your sample file if it contains all your training data</li>
                    </ul>
                  </div>

                  <UnifiedFileUploadZone
                    onFileSelected={(fileId, fileInfo, purposes) => {
                      // Handle training file selection
                      setTrainingFileInfo(fileInfo);
                      setTrainingFile(null); // Clear legacy state
                      setIsUploadingTraining(false);
                      setTrainingUploadError(null);

                      // Update purposes to include training
                      if (!purposes.includes('training')) {
                        unifiedDataManager.updatePurposes(fileId, [...purposes, 'training']);
                      }
                    }}
                    onFileRemoved={() => {
                      handleTrainingFileRemove();
                    }}
                    requiredPurposes={['training']}
                    suggestedPurposes={['training', 'analysis']}
                    allowMultiplePurposes={true}
                    showFileReuse={true}
                    title="Upload Training Data"
                    description="Upload your complete training dataset (can be the same as sample data)"
                    showContinueButton={false}
                  />

                  {trainingFileInfo && (
                    <div className="p-3 bg-ml-success/10 border border-ml-success/20 rounded-lg">
                      <div className="flex items-center gap-2">
                        <CheckCircle2 className="w-4 h-4 text-ml-success" />
                        <span className="text-sm font-medium">Training data ready</span>
                      </div>
                      <p className="text-xs text-muted-foreground mt-1">
                        {trainingFileInfo.filename} ({trainingFileInfo.num_rows.toLocaleString()} rows)
                      </p>
                    </div>
                  )}
                </div>
              )}

              {/* LLM Configuration Panel */}
              {showLLMConfig && (
                <LLMConfigurationPanel
                  onConfigurationChange={handleLLMConfigurationChange}
                  onApply={handleApplyLLMConfig}
                  onCancel={() => setShowLLMConfig(false)}
                  classificationType={selectedClassificationType}
                  initialConfig={llmConfig || undefined}
                />
              )}

              {/* Action Buttons */}
              {!showLLMConfig && (
                <div className="flex gap-3 pt-4 border-t">
                  <Button
                    className="flex-1 bg-ml-secondary hover:bg-ml-secondary-dark"
                    size="lg"
                    onClick={handleAcceptRecommendation}
                    disabled={
                      (selectedMethod === 'llm' && !llmConfig) ||
                      (selectedMethod === 'custom' && !trainingFileInfo) ||
                      selectedTextColumns.length === 0 ||
                      selectedLabelColumns.length === 0
                    }
                  >
                    <CheckCircle2 className="w-4 h-4 mr-2" />
                    {selectedMethod === 'llm' ? 'Start LLM Classification' : 'Start Custom Training'}
                    <ArrowRight className="w-4 h-4 ml-2" />
                  </Button>
                  {onCustomize && selectedMethod === 'custom' && (
                    <Button
                      variant="outline"
                      size="lg"
                      onClick={onCustomize}
                    >
                      <Settings className="w-4 h-4 mr-2" />
                      Advanced Settings
                    </Button>
                  )}
                </div>
              )}
            </TabsContent>
            
            <TabsContent value="alternatives" className="space-y-4 mt-6">
              {/* Alternative Classification Types */}
              <div className="space-y-3">
                {analysis.suggestions.map((suggestion, index) => {
                  const altInfo = getClassificationTypeInfo(suggestion.suggested_config?.classification_type || 'flat');
                  const AltIcon = altInfo.icon;
                  const isSelected = selectedClassificationType === suggestion.suggested_config?.classification_type;

                  return (
                    <div 
                      key={`suggestion-${index}`} 
                      className={`flex items-center justify-between p-3 border rounded-lg transition-colors cursor-pointer ${
                        isSelected ? 'border-primary bg-primary/5' : 'hover:bg-muted/50'
                      }`}
                      onClick={() => handleClassificationTypeChange(suggestion.suggested_config?.classification_type || 'multi-class')}
                    >
                      <div className="flex items-center gap-3">
                        <div className={`w-8 h-8 rounded-lg bg-${altInfo.color}/10 flex items-center justify-center`}>
                          <AltIcon className={`w-4 h-4 text-${altInfo.color}`} />
                        </div>
                        <div>
                          <h4 className="font-medium text-sm">{altInfo.title}</h4>
                          <p className="text-xs text-muted-foreground">{Math.round(suggestion.confidence * 100)}% confidence</p>
                        </div>
                      </div>
                      {isSelected && (
                        <CheckCircle2 className="w-4 h-4 text-primary" />
                      )}
                    </div>
                  );
                })}

                {/* Show remaining classification types */}
                {(() => {
                  const allTypes = ['binary', 'multi-class', 'multi-label', 'hierarchical', 'flat'];
                  const suggestedTypes = analysis.suggestions.map(s => s.suggested_config?.classification_type);
                  const remainingTypes = allTypes.filter(type => !suggestedTypes.includes(type));

                  return remainingTypes.map((type) => {
                    const altInfo = getClassificationTypeInfo(type);
                    const AltIcon = altInfo.icon;
                    const isSelected = selectedClassificationType === type;

                    return (
                      <div 
                        key={`type-${type}`} 
                        className={`flex items-center justify-between p-3 border rounded-lg transition-colors cursor-pointer ${
                          isSelected ? 'border-primary bg-primary/5' : 'hover:bg-muted/50'
                        }`}
                        onClick={() => handleClassificationTypeChange(type)}
                      >
                        <div className="flex items-center gap-3">
                          <div className={`w-8 h-8 rounded-lg bg-${altInfo.color}/10 flex items-center justify-center`}>
                            <AltIcon className={`w-4 h-4 text-${altInfo.color}`} />
                          </div>
                          <div>
                            <h4 className="font-medium text-sm">{altInfo.title}</h4>
                            <p className="text-xs text-muted-foreground">Manual selection</p>
                          </div>
                        </div>
                        {isSelected && (
                          <CheckCircle2 className="w-4 h-4 text-primary" />
                        )}
                      </div>
                    );
                  });
                })()}
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Data Insights */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Data Analysis Summary</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-3 bg-muted/30 rounded-lg">
              <div className="text-lg font-bold text-primary">
                {analysis.preview.total_rows.toLocaleString()}
              </div>
              <div className="text-xs text-muted-foreground">Samples</div>
            </div>
            <div className="text-center p-3 bg-muted/30 rounded-lg">
              <div className="text-lg font-bold text-ml-secondary">
                {analysis.preview.label_columns.length}
              </div>
              <div className="text-xs text-muted-foreground">Label Columns</div>
            </div>
            <div className="text-center p-3 bg-muted/30 rounded-lg">
              <div className="text-lg font-bold text-ml-success">
                {Math.round((1 - analysis.data_quality.missing_data_percentage / 100) * 100)}%
              </div>
              <div className="text-xs text-muted-foreground">Data Quality</div>
            </div>
          </div>

          {/* Why This Recommendation */}
          <div className="mt-4 p-4 bg-muted/50 rounded-lg">
            <h4 className="font-semibold mb-2 flex items-center gap-2">
              <Brain className="w-4 h-4 text-primary" />
              Why we recommend this
            </h4>
            <p className="text-sm text-muted-foreground">
              {currentSuggestion?.reasoning || 'This classification type best matches your data structure and patterns.'}
            </p>
          </div>

          {/* Example Use Cases */}
          <div className="mt-4 p-4 bg-muted/50 rounded-lg">
            <h4 className="font-semibold mb-2 flex items-center gap-2">
              <Users className="w-4 h-4 text-ml-accent" />
              Common use cases
            </h4>
            <ul className="text-sm text-muted-foreground space-y-1">
              {currentClassificationInfo.examples.map((example, index) => (
                <li key={index}>• {example}</li>
              ))}
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
