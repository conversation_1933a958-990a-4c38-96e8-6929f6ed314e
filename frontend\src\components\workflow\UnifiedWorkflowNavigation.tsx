/**
 * UnifiedWorkflowNavigation.tsx
 * 
 * Unified navigation system for all ClassyWeb workflows with consistent UX
 * Implements Phase 4 requirements for consistent step navigation and progress indicators
 */

import React from 'react';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { 
  ChevronLeft, 
  ChevronRight, 
  CheckCircle2, 
  Circle, 
  AlertCircle,
  Clock,
  HelpCircle,
  Keyboard
} from 'lucide-react';

export interface WorkflowStep {
  id: number;
  title: string;
  description: string;
  icon: React.ComponentType<any>;
  status: 'pending' | 'current' | 'complete' | 'error';
  isOptional?: boolean;
  estimatedTime?: string;
  helpText?: string;
}

export interface UnifiedWorkflowNavigationProps {
  steps: WorkflowStep[];
  currentStep: number;
  onStepChange: (step: number) => void;
  onNext?: () => void;
  onPrevious?: () => void;
  onSkip?: () => void;
  canNavigateToStep?: (step: number) => boolean;
  isStepComplete?: (step: number) => boolean;
  showProgress?: boolean;
  showStepList?: boolean;
  showKeyboardHints?: boolean;
  nextLabel?: string;
  previousLabel?: string;
  nextDisabled?: boolean;
  previousDisabled?: boolean;
  className?: string;
  // Enhanced guidance props
  showEstimatedTime?: boolean;
  showHelpTooltips?: boolean;
  compactMode?: boolean;
}

export const UnifiedWorkflowNavigation: React.FC<UnifiedWorkflowNavigationProps> = ({
  steps,
  currentStep,
  onStepChange,
  onNext,
  onPrevious,
  onSkip,
  canNavigateToStep,
  isStepComplete,
  showProgress = true,
  showStepList = true,
  showKeyboardHints = false,
  nextLabel = "Next Step",
  previousLabel = "Previous Step",
  nextDisabled = false,
  previousDisabled = false,
  className = "",
  showEstimatedTime = true,
  showHelpTooltips = true,
  compactMode = false
}) => {
  const currentStepData = steps.find(step => step.id === currentStep);
  const totalSteps = steps.length;
  const completedSteps = steps.filter(step => step.status === 'complete').length;
  const progressPercentage = (completedSteps / totalSteps) * 100;

  const defaultCanNavigateToStep = (stepId: number) => {
    // Can navigate to completed steps, current step, or next step if current is complete
    const currentStepComplete = currentStepData?.status === 'complete';
    return stepId <= currentStep || (stepId === currentStep + 1 && currentStepComplete);
  };

  const defaultIsStepComplete = (stepId: number) => {
    const step = steps.find(s => s.id === stepId);
    return step?.status === 'complete';
  };

  const canNavigateToStepFunc = canNavigateToStep || defaultCanNavigateToStep;
  const isStepCompleteFunc = isStepComplete || defaultIsStepComplete;

  const handleStepClick = (stepId: number) => {
    if (canNavigateToStepFunc(stepId)) {
      onStepChange(stepId);
    }
  };

  const getStepIcon = (step: WorkflowStep) => {
    switch (step.status) {
      case 'complete':
        return <CheckCircle2 className="w-4 h-4 text-green-600" />;
      case 'current':
        return <Circle className="w-4 h-4 text-blue-600 fill-current" />;
      case 'error':
        return <AlertCircle className="w-4 h-4 text-red-600" />;
      default:
        return <Circle className="w-4 h-4 text-gray-400" />;
    }
  };

  const getStepStatusColor = (step: WorkflowStep) => {
    switch (step.status) {
      case 'complete':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'current':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'error':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-600 border-gray-200';
    }
  };

  return (
    <TooltipProvider>
      <div className={`space-y-6 ${className}`}>
        {/* Progress Bar */}
        {showProgress && (
          <div className="space-y-2">
            <div className="flex justify-between items-center text-sm">
              <span className="text-muted-foreground">
                Step {currentStep} of {totalSteps}
              </span>
              <span className="text-muted-foreground">
                {Math.round(progressPercentage)}% Complete
              </span>
            </div>
            <Progress value={progressPercentage} className="h-2" />
          </div>
        )}

        {/* Current Step Info */}
        {currentStepData && (
          <Card className="border-l-4 border-l-blue-500">
            <CardContent className="p-4">
              <div className="flex items-start gap-3">
                <div className="mt-1">
                  {React.createElement(currentStepData.icon, { className: "w-5 h-5 text-blue-600" })}
                </div>
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-1">
                    <h3 className="font-semibold">{currentStepData.title}</h3>
                    {currentStepData.isOptional && (
                      <Badge variant="outline" className="text-xs">Optional</Badge>
                    )}
                    {showEstimatedTime && currentStepData.estimatedTime && (
                      <div className="flex items-center gap-1 text-xs text-muted-foreground">
                        <Clock className="w-3 h-3" />
                        {currentStepData.estimatedTime}
                      </div>
                    )}
                    {showHelpTooltips && currentStepData.helpText && (
                      <Tooltip>
                        <TooltipTrigger>
                          <HelpCircle className="w-4 h-4 text-muted-foreground hover:text-foreground" />
                        </TooltipTrigger>
                        <TooltipContent>
                          <p className="max-w-xs">{currentStepData.helpText}</p>
                        </TooltipContent>
                      </Tooltip>
                    )}
                  </div>
                  <p className="text-sm text-muted-foreground">{currentStepData.description}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Step List */}
        {showStepList && !compactMode && (
          <div className="space-y-2">
            {steps.map((step, index) => (
              <div
                key={step.id}
                className={`flex items-center gap-3 p-3 rounded-lg border cursor-pointer transition-colors ${
                  canNavigateToStepFunc(step.id) 
                    ? 'hover:bg-muted/50' 
                    : 'cursor-not-allowed opacity-60'
                } ${getStepStatusColor(step)}`}
                onClick={() => handleStepClick(step.id)}
              >
                <div className="flex items-center gap-2">
                  <span className="text-xs font-medium w-6 text-center">
                    {step.id}
                  </span>
                  {getStepIcon(step)}
                </div>
                <div className="flex-1">
                  <div className="flex items-center gap-2">
                    <span className="font-medium text-sm">{step.title}</span>
                    {step.isOptional && (
                      <Badge variant="outline" className="text-xs">Optional</Badge>
                    )}
                  </div>
                  <p className="text-xs text-muted-foreground">{step.description}</p>
                </div>
                {showEstimatedTime && step.estimatedTime && (
                  <div className="flex items-center gap-1 text-xs text-muted-foreground">
                    <Clock className="w-3 h-3" />
                    {step.estimatedTime}
                  </div>
                )}
              </div>
            ))}
          </div>
        )}

        {/* Compact Step List */}
        {showStepList && compactMode && (
          <div className="flex items-center gap-2 overflow-x-auto pb-2">
            {steps.map((step, index) => (
              <Tooltip key={step.id}>
                <TooltipTrigger>
                  <div
                    className={`flex items-center gap-2 px-3 py-2 rounded-lg border cursor-pointer transition-colors min-w-fit ${
                      canNavigateToStepFunc(step.id) 
                        ? 'hover:bg-muted/50' 
                        : 'cursor-not-allowed opacity-60'
                    } ${getStepStatusColor(step)}`}
                    onClick={() => handleStepClick(step.id)}
                  >
                    <span className="text-xs font-medium">{step.id}</span>
                    {getStepIcon(step)}
                    <span className="text-sm font-medium">{step.title}</span>
                  </div>
                </TooltipTrigger>
                <TooltipContent>
                  <div className="space-y-1">
                    <p className="font-medium">{step.title}</p>
                    <p className="text-xs">{step.description}</p>
                    {step.estimatedTime && (
                      <p className="text-xs text-muted-foreground">
                        Estimated time: {step.estimatedTime}
                      </p>
                    )}
                  </div>
                </TooltipContent>
              </Tooltip>
            ))}
          </div>
        )}

        {/* Navigation Controls */}
        <div className="flex justify-between items-center pt-4 border-t">
          <div className="flex gap-2">
            {currentStep > 1 && (
              <Button
                variant="outline"
                onClick={onPrevious}
                disabled={previousDisabled}
                className="flex items-center gap-2"
              >
                <ChevronLeft className="w-4 h-4" />
                {previousLabel}
              </Button>
            )}
          </div>

          <div className="flex items-center gap-2">
            {showKeyboardHints && (
              <Tooltip>
                <TooltipTrigger>
                  <div className="flex items-center gap-1 text-xs text-muted-foreground">
                    <Keyboard className="w-3 h-3" />
                    <span>←→</span>
                  </div>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Use arrow keys to navigate between steps</p>
                </TooltipContent>
              </Tooltip>
            )}

            {currentStepData?.isOptional && onSkip && (
              <Button variant="ghost" onClick={onSkip} className="text-sm">
                Skip Step
              </Button>
            )}

            {currentStep < totalSteps && (
              <Button
                onClick={onNext}
                disabled={nextDisabled}
                className="flex items-center gap-2"
              >
                {nextLabel}
                <ChevronRight className="w-4 h-4" />
              </Button>
            )}
          </div>
        </div>
      </div>
    </TooltipProvider>
  );
};
