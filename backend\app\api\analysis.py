"""Analysis API endpoints for ClassyWeb Universal Platform."""

import logging
from typing import Dict, List, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Body
from sqlalchemy.orm import Session
from pydantic import BaseModel

from ..database import get_db
from ..auth import get_current_user, get_optional_current_user
from ..models.auth import User
from ..services.data_analysis_service import DataStructureDetector

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/analysis", tags=["analysis"])

# Initialize data structure detector
data_structure_detector = DataStructureDetector()


# --- Request/Response Models ---

class FileStructureRequest(BaseModel):
    """Request model for file structure analysis."""
    file_id: str
    sample_size: Optional[int] = 1000

class WorkflowRecommendationRequest(BaseModel):
    """Request model for workflow recommendations."""
    analysis: Dict[str, Any]


class WorkflowRecommendationResponse(BaseModel):
    """Response model for workflow recommendations."""
    recommended_workflow: str  # 'LLM', 'HF', 'NonHierarchical'
    reasoning: str
    configuration_hints: List[str]


# --- Analysis Endpoints ---

@router.post("/file-structure")
async def analyze_file_structure(
    request: FileStructureRequest,
    db: Session = Depends(get_db),
    current_user: Optional[User] = Depends(get_optional_current_user)
):
    """Analyze file structure and detect classification patterns."""
    try:
        # Import here to avoid circular imports
        from ..database import get_file

        # Get file from database
        file_record = get_file(db, request.file_id)
        if not file_record:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="File not found"
            )

        # Analyze file structure
        analysis = data_structure_detector.analyze_file_structure(
            file_record.file_path,
            request.sample_size
        )

        return analysis

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error analyzing file structure: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to analyze file structure"
        )

@router.post("/workflow-recommendations", response_model=WorkflowRecommendationResponse)
async def get_workflow_recommendations(
    request: WorkflowRecommendationRequest,
    db: Session = Depends(get_db),
    current_user: Optional[User] = Depends(get_optional_current_user)
):
    """Get workflow recommendations based on data analysis."""
    try:
        analysis = request.analysis
        
        # Extract key characteristics from analysis
        detected_structure = analysis.get('detected_structure', 'unclear')
        confidence = analysis.get('confidence', 0.0)
        data_quality = analysis.get('data_quality', {})
        column_analysis = analysis.get('column_analysis', {})
        
        # Get data size and complexity metrics
        total_rows = data_quality.get('total_rows', 0)
        total_columns = data_quality.get('total_columns', 0)
        
        # Count potential label columns
        label_columns = [
            col for col, info in column_analysis.items() 
            if info.get('is_potential_label', False)
        ]
        
        # Determine recommended workflow
        recommended_workflow = 'LLM'  # Default
        reasoning = ""
        configuration_hints = []
        
        # Decision logic based on data characteristics
        if detected_structure == 'hierarchical':
            if total_rows > 1000 and confidence > 0.8:
                recommended_workflow = 'HF'
                reasoning = f"Large hierarchical dataset ({total_rows:,} rows) with high confidence ({confidence:.1%}) is ideal for custom model training with Hugging Face transformers."
                configuration_hints = [
                    "Use hierarchical classification engine",
                    "Consider multi-level training approach",
                    "Enable hierarchy-aware loss functions"
                ]
            else:
                recommended_workflow = 'LLM'
                reasoning = f"Hierarchical structure detected but dataset size ({total_rows:,} rows) or confidence ({confidence:.1%}) suggests LLM inference for better results."
                configuration_hints = [
                    "Use hierarchical prompting strategy",
                    "Enable chain-of-thought reasoning",
                    "Consider few-shot examples"
                ]
        
        elif detected_structure == 'flat':
            num_labels = sum(1 for col, info in column_analysis.items() if info.get('is_potential_label', False))
            
            if total_rows > 500 and num_labels > 2:
                recommended_workflow = 'HF'
                reasoning = f"Multi-class flat structure with {num_labels} label columns and {total_rows:,} rows is suitable for custom model training."
                configuration_hints = [
                    "Use multi-class or multi-label classification",
                    "Consider class balancing techniques",
                    "Enable cross-validation for robust evaluation"
                ]
            else:
                recommended_workflow = 'LLM'
                reasoning = f"Flat structure with moderate complexity is well-suited for LLM inference."
                configuration_hints = [
                    "Use clear classification prompts",
                    "Enable confidence scoring",
                    "Consider batch processing for efficiency"
                ]
        
        elif detected_structure == 'mixed':
            recommended_workflow = 'LLM'
            reasoning = "Mixed structure detected - LLM inference provides flexibility to handle complex patterns."
            configuration_hints = [
                "Use adaptive prompting strategies",
                "Enable multi-step reasoning",
                "Consider ensemble approaches"
            ]
        
        else:  # unclear
            if total_rows > 1000:
                recommended_workflow = 'HF'
                reasoning = "Large dataset with unclear structure - custom training can learn patterns automatically."
                configuration_hints = [
                    "Start with simple classification approach",
                    "Use automated feature engineering",
                    "Enable extensive validation"
                ]
            else:
                recommended_workflow = 'LLM'
                reasoning = "Unclear structure with moderate size - LLM inference provides interpretable results."
                configuration_hints = [
                    "Use exploratory prompting",
                    "Enable detailed explanations",
                    "Consider iterative refinement"
                ]
        
        # Add general hints based on data quality
        missing_data_pct = data_quality.get('missing_data_percentage', 0)
        if missing_data_pct > 10:
            configuration_hints.append(f"Handle missing data ({missing_data_pct:.1f}% missing)")
        
        duplicate_rows = data_quality.get('duplicate_rows', 0)
        if duplicate_rows > 0:
            configuration_hints.append(f"Consider deduplication ({duplicate_rows} duplicate rows)")
        
        return WorkflowRecommendationResponse(
            recommended_workflow=recommended_workflow,
            reasoning=reasoning,
            configuration_hints=configuration_hints
        )
        
    except Exception as e:
        logger.error(f"Error generating workflow recommendations: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate workflow recommendations: {str(e)}"
        )


@router.post("/data-insights")
async def get_data_insights(
    request: Dict[str, Any] = Body(...),
    db: Session = Depends(get_db),
    current_user: Optional[User] = Depends(get_optional_current_user)
):
    """Get additional data insights for advanced users."""
    try:
        analysis = request.get('analysis', {})
        
        # Generate insights based on analysis
        insights = {
            "complexity_score": _calculate_complexity_score(analysis),
            "recommended_preprocessing": _get_preprocessing_recommendations(analysis),
            "performance_estimates": _estimate_performance(analysis),
            "resource_requirements": _estimate_resources(analysis)
        }
        
        return insights
        
    except Exception as e:
        logger.error(f"Error generating data insights: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate data insights: {str(e)}"
        )


# --- Helper Functions ---

def _calculate_complexity_score(analysis: Dict[str, Any]) -> float:
    """Calculate data complexity score (0.0 to 1.0)."""
    try:
        data_quality = analysis.get('data_quality', {})
        column_analysis = analysis.get('column_analysis', {})
        
        # Base complexity factors
        total_rows = data_quality.get('total_rows', 0)
        total_columns = data_quality.get('total_columns', 0)
        missing_data_pct = data_quality.get('missing_data_percentage', 0)
        
        # Size complexity (0.0 to 0.4)
        size_complexity = min(0.4, (total_rows * total_columns) / 1000000)
        
        # Missing data complexity (0.0 to 0.3)
        missing_complexity = min(0.3, missing_data_pct / 100)
        
        # Label complexity (0.0 to 0.3)
        label_columns = [col for col, info in column_analysis.items() if info.get('is_potential_label', False)]
        label_complexity = min(0.3, len(label_columns) / 10)
        
        return min(1.0, size_complexity + missing_complexity + label_complexity)
        
    except Exception:
        return 0.5  # Default moderate complexity


def _get_preprocessing_recommendations(analysis: Dict[str, Any]) -> List[str]:
    """Get preprocessing recommendations based on analysis."""
    recommendations = []
    
    try:
        data_quality = analysis.get('data_quality', {})
        
        missing_data_pct = data_quality.get('missing_data_percentage', 0)
        if missing_data_pct > 5:
            recommendations.append("Handle missing data with imputation or removal")
        
        duplicate_rows = data_quality.get('duplicate_rows', 0)
        if duplicate_rows > 0:
            recommendations.append("Remove duplicate rows to improve data quality")
        
        # Add text-specific recommendations
        recommendations.extend([
            "Normalize text casing and remove special characters",
            "Consider text length normalization",
            "Apply tokenization and stop word removal"
        ])
        
    except Exception:
        recommendations = ["Apply standard text preprocessing"]
    
    return recommendations


def _estimate_performance(analysis: Dict[str, Any]) -> Dict[str, Any]:
    """Estimate model performance based on data characteristics."""
    try:
        data_quality = analysis.get('data_quality', {})
        detected_structure = analysis.get('detected_structure', 'unclear')
        confidence = analysis.get('confidence', 0.0)
        
        total_rows = data_quality.get('total_rows', 0)
        
        # Base accuracy estimate
        base_accuracy = 0.7
        
        # Adjust based on structure confidence
        structure_bonus = confidence * 0.2
        
        # Adjust based on data size
        if total_rows > 1000:
            size_bonus = 0.1
        elif total_rows > 100:
            size_bonus = 0.05
        else:
            size_bonus = 0.0
        
        estimated_accuracy = min(0.95, base_accuracy + structure_bonus + size_bonus)
        
        return {
            "estimated_accuracy": round(estimated_accuracy, 3),
            "confidence_interval": [
                round(estimated_accuracy - 0.1, 3),
                round(estimated_accuracy + 0.05, 3)
            ],
            "factors": {
                "data_structure": detected_structure,
                "structure_confidence": confidence,
                "dataset_size": total_rows
            }
        }
        
    except Exception:
        return {
            "estimated_accuracy": 0.75,
            "confidence_interval": [0.65, 0.85],
            "factors": {"note": "Default estimates due to analysis error"}
        }


def _estimate_resources(analysis: Dict[str, Any]) -> Dict[str, Any]:
    """Estimate computational resource requirements."""
    try:
        data_quality = analysis.get('data_quality', {})
        total_rows = data_quality.get('total_rows', 0)
        total_columns = data_quality.get('total_columns', 0)
        
        # Estimate training time
        if total_rows < 100:
            training_time = "1-2 minutes"
            memory_usage = "Low (< 1GB)"
        elif total_rows < 1000:
            training_time = "5-10 minutes"
            memory_usage = "Moderate (1-2GB)"
        elif total_rows < 10000:
            training_time = "15-30 minutes"
            memory_usage = "High (2-4GB)"
        else:
            training_time = "30+ minutes"
            memory_usage = "Very High (4+ GB)"
        
        return {
            "estimated_training_time": training_time,
            "memory_usage": memory_usage,
            "recommended_batch_size": min(32, max(8, total_rows // 100)),
            "gpu_recommended": total_rows > 1000
        }
        
    except Exception:
        return {
            "estimated_training_time": "5-10 minutes",
            "memory_usage": "Moderate",
            "recommended_batch_size": 16,
            "gpu_recommended": False
        }
