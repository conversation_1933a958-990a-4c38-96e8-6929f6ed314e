/**
 * FileManagementDashboard.tsx
 * 
 * Comprehensive dashboard for managing uploaded files, their purposes,
 * usage statistics, and providing insights for optimization.
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line
} from 'recharts';
import {
  Database,
  Brain,
  Target,
  File,
  Trash2,
  RefreshCw,
  Download,
  Eye,
  TrendingUp,
  Clock,
  HardDrive,
  Activity,
  AlertCircle,
  CheckCircle2,
  BarChart3,
  <PERSON><PERSON><PERSON> as PieChartIcon,
  Calendar
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";

import { 
  unifiedDataManager, 
  UnifiedDataUpload, 
  DataPurpose 
} from "@/services/unifiedDataManager";
import { unifiedProgressMonitor } from "@/services/unifiedProgressMonitor";
import { FileReuseManager } from "./FileReuseManager";

interface FileManagementDashboardProps {
  className?: string;
  showDetailedAnalytics?: boolean;
  onFileSelected?: (fileId: string) => void;
}

export const FileManagementDashboard: React.FC<FileManagementDashboardProps> = ({
  className = '',
  showDetailedAnalytics = true,
  onFileSelected
}) => {
  const [files, setFiles] = useState<UnifiedDataUpload[]>([]);
  const [usageStats, setUsageStats] = useState<any>(null);
  const [progressStats, setProgressStats] = useState<any>(null);
  const [selectedFile, setSelectedFile] = useState<UnifiedDataUpload | null>(null);
  const [showDeleteDialog, setShowDeleteDialog] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'overview' | 'files' | 'analytics' | 'activity'>('overview');

  const { toast } = useToast();

  useEffect(() => {
    loadData();
    const interval = setInterval(loadData, 30000); // Refresh every 30 seconds
    return () => clearInterval(interval);
  }, []);

  const loadData = async () => {
    try {
      const result = await unifiedDataManager.getValidatedFiles();
      const stats = unifiedDataManager.getUsageStats();
      const progressStatistics = unifiedProgressMonitor.getStatistics();

      setFiles(result.files);
      setUsageStats(stats);
      setProgressStats(progressStatistics);

      // Show notification if files were cleaned up
      if (result.cleanedCount > 0) {
        console.log(`Cleaned up ${result.cleanedCount} stale files from cache`);
      }
    } catch (error) {
      console.error('Failed to load files:', error);
      setFiles([]);
    }
  };

  const handleDeleteFile = async (fileId: string) => {
    const success = unifiedDataManager.removeFile(fileId);
    if (success) {
      loadData();
      toast({
        title: "File deleted",
        description: "File has been removed from the system"
      });
    } else {
      toast({
        title: "Error",
        description: "Failed to delete file",
        variant: "destructive"
      });
    }
    setShowDeleteDialog(null);
  };

  const handleClearAllFiles = () => {
    unifiedDataManager.clearAll();
    loadData();
    toast({
      title: "All files cleared",
      description: "All uploaded files have been removed"
    });
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (date: Date) => {
    return new Intl.RelativeTimeFormat('en', { numeric: 'auto' }).format(
      Math.ceil((date.getTime() - Date.now()) / (1000 * 60 * 60 * 24)),
      'day'
    );
  };

  const getPurposeIcon = (purpose: DataPurpose) => {
    switch (purpose) {
      case 'analysis': return <Database className="w-4 h-4" />;
      case 'training': return <Brain className="w-4 h-4" />;
      case 'classification': return <Target className="w-4 h-4" />;
      default: return <File className="w-4 h-4" />;
    }
  };

  const getPurposeColor = (purpose: DataPurpose) => {
    switch (purpose) {
      case 'analysis': return '#3b82f6';
      case 'training': return '#10b981';
      case 'classification': return '#8b5cf6';
      default: return '#6b7280';
    }
  };

  // Prepare chart data
  const purposeChartData = usageStats ? [
    { name: 'Analysis', value: usageStats.purposeBreakdown.analysis, color: '#3b82f6' },
    { name: 'Training', value: usageStats.purposeBreakdown.training, color: '#10b981' },
    { name: 'Classification', value: usageStats.purposeBreakdown.classification, color: '#8b5cf6' }
  ].filter(item => item.value > 0) : [];

  const fileSizeData = files.map(file => ({
    name: file.fileInfo.filename.substring(0, 20) + (file.fileInfo.filename.length > 20 ? '...' : ''),
    size: file.file?.size || 0,
    rows: file.fileInfo.num_rows
  })).slice(0, 10);

  const activityData = files.map(file => ({
    name: file.fileInfo.filename.substring(0, 15) + '...',
    uploaded: file.uploadedAt.getTime(),
    lastUsed: file.lastUsed.getTime()
  })).slice(0, 8);

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">File Management Dashboard</h2>
          <p className="text-muted-foreground">
            Manage your uploaded files and monitor system activity
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={loadData}>
            <RefreshCw className="w-4 h-4 mr-2" />
            Refresh
          </Button>
          {files.length > 0 && (
            <Button variant="destructive" onClick={handleClearAllFiles}>
              <Trash2 className="w-4 h-4 mr-2" />
              Clear All
            </Button>
          )}
        </div>
      </div>

      {/* Main Dashboard */}
      <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as any)}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview" className="flex items-center gap-2">
            <BarChart3 className="w-4 h-4" />
            Overview
          </TabsTrigger>
          <TabsTrigger value="files" className="flex items-center gap-2">
            <File className="w-4 h-4" />
            Files ({files.length})
          </TabsTrigger>
          <TabsTrigger value="analytics" className="flex items-center gap-2">
            <PieChartIcon className="w-4 h-4" />
            Analytics
          </TabsTrigger>
          <TabsTrigger value="activity" className="flex items-center gap-2">
            <Activity className="w-4 h-4" />
            Activity
          </TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          {/* Key Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Total Files</p>
                    <p className="text-2xl font-bold">{usageStats?.totalFiles || 0}</p>
                  </div>
                  <File className="w-8 h-8 text-muted-foreground" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Total Size</p>
                    <p className="text-2xl font-bold">
                      {usageStats ? formatFileSize(usageStats.totalSize) : '0 B'}
                    </p>
                  </div>
                  <HardDrive className="w-8 h-8 text-muted-foreground" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Success Rate</p>
                    <p className="text-2xl font-bold">
                      {progressStats ? `${progressStats.successRate.toFixed(1)}%` : '0%'}
                    </p>
                  </div>
                  <CheckCircle2 className="w-8 h-8 text-green-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Active Operations</p>
                    <p className="text-2xl font-bold">{progressStats?.active || 0}</p>
                  </div>
                  <Activity className="w-8 h-8 text-blue-500" />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Purpose Distribution */}
          {purposeChartData.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>File Purpose Distribution</CardTitle>
                <CardDescription>How your files are being used</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-64">
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={purposeChartData}
                        cx="50%"
                        cy="50%"
                        outerRadius={80}
                        dataKey="value"
                        label={({ name, value }) => `${name}: ${value}`}
                      >
                        {purposeChartData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.color} />
                        ))}
                      </Pie>
                      <Tooltip />
                    </PieChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Recent Activity */}
          <Card>
            <CardHeader>
              <CardTitle>Recent Files</CardTitle>
              <CardDescription>Your most recently used files</CardDescription>
            </CardHeader>
            <CardContent>
              {files.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <File className="w-12 h-12 mx-auto mb-4 opacity-50" />
                  <p>No files uploaded yet</p>
                </div>
              ) : (
                <div className="space-y-3">
                  {files.slice(0, 5).map((file) => (
                    <div
                      key={file.fileInfo.file_id}
                      className="flex items-center justify-between p-3 border rounded-lg hover:bg-muted/50"
                    >
                      <div className="flex items-center gap-3">
                        <File className="w-8 h-8 text-primary" />
                        <div>
                          <p className="font-medium">{file.fileInfo.filename}</p>
                          <p className="text-sm text-muted-foreground">
                            {file.fileInfo.num_rows.toLocaleString()} rows • {formatDate(file.lastUsed)}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        {Object.entries(file.purposes)
                          .filter(([_, enabled]) => enabled)
                          .map(([purpose]) => (
                            <Badge key={purpose} variant="outline" className="text-xs">
                              {getPurposeIcon(purpose as DataPurpose)}
                              <span className="ml-1">{purpose}</span>
                            </Badge>
                          ))}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Files Tab */}
        <TabsContent value="files">
          <FileReuseManager
            onFileSelected={(fileId, fileInfo, purposes) => {
              if (onFileSelected) {
                onFileSelected(fileId);
              }
            }}
            showFileDetails={true}
            allowPurposeUpdate={true}
            maxDisplayFiles={0} // Show all files
          />
        </TabsContent>

        {/* Analytics Tab */}
        <TabsContent value="analytics" className="space-y-6">
          {showDetailedAnalytics && (
            <>
              {/* File Size Distribution */}
              {fileSizeData.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle>File Size Distribution</CardTitle>
                    <CardDescription>Size comparison of your uploaded files</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="h-64">
                      <ResponsiveContainer width="100%" height="100%">
                        <BarChart data={fileSizeData}>
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="name" />
                          <YAxis />
                          <Tooltip 
                            formatter={(value, name) => [
                              name === 'size' ? formatFileSize(value as number) : value,
                              name === 'size' ? 'File Size' : 'Rows'
                            ]}
                          />
                          <Bar dataKey="size" fill="#3b82f6" name="size" />
                        </BarChart>
                      </ResponsiveContainer>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* System Performance */}
              {progressStats && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <Card>
                    <CardHeader>
                      <CardTitle>Operation Statistics</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="flex justify-between">
                        <span>Completed Operations</span>
                        <span className="font-medium">{progressStats.completed}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Failed Operations</span>
                        <span className="font-medium text-red-600">{progressStats.failed}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Average Duration</span>
                        <span className="font-medium">
                          {progressStats.averageDuration.toFixed(1)}s
                        </span>
                      </div>
                      <div className="space-y-2">
                        <div className="flex justify-between">
                          <span>Success Rate</span>
                          <span className="font-medium">{progressStats.successRate.toFixed(1)}%</span>
                        </div>
                        <Progress value={progressStats.successRate} className="h-2" />
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle>Storage Usage</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="flex justify-between">
                        <span>Total Files</span>
                        <span className="font-medium">{usageStats?.totalFiles || 0}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Total Size</span>
                        <span className="font-medium">
                          {usageStats ? formatFileSize(usageStats.totalSize) : '0 B'}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span>Average File Size</span>
                        <span className="font-medium">
                          {usageStats && usageStats.totalFiles > 0 
                            ? formatFileSize(usageStats.totalSize / usageStats.totalFiles)
                            : '0 B'
                          }
                        </span>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              )}
            </>
          )}
        </TabsContent>

        {/* Activity Tab */}
        <TabsContent value="activity" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Recent Activity</CardTitle>
              <CardDescription>Timeline of file operations and usage</CardDescription>
            </CardHeader>
            <CardContent>
              {files.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <Clock className="w-12 h-12 mx-auto mb-4 opacity-50" />
                  <p>No activity to show</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {files.slice(0, 10).map((file) => (
                    <div key={file.fileInfo.file_id} className="flex items-start gap-4 p-4 border rounded-lg">
                      <div className="w-2 h-2 rounded-full bg-primary mt-2"></div>
                      <div className="flex-1">
                        <div className="flex items-center justify-between">
                          <p className="font-medium">{file.fileInfo.filename}</p>
                          <span className="text-sm text-muted-foreground">
                            {formatDate(file.lastUsed)}
                          </span>
                        </div>
                        <p className="text-sm text-muted-foreground mt-1">
                          Used for: {Object.entries(file.purposes)
                            .filter(([_, enabled]) => enabled)
                            .map(([purpose]) => purpose)
                            .join(', ')
                          }
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Delete Confirmation Dialog */}
      <Dialog open={!!showDeleteDialog} onOpenChange={() => setShowDeleteDialog(null)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete File</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this file? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowDeleteDialog(null)}>
              Cancel
            </Button>
            <Button 
              variant="destructive" 
              onClick={() => showDeleteDialog && handleDeleteFile(showDeleteDialog)}
            >
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};
