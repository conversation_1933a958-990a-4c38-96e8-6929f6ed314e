import { useState } from "react";
import { Navigation } from "@/components/Navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  BarChart3, 
  Brain, 
  Database, 
  Zap, 
  TrendingUp, 
  Activity,
  PlayCircle,
  Settings,
  Users,
  FileText,
  Download,
  Rocket,
  CheckCircle2,
  Clock,
  Target,
  Cpu
} from "lucide-react";

const Dashboard = () => {
  const [selectedProject, setSelectedProject] = useState("sentiment-analysis");

  const projects = [
    {
      id: "sentiment-analysis",
      name: "Sentiment Analysis",
      type: "Multi-class Classification",
      status: "Complete",
      accuracy: 94.7,
      lastTrained: "2 hours ago"
    },
    {
      id: "spam-detection",
      name: "Spam Detection",
      type: "Binary Classification",
      status: "Training",
      accuracy: 87.2,
      lastTrained: "Running"
    },
    {
      id: "document-tagging",
      name: "Document Tagging",
      type: "Multi-label Classification",
      status: "Ready",
      accuracy: null,
      lastTrained: "Never"
    }
  ];

  const currentProject = projects.find(p => p.id === selectedProject);

  return (
    <div className="min-h-screen bg-background">
      <Navigation />
      
      <main className="pt-20">
        {/* Header */}
        <section className="py-8 bg-gradient-card">
          <div className="max-w-7xl mx-auto px-6">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold mb-2">ML Classification Dashboard</h1>
                <p className="text-muted-foreground">Manage your classification projects and monitor performance</p>
              </div>
              <div className="flex gap-3">
                <Button variant="outline" asChild>
                  <a href="/get-started">
                    <PlayCircle className="w-4 h-4 mr-2" />
                    New Project
                  </a>
                </Button>
                <Button asChild>
                  <a href="/get-started">
                    <Rocket className="w-4 h-4 mr-2" />
                    Get Started
                  </a>
                </Button>
              </div>
            </div>
          </div>
        </section>

        <div className="max-w-7xl mx-auto px-6 py-8">
          {/* Stats Overview */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <Card>
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-sm font-medium text-muted-foreground">Active Projects</CardTitle>
                  <Brain className="w-4 h-4 text-muted-foreground" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">3</div>
                <p className="text-xs text-muted-foreground">+1 from last week</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-sm font-medium text-muted-foreground">Models Trained</CardTitle>
                  <Target className="w-4 h-4 text-muted-foreground" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">12</div>
                <p className="text-xs text-muted-foreground">This month</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-sm font-medium text-muted-foreground">Avg Accuracy</CardTitle>
                  <TrendingUp className="w-4 h-4 text-muted-foreground" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-ml-success">91.6%</div>
                <p className="text-xs text-muted-foreground">+2.3% improvement</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-sm font-medium text-muted-foreground">GPU Hours</CardTitle>
                  <Cpu className="w-4 h-4 text-muted-foreground" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">47.2h</div>
                <p className="text-xs text-muted-foreground">Used this month</p>
              </CardContent>
            </Card>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Project List */}
            <Card className="lg:col-span-1">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Database className="w-5 h-5" />
                  Projects
                </CardTitle>
                <CardDescription>Your classification projects</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {projects.map((project) => (
                    <div
                      key={project.id}
                      onClick={() => setSelectedProject(project.id)}
                      className={`p-4 rounded-lg border cursor-pointer transition-all hover:shadow-card ${
                        selectedProject === project.id 
                          ? 'border-primary bg-primary/5' 
                          : 'border-border hover:border-primary/30'
                      }`}
                    >
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-semibold">{project.name}</h4>
                        <Badge 
                          variant={project.status === 'Complete' ? 'secondary' : 
                                  project.status === 'Training' ? 'default' : 'outline'}
                          className={
                            project.status === 'Complete' ? 'text-ml-success' :
                            project.status === 'Training' ? 'bg-ml-primary' : ''
                          }
                        >
                          {project.status}
                        </Badge>
                      </div>
                      <p className="text-sm text-muted-foreground mb-2">{project.type}</p>
                      <div className="flex items-center justify-between text-xs">
                        <span className="text-muted-foreground">
                          {project.accuracy ? `${project.accuracy}% accuracy` : 'Not trained'}
                        </span>
                        <span className="text-muted-foreground">{project.lastTrained}</span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Project Details */}
            <div className="lg:col-span-2 space-y-6">
              {currentProject && (
                <>
                  {/* Project Overview */}
                  <Card>
                    <CardHeader>
                      <div className="flex items-center justify-between">
                        <div>
                          <CardTitle>{currentProject.name}</CardTitle>
                          <CardDescription>{currentProject.type}</CardDescription>
                        </div>
                        <Badge 
                          variant={currentProject.status === 'Complete' ? 'secondary' : 'default'}
                          className={currentProject.status === 'Complete' ? 'text-ml-success' : 'bg-ml-primary'}
                        >
                          {currentProject.status}
                        </Badge>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <Tabs defaultValue="overview">
                        <TabsList>
                          <TabsTrigger value="overview">Overview</TabsTrigger>
                          <TabsTrigger value="performance">Performance</TabsTrigger>
                          <TabsTrigger value="data">Data</TabsTrigger>
                        </TabsList>
                        
                        <TabsContent value="overview" className="space-y-4 mt-4">
                          <div className="grid grid-cols-2 gap-4">
                            <div className="p-4 bg-muted/50 rounded-lg">
                              <div className="flex items-center gap-2 mb-2">
                                <BarChart3 className="w-4 h-4 text-ml-primary" />
                                <span className="text-sm font-medium">Model Performance</span>
                              </div>
                              <div className="text-2xl font-bold text-ml-success">
                                {currentProject.accuracy ? `${currentProject.accuracy}%` : 'N/A'}
                              </div>
                            </div>
                            
                            <div className="p-4 bg-muted/50 rounded-lg">
                              <div className="flex items-center gap-2 mb-2">
                                <Clock className="w-4 h-4 text-ml-secondary" />
                                <span className="text-sm font-medium">Last Training</span>
                              </div>
                              <div className="text-sm font-medium">{currentProject.lastTrained}</div>
                            </div>
                          </div>
                          
                          {currentProject.status === 'Complete' && (
                            <div className="flex gap-3">
                              <Button size="sm">
                                <PlayCircle className="w-4 h-4 mr-2" />
                                Test Model
                              </Button>
                              <Button variant="outline" size="sm">
                                <Download className="w-4 h-4 mr-2" />
                                Export
                              </Button>
                              <Button variant="outline" size="sm">
                                <Rocket className="w-4 h-4 mr-2" />
                                Deploy
                              </Button>
                            </div>
                          )}
                        </TabsContent>
                        
                        <TabsContent value="performance" className="space-y-4 mt-4">
                          <div className="h-48 bg-muted/30 rounded-lg flex items-center justify-center">
                            <div className="text-center">
                              <BarChart3 className="w-12 h-12 text-muted-foreground mx-auto mb-2" />
                              <p className="text-sm text-muted-foreground">Performance charts would appear here</p>
                            </div>
                          </div>
                          
                          <div className="grid grid-cols-3 gap-4 text-center">
                            <div className="p-3 bg-muted/50 rounded-lg">
                              <p className="text-sm text-muted-foreground mb-1">Precision</p>
                              <p className="text-lg font-bold">93.2%</p>
                            </div>
                            <div className="p-3 bg-muted/50 rounded-lg">
                              <p className="text-sm text-muted-foreground mb-1">Recall</p>
                              <p className="text-lg font-bold">94.1%</p>
                            </div>
                            <div className="p-3 bg-muted/50 rounded-lg">
                              <p className="text-sm text-muted-foreground mb-1">F1-Score</p>
                              <p className="text-lg font-bold">93.6%</p>
                            </div>
                          </div>
                        </TabsContent>
                        
                        <TabsContent value="data" className="space-y-4 mt-4">
                          <div className="space-y-3">
                            <div className="flex justify-between items-center p-3 bg-muted/50 rounded-lg">
                              <span className="text-sm font-medium">Training samples</span>
                              <span className="text-sm">8,972</span>
                            </div>
                            <div className="flex justify-between items-center p-3 bg-muted/50 rounded-lg">
                              <span className="text-sm font-medium">Validation samples</span>
                              <span className="text-sm">1,247</span>
                            </div>
                            <div className="flex justify-between items-center p-3 bg-muted/50 rounded-lg">
                              <span className="text-sm font-medium">Test samples</span>
                              <span className="text-sm">623</span>
                            </div>
                            <div className="flex justify-between items-center p-3 bg-muted/50 rounded-lg">
                              <span className="text-sm font-medium">Features</span>
                              <span className="text-sm">12</span>
                            </div>
                          </div>
                        </TabsContent>
                      </Tabs>
                    </CardContent>
                  </Card>
                </>
              )}
            </div>
          </div>

          {/* Quick Actions */}
          <Card className="mt-8">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Zap className="w-5 h-5" />
                Quick Actions
              </CardTitle>
              <CardDescription>Get started with your next classification project</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Button variant="outline" className="h-20 flex-col" asChild>
                  <a href="/beginner">
                    <Users className="w-6 h-6 mb-2" />
                    <span>Start Guided Project</span>
                  </a>
                </Button>
                <Button variant="outline" className="h-20 flex-col" asChild>
                  <a href="/expert">
                    <Settings className="w-6 h-6 mb-2" />
                    <span>Advanced Setup</span>
                  </a>
                </Button>
                <Button variant="outline" className="h-20 flex-col">
                  <FileText className="w-6 h-6 mb-2" />
                  <span>View Documentation</span>
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  );
};

export default Dashboard;