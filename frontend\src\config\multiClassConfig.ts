/**
 * Production configuration for multi-class classification workflow
 */

// API Configuration
export const MULTICLASS_API_CONFIG = {
  // Base endpoints
  ENDPOINTS: {
    TRAIN: '/api/train/multi-class',
    CLASSIFY: '/api/classify/multi-class',
    MODELS: '/api/models/multi-class',
    TASKS: '/api/tasks',
    VALIDATE: '/api/validate/multi-class',
    RECOMMEND: '/api/recommend/strategy/multi-class'
  },

  // Request timeouts (in milliseconds)
  TIMEOUTS: {
    TRAINING: 30000,      // 30 seconds for training requests
    CLASSIFICATION: 60000, // 1 minute for classification requests
    MODEL_LIST: 10000,    // 10 seconds for model listing
    TASK_STATUS: 5000     // 5 seconds for task status checks
  },

  // Retry configuration
  RETRY: {
    MAX_ATTEMPTS: 3,
    BACKOFF_MULTIPLIER: 2,
    INITIAL_DELAY: 1000   // 1 second
  },

  // Progress monitoring
  MONITORING: {
    POLL_INTERVAL: 2000,  // 2 seconds
    MAX_POLL_DURATION: 3600000, // 1 hour
    PROGRESS_THRESHOLD: 0.01 // Minimum progress change to report
  }
};

// Label Format Configuration
export const LABEL_FORMATS = {
  SINGLE: {
    id: 'single',
    name: 'Single Label Column (Traditional)',
    description: 'One column containing class names for each sample',
    example: {
      text_column: 'content',
      label_column: 'category',
      sample_data: [
        { content: 'Breaking news about sports...', category: 'Sports' },
        { content: 'New technology breakthrough...', category: 'Technology' },
        { content: 'Political developments...', category: 'Politics' }
      ]
    },
    pros: [
      'Simple and intuitive format',
      'Compact data representation',
      'Easy to understand and validate',
      'Standard format for most ML libraries'
    ],
    cons: [
      'Cannot handle multi-label scenarios',
      'Requires string-to-index mapping'
    ],
    use_cases: [
      'Document classification',
      'Sentiment analysis with multiple categories',
      'Topic classification',
      'Intent classification'
    ]
  },

  MULTIPLE: {
    id: 'multiple',
    name: 'Multiple Binary Columns (One-Hot Encoded)',
    description: 'Separate binary columns for each class (1 for positive, 0 for negative)',
    example: {
      text_column: 'content',
      label_columns: ['Sports', 'Technology', 'Politics'],
      sample_data: [
        { content: 'Breaking news about sports...', Sports: 1, Technology: 0, Politics: 0 },
        { content: 'New technology breakthrough...', Sports: 0, Technology: 1, Politics: 0 },
        { content: 'Political developments...', Sports: 0, Technology: 0, Politics: 1 }
      ]
    },
    pros: [
      'Direct binary representation',
      'No string-to-index mapping needed',
      'Can be extended to multi-label easily',
      'Clear class boundaries'
    ],
    cons: [
      'More columns required',
      'Larger file sizes',
      'Must ensure exactly one 1 per row for multi-class'
    ],
    use_cases: [
      'Pre-processed datasets',
      'Data from ML pipelines',
      'Datasets prepared for neural networks',
      'Migration from multi-label to multi-class'
    ]
  }
};

// Training Configuration Limits
export const TRAINING_LIMITS = {
  // Model parameters
  EPOCHS: { MIN: 1, MAX: 20, DEFAULT: 3 },
  BATCH_SIZE: { MIN: 1, MAX: 128, DEFAULT: 16 },
  LEARNING_RATE: { MIN: 1e-6, MAX: 1e-2, DEFAULT: 2e-5 },
  VALIDATION_SPLIT: { MIN: 0.1, MAX: 0.5, DEFAULT: 0.2 },

  // Data requirements
  MIN_CLASSES: 3,
  MIN_SAMPLES_PER_CLASS: 5,
  MIN_TOTAL_SAMPLES: 10,
  RECOMMENDED_SAMPLES_PER_CLASS: 50,
  RECOMMENDED_TOTAL_SAMPLES: 1000,

  // Text processing
  MAX_TEXT_LENGTH: 512,
  MIN_TEXT_LENGTH: 1,

  // Class imbalance
  MAX_IMBALANCE_RATIO: 50,
  WARNING_IMBALANCE_RATIO: 10,

  // Label format specific limits
  SINGLE_LABEL: {
    MAX_CLASS_NAME_LENGTH: 100,
    MIN_CLASS_NAME_LENGTH: 1,
    RESERVED_NAMES: ['null', 'undefined', 'none', 'empty']
  },

  MULTIPLE_BINARY: {
    MIN_BINARY_COLUMNS: 3,
    MAX_BINARY_COLUMNS: 100,
    VALID_BINARY_VALUES: [0, 1, '0', '1', true, false, 'true', 'false']
  }
};

// Strategy Configuration
export const STRATEGY_CONFIG = {
  SOFTMAX: {
    name: 'Softmax (Native Multi-class)',
    description: 'Direct multi-class classification with softmax output layer',
    pros: ['Fastest training', 'Most memory efficient', 'Natural probability distribution'],
    cons: ['May struggle with imbalanced data', 'Less flexible than ensemble methods'],
    complexity: 'Low',
    color: '#3b82f6',
    recommendedFor: ['balanced datasets', 'few classes (< 20)', 'speed requirements'],
    maxClasses: 100
  },
  
  OVR: {
    name: 'One-vs-Rest',
    description: 'Train one binary classifier per class against all other classes',
    pros: ['Handles imbalanced data well', 'Scales to many classes', 'Interpretable'],
    cons: ['Slower training', 'Less calibrated probabilities', 'More memory usage'],
    complexity: 'Medium',
    color: '#10b981',
    recommendedFor: ['imbalanced datasets', 'many classes', 'interpretability'],
    maxClasses: 1000
  },
  
  OVO: {
    name: 'One-vs-One',
    description: 'Train one binary classifier for each pair of classes',
    pros: ['Good for small datasets', 'Robust to outliers', 'Handles complex boundaries'],
    cons: ['Quadratic complexity', 'Slower inference', 'Memory intensive'],
    complexity: 'High',
    color: '#f59e0b',
    recommendedFor: ['small datasets', 'complex class boundaries', 'high accuracy needs'],
    maxClasses: 50
  }
};

// Model Configuration
export const MODEL_CONFIG = {
  // Available base models
  BASE_MODELS: [
    {
      id: 'distilbert-base-uncased',
      name: 'DistilBERT Base',
      description: 'Fast and efficient, good for most tasks',
      size: '267MB',
      speed: 5,
      accuracy: 3,
      memory: 5,
      recommendedFor: ['quick prototyping', 'resource constraints', 'real-time inference']
    },
    {
      id: 'bert-base-uncased',
      name: 'BERT Base',
      description: 'Balanced performance and speed',
      size: '440MB',
      speed: 3,
      accuracy: 4,
      memory: 3,
      recommendedFor: ['balanced tasks', 'general purpose', 'good baseline']
    },
    {
      id: 'roberta-base',
      name: 'RoBERTa Base',
      description: 'Enhanced BERT with better training',
      size: '498MB',
      speed: 2,
      accuracy: 5,
      memory: 2,
      recommendedFor: ['high accuracy needs', 'complex text', 'production systems']
    },
    {
      id: 'electra-base-discriminator',
      name: 'ELECTRA Base',
      description: 'Fast training with good performance',
      size: '440MB',
      speed: 4,
      accuracy: 4,
      memory: 3,
      recommendedFor: ['fast training', 'balanced performance', 'efficiency']
    }
  ],

  // Preset configurations
  PRESETS: {
    FAST: {
      name: 'Fast Training',
      description: 'Optimized for speed and quick results',
      config: {
        numEpochs: 2,
        batchSize: 32,
        learningRate: 5e-5,
        useUnsloth: true,
        fp16: true,
        gradientCheckpointing: false,
        enableEarlyStopping: false
      },
      estimatedTime: '5-15 minutes',
      recommendedFor: ['prototyping', 'small datasets', 'quick validation']
    },
    
    BALANCED: {
      name: 'Balanced',
      description: 'Good balance of speed and accuracy',
      config: {
        numEpochs: 3,
        batchSize: 16,
        learningRate: 2e-5,
        useUnsloth: true,
        fp16: true,
        gradientCheckpointing: true,
        enableEarlyStopping: true,
        patience: 2
      },
      estimatedTime: '15-30 minutes',
      recommendedFor: ['general use', 'medium datasets', 'production ready']
    },
    
    HIGH_QUALITY: {
      name: 'High Quality',
      description: 'Optimized for best possible accuracy',
      config: {
        numEpochs: 5,
        batchSize: 8,
        learningRate: 1e-5,
        useUnsloth: false,
        fp16: false,
        gradientCheckpointing: true,
        enableEarlyStopping: true,
        patience: 3,
        warmupSteps: 1000
      },
      estimatedTime: '30-60 minutes',
      recommendedFor: ['critical applications', 'large datasets', 'maximum accuracy']
    }
  }
};

// Validation Rules
export const VALIDATION_RULES = {
  // Required fields
  REQUIRED_FIELDS: ['modelName', 'numEpochs', 'batchSize', 'learningRate'],
  
  // Field validation patterns
  MODEL_NAME_PATTERN: /^[a-zA-Z0-9_-]+$/,
  MODEL_NAME_MAX_LENGTH: 50,
  
  // Custom validation functions
  validateEpochs: (epochs: number) => {
    if (epochs < TRAINING_LIMITS.EPOCHS.MIN || epochs > TRAINING_LIMITS.EPOCHS.MAX) {
      return `Epochs must be between ${TRAINING_LIMITS.EPOCHS.MIN} and ${TRAINING_LIMITS.EPOCHS.MAX}`;
    }
    return null;
  },
  
  validateBatchSize: (batchSize: number) => {
    if (batchSize < TRAINING_LIMITS.BATCH_SIZE.MIN || batchSize > TRAINING_LIMITS.BATCH_SIZE.MAX) {
      return `Batch size must be between ${TRAINING_LIMITS.BATCH_SIZE.MIN} and ${TRAINING_LIMITS.BATCH_SIZE.MAX}`;
    }
    return null;
  },
  
  validateLearningRate: (lr: number) => {
    if (lr < TRAINING_LIMITS.LEARNING_RATE.MIN || lr > TRAINING_LIMITS.LEARNING_RATE.MAX) {
      return `Learning rate must be between ${TRAINING_LIMITS.LEARNING_RATE.MIN} and ${TRAINING_LIMITS.LEARNING_RATE.MAX}`;
    }
    return null;
  },
  
  validateClassDistribution: (distribution: Record<string, number>) => {
    const counts = Object.values(distribution);
    const minCount = Math.min(...counts);
    const maxCount = Math.max(...counts);
    const imbalanceRatio = maxCount / minCount;

    if (minCount < TRAINING_LIMITS.MIN_SAMPLES_PER_CLASS) {
      return `Some classes have fewer than ${TRAINING_LIMITS.MIN_SAMPLES_PER_CLASS} samples`;
    }

    if (imbalanceRatio > TRAINING_LIMITS.MAX_IMBALANCE_RATIO) {
      return `Class imbalance is too high (${imbalanceRatio.toFixed(1)}:1). Consider balancing your data.`;
    }

    return null;
  },

  // Label format specific validation
  validateSingleLabelFormat: (data: any[], textColumn: string, labelColumn: string) => {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Check if columns exist
    if (!data[0]?.hasOwnProperty(textColumn)) {
      errors.push(`Text column '${textColumn}' not found in data`);
    }
    if (!data[0]?.hasOwnProperty(labelColumn)) {
      errors.push(`Label column '${labelColumn}' not found in data`);
    }

    if (errors.length > 0) return { valid: false, errors, warnings };

    // Validate label values
    const uniqueLabels = new Set();
    const emptyLabels = [];
    const invalidLabels = [];

    data.forEach((row, index) => {
      const label = row[labelColumn];

      if (!label || label === '' || label === null || label === undefined) {
        emptyLabels.push(index + 1);
      } else if (typeof label !== 'string' && typeof label !== 'number') {
        invalidLabels.push(`Row ${index + 1}: ${typeof label}`);
      } else {
        const labelStr = String(label).trim();
        if (labelStr.length > TRAINING_LIMITS.SINGLE_LABEL.MAX_CLASS_NAME_LENGTH) {
          warnings.push(`Row ${index + 1}: Label too long (${labelStr.length} chars)`);
        }
        if (TRAINING_LIMITS.SINGLE_LABEL.RESERVED_NAMES.includes(labelStr.toLowerCase())) {
          warnings.push(`Row ${index + 1}: Reserved label name '${labelStr}'`);
        }
        uniqueLabels.add(labelStr);
      }
    });

    if (emptyLabels.length > 0) {
      errors.push(`Empty labels found in rows: ${emptyLabels.slice(0, 5).join(', ')}${emptyLabels.length > 5 ? '...' : ''}`);
    }

    if (invalidLabels.length > 0) {
      errors.push(`Invalid label types: ${invalidLabels.slice(0, 3).join(', ')}${invalidLabels.length > 3 ? '...' : ''}`);
    }

    if (uniqueLabels.size < TRAINING_LIMITS.MIN_CLASSES) {
      errors.push(`Need at least ${TRAINING_LIMITS.MIN_CLASSES} classes, found ${uniqueLabels.size}`);
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings,
      stats: {
        num_classes: uniqueLabels.size,
        class_names: Array.from(uniqueLabels),
        empty_labels: emptyLabels.length,
        invalid_labels: invalidLabels.length
      }
    };
  },

  validateMultipleBinaryFormat: (data: any[], textColumn: string, labelColumns: string[]) => {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Check minimum columns
    if (labelColumns.length < TRAINING_LIMITS.MULTIPLE_BINARY.MIN_BINARY_COLUMNS) {
      errors.push(`Need at least ${TRAINING_LIMITS.MULTIPLE_BINARY.MIN_BINARY_COLUMNS} binary columns, selected ${labelColumns.length}`);
    }

    if (labelColumns.length > TRAINING_LIMITS.MULTIPLE_BINARY.MAX_BINARY_COLUMNS) {
      errors.push(`Too many binary columns (${labelColumns.length}), maximum is ${TRAINING_LIMITS.MULTIPLE_BINARY.MAX_BINARY_COLUMNS}`);
    }

    // Check if columns exist
    if (!data[0]?.hasOwnProperty(textColumn)) {
      errors.push(`Text column '${textColumn}' not found in data`);
    }

    const missingColumns = labelColumns.filter(col => !data[0]?.hasOwnProperty(col));
    if (missingColumns.length > 0) {
      errors.push(`Binary columns not found: ${missingColumns.join(', ')}`);
    }

    if (errors.length > 0) return { valid: false, errors, warnings };

    // Validate binary values and one-hot encoding
    const invalidRows = [];
    const multipleActiveRows = [];
    const noActiveRows = [];
    const invalidValues = [];

    data.forEach((row, index) => {
      const values = labelColumns.map(col => row[col]);
      const activeCount = values.filter(val => val === 1 || val === '1' || val === true || val === 'true').length;
      const invalidVals = values.filter(val => !TRAINING_LIMITS.MULTIPLE_BINARY.VALID_BINARY_VALUES.includes(val));

      if (invalidVals.length > 0) {
        invalidValues.push(`Row ${index + 1}: ${invalidVals.join(', ')}`);
      } else if (activeCount === 0) {
        noActiveRows.push(index + 1);
      } else if (activeCount > 1) {
        multipleActiveRows.push(index + 1);
      }
    });

    if (invalidValues.length > 0) {
      errors.push(`Invalid binary values: ${invalidValues.slice(0, 3).join('; ')}${invalidValues.length > 3 ? '...' : ''}`);
    }

    if (noActiveRows.length > 0) {
      errors.push(`Rows with no active class: ${noActiveRows.slice(0, 10).join(', ')}${noActiveRows.length > 10 ? '...' : ''}`);
    }

    if (multipleActiveRows.length > 0) {
      warnings.push(`Rows with multiple active classes (multi-label): ${multipleActiveRows.slice(0, 10).join(', ')}${multipleActiveRows.length > 10 ? '...' : ''}`);
    }

    // Calculate class distribution
    const classDistribution: Record<string, number> = {};
    labelColumns.forEach(col => {
      classDistribution[col] = data.filter(row =>
        row[col] === 1 || row[col] === '1' || row[col] === true || row[col] === 'true'
      ).length;
    });

    return {
      valid: errors.length === 0,
      errors,
      warnings,
      stats: {
        num_classes: labelColumns.length,
        class_names: labelColumns,
        class_distribution: classDistribution,
        rows_with_no_class: noActiveRows.length,
        rows_with_multiple_classes: multipleActiveRows.length,
        invalid_value_rows: invalidValues.length
      }
    };
  }
};

// Export Configuration
export const EXPORT_CONFIG = {
  // Supported formats
  FORMATS: [
    {
      id: 'pytorch',
      name: 'PyTorch',
      extension: '.pt',
      description: 'Native PyTorch format',
      size: 'Original',
      compatibility: ['Python', 'PyTorch']
    },
    {
      id: 'onnx',
      name: 'ONNX',
      extension: '.onnx',
      description: 'Cross-platform format',
      size: 'Optimized',
      compatibility: ['Python', 'C++', 'JavaScript', 'C#']
    },
    {
      id: 'huggingface',
      name: 'HuggingFace',
      extension: '.bin',
      description: 'HuggingFace Transformers format',
      size: 'Original',
      compatibility: ['Python', 'HuggingFace Hub']
    },
    {
      id: 'tensorflow',
      name: 'TensorFlow',
      extension: '.pb',
      description: 'TensorFlow SavedModel format',
      size: 'Optimized',
      compatibility: ['Python', 'TensorFlow', 'TensorFlow Lite']
    }
  ],

  // Documentation levels
  DOCUMENTATION_LEVELS: [
    {
      id: 'minimal',
      name: 'Minimal',
      description: 'README only',
      includes: ['README.md', 'requirements.txt']
    },
    {
      id: 'standard',
      name: 'Standard',
      description: 'README + API docs',
      includes: ['README.md', 'requirements.txt', 'api_docs.md', 'examples.py']
    },
    {
      id: 'comprehensive',
      name: 'Comprehensive',
      description: 'Full documentation',
      includes: ['README.md', 'requirements.txt', 'api_docs.md', 'examples.py', 'training_guide.md', 'deployment_guide.md']
    }
  ]
};

// Deployment Configuration
export const DEPLOYMENT_CONFIG = {
  // Deployment types
  TYPES: [
    {
      id: 'api',
      name: 'REST API',
      description: 'Deploy as a REST API endpoint',
      features: ['Real-time inference', 'Authentication', 'Rate limiting', 'Auto-scaling'],
      estimatedCost: 'Low to Medium'
    },
    {
      id: 'batch',
      name: 'Batch Processing',
      description: 'Process large datasets in batches',
      features: ['High throughput', 'Cost effective', 'Scheduled processing', 'Large file support'],
      estimatedCost: 'Low'
    },
    {
      id: 'edge',
      name: 'Edge Deployment',
      description: 'Deploy to mobile or edge devices',
      features: ['Offline capability', 'Low latency', 'Privacy focused', 'Optimized models'],
      estimatedCost: 'Very Low'
    }
  ],

  // Cloud providers
  CLOUD_PROVIDERS: [
    {
      id: 'aws',
      name: 'Amazon Web Services',
      services: ['Lambda', 'SageMaker', 'ECS', 'EC2'],
      regions: ['us-east-1', 'us-west-2', 'eu-west-1', 'ap-southeast-1']
    },
    {
      id: 'gcp',
      name: 'Google Cloud Platform',
      services: ['Cloud Functions', 'AI Platform', 'Cloud Run', 'Compute Engine'],
      regions: ['us-central1', 'us-west1', 'europe-west1', 'asia-southeast1']
    },
    {
      id: 'azure',
      name: 'Microsoft Azure',
      services: ['Functions', 'Machine Learning', 'Container Instances', 'Virtual Machines'],
      regions: ['eastus', 'westus2', 'westeurope', 'southeastasia']
    }
  ]
};

// Error Messages
export const ERROR_MESSAGES = {
  TRAINING: {
    INSUFFICIENT_DATA: 'Insufficient training data. Please provide more samples.',
    INVALID_CONFIG: 'Invalid training configuration. Please check your parameters.',
    MODEL_LOAD_FAILED: 'Failed to load the specified model.',
    TRAINING_TIMEOUT: 'Training took too long and was terminated.',
    OUT_OF_MEMORY: 'Training failed due to insufficient memory. Try reducing batch size.',
    VALIDATION_FAILED: 'Training validation failed. Please check your data quality.'
  },
  
  CLASSIFICATION: {
    MODEL_NOT_FOUND: 'The specified model was not found.',
    INVALID_INPUT: 'Invalid input data for classification.',
    CLASSIFICATION_TIMEOUT: 'Classification took too long and was terminated.',
    PROCESSING_ERROR: 'Error occurred during text processing.'
  },
  
  DATA: {
    INVALID_FORMAT: 'Invalid file format. Please use CSV, Excel, or JSON.',
    MISSING_COLUMNS: 'Required columns are missing from the dataset.',
    EMPTY_DATASET: 'The dataset is empty or contains no valid data.',
    ENCODING_ERROR: 'File encoding error. Please ensure UTF-8 encoding.'
  },
  
  NETWORK: {
    CONNECTION_ERROR: 'Network connection error. Please check your internet connection.',
    TIMEOUT: 'Request timed out. Please try again.',
    SERVER_ERROR: 'Server error occurred. Please try again later.',
    RATE_LIMITED: 'Too many requests. Please wait before trying again.'
  }
};

// Success Messages
export const SUCCESS_MESSAGES = {
  TRAINING: {
    STARTED: 'Training started successfully',
    COMPLETED: 'Model training completed successfully',
    SAVED: 'Model saved successfully'
  },
  
  CLASSIFICATION: {
    STARTED: 'Classification started successfully',
    COMPLETED: 'Classification completed successfully'
  },
  
  EXPORT: {
    STARTED: 'Model export started',
    COMPLETED: 'Model exported successfully'
  },
  
  DEPLOYMENT: {
    STARTED: 'Deployment started',
    COMPLETED: 'Model deployed successfully'
  }
};
