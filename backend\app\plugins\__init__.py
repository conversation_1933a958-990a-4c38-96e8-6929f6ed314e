"""Plugin system for ClassyWeb Universal Platform.

This module provides a comprehensive plugin architecture that allows:
- Third-party classification engines
- Custom workflow components
- Data processing plugins
- UI component plugins
- Analytics and reporting plugins
"""

from .plugin_registry import PluginRegistry
from .base_plugin import BasePlugin, ClassificationPlugin, WorkflowPlugin, DataProcessorPlugin
from .plugin_manager import PluginManager
from .plugin_loader import PluginLoader

__all__ = [
    "PluginRegistry",
    "BasePlugin", 
    "ClassificationPlugin",
    "WorkflowPlugin", 
    "DataProcessorPlugin",
    "PluginManager",
    "PluginLoader"
]
