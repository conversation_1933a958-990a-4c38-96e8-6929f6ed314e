/**
 * MultiLabelWorkflow.tsx
 *
 * Complete multi-label classification workflow component following the hierarchical pattern.
 * Implements 7-step workflow with dual data upload, advanced training configuration,
 * model management, and comprehensive deployment options.
 */

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";
import { useToast } from "@/hooks/use-toast";
import {
  Upload,
  Tags,
  Settings,
  Brain,
  Zap,
  BarChart3,
  Download,
  CheckCircle2,
  AlertCircle,
  ArrowLeft,
  ArrowRight,
  Loader2,
  FileText,
  Target,
  Network,
  TrendingUp,
  Info
} from "lucide-react";

// Import components
import { ColumnSelector } from "@/components/ColumnSelector";
import { ResultsDataTable } from "@/components/ResultsDataTable";
import { LLMConfigurationPanel } from "@/components/LLMConfigurationPanel";
import { UnifiedFileUploadZone } from "@/components/UnifiedFileUploadZone";
import { DeployStep } from "./DeployStep";

// Import services
import {
  startUniversalTraining,
  startUniversalInference,
  getUniversalTaskStatus,
  UniversalTrainingRequest,
  UniversalInferenceRequest
} from "@/services/universalApi";
import { getResultData } from "@/services/taskApi";
import { ClassificationResultRow } from "@/types";
import { unifiedDataManager, DataPurpose } from "@/services/unifiedDataManager";
import { downloadResultsCSV, downloadResultsExcel } from "@/services/exportApi";
import { getUserLicense, getLicenseFeatures } from "@/services/licenseApi";

interface MultiLabelWorkflowProps {
  initialData?: any;
  onComplete: (results: any) => void;
}

interface DualDataUpload {
  trainingData: {
    fileId: string;
    fileInfo: any;
    purpose: string;
  };
  classificationData: {
    fileId: string;
    fileInfo: any;
    purpose: string;
  };
  dualUpload: boolean;
}

interface MultiLabelDetection {
  detectedLabels: string[];
  labelCorrelations: Record<string, string[]>;
  labelFrequencies: Record<string, number>;
  suggestedThresholds: Record<string, number>;
}

export const MultiLabelWorkflow: React.FC<MultiLabelWorkflowProps> = ({
  initialData,
  onComplete
}) => {
  // Navigation hooks
  const navigate = useNavigate();
  const location = useLocation();
  const { toast } = useToast();

  // Check if we came from expert workflow
  const fromExpertWorkflow = location.state?.fromExpertWorkflow ||
                            sessionStorage.getItem('expertWorkflowDualData');

  // Core state
  const [currentStep, setCurrentStep] = useState(1);
  const [uploadedData, setUploadedData] = useState<any>(null);
  const [dualData, setDualData] = useState<DualDataUpload | null>(null);
  const [availableColumns, setAvailableColumns] = useState<string[]>([]);
  const [columnInfo, setColumnInfo] = useState<Record<string, any>>({});
  const [selectedTextColumns, setSelectedTextColumns] = useState<string[]>([]);
  const [selectedLabelColumns, setSelectedLabelColumns] = useState<string[]>([]);
  const [errors, setErrors] = useState<string[]>([]);

  // Multi-label specific state
  const [multiLabelDetection, setMultiLabelDetection] = useState<MultiLabelDetection | null>(null);
  const [labelThresholds, setLabelThresholds] = useState<Record<string, number>>({});
  const [correlationAnalysis, setCorrelationAnalysis] = useState<any>(null);
  const [isAnalyzingLabels, setIsAnalyzingLabels] = useState(false);

  // Training state
  const [trainingMethod, setTrainingMethod] = useState<'llm' | 'custom'>('llm');
  const [llmProvider, setLlmProvider] = useState<string>('openai');
  const [llmModel, setLlmModel] = useState<string>('gpt-3.5-turbo');
  const [customPrompt, setCustomPrompt] = useState<string>('');
  const [trainingConfig, setTrainingConfig] = useState<any>(null);
  const [isTraining, setIsTraining] = useState(false);
  const [trainingProgress, setTrainingProgress] = useState(0);
  const [trainingTaskId, setTrainingTaskId] = useState<string>('');
  const [trainingResults, setTrainingResults] = useState<any>(null);
  const [metrics, setMetrics] = useState<any>(null);

  // Classification state
  const [isClassifying, setIsClassifying] = useState(false);
  const [classificationProgress, setClassificationProgress] = useState(0);
  const [classificationResults, setClassificationResults] = useState<ClassificationResultRow[]>([]);

  // License state
  const [userLicense, setUserLicense] = useState<{
    type: 'personal' | 'professional' | 'enterprise';
    features: string[];
    limits: {
      max_deployments?: number;
      max_api_calls_per_month?: number;
      cloud_deployment?: boolean;
      enterprise_features?: boolean;
    };
  }>({
    type: 'personal',
    features: [],
    limits: {}
  });

  // Helper function to analyze column data from uploaded data
  const analyzeColumnData = useCallback((data: any[], columns: string[]) => {
    if (!data || data.length === 0) {
      return columns.reduce((acc: Record<string, any>, col: string) => {
        acc[col] = {
          name: col,
          type: 'string',
          unique_count: 0,
          total_count: 0,
          uniqueness_ratio: 0,
          sample_values: [],
          is_potential_text: col.toLowerCase().includes('text') || col.toLowerCase().includes('content') ||
                           col.toLowerCase().includes('message') || col.toLowerCase().includes('description'),
          is_potential_label: col.toLowerCase().includes('label') || col.toLowerCase().includes('category') ||
                            col.toLowerCase().includes('class') || col.toLowerCase().includes('type')
        };
        return acc;
      }, {});
    }

    return columns.reduce((acc: Record<string, any>, col: string) => {
      const values = data
        .map(row => row[col])
        .filter(val => val !== null && val !== undefined && val !== '');

      const uniqueValues = [...new Set(values)];
      const sampleValues = uniqueValues.slice(0, 5);
      const uniquenessRatio = values.length > 0 ? uniqueValues.length / values.length : 0;

      // Enhanced heuristics for multi-label classification
      const colLower = col.toLowerCase();
      const isTextLike = colLower.includes('text') || colLower.includes('content') ||
                        colLower.includes('message') || colLower.includes('description') ||
                        colLower.includes('comment') || colLower.includes('review');
      const isLabelLike = colLower.includes('label') || colLower.includes('category') ||
                         colLower.includes('class') || colLower.includes('type') ||
                         colLower.includes('sentiment') || colLower.includes('tag');

      // Check if column contains multi-label patterns (comma-separated values, etc.)
      const hasMultiLabelPattern = values.some((val: any) =>
        typeof val === 'string' && (val.includes(',') || val.includes(';') || val.includes('|'))
      );

      acc[col] = {
        name: col,
        type: 'string',
        unique_count: uniqueValues.length,
        total_count: values.length,
        uniqueness_ratio: uniquenessRatio,
        sample_values: sampleValues,
        is_potential_text: isTextLike,
        is_potential_label: isLabelLike || hasMultiLabelPattern
      };
      return acc;
    }, {});
  }, []);

  // Define workflow steps
  const getWorkflowSteps = () => [
    { id: 1, key: 'upload', title: 'Data Upload', icon: Upload, description: 'Upload your dataset' },
    { id: 2, key: 'labels', title: 'Label Setup', icon: Tags, description: 'Configure multi-labels' },
    { id: 3, key: 'configure', title: 'Configuration', icon: Settings, description: 'Set training parameters' },
    { id: 4, key: 'method', title: 'Method Selection', icon: Brain, description: 'Choose training approach' },
    { id: 5, key: 'train', title: 'Training/Inference', icon: Zap, description: 'Train model or run inference' },
    { id: 6, key: 'results', title: 'Results', icon: BarChart3, description: 'View results and metrics' },
    { id: 7, key: 'deploy', title: 'Deploy', icon: Download, description: 'Export and deploy model' }
  ];

  const isCurrentStep = (stepKey: string) => {
    const steps = getWorkflowSteps();
    const step = steps.find(s => s.key === stepKey);
    return step ? currentStep === step.id : false;
  };

  const goToStep = (stepKey: string) => {
    const steps = getWorkflowSteps();
    const step = steps.find(s => s.key === stepKey);
    if (step) {
      setCurrentStep(step.id);
    }
  };

  const steps = getWorkflowSteps().map(step => ({
    ...step,
    status: currentStep === step.id ? "current" : currentStep > step.id ? "complete" : "pending"
  }));

  // Initialize data from expert workflow or start fresh
  useEffect(() => {
    const checkForDualData = () => {
      const storedDualData = sessionStorage.getItem('expertWorkflowDualData');
      if (storedDualData) {
        try {
          const parsedDualData = JSON.parse(storedDualData);
          setDualData(parsedDualData);
          console.log('Dual data detected from expert workflow:', parsedDualData);

          // Set available columns from training data
          if (parsedDualData.trainingData?.fileInfo?.columns) {
            setAvailableColumns(parsedDualData.trainingData.fileInfo.columns);

            // Analyze column data from training data preview
            const trainingDataPreview = parsedDualData.trainingData.fileInfo.preview || [];
            const columnInfo = analyzeColumnData(trainingDataPreview, parsedDualData.trainingData.fileInfo.columns);
            setColumnInfo(columnInfo);
          }

          // Auto-detect multi-labels from training data
          detectMultiLabelsFromTrainingData(parsedDualData.trainingData.fileInfo);

          // Skip to step 2 since we have data
          setCurrentStep(2);
          return;
        } catch (error) {
          console.error('Error parsing dual data:', error);
        }
      }
    };

    checkForDualData();

    // Load user license
    loadUserLicense();

    // Only run fallback initialization if no dual data was found
    if (!sessionStorage.getItem('expertWorkflowDualData') && initialData) {
      setUploadedData(initialData);
      if (initialData.columns) {
        setAvailableColumns(initialData.columns);

        // Analyze column data from initial data
        const initialDataPreview = initialData.data || initialData.preview || [];
        const columnInfo = analyzeColumnData(initialDataPreview, initialData.columns);
        setColumnInfo(columnInfo);
      }
    } else if (!sessionStorage.getItem('expertWorkflowDualData')) {
      // Try to get data from unified data manager
      const allFiles = unifiedDataManager.getAllFiles();
      if (allFiles.length > 0) {
        const latestFile = allFiles[0];
        const fileData = latestFile.fileInfo;
        setUploadedData(fileData);
        if (fileData.columns) {
          setAvailableColumns(fileData.columns);

          // Analyze column data from file data
          const fileDataPreview = fileData.data || fileData.preview || [];
          const columnInfo = analyzeColumnData(fileDataPreview, fileData.columns);
          setColumnInfo(columnInfo);
        }
      }
    }
  }, [initialData]);

  const loadUserLicense = async () => {
    try {
      const license = await getUserLicense();
      setUserLicense(license);
    } catch (error) {
      console.error('Failed to load user license:', error);
    }
  };

  const detectMultiLabelsFromTrainingData = async (fileInfo: any) => {
    if (!fileInfo?.preview || !fileInfo?.columns) return;

    setIsAnalyzingLabels(true);
    try {
      // Analyze the data to detect multi-label patterns
      const preview = fileInfo.preview;
      const columns = fileInfo.columns;

      // Look for columns that might contain multiple labels
      const potentialLabelColumns = columns.filter((col: string) => {
        const sampleValues = preview.slice(0, 10).map((row: any) => row[col]);
        return sampleValues.some((val: any) =>
          typeof val === 'string' && (val.includes(',') || val.includes(';') || val.includes('|'))
        );
      });

      // Analyze label frequencies and correlations
      const labelAnalysis = analyzeLabelData(preview, potentialLabelColumns);

      setMultiLabelDetection(labelAnalysis);
      setSelectedLabelColumns(potentialLabelColumns.slice(0, 5)); // Limit to first 5 detected

      // Set default thresholds
      const defaultThresholds: Record<string, number> = {};
      labelAnalysis.detectedLabels.forEach(label => {
        defaultThresholds[label] = labelAnalysis.suggestedThresholds[label] || 0.5;
      });
      setLabelThresholds(defaultThresholds);

    } catch (error) {
      console.error('Error detecting multi-labels:', error);
      toast({
        title: "Label Detection Failed",
        description: "Could not automatically detect multi-labels. Please configure manually.",
        variant: "destructive"
      });
    } finally {
      setIsAnalyzingLabels(false);
    }
  };

  const analyzeLabelData = (data: any[], labelColumns: string[]): MultiLabelDetection => {
    const detectedLabels: string[] = [];
    const labelFrequencies: Record<string, number> = {};
    const labelCorrelations: Record<string, string[]> = {};
    const suggestedThresholds: Record<string, number> = {};

    // Analyze each label column
    labelColumns.forEach(column => {
      const values = data.map(row => row[column]).filter(val => val);

      values.forEach(val => {
        if (typeof val === 'string') {
          // Split by common delimiters
          const labels = val.split(/[,;|]/).map(l => l.trim()).filter(l => l);
          labels.forEach(label => {
            if (!detectedLabels.includes(label)) {
              detectedLabels.push(label);
            }
            labelFrequencies[label] = (labelFrequencies[label] || 0) + 1;
          });
        }
      });
    });

    // Calculate suggested thresholds based on frequency
    const totalSamples = data.length;
    detectedLabels.forEach(label => {
      const frequency = labelFrequencies[label] || 0;
      const ratio = frequency / totalSamples;
      // Suggest threshold based on label frequency (more frequent = lower threshold)
      suggestedThresholds[label] = Math.max(0.3, Math.min(0.7, 1 - ratio));
    });

    // Simple correlation analysis (labels that appear together)
    detectedLabels.forEach(label1 => {
      const correlatedLabels: string[] = [];
      detectedLabels.forEach(label2 => {
        if (label1 !== label2) {
          // Count co-occurrences
          let coOccurrences = 0;
          data.forEach(row => {
            const rowLabels = labelColumns.flatMap(col => {
              const val = row[col];
              return typeof val === 'string' ? val.split(/[,;|]/).map(l => l.trim()) : [];
            });
            if (rowLabels.includes(label1) && rowLabels.includes(label2)) {
              coOccurrences++;
            }
          });

          // If labels co-occur in more than 20% of cases, consider them correlated
          if (coOccurrences / labelFrequencies[label1] > 0.2) {
            correlatedLabels.push(label2);
          }
        }
      });
      labelCorrelations[label1] = correlatedLabels;
    });

    return {
      detectedLabels,
      labelCorrelations,
      labelFrequencies,
      suggestedThresholds
    };
  };

  // Function to analyze selected columns and update multi-label detection
  const analyzeSelectedColumns = useCallback(() => {
    if (selectedLabelColumns.length === 0) {
      setMultiLabelDetection(null);
      return;
    }

    // Show loading state during analysis
    setIsAnalyzingLabels(true);

    // Get the data source (dual data training data, uploaded data, etc.)
    let dataSource = null;
    if (dualData?.trainingData?.fileInfo?.preview) {
      dataSource = dualData.trainingData.fileInfo.preview;
    } else if (uploadedData?.preview) {
      dataSource = uploadedData.preview;
    } else if (uploadedData?.data) {
      dataSource = uploadedData.data;
    }

    if (!dataSource || dataSource.length === 0) {
      console.warn('No data available for label analysis');
      setMultiLabelDetection(null);
      return;
    }

    console.log('Analyzing selected label columns:', selectedLabelColumns);
    console.log('Using data source with', dataSource.length, 'rows');

    try {
      const labelAnalysis = analyzeLabelData(dataSource, selectedLabelColumns);
      console.log('Label analysis results:', labelAnalysis);
      setMultiLabelDetection(labelAnalysis);

      // Set default thresholds
      const defaultThresholds: Record<string, number> = {};
      labelAnalysis.detectedLabels.forEach(label => {
        defaultThresholds[label] = labelAnalysis.suggestedThresholds[label] || 0.5;
      });
      setLabelThresholds(defaultThresholds);
    } catch (error) {
      console.error('Error analyzing selected columns:', error);
      setMultiLabelDetection(null);
    } finally {
      setIsAnalyzingLabels(false);
    }
  }, [selectedLabelColumns, dualData, uploadedData]);

  // Effect to analyze selected columns when they change
  useEffect(() => {
    analyzeSelectedColumns();
  }, [analyzeSelectedColumns]);

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="border-b bg-card">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              {fromExpertWorkflow && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => navigate('/expert')}
                  className="flex items-center gap-2"
                >
                  <ArrowLeft className="w-4 h-4" />
                  Back to Expert
                </Button>
              )}
              <div>
                <h1 className="text-2xl font-bold">Multi-Label Classification</h1>
                <p className="text-muted-foreground">
                  Classify text with multiple non-exclusive labels
                </p>
              </div>
            </div>
            <Badge variant="secondary" className="flex items-center gap-2">
              <Target className="w-4 h-4" />
              Step {currentStep} of {steps.length}
            </Badge>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-8">
        {errors.length > 0 && (
          <Alert variant="destructive" className="mb-6">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              <ul className="list-disc list-inside">
                {errors.map((error, index) => (
                  <li key={index}>{error}</li>
                ))}
              </ul>
            </AlertDescription>
          </Alert>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Sidebar - Steps */}
          <div className="lg:col-span-1">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Workflow Steps</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {steps.map((step) => {
                    const Icon = step.icon;
                    const isActive = currentStep === step.id;
                    const isComplete = step.status === 'complete';

                    return (
                      <div
                        key={step.id}
                        className={`flex items-center gap-3 p-3 rounded-lg transition-colors cursor-pointer ${
                          isActive ? 'bg-primary/10 border border-primary/20' :
                          isComplete ? 'bg-green-50 border border-green-200' : 'bg-muted/30'
                        }`}
                        onClick={() => {
                          if (isComplete || step.id <= currentStep) {
                            setCurrentStep(step.id);
                          }
                        }}
                      >
                        <div className={`p-2 rounded-full ${
                          isActive ? 'bg-primary text-primary-foreground' :
                          isComplete ? 'bg-green-500 text-white' : 'bg-muted'
                        }`}>
                          {isComplete ? (
                            <CheckCircle2 className="w-4 h-4" />
                          ) : (
                            <Icon className="w-4 h-4" />
                          )}
                        </div>
                        <div className="flex-1">
                          <div className="font-medium">{step.title}</div>
                          <div className="text-sm text-muted-foreground">
                            {step.description}
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Main Content Area */}
          <div className="lg:col-span-3">
            {/* Step 1: Data Upload */}
            {isCurrentStep('upload') && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Upload className="w-5 h-5" />
                    Data Upload
                  </CardTitle>
                  <CardDescription>
                    Upload your multi-label dataset or use data from the Expert Workflow.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {dualData ? (
                    <div className="space-y-4">
                      <Alert>
                        <FileText className="h-4 w-4" />
                        <AlertDescription>
                          Using data from Expert Workflow with dual file setup:
                          <br />
                          <strong>Training Data:</strong> {dualData.trainingData.fileInfo.name}
                          <br />
                          <strong>Classification Data:</strong> {dualData.classificationData.fileInfo.name}
                        </AlertDescription>
                      </Alert>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <Card>
                          <CardHeader>
                            <CardTitle className="text-sm">Training Data</CardTitle>
                          </CardHeader>
                          <CardContent>
                            <div className="text-sm space-y-1">
                              <div><strong>File:</strong> {dualData.trainingData.fileInfo.name}</div>
                              <div><strong>Rows:</strong> {dualData.trainingData.fileInfo.rows?.toLocaleString()}</div>
                              <div><strong>Columns:</strong> {dualData.trainingData.fileInfo.columns?.length}</div>
                            </div>
                          </CardContent>
                        </Card>

                        <Card>
                          <CardHeader>
                            <CardTitle className="text-sm">Classification Data</CardTitle>
                          </CardHeader>
                          <CardContent>
                            <div className="text-sm space-y-1">
                              <div><strong>File:</strong> {dualData.classificationData.fileInfo.name}</div>
                              <div><strong>Rows:</strong> {dualData.classificationData.fileInfo.rows?.toLocaleString()}</div>
                              <div><strong>Columns:</strong> {dualData.classificationData.fileInfo.columns?.length}</div>
                            </div>
                          </CardContent>
                        </Card>
                      </div>
                    </div>
                  ) : (
                    <UnifiedFileUploadZone
                      onFileSelected={(fileId, fileInfo, purposes) => {
                        console.log('File selected:', fileInfo);
                        setUploadedData(fileInfo);

                        if (fileInfo.columns) {
                          setAvailableColumns(fileInfo.columns);

                          // Analyze column data from file preview
                          const fileDataPreview = fileInfo.preview || fileInfo.data || [];
                          const columnInfo = analyzeColumnData(fileDataPreview, fileInfo.columns);
                          setColumnInfo(columnInfo);
                        }

                        toast({
                          title: "File uploaded successfully",
                          description: `Uploaded ${fileInfo.name} with ${fileInfo.rows} rows`
                        });
                      }}
                      onFileRemoved={() => {
                        setUploadedData(null);
                        setAvailableColumns([]);
                        setColumnInfo({});
                      }}
                      requiredPurposes={['training', 'classification']}
                      allowMultiplePurposes={true}
                      title="Upload Multi-Label Dataset"
                      description="Upload your dataset containing text and multiple labels"
                    />
                  )}

                  {(uploadedData || dualData) && (
                    <div className="mt-6 flex justify-end">
                      <Button onClick={() => setCurrentStep(2)}>
                        Continue to Label Setup
                        <ArrowRight className="w-4 h-4 ml-2" />
                      </Button>
                    </div>
                  )}
                </CardContent>
              </Card>
            )}

            {/* Step 2: Label Setup */}
            {isCurrentStep('labels') && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Tags className="w-5 h-5" />
                    Multi-Label Setup
                  </CardTitle>
                  <CardDescription>
                    Configure your multi-label classification setup and analyze label relationships.
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  {isAnalyzingLabels && (
                    <Alert>
                      <Loader2 className="h-4 w-4 animate-spin" />
                      <AlertDescription>
                        Analyzing label patterns and correlations in your data...
                      </AlertDescription>
                    </Alert>
                  )}

                  {/* Column Selection */}
                  <div className="space-y-4">
                    <ColumnSelector
                      columns={columnInfo}
                      selectedTextColumns={selectedTextColumns}
                      selectedLabelColumns={selectedLabelColumns}
                      onTextColumnsChange={setSelectedTextColumns}
                      onLabelColumnsChange={setSelectedLabelColumns}
                      showContinueButton={false}
                      showSkipButton={false}
                    />

                    {/* Show message when no label columns selected */}
                    {selectedLabelColumns.length === 0 && !isAnalyzingLabels && (
                      <Alert>
                        <Info className="h-4 w-4" />
                        <AlertDescription>
                          Please select one or more label columns above to see the label analysis results.
                        </AlertDescription>
                      </Alert>
                    )}

                    {/* Multi-label Detection Results */}
                    {multiLabelDetection && (
                      <div className="space-y-4">
                        <Separator />
                        <div>
                          <h3 className="text-lg font-semibold mb-2 flex items-center gap-2">
                            <Network className="w-5 h-5" />
                            Label Analysis Results
                          </h3>

                          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <Card>
                              <CardHeader>
                                <CardTitle className="text-sm">Detected Labels</CardTitle>
                              </CardHeader>
                              <CardContent>
                                <div className="text-2xl font-bold text-primary">
                                  {multiLabelDetection.detectedLabels.length}
                                </div>
                                <div className="text-sm text-muted-foreground">
                                  Unique labels found
                                </div>
                              </CardContent>
                            </Card>

                            <Card>
                              <CardHeader>
                                <CardTitle className="text-sm">Label Correlations</CardTitle>
                              </CardHeader>
                              <CardContent>
                                <div className="text-2xl font-bold text-blue-600">
                                  {Object.values(multiLabelDetection.labelCorrelations).flat().length}
                                </div>
                                <div className="text-sm text-muted-foreground">
                                  Correlation pairs
                                </div>
                              </CardContent>
                            </Card>

                            <Card>
                              <CardHeader>
                                <CardTitle className="text-sm">Avg. Frequency</CardTitle>
                              </CardHeader>
                              <CardContent>
                                <div className="text-2xl font-bold text-green-600">
                                  {Math.round(
                                    Object.values(multiLabelDetection.labelFrequencies).reduce((a, b) => a + b, 0) /
                                    multiLabelDetection.detectedLabels.length
                                  )}
                                </div>
                                <div className="text-sm text-muted-foreground">
                                  Occurrences per label
                                </div>
                              </CardContent>
                            </Card>
                          </div>

                          {/* Label List with Frequencies */}
                          <div className="mt-4">
                            <h4 className="font-medium mb-2">Label Frequencies</h4>
                            <div className="max-h-40 overflow-y-auto space-y-1">
                              {multiLabelDetection.detectedLabels.map(label => (
                                <div key={label} className="flex justify-between items-center p-2 bg-muted/30 rounded">
                                  <span className="text-sm">{label}</span>
                                  <Badge variant="secondary">
                                    {multiLabelDetection.labelFrequencies[label]} occurrences
                                  </Badge>
                                </div>
                              ))}
                            </div>
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Manual Label Configuration */}
                    {!multiLabelDetection && selectedLabelColumns.length > 0 && (
                      <div className="space-y-4">
                        <Separator />
                        <div>
                          <h3 className="text-lg font-semibold mb-2">Manual Label Configuration</h3>
                          <Alert>
                            <TrendingUp className="h-4 w-4" />
                            <AlertDescription>
                              Configure thresholds and settings for each label column manually.
                            </AlertDescription>
                          </Alert>
                        </div>
                      </div>
                    )}
                  </div>

                  {(selectedTextColumns.length > 0 && selectedLabelColumns.length > 0) && (
                    <div className="flex justify-end">
                      <Button onClick={() => setCurrentStep(3)}>
                        Continue to Configuration
                        <ArrowRight className="w-4 h-4 ml-2" />
                      </Button>
                    </div>
                  )}
                </CardContent>
              </Card>
            )}

            {/* Step 3: Configuration - Placeholder for now */}
            {isCurrentStep('configure') && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Settings className="w-5 h-5" />
                    Configuration
                  </CardTitle>
                  <CardDescription>
                    Configure training parameters and multi-label specific settings.
                  </CardDescription>
                </CardHeader>
                <CardContent className="p-8 text-center">
                  <div className="w-16 h-16 rounded-full bg-primary/10 flex items-center justify-center mx-auto mb-4">
                    <Settings className="w-8 h-8 text-primary" />
                  </div>
                  <h3 className="text-xl font-semibold mb-2">Configuration Step</h3>
                  <p className="text-muted-foreground mb-4">
                    Multi-label training configuration will be implemented here.
                  </p>
                  <Button onClick={() => setCurrentStep(4)}>
                    Continue to Method Selection
                    <ArrowRight className="w-4 h-4 ml-2" />
                  </Button>
                </CardContent>
              </Card>
            )}

            {/* Step 7: Deploy */}
            {isCurrentStep('deploy') && (
              <DeployStep
                modelId={trainingTaskId}
                modelName={`Multi-Label Model ${trainingTaskId ? trainingTaskId.slice(-8) : ''}`}
                classificationType="multi-label"
                trainingMetrics={{
                  f1_macro: metrics?.f1_macro || 0,
                  f1_micro: metrics?.f1_micro || 0,
                  hamming_loss: metrics?.hamming_loss || 0,
                  jaccard_score: metrics?.jaccard_score || 0,
                  subset_accuracy: metrics?.subset_accuracy || 0,
                  training_time: metrics?.training_time || 0,
                  model_size_mb: metrics?.model_size_mb || 0
                }}
                labels={multiLabelDetection?.detectedLabels || selectedLabelColumns}
                labelThresholds={labelThresholds}
                labelCorrelations={multiLabelDetection?.labelCorrelations || {}}
                userLicense={userLicense}
                onComplete={(deploymentInfo) => {
                  console.log('Deployment completed:', deploymentInfo);
                  onComplete({
                    type: 'multi-label',
                    taskId: trainingTaskId,
                    results: trainingResults,
                    metrics: metrics,
                    deployment: deploymentInfo
                  });
                }}
                onBack={() => goToStep('results')}
              />
            )}

            {/* Remaining steps - Placeholders for now */}
            {(isCurrentStep('method') || isCurrentStep('train') || isCurrentStep('results')) && (
              <Card>
                <CardContent className="p-8 text-center">
                  <div className="w-16 h-16 rounded-full bg-primary/10 flex items-center justify-center mx-auto mb-4">
                    <Tags className="w-8 h-8 text-primary" />
                  </div>
                  <h3 className="text-xl font-semibold mb-2">Step {currentStep} Implementation</h3>
                  <p className="text-muted-foreground mb-4">
                    This step is being implemented. The multi-label classification workflow will continue here.
                  </p>
                  <div className="flex gap-3 justify-center">
                    <Button variant="outline" onClick={() => setCurrentStep(Math.max(1, currentStep - 1))}>
                      <ArrowLeft className="w-4 h-4 mr-2" />
                      Previous
                    </Button>
                    <Button onClick={() => setCurrentStep(Math.min(steps.length, currentStep + 1))}>
                      Next Step
                      <ArrowRight className="w-4 h-4 ml-2" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Navigation */}
            <div className="mt-8 flex justify-between">
              <Button
                variant="outline"
                onClick={() => setCurrentStep(Math.max(1, currentStep - 1))}
                disabled={currentStep === 1}
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Previous
              </Button>

              <Button
                onClick={() => setCurrentStep(Math.min(steps.length, currentStep + 1))}
                disabled={currentStep === steps.length}
              >
                Next
                <ArrowRight className="w-4 h-4 ml-2" />
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
