/**
 * ThresholdOptimizer.tsx
 * 
 * Advanced threshold optimization component with multiple strategies.
 * Supports <PERSON><PERSON>'s J, F1 optimization, precision-recall balance, and custom objectives.
 */

import React, { useState, useMemo, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON>lider } from "@/components/ui/slider";
import { Ta<PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  Target, 
  TrendingUp, 
  Scale, 
  Zap,
  Settings,
  Info,
  CheckCircle2,
  AlertTriangle,
  BarChart3
} from "lucide-react";
import { 
  LineChart, 
  Line, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  ReferenceLine,
  Scatter<PERSON><PERSON>,
  <PERSON>att<PERSON>
} from 'recharts';

interface ThresholdPoint {
  threshold: number;
  tpr: number; // True Positive Rate (Sensitivity)
  fpr: number; // False Positive Rate
  tnr: number; // True Negative Rate (Specificity)
  fnr: number; // False Negative Rate
  precision: number;
  recall: number;
  f1_score: number;
  accuracy: number;
  youden_j: number; // Youden's J statistic
  tp: number; // True Positives
  fp: number; // False Positives
  tn: number; // True Negatives
  fn: number; // False Negatives
}

interface OptimizationStrategy {
  id: string;
  name: string;
  description: string;
  metric: keyof ThresholdPoint;
  objective: 'maximize' | 'minimize';
  icon: React.ComponentType<any>;
  color: string;
}

interface ThresholdOptimizerProps {
  thresholdData: ThresholdPoint[];
  currentThreshold?: number;
  onThresholdChange?: (threshold: number) => void;
  onOptimalThresholdSelect?: (threshold: number, strategy: string) => void;
  showAdvancedMetrics?: boolean;
  costMatrix?: {
    tp_cost: number;
    fp_cost: number;
    tn_cost: number;
    fn_cost: number;
  };
}

const OPTIMIZATION_STRATEGIES: OptimizationStrategy[] = [
  {
    id: 'youden',
    name: "Youden's J",
    description: 'Maximizes sensitivity + specificity - 1. Balances true positive and true negative rates.',
    metric: 'youden_j',
    objective: 'maximize',
    icon: Scale,
    color: '#3b82f6'
  },
  {
    id: 'f1',
    name: 'F1 Score',
    description: 'Maximizes the harmonic mean of precision and recall. Good for balanced performance.',
    metric: 'f1_score',
    objective: 'maximize',
    icon: Target,
    color: '#22c55e'
  },
  {
    id: 'accuracy',
    name: 'Accuracy',
    description: 'Maximizes overall classification accuracy. May not work well with imbalanced data.',
    metric: 'accuracy',
    objective: 'maximize',
    icon: CheckCircle2,
    color: '#8b5cf6'
  },
  {
    id: 'precision',
    name: 'Precision',
    description: 'Maximizes precision (minimize false positives). Use when false positives are costly.',
    metric: 'precision',
    objective: 'maximize',
    icon: TrendingUp,
    color: '#f59e0b'
  },
  {
    id: 'recall',
    name: 'Recall',
    description: 'Maximizes recall/sensitivity (minimize false negatives). Use when false negatives are costly.',
    metric: 'recall',
    objective: 'maximize',
    icon: Zap,
    color: '#ef4444'
  }
];

export const ThresholdOptimizer: React.FC<ThresholdOptimizerProps> = ({
  thresholdData,
  currentThreshold = 0.5,
  onThresholdChange,
  onOptimalThresholdSelect,
  showAdvancedMetrics = true,
  costMatrix
}) => {
  const [selectedStrategy, setSelectedStrategy] = useState<string>('youden');
  const [customWeights, setCustomWeights] = useState({ precision: 0.5, recall: 0.5 });
  const [showCostAnalysis, setShowCostAnalysis] = useState(false);

  // Find optimal thresholds for each strategy
  const optimalThresholds = useMemo(() => {
    const results: Record<string, { threshold: number; value: number; point: ThresholdPoint }> = {};

    OPTIMIZATION_STRATEGIES.forEach(strategy => {
      const optimal = thresholdData.reduce((best, current) => {
        const currentValue = current[strategy.metric] as number;
        const bestValue = best[strategy.metric] as number;
        
        return strategy.objective === 'maximize' 
          ? (currentValue > bestValue ? current : best)
          : (currentValue < bestValue ? current : best);
      });

      results[strategy.id] = {
        threshold: optimal.threshold,
        value: optimal[strategy.metric] as number,
        point: optimal
      };
    });

    return results;
  }, [thresholdData]);

  // Calculate custom weighted F-beta score
  const customOptimal = useMemo(() => {
    const beta = Math.sqrt(customWeights.recall / customWeights.precision);
    
    const optimal = thresholdData.reduce((best, current) => {
      const currentFBeta = (1 + beta * beta) * current.precision * current.recall / 
                          (beta * beta * current.precision + current.recall);
      const bestFBeta = (1 + beta * beta) * best.precision * best.recall / 
                       (beta * beta * best.precision + best.recall);
      
      return currentFBeta > bestFBeta ? current : best;
    });

    return optimal;
  }, [thresholdData, customWeights]);

  // Calculate cost-based optimal threshold
  const costOptimal = useMemo(() => {
    if (!costMatrix) return null;

    const optimal = thresholdData.reduce((best, current) => {
      const currentCost = current.tp * costMatrix.tp_cost + 
                         current.fp * costMatrix.fp_cost + 
                         current.tn * costMatrix.tn_cost + 
                         current.fn * costMatrix.fn_cost;
      
      const bestCost = best.tp * costMatrix.tp_cost + 
                      best.fp * costMatrix.fp_cost + 
                      best.tn * costMatrix.tn_cost + 
                      best.fn * costMatrix.fn_cost;

      return currentCost < bestCost ? current : best;
    });

    return optimal;
  }, [thresholdData, costMatrix]);

  // Get current threshold point
  const currentPoint = useMemo(() => {
    return thresholdData.reduce((prev, curr) => 
      Math.abs(curr.threshold - currentThreshold) < Math.abs(prev.threshold - currentThreshold) 
        ? curr : prev
    );
  }, [thresholdData, currentThreshold]);

  // Prepare chart data
  const chartData = useMemo(() => {
    return thresholdData.map(point => ({
      threshold: point.threshold,
      youden_j: point.youden_j,
      f1_score: point.f1_score,
      accuracy: point.accuracy,
      precision: point.precision,
      recall: point.recall,
      specificity: point.tnr
    }));
  }, [thresholdData]);

  const handleStrategySelect = useCallback((strategyId: string) => {
    setSelectedStrategy(strategyId);
    const optimal = optimalThresholds[strategyId];
    if (optimal && onOptimalThresholdSelect) {
      onOptimalThresholdSelect(optimal.threshold, strategyId);
    }
  }, [optimalThresholds, onOptimalThresholdSelect]);

  const formatMetric = (value: number, isPercentage = true) => {
    if (isNaN(value)) return 'N/A';
    return isPercentage ? `${(value * 100).toFixed(1)}%` : value.toFixed(3);
  };

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-background border rounded-lg p-3 shadow-lg">
          <p className="font-medium">{`Threshold: ${label?.toFixed(3)}`}</p>
          {payload.map((entry: any, index: number) => (
            <p key={index} style={{ color: entry.color }}>
              {`${entry.name}: ${formatMetric(entry.value)}`}
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  if (thresholdData.length === 0) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <Target className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">No Threshold Data Available</h3>
          <p className="text-muted-foreground">
            Train a binary classification model to optimize thresholds.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Current Threshold Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Target className="w-5 h-5" />
            Threshold Optimization
          </CardTitle>
          <CardDescription>
            Find the optimal classification threshold using different strategies
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Current Threshold Slider */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <label className="text-sm font-medium">Current Threshold</label>
              <Badge variant="outline">{currentThreshold.toFixed(3)}</Badge>
            </div>
            <Slider
              value={[currentThreshold]}
              onValueChange={(value) => onThresholdChange?.(value[0])}
              min={0}
              max={1}
              step={0.001}
              className="w-full"
            />
          </div>

          {/* Current Performance */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center p-3 bg-muted/50 rounded-lg">
              <div className="text-lg font-bold text-blue-600">
                {formatMetric(currentPoint.accuracy)}
              </div>
              <div className="text-xs text-muted-foreground">Accuracy</div>
            </div>
            <div className="text-center p-3 bg-muted/50 rounded-lg">
              <div className="text-lg font-bold text-green-600">
                {formatMetric(currentPoint.f1_score)}
              </div>
              <div className="text-xs text-muted-foreground">F1 Score</div>
            </div>
            <div className="text-center p-3 bg-muted/50 rounded-lg">
              <div className="text-lg font-bold text-orange-600">
                {formatMetric(currentPoint.precision)}
              </div>
              <div className="text-xs text-muted-foreground">Precision</div>
            </div>
            <div className="text-center p-3 bg-muted/50 rounded-lg">
              <div className="text-lg font-bold text-red-600">
                {formatMetric(currentPoint.recall)}
              </div>
              <div className="text-xs text-muted-foreground">Recall</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Optimization Strategies */}
      <Card>
        <CardHeader>
          <CardTitle>Optimization Strategies</CardTitle>
          <CardDescription>
            Choose the best strategy based on your use case requirements
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {OPTIMIZATION_STRATEGIES.map((strategy) => {
              const optimal = optimalThresholds[strategy.id];
              const Icon = strategy.icon;
              const isSelected = selectedStrategy === strategy.id;

              return (
                <div
                  key={strategy.id}
                  className={`p-4 border rounded-lg cursor-pointer transition-all ${
                    isSelected ? 'border-primary bg-primary/5' : 'hover:bg-muted/50'
                  }`}
                  onClick={() => handleStrategySelect(strategy.id)}
                >
                  <div className="flex items-center gap-3 mb-3">
                    <div 
                      className="w-10 h-10 rounded-full flex items-center justify-center"
                      style={{ backgroundColor: `${strategy.color}20`, color: strategy.color }}
                    >
                      <Icon className="w-5 h-5" />
                    </div>
                    <div>
                      <h4 className="font-medium">{strategy.name}</h4>
                      <Badge variant="outline" className="text-xs">
                        {formatMetric(optimal.value)}
                      </Badge>
                    </div>
                  </div>
                  <p className="text-sm text-muted-foreground mb-3">
                    {strategy.description}
                  </p>
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-muted-foreground">Optimal Threshold:</span>
                    <span className="font-medium">{optimal.threshold.toFixed(3)}</span>
                  </div>
                  {isSelected && (
                    <Button 
                      size="sm" 
                      className="w-full mt-3"
                      onClick={(e) => {
                        e.stopPropagation();
                        onThresholdChange?.(optimal.threshold);
                      }}
                    >
                      Apply This Threshold
                    </Button>
                  )}
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Threshold Performance Chart */}
      <Card>
        <CardHeader>
          <CardTitle>Performance vs Threshold</CardTitle>
          <CardDescription>
            How different metrics change across threshold values
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="metrics" className="space-y-4">
            <TabsList>
              <TabsTrigger value="metrics">Key Metrics</TabsTrigger>
              <TabsTrigger value="tradeoffs">Precision-Recall Tradeoff</TabsTrigger>
              <TabsTrigger value="roc">ROC Space</TabsTrigger>
            </TabsList>

            <TabsContent value="metrics">
              <ResponsiveContainer width="100%" height={400}>
                <LineChart data={chartData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis 
                    dataKey="threshold" 
                    label={{ value: 'Threshold', position: 'insideBottom', offset: -10 }}
                  />
                  <YAxis />
                  <Tooltip content={<CustomTooltip />} />
                  <ReferenceLine x={currentThreshold} stroke="#94a3b8" strokeDasharray="5 5" />
                  
                  <Line type="monotone" dataKey="accuracy" stroke="#3b82f6" strokeWidth={2} dot={false} name="Accuracy" />
                  <Line type="monotone" dataKey="f1_score" stroke="#22c55e" strokeWidth={2} dot={false} name="F1 Score" />
                  <Line type="monotone" dataKey="precision" stroke="#f59e0b" strokeWidth={2} dot={false} name="Precision" />
                  <Line type="monotone" dataKey="recall" stroke="#ef4444" strokeWidth={2} dot={false} name="Recall" />
                  <Line type="monotone" dataKey="youden_j" stroke="#8b5cf6" strokeWidth={2} dot={false} name="Youden's J" />
                </LineChart>
              </ResponsiveContainer>
            </TabsContent>

            <TabsContent value="tradeoffs">
              <ResponsiveContainer width="100%" height={400}>
                <ScatterChart data={thresholdData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis 
                    dataKey="recall" 
                    label={{ value: 'Recall', position: 'insideBottom', offset: -10 }}
                  />
                  <YAxis 
                    dataKey="precision"
                    label={{ value: 'Precision', angle: -90, position: 'insideLeft' }}
                  />
                  <Tooltip 
                    formatter={(value, name) => [formatMetric(value as number), name]}
                    labelFormatter={(label) => `Threshold: ${label}`}
                  />
                  <Scatter 
                    dataKey="precision" 
                    fill="#3b82f6"
                    name="Precision vs Recall"
                  />
                </ScatterChart>
              </ResponsiveContainer>
            </TabsContent>

            <TabsContent value="roc">
              <ResponsiveContainer width="100%" height={400}>
                <ScatterChart data={thresholdData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis 
                    dataKey="fpr" 
                    label={{ value: 'False Positive Rate', position: 'insideBottom', offset: -10 }}
                  />
                  <YAxis 
                    dataKey="tpr"
                    label={{ value: 'True Positive Rate', angle: -90, position: 'insideLeft' }}
                  />
                  <Tooltip 
                    formatter={(value, name) => [formatMetric(value as number), name]}
                    labelFormatter={(label) => `Threshold: ${label}`}
                  />
                  <Scatter 
                    dataKey="tpr" 
                    fill="#22c55e"
                    name="ROC Space"
                  />
                  <ReferenceLine 
                    segment={[{ x: 0, y: 0 }, { x: 1, y: 1 }]} 
                    stroke="#94a3b8" 
                    strokeDasharray="5 5"
                  />
                </ScatterChart>
              </ResponsiveContainer>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Advanced Options */}
      {showAdvancedMetrics && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="w-5 h-5" />
              Advanced Optimization
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Custom Weighted F-Beta */}
            <div className="space-y-4">
              <h4 className="font-medium">Custom Precision-Recall Balance</h4>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label className="text-sm">Precision Weight: {customWeights.precision.toFixed(2)}</label>
                  <Slider
                    value={[customWeights.precision]}
                    onValueChange={(value) => setCustomWeights(prev => ({ ...prev, precision: value[0] }))}
                    min={0.1}
                    max={0.9}
                    step={0.1}
                  />
                </div>
                <div className="space-y-2">
                  <label className="text-sm">Recall Weight: {customWeights.recall.toFixed(2)}</label>
                  <Slider
                    value={[customWeights.recall]}
                    onValueChange={(value) => setCustomWeights(prev => ({ ...prev, recall: value[0] }))}
                    min={0.1}
                    max={0.9}
                    step={0.1}
                  />
                </div>
              </div>
              <div className="p-3 bg-muted/50 rounded-lg">
                <div className="flex justify-between items-center">
                  <span className="text-sm">Custom Optimal Threshold:</span>
                  <Badge variant="outline">{customOptimal.threshold.toFixed(3)}</Badge>
                </div>
              </div>
            </div>

            {/* Cost Matrix Analysis */}
            {costMatrix && (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h4 className="font-medium">Cost-Based Optimization</h4>
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => setShowCostAnalysis(!showCostAnalysis)}
                  >
                    {showCostAnalysis ? 'Hide' : 'Show'} Analysis
                  </Button>
                </div>
                
                {showCostAnalysis && costOptimal && (
                  <div className="p-4 border rounded-lg">
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                      <div className="text-center">
                        <div className="text-sm text-muted-foreground">TP Cost</div>
                        <div className="font-medium">{costMatrix.tp_cost}</div>
                      </div>
                      <div className="text-center">
                        <div className="text-sm text-muted-foreground">FP Cost</div>
                        <div className="font-medium">{costMatrix.fp_cost}</div>
                      </div>
                      <div className="text-center">
                        <div className="text-sm text-muted-foreground">TN Cost</div>
                        <div className="font-medium">{costMatrix.tn_cost}</div>
                      </div>
                      <div className="text-center">
                        <div className="text-sm text-muted-foreground">FN Cost</div>
                        <div className="font-medium">{costMatrix.fn_cost}</div>
                      </div>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm">Cost-Optimal Threshold:</span>
                      <Badge variant="outline">{costOptimal.threshold.toFixed(3)}</Badge>
                    </div>
                  </div>
                )}
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Recommendations */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Info className="w-5 h-5" />
            Recommendations
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <Alert>
              <CheckCircle2 className="h-4 w-4" />
              <AlertDescription>
                <strong>Best Overall:</strong> Use Youden's J ({optimalThresholds.youden?.threshold.toFixed(3)}) 
                for balanced sensitivity and specificity.
              </AlertDescription>
            </Alert>
            
            <Alert>
              <Target className="h-4 w-4" />
              <AlertDescription>
                <strong>Best F1:</strong> Use F1 optimization ({optimalThresholds.f1?.threshold.toFixed(3)}) 
                for balanced precision and recall.
              </AlertDescription>
            </Alert>
            
            {currentPoint.precision < 0.7 && (
              <Alert variant="destructive">
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  <strong>Low Precision Warning:</strong> Current threshold results in high false positive rate. 
                  Consider increasing threshold to {optimalThresholds.precision?.threshold.toFixed(3)}.
                </AlertDescription>
              </Alert>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ThresholdOptimizer;
