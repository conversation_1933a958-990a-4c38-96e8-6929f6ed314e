#!/usr/bin/env python3
"""
Test script to verify that hierarchical classification returns individual level columns.
"""

import json
import sys
import os

def test_hierarchy_columns():
    """Test that the results contain individual hierarchy level columns."""
    print("Testing hierarchy column structure...")
    
    # Check existing results file
    results_file = "backend/results/hierarchical_results_24dbd677-03c5-4e09-9dd0-63e84557b97e.json"
    
    if not os.path.exists(results_file):
        print(f"❌ Results file not found: {results_file}")
        return False
    
    try:
        with open(results_file, 'r') as f:
            data = json.load(f)
        
        print(f"✅ Loaded results file: {results_file}")
        
        # Check structure
        if 'results' not in data:
            print("❌ No 'results' key found in data")
            return False
        
        results = data['results']
        if not results:
            print("❌ No results found")
            return False
        
        # Check first result
        first_result = results[0]
        print(f"📊 First result keys: {list(first_result.keys())}")
        
        # Check for hierarchy levels
        hierarchy_levels = data.get('hierarchy_levels', [])
        print(f"📋 Hierarchy levels: {hierarchy_levels}")
        
        # Check for individual level columns
        has_individual_columns = False
        has_llm_columns = False
        
        for level in hierarchy_levels:
            if level in first_result:
                has_individual_columns = True
                print(f"✅ Found individual column: {level} = {first_result[level]}")
            
            llm_column = f"LLM_{level}"
            if llm_column in first_result:
                has_llm_columns = True
                print(f"✅ Found LLM column: {llm_column} = {first_result[llm_column]}")
        
        # Check for hierarchy_path
        if 'hierarchy_path' in first_result:
            print(f"✅ Found hierarchy_path: {first_result['hierarchy_path']}")
        
        # Check for predictions
        if 'predictions' in first_result:
            print(f"✅ Found predictions: {first_result['predictions']}")
        
        # Summary
        print("\n📈 Summary:")
        print(f"  - Individual level columns: {'✅' if has_individual_columns else '❌'}")
        print(f"  - LLM level columns: {'✅' if has_llm_columns else '❌'}")
        print(f"  - Hierarchy path: {'✅' if 'hierarchy_path' in first_result else '❌'}")
        print(f"  - Predictions array: {'✅' if 'predictions' in first_result else '❌'}")
        
        # Show what the ideal structure should look like
        print("\n🎯 Ideal structure for frontend display:")
        print("Columns should include:")
        print("  - text")
        for level in hierarchy_levels:
            print(f"  - {level}")
        print("  - confidence")
        print("  - hierarchy_path (for backward compatibility)")
        
        return has_individual_columns or has_llm_columns
        
    except Exception as e:
        print(f"❌ Error reading results file: {e}")
        return False

def main():
    """Run the test."""
    print("🧪 Testing Hierarchy Column Structure")
    print("=" * 50)
    
    success = test_hierarchy_columns()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 Test passed! Individual hierarchy columns are available.")
        print("💡 The frontend should display these columns instead of just hierarchy_path.")
    else:
        print("❌ Test failed! Individual hierarchy columns are missing.")
        print("🔧 The backend needs to be updated to include individual level columns.")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
