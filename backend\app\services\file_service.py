"""File service for handling file uploads and management."""

import logging
import os
import uuid
from pathlib import Path
from typing import Op<PERSON>
from fastapi import UploadFile

from ..config import UPLOAD_DIR

logger = logging.getLogger(__name__)

async def save_uploaded_file(file: UploadFile, user_id: int) -> str:
    """Save uploaded file to disk and return the file path."""
    try:
        # Create user-specific directory
        user_dir = UPLOAD_DIR / str(user_id)
        user_dir.mkdir(parents=True, exist_ok=True)
        
        # Generate unique filename
        file_extension = Path(file.filename).suffix if file.filename else ""
        unique_filename = f"{uuid.uuid4()}{file_extension}"
        file_path = user_dir / unique_filename
        
        # Save file
        with open(file_path, "wb") as buffer:
            content = await file.read()
            buffer.write(content)
        
        logger.info(f"File saved: {file_path}")
        return str(file_path)
        
    except Exception as e:
        logger.error(f"Error saving uploaded file: {e}")
        raise

def get_file_path(file_id: str, user_id: int) -> Optional[str]:
    """Get file path by file ID and user ID."""
    try:
        user_dir = UPLOAD_DIR / str(user_id)
        
        # Search for file with matching ID
        for file_path in user_dir.glob(f"{file_id}*"):
            if file_path.is_file():
                return str(file_path)
        
        return None
        
    except Exception as e:
        logger.error(f"Error getting file path for {file_id}: {e}")
        return None

def delete_file(file_path: str) -> bool:
    """Delete file from disk."""
    try:
        path = Path(file_path)
        if path.exists():
            path.unlink()
            logger.info(f"File deleted: {file_path}")
            return True
        else:
            logger.warning(f"File not found: {file_path}")
            return False
            
    except Exception as e:
        logger.error(f"Error deleting file {file_path}: {e}")
        return False

def get_file_size(file_path: str) -> int:
    """Get file size in bytes."""
    try:
        path = Path(file_path)
        if path.exists():
            return path.stat().st_size
        return 0
        
    except Exception as e:
        logger.error(f"Error getting file size for {file_path}: {e}")
        return 0

def file_exists(file_path: str) -> bool:
    """Check if file exists."""
    try:
        return Path(file_path).exists()
    except Exception as e:
        logger.error(f"Error checking file existence for {file_path}: {e}")
        return False
