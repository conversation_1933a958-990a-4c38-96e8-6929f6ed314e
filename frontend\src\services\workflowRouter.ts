/**
 * workflowRouter.ts
 * 
 * Dynamic workflow router for ClassyWeb Phase 4 implementation
 * Routes users to appropriate workflows based on classification type and user experience level
 */

import { NavigateFunction } from 'react-router-dom';

export type ClassificationType = 'binary' | 'multiclass' | 'multilabel' | 'hierarchical' | 'flat';
export type UserExperienceLevel = 'beginner' | 'expert';
export type WorkflowMode = 'guided' | 'advanced' | 'custom';

export interface WorkflowRouteConfig {
  classificationType: ClassificationType;
  experienceLevel: UserExperienceLevel;
  mode: WorkflowMode;
  route: string;
  title: string;
  description: string;
  estimatedTime: string;
  features: string[];
  requirements: string[];
}

export interface WorkflowContext {
  fileId?: string;
  step?: number;
  config?: Record<string, any>;
  resumeData?: Record<string, any>;
}

/**
 * Workflow route configurations
 */
export const WORKFLOW_ROUTES: Record<string, WorkflowRouteConfig> = {
  // Beginner workflows - guided experience
  'beginner-binary': {
    classificationType: 'binary',
    experienceLevel: 'beginner',
    mode: 'guided',
    route: '/classification/binary?mode=guided',
    title: 'Guided Binary Classification',
    description: 'Step-by-step binary classification with smart recommendations',
    estimatedTime: '10-15 minutes',
    features: ['Smart detection', 'Auto-configuration', 'Real-time guidance'],
    requirements: ['Labeled data with 2 classes']
  },
  'beginner-multiclass': {
    classificationType: 'multiclass',
    experienceLevel: 'beginner',
    mode: 'guided',
    route: '/classification/multiclass?mode=guided',
    title: 'Guided Multi-Class Classification',
    description: 'Step-by-step multi-class classification with class balance analysis',
    estimatedTime: '15-20 minutes',
    features: ['Class balance analysis', 'Strategy recommendations', 'Performance optimization'],
    requirements: ['Labeled data with 3+ classes']
  },
  'beginner-multilabel': {
    classificationType: 'multilabel',
    experienceLevel: 'beginner',
    mode: 'guided',
    route: '/classification/multilabel?mode=guided',
    title: 'Guided Multi-Label Classification',
    description: 'Step-by-step multi-label classification with threshold optimization',
    estimatedTime: '15-25 minutes',
    features: ['Threshold optimization', 'Label correlation analysis', 'Performance metrics'],
    requirements: ['Data with multiple labels per instance']
  },
  'beginner-hierarchical': {
    classificationType: 'hierarchical',
    experienceLevel: 'beginner',
    mode: 'guided',
    route: '/classification/hierarchical?mode=guided',
    title: 'Guided Hierarchical Classification',
    description: 'Step-by-step hierarchical classification with hierarchy builder',
    estimatedTime: '20-30 minutes',
    features: ['Visual hierarchy builder', 'Level-wise training', 'Cascade optimization'],
    requirements: ['Hierarchically structured labels']
  },
  'beginner-flat': {
    classificationType: 'flat',
    experienceLevel: 'beginner',
    mode: 'guided',
    route: '/classification/flat?mode=guided',
    title: 'Guided Flat Classification',
    description: 'Step-by-step flat classification with comprehensive analysis',
    estimatedTime: '10-20 minutes',
    features: ['Smart detection', 'Method comparison', 'Deployment ready'],
    requirements: ['Any labeled dataset']
  },

  // Expert workflows - advanced controls
  'expert-binary': {
    classificationType: 'binary',
    experienceLevel: 'expert',
    mode: 'advanced',
    route: '/classification/binary?mode=expert',
    title: 'Advanced Binary Classification',
    description: 'Full control over binary classification with custom architectures',
    estimatedTime: '30-60 minutes',
    features: ['Custom architectures', 'Hyperparameter tuning', 'Advanced metrics'],
    requirements: ['ML expertise', 'Custom training data']
  },
  'expert-multiclass': {
    classificationType: 'multiclass',
    experienceLevel: 'expert',
    mode: 'advanced',
    route: '/classification/multiclass?mode=expert',
    title: 'Advanced Multi-Class Classification',
    description: 'Full control over multi-class classification with ensemble methods',
    estimatedTime: '45-90 minutes',
    features: ['Ensemble methods', 'Custom loss functions', 'Advanced evaluation'],
    requirements: ['ML expertise', 'Complex datasets']
  },
  'expert-multilabel': {
    classificationType: 'multilabel',
    experienceLevel: 'expert',
    mode: 'advanced',
    route: '/classification/multilabel?mode=expert',
    title: 'Advanced Multi-Label Classification',
    description: 'Full control over multi-label classification with custom thresholds',
    estimatedTime: '45-90 minutes',
    features: ['Custom threshold strategies', 'Label dependency modeling', 'Advanced metrics'],
    requirements: ['ML expertise', 'Complex multi-label data']
  },
  'expert-hierarchical': {
    classificationType: 'hierarchical',
    experienceLevel: 'expert',
    mode: 'advanced',
    route: '/classification/hierarchical?mode=expert',
    title: 'Advanced Hierarchical Classification',
    description: 'Full control over hierarchical classification with custom architectures',
    estimatedTime: '60-120 minutes',
    features: ['Custom hierarchy architectures', 'Advanced cascade strategies', 'Performance optimization'],
    requirements: ['ML expertise', 'Complex hierarchical data']
  },
  'expert-flat': {
    classificationType: 'flat',
    experienceLevel: 'expert',
    mode: 'advanced',
    route: '/classification/flat?mode=expert',
    title: 'Advanced Flat Classification',
    description: 'Full control over flat classification with custom pipelines',
    estimatedTime: '30-90 minutes',
    features: ['Custom pipelines', 'Advanced preprocessing', 'Model comparison'],
    requirements: ['ML expertise', 'Custom requirements']
  }
};

/**
 * Dynamic workflow router class
 */
export class WorkflowRouter {
  private static instance: WorkflowRouter;
  private currentContext: WorkflowContext | null = null;

  private constructor() {}

  static getInstance(): WorkflowRouter {
    if (!WorkflowRouter.instance) {
      WorkflowRouter.instance = new WorkflowRouter();
    }
    return WorkflowRouter.instance;
  }

  /**
   * Get the appropriate workflow route based on classification type and experience level
   */
  getWorkflowRoute(
    classificationType: ClassificationType,
    experienceLevel: UserExperienceLevel,
    mode?: WorkflowMode
  ): WorkflowRouteConfig | null {
    const key = `${experienceLevel}-${classificationType}`;
    const config = WORKFLOW_ROUTES[key];
    
    if (!config) {
      console.warn(`No workflow route found for ${key}`);
      return null;
    }

    // Override mode if specified
    if (mode && mode !== config.mode) {
      return {
        ...config,
        mode,
        route: config.route.replace(/mode=\w+/, `mode=${mode}`)
      };
    }

    return config;
  }

  /**
   * Navigate to the appropriate workflow
   */
  navigateToWorkflow(
    navigate: NavigateFunction,
    classificationType: ClassificationType,
    experienceLevel: UserExperienceLevel,
    context?: WorkflowContext
  ): boolean {
    const config = this.getWorkflowRoute(classificationType, experienceLevel);
    
    if (!config) {
      console.error(`Cannot navigate: no workflow found for ${experienceLevel} ${classificationType}`);
      return false;
    }

    // Store context for workflow resume
    if (context) {
      this.setWorkflowContext(context);
    }

    // Build route with context parameters
    let route = config.route;
    if (context?.fileId) {
      route += `${route.includes('?') ? '&' : '?'}fileId=${context.fileId}`;
    }
    if (context?.step) {
      route += `${route.includes('?') ? '&' : '?'}step=${context.step}`;
    }

    navigate(route);
    return true;
  }

  /**
   * Get all available workflows for a classification type
   */
  getWorkflowsForType(classificationType: ClassificationType): WorkflowRouteConfig[] {
    return Object.values(WORKFLOW_ROUTES).filter(
      config => config.classificationType === classificationType
    );
  }

  /**
   * Get recommended workflow based on user data and preferences
   */
  getRecommendedWorkflow(
    userExperience: 'beginner' | 'intermediate' | 'expert',
    dataComplexity: 'simple' | 'moderate' | 'complex',
    timeAvailable: 'quick' | 'moderate' | 'extensive'
  ): { classificationType: ClassificationType; experienceLevel: UserExperienceLevel } {
    // Smart recommendation logic
    let experienceLevel: UserExperienceLevel = 'beginner';
    let classificationType: ClassificationType = 'flat';

    // Determine experience level
    if (userExperience === 'expert' || (userExperience === 'intermediate' && dataComplexity === 'complex')) {
      experienceLevel = 'expert';
    }

    // Determine classification type based on complexity and time
    if (dataComplexity === 'simple' && timeAvailable === 'quick') {
      classificationType = 'binary';
    } else if (dataComplexity === 'moderate') {
      classificationType = timeAvailable === 'quick' ? 'multiclass' : 'flat';
    } else if (dataComplexity === 'complex') {
      classificationType = timeAvailable === 'extensive' ? 'hierarchical' : 'multilabel';
    }

    return { classificationType, experienceLevel };
  }

  /**
   * Set workflow context for resume functionality
   */
  setWorkflowContext(context: WorkflowContext): void {
    this.currentContext = context;
    // Persist to localStorage for resume functionality
    localStorage.setItem('classyweb-workflow-context', JSON.stringify(context));
  }

  /**
   * Get current workflow context
   */
  getWorkflowContext(): WorkflowContext | null {
    if (this.currentContext) {
      return this.currentContext;
    }

    // Try to restore from localStorage
    try {
      const stored = localStorage.getItem('classyweb-workflow-context');
      if (stored) {
        this.currentContext = JSON.parse(stored);
        return this.currentContext;
      }
    } catch (error) {
      console.warn('Failed to restore workflow context:', error);
    }

    return null;
  }

  /**
   * Clear workflow context
   */
  clearWorkflowContext(): void {
    this.currentContext = null;
    localStorage.removeItem('classyweb-workflow-context');
  }

  /**
   * Check if user can resume a workflow
   */
  canResumeWorkflow(): boolean {
    const context = this.getWorkflowContext();
    return context !== null && (context.step !== undefined || context.fileId !== undefined);
  }
}

// Export singleton instance
export const workflowRouter = WorkflowRouter.getInstance();
