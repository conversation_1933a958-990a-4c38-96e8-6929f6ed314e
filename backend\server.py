#!/usr/bin/env python
"""
Server script for the ClassyWeb application.

This script is the recommended way to run the ClassyWeb application.
It imports the FastAPI app instance from the app package and runs it using uvicorn.

Usage:
    cd backend
    python server.py [--host HOST] [--port PORT] [--reload]

Options:
    --host HOST     Host to bind to (default: 127.0.0.1)
    --port PORT     Port to bind to (default: 8000)
    --reload        Enable auto-reload on code changes
    --workers N     Number of worker processes (default: 1)
    --log-level LVL Logging level (default: info)
"""
import sys
import os
import argparse
import logging
import uvicorn
from dotenv import load_dotenv

# Load environment variables from .env file
env_path = os.path.join(os.path.dirname(__file__), '.env')
if os.path.exists(env_path):
    load_dotenv(env_path)
    print(f"Loaded environment variables from {env_path}")
else:
    print(f"Warning: .env file not found at {env_path}")

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger("server")

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Run the ClassyWeb application server")
    parser.add_argument("--host", default="127.0.0.1", help="Host to bind to (default: 127.0.0.1)")
    parser.add_argument("--port", type=int, default=8001, help="Port to bind to (default: 8001)")
    parser.add_argument("--reload", action="store_true", help="Enable auto-reload on code changes")
    parser.add_argument("--workers", type=int, default=1, help="Number of worker processes (default: 1)")
    parser.add_argument("--log-level", default="info", help="Logging level (default: info)")
    return parser.parse_args()

def main():
    """Main entry point for the server."""
    args = parse_args()

    # Log startup information
    logger.info(f"Starting ClassyWeb server on {args.host}:{args.port}")
    logger.info(f"Auto-reload: {'enabled' if args.reload else 'disabled'}")
    logger.info(f"Workers: {args.workers}")
    logger.info(f"Log level: {args.log_level}")

    # Run the server
    uvicorn.run(
        "app.main:app",
        host=args.host,
        port=args.port,
        reload=args.reload,
        workers=args.workers,
        log_level=args.log_level
    )

if __name__ == "__main__":
    main()
