"""Hierarchical Constraint Satisfaction System for ClassyWeb.

This module implements constraint satisfaction for hierarchical classification,
ensuring that predictions respect hierarchical relationships and business rules.
"""

import logging
import json
from typing import List, Dict, Any, Optional, Set, Tuple
from collections import defaultdict, deque
from dataclasses import dataclass
import numpy as np
import networkx as nx

logger = logging.getLogger(__name__)

@dataclass
class HierarchyConstraint:
    """Represents a hierarchical constraint."""
    constraint_id: str
    constraint_type: str  # 'parent_child', 'mutual_exclusion', 'implication', 'cardinality'
    source_labels: List[str]
    target_labels: List[str]
    constraint_rule: str
    confidence: float
    examples: List[str]

@dataclass
class ConstraintViolation:
    """Represents a constraint violation."""
    constraint_id: str
    violation_type: str
    conflicting_labels: List[str]
    suggested_fix: str
    confidence_penalty: float

class HierarchicalConstraintSolver:
    """Constraint satisfaction solver for hierarchical classification."""
    
    def __init__(self):
        self.constraints: List[HierarchyConstraint] = []
        self.hierarchy_structure: Optional[List[str]] = None
        self.label_hierarchy: Dict[str, Dict[str, Any]] = {}
        self.constraint_graph: Dict[str, List[str]] = defaultdict(list)
        
    def learn_constraints(
        self,
        training_labels: List[List[str]],
        hierarchy_structure: Optional[List[str]] = None,
        hierarchy_data: Optional[List[Dict[str, str]]] = None
    ) -> None:
        """Learn hierarchical constraints from training data."""
        logger.info("Learning hierarchical constraints...")
        
        self.hierarchy_structure = hierarchy_structure
        self.constraints = []
        
        if hierarchy_structure and hierarchy_data:
            self._learn_structural_constraints(hierarchy_data, hierarchy_structure)
        
        self._learn_cooccurrence_constraints(training_labels)
        self._learn_implication_constraints(training_labels)
        self._learn_exclusion_constraints(training_labels)
        
        self._build_constraint_graph()
        
        logger.info(f"Learned {len(self.constraints)} hierarchical constraints")
    
    def _learn_structural_constraints(
        self,
        hierarchy_data: List[Dict[str, str]],
        hierarchy_structure: List[str]
    ) -> None:
        """Learn constraints from explicit hierarchy structure."""
        # Build hierarchy tree
        hierarchy_tree = defaultdict(lambda: defaultdict(set))
        
        for row in hierarchy_data:
            prev_level_value = None
            for level in hierarchy_structure:
                current_value = row.get(level, "").strip()
                if current_value:
                    if prev_level_value:
                        hierarchy_tree[prev_level_value][level].add(current_value)
                    prev_level_value = current_value
        
        # Create parent-child constraints
        constraint_id = 0
        for parent, children_by_level in hierarchy_tree.items():
            for level, children in children_by_level.items():
                for child in children:
                    constraint = HierarchyConstraint(
                        constraint_id=f"parent_child_{constraint_id}",
                        constraint_type='parent_child',
                        source_labels=[child],
                        target_labels=[parent],
                        constraint_rule=f"IF {child} THEN {parent}",
                        confidence=1.0,  # Structural constraints have high confidence
                        examples=[]
                    )
                    self.constraints.append(constraint)
                    constraint_id += 1
    
    def _learn_cooccurrence_constraints(self, training_labels: List[List[str]]) -> None:
        """Learn constraints based on label co-occurrence patterns."""
        # Count label co-occurrences
        cooccurrence_counts = defaultdict(int)
        label_counts = defaultdict(int)
        
        for label_list in training_labels:
            for label in label_list:
                label_counts[label] += 1
                for other_label in label_list:
                    if label != other_label:
                        pair = tuple(sorted([label, other_label]))
                        cooccurrence_counts[pair] += 1
        
        # Create implication constraints for strong co-occurrences
        constraint_id = len(self.constraints)
        for (label1, label2), cooccur_count in cooccurrence_counts.items():
            label1_count = label_counts[label1]
            label2_count = label_counts[label2]
            
            # Check if label1 strongly implies label2
            if cooccur_count / label1_count > 0.8 and cooccur_count >= 3:
                constraint = HierarchyConstraint(
                    constraint_id=f"implication_{constraint_id}",
                    constraint_type='implication',
                    source_labels=[label1],
                    target_labels=[label2],
                    constraint_rule=f"IF {label1} THEN likely {label2}",
                    confidence=cooccur_count / label1_count,
                    examples=[]
                )
                self.constraints.append(constraint)
                constraint_id += 1
    
    def _learn_implication_constraints(self, training_labels: List[List[str]]) -> None:
        """Learn implication constraints (A implies B)."""
        # This is handled in _learn_cooccurrence_constraints
        # Additional implication learning could be added here for specific domain rules
        logger.debug("Implication constraints learning delegated to co-occurrence analysis")
    
    def _learn_exclusion_constraints(self, training_labels: List[List[str]]) -> None:
        """Learn mutual exclusion constraints (A excludes B)."""
        all_labels = set()
        for label_list in training_labels:
            all_labels.update(label_list)
        all_labels = list(all_labels)
        
        # Check for labels that never appear together
        exclusion_pairs = []
        for i, label1 in enumerate(all_labels):
            for j, label2 in enumerate(all_labels[i+1:], i+1):
                # Check if these labels ever appear together
                appear_together = any(
                    label1 in label_list and label2 in label_list
                    for label_list in training_labels
                )
                
                # Check if both labels appear frequently enough
                label1_count = sum(1 for label_list in training_labels if label1 in label_list)
                label2_count = sum(1 for label_list in training_labels if label2 in label_list)
                
                if not appear_together and label1_count >= 3 and label2_count >= 3:
                    exclusion_pairs.append((label1, label2))
        
        # Create exclusion constraints
        constraint_id = len(self.constraints)
        for label1, label2 in exclusion_pairs:
            constraint = HierarchyConstraint(
                constraint_id=f"exclusion_{constraint_id}",
                constraint_type='mutual_exclusion',
                source_labels=[label1],
                target_labels=[label2],
                constraint_rule=f"{label1} EXCLUDES {label2}",
                confidence=0.9,  # High confidence for exclusions
                examples=[]
            )
            self.constraints.append(constraint)
            constraint_id += 1
    
    def _build_constraint_graph(self) -> None:
        """Build constraint graph for efficient constraint checking."""
        self.constraint_graph = defaultdict(list)
        
        for constraint in self.constraints:
            for source_label in constraint.source_labels:
                self.constraint_graph[source_label].append(constraint)
    
    def apply_constraints(
        self,
        predictions: List[str],
        confidence_scores: Optional[Dict[str, float]] = None
    ) -> Tuple[List[str], List[ConstraintViolation]]:
        """Apply constraints to predictions and resolve violations."""
        if not predictions:
            return predictions, []
        
        violations = self._detect_violations(predictions)
        
        if not violations:
            return predictions, []
        
        # Resolve violations
        resolved_predictions = self._resolve_violations(predictions, violations, confidence_scores)
        
        return resolved_predictions, violations
    
    def _detect_violations(self, predictions: List[str]) -> List[ConstraintViolation]:
        """Detect constraint violations in predictions."""
        violations = []
        prediction_set = set(predictions)
        
        for prediction in predictions:
            if prediction in self.constraint_graph:
                for constraint in self.constraint_graph[prediction]:
                    violation = self._check_constraint_violation(
                        constraint, prediction_set
                    )
                    if violation:
                        violations.append(violation)
        
        return violations
    
    def _check_constraint_violation(
        self,
        constraint: HierarchyConstraint,
        prediction_set: Set[str]
    ) -> Optional[ConstraintViolation]:
        """Check if a specific constraint is violated."""
        if constraint.constraint_type == 'parent_child':
            # If child is present, parent must be present
            child_present = any(label in prediction_set for label in constraint.source_labels)
            parent_present = any(label in prediction_set for label in constraint.target_labels)
            
            if child_present and not parent_present:
                return ConstraintViolation(
                    constraint_id=constraint.constraint_id,
                    violation_type='missing_parent',
                    conflicting_labels=constraint.source_labels + constraint.target_labels,
                    suggested_fix=f"Add parent label(s): {', '.join(constraint.target_labels)}",
                    confidence_penalty=0.2
                )
        
        elif constraint.constraint_type == 'mutual_exclusion':
            # Labels cannot appear together
            source_present = any(label in prediction_set for label in constraint.source_labels)
            target_present = any(label in prediction_set for label in constraint.target_labels)
            
            if source_present and target_present:
                return ConstraintViolation(
                    constraint_id=constraint.constraint_id,
                    violation_type='mutual_exclusion',
                    conflicting_labels=constraint.source_labels + constraint.target_labels,
                    suggested_fix=f"Remove one of: {', '.join(constraint.source_labels + constraint.target_labels)}",
                    confidence_penalty=0.3
                )
        
        elif constraint.constraint_type == 'implication':
            # If source is present, target should be present
            source_present = any(label in prediction_set for label in constraint.source_labels)
            target_present = any(label in prediction_set for label in constraint.target_labels)
            
            if source_present and not target_present and constraint.confidence > 0.7:
                return ConstraintViolation(
                    constraint_id=constraint.constraint_id,
                    violation_type='missing_implication',
                    conflicting_labels=constraint.source_labels + constraint.target_labels,
                    suggested_fix=f"Consider adding: {', '.join(constraint.target_labels)}",
                    confidence_penalty=0.1
                )
        
        return None
    
    def _resolve_violations(
        self,
        predictions: List[str],
        violations: List[ConstraintViolation],
        confidence_scores: Optional[Dict[str, float]] = None
    ) -> List[str]:
        """Resolve constraint violations by modifying predictions."""
        resolved_predictions = predictions.copy()
        confidence_scores = confidence_scores or {}
        
        # Sort violations by severity (confidence penalty)
        violations.sort(key=lambda v: v.confidence_penalty, reverse=True)
        
        for violation in violations:
            if violation.violation_type == 'missing_parent':
                # Add missing parent labels
                for label in violation.conflicting_labels:
                    if label not in resolved_predictions:
                        # Check if this is a target label (parent)
                        constraint = next(
                            c for c in self.constraints 
                            if c.constraint_id == violation.constraint_id
                        )
                        if label in constraint.target_labels:
                            resolved_predictions.append(label)
            
            elif violation.violation_type == 'mutual_exclusion':
                # Remove the label with lower confidence
                conflicting_labels = [
                    label for label in violation.conflicting_labels 
                    if label in resolved_predictions
                ]
                
                if len(conflicting_labels) > 1:
                    # Keep the label with highest confidence
                    best_label = max(
                        conflicting_labels,
                        key=lambda l: confidence_scores.get(l, 0.5)
                    )
                    
                    for label in conflicting_labels:
                        if label != best_label and label in resolved_predictions:
                            resolved_predictions.remove(label)
            
            elif violation.violation_type == 'missing_implication':
                # Add implied labels if confidence is high enough
                constraint = next(
                    c for c in self.constraints 
                    if c.constraint_id == violation.constraint_id
                )
                
                if constraint.confidence > 0.8:
                    for label in constraint.target_labels:
                        if label not in resolved_predictions:
                            resolved_predictions.append(label)
        
        return resolved_predictions
    
    def validate_hierarchy_consistency(self, predictions: List[str]) -> Dict[str, Any]:
        """Validate that predictions are hierarchically consistent."""
        violations = self._detect_violations(predictions)
        
        consistency_score = 1.0
        if violations:
            penalty = sum(v.confidence_penalty for v in violations) / len(predictions)
            consistency_score = max(0.0, 1.0 - penalty)
        
        return {
            'is_consistent': len(violations) == 0,
            'consistency_score': consistency_score,
            'violations': [
                {
                    'type': v.violation_type,
                    'labels': v.conflicting_labels,
                    'suggestion': v.suggested_fix,
                    'penalty': v.confidence_penalty
                }
                for v in violations
            ],
            'total_violations': len(violations)
        }

    def validate_comprehensive_rules(
        self,
        hierarchy_data: List[Dict[str, str]],
        hierarchy_levels: List[str],
        validation_rules: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Comprehensive validation of all hierarchy rules."""
        validation_results = {
            'overall_valid': True,
            'rule_results': {},
            'total_violations': 0,
            'critical_violations': 0,
            'warnings': 0
        }

        # Build hierarchy graph for analysis
        hierarchy_graph = self._build_hierarchy_graph(hierarchy_data, hierarchy_levels)

        for rule in validation_rules:
            if not rule.get('enabled', True):
                continue

            rule_id = rule['id']
            rule_result = self._validate_single_rule(
                rule_id, hierarchy_data, hierarchy_levels, hierarchy_graph
            )

            validation_results['rule_results'][rule_id] = rule_result

            if rule_result['violations']:
                validation_results['total_violations'] += len(rule_result['violations'])
                if rule.get('severity') == 'error':
                    validation_results['critical_violations'] += len(rule_result['violations'])
                    validation_results['overall_valid'] = False
                else:
                    validation_results['warnings'] += len(rule_result['violations'])

        return validation_results

    def _build_hierarchy_graph(self, hierarchy_data: List[Dict[str, str]], hierarchy_levels: List[str]) -> nx.DiGraph:
        """Build a directed graph representing the hierarchy structure."""
        graph = nx.DiGraph()

        for row in hierarchy_data:
            # Add nodes and edges for each level transition
            for i in range(len(hierarchy_levels) - 1):
                parent_level = hierarchy_levels[i]
                child_level = hierarchy_levels[i + 1]

                parent_value = row.get(parent_level)
                child_value = row.get(child_level)

                if parent_value and child_value:
                    # Add nodes with level information
                    parent_node = f"{parent_level}:{parent_value}"
                    child_node = f"{child_level}:{child_value}"

                    graph.add_node(parent_node, level=i, value=parent_value, level_name=parent_level)
                    graph.add_node(child_node, level=i+1, value=child_value, level_name=child_level)
                    graph.add_edge(parent_node, child_node)

        return graph

    def _validate_single_rule(
        self,
        rule_id: str,
        hierarchy_data: List[Dict[str, str]],
        hierarchy_levels: List[str],
        hierarchy_graph: nx.DiGraph
    ) -> Dict[str, Any]:
        """Validate a single rule against the hierarchy data."""
        rule_result = {
            'rule_id': rule_id,
            'valid': True,
            'violations': [],
            'warnings': []
        }

        if rule_id == 'parent_child_consistency':
            rule_result = self._validate_parent_child_consistency(hierarchy_data, hierarchy_levels, hierarchy_graph)
        elif rule_id == 'circular_dependencies':
            rule_result = self._validate_circular_dependencies(hierarchy_graph)
        elif rule_id == 'orphaned_nodes':
            rule_result = self._validate_orphaned_nodes(hierarchy_graph, hierarchy_levels)
        elif rule_id == 'level_completeness':
            rule_result = self._validate_level_completeness(hierarchy_data, hierarchy_levels)
        elif rule_id == 'duplicate_paths':
            rule_result = self._validate_duplicate_paths(hierarchy_data, hierarchy_levels)
        else:
            rule_result['warnings'].append(f"Unknown validation rule: {rule_id}")

        rule_result['valid'] = len(rule_result['violations']) == 0
        return rule_result

    def _validate_parent_child_consistency(
        self,
        hierarchy_data: List[Dict[str, str]],
        hierarchy_levels: List[str],
        hierarchy_graph: nx.DiGraph
    ) -> Dict[str, Any]:
        """Validate that all child values have valid parent relationships."""
        result = {'rule_id': 'parent_child_consistency', 'violations': [], 'warnings': []}

        # Check each row for parent-child consistency
        for row_idx, row in enumerate(hierarchy_data):
            for i in range(1, len(hierarchy_levels)):
                parent_level = hierarchy_levels[i-1]
                child_level = hierarchy_levels[i]

                parent_value = row.get(parent_level)
                child_value = row.get(child_level)

                if child_value and not parent_value:
                    result['violations'].append({
                        'type': 'missing_parent',
                        'row_index': row_idx,
                        'child_level': child_level,
                        'child_value': child_value,
                        'parent_level': parent_level,
                        'message': f"Child '{child_value}' at level '{child_level}' has no parent at level '{parent_level}'"
                    })

                # Check if parent-child relationship is valid based on learned constraints
                if parent_value and child_value:
                    if parent_value in self.hierarchy_constraints:
                        valid_children = self.hierarchy_constraints[parent_value]
                        if child_value not in valid_children:
                            result['violations'].append({
                                'type': 'invalid_parent_child',
                                'row_index': row_idx,
                                'parent_value': parent_value,
                                'child_value': child_value,
                                'valid_children': valid_children,
                                'message': f"'{child_value}' is not a valid child of '{parent_value}'"
                            })

        return result

    def _validate_circular_dependencies(self, hierarchy_graph: nx.DiGraph) -> Dict[str, Any]:
        """Detect circular dependencies in the hierarchy."""
        result = {'rule_id': 'circular_dependencies', 'violations': [], 'warnings': []}

        try:
            # Check if the graph is a DAG (Directed Acyclic Graph)
            if not nx.is_directed_acyclic_graph(hierarchy_graph):
                # Find all cycles
                cycles = list(nx.simple_cycles(hierarchy_graph))
                for cycle in cycles:
                    result['violations'].append({
                        'type': 'circular_dependency',
                        'cycle': cycle,
                        'message': f"Circular dependency detected: {' -> '.join(cycle)} -> {cycle[0]}"
                    })
        except Exception as e:
            result['warnings'].append({
                'type': 'validation_error',
                'message': f"Error checking for circular dependencies: {str(e)}"
            })

        return result

    def _validate_orphaned_nodes(self, hierarchy_graph: nx.DiGraph, hierarchy_levels: List[str]) -> Dict[str, Any]:
        """Detect nodes without proper parent relationships."""
        result = {'rule_id': 'orphaned_nodes', 'violations': [], 'warnings': []}

        try:
            # Find nodes that should have parents but don't
            for node in hierarchy_graph.nodes():
                node_data = hierarchy_graph.nodes[node]
                level = node_data.get('level', 0)

                # Nodes at level 0 (root level) should not have parents
                if level > 0:
                    # Check if this node has any incoming edges (parents)
                    if hierarchy_graph.in_degree(node) == 0:
                        result['violations'].append({
                            'type': 'orphaned_node',
                            'node': node,
                            'level': level,
                            'value': node_data.get('value'),
                            'level_name': node_data.get('level_name'),
                            'message': f"Node '{node}' at level {level} has no parent relationships"
                        })

                # Check for nodes with too many parents (should typically have one parent in hierarchy)
                if hierarchy_graph.in_degree(node) > 1:
                    parents = list(hierarchy_graph.predecessors(node))
                    result['warnings'].append({
                        'type': 'multiple_parents',
                        'node': node,
                        'parents': parents,
                        'message': f"Node '{node}' has multiple parents: {parents}"
                    })

        except Exception as e:
            result['warnings'].append({
                'type': 'validation_error',
                'message': f"Error checking for orphaned nodes: {str(e)}"
            })

        return result

    def _validate_level_completeness(self, hierarchy_data: List[Dict[str, str]], hierarchy_levels: List[str]) -> Dict[str, Any]:
        """Validate that hierarchy levels are complete."""
        result = {'rule_id': 'level_completeness', 'violations': [], 'warnings': []}

        for row_idx, row in enumerate(hierarchy_data):
            missing_levels = []
            incomplete_path = False

            # Check for missing intermediate levels
            for i, level in enumerate(hierarchy_levels):
                value = row.get(level)
                if not value or str(value).strip() == '':
                    missing_levels.append(level)

                    # If we have a value at a deeper level but missing at current level
                    for j in range(i + 1, len(hierarchy_levels)):
                        deeper_level = hierarchy_levels[j]
                        deeper_value = row.get(deeper_level)
                        if deeper_value and str(deeper_value).strip():
                            incomplete_path = True
                            break

            if missing_levels:
                severity = 'violation' if incomplete_path else 'warning'
                result[f'{severity}s'].append({
                    'type': 'incomplete_levels',
                    'row_index': row_idx,
                    'missing_levels': missing_levels,
                    'incomplete_path': incomplete_path,
                    'message': f"Row {row_idx + 1} has missing levels: {', '.join(missing_levels)}"
                })

        return result

    def _validate_duplicate_paths(self, hierarchy_data: List[Dict[str, str]], hierarchy_levels: List[str]) -> Dict[str, Any]:
        """Detect duplicate hierarchical paths."""
        result = {'rule_id': 'duplicate_paths', 'violations': [], 'warnings': []}

        path_counts = defaultdict(list)

        # Build complete paths and track duplicates
        for row_idx, row in enumerate(hierarchy_data):
            path_values = []
            for level in hierarchy_levels:
                value = row.get(level, '').strip()
                path_values.append(value)

            # Create path key (only include non-empty values)
            path_key = '|'.join([v for v in path_values if v])
            if path_key:  # Only track non-empty paths
                path_counts[path_key].append(row_idx)

        # Find duplicates
        for path_key, row_indices in path_counts.items():
            if len(row_indices) > 1:
                result['violations'].append({
                    'type': 'duplicate_path',
                    'path': path_key,
                    'row_indices': row_indices,
                    'count': len(row_indices),
                    'message': f"Duplicate path '{path_key}' found in rows: {[i+1 for i in row_indices]}"
                })

        return result
    
    def get_constraint_explanation(self, predictions: List[str]) -> Dict[str, Any]:
        """Get explanation of which constraints apply to the predictions."""
        applicable_constraints = []
        prediction_set = set(predictions)
        
        for prediction in predictions:
            if prediction in self.constraint_graph:
                for constraint in self.constraint_graph[prediction]:
                    applicable_constraints.append({
                        'constraint_id': constraint.constraint_id,
                        'type': constraint.constraint_type,
                        'rule': constraint.constraint_rule,
                        'confidence': constraint.confidence,
                        'applies_to': prediction
                    })
        
        return {
            'predictions': predictions,
            'applicable_constraints': applicable_constraints,
            'total_constraints': len(self.constraints)
        }
    
    def export_constraints(self) -> Dict[str, Any]:
        """Export learned constraints for storage."""
        return {
            'constraints': [
                {
                    'constraint_id': c.constraint_id,
                    'constraint_type': c.constraint_type,
                    'source_labels': c.source_labels,
                    'target_labels': c.target_labels,
                    'constraint_rule': c.constraint_rule,
                    'confidence': c.confidence,
                    'examples': c.examples
                }
                for c in self.constraints
            ],
            'hierarchy_structure': self.hierarchy_structure,
            'constraint_count': len(self.constraints)
        }
    
    def import_constraints(self, constraint_data: Dict[str, Any]) -> None:
        """Import previously learned constraints."""
        self.constraints = [
            HierarchyConstraint(**constraint_dict)
            for constraint_dict in constraint_data['constraints']
        ]
        self.hierarchy_structure = constraint_data.get('hierarchy_structure')
        self._build_constraint_graph()
        
        logger.info(f"Imported {len(self.constraints)} constraints")

# Global instance
constraint_solver = HierarchicalConstraintSolver()
