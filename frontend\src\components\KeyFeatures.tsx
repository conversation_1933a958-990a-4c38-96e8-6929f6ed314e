import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  Zap, 
  Cpu, 
  Users, 
  BarChart3, 
  Puzzle, 
  Monitor,
  CheckCircle2,
  Sparkles
} from "lucide-react";

const features = [
  {
    icon: Zap,
    title: "Dual Training Approaches",
    description: "Train custom models with GPU acceleration or leverage powerful LLMs for instant inference.",
    highlight: "Custom Models + LLMs"
  },
  {
    icon: Cpu,
    title: "GPU Acceleration",
    description: "Unsloth integration accelerates HuggingFace transformer training for lightning-fast results.",
    highlight: "10x Faster Training"
  },
  {
    icon: Users,
    title: "User-Friendly Design",
    description: "Guided workflows for beginners and advanced controls for ML experts with hyperparameter tuning.",
    highlight: "Beginner to Expert"
  },
  {
    icon: BarChart3,
    title: "Real-time Monitoring",
    description: "Watch your models train in real-time with comprehensive progress tracking and metrics.",
    highlight: "Live Tracking"
  },
  {
    icon: Puzzle,
    title: "Plugin Architecture",
    description: "Flexible, extensible platform built with modern plugin-based architecture for customization.",
    highlight: "Fully Extensible"
  },
  {
    icon: Monitor,
    title: "Desktop Compatible",
    description: "Seamless Electron compatibility for powerful desktop deployment and offline capabilities.",
    highlight: "Cross-Platform"
  }
];

export const KeyFeatures = () => {
  return (
    <section id="features" className="py-20 bg-muted/30">
      <div className="max-w-7xl mx-auto px-6">
        <div className="text-center mb-16">
          <Badge variant="outline" className="mb-4">
            <Sparkles className="w-4 h-4 mr-2" />
            Key Features
          </Badge>
          <h2 className="text-4xl md:text-5xl font-bold mb-6 text-foreground">
            Built for
            <span className="text-primary font-bold"> Modern ML Workflows</span>
          </h2>
          <p className="text-xl text-foreground/90 max-w-3xl mx-auto font-medium">
            From rapid prototyping to production deployment, ClassyWeb provides all the tools
            you need for successful machine learning classification projects.
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => {
            const Icon = feature.icon;
            return (
              <Card 
                key={index}
                className="group hover:shadow-card transition-all duration-300 border-border/50 hover:border-primary/20 bg-background relative overflow-hidden"
              >
                <div className="absolute top-0 right-0 w-20 h-20 bg-primary/10 rounded-full -mr-10 -mt-10" />
                
                <CardHeader className="relative z-10">
                  <div className="w-12 h-12 rounded-lg bg-primary/10 flex items-center justify-center mb-4 group-hover:bg-primary/20 transition-colors">
                    <Icon className="w-6 h-6 text-primary" />
                  </div>
                  <CardTitle className="text-xl group-hover:text-primary transition-colors">
                    {feature.title}
                  </CardTitle>
                  <CardDescription>
                    {feature.description}
                  </CardDescription>
                </CardHeader>
                
                <CardContent className="pt-0 relative z-10">
                  <Badge variant="secondary" className="text-xs font-medium">
                    <CheckCircle2 className="w-3 h-3 mr-1" />
                    {feature.highlight}
                  </Badge>
                </CardContent>
              </Card>
            );
          })}
        </div>
      </div>
    </section>
  );
};
