/**
 * UnifiedDataManager.ts
 * 
 * Unified data management service to handle single file upload for multiple purposes.
 * Eliminates the need for users to upload the same file multiple times for analysis, training, and classification.
 */

import { uploadFile, UploadedFile } from './fileUploadApi';
import { SmartDataPurposeDetection } from './smartDataPurposeDetection';
import { errorHandler, ErrorUtils } from './errorHandling';
import apiClient from './apiClient';

export type DataPurpose = 'analysis' | 'training' | 'classification';

export interface UnifiedDataUpload {
  file: File;
  fileInfo: UploadedFile;
  purposes: {
    analysis: boolean;      // For smart detection & column selection
    training: boolean;      // For model training
    classification: boolean; // For inference on new data
  };
  dataSubsets?: {
    trainingRows?: number[];
    classificationRows?: number[];
  };
  uploadedAt: Date;
  lastUsed: Date;
}

export interface DataPurposeSuggestion {
  canUseForTraining: boolean;
  canUseForClassification: boolean;
  hasLabels: boolean;
  suggestedPurposes: DataPurpose[];
  recommendations: string[];
  warnings: string[];
}

export interface DataSplitOptions {
  trainRatio: number;
  validationRatio: number;
  testRatio: number;
  stratify: boolean;
  randomSeed?: number;
}

/**
 * UnifiedDataManager - Manages file uploads and data reuse across different purposes
 */
export class UnifiedDataManager {
  private uploadedData: Map<string, UnifiedDataUpload> = new Map();
  private static instance: UnifiedDataManager;

  private constructor() {
    // Load existing data from localStorage on initialization
    this.loadFromStorage();
  }

  public static getInstance(): UnifiedDataManager {
    if (!UnifiedDataManager.instance) {
      UnifiedDataManager.instance = new UnifiedDataManager();
    }
    return UnifiedDataManager.instance;
  }

  /**
   * Upload a file and specify its intended purposes
   */
  async uploadFile(file: File, purposes: DataPurpose[] = ['analysis']): Promise<string> {
    return ErrorUtils.withErrorHandling(
      async () => {
        // Upload file using existing API
        const fileInfo = await uploadFile(file);

        // Create unified data entry
        const unifiedData: UnifiedDataUpload = {
          file,
          fileInfo,
          purposes: {
            analysis: purposes.includes('analysis'),
            training: purposes.includes('training'),
            classification: purposes.includes('classification')
          },
          uploadedAt: new Date(),
          lastUsed: new Date()
        };

        // Store in memory and localStorage
        const fileId = fileInfo.file_id;
        this.uploadedData.set(fileId, unifiedData);
        this.saveToStorage();

        return fileId;
      },
      {
        operation: 'file_upload',
        component: 'UnifiedDataManager',
        fileName: file.name
      },
      [
        ErrorUtils.createRecoveryAction(
          'retry',
          'Retry Upload',
          'Try uploading the file again',
          () => this.uploadFile(file, purposes),
          1
        )
      ]
    );
  }

  /**
   * Get data for a specific purpose without re-uploading
   */
  getDataForPurpose(fileId: string, purpose: DataPurpose): UploadedFile | null {
    const data = this.uploadedData.get(fileId);
    if (!data) return null;

    // Update last used timestamp
    data.lastUsed = new Date();
    this.saveToStorage();

    // Check if file can be used for this purpose
    if (!data.purposes[purpose]) {
      console.warn(`File ${fileId} was not uploaded for purpose: ${purpose}`);
    }

    return data.fileInfo;
  }

  /**
   * Analyze file and suggest appropriate purposes using enhanced detection
   */
  suggestDataPurposes(fileInfo: UploadedFile): DataPurposeSuggestion {
    try {
      // Use enhanced smart detection service
      const enhancedAnalysis = SmartDataPurposeDetection.analyzeFile(fileInfo);

      // Return the base suggestion part (compatible with existing interface)
      return {
        canUseForTraining: enhancedAnalysis.canUseForTraining,
        canUseForClassification: enhancedAnalysis.canUseForClassification,
        hasLabels: enhancedAnalysis.hasLabels,
        suggestedPurposes: enhancedAnalysis.suggestedPurposes,
        recommendations: [
          ...enhancedAnalysis.recommendations,
          ...enhancedAnalysis.optimizationRecommendations
        ],
        warnings: enhancedAnalysis.warnings
      };
    } catch (error) {
      console.warn('Enhanced analysis failed, falling back to basic analysis:', error);
      return this.basicDataPurposeAnalysis(fileInfo);
    }
  }

  /**
   * Get enhanced analysis with detailed metrics
   */
  getEnhancedAnalysis(fileInfo: UploadedFile) {
    return SmartDataPurposeDetection.analyzeFile(fileInfo);
  }

  /**
   * Fallback basic analysis method
   */
  private basicDataPurposeAnalysis(fileInfo: UploadedFile): DataPurposeSuggestion {
    const suggestions: DataPurposeSuggestion = {
      canUseForTraining: false,
      canUseForClassification: false,
      hasLabels: false,
      suggestedPurposes: ['analysis'],
      recommendations: [],
      warnings: []
    };

    if (!fileInfo.columns || !fileInfo.preview) {
      suggestions.warnings.push('Unable to analyze file structure');
      return suggestions;
    }

    // Basic detection logic (simplified version of original)
    const labelColumns = fileInfo.columns.filter(col =>
      col.toLowerCase().includes('label') ||
      col.toLowerCase().includes('category') ||
      col.toLowerCase().includes('class')
    );

    const textColumns = fileInfo.columns.filter(col =>
      col.toLowerCase().includes('text') ||
      col.toLowerCase().includes('content') ||
      col.toLowerCase().includes('message')
    );

    suggestions.hasLabels = labelColumns.length > 0;
    suggestions.canUseForTraining = suggestions.hasLabels && textColumns.length > 0;
    suggestions.canUseForClassification = textColumns.length > 0;

    if (suggestions.canUseForTraining) {
      suggestions.suggestedPurposes.push('training');
      suggestions.recommendations.push('Suitable for training');
    }

    if (suggestions.canUseForClassification) {
      suggestions.suggestedPurposes.push('classification');
      suggestions.recommendations.push('Can be used for classification');
    }

    return suggestions;
  }

  /**
   * Update purposes for an existing file
   */
  updatePurposes(fileId: string, purposes: DataPurpose[]): boolean {
    const data = this.uploadedData.get(fileId);
    if (!data) return false;

    data.purposes = {
      analysis: purposes.includes('analysis'),
      training: purposes.includes('training'),
      classification: purposes.includes('classification')
    };
    data.lastUsed = new Date();

    this.saveToStorage();
    return true;
  }

  /**
   * Split data for training and validation
   */
  splitData(fileId: string, options: DataSplitOptions): {
    trainingRows: number[];
    validationRows: number[];
    testRows: number[];
  } {
    const data = this.uploadedData.get(fileId);
    if (!data) {
      throw new Error(`File ${fileId} not found`);
    }

    const totalRows = data.fileInfo.num_rows;
    const indices = Array.from({ length: totalRows }, (_, i) => i);

    // Shuffle indices if random seed is provided
    if (options.randomSeed !== undefined) {
      this.shuffleArray(indices, options.randomSeed);
    }

    // Calculate split sizes
    const trainSize = Math.floor(totalRows * options.trainRatio);
    const validationSize = Math.floor(totalRows * options.validationRatio);
    const testSize = totalRows - trainSize - validationSize;

    // Split indices
    const trainingRows = indices.slice(0, trainSize);
    const validationRows = indices.slice(trainSize, trainSize + validationSize);
    const testRows = indices.slice(trainSize + validationSize);

    // Store split information
    data.dataSubsets = {
      trainingRows,
      classificationRows: testRows
    };

    this.saveToStorage();

    return {
      trainingRows,
      validationRows,
      testRows
    };
  }

  /**
   * Validate file existence in backend and clean up stale files
   */
  async validateAndCleanFiles(): Promise<void> {
    const filesToRemove: string[] = [];

    for (const [fileId, fileData] of this.uploadedData.entries()) {
      try {
        // Check if file exists in backend
        await apiClient.get(`/files/${fileId}`);
      } catch (error: any) {
        if (error.response?.status === 404) {
          console.log(`File ${fileId} no longer exists in backend, removing from cache`);
          filesToRemove.push(fileId);
        }
        // For other errors (network issues, etc.), keep the file in cache
      }
    }

    // Remove stale files
    filesToRemove.forEach(fileId => {
      this.uploadedData.delete(fileId);
    });

    if (filesToRemove.length > 0) {
      this.saveToStorage();
      console.log(`Cleaned up ${filesToRemove.length} stale files from cache`);
    }
  }

  /**
   * Get all uploaded files (with optional validation)
   */
  getAllFiles(skipValidation: boolean = false): UnifiedDataUpload[] {
    // Trigger validation in background if not skipped
    if (!skipValidation) {
      this.validateAndCleanFiles().catch(error => {
        console.warn('File validation failed:', error);
      });
    }

    return Array.from(this.uploadedData.values())
      .sort((a, b) => b.lastUsed.getTime() - a.lastUsed.getTime());
  }

  /**
   * Get files for a specific purpose with validation
   */
  async getFilesForPurpose(purpose: DataPurpose): Promise<UnifiedDataUpload[]> {
    // First validate and clean files
    await this.validateAndCleanFiles();

    return Array.from(this.uploadedData.values())
      .filter(file => file.purposes[purpose])
      .sort((a, b) => b.lastUsed.getTime() - a.lastUsed.getTime());
  }

  /**
   * Get files with validation and return status
   */
  async getValidatedFiles(): Promise<{
    files: UnifiedDataUpload[];
    hasFiles: boolean;
    cleanedCount: number;
  }> {
    const initialCount = this.uploadedData.size;
    await this.validateAndCleanFiles();
    const finalCount = this.uploadedData.size;
    const cleanedCount = initialCount - finalCount;

    const files = Array.from(this.uploadedData.values())
      .sort((a, b) => b.lastUsed.getTime() - a.lastUsed.getTime());

    return {
      files,
      hasFiles: files.length > 0,
      cleanedCount
    };
  }

  /**
   * Remove a file from the manager
   */
  removeFile(fileId: string): boolean {
    const removed = this.uploadedData.delete(fileId);
    if (removed) {
      this.saveToStorage();
    }
    return removed;
  }

  /**
   * Clear all uploaded files
   */
  clearAll(): void {
    this.uploadedData.clear();
    this.saveToStorage();
  }

  /**
   * Force validation and cleanup of all files
   */
  async forceValidation(): Promise<{ cleanedCount: number; remainingCount: number }> {
    const initialCount = this.uploadedData.size;
    await this.validateAndCleanFiles();
    const finalCount = this.uploadedData.size;

    return {
      cleanedCount: initialCount - finalCount,
      remainingCount: finalCount
    };
  }

  /**
   * Handle database reset - clear all cached files
   */
  handleDatabaseReset(): void {
    console.log('Database reset detected, clearing all cached files');
    this.clearAll();
  }

  /**
   * Get usage statistics
   */
  getUsageStats(): {
    totalFiles: number;
    purposeBreakdown: Record<DataPurpose, number>;
    totalSize: number;
    oldestFile: Date | null;
    newestFile: Date | null;
  } {
    const files = this.getAllFiles();
    const stats = {
      totalFiles: files.length,
      purposeBreakdown: {
        analysis: 0,
        training: 0,
        classification: 0
      } as Record<DataPurpose, number>,
      totalSize: 0,
      oldestFile: null as Date | null,
      newestFile: null as Date | null
    };

    files.forEach(file => {
      // Count purposes
      Object.entries(file.purposes).forEach(([purpose, enabled]) => {
        if (enabled) {
          stats.purposeBreakdown[purpose as DataPurpose]++;
        }
      });

      // Calculate total size (approximate) - handle null file
      if (file.file && file.file.size) {
        stats.totalSize += file.file.size;
      } else if (file.fileInfo && file.fileInfo.size) {
        stats.totalSize += file.fileInfo.size;
      }

      // Track dates
      if (!stats.oldestFile || file.uploadedAt < stats.oldestFile) {
        stats.oldestFile = file.uploadedAt;
      }
      if (!stats.newestFile || file.uploadedAt > stats.newestFile) {
        stats.newestFile = file.uploadedAt;
      }
    });

    return stats;
  }

  /**
   * Save data to localStorage
   */
  private saveToStorage(): void {
    try {
      const serializedData = Array.from(this.uploadedData.entries()).map(([key, value]) => [
        key,
        {
          ...value,
          file: null, // Don't store File object in localStorage
          uploadedAt: value.uploadedAt.toISOString(),
          lastUsed: value.lastUsed.toISOString()
        }
      ]);
      localStorage.setItem('unifiedDataManager', JSON.stringify(serializedData));
    } catch (error) {
      console.warn('Failed to save to localStorage:', error);
    }
  }

  /**
   * Load data from localStorage
   */
  private loadFromStorage(): void {
    try {
      const stored = localStorage.getItem('unifiedDataManager');
      if (stored) {
        const serializedData = JSON.parse(stored);
        serializedData.forEach(([key, value]: [string, any]) => {
          this.uploadedData.set(key, {
            ...value,
            file: null, // File object cannot be restored
            uploadedAt: new Date(value.uploadedAt),
            lastUsed: new Date(value.lastUsed)
          });
        });
      }
    } catch (error) {
      console.warn('Failed to load from localStorage:', error);
    }
  }

  /**
   * Shuffle array using Fisher-Yates algorithm with seed
   */
  private shuffleArray(array: number[], seed: number): void {
    let random = this.seededRandom(seed);
    for (let i = array.length - 1; i > 0; i--) {
      const j = Math.floor(random() * (i + 1));
      [array[i], array[j]] = [array[j], array[i]];
    }
  }

  /**
   * Seeded random number generator
   */
  private seededRandom(seed: number): () => number {
    return function() {
      seed = (seed * 9301 + 49297) % 233280;
      return seed / 233280;
    };
  }
}

// Export singleton instance
export const unifiedDataManager = UnifiedDataManager.getInstance();

// Global utility for debugging/manual cleanup
if (typeof window !== 'undefined') {
  (window as any).clearFileCache = () => {
    unifiedDataManager.clearAll();
    console.log('File cache cleared');
  };

  (window as any).validateFiles = async () => {
    const result = await unifiedDataManager.forceValidation();
    console.log(`Validation complete: ${result.cleanedCount} files removed, ${result.remainingCount} files remaining`);
    return result;
  };
}
