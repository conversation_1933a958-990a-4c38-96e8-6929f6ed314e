// frontend/src/services/nonHierarchicalApi.ts
import apiClient from './apiClient';

export interface NonHierarchicalTrainingRequest {
  text_columns: string[];
  label_columns: string[];
  model_choice: string;
  num_epochs: number;
  validation_split: number;
  use_unsloth?: boolean;
  learning_rate?: number;
  batch_size?: number;
  label_keywords?: { [key: string]: string };
}

export interface NonHierarchicalTrainingResponse {
  job_id: string;
  message: string;
}

export interface NonHierarchicalClassificationRequest {
  texts: string[];
  model_id: string;
  confidence_threshold: number;
}

export interface NonHierarchicalClassificationResponse {
  predictions: string[][];
  confidence_scores?: { [key: string]: number }[];
  model_id: string;
  texts_processed: number;
}

export interface NonHierarchicalModel {
  id: string;
  name: string;
  created_at: string;
  parameters: {
    text_columns: string[];
    label_columns: string[];
    model_choice: string;
    num_epochs: number;
    validation_split: number;
    use_unsloth?: boolean;
    learning_rate?: number;
    batch_size?: number;
  };
  metrics: {
    num_labels: number;
    num_samples: number;
    training_time?: number;
    accuracy?: number;
  };
}

export interface NonHierarchicalModelsResponse {
  models: NonHierarchicalModel[];
}

export interface TrainingJob {
  id: string;
  status: 'processing' | 'completed' | 'failed';
  parameters: NonHierarchicalTrainingRequest;
  result?: {
    model_id: string;
    metrics: {
      training_loss: number;
      validation_loss?: number;
      accuracy?: number;
      f1_score?: number;
    };
  };
  error?: string;
  created_at: string;
  updated_at: string;
  progress?: number;
}

/**
 * Start non-hierarchical model training
 */
export const startNonHierarchicalTraining = async (
  request: NonHierarchicalTrainingRequest
): Promise<NonHierarchicalTrainingResponse> => {
  const formData = new FormData();
  
  // Add training parameters to form data
  formData.append('text_columns', JSON.stringify(request.text_columns));
  formData.append('label_columns', JSON.stringify(request.label_columns));
  formData.append('model_choice', request.model_choice);
  formData.append('num_epochs', request.num_epochs.toString());
  formData.append('validation_split', request.validation_split.toString());

  // Add label keywords if provided
  if (request.label_keywords) {
    formData.append('label_keywords', JSON.stringify(request.label_keywords));
  }

  if (request.use_unsloth) {
    formData.append('use_unsloth', 'true');
    if (request.learning_rate) {
      formData.append('learning_rate', request.learning_rate.toString());
    }
    if (request.batch_size) {
      formData.append('batch_size', request.batch_size.toString());
    }
  }

  const response = await apiClient.post('/api/non-hierarchical/train', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });

  return response.data;
};

/**
 * Classify texts using a trained non-hierarchical model
 */
export const classifyNonHierarchical = async (
  request: NonHierarchicalClassificationRequest
): Promise<NonHierarchicalClassificationResponse> => {
  const response = await apiClient.post('/api/non-hierarchical/classify', request);
  return response.data;
};

/**
 * List all non-hierarchical models for the current user
 */
export const listNonHierarchicalModels = async (): Promise<NonHierarchicalModelsResponse> => {
  const response = await apiClient.get('/api/non-hierarchical/models');
  return response.data;
};

/**
 * Get training job status
 */
export const getNonHierarchicalTrainingJob = async (jobId: string): Promise<TrainingJob> => {
  const response = await apiClient.get(`/api/non-hierarchical/jobs/${jobId}`);
  return response.data;
};

/**
 * Delete a non-hierarchical model
 */
export const deleteNonHierarchicalModel = async (modelId: string): Promise<void> => {
  await apiClient.delete(`/api/non-hierarchical/models/${modelId}`);
};

/**
 * Get model details
 */
export const getNonHierarchicalModelDetails = async (modelId: string): Promise<NonHierarchicalModel> => {
  const response = await apiClient.get(`/api/non-hierarchical/models/${modelId}`);
  return response.data;
};

/**
 * Batch classify texts from uploaded file
 */
export const batchClassifyNonHierarchical = async (
  modelId: string,
  confidenceThreshold: number = 0.5
): Promise<{ job_id: string; message: string }> => {
  const response = await apiClient.post('/api/non-hierarchical/batch-classify', {
    model_id: modelId,
    confidence_threshold: confidenceThreshold
  });
  return response.data;
};

/**
 * Compare multiple non-hierarchical models
 */
export const compareNonHierarchicalModels = async (
  modelIds: string[],
  testTexts: string[]
): Promise<{
  models: NonHierarchicalModel[];
  comparisons: {
    model_id: string;
    predictions: string[][];
    average_confidence: number;
    prediction_time_ms: number;
  }[];
}> => {
  const response = await apiClient.post('/api/non-hierarchical/compare', {
    model_ids: modelIds,
    test_texts: testTexts
  });
  return response.data;
};

/**
 * Get model performance metrics
 */
export const getNonHierarchicalModelMetrics = async (
  modelId: string
): Promise<{
  model_id: string;
  metrics: {
    accuracy: number;
    precision: number;
    recall: number;
    f1_score: number;
    label_metrics: { [label: string]: { precision: number; recall: number; f1_score: number } };
  };
}> => {
  const response = await apiClient.get(`/api/non-hierarchical/models/${modelId}/metrics`);
  return response.data;
};

/**
 * Export model predictions to various formats
 */
export const exportNonHierarchicalResults = async (
  jobId: string,
  format: 'csv' | 'excel' | 'json' = 'csv'
): Promise<Blob> => {
  const response = await apiClient.get(`/api/non-hierarchical/jobs/${jobId}/export`, {
    params: { format },
    responseType: 'blob'
  });
  return response.data;
};

/**
 * Get training progress for active jobs
 */
export const getNonHierarchicalTrainingProgress = async (
  jobId: string
): Promise<{
  job_id: string;
  status: string;
  progress: number;
  current_epoch?: number;
  total_epochs?: number;
  current_loss?: number;
  estimated_time_remaining?: number;
}> => {
  const response = await apiClient.get(`/api/non-hierarchical/jobs/${jobId}/progress`);
  return response.data;
};

/**
 * Cancel a running training job
 */
export const cancelNonHierarchicalTraining = async (jobId: string): Promise<void> => {
  await apiClient.post(`/api/non-hierarchical/jobs/${jobId}/cancel`);
};

/**
 * Get model training logs
 */
export const getNonHierarchicalTrainingLogs = async (
  jobId: string
): Promise<{
  job_id: string;
  logs: string[];
  last_updated: string;
}> => {
  const response = await apiClient.get(`/api/non-hierarchical/jobs/${jobId}/logs`);
  return response.data;
};

/**
 * Validate model configuration before training
 */
export const validateNonHierarchicalConfig = async (
  request: NonHierarchicalTrainingRequest
): Promise<{
  is_valid: boolean;
  warnings: string[];
  errors: string[];
  estimated_training_time?: number;
  estimated_memory_usage?: number;
}> => {
  const response = await apiClient.post('/api/non-hierarchical/validate-config', request);
  return response.data;
};

/**
 * Get available base models for non-hierarchical training
 */
export const getAvailableNonHierarchicalModels = async (): Promise<{
  standard_models: string[];
  unsloth_models: string[];
  model_info: {
    [model: string]: {
      description: string;
      parameters: number;
      memory_requirement: string;
      training_speed: 'fast' | 'medium' | 'slow';
    };
  };
}> => {
  const response = await apiClient.get('/api/non-hierarchical/available-models');
  return response.data;
};

/**
 * Get recommendations for model selection based on data
 */
export const getNonHierarchicalModelRecommendations = async (
  dataStats: {
    num_samples: number;
    num_labels: number;
    avg_text_length: number;
    label_distribution: { [label: string]: number };
  }
): Promise<{
  recommended_model: string;
  recommended_epochs: number;
  recommended_batch_size: number;
  reasoning: string;
  alternatives: Array<{
    model: string;
    pros: string[];
    cons: string[];
  }>;
}> => {
  const response = await apiClient.post('/api/non-hierarchical/recommendations', dataStats);
  return response.data;
};
