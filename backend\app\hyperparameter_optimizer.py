"""Advanced Hyperparameter Optimization for ClassyWeb ML Platform Phase 3.

This module implements comprehensive hyperparameter optimization with:
- Bayesian optimization using Gaussian processes
- Grid search and random search strategies
- Performance-based recommendations and adaptive tuning
- Multi-objective optimization for different classification types
"""

import logging
import time
import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple, Union, Callable
from dataclasses import dataclass, field
from concurrent.futures import ThreadPoolExecutor, as_completed
import json

# Bayesian optimization imports
try:
    from skopt import gp_minimize, forest_minimize, gbrt_minimize
    from skopt.space import Real, Integer, Categorical
    from skopt.utils import use_named_args
    from skopt.acquisition import gaussian_ei, gaussian_pi, gaussian_lcb
    SKOPT_AVAILABLE = True
except ImportError:
    SKOPT_AVAILABLE = False
    logging.warning("scikit-optimize not available. Bayesian optimization will be disabled.")

# Standard optimization imports
from sklearn.model_selection import ParameterGrid, ParameterSampler
from sklearn.metrics import f1_score, accuracy_score, precision_score, recall_score

logger = logging.getLogger(__name__)


@dataclass
class OptimizationSpace:
    """Defines the hyperparameter search space for optimization."""
    learning_rate: Tuple[float, float] = (1e-6, 1e-2)
    batch_size: List[int] = field(default_factory=lambda: [8, 16, 32, 64])
    num_epochs: Tuple[int, int] = (1, 10)
    warmup_steps: Tuple[int, int] = (0, 1000)
    weight_decay: Tuple[float, float] = (0.0, 0.3)
    dropout_rate: Tuple[float, float] = (0.0, 0.5)
    gradient_accumulation_steps: List[int] = field(default_factory=lambda: [1, 2, 4, 8])
    
    # Classification-specific parameters
    threshold: Tuple[float, float] = (0.1, 0.9)  # For binary/multilabel
    class_weight: List[str] = field(default_factory=lambda: ['balanced', 'none'])
    
    # Advanced parameters
    scheduler_type: List[str] = field(default_factory=lambda: ['linear', 'cosine', 'polynomial'])
    optimizer_type: List[str] = field(default_factory=lambda: ['adamw', 'adam', 'sgd'])


@dataclass
class OptimizationResult:
    """Results from hyperparameter optimization."""
    best_params: Dict[str, Any]
    best_score: float
    optimization_history: List[Dict[str, Any]]
    total_time: float
    n_trials: int
    optimization_method: str
    convergence_info: Dict[str, Any] = field(default_factory=dict)
    cross_validation_scores: List[float] = field(default_factory=list))


class BayesianOptimizer:
    """Bayesian optimization using Gaussian processes."""
    
    def __init__(self, classification_type: str, metric: str = 'f1_macro'):
        self.classification_type = classification_type
        self.metric = metric
        self.optimization_history = []
        self.best_score = -np.inf
        self.best_params = {}
        
        if not SKOPT_AVAILABLE:
            raise ImportError("scikit-optimize is required for Bayesian optimization")
    
    def create_search_space(self, space_config: OptimizationSpace) -> List:
        """Create scikit-optimize search space."""
        search_space = []
        
        # Learning rate (log-uniform)
        search_space.append(Real(space_config.learning_rate[0], space_config.learning_rate[1], 
                                prior='log-uniform', name='learning_rate'))
        
        # Batch size (categorical)
        search_space.append(Categorical(space_config.batch_size, name='batch_size'))
        
        # Number of epochs
        search_space.append(Integer(space_config.num_epochs[0], space_config.num_epochs[1], 
                                   name='num_epochs'))
        
        # Warmup steps
        search_space.append(Integer(space_config.warmup_steps[0], space_config.warmup_steps[1], 
                                   name='warmup_steps'))
        
        # Weight decay
        search_space.append(Real(space_config.weight_decay[0], space_config.weight_decay[1], 
                                name='weight_decay'))
        
        # Dropout rate
        search_space.append(Real(space_config.dropout_rate[0], space_config.dropout_rate[1], 
                                name='dropout_rate'))
        
        # Gradient accumulation steps
        search_space.append(Categorical(space_config.gradient_accumulation_steps, 
                                       name='gradient_accumulation_steps'))
        
        # Classification-specific parameters
        if self.classification_type in ['binary', 'multilabel']:
            search_space.append(Real(space_config.threshold[0], space_config.threshold[1], 
                                    name='threshold'))
        
        # Class weight
        search_space.append(Categorical(space_config.class_weight, name='class_weight'))
        
        # Scheduler type
        search_space.append(Categorical(space_config.scheduler_type, name='scheduler_type'))
        
        # Optimizer type
        search_space.append(Categorical(space_config.optimizer_type, name='optimizer_type'))
        
        return search_space
    
    def optimize(
        self, 
        objective_function: Callable,
        space_config: OptimizationSpace,
        n_calls: int = 50,
        n_initial_points: int = 10,
        acquisition_function: str = 'EI',
        random_state: int = 42
    ) -> OptimizationResult:
        """Perform Bayesian optimization."""
        
        start_time = time.time()
        search_space = self.create_search_space(space_config)
        
        # Map acquisition function names
        acq_func_map = {
            'EI': gaussian_ei,
            'PI': gaussian_pi,
            'LCB': gaussian_lcb
        }
        
        acq_func = acq_func_map.get(acquisition_function, gaussian_ei)
        
        @use_named_args(search_space)
        def objective(**params):
            """Objective function wrapper for scikit-optimize."""
            try:
                score = objective_function(params)
                
                # Store optimization history
                self.optimization_history.append({
                    'params': params.copy(),
                    'score': score,
                    'timestamp': time.time()
                })
                
                # Update best score (scikit-optimize minimizes, so we negate)
                if score > self.best_score:
                    self.best_score = score
                    self.best_params = params.copy()
                
                # Return negative score for minimization
                return -score
                
            except Exception as e:
                logger.error(f"Objective function evaluation failed: {e}")
                return 1.0  # Return high value (bad score) for minimization
        
        # Perform Bayesian optimization
        try:
            result = gp_minimize(
                func=objective,
                dimensions=search_space,
                n_calls=n_calls,
                n_initial_points=n_initial_points,
                acquisition_func=acq_func,
                random_state=random_state,
                n_jobs=1  # Keep single-threaded for stability
            )
            
            # Extract results
            best_params_list = result.x
            best_params = {}
            for i, param_name in enumerate([dim.name for dim in search_space]):
                best_params[param_name] = best_params_list[i]
            
            optimization_time = time.time() - start_time
            
            # Calculate convergence info
            convergence_info = {
                'converged': len(self.optimization_history) >= n_calls,
                'improvement_rate': self._calculate_improvement_rate(),
                'final_acquisition_value': float(result.func_vals[-1]) if result.func_vals else 0.0
            }
            
            return OptimizationResult(
                best_params=best_params,
                best_score=self.best_score,
                optimization_history=self.optimization_history,
                total_time=optimization_time,
                n_trials=len(self.optimization_history),
                optimization_method='bayesian_gp',
                convergence_info=convergence_info
            )
            
        except Exception as e:
            logger.error(f"Bayesian optimization failed: {e}")
            # Return best result found so far
            return OptimizationResult(
                best_params=self.best_params,
                best_score=self.best_score,
                optimization_history=self.optimization_history,
                total_time=time.time() - start_time,
                n_trials=len(self.optimization_history),
                optimization_method='bayesian_gp_partial',
                convergence_info={'error': str(e)}
            )
    
    def _calculate_improvement_rate(self) -> float:
        """Calculate the rate of improvement over optimization history."""
        if len(self.optimization_history) < 2:
            return 0.0
        
        scores = [entry['score'] for entry in self.optimization_history]
        
        # Calculate improvement rate as slope of best scores over time
        best_scores = []
        current_best = -np.inf
        
        for score in scores:
            if score > current_best:
                current_best = score
            best_scores.append(current_best)
        
        if len(best_scores) < 2:
            return 0.0
        
        # Simple linear regression slope
        x = np.arange(len(best_scores))
        y = np.array(best_scores)
        
        if np.std(x) == 0:
            return 0.0
        
        slope = np.corrcoef(x, y)[0, 1] * (np.std(y) / np.std(x))
        return float(slope)


class GridSearchOptimizer:
    """Grid search optimization with parallel execution."""
    
    def __init__(self, classification_type: str, metric: str = 'f1_macro'):
        self.classification_type = classification_type
        self.metric = metric
        self.optimization_history = []
    
    def create_parameter_grid(self, space_config: OptimizationSpace) -> Dict[str, List]:
        """Create parameter grid for grid search."""
        param_grid = {
            'learning_rate': np.logspace(
                np.log10(space_config.learning_rate[0]), 
                np.log10(space_config.learning_rate[1]), 
                5
            ).tolist(),
            'batch_size': space_config.batch_size,
            'num_epochs': list(range(space_config.num_epochs[0], space_config.num_epochs[1] + 1, 2)),
            'warmup_steps': [0, 100, 500],
            'weight_decay': np.linspace(space_config.weight_decay[0], space_config.weight_decay[1], 4).tolist(),
            'gradient_accumulation_steps': space_config.gradient_accumulation_steps,
            'class_weight': space_config.class_weight,
            'scheduler_type': space_config.scheduler_type[:2],  # Limit for grid search
            'optimizer_type': space_config.optimizer_type[:2]   # Limit for grid search
        }
        
        # Add classification-specific parameters
        if self.classification_type in ['binary', 'multilabel']:
            param_grid['threshold'] = np.linspace(space_config.threshold[0], space_config.threshold[1], 5).tolist()
        
        return param_grid
    
    def optimize(
        self, 
        objective_function: Callable,
        space_config: OptimizationSpace,
        max_combinations: int = 100,
        n_jobs: int = 4
    ) -> OptimizationResult:
        """Perform grid search optimization."""
        
        start_time = time.time()
        param_grid = self.create_parameter_grid(space_config)
        
        # Create parameter combinations
        param_combinations = list(ParameterGrid(param_grid))
        
        # Limit combinations if too many
        if len(param_combinations) > max_combinations:
            logger.info(f"Limiting grid search to {max_combinations} combinations (from {len(param_combinations)})")
            np.random.seed(42)
            param_combinations = np.random.choice(param_combinations, max_combinations, replace=False).tolist()
        
        best_score = -np.inf
        best_params = {}
        
        # Parallel execution
        with ThreadPoolExecutor(max_workers=n_jobs) as executor:
            # Submit all jobs
            future_to_params = {
                executor.submit(self._evaluate_params, objective_function, params): params
                for params in param_combinations
            }
            
            # Collect results
            for future in as_completed(future_to_params):
                params = future_to_params[future]
                try:
                    score = future.result()
                    
                    self.optimization_history.append({
                        'params': params,
                        'score': score,
                        'timestamp': time.time()
                    })
                    
                    if score > best_score:
                        best_score = score
                        best_params = params
                        
                except Exception as e:
                    logger.error(f"Grid search evaluation failed for params {params}: {e}")
        
        optimization_time = time.time() - start_time
        
        return OptimizationResult(
            best_params=best_params,
            best_score=best_score,
            optimization_history=self.optimization_history,
            total_time=optimization_time,
            n_trials=len(self.optimization_history),
            optimization_method='grid_search'
        )
    
    def _evaluate_params(self, objective_function: Callable, params: Dict[str, Any]) -> float:
        """Evaluate a single parameter combination."""
        try:
            return objective_function(params)
        except Exception as e:
            logger.error(f"Parameter evaluation failed: {e}")
            return -1.0  # Return bad score


class RandomSearchOptimizer:
    """Random search optimization."""
    
    def __init__(self, classification_type: str, metric: str = 'f1_macro'):
        self.classification_type = classification_type
        self.metric = metric
        self.optimization_history = []
    
    def optimize(
        self, 
        objective_function: Callable,
        space_config: OptimizationSpace,
        n_iter: int = 50,
        n_jobs: int = 4,
        random_state: int = 42
    ) -> OptimizationResult:
        """Perform random search optimization."""
        
        start_time = time.time()
        param_grid = self._create_parameter_distributions(space_config)
        
        # Generate random parameter combinations
        param_sampler = ParameterSampler(param_grid, n_iter=n_iter, random_state=random_state)
        param_combinations = list(param_sampler)
        
        best_score = -np.inf
        best_params = {}
        
        # Parallel execution
        with ThreadPoolExecutor(max_workers=n_jobs) as executor:
            future_to_params = {
                executor.submit(self._evaluate_params, objective_function, params): params
                for params in param_combinations
            }
            
            for future in as_completed(future_to_params):
                params = future_to_params[future]
                try:
                    score = future.result()
                    
                    self.optimization_history.append({
                        'params': params,
                        'score': score,
                        'timestamp': time.time()
                    })
                    
                    if score > best_score:
                        best_score = score
                        best_params = params
                        
                except Exception as e:
                    logger.error(f"Random search evaluation failed for params {params}: {e}")
        
        optimization_time = time.time() - start_time
        
        return OptimizationResult(
            best_params=best_params,
            best_score=best_score,
            optimization_history=self.optimization_history,
            total_time=optimization_time,
            n_trials=len(self.optimization_history),
            optimization_method='random_search'
        )
    
    def _create_parameter_distributions(self, space_config: OptimizationSpace) -> Dict[str, Any]:
        """Create parameter distributions for random sampling."""
        from scipy.stats import uniform, loguniform, randint
        
        param_distributions = {
            'learning_rate': loguniform(space_config.learning_rate[0], space_config.learning_rate[1]),
            'batch_size': space_config.batch_size,
            'num_epochs': randint(space_config.num_epochs[0], space_config.num_epochs[1] + 1),
            'warmup_steps': randint(space_config.warmup_steps[0], space_config.warmup_steps[1] + 1),
            'weight_decay': uniform(space_config.weight_decay[0], 
                                   space_config.weight_decay[1] - space_config.weight_decay[0]),
            'gradient_accumulation_steps': space_config.gradient_accumulation_steps,
            'class_weight': space_config.class_weight,
            'scheduler_type': space_config.scheduler_type,
            'optimizer_type': space_config.optimizer_type
        }
        
        # Add classification-specific parameters
        if self.classification_type in ['binary', 'multilabel']:
            param_distributions['threshold'] = uniform(
                space_config.threshold[0], 
                space_config.threshold[1] - space_config.threshold[0]
            )
        
        return param_distributions
    
    def _evaluate_params(self, objective_function: Callable, params: Dict[str, Any]) -> float:
        """Evaluate a single parameter combination."""
        try:
            return objective_function(params)
        except Exception as e:
            logger.error(f"Parameter evaluation failed: {e}")
            return -1.0


class HyperparameterOptimizationManager:
    """Main manager for hyperparameter optimization with multiple strategies."""

    def __init__(self, classification_type: str, metric: str = 'f1_macro'):
        self.classification_type = classification_type
        self.metric = metric
        self.optimization_results = {}

        # Initialize optimizers
        self.optimizers = {
            'bayesian': BayesianOptimizer(classification_type, metric) if SKOPT_AVAILABLE else None,
            'grid_search': GridSearchOptimizer(classification_type, metric),
            'random_search': RandomSearchOptimizer(classification_type, metric)
        }

    def optimize(
        self,
        objective_function: Callable,
        optimization_config: Dict[str, Any],
        strategies: List[str] = None
    ) -> Dict[str, OptimizationResult]:
        """Run hyperparameter optimization with multiple strategies."""

        if strategies is None:
            strategies = ['bayesian', 'random_search'] if SKOPT_AVAILABLE else ['random_search', 'grid_search']

        # Create optimization space
        space_config = OptimizationSpace()

        # Update space config from optimization_config
        if 'space_config' in optimization_config:
            for key, value in optimization_config['space_config'].items():
                if hasattr(space_config, key):
                    setattr(space_config, key, value)

        results = {}

        for strategy in strategies:
            if strategy not in self.optimizers or self.optimizers[strategy] is None:
                logger.warning(f"Optimizer {strategy} not available, skipping")
                continue

            logger.info(f"Starting {strategy} optimization...")

            try:
                optimizer = self.optimizers[strategy]

                if strategy == 'bayesian':
                    result = optimizer.optimize(
                        objective_function=objective_function,
                        space_config=space_config,
                        n_calls=optimization_config.get('n_calls', 50),
                        n_initial_points=optimization_config.get('n_initial_points', 10),
                        acquisition_function=optimization_config.get('acquisition_function', 'EI')
                    )
                elif strategy == 'grid_search':
                    result = optimizer.optimize(
                        objective_function=objective_function,
                        space_config=space_config,
                        max_combinations=optimization_config.get('max_combinations', 100),
                        n_jobs=optimization_config.get('n_jobs', 4)
                    )
                elif strategy == 'random_search':
                    result = optimizer.optimize(
                        objective_function=objective_function,
                        space_config=space_config,
                        n_iter=optimization_config.get('n_iter', 50),
                        n_jobs=optimization_config.get('n_jobs', 4)
                    )

                results[strategy] = result
                logger.info(f"{strategy} optimization completed. Best score: {result.best_score:.4f}")

            except Exception as e:
                logger.error(f"{strategy} optimization failed: {e}")
                results[strategy] = OptimizationResult(
                    best_params={},
                    best_score=-1.0,
                    optimization_history=[],
                    total_time=0.0,
                    n_trials=0,
                    optimization_method=strategy,
                    convergence_info={'error': str(e)}
                )

        self.optimization_results = results
        return results

    def get_best_result(self) -> Tuple[str, OptimizationResult]:
        """Get the best optimization result across all strategies."""
        if not self.optimization_results:
            return None, None

        best_strategy = None
        best_result = None
        best_score = -np.inf

        for strategy, result in self.optimization_results.items():
            if result.best_score > best_score:
                best_score = result.best_score
                best_result = result
                best_strategy = strategy

        return best_strategy, best_result

    def generate_recommendations(self) -> Dict[str, Any]:
        """Generate performance-based recommendations."""
        recommendations = {
            'best_strategy': None,
            'best_params': {},
            'performance_comparison': {},
            'insights': [],
            'next_steps': []
        }

        if not self.optimization_results:
            recommendations['insights'].append("No optimization results available")
            return recommendations

        # Find best strategy and parameters
        best_strategy, best_result = self.get_best_result()

        if best_result:
            recommendations['best_strategy'] = best_strategy
            recommendations['best_params'] = best_result.best_params

            # Performance comparison
            for strategy, result in self.optimization_results.items():
                recommendations['performance_comparison'][strategy] = {
                    'best_score': result.best_score,
                    'n_trials': result.n_trials,
                    'total_time': result.total_time,
                    'time_per_trial': result.total_time / max(result.n_trials, 1)
                }

            # Generate insights
            self._generate_insights(recommendations)

            # Generate next steps
            self._generate_next_steps(recommendations, best_result)

        return recommendations

    def _generate_insights(self, recommendations: Dict[str, Any]):
        """Generate insights from optimization results."""
        insights = []

        # Compare strategies
        if len(self.optimization_results) > 1:
            scores = {strategy: result.best_score for strategy, result in self.optimization_results.items()}
            best_strategy = max(scores, key=scores.get)
            worst_strategy = min(scores, key=scores.get)

            score_diff = scores[best_strategy] - scores[worst_strategy]
            if score_diff > 0.05:
                insights.append(f"{best_strategy} significantly outperformed {worst_strategy} by {score_diff:.3f}")
            else:
                insights.append("All optimization strategies performed similarly")

        # Analyze parameter importance
        best_params = recommendations['best_params']
        if best_params:
            # Learning rate insights
            lr = best_params.get('learning_rate', 0)
            if lr < 1e-5:
                insights.append("Very low learning rate suggests need for longer training")
            elif lr > 1e-3:
                insights.append("High learning rate suggests aggressive optimization")

            # Batch size insights
            batch_size = best_params.get('batch_size', 0)
            if batch_size <= 8:
                insights.append("Small batch size may indicate memory constraints or small dataset")
            elif batch_size >= 64:
                insights.append("Large batch size suggests good hardware resources")

            # Epochs insights
            epochs = best_params.get('num_epochs', 0)
            if epochs <= 2:
                insights.append("Few epochs suggest fast convergence or overfitting risk")
            elif epochs >= 8:
                insights.append("Many epochs suggest complex optimization landscape")

        recommendations['insights'] = insights

    def _generate_next_steps(self, recommendations: Dict[str, Any], best_result: OptimizationResult):
        """Generate next steps based on optimization results."""
        next_steps = []

        # Check convergence
        if best_result.convergence_info.get('converged', False):
            next_steps.append("Optimization converged successfully - ready for production")
        else:
            next_steps.append("Consider running more optimization trials for better results")

        # Check improvement rate
        improvement_rate = best_result.convergence_info.get('improvement_rate', 0)
        if improvement_rate > 0.01:
            next_steps.append("Good improvement rate - consider extending optimization")
        elif improvement_rate < 0.001:
            next_steps.append("Low improvement rate - current parameters may be near optimal")

        # Performance-based recommendations
        if best_result.best_score < 0.7:
            next_steps.append("Low performance - consider data quality improvements or different model architecture")
        elif best_result.best_score > 0.9:
            next_steps.append("Excellent performance - validate on test set and monitor for overfitting")

        # Time-based recommendations
        avg_time_per_trial = best_result.total_time / max(best_result.n_trials, 1)
        if avg_time_per_trial > 300:  # 5 minutes
            next_steps.append("Long training times - consider model compression or hardware upgrades")

        recommendations['next_steps'] = next_steps

    def save_results(self, filepath: str):
        """Save optimization results to file."""
        try:
            results_data = {}
            for strategy, result in self.optimization_results.items():
                results_data[strategy] = {
                    'best_params': result.best_params,
                    'best_score': result.best_score,
                    'optimization_history': result.optimization_history,
                    'total_time': result.total_time,
                    'n_trials': result.n_trials,
                    'optimization_method': result.optimization_method,
                    'convergence_info': result.convergence_info
                }

            with open(filepath, 'w') as f:
                json.dump(results_data, f, indent=2, default=str)

            logger.info(f"Optimization results saved to {filepath}")

        except Exception as e:
            logger.error(f"Failed to save optimization results: {e}")

    def load_results(self, filepath: str):
        """Load optimization results from file."""
        try:
            with open(filepath, 'r') as f:
                results_data = json.load(f)

            self.optimization_results = {}
            for strategy, data in results_data.items():
                self.optimization_results[strategy] = OptimizationResult(
                    best_params=data['best_params'],
                    best_score=data['best_score'],
                    optimization_history=data['optimization_history'],
                    total_time=data['total_time'],
                    n_trials=data['n_trials'],
                    optimization_method=data['optimization_method'],
                    convergence_info=data.get('convergence_info', {})
                )

            logger.info(f"Optimization results loaded from {filepath}")

        except Exception as e:
            logger.error(f"Failed to load optimization results: {e}")


def create_objective_function(
    train_function: Callable,
    validation_data: Tuple[Any, Any],
    metric: str = 'f1_macro'
) -> Callable:
    """Create an objective function for hyperparameter optimization."""

    def objective(params: Dict[str, Any]) -> float:
        """Objective function that trains and evaluates a model."""
        try:
            # Train model with given parameters
            model, training_time = train_function(params)

            # Evaluate on validation data
            X_val, y_val = validation_data
            predictions = model.predict(X_val)

            # Calculate metric
            if metric == 'f1_macro':
                score = f1_score(y_val, predictions, average='macro')
            elif metric == 'f1_micro':
                score = f1_score(y_val, predictions, average='micro')
            elif metric == 'accuracy':
                score = accuracy_score(y_val, predictions)
            elif metric == 'precision_macro':
                score = precision_score(y_val, predictions, average='macro')
            elif metric == 'recall_macro':
                score = recall_score(y_val, predictions, average='macro')
            else:
                score = f1_score(y_val, predictions, average='macro')

            return float(score)

        except Exception as e:
            logger.error(f"Objective function evaluation failed: {e}")
            return 0.0

    return objective
