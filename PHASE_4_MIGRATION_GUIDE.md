# Phase 4 Migration Guide

This guide helps you migrate existing ClassyWeb workflows to use the new Phase 4 unified system.

## 🚀 Quick Migration (5 minutes)

### Step 1: Update App.tsx
```typescript
// Add UnifiedWorkflowProvider to your app
import { UnifiedWorkflowProvider } from '@/contexts/UnifiedWorkflowContext';

const App = () => (
  <ThemeProvider>
    <AuthProvider>
      <UnifiedDataProvider>
        <UnifiedWorkflowProvider>  {/* Add this */}
          <Routes>
            {/* Your routes */}
          </Routes>
        </UnifiedWorkflowProvider>
      </UnifiedDataProvider>
    </AuthProvider>
  </ThemeProvider>
);
```

### Step 2: Update Your Workflow Component
```typescript
// Before (Phase 3)
const MyWorkflow = () => {
  const [currentStep, setCurrentStep] = useState(1);
  const [completedSteps, setCompletedSteps] = useState([]);
  
  return (
    <div>
      {/* Your workflow content */}
    </div>
  );
};

// After (Phase 4)
import { useUnifiedWorkflow } from '@/contexts/UnifiedWorkflowContext';
import { UnifiedWorkflowNavigation } from '@/components/workflow/UnifiedWorkflowNavigation';

const MyWorkflow = () => {
  const {
    currentStep,
    completedSteps,
    navigateToStep,
    nextStep,
    previousStep,
    startNewWorkflow,
    saveProgress
  } = useUnifiedWorkflow();

  // Define your steps
  const steps = [
    { id: 1, title: "Upload", description: "Upload data", icon: Upload, status: 'current' },
    // ... more steps
  ];

  // Initialize workflow
  useEffect(() => {
    if (!currentWorkflowId) {
      startNewWorkflow('binary', 'beginner', steps);
    }
  }, []);

  return (
    <div>
      {/* Add unified navigation */}
      <UnifiedWorkflowNavigation
        steps={steps}
        currentStep={currentStep}
        onStepChange={navigateToStep}
        onNext={nextStep}
        onPrevious={previousStep}
      />
      
      {/* Your existing workflow content */}
    </div>
  );
};
```

### Step 3: Add Resume Functionality (Optional)
```typescript
import { WorkflowResumeDialog } from '@/components/workflow/WorkflowResumeDialog';

// Add to your component
{canResume && <WorkflowResumeDialog autoShow={true} />}
```

## 📋 Complete Migration Checklist

### Required Changes
- [ ] Add `UnifiedWorkflowProvider` to App.tsx
- [ ] Import and use `useUnifiedWorkflow()` hook
- [ ] Define `WorkflowStep[]` array for your workflow
- [ ] Replace custom navigation with `UnifiedWorkflowNavigation`
- [ ] Call `startNewWorkflow()` on component mount
- [ ] Call `saveProgress()` when state changes

### Optional Enhancements
- [ ] Add `WorkflowResumeDialog` for resume functionality
- [ ] Add `EnhancedGuidanceSystem` for tours and recommendations
- [ ] Use `handleError()` for unified error handling
- [ ] Add contextual help tooltips to steps
- [ ] Define guided tours for complex workflows

### Testing
- [ ] Test workflow navigation (forward/backward)
- [ ] Test progress persistence (refresh page)
- [ ] Test resume functionality
- [ ] Test keyboard navigation (arrow keys)
- [ ] Test error handling

## 🔄 Migration Examples

### Example 1: Simple Workflow Migration

**Before:**
```typescript
const SimpleWorkflow = () => {
  const [step, setStep] = useState(1);
  
  return (
    <div>
      <div>Step {step} of 3</div>
      <button onClick={() => setStep(step + 1)}>Next</button>
    </div>
  );
};
```

**After:**
```typescript
const SimpleWorkflow = () => {
  const { currentStep, nextStep, startNewWorkflow } = useUnifiedWorkflow();
  
  const steps = [
    { id: 1, title: "Step 1", description: "First step", icon: Upload, status: 'current' },
    { id: 2, title: "Step 2", description: "Second step", icon: Settings, status: 'pending' },
    { id: 3, title: "Step 3", description: "Third step", icon: CheckCircle, status: 'pending' }
  ];

  useEffect(() => {
    startNewWorkflow('flat', 'beginner', steps);
  }, []);
  
  return (
    <div>
      <UnifiedWorkflowNavigation
        steps={steps}
        currentStep={currentStep}
        onNext={nextStep}
      />
    </div>
  );
};
```

### Example 2: Complex Workflow with State

**Before:**
```typescript
const ComplexWorkflow = () => {
  const [currentStep, setCurrentStep] = useState(1);
  const [uploadedFile, setUploadedFile] = useState(null);
  const [configuration, setConfiguration] = useState({});
  const [results, setResults] = useState(null);
  
  const handleNext = () => {
    setCurrentStep(currentStep + 1);
    // Save state manually
    localStorage.setItem('workflow-state', JSON.stringify({
      step: currentStep + 1,
      file: uploadedFile,
      config: configuration
    }));
  };
  
  return <div>{/* Complex workflow */}</div>;
};
```

**After:**
```typescript
const ComplexWorkflow = () => {
  const { 
    currentStep, 
    nextStep, 
    saveProgress, 
    startNewWorkflow 
  } = useUnifiedWorkflow();
  
  const [uploadedFile, setUploadedFile] = useState(null);
  const [configuration, setConfiguration] = useState({});
  const [results, setResults] = useState(null);
  
  // Auto-save when state changes
  useEffect(() => {
    saveProgress({
      uploadedFiles: { main: uploadedFile },
      configuration,
      classificationResults: results
    });
  }, [uploadedFile, configuration, results]);
  
  const handleNext = () => {
    nextStep(); // Automatically saves progress
  };
  
  return <div>{/* Same complex workflow, but with auto-save */}</div>;
};
```

## 🎯 Benefits After Migration

### For Users
- ✅ **Resume workflows** - Never lose progress again
- ✅ **Consistent navigation** - Same experience across all workflows
- ✅ **Keyboard shortcuts** - Navigate with arrow keys
- ✅ **Progress indicators** - Always know where you are
- ✅ **Contextual help** - Tooltips and guidance when needed
- ✅ **Smart recommendations** - AI-powered suggestions

### For Developers
- ✅ **Less code** - Unified system handles navigation, persistence, errors
- ✅ **Consistent UX** - All workflows look and behave the same
- ✅ **Better testing** - Standardized components are easier to test
- ✅ **Error handling** - Automatic error categorization and recovery
- ✅ **Analytics ready** - Built-in progress tracking and metrics

## 🐛 Common Migration Issues

### Issue 1: State Management Conflicts
**Problem:** Existing state management conflicts with unified system
**Solution:** Use unified workflow state for navigation, keep domain-specific state separate

```typescript
// Good: Separate concerns
const { currentStep, nextStep } = useUnifiedWorkflow(); // Navigation
const [myData, setMyData] = useState({}); // Domain-specific state
```

### Issue 2: Custom Navigation Components
**Problem:** Existing custom navigation components
**Solution:** Replace with UnifiedWorkflowNavigation or extend it

```typescript
// Replace custom navigation
<UnifiedWorkflowNavigation
  steps={steps}
  currentStep={currentStep}
  // Add custom props if needed
  className="my-custom-styles"
/>
```

### Issue 3: URL Parameter Conflicts
**Problem:** Existing URL parameter handling conflicts
**Solution:** Use unified workflow URL parameters or namespace yours

```typescript
// Unified workflow uses: ?resume=id&step=1
// Use different parameters for your needs: ?myParam=value
```

## 📞 Support

### Getting Help
- Check the [Phase 4 Implementation Guide](./PHASE_4_IMPLEMENTATION.md)
- Review the [Integration Example](./frontend/src/examples/Phase4IntegrationExample.tsx)
- Open an issue on GitHub for specific problems

### Migration Assistance
If you need help migrating a complex workflow:
1. Create a minimal reproduction of your current workflow
2. Follow the migration steps above
3. Test thoroughly with your specific use cases
4. Open an issue if you encounter problems

---

**Migration Time Estimate:**
- Simple workflows: 15-30 minutes
- Complex workflows: 1-2 hours
- Testing and validation: 30-60 minutes

**Total: 1-3 hours per workflow**
