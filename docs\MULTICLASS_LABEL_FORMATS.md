# Multi-Class Classification - Label Format Support

## 🎯 Overview

ClassyWeb's multi-class classification workflow now supports **two distinct label formats**, providing flexibility for different data structures and use cases. This enhancement allows users to work with both traditional single-column labels and modern one-hot encoded binary columns.

## 📊 Supported Label Formats

### 1. **Single Label Column (Traditional Format)**

**Description**: One column containing class names for each sample.

**Structure**:
```csv
text_content,category
"Breaking news about sports...",Sports
"New technology breakthrough...",Technology
"Political developments...",Politics
"Economic market update...",Finance
```

**Characteristics**:
- ✅ **Simple and intuitive**: Easy to understand and create
- ✅ **Compact**: Minimal storage requirements
- ✅ **Standard**: Compatible with most ML libraries
- ✅ **Human-readable**: Class names are directly visible

**Best for**:
- Document classification
- Sentiment analysis with categories
- Topic classification
- Intent classification
- Traditional ML workflows

### 2. **Multiple Binary Columns (One-Hot Encoded Format)**

**Description**: Separate binary columns for each class, with exactly one column set to 1 per row.

**Structure**:
```csv
text_content,Sports,Technology,Politics,Finance
"Breaking news about sports...",1,0,0,0
"New technology breakthrough...",0,1,0,0
"Political developments...",0,0,1,0
"Economic market update...",0,0,0,1
```

**Characteristics**:
- ✅ **Direct binary representation**: No string-to-index mapping needed
- ✅ **Neural network ready**: Optimized for deep learning models
- ✅ **Extensible**: Can be easily modified for multi-label scenarios
- ✅ **Clear boundaries**: Explicit class membership

**Best for**:
- Pre-processed datasets from ML pipelines
- Data exported from neural networks
- Migration from multi-label to multi-class
- High-performance training scenarios

## 🔧 Implementation Details

### **Automatic Format Detection**

ClassyWeb automatically detects the label format during data upload:

```typescript
// Single Label Detection
const labelColumn = columns.find(col =>
  col.toLowerCase().includes('label') ||
  col.toLowerCase().includes('category') ||
  col.toLowerCase().includes('class')
);

// Binary Columns Detection
const binaryColumns = columns.filter(col => {
  const values = data.map(row => row[col]);
  const uniqueValues = new Set(values);
  return uniqueValues.size <= 2 && 
         Array.from(uniqueValues).every(val => 
           [0, 1, '0', '1', true, false, 'true', 'false'].includes(val)
         );
});
```

### **Data Validation**

#### Single Label Format Validation:
- ✅ Column existence verification
- ✅ Empty/null label detection
- ✅ Class name length validation
- ✅ Reserved name checking
- ✅ Minimum class count verification

#### Binary Format Validation:
- ✅ Binary value verification (0/1, true/false)
- ✅ One-hot encoding validation (exactly one 1 per row)
- ✅ Multi-label detection and warnings
- ✅ Missing class detection
- ✅ Column count validation

### **Training Configuration**

Both formats are handled seamlessly in the training pipeline:

```typescript
// Format-specific configuration
if (labelFormat === 'single') {
  labelConfig = {
    label_format: 'single',
    label_column: selectedLabelColumn,
    classes: detectedClasses
  };
} else {
  labelConfig = {
    label_format: 'multiple',
    label_columns: selectedLabelColumns,
    classes: selectedLabelColumns,
    binary_encoding: true
  };
}
```

## 📋 Data Requirements

### **Single Label Format**
- **Minimum Classes**: 3 distinct class names
- **Class Name Length**: 1-100 characters
- **Samples per Class**: Minimum 5, recommended 50+
- **Supported Values**: Strings, numbers (converted to strings)
- **Reserved Names**: Avoid 'null', 'undefined', 'none', 'empty'

### **Binary Format**
- **Minimum Columns**: 3 binary class columns
- **Maximum Columns**: 100 binary class columns
- **Valid Values**: 0, 1, '0', '1', true, false, 'true', 'false'
- **Encoding Rule**: Exactly one column = 1 per row (one-hot)
- **Samples per Class**: Minimum 5, recommended 50+

## 🎨 User Interface

### **Format Selection**
Users can choose between formats with clear descriptions:

```
○ Single Label Column (Traditional)
  Traditional Format: One column with class names (e.g., "Sports", "Tech", "Politics")

○ Multiple Binary Columns (One-Hot Encoded)  
  One-Hot Encoded Format: Multiple binary columns, one per class (e.g., Sports=1, Tech=0, Politics=0)
```

### **Dynamic Column Selection**
- **Single Format**: Dropdown to select the label column
- **Binary Format**: Checkboxes to select multiple binary columns

### **Real-time Validation**
- ✅ Format compatibility checking
- ✅ Data quality validation
- ✅ Class distribution analysis
- ✅ Imbalance ratio warnings

## 🔄 Format Conversion

### **Single to Binary Conversion**
```python
# Example conversion logic
def single_to_binary(df, text_col, label_col):
    classes = df[label_col].unique()
    for cls in classes:
        df[cls] = (df[label_col] == cls).astype(int)
    return df.drop(columns=[label_col])
```

### **Binary to Single Conversion**
```python
# Example conversion logic
def binary_to_single(df, text_col, binary_cols):
    def get_class(row):
        active = [col for col in binary_cols if row[col] == 1]
        return active[0] if len(active) == 1 else 'unknown'
    
    df['category'] = df.apply(get_class, axis=1)
    return df.drop(columns=binary_cols)
```

## 📊 Performance Considerations

### **Training Performance**
- **Single Format**: Requires string-to-index mapping during training
- **Binary Format**: Direct numerical input, faster preprocessing

### **Memory Usage**
- **Single Format**: More memory efficient (one column vs many)
- **Binary Format**: Higher memory usage but better for large-scale training

### **Inference Speed**
- **Single Format**: Requires post-processing to convert indices to names
- **Binary Format**: Direct binary output, faster inference

## 🚀 Advanced Features

### **Strategy Optimization**
Different strategies work better with different formats:

- **Softmax**: Optimized for both formats
- **One-vs-Rest**: Particularly effective with binary format
- **One-vs-One**: Works well with single format for interpretability

### **Class Imbalance Handling**
- **Single Format**: Class weights based on label frequency
- **Binary Format**: Column-wise weight calculation

### **Export Compatibility**
Both formats support all export options:
- ✅ PyTorch models
- ✅ ONNX format
- ✅ HuggingFace integration
- ✅ TensorFlow export

## 🔍 Troubleshooting

### **Common Issues**

#### Single Label Format:
- **Empty Labels**: Remove or fill missing label values
- **Inconsistent Naming**: Standardize class name formatting
- **Too Few Classes**: Ensure minimum 3 distinct classes

#### Binary Format:
- **Multiple Active Classes**: Check for multi-label data (should be single-label)
- **No Active Classes**: Ensure each row has exactly one 1
- **Invalid Values**: Use only 0/1 or true/false values

### **Data Quality Checks**
```typescript
// Validation example
const validation = validateMultipleBinaryFormat(data, textColumn, binaryColumns);
if (!validation.valid) {
  console.error('Validation errors:', validation.errors);
  console.warn('Warnings:', validation.warnings);
}
```

## 📈 Best Practices

### **Data Preparation**
1. **Consistent Formatting**: Ensure consistent value types across columns
2. **Quality Validation**: Run validation before training
3. **Class Balance**: Check for severe imbalances
4. **Text Quality**: Ensure text columns have meaningful content

### **Format Selection**
- **Choose Single** for: Human-readable data, traditional workflows, interpretability
- **Choose Binary** for: ML pipeline integration, performance optimization, neural networks

### **Training Optimization**
- **Single Format**: Use class weights for imbalanced data
- **Binary Format**: Consider batch size optimization for memory efficiency

## 🎯 Migration Guide

### **From Single to Binary**
1. Export your single-label data
2. Convert using pandas or similar tools
3. Upload the binary format data
4. Select "Multiple Binary Columns" format
5. Choose your binary columns

### **From Binary to Single**
1. Identify the active column for each row
2. Create a new category column
3. Upload the single-label data
4. Select "Single Label Column" format
5. Choose your category column

## 🚀 Future Enhancements

### **Planned Features**
- **Automatic Format Conversion**: Built-in conversion tools
- **Hybrid Support**: Mixed format handling
- **Advanced Validation**: More sophisticated data quality checks
- **Performance Metrics**: Format-specific performance analysis

---

## 🎉 **Ready for Production**

ClassyWeb's multi-class classification now supports both traditional and modern label formats, providing:

- ✅ **Flexible Data Input**: Support for diverse data structures
- ✅ **Automatic Detection**: Smart format recognition
- ✅ **Comprehensive Validation**: Robust data quality checks
- ✅ **Optimized Training**: Format-specific optimizations
- ✅ **Professional Results**: High-quality classification outcomes

Choose the format that best fits your data and workflow requirements!
