import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Brain, Menu, X, User, LogOut, Sun, Moon } from "lucide-react";
import { useState } from "react";
import { useAuthStore } from "@/store/authStore";
import { logoutUser } from "@/services/authApi";
import { useNavigate, Link } from "react-router-dom";
import { useTheme } from "@/components/ThemeProvider";

export const Navigation = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const { user, logout } = useAuthStore();
  const { actualTheme, setTheme } = useTheme();
  const navigate = useNavigate();

  const toggleMenu = () => setIsMenuOpen(!isMenuOpen);

  const toggleTheme = () => {
    // Toggle between light and dark, avoiding system for direct user control
    const newTheme = actualTheme === "light" ? "dark" : "light";
    setTheme(newTheme);
  };

  const handleLogout = async () => {
    try {
      await logoutUser();
      logout();
      navigate('/login');
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  const handleProfile = () => {
    navigate('/profile');
  };

  return (
    <nav className="fixed top-0 w-full z-50 bg-background/98 backdrop-blur-xl border-b border-border/50 shadow-sm">
      <div className="max-w-7xl mx-auto px-6">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <Link to="/" className="flex items-center gap-2 hover:opacity-80 transition-opacity">
            <div className="w-9 h-9 rounded-lg bg-primary flex items-center justify-center shadow-sm">
              <Brain className="w-5 h-5 text-primary-foreground" />
            </div>
            <div className="flex flex-col">
              <span className="text-xl font-bold text-foreground leading-none">
                ClassyWeb
              </span>
              <Badge variant="secondary" className="text-[10px] px-1 py-0 h-4 w-fit">
                Beta
              </Badge>
            </div>
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-6">
            <Link to="/#features" className="text-foreground hover:text-primary transition-colors font-medium text-sm">
              Features
            </Link>
            <Link to="/#classification" className="text-foreground hover:text-primary transition-colors font-medium text-sm">
              Types
            </Link>
            {user && (
              <Link to="/dashboard" className="text-foreground hover:text-primary transition-colors font-medium text-sm">
                Dashboard
              </Link>
            )}
            <Link to="/get-started" className="text-foreground hover:text-primary transition-colors font-medium text-sm">
              Workflows
            </Link>
            <a href="#docs" className="text-foreground hover:text-primary transition-colors font-medium text-sm">
              Docs
            </a>
          </div>

          {/* Desktop Actions */}
          <div className="hidden md:flex items-center space-x-3">
            {/* Theme Toggle */}
            <Button
              variant="ghost"
              size="icon"
              onClick={toggleTheme}
              className="w-9 h-9 hover:bg-primary/10"
              title={`Switch to ${actualTheme === "light" ? "dark" : "light"} mode`}
            >
              {actualTheme === "light" ? (
                <Moon className="w-4 h-4" />
              ) : (
                <Sun className="w-4 h-4" />
              )}
            </Button>

            {user ? (
              <div className="flex items-center space-x-2 pl-2 border-l border-border">
                <span className="text-sm text-foreground/70 max-w-[120px] truncate">
                  {user.username || user.email}
                </span>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={handleProfile}
                  className="w-9 h-9 hover:bg-primary/10"
                >
                  <User className="w-4 h-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={handleLogout}
                  className="w-9 h-9 hover:bg-destructive/10 hover:text-destructive"
                >
                  <LogOut className="w-4 h-4" />
                </Button>
              </div>
            ) : (
              <>
                <Button variant="ghost" size="sm" asChild>
                  <Link to="/login">Sign In</Link>
                </Button>
                <Button size="sm" className="bg-primary hover:bg-primary/90" asChild>
                  <Link to="/get-started">Get Started</Link>
                </Button>
              </>
            )}
          </div>

          {/* Mobile Menu Button */}
          <div className="md:hidden">
            <Button variant="ghost" size="icon" onClick={toggleMenu}>
              {isMenuOpen ? <X className="w-5 h-5" /> : <Menu className="w-5 h-5" />}
            </Button>
          </div>
        </div>

        {/* Mobile Menu */}
        {isMenuOpen && (
          <div className="md:hidden py-4 border-t border-border bg-background/98 backdrop-blur-lg">
            <div className="flex flex-col space-y-3">
              <Link to="/#features" className="text-foreground hover:text-primary transition-colors px-3 py-2 rounded-md hover:bg-primary/5 font-medium text-sm">
                Features
              </Link>
              <Link to="/#classification" className="text-foreground hover:text-primary transition-colors px-3 py-2 rounded-md hover:bg-primary/5 font-medium text-sm">
                Classification Types
              </Link>
              {user && (
                <Link to="/dashboard" className="text-foreground hover:text-primary transition-colors px-3 py-2 rounded-md hover:bg-primary/5 font-medium text-sm">
                  Dashboard
                </Link>
              )}
              <Link to="/get-started" className="text-foreground hover:text-primary transition-colors px-3 py-2 rounded-md hover:bg-primary/5 font-medium text-sm">
                Workflows
              </Link>
              <a href="#docs" className="text-foreground hover:text-primary transition-colors px-3 py-2 rounded-md hover:bg-primary/5 font-medium text-sm">
                Documentation
              </a>

              <div className="flex flex-col space-y-2 pt-4 border-t border-border">
                {/* Theme Toggle for Mobile */}
                <Button variant="ghost" className="justify-start text-foreground hover:bg-primary/5" onClick={toggleTheme}>
                  {actualTheme === "light" ? (
                    <>
                      <Moon className="w-4 h-4 mr-2" />
                      Dark Mode
                    </>
                  ) : (
                    <>
                      <Sun className="w-4 h-4 mr-2" />
                      Light Mode
                    </>
                  )}
                </Button>

                {user ? (
                  <>
                    <div className="px-3 py-2 text-sm text-foreground/70 font-medium border-b border-border">
                      {user.username || user.email}
                    </div>
                    <Button variant="ghost" className="justify-start text-foreground hover:bg-primary/5" onClick={handleProfile}>
                      <User className="w-4 h-4 mr-2" />
                      Profile
                    </Button>
                    <Button variant="ghost" className="justify-start text-destructive hover:bg-destructive/5" onClick={handleLogout}>
                      <LogOut className="w-4 h-4 mr-2" />
                      Logout
                    </Button>
                  </>
                ) : (
                  <>
                    <Button variant="ghost" className="justify-start text-foreground hover:bg-primary/5" asChild>
                      <Link to="/login">Sign In</Link>
                    </Button>
                    <Button className="justify-start bg-primary hover:bg-primary/90" asChild>
                      <Link to="/get-started">Get Started</Link>
                    </Button>
                  </>
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    </nav>
  );
};
