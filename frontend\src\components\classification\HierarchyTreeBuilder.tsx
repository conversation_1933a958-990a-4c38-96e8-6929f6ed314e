/**
 * HierarchyTreeBuilder.tsx
 * 
 * Visual tree structure builder for hierarchical classification
 */

import React, { useState, useEffect, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { ScrollArea } from "@/components/ui/scroll-area";
import { 
  TreePine, 
  Plus, 
  Minus, 
  Search, 
  Filter,
  ChevronRight,
  ChevronDown,
  Info,
  AlertCircle
} from "lucide-react";

interface HierarchyNode {
  id: string;
  name: string;
  level: number;
  parentId?: string;
  children: HierarchyNode[];
  count?: number;
  examples?: string[];
  isExpanded?: boolean;
}

interface HierarchyConstraintInfo {
  parentValue: string;
  parentLevel: number;
  childLevel: number;
  allowedChildren: string[];
}

interface HierarchyTreeBuilderProps {
  data: Record<string, any>[];
  hierarchyLevels: Array<{ name: string; column: string; order: number }>;
  onTreeChange: (tree: HierarchyNode[]) => void;
  onConstraintsChange: (constraints: Record<string, string[]>, constraintInfo?: HierarchyConstraintInfo[]) => void;
  maxNodesPerLevel?: number;
}

export const HierarchyTreeBuilder: React.FC<HierarchyTreeBuilderProps> = ({
  data,
  hierarchyLevels,
  onTreeChange,
  onConstraintsChange,
  maxNodesPerLevel = 100
}) => {
  const [tree, setTree] = useState<HierarchyNode[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedLevel, setSelectedLevel] = useState<number | null>(null);
  const [expandedNodes, setExpandedNodes] = useState<Set<string>>(new Set());

  // Build tree from data
  const buildTreeFromData = useMemo(() => {
    if (process.env.NODE_ENV === 'development') {
      console.log('HierarchyTreeBuilder: Building tree from data:', {
        dataLength: data?.length || 0,
        hierarchyLevelsLength: hierarchyLevels?.length || 0,
        hierarchyLevels: hierarchyLevels,
        sampleData: data?.slice(0, 2)
      });
    }

    if (!data || data.length === 0 || hierarchyLevels.length === 0) {
      if (process.env.NODE_ENV === 'development') {
        console.log('HierarchyTreeBuilder: No data or hierarchy levels, returning empty tree');
      }
      return [];
    }

    const nodeMap = new Map<string, HierarchyNode>();
    const rootNodes: HierarchyNode[] = [];

    // Sort hierarchy levels by order
    const sortedLevels = [...hierarchyLevels].sort((a, b) => a.order - b.order);

    // Process each data row
    data.forEach((row, rowIndex) => {
      let currentParentId: string | undefined = undefined;

      sortedLevels.forEach((level, levelIndex) => {
        const value = row[level.column];
        if (!value) return;

        const nodeId = currentParentId 
          ? `${currentParentId}/${value}` 
          : `${levelIndex}/${value}`;

        if (!nodeMap.has(nodeId)) {
          const node: HierarchyNode = {
            id: nodeId,
            name: String(value),
            level: levelIndex,
            parentId: currentParentId,
            children: [],
            count: 0,
            examples: [],
            isExpanded: levelIndex < 2 // Auto-expand first 2 levels
          };

          nodeMap.set(nodeId, node);

          if (currentParentId) {
            const parent = nodeMap.get(currentParentId);
            if (parent) {
              parent.children.push(node);
            }
          } else {
            rootNodes.push(node);
          }
        }

        const node = nodeMap.get(nodeId)!;
        node.count = (node.count || 0) + 1;

        // Add example if we don't have too many
        if (node.examples && node.examples.length < 3) {
          const textColumn = Object.keys(row).find(key => 
            key.toLowerCase().includes('text') || 
            key.toLowerCase().includes('content') ||
            key.toLowerCase().includes('description')
          );
          if (textColumn && row[textColumn]) {
            const example = String(row[textColumn]).substring(0, 100);
            if (!node.examples.includes(example)) {
              node.examples.push(example);
            }
          }
        }

        currentParentId = nodeId;
      });
    });

    // Sort children by count (descending)
    const sortChildren = (nodes: HierarchyNode[]) => {
      nodes.forEach(node => {
        node.children.sort((a, b) => (b.count || 0) - (a.count || 0));
        sortChildren(node.children);
      });
    };

    sortChildren(rootNodes);
    rootNodes.sort((a, b) => (b.count || 0) - (a.count || 0));

    return rootNodes;
  }, [data, hierarchyLevels]);

  useEffect(() => {
    setTree(buildTreeFromData);
    
    // Auto-expand first level nodes
    const firstLevelIds = buildTreeFromData.map(node => node.id);
    setExpandedNodes(new Set(firstLevelIds));
  }, [buildTreeFromData]);

  useEffect(() => {
    onTreeChange(tree);

    // Generate enhanced constraints with detailed level information
    const constraints: Record<string, string[]> = {};
    const constraintInfo: HierarchyConstraintInfo[] = [];

    const extractConstraints = (nodes: HierarchyNode[]) => {
      nodes.forEach(node => {
        if (node.children.length > 0) {
          const childrenNames = node.children.map(child => child.name);
          constraints[node.name] = childrenNames;

          // Add detailed constraint information with proper level mapping
          constraintInfo.push({
            parentValue: node.name,
            parentLevel: node.level,
            childLevel: node.level + 1,
            allowedChildren: childrenNames
          });

          extractConstraints(node.children);
        }
      });
    };

    extractConstraints(tree);

    // Debug logging (only in development)
    if (process.env.NODE_ENV === 'development') {
      console.log('HierarchyTreeBuilder: Tree built with', tree.length, 'root nodes');
      console.log('HierarchyTreeBuilder: Extracted constraints:', constraints);
      console.log('HierarchyTreeBuilder: Constraint info:', constraintInfo);
    }

    onConstraintsChange(constraints, constraintInfo);
  }, [tree, onTreeChange, onConstraintsChange]);

  const toggleNodeExpansion = (nodeId: string) => {
    const newExpanded = new Set(expandedNodes);
    if (newExpanded.has(nodeId)) {
      newExpanded.delete(nodeId);
    } else {
      newExpanded.add(nodeId);
    }
    setExpandedNodes(newExpanded);
  };

  const filterNodes = (nodes: HierarchyNode[], term: string, level: number | null): HierarchyNode[] => {
    if (!term && level === null) return nodes;

    return nodes.filter(node => {
      const matchesSearch = !term || node.name.toLowerCase().includes(term.toLowerCase());
      const matchesLevel = level === null || node.level === level;
      const hasMatchingChildren = node.children.length > 0 && 
        filterNodes(node.children, term, level).length > 0;

      return (matchesSearch && matchesLevel) || hasMatchingChildren;
    }).map(node => ({
      ...node,
      children: filterNodes(node.children, term, level)
    }));
  };

  const renderNode = (node: HierarchyNode, depth: number = 0) => {
    const isExpanded = expandedNodes.has(node.id);
    const hasChildren = node.children.length > 0;
    const levelInfo = hierarchyLevels.find(l => l.order === node.level);

    return (
      <div key={node.id} className="space-y-1">
        <div 
          className={`flex items-center gap-2 p-2 rounded hover:bg-muted/50 cursor-pointer ${
            depth > 0 ? 'ml-' + (depth * 4) : ''
          }`}
          style={{ marginLeft: depth * 16 }}
          onClick={() => hasChildren && toggleNodeExpansion(node.id)}
        >
          {hasChildren ? (
            isExpanded ? (
              <ChevronDown className="w-4 h-4 text-muted-foreground" />
            ) : (
              <ChevronRight className="w-4 h-4 text-muted-foreground" />
            )
          ) : (
            <div className="w-4 h-4" />
          )}

          <div className="flex-1 flex items-center gap-2">
            <span className="font-medium">{node.name}</span>
            <Badge variant="secondary" className="text-xs">
              {node.count} items
            </Badge>
            {levelInfo && (
              <Badge variant="outline" className="text-xs">
                {levelInfo.name}
              </Badge>
            )}
          </div>
        </div>

        {isExpanded && hasChildren && (
          <div className="space-y-1">
            {node.children.map(child => renderNode(child, depth + 1))}
          </div>
        )}
      </div>
    );
  };

  const filteredTree = filterNodes(tree, searchTerm, selectedLevel);

  const getTreeStats = () => {
    const stats = {
      totalNodes: 0,
      nodesByLevel: {} as Record<number, number>,
      maxDepth: 0
    };

    const countNodes = (nodes: HierarchyNode[], currentDepth: number = 0) => {
      stats.maxDepth = Math.max(stats.maxDepth, currentDepth);
      
      nodes.forEach(node => {
        stats.totalNodes++;
        stats.nodesByLevel[node.level] = (stats.nodesByLevel[node.level] || 0) + 1;
        countNodes(node.children, currentDepth + 1);
      });
    };

    countNodes(tree);
    return stats;
  };

  const stats = getTreeStats();

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <TreePine className="w-5 h-5" />
          Hierarchy Tree Structure
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Info Alert */}
        <Alert>
          <Info className="h-4 w-4" />
          <AlertDescription>
            This tree shows the hierarchical structure detected in your data. 
            Each node represents a unique value at that hierarchy level.
          </AlertDescription>
        </Alert>

        {/* Tree Statistics */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center p-3 bg-muted/30 rounded">
            <div className="text-2xl font-bold">{stats.totalNodes}</div>
            <div className="text-sm text-muted-foreground">Total Nodes</div>
          </div>
          <div className="text-center p-3 bg-muted/30 rounded">
            <div className="text-2xl font-bold">{stats.maxDepth}</div>
            <div className="text-sm text-muted-foreground">Max Depth</div>
          </div>
          <div className="text-center p-3 bg-muted/30 rounded">
            <div className="text-2xl font-bold">{hierarchyLevels.length}</div>
            <div className="text-sm text-muted-foreground">Levels</div>
          </div>
          <div className="text-center p-3 bg-muted/30 rounded">
            <div className="text-2xl font-bold">{data.length}</div>
            <div className="text-sm text-muted-foreground">Data Points</div>
          </div>
        </div>

        {/* Level Statistics */}
        <div className="space-y-2">
          <h4 className="font-semibold">Nodes per Level</h4>
          <div className="grid gap-2">
            {hierarchyLevels.map(level => (
              <div key={level.order} className="flex items-center justify-between p-2 bg-muted/20 rounded">
                <span className="font-medium">{level.name}</span>
                <div className="flex items-center gap-2">
                  <Badge variant="secondary">
                    {stats.nodesByLevel[level.order] || 0} nodes
                  </Badge>
                  {(stats.nodesByLevel[level.order] || 0) > maxNodesPerLevel && (
                    <AlertCircle className="w-4 h-4 text-orange-500" />
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Search and Filter */}
        <div className="flex gap-2">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
            <Input
              placeholder="Search nodes..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          <Button
            variant="outline"
            onClick={() => setSelectedLevel(selectedLevel === null ? 0 : null)}
          >
            <Filter className="w-4 h-4 mr-2" />
            {selectedLevel !== null ? `Level ${selectedLevel + 1}` : 'All Levels'}
          </Button>
        </div>

        {/* Tree Visualization */}
        <ScrollArea className="h-96 border rounded p-4">
          {filteredTree.length > 0 ? (
            <div className="space-y-1">
              {filteredTree.map(node => renderNode(node))}
            </div>
          ) : (
            <div className="text-center text-muted-foreground py-8">
              {searchTerm || selectedLevel !== null ? 
                'No nodes match your filter criteria' : 
                'No hierarchy structure detected'
              }
            </div>
          )}
        </ScrollArea>

        {/* Warnings */}
        {Object.values(stats.nodesByLevel).some(count => count > maxNodesPerLevel) && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              Some hierarchy levels have more than {maxNodesPerLevel} nodes. 
              This may impact performance. Consider grouping similar categories.
            </AlertDescription>
          </Alert>
        )}

        {/* Actions */}
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={() => setExpandedNodes(new Set(tree.map(node => node.id)))}
          >
            Expand All
          </Button>
          <Button
            variant="outline"
            onClick={() => setExpandedNodes(new Set())}
          >
            Collapse All
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};
