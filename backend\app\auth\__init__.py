"""
Authentication utilities for the ClassyWeb application.
"""
import logging
import secrets
import uuid
from datetime import datetime, timedelta, timezone
from typing import Dict, Any, Optional, Union, Tuple

from fastapi import Depends, HTTPException, status, Request
from fastapi.security import OAuth2<PERSON><PERSON><PERSON><PERSON>earer
from jose import JWTError, jwt
from passlib.context import CryptContext
from sqlalchemy.orm import Session

from .. import config # Adjusted import path
from ..database import get_db, get_user_by_email, User # Adjusted import path
from ..redis_client import blacklist_token, is_token_blacklisted, store_refresh_token, validate_refresh_token, invalidate_refresh_token

# Set up logging
logger = logging.getLogger(__name__)

# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# OAuth2 token URL - Note: The tokenUrl might need adjustment depending on the final router prefix
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="auth/token") # Keep as relative for now

# Token generation
def create_access_token(data: Dict[str, Any], expires_delta: Optional[timedelta] = None,
                     user_id: Optional[int] = None, metadata: Optional[Dict[str, Any]] = None) -> str:
    """
    Create a JWT access token with enhanced security features.

    Args:
        data: Data to encode in the token (must include 'sub' claim)
        expires_delta: Optional expiration time delta
        user_id: Optional user ID to include in the token
        metadata: Optional metadata to include in the token

    Returns:
        JWT token string
    """
    to_encode = data.copy()

    # Set token expiration
    if expires_delta:
        expire = datetime.now(timezone.utc) + expires_delta
    else:
        expire = datetime.now(timezone.utc) + timedelta(minutes=config.ACCESS_TOKEN_EXPIRE_MINUTES)

    # Current time for issued at claim
    now = datetime.now(timezone.utc)

    # Add standard JWT claims
    to_encode.update({
        "exp": expire,                      # Expiration time
        "iat": now,                         # Issued at time
        "nbf": now,                         # Not valid before time
        "jti": str(uuid.uuid4()),           # JWT ID (unique identifier)
    })

    # Add optional user ID if provided
    if user_id:
        to_encode["uid"] = user_id

    # Add optional metadata if provided
    if metadata:
        # Only include safe metadata (don't include sensitive information)
        safe_metadata = {
            k: v for k, v in metadata.items()
            if k in ["ip", "user_agent", "device", "source"]
        }
        if safe_metadata:
            to_encode["meta"] = safe_metadata

    # Encode the token
    encoded_jwt = jwt.encode(to_encode, config.JWT_SECRET_KEY, algorithm=config.JWT_ALGORITHM)
    return encoded_jwt

def create_refresh_token(user_id: int, metadata: dict = None) -> str:
    """
    Create a refresh token.

    Args:
        user_id: User ID
        metadata: Optional metadata about the token (user agent, IP, etc.)

    Returns:
        Refresh token string
    """
    # Generate a random token
    token = secrets.token_urlsafe(64)

    # Store in Redis with expiration
    expires_delta = timedelta(days=config.REFRESH_TOKEN_EXPIRE_DAYS).total_seconds()
    store_refresh_token(user_id, token, int(expires_delta), metadata)

    return token

# Password utilities
def verify_password(plain_password: str, hashed_password: str) -> bool:
    """
    Verify a password against a hash.

    Args:
        plain_password: Plain text password
        hashed_password: Hashed password

    Returns:
        True if the password matches the hash, False otherwise
    """
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password: str) -> str:
    """
    Hash a password.

    Args:
        password: Plain text password

    Returns:
        Hashed password
    """
    return pwd_context.hash(password)

# Token generation for email verification and password reset
def generate_verification_token() -> str:
    """
    Generate a random token for email verification.

    Returns:
        Random token string
    """
    return secrets.token_urlsafe(32)

# User authentication
def authenticate_user(db: Session, email: str, password: str) -> Tuple[Optional[User], Optional[str]]:
    """
    Authenticate a user with email and password.

    Args:
        db: Database session
        email: User email
        password: User password

    Returns:
        Tuple containing (User object if authentication is successful, error message if any)
        If authentication fails, the first element will be None and the second will contain the error message
        If authentication succeeds, the second element will be None
    """
    user = get_user_by_email(db, email)
    if not user:
        return None, "user_not_found"
    if not user.hashed_password:
        return None, "oauth_only_user"  # OAuth-only user
    if not verify_password(password, user.hashed_password):
        return None, "invalid_password"
    return user, None

# Current user dependency
async def get_current_user(token: str = Depends(oauth2_scheme), db: Session = Depends(get_db)) -> User:
    """
    Get the current user from the JWT token.

    Args:
        token: JWT token
        db: Database session

    Returns:
        User object

    Raises:
        HTTPException: If the token is invalid or the user is not found
    """
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )

    # Check if token is blacklisted
    if is_token_blacklisted(token):
        logger.warning("Attempt to use blacklisted token")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Token has been revoked",
            headers={"WWW-Authenticate": "Bearer"},
        )

    try:
        # Add leeway to account for clock skew between servers
        options = {
            "verify_signature": True,
            "verify_exp": True,
            "verify_nbf": True,
            "verify_iat": True,
            "verify_aud": False,  # We don't use audience claims yet
            "require_exp": True,
            "leeway": config.JWT_TOKEN_LEEWAY_SECONDS
        }

        # Decode and validate the token
        payload = jwt.decode(
            token,
            config.JWT_SECRET_KEY,
            algorithms=[config.JWT_ALGORITHM],
            options=options
        )

        # Extract and validate required claims
        email = payload.get("sub")
        if not email:
            logger.warning("Token missing 'sub' claim")
            raise credentials_exception

        # Check for JWT ID (used for blacklisting)
        if "jti" not in payload:
            logger.warning("Token missing 'jti' claim")
            raise credentials_exception

    except JWTError as e:
        logger.warning(f"JWT validation error: {str(e)}")
        raise credentials_exception

    # Get user from database
    user = get_user_by_email(db, email)
    if user is None:
        logger.warning(f"User with email {email} not found in database")
        raise credentials_exception

    # Check if user is active
    if not user.is_active:
        logger.warning(f"Inactive user {email} attempted to authenticate")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Account is inactive",
            headers={"WWW-Authenticate": "Bearer"},
        )

    return user

# Current active user dependency
async def get_current_active_user(current_user: User = Depends(get_current_user)) -> User:
    """
    Get the current active user.

    Args:
        current_user: Current user

    Returns:
        User object

    Raises:
        HTTPException: If the user is inactive
    """
    if not current_user.is_active:
        raise HTTPException(status_code=400, detail="Inactive user")
    return current_user

# Optional token extraction (without OAuth2PasswordBearer)
async def get_optional_token(request: Request) -> Optional[str]:
    """
    Extract token from Authorization header without raising errors if missing.
    """
    authorization = request.headers.get("Authorization")
    if not authorization:
        return None

    try:
        scheme, token = authorization.split()
        if scheme.lower() != "bearer":
            return None
        return token
    except ValueError:
        return None

# Optional current user dependency
async def get_optional_current_user(
    token: Optional[str] = Depends(get_optional_token), db: Session = Depends(get_db)
) -> Optional[User]:
    """
    Get the current user from the JWT token, or None if no token is provided.

    Args:
        token: JWT token (optional)
        db: Database session

    Returns:
        User object or None
    """
    if token is None:
        return None

    # Check if token is blacklisted
    if is_token_blacklisted(token):
        logger.warning("Attempt to use blacklisted token in optional auth")
        return None

    try:
        # Add leeway to account for clock skew between servers
        options = {
            "verify_signature": True,
            "verify_exp": True,
            "verify_nbf": True,
            "verify_iat": True,
            "verify_aud": False,  # We don't use audience claims yet
            "require_exp": True,
            "leeway": config.JWT_TOKEN_LEEWAY_SECONDS
        }

        # Decode and validate the token
        payload = jwt.decode(
            token,
            config.JWT_SECRET_KEY,
            algorithms=[config.JWT_ALGORITHM],
            options=options
        )

        # Extract and validate required claims
        email = payload.get("sub")
        if not email:
            logger.debug("Token missing 'sub' claim in optional auth")
            return None

        # Check for JWT ID (used for blacklisting)
        if "jti" not in payload:
            logger.debug("Token missing 'jti' claim in optional auth")
            return None

    except JWTError as e:
        logger.debug(f"JWT validation error in optional auth: {str(e)}")
        return None

    # Get user from database
    user = get_user_by_email(db, email)

    # Don't check if user is active here - that's the responsibility of the endpoint
    return user


# Token-based user authentication (for WebSocket and direct token usage)
async def get_current_user_from_token(token: str, db: Session = None) -> User:
    """
    Get the current user from a JWT token string (for WebSocket authentication).

    Args:
        token: JWT token string
        db: Database session (optional, will create one if not provided)

    Returns:
        User object

    Raises:
        HTTPException: If the token is invalid or the user is not found
    """
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )

    # Get database session if not provided
    if db is None:
        from ..database import get_db
        db = next(get_db())

    # Check if token is blacklisted
    if is_token_blacklisted(token):
        logger.warning("Attempt to use blacklisted token")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Token has been revoked",
            headers={"WWW-Authenticate": "Bearer"},
        )

    try:
        # Add leeway to account for clock skew between servers
        options = {
            "verify_signature": True,
            "verify_exp": True,
            "verify_nbf": True,
            "verify_iat": True,
            "verify_aud": False,  # We don't use audience claims yet
            "require_exp": True,
            "leeway": config.JWT_TOKEN_LEEWAY_SECONDS
        }

        # Decode and validate the token
        payload = jwt.decode(
            token,
            config.JWT_SECRET_KEY,
            algorithms=[config.JWT_ALGORITHM],
            options=options
        )

        # Extract and validate required claims
        email = payload.get("sub")
        if not email:
            logger.warning("Token missing 'sub' claim")
            raise credentials_exception

        # Check for JWT ID (used for blacklisting)
        if "jti" not in payload:
            logger.warning("Token missing 'jti' claim")
            raise credentials_exception

    except JWTError as e:
        logger.warning(f"JWT validation error: {str(e)}")
        raise credentials_exception

    # Get user from database
    user = get_user_by_email(db, email)
    if user is None:
        logger.warning(f"User with email {email} not found in database")
        raise credentials_exception

    # Check if user is active
    if not user.is_active:
        logger.warning(f"Inactive user {email} attempted to authenticate")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Account is inactive",
            headers={"WWW-Authenticate": "Bearer"},
        )

    return user


# Constants for WebSocket authentication
SECRET_KEY = config.JWT_SECRET_KEY
ALGORITHM = config.JWT_ALGORITHM
