import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import './index.css'
import App from './App.tsx'

// Create a root element
const rootElement = document.getElementById('root');

// Check if root element exists
if (!rootElement) {
  console.error('Root element not found!');
} else {
  // Create React root and render the app
  const root = createRoot(rootElement);

  root.render(
    <StrictMode>
      <App />
    </StrictMode>
  );
}
