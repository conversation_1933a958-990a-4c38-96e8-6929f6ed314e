"""Enhanced WebSocket Manager for Real-time Updates in ClassyWeb ML Platform Phase 3.

This module provides comprehensive WebSocket functionality for real-time communication:
- Training progress monitoring with enhanced metrics
- Hyperparameter optimization progress tracking
- Model comparison updates
- Advanced performance monitoring
- Multi-client subscription management
"""

import logging
import json
import asyncio
from typing import Dict, Set, Any, Optional, List, Callable
from fastapi import WebSocket, WebSocketDisconnect
from sqlalchemy.orm import Session
import time
from enum import Enum
from dataclasses import dataclass
import uuid

from .database import get_db
from .database import TrainingSession, TrainingStatusEnum
from .metrics_system_v2 import metrics_system

logger = logging.getLogger(__name__)


class MessageType(Enum):
    """Enhanced message types for Phase 3 WebSocket communication."""
    # Training messages
    TRAINING_PROGRESS = "training_progress"
    TRAINING_COMPLETE = "training_complete"
    TRAINING_ERROR = "training_error"
    TRAINING_METRICS_UPDATE = "training_metrics_update"

    # Hyperparameter optimization messages
    OPTIMIZATION_PROGRESS = "optimization_progress"
    OPTIMIZATION_COMPLETE = "optimization_complete"
    OPTIMIZATION_ERROR = "optimization_error"
    OPTIMIZATION_TRIAL_COMPLETE = "optimization_trial_complete"

    # Model comparison messages
    MODEL_COMPARISON_START = "model_comparison_start"
    MODEL_COMPARISON_UPDATE = "model_comparison_update"
    MODEL_COMPARISON_COMPLETE = "model_comparison_complete"
    MODEL_COMPARISON_ERROR = "model_comparison_error"

    # Enhanced metrics messages
    METRICS_UPDATE = "metrics_update"
    PERFORMANCE_ALERT = "performance_alert"
    SYSTEM_STATUS = "system_status"

    # Client management
    CLIENT_CONNECT = "client_connect"
    CLIENT_DISCONNECT = "client_disconnect"
    SUBSCRIPTION_UPDATE = "subscription_update"

    # General messages
    ERROR = "error"
    HEARTBEAT = "heartbeat"
    NOTIFICATION = "notification"


@dataclass
class EnhancedWebSocketMessage:
    """Enhanced message structure for Phase 3 WebSocket communication."""
    type: MessageType
    data: Dict[str, Any]
    timestamp: float
    session_id: str
    user_id: Optional[int] = None
    message_id: Optional[str] = None
    priority: str = "normal"  # low, normal, high, critical

    def __post_init__(self):
        if self.message_id is None:
            self.message_id = str(uuid.uuid4())

    def to_dict(self) -> Dict[str, Any]:
        """Convert message to dictionary."""
        return {
            'type': self.type.value,
            'data': self.data,
            'timestamp': self.timestamp,
            'session_id': self.session_id,
            'user_id': self.user_id,
            'message_id': self.message_id,
            'priority': self.priority
        }

    def to_json(self) -> str:
        """Convert message to JSON string."""
        return json.dumps(self.to_dict())


class EnhancedConnectionManager:
    """Enhanced WebSocket connection manager for Phase 3 real-time updates."""

    def __init__(self):
        # Store active connections by session_id
        self.active_connections: Dict[str, Set[WebSocket]] = {}
        # Store user connections for broadcasting
        self.user_connections: Dict[int, Set[WebSocket]] = {}
        # Store connection metadata
        self.connection_metadata: Dict[WebSocket, Dict[str, Any]] = {}

        # Enhanced Phase 3 features
        # Topic-based subscriptions for targeted updates
        self.topic_subscriptions: Dict[str, Set[WebSocket]] = {}
        # Message handlers for different message types
        self.message_handlers: Dict[MessageType, List[Callable]] = {}
        # Message queue for offline clients
        self.message_queue: Dict[int, List[EnhancedWebSocketMessage]] = {}
        # Connection statistics
        self.connection_stats: Dict[str, Any] = {
            'total_connections': 0,
            'active_connections': 0,
            'messages_sent': 0,
            'messages_received': 0,
            'start_time': time.time()
        }

    def add_message_handler(self, message_type: MessageType, handler: Callable):
        """Add a message handler for a specific message type."""
        if message_type not in self.message_handlers:
            self.message_handlers[message_type] = []
        self.message_handlers[message_type].append(handler)
        logger.info(f"Added message handler for {message_type.value}")

    async def connect(self, websocket: WebSocket, user_id: int, session_id: Optional[str] = None):
        """Accept a new WebSocket connection with enhanced features."""
        await websocket.accept()

        # Update connection statistics
        self.connection_stats['total_connections'] += 1
        self.connection_stats['active_connections'] += 1

        # Store connection metadata
        self.connection_metadata[websocket] = {
            'user_id': user_id,
            'session_id': session_id,
            'connected_at': time.time(),
            'last_heartbeat': time.time(),
            'subscriptions': set(),
            'message_count': 0
        }

        # Add to user connections
        if user_id not in self.user_connections:
            self.user_connections[user_id] = set()
        self.user_connections[user_id].add(websocket)

        # Add to session connections if session_id provided
        if session_id:
            if session_id not in self.active_connections:
                self.active_connections[session_id] = set()
            self.active_connections[session_id].add(websocket)
        
        logger.info(f"WebSocket connected for user {user_id}, session {session_id}")
        
        # Send initial connection confirmation
        await self.send_personal_message({
            'type': 'connection_established',
            'user_id': user_id,
            'session_id': session_id,
            'timestamp': time.time()
        }, websocket)
    
    def disconnect(self, websocket: WebSocket):
        """Remove a WebSocket connection."""
        if websocket not in self.connection_metadata:
            return
        
        metadata = self.connection_metadata[websocket]
        user_id = metadata['user_id']
        session_id = metadata.get('session_id')
        
        # Remove from user connections
        if user_id in self.user_connections:
            self.user_connections[user_id].discard(websocket)
            if not self.user_connections[user_id]:
                del self.user_connections[user_id]
        
        # Remove from session connections
        if session_id and session_id in self.active_connections:
            self.active_connections[session_id].discard(websocket)
            if not self.active_connections[session_id]:
                del self.active_connections[session_id]
        
        # Remove metadata
        del self.connection_metadata[websocket]
        
        logger.info(f"WebSocket disconnected for user {user_id}, session {session_id}")
    
    async def send_personal_message(self, message: Dict[str, Any], websocket: WebSocket):
        """Send a message to a specific WebSocket connection."""
        try:
            await websocket.send_text(json.dumps(message))
        except Exception as e:
            logger.error(f"Failed to send message to WebSocket: {e}")
            self.disconnect(websocket)
    
    async def broadcast_to_session(self, session_id: str, message: Dict[str, Any]):
        """Broadcast a message to all connections monitoring a specific session."""
        if session_id not in self.active_connections:
            return
        
        message['session_id'] = session_id
        message['timestamp'] = time.time()
        
        # Send to all connections for this session
        disconnected = []
        for websocket in self.active_connections[session_id].copy():
            try:
                await websocket.send_text(json.dumps(message))
            except Exception as e:
                logger.error(f"Failed to broadcast to session {session_id}: {e}")
                disconnected.append(websocket)
        
        # Clean up disconnected websockets
        for websocket in disconnected:
            self.disconnect(websocket)
    
    async def broadcast_to_user(self, user_id: int, message: Dict[str, Any]):
        """Broadcast a message to all connections for a specific user."""
        if user_id not in self.user_connections:
            return
        
        message['user_id'] = user_id
        message['timestamp'] = time.time()
        
        # Send to all connections for this user
        disconnected = []
        for websocket in self.user_connections[user_id].copy():
            try:
                await websocket.send_text(json.dumps(message))
            except Exception as e:
                logger.error(f"Failed to broadcast to user {user_id}: {e}")
                disconnected.append(websocket)
        
        # Clean up disconnected websockets
        for websocket in disconnected:
            self.disconnect(websocket)
    
    def get_connection_stats(self) -> Dict[str, Any]:
        """Get statistics about active connections."""
        return {
            'total_connections': sum(len(connections) for connections in self.user_connections.values()),
            'active_sessions': len(self.active_connections),
            'connected_users': len(self.user_connections),
            'session_connections': {
                session_id: len(connections) 
                for session_id, connections in self.active_connections.items()
            }
        }


class TrainingMonitor:
    """Monitors training sessions and broadcasts updates via WebSocket."""

    def __init__(self, connection_manager: "ConnectionManager"):
        self.connection_manager = connection_manager
        self.monitoring_tasks: Dict[str, asyncio.Task] = {}
        
    async def start_monitoring(self, session_id: str, user_id: int):
        """Start monitoring a training session."""
        if session_id in self.monitoring_tasks:
            logger.warning(f"Already monitoring session {session_id}")
            return
        
        # Create monitoring task
        task = asyncio.create_task(self._monitor_session(session_id, user_id))
        self.monitoring_tasks[session_id] = task
        
        logger.info(f"Started monitoring session {session_id}")
    
    def stop_monitoring(self, session_id: str):
        """Stop monitoring a training session."""
        if session_id in self.monitoring_tasks:
            self.monitoring_tasks[session_id].cancel()
            del self.monitoring_tasks[session_id]
            logger.info(f"Stopped monitoring session {session_id}")
    
    async def _monitor_session(self, session_id: str, user_id: int):
        """Monitor a training session and broadcast updates."""
        try:
            while True:
                # Get current session status from database
                with Session(bind=get_db().bind) as db:
                    session = db.query(TrainingSession).filter(
                        TrainingSession.id == session_id
                    ).first()
                    
                    if not session:
                        logger.warning(f"Session {session_id} not found, stopping monitoring")
                        break
                    
                    # Prepare status update
                    status_update = {
                        'type': 'training_progress',
                        'session_id': session_id,
                        'status': session.status.value,
                        'progress_percentage': session.progress_percentage,
                        'current_stage': session.current_stage,
                        'progress_data': session.progress_data
                    }
                    
                    # Get system metrics if available
                    system_metrics = metrics_system.get_session_metrics(session_id)
                    if system_metrics.get('active'):
                        status_update['system_metrics'] = system_metrics
                    
                    # Broadcast update
                    await self.connection_manager.broadcast_to_session(session_id, status_update)
                    
                    # Check if training is complete
                    if session.status in [TrainingStatusEnum.COMPLETED, TrainingStatusEnum.FAILED, TrainingStatusEnum.CANCELLED]:
                        # Send final update
                        final_update = {
                            'type': 'training_complete',
                            'session_id': session_id,
                            'status': session.status.value,
                            'final_metrics': session.final_metrics,
                            'error_message': session.error_message
                        }
                        await self.connection_manager.broadcast_to_session(session_id, final_update)
                        break
                
                # Wait before next update
                await asyncio.sleep(2)  # Update every 2 seconds
                
        except asyncio.CancelledError:
            logger.info(f"Monitoring cancelled for session {session_id}")
        except Exception as e:
            logger.error(f"Error monitoring session {session_id}: {e}")
        finally:
            # Clean up
            if session_id in self.monitoring_tasks:
                del self.monitoring_tasks[session_id]
    
    async def broadcast_training_event(self, session_id: str, event_type: str, data: Dict[str, Any]):
        """Broadcast a specific training event."""
        message = {
            'type': f'training_{event_type}',
            'session_id': session_id,
            'data': data
        }
        await self.connection_manager.broadcast_to_session(session_id, message)
    
    def get_monitoring_stats(self) -> Dict[str, Any]:
        """Get statistics about active monitoring."""
        return {
            'active_sessions': len(self.monitoring_tasks),
            'session_ids': list(self.monitoring_tasks.keys())
        }


class SystemMonitor:
    """Monitors system-wide metrics and broadcasts updates."""

    def __init__(self, connection_manager: "ConnectionManager"):
        self.connection_manager = connection_manager
        self.monitoring_active = False
        self.monitoring_task: Optional[asyncio.Task] = None
    
    async def start_system_monitoring(self):
        """Start system-wide monitoring."""
        if self.monitoring_active:
            return
        
        self.monitoring_active = True
        self.monitoring_task = asyncio.create_task(self._monitor_system())
        logger.info("Started system monitoring")
    
    def stop_system_monitoring(self):
        """Stop system-wide monitoring."""
        self.monitoring_active = False
        if self.monitoring_task:
            self.monitoring_task.cancel()
            self.monitoring_task = None
        logger.info("Stopped system monitoring")
    
    async def _monitor_system(self):
        """Monitor system metrics and broadcast updates."""
        try:
            while self.monitoring_active:
                # Get current system metrics
                system_metrics = metrics_system.system_monitor.get_current_metrics()
                
                # Broadcast to all connected users
                system_update = {
                    'type': 'system_metrics',
                    'metrics': {
                        'cpu_percent': system_metrics.cpu_percent,
                        'memory_percent': system_metrics.memory_percent,
                        'memory_used_gb': system_metrics.memory_used_gb,
                        'gpu_metrics': system_metrics.gpu_metrics
                    }
                }
                
                # Broadcast to all users (could be optimized to only send to interested users)
                for user_id in self.connection_manager.user_connections:
                    await self.connection_manager.broadcast_to_user(user_id, system_update)
                
                await asyncio.sleep(5)  # Update every 5 seconds
                
        except asyncio.CancelledError:
            logger.info("System monitoring cancelled")
        except Exception as e:
            logger.error(f"Error in system monitoring: {e}")


# Enhanced Phase 3 methods for the ConnectionManager
class ConnectionManager(EnhancedConnectionManager):
    """Backward compatible ConnectionManager with Phase 3 enhancements."""

    async def send_optimization_progress(
        self,
        optimization_id: str,
        strategy: str,
        progress: float,
        current_trial: int,
        total_trials: int,
        best_score: float = None,
        current_params: Dict[str, Any] = None
    ):
        """Send hyperparameter optimization progress update."""
        message = EnhancedWebSocketMessage(
            type=MessageType.OPTIMIZATION_PROGRESS,
            data={
                'optimization_id': optimization_id,
                'strategy': strategy,
                'progress': progress,
                'current_trial': current_trial,
                'total_trials': total_trials,
                'best_score': best_score,
                'current_params': current_params or {}
            },
            timestamp=time.time(),
            session_id=optimization_id,
            priority='normal'
        )

        await self.broadcast_to_topic(f'optimization:{optimization_id}', message)
        await self.broadcast_to_topic('optimization:all', message)

    async def send_model_comparison_update(
        self,
        comparison_id: str,
        models_compared: List[str],
        current_results: Dict[str, Any],
        progress: float = None
    ):
        """Send model comparison progress update."""
        message = EnhancedWebSocketMessage(
            type=MessageType.MODEL_COMPARISON_UPDATE,
            data={
                'comparison_id': comparison_id,
                'models_compared': models_compared,
                'current_results': current_results,
                'progress': progress
            },
            timestamp=time.time(),
            session_id=comparison_id,
            priority='normal'
        )

        await self.broadcast_to_topic(f'comparison:{comparison_id}', message)
        await self.broadcast_to_topic('comparison:all', message)

    async def send_enhanced_training_progress(
        self,
        session_id: str,
        progress: float,
        current_epoch: int,
        total_epochs: int,
        current_loss: float = None,
        metrics: Dict[str, float] = None,
        threshold_analysis: Dict[str, Any] = None,
        correlation_analysis: Dict[str, Any] = None
    ):
        """Send enhanced training progress with Phase 3 features."""
        message = EnhancedWebSocketMessage(
            type=MessageType.TRAINING_PROGRESS,
            data={
                'session_id': session_id,
                'progress': progress,
                'current_epoch': current_epoch,
                'total_epochs': total_epochs,
                'current_loss': current_loss,
                'metrics': metrics or {},
                'threshold_analysis': threshold_analysis,
                'correlation_analysis': correlation_analysis,
                'enhanced_features': True
            },
            timestamp=time.time(),
            session_id=session_id,
            priority='normal'
        )

        await self.broadcast_to_session(session_id, message.to_dict())
        await self.broadcast_to_topic('training:all', message)

    async def broadcast_to_topic(self, topic: str, message: EnhancedWebSocketMessage):
        """Broadcast message to all subscribers of a topic."""
        if topic not in self.topic_subscriptions:
            logger.debug(f"No subscribers for topic {topic}")
            return

        disconnected_websockets = []

        for websocket in self.topic_subscriptions[topic]:
            try:
                await websocket.send_text(message.to_json())
                self.connection_stats['messages_sent'] += 1

                # Update connection metadata
                if websocket in self.connection_metadata:
                    self.connection_metadata[websocket]['message_count'] += 1

            except Exception as e:
                logger.error(f"Failed to send message to websocket: {e}")
                disconnected_websockets.append(websocket)

        # Clean up disconnected websockets
        for websocket in disconnected_websockets:
            await self.disconnect(websocket)

    async def subscribe_to_topic(self, websocket: WebSocket, topic: str):
        """Subscribe a websocket to a topic."""
        if topic not in self.topic_subscriptions:
            self.topic_subscriptions[topic] = set()

        self.topic_subscriptions[topic].add(websocket)

        # Update connection metadata
        if websocket in self.connection_metadata:
            self.connection_metadata[websocket]['subscriptions'].add(topic)

        logger.info(f"WebSocket subscribed to topic: {topic}")

    async def unsubscribe_from_topic(self, websocket: WebSocket, topic: str):
        """Unsubscribe a websocket from a topic."""
        if topic in self.topic_subscriptions:
            self.topic_subscriptions[topic].discard(websocket)

            # Remove empty topics
            if not self.topic_subscriptions[topic]:
                del self.topic_subscriptions[topic]

        # Update connection metadata
        if websocket in self.connection_metadata:
            self.connection_metadata[websocket]['subscriptions'].discard(topic)

        logger.info(f"WebSocket unsubscribed from topic: {topic}")

    def get_enhanced_stats(self) -> Dict[str, Any]:
        """Get enhanced connection statistics."""
        current_time = time.time()
        uptime = current_time - self.connection_stats['start_time']

        return {
            **self.connection_stats,
            'uptime_seconds': uptime,
            'active_topics': len(self.topic_subscriptions),
            'total_subscriptions': sum(len(subs) for subs in self.topic_subscriptions.values()),
            'average_messages_per_connection': (
                self.connection_stats['messages_sent'] / max(self.connection_stats['total_connections'], 1)
            ),
            'messages_per_second': (
                self.connection_stats['messages_sent'] / max(uptime, 1)
            )
        }


# Global instances
connection_manager = ConnectionManager()
training_monitor = TrainingMonitor(connection_manager)
system_monitor = SystemMonitor(connection_manager)


# Convenience functions
async def start_session_monitoring(session_id: str, user_id: int):
    """Start monitoring a training session."""
    await training_monitor.start_monitoring(session_id, user_id)


def stop_session_monitoring(session_id: str):
    """Stop monitoring a training session."""
    training_monitor.stop_monitoring(session_id)


async def broadcast_training_event(session_id: str, event_type: str, data: Dict[str, Any]):
    """Broadcast a training event to all session subscribers."""
    await training_monitor.broadcast_training_event(session_id, event_type, data)


def get_websocket_stats() -> Dict[str, Any]:
    """Get comprehensive WebSocket statistics."""
    return {
        'connections': connection_manager.get_connection_stats(),
        'training_monitoring': training_monitor.get_monitoring_stats(),
        'system_monitoring': {'active': system_monitor.monitoring_active}
    }
