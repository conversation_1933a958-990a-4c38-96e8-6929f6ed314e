/**
 * HierarchyVisualization.tsx
 * 
 * Interactive tree visualization for hierarchical classification results
 */

import React, { useState, useMemo } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { ScrollArea } from "@/components/ui/scroll-area";
import { 
  TreePine, 
  Search, 
  Filter, 
  ZoomIn, 
  ZoomOut,
  ChevronRight,
  ChevronDown,
  Target,
  AlertCircle,
  CheckCircle2,
  Eye,
  EyeOff
} from "lucide-react";

interface HierarchyNode {
  id: string;
  name: string;
  level: number;
  parentId?: string;
  children: HierarchyNode[];
  
  // Classification results
  predicted_count: number;
  actual_count: number;
  correct_predictions: number;
  accuracy: number;
  confidence: number;
  
  // Visualization properties
  isExpanded?: boolean;
  isHighlighted?: boolean;
  isFiltered?: boolean;
}

interface HierarchyVisualizationProps {
  results: HierarchyNode[];
  hierarchyLevels: string[];
  onNodeSelect?: (node: HierarchyNode) => void;
  onNodeHover?: (node: HierarchyNode | null) => void;
  className?: string;
}

export const HierarchyVisualization: React.FC<HierarchyVisualizationProps> = ({
  results,
  hierarchyLevels,
  onNodeSelect,
  onNodeHover,
  className
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedLevel, setSelectedLevel] = useState<number | null>(null);
  const [minAccuracy, setMinAccuracy] = useState(0);
  const [expandedNodes, setExpandedNodes] = useState<Set<string>>(new Set());
  const [selectedNode, setSelectedNode] = useState<HierarchyNode | null>(null);
  const [showOnlyErrors, setShowOnlyErrors] = useState(false);

  // Filter and process nodes
  const processedResults = useMemo(() => {
    const filterNode = (node: HierarchyNode): HierarchyNode | null => {
      // Apply filters
      const matchesSearch = !searchTerm || 
        node.name.toLowerCase().includes(searchTerm.toLowerCase());
      
      const matchesLevel = selectedLevel === null || node.level === selectedLevel;
      
      const matchesAccuracy = node.accuracy >= minAccuracy;
      
      const matchesErrorFilter = !showOnlyErrors || node.accuracy < 0.8;

      // Process children recursively
      const filteredChildren = node.children
        .map(child => filterNode(child))
        .filter(Boolean) as HierarchyNode[];

      // Include node if it matches filters or has matching children
      const shouldInclude = (matchesSearch && matchesLevel && matchesAccuracy && matchesErrorFilter) || 
                           filteredChildren.length > 0;

      if (!shouldInclude) return null;

      return {
        ...node,
        children: filteredChildren,
        isFiltered: !(matchesSearch && matchesLevel && matchesAccuracy && matchesErrorFilter)
      };
    };

    return results.map(node => filterNode(node)).filter(Boolean) as HierarchyNode[];
  }, [results, searchTerm, selectedLevel, minAccuracy, showOnlyErrors]);

  const toggleNodeExpansion = (nodeId: string) => {
    const newExpanded = new Set(expandedNodes);
    if (newExpanded.has(nodeId)) {
      newExpanded.delete(nodeId);
    } else {
      newExpanded.add(nodeId);
    }
    setExpandedNodes(newExpanded);
  };

  const handleNodeClick = (node: HierarchyNode) => {
    setSelectedNode(node);
    onNodeSelect?.(node);
    
    // Auto-expand if has children
    if (node.children.length > 0) {
      toggleNodeExpansion(node.id);
    }
  };

  const getNodeColor = (node: HierarchyNode): string => {
    if (node.accuracy >= 0.9) return 'bg-green-100 border-green-300 text-green-800';
    if (node.accuracy >= 0.7) return 'bg-yellow-100 border-yellow-300 text-yellow-800';
    return 'bg-red-100 border-red-300 text-red-800';
  };

  const getAccuracyIcon = (accuracy: number) => {
    if (accuracy >= 0.8) return <CheckCircle2 className="w-4 h-4 text-green-600" />;
    if (accuracy >= 0.6) return <Target className="w-4 h-4 text-yellow-600" />;
    return <AlertCircle className="w-4 h-4 text-red-600" />;
  };

  const renderNode = (node: HierarchyNode, depth: number = 0) => {
    const isExpanded = expandedNodes.has(node.id);
    const hasChildren = node.children.length > 0;
    const isSelected = selectedNode?.id === node.id;
    const levelInfo = hierarchyLevels[node.level];

    return (
      <div key={node.id} className="space-y-1">
        <div 
          className={`flex items-center gap-2 p-3 rounded-lg border cursor-pointer transition-all hover:shadow-md ${
            isSelected ? 'ring-2 ring-blue-500 bg-blue-50' : getNodeColor(node)
          } ${node.isFiltered ? 'opacity-60' : ''}`}
          style={{ marginLeft: depth * 20 }}
          onClick={() => handleNodeClick(node)}
          onMouseEnter={() => onNodeHover?.(node)}
          onMouseLeave={() => onNodeHover?.(null)}
        >
          {/* Expand/Collapse Icon */}
          <div className="w-6 flex justify-center">
            {hasChildren ? (
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  toggleNodeExpansion(node.id);
                }}
                className="hover:bg-white/50 rounded p-1"
              >
                {isExpanded ? (
                  <ChevronDown className="w-4 h-4" />
                ) : (
                  <ChevronRight className="w-4 h-4" />
                )}
              </button>
            ) : (
              <div className="w-4 h-4" />
            )}
          </div>

          {/* Node Content */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-1">
              <span className="font-medium truncate">{node.name}</span>
              {getAccuracyIcon(node.accuracy)}
              <Badge variant="outline" className="text-xs">
                {levelInfo}
              </Badge>
            </div>
            
            <div className="flex items-center gap-4 text-xs">
              <span>
                <strong>Accuracy:</strong> {(node.accuracy * 100).toFixed(1)}%
              </span>
              <span>
                <strong>Predicted:</strong> {node.predicted_count}
              </span>
              <span>
                <strong>Actual:</strong> {node.actual_count}
              </span>
              <span>
                <strong>Correct:</strong> {node.correct_predictions}
              </span>
              {node.confidence && (
                <span>
                  <strong>Confidence:</strong> {(node.confidence * 100).toFixed(1)}%
                </span>
              )}
            </div>
          </div>

          {/* Node Statistics */}
          <div className="text-right">
            <div className="text-sm font-bold">
              {node.correct_predictions}/{node.predicted_count}
            </div>
            <div className="text-xs text-muted-foreground">
              {hasChildren ? `${node.children.length} children` : 'leaf'}
            </div>
          </div>
        </div>

        {/* Children */}
        {isExpanded && hasChildren && (
          <div className="space-y-1">
            {node.children.map(child => renderNode(child, depth + 1))}
          </div>
        )}
      </div>
    );
  };

  const getOverallStats = () => {
    let totalNodes = 0;
    let totalCorrect = 0;
    let totalPredicted = 0;
    let errorNodes = 0;

    const countStats = (nodes: HierarchyNode[]) => {
      nodes.forEach(node => {
        totalNodes++;
        totalCorrect += node.correct_predictions;
        totalPredicted += node.predicted_count;
        if (node.accuracy < 0.8) errorNodes++;
        countStats(node.children);
      });
    };

    countStats(results);

    return {
      totalNodes,
      totalCorrect,
      totalPredicted,
      errorNodes,
      overallAccuracy: totalPredicted > 0 ? totalCorrect / totalPredicted : 0
    };
  };

  const stats = getOverallStats();

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TreePine className="w-5 h-5" />
            Hierarchy Classification Results
          </CardTitle>
        </CardHeader>
        <CardContent>
          {/* Overall Statistics */}
          <div className="grid grid-cols-2 md:grid-cols-5 gap-4 mb-6">
            <div className="text-center p-3 bg-muted/30 rounded">
              <div className="text-2xl font-bold">{stats.totalNodes}</div>
              <div className="text-sm text-muted-foreground">Total Nodes</div>
            </div>
            <div className="text-center p-3 bg-muted/30 rounded">
              <div className="text-2xl font-bold text-green-600">
                {(stats.overallAccuracy * 100).toFixed(1)}%
              </div>
              <div className="text-sm text-muted-foreground">Overall Accuracy</div>
            </div>
            <div className="text-center p-3 bg-muted/30 rounded">
              <div className="text-2xl font-bold">{stats.totalCorrect}</div>
              <div className="text-sm text-muted-foreground">Correct Predictions</div>
            </div>
            <div className="text-center p-3 bg-muted/30 rounded">
              <div className="text-2xl font-bold">{stats.totalPredicted}</div>
              <div className="text-sm text-muted-foreground">Total Predictions</div>
            </div>
            <div className="text-center p-3 bg-muted/30 rounded">
              <div className="text-2xl font-bold text-red-600">{stats.errorNodes}</div>
              <div className="text-sm text-muted-foreground">Error Nodes</div>
            </div>
          </div>

          {/* Filters */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
              <Input
                placeholder="Search nodes..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>

            <Select
              value={selectedLevel?.toString() || 'all'}
              onValueChange={(value) => setSelectedLevel(value === 'all' ? null : parseInt(value))}
            >
              <SelectTrigger>
                <SelectValue placeholder="Filter by level" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Levels</SelectItem>
                {hierarchyLevels.map((level, index) => (
                  <SelectItem key={index} value={index.toString()}>
                    {level} (Level {index + 1})
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <div className="space-y-1">
              <label className="text-sm font-medium">Min Accuracy</label>
              <Input
                type="range"
                min="0"
                max="1"
                step="0.1"
                value={minAccuracy}
                onChange={(e) => setMinAccuracy(parseFloat(e.target.value))}
                className="w-full"
              />
              <div className="text-xs text-muted-foreground text-center">
                {(minAccuracy * 100).toFixed(0)}%
              </div>
            </div>

            <div className="flex items-center gap-2">
              <Button
                variant={showOnlyErrors ? "default" : "outline"}
                size="sm"
                onClick={() => setShowOnlyErrors(!showOnlyErrors)}
                className="flex-1"
              >
                {showOnlyErrors ? <Eye className="w-4 h-4 mr-2" /> : <EyeOff className="w-4 h-4 mr-2" />}
                {showOnlyErrors ? 'Show All' : 'Errors Only'}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Tree Visualization */}
      <Card>
        <CardHeader>
          <CardTitle>Interactive Hierarchy Tree</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-2 mb-4">
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                const allNodeIds = new Set<string>();
                const collectIds = (nodes: HierarchyNode[]) => {
                  nodes.forEach(node => {
                    allNodeIds.add(node.id);
                    collectIds(node.children);
                  });
                };
                collectIds(processedResults);
                setExpandedNodes(allNodeIds);
              }}
            >
              <ZoomOut className="w-4 h-4 mr-2" />
              Expand All
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setExpandedNodes(new Set())}
            >
              <ZoomIn className="w-4 h-4 mr-2" />
              Collapse All
            </Button>
          </div>

          <ScrollArea className="h-96 border rounded p-4">
            {processedResults.length > 0 ? (
              <div className="space-y-2">
                {processedResults.map(node => renderNode(node))}
              </div>
            ) : (
              <div className="text-center text-muted-foreground py-8">
                No nodes match your filter criteria
              </div>
            )}
          </ScrollArea>
        </CardContent>
      </Card>

      {/* Selected Node Details */}
      {selectedNode && (
        <Card>
          <CardHeader>
            <CardTitle>Node Details: {selectedNode.name}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div>
                <div className="text-sm text-muted-foreground">Level</div>
                <div className="font-semibold">{hierarchyLevels[selectedNode.level]}</div>
              </div>
              <div>
                <div className="text-sm text-muted-foreground">Accuracy</div>
                <div className={`font-semibold ${
                  selectedNode.accuracy >= 0.8 ? 'text-green-600' : 
                  selectedNode.accuracy >= 0.6 ? 'text-yellow-600' : 'text-red-600'
                }`}>
                  {(selectedNode.accuracy * 100).toFixed(2)}%
                </div>
              </div>
              <div>
                <div className="text-sm text-muted-foreground">Predictions</div>
                <div className="font-semibold">
                  {selectedNode.correct_predictions} / {selectedNode.predicted_count}
                </div>
              </div>
              <div>
                <div className="text-sm text-muted-foreground">Children</div>
                <div className="font-semibold">{selectedNode.children.length}</div>
              </div>
            </div>
            
            {selectedNode.confidence && (
              <div className="mt-4">
                <div className="text-sm text-muted-foreground mb-1">Confidence Score</div>
                <div className="flex items-center gap-2">
                  <div className="flex-1 bg-muted rounded-full h-2">
                    <div 
                      className="bg-blue-600 h-2 rounded-full"
                      style={{ width: `${selectedNode.confidence * 100}%` }}
                    />
                  </div>
                  <span className="text-sm font-medium">
                    {(selectedNode.confidence * 100).toFixed(1)}%
                  </span>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
};
