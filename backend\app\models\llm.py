"""
LLM-related Pydantic models.
"""
from typing import List, Dict, Any, Optional
from pydantic import BaseModel, Field

from .. import config # Corrected relative import

class LLMProviderConfig(BaseModel):
    """Model for LLM configuration, matching the definition from the old models.py."""
    provider: str = Field(..., description="LLM Provider (e.g., 'Groq', 'Ollama')")
    endpoint: str = Field(..., description="API endpoint URL")
    model_name: str = Field(..., description="Specific model name to use")
    api_key: Optional[str] = Field(None, description="API key, if required by the provider")

class ClassifyLLMRequest(BaseModel):
    """Request model for LLM classification."""
    file_id: str = Field(..., description="ID of the uploaded file to classify")
    original_filename: str = Field(..., description="Original name of the uploaded file")
    text_columns: List[str] = Field(..., description="Names of the columns containing text data")
    hierarchy: Dict[str, Any] = Field(..., description="Nested hierarchy structure for classification")
    llm_config: LLMProviderConfig # Use the renamed config model
    hierarchy_config_id: Optional[int] = Field(None, description="ID of the hierarchy configuration to use")

class ProviderListResponse(BaseModel):
    """Response model for listing LLM providers."""
    providers: List[str]
    default_endpoints: Dict[str, str] = Field(default_factory=dict, description="Default endpoints for each provider")
    default_models: Dict[str, str] = Field(default_factory=dict, description="Default models for each provider")
    api_keys: Dict[str, str] = Field(default_factory=dict, description="API keys from environment variables for each provider")

class FetchModelsRequest(BaseModel):
    """Request model for fetching available models."""
    provider: str
    endpoint: str
    api_key: Optional[str] = None

class ModelListResponse(BaseModel):
    """Response model for listing available models."""
    models: List[str]

class HierarchySuggestRequest(BaseModel):
    """Request model for hierarchy suggestion."""
    sample_texts: List[str] = Field(..., min_length=1, max_length=config.MAX_LLM_SAMPLE_SIZE, description="List of sample texts for suggestion")
    llm_config: LLMProviderConfig # Use the renamed config model

class HierarchySuggestResponse(BaseModel):
    """Response model for hierarchy suggestion."""
    suggestion: Optional[Dict[str, Any]] = Field(None, description="Nested dictionary representing the suggested hierarchy")
    error: Optional[str] = Field(None, description="Error message if suggestion failed")
