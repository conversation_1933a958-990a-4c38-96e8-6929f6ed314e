"""Plugin Loader for ClassyWeb Universal Platform."""

import logging
import importlib
import inspect
import os
import sys
from pathlib import Path
from typing import Dict, List, Any, Optional, Type
import zipfile
import tempfile
import hashlib

from .base_plugin import BasePlugin, PluginType
from .plugin_registry import PluginRegistry

logger = logging.getLogger(__name__)


class PluginLoader:
    """Loads and manages plugin discovery and installation."""
    
    def __init__(self, registry: PluginRegistry, plugins_directory: str = "plugins"):
        self.registry = registry
        self.plugins_directory = Path(plugins_directory)
        self.plugins_directory.mkdir(exist_ok=True)
        
        # Security settings
        self.allowed_imports = {
            'numpy', 'pandas', 'sklearn', 'torch', 'transformers',
            'fastapi', 'pydantic', 'sqlalchemy', 'asyncio', 'typing',
            'datetime', 'json', 'logging', 'pathlib', 'dataclasses'
        }
        
        self.blocked_imports = {
            'os.system', 'subprocess', 'eval', 'exec', 'compile',
            'open', '__import__', 'globals', 'locals'
        }
    
    async def discover_plugins(self, directory: Optional[Path] = None) -> List[Dict[str, Any]]:
        """Discover plugins in the specified directory."""
        search_dir = directory or self.plugins_directory
        discovered_plugins = []
        
        logger.info(f"Discovering plugins in: {search_dir}")
        
        try:
            for plugin_dir in search_dir.iterdir():
                if plugin_dir.is_dir() and not plugin_dir.name.startswith('.'):
                    plugin_info = await self._analyze_plugin_directory(plugin_dir)
                    if plugin_info:
                        discovered_plugins.append(plugin_info)
            
            logger.info(f"Discovered {len(discovered_plugins)} plugins")
            return discovered_plugins
            
        except Exception as e:
            logger.error(f"Error discovering plugins: {str(e)}")
            return []
    
    async def load_plugin(
        self,
        plugin_path: Path,
        config: Dict[str, Any] = None,
        validate_security: bool = True
    ) -> Optional[BasePlugin]:
        """Load a plugin from a directory or file."""
        try:
            logger.info(f"Loading plugin from: {plugin_path}")
            
            # Security validation
            if validate_security and not await self._validate_plugin_security(plugin_path):
                logger.error(f"Security validation failed for plugin: {plugin_path}")
                return None
            
            # Load plugin module
            plugin_module = await self._load_plugin_module(plugin_path)
            if not plugin_module:
                return None
            
            # Find plugin class
            plugin_class = self._find_plugin_class(plugin_module)
            if not plugin_class:
                logger.error(f"No valid plugin class found in: {plugin_path}")
                return None
            
            # Create plugin instance
            plugin_instance = plugin_class(config)
            
            # Validate plugin
            if not self._validate_plugin_instance(plugin_instance):
                logger.error(f"Plugin instance validation failed: {plugin_path}")
                return None
            
            logger.info(f"Successfully loaded plugin: {plugin_instance.get_metadata().name}")
            return plugin_instance
            
        except Exception as e:
            logger.error(f"Error loading plugin from {plugin_path}: {str(e)}")
            return None
    
    async def install_plugin_package(
        self,
        package_path: Path,
        target_directory: Optional[Path] = None
    ) -> bool:
        """Install a plugin package (ZIP file)."""
        try:
            target_dir = target_directory or self.plugins_directory
            
            logger.info(f"Installing plugin package: {package_path}")
            
            # Validate package
            if not package_path.exists() or package_path.suffix.lower() != '.zip':
                logger.error(f"Invalid plugin package: {package_path}")
                return False
            
            # Extract package
            with tempfile.TemporaryDirectory() as temp_dir:
                temp_path = Path(temp_dir)
                
                with zipfile.ZipFile(package_path, 'r') as zip_ref:
                    zip_ref.extractall(temp_path)
                
                # Find plugin directory in extracted files
                plugin_dirs = [d for d in temp_path.iterdir() if d.is_dir()]
                if not plugin_dirs:
                    logger.error("No plugin directory found in package")
                    return False
                
                plugin_dir = plugin_dirs[0]  # Use first directory
                
                # Validate plugin structure
                if not await self._validate_plugin_structure(plugin_dir):
                    logger.error("Invalid plugin structure")
                    return False
                
                # Security scan
                if not await self._validate_plugin_security(plugin_dir):
                    logger.error("Plugin failed security validation")
                    return False
                
                # Copy to plugins directory
                import shutil
                final_plugin_dir = target_dir / plugin_dir.name
                if final_plugin_dir.exists():
                    shutil.rmtree(final_plugin_dir)
                
                shutil.copytree(plugin_dir, final_plugin_dir)
                
                # Load and register plugin
                plugin = await self.load_plugin(final_plugin_dir)
                if plugin:
                    success = self.registry.register_plugin(
                        plugin.get_metadata().name,
                        type(plugin),
                        plugin.config
                    )
                    
                    if success:
                        logger.info(f"Successfully installed plugin: {plugin.get_metadata().name}")
                        return True
                
            return False
            
        except Exception as e:
            logger.error(f"Error installing plugin package {package_path}: {str(e)}")
            return False
    
    async def uninstall_plugin(self, plugin_name: str) -> bool:
        """Uninstall a plugin and remove its files."""
        try:
            # Unregister from registry
            success = self.registry.unregister_plugin(plugin_name)
            if not success:
                return False
            
            # Remove plugin directory
            plugin_dir = self.plugins_directory / plugin_name
            if plugin_dir.exists():
                import shutil
                shutil.rmtree(plugin_dir)
                logger.info(f"Removed plugin directory: {plugin_dir}")
            
            # Remove from active plugins
            if plugin_name in self.active_plugins:
                del self.active_plugins[plugin_name]
            
            # Remove health monitoring
            if plugin_name in self.plugin_health:
                del self.plugin_health[plugin_name]
            
            logger.info(f"Successfully uninstalled plugin: {plugin_name}")
            return True
            
        except Exception as e:
            logger.error(f"Error uninstalling plugin {plugin_name}: {str(e)}")
            return False
    
    async def get_plugin_recommendations(
        self,
        task_type: str,
        data_characteristics: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """Get plugin recommendations based on task and data characteristics."""
        try:
            available_plugins = await self.registry.get_available_plugins()
            recommendations = []
            
            for plugin_info in available_plugins:
                score = self._calculate_plugin_score(plugin_info, task_type, data_characteristics)
                if score > 0.3:  # Minimum relevance threshold
                    recommendations.append({
                        **plugin_info,
                        "recommendation_score": score,
                        "recommendation_reason": self._generate_recommendation_reason(
                            plugin_info, task_type, data_characteristics, score
                        )
                    })
            
            # Sort by recommendation score
            recommendations.sort(key=lambda x: x["recommendation_score"], reverse=True)
            
            return recommendations[:5]  # Return top 5 recommendations
            
        except Exception as e:
            logger.error(f"Error getting plugin recommendations: {str(e)}")
            return []
    
    async def _analyze_plugin_directory(self, plugin_dir: Path) -> Optional[Dict[str, Any]]:
        """Analyze a plugin directory to extract information."""
        try:
            # Look for plugin manifest
            manifest_file = plugin_dir / "plugin.json"
            if manifest_file.exists():
                import json
                with open(manifest_file, 'r') as f:
                    manifest = json.load(f)
                return manifest
            
            # Look for Python plugin files
            python_files = list(plugin_dir.glob("*.py"))
            if python_files:
                # Try to extract metadata from the first Python file
                return {
                    "name": plugin_dir.name,
                    "type": "unknown",
                    "files": [f.name for f in python_files],
                    "has_manifest": False
                }
            
            return None
            
        except Exception as e:
            logger.error(f"Error analyzing plugin directory {plugin_dir}: {str(e)}")
            return None
    
    async def _load_plugin_module(self, plugin_path: Path):
        """Load a plugin module dynamically."""
        try:
            # Add plugin directory to Python path
            plugin_dir = plugin_path if plugin_path.is_dir() else plugin_path.parent
            if str(plugin_dir) not in sys.path:
                sys.path.insert(0, str(plugin_dir))
            
            # Find main plugin file
            if plugin_path.is_file() and plugin_path.suffix == '.py':
                module_name = plugin_path.stem
            else:
                # Look for main.py or __init__.py
                main_file = plugin_dir / "main.py"
                init_file = plugin_dir / "__init__.py"
                
                if main_file.exists():
                    module_name = "main"
                elif init_file.exists():
                    module_name = plugin_dir.name
                else:
                    logger.error(f"No main plugin file found in: {plugin_dir}")
                    return None
            
            # Import module
            module = importlib.import_module(module_name)
            return module
            
        except Exception as e:
            logger.error(f"Error loading plugin module: {str(e)}")
            return None
    
    def _find_plugin_class(self, module) -> Optional[Type[BasePlugin]]:
        """Find the plugin class in a module."""
        try:
            for name, obj in inspect.getmembers(module, inspect.isclass):
                if (issubclass(obj, BasePlugin) and 
                    obj != BasePlugin and 
                    not inspect.isabstract(obj)):
                    return obj
            return None
            
        except Exception as e:
            logger.error(f"Error finding plugin class: {str(e)}")
            return None
    
    def _validate_plugin_instance(self, plugin: BasePlugin) -> bool:
        """Validate a plugin instance."""
        try:
            # Check required methods
            required_methods = ['get_metadata', 'get_capabilities', 'initialize', 'cleanup']
            for method in required_methods:
                if not hasattr(plugin, method):
                    logger.error(f"Plugin missing required method: {method}")
                    return False
            
            # Validate metadata
            metadata = plugin.get_metadata()
            if not metadata.name or not metadata.version:
                logger.error("Plugin metadata incomplete")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Plugin instance validation error: {str(e)}")
            return False
    
    async def _validate_plugin_security(self, plugin_path: Path) -> bool:
        """Validate plugin security (basic checks)."""
        try:
            # Check for suspicious imports
            if plugin_path.is_file():
                files_to_check = [plugin_path]
            else:
                files_to_check = list(plugin_path.glob("**/*.py"))
            
            for file_path in files_to_check:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                    # Check for blocked imports
                    for blocked in self.blocked_imports:
                        if blocked in content:
                            logger.error(f"Blocked import found in {file_path}: {blocked}")
                            return False
            
            return True
            
        except Exception as e:
            logger.error(f"Security validation error: {str(e)}")
            return False
    
    async def _validate_plugin_structure(self, plugin_dir: Path) -> bool:
        """Validate plugin directory structure."""
        try:
            # Check for required files
            required_files = ["__init__.py"]  # At minimum, need an __init__.py
            
            for required_file in required_files:
                if not (plugin_dir / required_file).exists():
                    logger.error(f"Missing required file: {required_file}")
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"Plugin structure validation error: {str(e)}")
            return False
    
    def _calculate_plugin_score(
        self,
        plugin_info: Dict[str, Any],
        task_type: str,
        data_characteristics: Dict[str, Any]
    ) -> float:
        """Calculate recommendation score for a plugin."""
        score = 0.0
        
        try:
            capabilities = plugin_info.get("capabilities", {})
            metadata = plugin_info.get("metadata", {})
            
            # Task type matching
            if task_type == "hierarchical" and capabilities.get("supports_hierarchical", False):
                score += 0.4
            elif task_type == "flat" and capabilities.get("supports_flat", False):
                score += 0.4
            
            # Data size considerations
            data_size = data_characteristics.get("size", 0)
            max_batch_size = capabilities.get("max_batch_size", 1000)
            
            if data_size <= max_batch_size:
                score += 0.2
            elif capabilities.get("supports_streaming", False):
                score += 0.1
            
            # Performance considerations
            estimated_speed = capabilities.get("estimated_speed_ms", 1000)
            if estimated_speed < 500:
                score += 0.2
            elif estimated_speed < 2000:
                score += 0.1
            
            # Health and reliability
            health = plugin_info.get("health", {})
            if health.get("status") == "healthy":
                score += 0.1
            
            # Enterprise features
            if metadata.get("enterprise_only", False) and data_characteristics.get("enterprise", False):
                score += 0.1
            
        except Exception as e:
            logger.error(f"Error calculating plugin score: {str(e)}")
        
        return min(score, 1.0)  # Cap at 1.0
    
    def _generate_recommendation_reason(
        self,
        plugin_info: Dict[str, Any],
        task_type: str,
        data_characteristics: Dict[str, Any],
        score: float
    ) -> str:
        """Generate human-readable recommendation reason."""
        reasons = []
        
        capabilities = plugin_info.get("capabilities", {})
        metadata = plugin_info.get("metadata", {})
        
        # Task compatibility
        if task_type == "hierarchical" and capabilities.get("supports_hierarchical", False):
            reasons.append("Supports hierarchical classification")
        elif task_type == "flat" and capabilities.get("supports_flat", False):
            reasons.append("Supports flat multi-label classification")
        
        # Performance benefits
        estimated_speed = capabilities.get("estimated_speed_ms", 1000)
        if estimated_speed < 500:
            reasons.append("Fast processing speed")
        
        # Special capabilities
        if capabilities.get("supports_multi_modal", False):
            reasons.append("Multi-modal support")
        
        if capabilities.get("supports_streaming", False):
            reasons.append("Streaming support for large datasets")
        
        # Enterprise features
        if metadata.get("enterprise_only", False):
            reasons.append("Enterprise-grade features")
        
        if not reasons:
            reasons.append("General compatibility")
        
        return f"Recommended because: {', '.join(reasons)} (Score: {score:.2f})"
    
    async def _load_plugins_from_db(self):
        """Load plugins from database on startup."""
        try:
            plugins_data = self.registry.load_plugins_from_db()
            
            for plugin_data in plugins_data:
                plugin_name = plugin_data["name"]
                plugin_dir = self.plugins_directory / plugin_name
                
                if plugin_dir.exists():
                    plugin = await self.load_plugin(plugin_dir, plugin_data.get("config"))
                    if plugin:
                        self.registry.register_plugin(
                            plugin_name,
                            type(plugin),
                            plugin_data.get("config"),
                            auto_initialize=True
                        )
                        logger.info(f"Loaded plugin from database: {plugin_name}")
                    else:
                        logger.warning(f"Failed to load plugin from database: {plugin_name}")
                else:
                    logger.warning(f"Plugin directory not found for: {plugin_name}")
            
        except Exception as e:
            logger.error(f"Error loading plugins from database: {str(e)}")


# Global plugin loader instance
plugin_loader = PluginLoader(PluginRegistry())
