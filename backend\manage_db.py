"""
Database management script for ClassyWeb.

This script provides utilities for managing the database, including:
- Creating the database and tables
- Dropping the database and tables
- Migrating data from in-memory storage to the database

Usage:
    python manage_db.py create  # Create database and tables
    python manage_db.py drop    # Drop database and tables
    python manage_db.py reset   # Drop and recreate database and tables
"""
import os
import sys
import logging
import argparse
from pathlib import Path

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Add the current directory to the path so we can import our modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import our database modules
from app.database import Base, engine, init_db, SessionLocal

def create_db():
    """Create the database and tables."""
    logger.info("Creating database and tables...")
    init_db()
    logger.info("Database and tables created successfully.")

def drop_db():
    """Drop the database and tables."""
    logger.info("Dropping database tables...")
    Base.metadata.drop_all(bind=engine)
    logger.info("Database tables dropped successfully.")

def reset_db():
    """Drop and recreate the database and tables."""
    logger.info("Resetting database...")
    drop_db()
    create_db()
    logger.info("Database reset successfully.")

def main():
    """Main entry point for the script."""
    parser = argparse.ArgumentParser(description="Database management script for ClassyWeb")
    parser.add_argument("command", choices=["create", "drop", "reset"], help="Command to execute")
    args = parser.parse_args()

    if args.command == "create":
        create_db()
    elif args.command == "drop":
        drop_db()
    elif args.command == "reset":
        reset_db()

if __name__ == "__main__":
    main()
