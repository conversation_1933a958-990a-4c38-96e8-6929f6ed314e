import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

import { Loader2, Brain, Zap, AlertCircle, CheckCircle2 } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { getLLMProviders, fetchLLMModels } from "@/services/llmApi";
import { ProviderListResponse } from "@/types/index";

export interface LLMConfiguration {
  provider: string;
  model: string;
  endpoint: string;
  customPrompt?: string;
  temperature?: number;
  maxTokens?: number;
}

interface LLMConfigurationPanelProps {
  onConfigurationChange: (config: LLMConfiguration) => void;
  onApply: (config: LLMConfiguration) => void;
  onCancel: () => void;
  initialConfig?: Partial<LLMConfiguration>;
  classificationType: string;
}

export const LLMConfigurationPanel = ({
  onConfigurationChange,
  onApply,
  onCancel,
  initialConfig,
  classificationType
}: LLMConfigurationPanelProps) => {
  const { toast } = useToast();
  const [providers, setProviders] = useState<ProviderListResponse | null>(null);
  const [models, setModels] = useState<string[]>([]);
  const [loadingProviders, setLoadingProviders] = useState(true);
  const [loadingModels, setLoadingModels] = useState(false);
  const [config, setConfig] = useState<LLMConfiguration>({
    provider: initialConfig?.provider || "",
    model: initialConfig?.model || "",
    endpoint: initialConfig?.endpoint || "",
    customPrompt: initialConfig?.customPrompt || "",
    temperature: initialConfig?.temperature || 0.1,
    maxTokens: initialConfig?.maxTokens || 100
  });

  // Load providers on component mount
  useEffect(() => {
    loadProviders();
  }, []);

  // Load models when provider or endpoint changes
  useEffect(() => {
    if (config.provider && config.endpoint) {
      loadModels();
    }
  }, [config.provider, config.endpoint]);

  // Notify parent of configuration changes
  useEffect(() => {
    if (onConfigurationChange && typeof onConfigurationChange === 'function') {
      onConfigurationChange(config);
    }
  }, [config, onConfigurationChange]);

  const loadProviders = async () => {
    try {
      setLoadingProviders(true);
      const providersData = await getLLMProviders();
      setProviders(providersData);
      
      // Set default provider if none selected and load its models
      if (!config.provider && providersData.providers.length > 0) {
        const defaultProvider = providersData.providers[0];
        const defaultEndpoint = providersData.default_endpoints[defaultProvider] || "";

        setConfig(prev => ({
          ...prev,
          provider: defaultProvider,
          endpoint: defaultEndpoint,
          model: "" // Will be set after loading models
        }));

        // Load models for the default provider
        try {
          setLoadingModels(true);
          const modelsData = await fetchLLMModels({
            provider: defaultProvider,
            endpoint: defaultEndpoint
          });
          setModels(modelsData.models);

          // Set default model
          if (modelsData.models.length > 0) {
            const defaultModel = providersData.default_models[defaultProvider] || modelsData.models[0];
            setConfig(prev => ({
              ...prev,
              model: defaultModel
            }));
          }
        } catch (error) {
          console.error('Failed to load models for default provider:', defaultProvider, error);
          // Set fallback model from provider data
          setConfig(prev => ({
            ...prev,
            model: providersData.default_models[defaultProvider] || ""
          }));
        } finally {
          setLoadingModels(false);
        }
      }
    } catch (error) {
      console.error('Failed to load providers:', error);
      toast({
        title: "Error loading providers",
        description: "Failed to fetch available LLM providers",
        variant: "destructive"
      });
    } finally {
      setLoadingProviders(false);
    }
  };

  const loadModels = async () => {
    if (!config.provider || !config.endpoint) return;
    
    try {
      setLoadingModels(true);
      const modelsData = await fetchLLMModels({
        provider: config.provider,
        endpoint: config.endpoint
      });
      setModels(modelsData.models);
      
      // Set default model if none selected and models are available
      if (!config.model && modelsData.models.length > 0) {
        setConfig(prev => ({
          ...prev,
          model: modelsData.models[0]
        }));
      }
    } catch (error) {
      console.error('Failed to load models:', error);
      toast({
        title: "Error loading models",
        description: `Failed to fetch models for ${config.provider}`,
        variant: "destructive"
      });
      setModels([]);
    } finally {
      setLoadingModels(false);
    }
  };

  const handleProviderChange = (provider: string) => {
    if (!providers) return;

    // Update provider and endpoint first
    setConfig(prev => ({
      ...prev,
      provider,
      endpoint: providers.default_endpoints[provider] || "",
      model: "" // Clear model selection while loading
    }));

    // Load models for the new provider asynchronously
    const loadModelsForProvider = async () => {
      try {
        setLoadingModels(true);
        const modelsData = await fetchLLMModels({
          provider: provider,
          endpoint: providers.default_endpoints[provider] || ""
        });
        setModels(modelsData.models);

        // Set default model if models are available
        if (modelsData.models.length > 0) {
          const defaultModel = providers.default_models[provider] || modelsData.models[0];
          setConfig(prev => ({
            ...prev,
            model: defaultModel
          }));
        }
      } catch (error) {
        console.error('Failed to load models for provider:', provider, error);
        toast({
          title: "Error loading models",
          description: `Failed to fetch models for ${provider}`,
          variant: "destructive"
        });
        setModels([]);
      } finally {
        setLoadingModels(false);
      }
    };

    // Execute async operation
    loadModelsForProvider();
  };

  const handleModelChange = (model: string) => {
    setConfig(prev => ({
      ...prev,
      model
    }));
  };



  const handleCustomPromptChange = (customPrompt: string) => {
    setConfig(prev => ({
      ...prev,
      customPrompt
    }));
  };

  const getProviderStatus = (provider: string) => {
    if (!providers) return "unknown";
    return providers.api_keys[provider] === "available" ? "available" : "missing";
  };

  const isConfigurationValid = () => {
    return config.provider && config.model && config.endpoint;
  };

  const getDefaultPromptForType = (type: string) => {
    const prompts = {
      binary: "Classify the following text as either positive or negative. Respond with only 'positive' or 'negative'.",
      "multi-class": "Classify the following text into one of the provided categories. Respond with only the category name.",
      "multi-label": "Identify all applicable labels for the following text. Respond with a comma-separated list of labels.",
      hierarchical: "Classify the following text using the provided hierarchical structure. Respond with the full path (e.g., 'Technology > AI > Machine Learning').",
      flat: "Classify the following text into the most appropriate category. Respond with only the category name."
    };
    return prompts[type as keyof typeof prompts] || prompts["multi-class"];
  };

  if (loadingProviders) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center space-x-2">
            <Loader2 className="w-4 h-4 animate-spin" />
            <span>Loading LLM providers...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="border-2 border-primary/20">
      <CardHeader>
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 rounded-lg bg-primary/10 flex items-center justify-center">
            <Brain className="w-5 h-5 text-primary" />
          </div>
          <div>
            <CardTitle>LLM Inference Configuration</CardTitle>
            <CardDescription>
              Configure your language model for {classificationType} classification
            </CardDescription>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Provider Selection */}
        <div className="space-y-2">
          <Label htmlFor="provider">AI Model Provider</Label>
          <Select value={config.provider} onValueChange={handleProviderChange}>
            <SelectTrigger>
              <SelectValue placeholder="Select a provider" />
            </SelectTrigger>
            <SelectContent>
              {providers?.providers.map((provider) => (
                <SelectItem key={provider} value={provider}>
                  <div className="flex items-center justify-between w-full">
                    <span>{provider}</span>
                    {getProviderStatus(provider) === "available" ? (
                      <CheckCircle2 className="w-4 h-4 text-green-500 ml-2" />
                    ) : (
                      <AlertCircle className="w-4 h-4 text-yellow-500 ml-2" />
                    )}
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {config.provider && getProviderStatus(config.provider) === "missing" && (
            <p className="text-xs text-yellow-600">
              API key not configured for {config.provider}. Some features may be limited.
            </p>
          )}
        </div>

        {/* Model Selection */}
        <div className="space-y-2">
          <Label htmlFor="model">Model</Label>
          <Select 
            value={config.model} 
            onValueChange={handleModelChange}
            disabled={!config.provider || loadingModels}
          >
            <SelectTrigger>
              <SelectValue placeholder={loadingModels ? "Loading models..." : "Select a model"} />
            </SelectTrigger>
            <SelectContent>
              {models.map((model) => (
                <SelectItem key={model} value={model}>
                  {model}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {loadingModels && (
            <div className="flex items-center space-x-2 text-xs text-muted-foreground">
              <Loader2 className="w-3 h-3 animate-spin" />
              <span>Loading available models...</span>
            </div>
          )}
        </div>

        {/* Custom Prompt */}
        <div className="space-y-2">
          <Label htmlFor="custom-prompt">Custom Prompt (Optional)</Label>
          <Textarea
            id="custom-prompt"
            placeholder={`Default: ${getDefaultPromptForType(classificationType)}`}
            value={config.customPrompt}
            onChange={(e) => handleCustomPromptChange(e.target.value)}
            rows={4}
            className="resize-none"
          />
          <p className="text-xs text-muted-foreground">
            Leave empty to use our optimized prompts for {classificationType} classification
          </p>
        </div>

        {/* Advanced Settings */}
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="temperature">Temperature</Label>
            <Select 
              value={config.temperature?.toString()} 
              onValueChange={(value) => setConfig(prev => ({ ...prev, temperature: parseFloat(value) }))}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="0.0">0.0 (Deterministic)</SelectItem>
                <SelectItem value="0.1">0.1 (Very Low)</SelectItem>
                <SelectItem value="0.3">0.3 (Low)</SelectItem>
                <SelectItem value="0.5">0.5 (Medium)</SelectItem>
                <SelectItem value="0.7">0.7 (High)</SelectItem>
                <SelectItem value="1.0">1.0 (Very High)</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="space-y-2">
            <Label htmlFor="max-tokens">Max Tokens</Label>
            <Select 
              value={config.maxTokens?.toString()} 
              onValueChange={(value) => setConfig(prev => ({ ...prev, maxTokens: parseInt(value) }))}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="50">50 (Short)</SelectItem>
                <SelectItem value="100">100 (Standard)</SelectItem>
                <SelectItem value="200">200 (Long)</SelectItem>
                <SelectItem value="500">500 (Very Long)</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex gap-3 pt-4 border-t">
          <Button
            onClick={() => onApply(config)}
            disabled={!isConfigurationValid()}
            className="flex-1"
          >
            <Zap className="w-4 h-4 mr-2" />
            Apply Settings
          </Button>
          <Button variant="outline" onClick={onCancel}>
            Cancel
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};
