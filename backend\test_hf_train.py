import requests
import json
from pprint import pprint

# Test HF training endpoint
def test_hf_training():
    # Create a test request with a valid hierarchy structure
    request_data = {
        "training_file_id": "test123",
        "original_training_filename": "test.csv",
        "text_column": "text",
        "hierarchy_columns": {
            "Theme": "theme",
            "Category": "category",
            "Segment": "segment",
            "Subsegment": "subsegment"
        },
        "base_model": "distilbert-base-uncased",
        "num_epochs": 3,
        "new_model_name": "test-model-1"
    }
    
    print("Sending request to /hf/train:")
    pprint(request_data)
    
    # Wrap the request data in a 'request' field as expected by the backend
    wrapped_request = {"request": request_data}
    
    # Send the request
    try:
        response = requests.post("http://localhost:8000/hf/train", json=wrapped_request)
        
        print(f"\nResponse status code: {response.status_code}")
        
        if response.status_code == 202:
            print("HF training request successful!")
            print("Response:")
            pprint(response.json())
            return response.json()
        else:
            print("HF training request failed!")
            print("Response:")
            try:
                pprint(response.json())
            except:
                print(response.text)
            return None
    except Exception as e:
        print(f"Error sending HF training request: {e}")
        return None

if __name__ == "__main__":
    test_hf_training()
