// frontend/src/services/dataApi.ts
import apiClient from './apiClient';
import { FileInfo, FileUpdateRequest, MessageResponse } from '../types';

/**
 * Uploads a file to the server
 * @param file The file to upload
 * @returns FileInfo object with file metadata
 */
export const uploadFile = async (file: File): Promise<FileInfo> => {
  const formData = new FormData();
  formData.append('file', file); // Key must match FastAPI parameter name 'file'
  try {
    const response = await apiClient.post<FileInfo>('/files/upload', formData, {
      headers: {
        // Axios might set this automatically for FormData, but explicitly is fine
        'Content-Type': 'multipart/form-data',
      },
      // Optional: Add progress tracking here if needed
      // onUploadProgress: progressEvent => { ... }
    });
    return response.data;
  } catch (error: any) {
    // Rethrow a more specific error or the formatted error from interceptor
    throw error;
  }
};

/**
 * Gets detailed information about a file
 * @param fileId The ID of the file to get
 * @returns FileInfo object with file metadata and preview
 */
export const getFileDetails = async (fileId: string): Promise<FileInfo> => {
  try {
    const response = await apiClient.get<FileInfo>(`/files/${fileId}`);
    return response.data;
  } catch (error: any) {
    throw error;
  }
};

/**
 * Updates a file's information
 * @param fileId The ID of the file to update
 * @param updateData The data to update
 * @returns Updated FileInfo object
 */
export const updateFile = async (fileId: string, updateData: FileUpdateRequest): Promise<FileInfo> => {
  try {
    const response = await apiClient.put<FileInfo>(`/files/${fileId}`, updateData);
    return response.data;
  } catch (error: any) {
    throw error;
  }
};

/**
 * Deletes a file
 * @param fileId The ID of the file to delete
 * @returns Message response
 */
export const deleteFile = async (fileId: string): Promise<MessageResponse> => {
  try {
    const response = await apiClient.delete<MessageResponse>(`/files/${fileId}`);
    return response.data;
  } catch (error: any) {
    throw error;
  }
};
