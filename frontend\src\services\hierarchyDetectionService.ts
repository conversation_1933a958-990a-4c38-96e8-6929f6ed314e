/**
 * HierarchyDetectionService.ts
 * 
 * Advanced service for automatically detecting hierarchical structures from training data
 */

import { UploadedFile } from './fileUploadApi';

export interface DetectedHierarchyLevel {
  id: string;
  name: string;
  column: string;
  displayName: string;
  description: string;
  order: number;
  confidence: number; // 0-1 confidence score
  uniqueValues: number;
  sampleValues: string[];
  isNested: boolean; // Whether this level shows hierarchical nesting
}

export interface HierarchyDetectionResult {
  levels: DetectedHierarchyLevel[];
  confidence: number;
  recommendations: string[];
  warnings: string[];
  detectionMethod: 'column_names' | 'value_analysis' | 'pattern_matching' | 'manual';
}

export class HierarchyDetectionService {
  
  /**
   * Automatically detect hierarchy levels from training data
   */
  static async detectHierarchyFromTrainingData(
    trainingFileInfo: UploadedFile,
    trainingData?: any[]
  ): Promise<HierarchyDetectionResult> {
    
    const result: HierarchyDetectionResult = {
      levels: [],
      confidence: 0,
      recommendations: [],
      warnings: [],
      detectionMethod: 'column_names'
    };

    if (!trainingFileInfo || !trainingFileInfo.columns) {
      result.warnings.push('No training data or columns available for hierarchy detection');
      return result;
    }

    // Step 1: Detect potential hierarchy columns by name patterns
    const hierarchyColumns = this.detectHierarchyColumnsByName(trainingFileInfo.columns);
    
    // Step 2: If we have preview data, analyze value patterns
    let valueAnalysis: any = {};
    if (trainingFileInfo.preview && trainingFileInfo.preview.length > 0) {
      valueAnalysis = this.analyzeColumnValues(trainingFileInfo.preview, hierarchyColumns);
    }

    // Step 3: Create hierarchy levels based on detection
    result.levels = this.createHierarchyLevels(hierarchyColumns, valueAnalysis, trainingFileInfo);
    
    // Step 4: Calculate overall confidence and provide recommendations
    result.confidence = this.calculateOverallConfidence(result.levels);
    result.recommendations = this.generateRecommendations(result.levels, trainingFileInfo);
    result.warnings = this.generateWarnings(result.levels, trainingFileInfo);

    return result;
  }

  /**
   * Detect hierarchy columns based on column name patterns
   */
  private static detectHierarchyColumnsByName(columns: string[]): string[] {
    const hierarchyPatterns = [
      // Direct hierarchy indicators
      /level\s*\d*/i,
      /tier\s*\d*/i,
      /category/i,
      /class/i,
      /type/i,
      /group/i,
      /division/i,
      /section/i,
      /department/i,
      /theme/i,
      /topic/i,
      /subject/i,
      
      // Hierarchical naming patterns
      /main.*category/i,
      /sub.*category/i,
      /parent/i,
      /child/i,
      /primary/i,
      /secondary/i,
      /tertiary/i,
      
      // Common business hierarchies
      /industry/i,
      /sector/i,
      /domain/i,
      /area/i,
      /field/i,
      /branch/i,
      
      // Numbered levels
      /l\d+/i, // L1, L2, etc.
      /level_?\d+/i,
      /cat_?\d+/i,
      /hierarchy_?\d+/i
    ];

    const potentialColumns = columns.filter(col => 
      hierarchyPatterns.some(pattern => pattern.test(col))
    );

    // Sort by likely hierarchy order (level1, level2, etc.)
    return potentialColumns.sort((a, b) => {
      const aNum = this.extractLevelNumber(a);
      const bNum = this.extractLevelNumber(b);
      
      if (aNum !== null && bNum !== null) {
        return aNum - bNum;
      }
      
      // Fallback to alphabetical
      return a.localeCompare(b);
    });
  }

  /**
   * Extract level number from column name (e.g., "level1" -> 1)
   */
  private static extractLevelNumber(columnName: string): number | null {
    const matches = columnName.match(/(\d+)/);
    return matches ? parseInt(matches[1]) : null;
  }

  /**
   * Analyze column values to understand hierarchy structure
   */
  private static analyzeColumnValues(previewData: any[], hierarchyColumns: string[]): any {
    const analysis: any = {};

    hierarchyColumns.forEach(column => {
      const values = previewData
        .map(row => row[column])
        .filter(val => val !== null && val !== undefined && val !== '');

      const uniqueValues = [...new Set(values)];
      
      analysis[column] = {
        totalValues: values.length,
        uniqueValues: uniqueValues.length,
        uniqueValuesList: uniqueValues.slice(0, 10), // Sample values
        cardinality: uniqueValues.length / values.length,
        sampleValues: uniqueValues.slice(0, 5)
      };
    });

    return analysis;
  }

  /**
   * Create hierarchy level objects from detected columns
   */
  private static createHierarchyLevels(
    hierarchyColumns: string[], 
    valueAnalysis: any, 
    fileInfo: UploadedFile
  ): DetectedHierarchyLevel[] {
    
    return hierarchyColumns.map((column, index) => {
      const analysis = valueAnalysis[column] || {};
      const confidence = this.calculateColumnConfidence(column, analysis);
      
      return {
        id: `detected_level_${index}`,
        name: this.generateLevelName(column, index),
        column: column,
        displayName: this.formatDisplayName(column),
        description: this.generateLevelDescription(column, index, hierarchyColumns.length),
        order: index,
        confidence: confidence,
        uniqueValues: analysis.uniqueValues || 0,
        sampleValues: analysis.sampleValues || [],
        isNested: this.detectNesting(column, analysis)
      };
    });
  }

  /**
   * Calculate confidence score for a column being a hierarchy level
   */
  private static calculateColumnConfidence(column: string, analysis: any): number {
    let confidence = 0.5; // Base confidence

    // Name-based confidence
    if (/level|tier|category|class/i.test(column)) confidence += 0.3;
    if (/\d+/.test(column)) confidence += 0.2; // Has numbers
    
    // Value-based confidence
    if (analysis.uniqueValues) {
      const cardinality = analysis.cardinality || 0;
      
      // Good hierarchy levels have moderate cardinality (not too high, not too low)
      if (cardinality > 0.1 && cardinality < 0.8) confidence += 0.2;
      if (analysis.uniqueValues > 1 && analysis.uniqueValues < 50) confidence += 0.1;
    }

    return Math.min(confidence, 1.0);
  }

  /**
   * Generate a user-friendly level name
   */
  private static generateLevelName(column: string, index: number): string {
    // Try to extract meaningful name from column
    const cleanName = column
      .replace(/[_-]/g, ' ')
      .replace(/\d+/g, '')
      .trim()
      .replace(/\b\w/g, l => l.toUpperCase());

    if (cleanName && cleanName !== column) {
      return cleanName;
    }

    // Fallback to generic names
    const levelNames = ['Category', 'Subcategory', 'Type', 'Subtype', 'Class', 'Subclass'];
    return levelNames[index] || `Level ${index + 1}`;
  }

  /**
   * Format column name for display
   */
  private static formatDisplayName(column: string): string {
    return column
      .replace(/[_-]/g, ' ')
      .replace(/\b\w/g, l => l.toUpperCase());
  }

  /**
   * Generate description for hierarchy level
   */
  private static generateLevelDescription(column: string, index: number, totalLevels: number): string {
    const position = index === 0 ? 'Most general' : 
                    index === totalLevels - 1 ? 'Most specific' : 
                    'Intermediate';
    
    return `${position} level of the hierarchy (Column: ${column})`;
  }

  /**
   * Detect if column shows hierarchical nesting patterns
   */
  private static detectNesting(column: string, analysis: any): boolean {
    // Simple heuristic: if values contain separators, might be nested
    if (analysis.sampleValues) {
      return analysis.sampleValues.some((val: string) => 
        typeof val === 'string' && (val.includes('/') || val.includes('>') || val.includes('|'))
      );
    }
    return false;
  }

  /**
   * Calculate overall confidence for the hierarchy detection
   */
  private static calculateOverallConfidence(levels: DetectedHierarchyLevel[]): number {
    if (levels.length === 0) return 0;
    
    const avgConfidence = levels.reduce((sum, level) => sum + level.confidence, 0) / levels.length;
    
    // Bonus for having multiple levels
    const levelBonus = Math.min(levels.length / 4, 0.2);
    
    return Math.min(avgConfidence + levelBonus, 1.0);
  }

  /**
   * Generate recommendations based on detected hierarchy
   */
  private static generateRecommendations(levels: DetectedHierarchyLevel[], fileInfo: UploadedFile): string[] {
    const recommendations: string[] = [];

    if (levels.length === 0) {
      recommendations.push('No hierarchy columns detected. Consider adding columns with hierarchical labels.');
      recommendations.push('Hierarchy columns should have names like "category", "subcategory", "level1", "level2", etc.');
    } else if (levels.length === 1) {
      recommendations.push('Only one hierarchy level detected. Consider adding more levels for better classification.');
    } else if (levels.length > 5) {
      recommendations.push('Many hierarchy levels detected. Consider consolidating to 2-4 levels for optimal performance.');
    }

    // Check for low confidence levels
    const lowConfidenceLevels = levels.filter(level => level.confidence < 0.6);
    if (lowConfidenceLevels.length > 0) {
      recommendations.push(`Review these potentially incorrect levels: ${lowConfidenceLevels.map(l => l.column).join(', ')}`);
    }

    return recommendations;
  }

  /**
   * Generate warnings about potential issues
   */
  private static generateWarnings(levels: DetectedHierarchyLevel[], fileInfo: UploadedFile): string[] {
    const warnings: string[] = [];

    if (levels.length < 2) {
      warnings.push('Hierarchical classification requires at least 2 hierarchy levels.');
    }

    // Check for levels with too few unique values
    const lowCardinalityLevels = levels.filter(level => level.uniqueValues < 2);
    if (lowCardinalityLevels.length > 0) {
      warnings.push(`These levels have very few categories: ${lowCardinalityLevels.map(l => l.column).join(', ')}`);
    }

    return warnings;
  }
}
