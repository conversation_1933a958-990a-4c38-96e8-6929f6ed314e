// frontend/src/services/taskApi.ts
import apiClient, { API_BASE_URL } from './apiClient';
import axios from 'axios';
import { TaskStatus, ClassificationResultRow, MessageResponse } from '../types';

/**
 * Fetches the status of a task
 * @param taskId ID of the task
 * @returns TaskStatus with the current status
 */
export const getTaskStatus = async (taskId: string): Promise<TaskStatus> => {
  if (!taskId) {
      console.error("Task ID is required to fetch status.");
      throw new Error("Task ID is required.");
  }
  try {
    const response = await apiClient.get<TaskStatus>(`/tasks/${taskId}`);
    return response.data;
  } catch (error: any) {
    console.error(`Error fetching status for task ${taskId}:`, error);
    // Handle 404 specifically?
    if (axios.isAxiosError(error) && error.response?.status === 404) {
        throw new Error(`Task with ID ${taskId} not found.`);
    }
    throw error; // Rethrow formatted error from interceptor
  }
};

/**
 * Fetches the result data for a completed task
 * @param taskId ID of the task
 * @returns Array of ClassificationResultRow objects
 */
export const getResultData = async (taskId: string): Promise<ClassificationResultRow[]> => {
    if (!taskId) {
        console.error("Task ID is required to fetch result data.");
        throw new Error("Task ID is required.");
    }
    try {
        // The backend endpoint /results/{task_id}/data returns the JSON array
        console.log(`Fetching results data from /results/${taskId}/data`);
        const response = await apiClient.get<ClassificationResultRow[]>(`/results/${taskId}/data`);
        console.log(`Results data received:`, {
            dataLength: response.data?.length || 0,
            firstRow: response.data && response.data.length > 0 ? response.data[0] : null
        });
        return response.data;
    } catch (error: any) {
        console.error(`Error fetching result data for task ${taskId}:`, error);
        if (axios.isAxiosError(error) && error.response?.status === 404) {
            throw new Error(`Result data not found for task ${taskId}.`);
        }
        if (axios.isAxiosError(error) && error.response?.status === 409) {
            throw new Error(`Task ${taskId} is not yet completed successfully.`);
        }
        throw error; // Rethrow formatted error from interceptor or other errors
    }
};

/**
 * Downloads the results of a task as Excel or CSV
 * @param taskId ID of the task
 */
export const downloadResultExcel = (taskId: string): void => {
    if (!taskId) {
        console.error("Task ID is required to download results.");
        alert("Task ID is required to download results.");
        return;
    }

    try {
        // Create a URL to the Excel/CSV endpoint
        const downloadUrl = `${API_BASE_URL}/results/${taskId}/excel`;

        // Create a temporary link element to trigger the download
        // Note: We don't set the download attribute with extension since the server
        // will determine if it's Excel or CSV and set the appropriate Content-Disposition header
        const link = document.createElement('a');
        link.href = downloadUrl;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    } catch (error) {
        console.error(`Error downloading results for task ${taskId}:`, error);
        alert("Failed to download results file. Please try again.");
    }
};

/**
 * Deletes a task
 * @param taskId ID of the task to delete
 * @returns Message response
 */
export const deleteTask = async (taskId: string): Promise<MessageResponse> => {
  if (!taskId) {
    console.error("Task ID is required to delete task.");
    throw new Error("Task ID is required.");
  }
  try {
    const response = await apiClient.delete<MessageResponse>(`/tasks/${taskId}`);
    return response.data;
  } catch (error: any) {
    console.error(`Error deleting task ${taskId}:`, error);
    if (axios.isAxiosError(error) && error.response?.status === 404) {
      throw new Error(`Task with ID ${taskId} not found.`);
    }
    throw error; // Rethrow formatted error from interceptor
  }
};