"""
Sc<PERSON><PERSON> to test the Google OAuth flow directly.
"""
import os
import sys
import logging
import requests
import urllib.parse
import webbrowser
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import parse_qs, urlparse
import threading
import json
from datetime import datetime, timezone

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Try to load environment variables from .env file
try:
    from dotenv import load_dotenv
    load_dotenv()
    logger.info("Loaded environment variables from .env file")
except ImportError:
    logger.warning("python-dotenv not installed, skipping .env loading")

# Get OAuth configuration from environment variables
client_id = os.getenv("GOOGLE_CLIENT_ID", "")
client_secret = os.getenv("GOOGLE_CLIENT_SECRET", "")
redirect_uri = "http://localhost:8080/callback"  # Use a different port for this test

# OAuth endpoints
auth_url = "https://accounts.google.com/o/oauth2/v2/auth"
token_url = "https://oauth2.googleapis.com/token"
userinfo_url = "https://www.googleapis.com/oauth2/v2/userinfo"

# Global variable to store the authorization code
auth_code = None
auth_code_event = threading.Event()

class OAuthCallbackHandler(BaseHTTPRequestHandler):
    def do_GET(self):
        global auth_code
        
        # Parse the query parameters
        query = urlparse(self.path).query
        params = parse_qs(query)
        
        if "code" in params:
            auth_code = params["code"][0]
            logger.info(f"Received authorization code: {auth_code[:10]}...")
            
            # Send a success response
            self.send_response(200)
            self.send_header("Content-type", "text/html")
            self.end_headers()
            
            response = """
            <html>
            <head><title>OAuth Test Success</title></head>
            <body>
                <h1>Authorization Successful</h1>
                <p>You can close this window and return to the terminal.</p>
            </body>
            </html>
            """
            self.wfile.write(response.encode())
            
            # Signal that we've received the code
            auth_code_event.set()
        elif "error" in params:
            error = params["error"][0]
            logger.error(f"OAuth error: {error}")
            
            # Send an error response
            self.send_response(400)
            self.send_header("Content-type", "text/html")
            self.end_headers()
            
            response = f"""
            <html>
            <head><title>OAuth Test Error</title></head>
            <body>
                <h1>Authorization Failed</h1>
                <p>Error: {error}</p>
                <p>You can close this window and return to the terminal.</p>
            </body>
            </html>
            """
            self.wfile.write(response.encode())
            
            # Signal that we've received an error
            auth_code_event.set()
        else:
            # Send a bad request response
            self.send_response(400)
            self.send_header("Content-type", "text/html")
            self.end_headers()
            
            response = """
            <html>
            <head><title>OAuth Test Error</title></head>
            <body>
                <h1>Invalid Request</h1>
                <p>No authorization code or error received.</p>
                <p>You can close this window and return to the terminal.</p>
            </body>
            </html>
            """
            self.wfile.write(response.encode())

def start_callback_server():
    """Start the callback server to receive the authorization code."""
    server_address = ("", 8080)
    httpd = HTTPServer(server_address, OAuthCallbackHandler)
    
    # Start the server in a separate thread
    server_thread = threading.Thread(target=httpd.serve_forever)
    server_thread.daemon = True
    server_thread.start()
    
    logger.info("Callback server started on port 8080")
    
    return httpd

def exchange_code_for_token(code):
    """Exchange the authorization code for an access token."""
    logger.info("Exchanging authorization code for access token...")
    
    token_data = {
        "code": code,
        "client_id": client_id,
        "client_secret": client_secret,
        "redirect_uri": redirect_uri,
        "grant_type": "authorization_code"
    }
    
    headers = {
        "Content-Type": "application/x-www-form-urlencoded"
    }
    
    try:
        response = requests.post(token_url, data=urllib.parse.urlencode(token_data), headers=headers)
        logger.info(f"Token request status code: {response.status_code}")
        
        if response.status_code == 200:
            token_json = response.json()
            logger.info("Successfully obtained access token")
            return token_json
        else:
            logger.error(f"Failed to obtain access token: {response.text}")
            return None
    except Exception as e:
        logger.error(f"Error exchanging code for token: {e}")
        return None

def get_user_info(access_token):
    """Get user information using the access token."""
    logger.info("Getting user information...")
    
    headers = {
        "Authorization": f"Bearer {access_token}"
    }
    
    try:
        response = requests.get(userinfo_url, headers=headers)
        logger.info(f"User info request status code: {response.status_code}")
        
        if response.status_code == 200:
            user_info = response.json()
            logger.info(f"Successfully obtained user info: {user_info}")
            return user_info
        else:
            logger.error(f"Failed to obtain user info: {response.text}")
            return None
    except Exception as e:
        logger.error(f"Error getting user info: {e}")
        return None

def main():
    """Main function to test the OAuth flow."""
    logger.info("=== Google OAuth Flow Test ===")
    
    # Check if client ID and secret are set
    if not client_id:
        logger.error("GOOGLE_CLIENT_ID is not set")
        return
    
    if not client_secret:
        logger.error("GOOGLE_CLIENT_SECRET is not set")
        return
    
    logger.info(f"Using client ID: {client_id[:8]}...{client_id[-8:]}")
    logger.info(f"Using redirect URI: {redirect_uri}")
    
    # Start the callback server
    httpd = start_callback_server()
    
    # Construct the authorization URL
    auth_params = {
        "client_id": client_id,
        "redirect_uri": redirect_uri,
        "response_type": "code",
        "scope": "email profile",
        "access_type": "offline",
        "prompt": "consent"
    }
    
    auth_request_url = f"{auth_url}?{urllib.parse.urlencode(auth_params)}"
    logger.info(f"Opening browser to: {auth_request_url}")
    
    # Open the browser to the authorization URL
    webbrowser.open(auth_request_url)
    
    # Wait for the authorization code
    logger.info("Waiting for authorization code...")
    auth_code_event.wait(timeout=300)  # Wait up to 5 minutes
    
    # Stop the server
    httpd.shutdown()
    
    if not auth_code:
        logger.error("No authorization code received")
        return
    
    # Exchange the code for a token
    token_json = exchange_code_for_token(auth_code)
    
    if not token_json:
        logger.error("Failed to exchange code for token")
        return
    
    # Get user information
    if "access_token" in token_json:
        user_info = get_user_info(token_json["access_token"])
        
        if user_info:
            logger.info("OAuth flow test completed successfully")
        else:
            logger.error("Failed to get user information")
    else:
        logger.error("No access token received")

if __name__ == "__main__":
    main()
