import { useState } from "react";
import { Navigation } from "@/components/Navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Slider } from "@/components/ui/slider";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import { useToast } from "@/hooks/use-toast";
import { 
  Database, 
  Sliders, 
  Code, 
  Brain, 
  Settings, 
  Activity, 
  GitCompare, 
  Cloud,
  Upload,
  Play,
  Pause,
  BarChart3,
  TrendingUp,
  Zap,
  Cpu
} from "lucide-react";

const ExpertWorkflow = () => {
  const [selectedTab, setSelectedTab] = useState("upload");
  const [isTraining, setIsTraining] = useState(false);
  const [trainingProgress, setTrainingProgress] = useState(0);
  const [learningRate, setLearningRate] = useState([0.001]);
  const [batchSize, setBatchSize] = useState([32]);
  const [epochs, setEpochs] = useState([100]);
  const { toast } = useToast();

  const startTraining = () => {
    setIsTraining(true);
    setTrainingProgress(0);
    
    // Simulate training progress
    const interval = setInterval(() => {
      setTrainingProgress(prev => {
        if (prev >= 100) {
          setIsTraining(false);
          clearInterval(interval);
          toast({
            title: "Training Complete",
            description: "Model achieved 94.7% accuracy on validation set",
          });
          return 100;
        }
        return prev + 2;
      });
    }, 200);
  };

  return (
    <div className="min-h-screen bg-background">
      <Navigation />
      
      <main className="pt-20">
        {/* Header */}
        <section className="py-8 bg-ml-primary/5">
          <div className="max-w-7xl mx-auto px-6">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-8 h-8 rounded-lg bg-ml-primary/20 flex items-center justify-center">
                <Brain className="w-5 h-5 text-ml-primary" />
              </div>
              <Badge variant="outline" className="border-ml-primary/30 text-ml-primary">
                Expert Journey
              </Badge>
            </div>
            <h1 className="text-3xl font-bold mb-2">Advanced ML Classification Studio</h1>
            <p className="text-muted-foreground">Full control over your machine learning workflow</p>
          </div>
        </section>

        <div className="max-w-7xl mx-auto px-6 py-8">
          <Tabs value={selectedTab} onValueChange={setSelectedTab} className="space-y-6">
            <TabsList className="grid w-full grid-cols-4 lg:grid-cols-8">
              <TabsTrigger value="upload" className="flex items-center gap-2">
                <Database className="w-4 h-4" />
                <span className="hidden sm:inline">Upload</span>
              </TabsTrigger>
              <TabsTrigger value="selection" className="flex items-center gap-2">
                <Sliders className="w-4 h-4" />
                <span className="hidden sm:inline">Selection</span>
              </TabsTrigger>
              <TabsTrigger value="config" className="flex items-center gap-2">
                <Code className="w-4 h-4" />
                <span className="hidden sm:inline">Config</span>
              </TabsTrigger>
              <TabsTrigger value="architecture" className="flex items-center gap-2">
                <Brain className="w-4 h-4" />
                <span className="hidden sm:inline">Architecture</span>
              </TabsTrigger>
              <TabsTrigger value="hyperparams" className="flex items-center gap-2">
                <Settings className="w-4 h-4" />
                <span className="hidden sm:inline">Hyperparams</span>
              </TabsTrigger>
              <TabsTrigger value="training" className="flex items-center gap-2">
                <Activity className="w-4 h-4" />
                <span className="hidden sm:inline">Training</span>
              </TabsTrigger>
              <TabsTrigger value="comparison" className="flex items-center gap-2">
                <GitCompare className="w-4 h-4" />
                <span className="hidden sm:inline">Compare</span>
              </TabsTrigger>
              <TabsTrigger value="deployment" className="flex items-center gap-2">
                <Cloud className="w-4 h-4" />
                <span className="hidden sm:inline">Deploy</span>
              </TabsTrigger>
            </TabsList>

            {/* Data Upload & Preprocessing */}
            <TabsContent value="upload">
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <Card className="lg:col-span-2">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Database className="w-5 h-5" />
                      Advanced Data Upload
                    </CardTitle>
                    <CardDescription>Upload and configure your dataset with preprocessing options</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="border-2 border-dashed border-border rounded-lg p-6">
                      <Upload className="w-8 h-8 text-muted-foreground mx-auto mb-4" />
                      <div className="text-center space-y-2">
                        <h3 className="font-semibold">Upload Dataset</h3>
                        <p className="text-sm text-muted-foreground">Multiple formats supported</p>
                        <Button variant="outline">
                          Choose Files
                        </Button>
                      </div>
                    </div>
                    
                    <div className="space-y-4">
                      <h4 className="font-semibold">Preprocessing Options</h4>
                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label>Data Split Ratio</Label>
                          <Select defaultValue="70-20-10">
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="70-20-10">70% Train / 20% Val / 10% Test</SelectItem>
                              <SelectItem value="80-10-10">80% Train / 10% Val / 10% Test</SelectItem>
                              <SelectItem value="60-20-20">60% Train / 20% Val / 20% Test</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                        <div className="space-y-2">
                          <Label>Normalization</Label>
                          <Select defaultValue="standard">
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="standard">Standard Scaler</SelectItem>
                              <SelectItem value="minmax">Min-Max Scaler</SelectItem>
                              <SelectItem value="robust">Robust Scaler</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Dataset Preview</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3 text-sm">
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Rows:</span>
                        <span className="font-medium">1,247</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Columns:</span>
                        <span className="font-medium">12</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Target Classes:</span>
                        <span className="font-medium">3</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Missing Values:</span>
                        <span className="font-medium text-ml-warning">2.3%</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            {/* Hyperparameter Tuning */}
            <TabsContent value="hyperparams">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Settings className="w-5 h-5" />
                      Training Parameters
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="space-y-2">
                      <Label>Learning Rate: {learningRate[0]}</Label>
                      <Slider
                        value={learningRate}
                        onValueChange={setLearningRate}
                        max={0.1}
                        min={0.0001}
                        step={0.0001}
                        className="w-full"
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <Label>Batch Size: {batchSize[0]}</Label>
                      <Slider
                        value={batchSize}
                        onValueChange={setBatchSize}
                        max={256}
                        min={16}
                        step={16}
                        className="w-full"
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <Label>Epochs: {epochs[0]}</Label>
                      <Slider
                        value={epochs}
                        onValueChange={setEpochs}
                        max={500}
                        min={10}
                        step={10}
                        className="w-full"
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <Label>Optimizer</Label>
                      <Select defaultValue="adam">
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="adam">Adam</SelectItem>
                          <SelectItem value="sgd">SGD</SelectItem>
                          <SelectItem value="rmsprop">RMSprop</SelectItem>
                          <SelectItem value="adamw">AdamW</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Regularization & Dropout</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="space-y-2">
                      <Label>Dropout Rate: 0.2</Label>
                      <Slider
                        defaultValue={[0.2]}
                        max={0.8}
                        min={0}
                        step={0.1}
                        className="w-full"
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <Label>L2 Regularization: 0.001</Label>
                      <Slider
                        defaultValue={[0.001]}
                        max={0.1}
                        min={0}
                        step={0.001}
                        className="w-full"
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <Label>Early Stopping Patience</Label>
                      <Input type="number" defaultValue={10} />
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            {/* Training & Monitoring */}
            <TabsContent value="training">
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <Card className="lg:col-span-2">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Activity className="w-5 h-5" />
                      Real-time Training Monitor
                    </CardTitle>
                    <CardDescription>
                      {isTraining ? "Training in progress..." : "Ready to start training"}
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="flex items-center gap-4">
                      <Button 
                        onClick={startTraining} 
                        disabled={isTraining}
                        className="bg-ml-primary hover:bg-ml-primary/90"
                      >
                        {isTraining ? (
                          <>
                            <Pause className="w-4 h-4 mr-2" />
                            Pause Training
                          </>
                        ) : (
                          <>
                            <Play className="w-4 h-4 mr-2" />
                            Start Training
                          </>
                        )}
                      </Button>
                      
                      {isTraining && (
                        <div className="flex items-center gap-2">
                          <Cpu className="w-4 h-4 text-ml-primary" />
                          <span className="text-sm">Using GPU acceleration</span>
                        </div>
                      )}
                    </div>
                    
                    {(isTraining || trainingProgress > 0) && (
                      <div className="space-y-4">
                        <div className="space-y-2">
                          <div className="flex justify-between text-sm">
                            <span>Training Progress</span>
                            <span>{trainingProgress}%</span>
                          </div>
                          <Progress value={trainingProgress} className="h-2" />
                        </div>
                        
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
                          <div className="p-3 bg-muted/50 rounded-lg">
                            <p className="text-xs text-muted-foreground mb-1">Epoch</p>
                            <p className="text-lg font-bold">{Math.floor(trainingProgress / 10)}/10</p>
                          </div>
                          <div className="p-3 bg-muted/50 rounded-lg">
                            <p className="text-xs text-muted-foreground mb-1">Loss</p>
                            <p className="text-lg font-bold text-ml-primary">0.234</p>
                          </div>
                          <div className="p-3 bg-muted/50 rounded-lg">
                            <p className="text-xs text-muted-foreground mb-1">Accuracy</p>
                            <p className="text-lg font-bold text-ml-success">94.7%</p>
                          </div>
                          <div className="p-3 bg-muted/50 rounded-lg">
                            <p className="text-xs text-muted-foreground mb-1">Val Acc</p>
                            <p className="text-lg font-bold text-ml-secondary">92.1%</p>
                          </div>
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <TrendingUp className="w-5 h-5" />
                      Training Metrics
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="h-32 bg-muted/30 rounded-lg flex items-center justify-center">
                        <BarChart3 className="w-8 h-8 text-muted-foreground" />
                      </div>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Best Val Acc:</span>
                          <span className="font-medium">92.3%</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Training Time:</span>
                          <span className="font-medium">2m 34s</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">GPU Utilization:</span>
                          <span className="font-medium text-ml-primary">87%</span>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            {/* Model Comparison */}
            <TabsContent value="comparison">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <GitCompare className="w-5 h-5" />
                    Model Performance Comparison
                  </CardTitle>
                  <CardDescription>Compare different model architectures and hyperparameter settings</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="overflow-x-auto">
                    <table className="w-full text-sm">
                      <thead>
                        <tr className="border-b">
                          <th className="text-left p-2">Model</th>
                          <th className="text-left p-2">Accuracy</th>
                          <th className="text-left p-2">Precision</th>
                          <th className="text-left p-2">Recall</th>
                          <th className="text-left p-2">F1-Score</th>
                          <th className="text-left p-2">Training Time</th>
                          <th className="text-left p-2">Status</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr className="border-b hover:bg-muted/50">
                          <td className="p-2 font-medium">BERT-Base</td>
                          <td className="p-2">94.7%</td>
                          <td className="p-2">93.2%</td>
                          <td className="p-2">94.1%</td>
                          <td className="p-2">93.6%</td>
                          <td className="p-2">2m 34s</td>
                          <td className="p-2">
                            <Badge variant="secondary" className="text-ml-success">Complete</Badge>
                          </td>
                        </tr>
                        <tr className="border-b hover:bg-muted/50">
                          <td className="p-2 font-medium">RoBERTa-Large</td>
                          <td className="p-2">96.1%</td>
                          <td className="p-2">95.8%</td>
                          <td className="p-2">96.0%</td>
                          <td className="p-2">95.9%</td>
                          <td className="p-2">4m 12s</td>
                          <td className="p-2">
                            <Badge variant="secondary" className="text-ml-success">Complete</Badge>
                          </td>
                        </tr>
                        <tr className="border-b hover:bg-muted/50 bg-ml-primary/5">
                          <td className="p-2 font-medium">Custom CNN</td>
                          <td className="p-2">92.3%</td>
                          <td className="p-2">91.7%</td>
                          <td className="p-2">92.8%</td>
                          <td className="p-2">92.2%</td>
                          <td className="p-2">1m 45s</td>
                          <td className="p-2">
                            <Badge className="bg-ml-primary">Training</Badge>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                  
                  <div className="mt-6 flex gap-4">
                    <Button variant="outline">
                      <Zap className="w-4 h-4 mr-2" />
                      Export Results
                    </Button>
                    <Button>
                      <GitCompare className="w-4 h-4 mr-2" />
                      Deploy Best Model
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </main>
    </div>
  );
};

export default ExpertWorkflow;