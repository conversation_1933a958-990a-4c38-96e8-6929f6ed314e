"""
Helper functions for the application.
"""
import os
import io
import logging
import pandas as pd
import traceback
from pathlib import Path
from typing import Optional, Dict, Any, List, Tuple
from collections import defaultdict

# Import config using relative import
from .. import config

# Configure logging
logger = logging.getLogger(__name__)

def load_data(file_path: Path | str, original_filename: str) -> Optional[pd.DataFrame]:
    """Loads data from CSV or Excel file path, handles common errors, using original filename for type detection."""
    if not file_path or not original_filename:
        logger.error("load_data received an empty file path or original filename.")
        return None

    file_path = Path(file_path) # Ensure it's a Path object
    if not file_path.exists():
        logger.error(f"File not found at path: {file_path}")
        return None

    try:
        # Use original_filename for logging and extension check
        logger.info(f"Loading '{original_filename}' from path: {file_path}")
        df = None
        # Use original_filename.lower() for case-insensitive extension check
        if original_filename.lower().endswith('.csv'):
            try:
                df = pd.read_csv(file_path, encoding='utf-8')
            except UnicodeDecodeError:
                logger.warning(f"UTF-8 decoding failed for '{original_filename}', trying latin1...")
                df = pd.read_csv(file_path, encoding='latin1')
            except pd.errors.EmptyDataError:
                 logger.warning(f"CSV file '{original_filename}' is empty.")
                 return pd.DataFrame()
            except Exception as e_csv:
                 logger.error(f"Error reading CSV file '{original_filename}': {e_csv}", exc_info=True)
                 return None
        elif original_filename.lower().endswith(('.xls', '.xlsx')):
             try:
                df = pd.read_excel(file_path, engine='openpyxl')
             except Exception as e_excel:
                 logger.error(f"Error reading Excel file '{original_filename}': {e_excel}", exc_info=True)
                 return None
        elif original_filename.lower().endswith('.json'):
            try:
                import json
                with open(file_path, 'r', encoding='utf-8') as f:
                    json_data = json.load(f)

                # Handle different JSON structures
                if isinstance(json_data, dict):
                    if 'results' in json_data:
                        # Hierarchical results format: {"results": [...], "metrics": {...}}
                        df = pd.DataFrame(json_data['results'])
                    else:
                        # Direct dictionary - try to convert to DataFrame
                        df = pd.DataFrame([json_data])
                elif isinstance(json_data, list):
                    # List of records
                    df = pd.DataFrame(json_data)
                else:
                    logger.error(f"Unsupported JSON structure in '{original_filename}'")
                    return None

            except Exception as e_json:
                logger.error(f"Error reading JSON file '{original_filename}': {e_json}", exc_info=True)
                return None
        else:
            # Log error using original_filename
            logger.error(f"Unsupported file format for '{original_filename}'. Please use CSV, Excel, or JSON.")
            return None # Explicitly return None for unsupported format

        if df is None:
             logger.error(f"DataFrame remained None after attempting to load '{original_filename}'.")
             return None

        # Basic cleaning (remains the same)
        original_shape = df.shape
        df = df.dropna(axis=1, how='all').dropna(axis=0, how='all')
        if df.shape != original_shape:
             logger.info(f"Dropped empty rows/columns from '{original_filename}'. Original: {original_shape}, New: {df.shape}")

        logger.info(f"Successfully loaded '{original_filename}' ({df.shape[0]} rows, {df.shape[1]} columns)")
        df.columns = df.columns.astype(str)
        return df

    except Exception as e:
        logger.error(f"Generic error loading file '{original_filename}': {e}")
        logger.error(traceback.format_exc())
        return None

def format_file_size(size_bytes: int) -> str:
    """
    Format file size in bytes to human-readable format.

    Args:
        size_bytes: File size in bytes

    Returns:
        Formatted file size string
    """
    for unit in ['B', 'KB', 'MB', 'GB']:
        if size_bytes < 1024.0:
            return f"{size_bytes:.2f} {unit}"
        size_bytes /= 1024.0
    return f"{size_bytes:.2f} TB"

def validate_hierarchy_config(hierarchy_config: Dict[str, Any]) -> List[str]:
    """
    Validate a hierarchy configuration.

    Args:
        hierarchy_config: Hierarchy configuration dictionary

    Returns:
        List of validation errors, empty if valid
    """
    errors = []

    # Check if hierarchy_levels exists and is a list
    if "hierarchy_levels" not in hierarchy_config:
        errors.append("Missing 'hierarchy_levels' in hierarchy configuration")
    elif not isinstance(hierarchy_config["hierarchy_levels"], list):
        errors.append("'hierarchy_levels' must be a list")
    elif len(hierarchy_config["hierarchy_levels"]) == 0:
        errors.append("'hierarchy_levels' cannot be empty")

    # Check if hierarchy_levels contains only strings
    if "hierarchy_levels" in hierarchy_config and isinstance(hierarchy_config["hierarchy_levels"], list):
        for level in hierarchy_config["hierarchy_levels"]:
            if not isinstance(level, str):
                errors.append(f"Hierarchy level '{level}' must be a string")

    return errors


# --- Data Conversion Utilities ---

def df_to_excel_bytes(df: pd.DataFrame) -> tuple[bytes, str]:
    """Converts DataFrame to Excel bytes or CSV as fallback.

    Returns:
        A tuple containing (file_bytes, file_extension) where file_extension is either 'xlsx' or 'csv'
    """
    if df is None or df.empty:
        logger.warning("Cannot convert empty DataFrame to Excel or CSV.")
        return b"", "csv"

    # Log the DataFrame columns before conversion
    logger.info(f"DataFrame columns before Excel/CSV conversion: {list(df.columns)}")

    # Try Excel libraries first
    output = io.BytesIO()

    # Try with openpyxl first (most reliable)
    try:
        import openpyxl
        logger.info("Attempting to write Excel using openpyxl.")
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer, index=False, sheet_name='Results')
        excel_data = output.getvalue()
        if len(excel_data) > 100:
            logger.info("Excel conversion with openpyxl successful.")
            return excel_data, "xlsx"
        logger.warning("openpyxl produced suspiciously small output.")
    except ImportError:
        logger.warning("`openpyxl` not found, trying other methods.")
    except Exception as e_openpyxl:
        logger.error(f"Error generating Excel with openpyxl: {e_openpyxl}", exc_info=True)

    # Reset buffer for next attempt
    output = io.BytesIO()

    # Try with xlsxwriter
    try:
        import xlsxwriter
        logger.info("Attempting to write Excel using xlsxwriter.")
        with pd.ExcelWriter(output, engine='xlsxwriter') as writer:
            df.to_excel(writer, index=False, sheet_name='Results')
        excel_data = output.getvalue()
        if len(excel_data) > 100:
            logger.info("Excel conversion with xlsxwriter successful.")
            return excel_data, "xlsx"
        logger.warning("xlsxwriter produced suspiciously small output.")
    except ImportError:
        logger.warning("`xlsxwriter` not found, skipping this method.")
    except Exception as e_xlsx:
        logger.error(f"Error generating Excel with xlsxwriter: {e_xlsx}", exc_info=True)

    # Reset buffer for next attempt
    output = io.BytesIO()

    # Try with pandas to_excel directly as last Excel attempt
    try:
        logger.info("Attempting direct pandas to_excel conversion.")
        df.to_excel(output, index=False, engine=None)  # Let pandas choose the engine
        excel_data = output.getvalue()
        if len(excel_data) > 100:
            logger.info("Direct pandas to_excel conversion successful.")
            return excel_data, "xlsx"
        logger.warning("Direct pandas to_excel produced suspiciously small output.")
    except Exception as e_direct:
        logger.error(f"Error with direct pandas to_excel: {e_direct}", exc_info=True)

    # If all Excel methods failed, fall back to CSV
    try:
        logger.info("All Excel methods failed. Falling back to CSV format.")
        csv_data = df.to_csv(index=False).encode('utf-8')
        if len(csv_data) > 0:
            logger.info("Successfully converted DataFrame to CSV.")
            return csv_data, "csv"
    except Exception as e_csv:
        logger.error(f"Error converting DataFrame to CSV: {e_csv}", exc_info=True)

    # If everything failed, return empty bytes with csv extension as default
    logger.error("All conversion methods failed. Returning empty bytes.")
    return b"", "csv"


def parse_predicted_labels_to_columns(predicted_labels_list: List[List[str]], hierarchy_levels: List[str] = None) -> List[Dict[str, Optional[str]]]:
    """
    Parses lists of potentially prefixed predicted labels into structured dictionaries.

    Args:
        predicted_labels_list: List of lists of predicted labels
        hierarchy_levels: List of hierarchy level names. If None, uses DEFAULT_HIERARCHY_LEVELS from config.

    Returns:
        List of dictionaries mapping hierarchy levels to their values
    """
    # Use provided hierarchy levels or default from config
    if hierarchy_levels is None:
        hierarchy_levels = config.DEFAULT_HIERARCHY_LEVELS.copy()

    structured_results = []
    # Create prefixes for each level (e.g., "theme:" for "Theme")
    prefixes = {level: f"{level.lower()}:" for level in hierarchy_levels}

    for labels in predicted_labels_list:
        row_dict: Dict[str, Optional[str]] = {level: None for level in hierarchy_levels}
        if not labels:
            structured_results.append(row_dict)
            continue

        # Use a dictionary to store the *first* label found for each level
        first_found_label = {level: None for level in hierarchy_levels}

        for label in labels:
            if not isinstance(label, str) or not label.strip():
                continue  # Skip non-strings or empty strings

            label_lower = label.lower()

            # Check if the label starts with any of the level prefixes
            for level in hierarchy_levels:
                prefix_lower = prefixes[level]
                if label_lower.startswith(prefix_lower):
                    # Extract value after "Level: "
                    value = label[len(level) + 2:].strip()
                    if value and first_found_label[level] is None:  # Only store the first one found
                        first_found_label[level] = value
                    break  # Move to next label once prefix match found

        # Assign the first found label for each level to the result dict
        for level in hierarchy_levels:
            row_dict[level] = first_found_label[level]

        structured_results.append(row_dict)

    return structured_results


def build_hierarchy_from_df(df: pd.DataFrame, hierarchy_levels: List[str] = None) -> Dict[str, Any]:
    """
    Converts a flat hierarchy DataFrame back into a nested dictionary structure.

    Args:
        df: DataFrame containing hierarchy data
        hierarchy_levels: List of hierarchy level names. If None, uses DEFAULT_HIERARCHY_LEVELS from config.

    Returns:
        Nested dictionary structure representing the hierarchy
    """
    if df is None or df.empty:
        return {}

    # Use provided hierarchy levels or default from config
    if hierarchy_levels is None:
        hierarchy_levels = config.DEFAULT_HIERARCHY_LEVELS.copy()

    # Ensure we have at least 1 level
    if len(hierarchy_levels) < 1:
        logger.error(f"Build Hierarchy Error: Need at least 1 hierarchy level, got {len(hierarchy_levels)}")
        return {}

    # The last level is always the leaf level with keywords
    leaf_level = hierarchy_levels[-1]
    keywords_key = "Keywords"

    # Check if all required columns exist in the DataFrame
    for level in hierarchy_levels:
        if level not in df.columns:
            logger.warning(f"Build Hierarchy Warning: Column '{level}' missing in input DataFrame. Creating empty column.")
            df[level] = ''

    if keywords_key not in df.columns:
        logger.warning(f"Build Hierarchy Warning: Column '{keywords_key}' missing in input DataFrame. Creating empty column.")
        df[keywords_key] = ''

    # Ensure only expected columns remain and fill NaN/astype string
    df_processed = df[hierarchy_levels + [keywords_key]].fillna('').astype(str)

    # Create a nested structure based on the hierarchy levels
    # Use the first level name as the root key (pluralized)
    root_level_name = hierarchy_levels[0].lower() + 's'

    # Initialize the result structure dynamically
    result = {root_level_name: []}

    # Group the data by all levels except the last one (leaf level)
    processed_rows, skipped_rows = 0, 0

    # Process each row to build the nested structure
    for _, row in df_processed.iterrows():
        # Extract values for each level
        level_values = [row[level].strip() for level in hierarchy_levels]

        # Skip row if any level is empty
        if not all(level_values):
            skipped_rows += 1
            continue

        # Extract keywords
        keywords_raw = row[keywords_key]
        keywords = [k.strip() for k in keywords_raw.split(',') if k.strip()]

        # Find or create the path in the nested structure
        current = result

        # Handle single level hierarchy differently
        if len(hierarchy_levels) == 1:
            # For single level, we just add directly to the root level
            level_value = level_values[0]

            # Check if this item already exists
            found = False
            for item in current[root_level_name]:
                if item['name'] == level_value:
                    current = item
                    found = True
                    break

            if not found:
                new_item = {'name': level_value, 'subsegments': []}
                current[root_level_name].append(new_item)
                current = new_item
        else:
            # Process all levels except the last one (leaf level)
            for i, level in enumerate(hierarchy_levels[:-1]):
                level_value = level_values[i]
                level_key = level.lower() + 's'

                # Find or create the current level in the structure
                found = False
                for item in current[level_key]:
                    if item['name'] == level_value:
                        current = item
                        found = True
                        break

                if not found:
                    new_item = {'name': level_value}
                    # Add the next level key
                    if i+1 < len(hierarchy_levels)-1:
                        next_level_key = hierarchy_levels[i+1].lower() + 's'
                    else:
                        next_level_key = 'subsegments'  # Last level before leaf
                    new_item[next_level_key] = []
                    current[level_key].append(new_item)
                    current = new_item

        # Process the leaf level
        leaf_value = level_values[-1]

        # For the last level, we always use 'subsegments' as the key for backward compatibility
        if 'subsegments' not in current:
            current['subsegments'] = []

        # Check if this subsegment already exists
        found = False
        for item in current['subsegments']:
            if item['name'] == leaf_value:
                found = True
                break

        if not found:
            current['subsegments'].append({
                'name': leaf_value,
                'keywords': keywords
            })

        processed_rows += 1

    if skipped_rows > 0:
        logger.info(f"Build Hierarchy: Skipped {skipped_rows} rows due to missing required values.")

    if not result[root_level_name] and processed_rows > 0:
        logger.warning("Build Hierarchy: Processed rows, but the resulting nested hierarchy is empty. Check input data structure.")
    elif not result[root_level_name]:
        logger.info("Build Hierarchy: Input DataFrame resulted in an empty hierarchy.")

    return result


def flatten_hierarchy(nested_hierarchy: Dict[str, Any], hierarchy_levels: List[str] = None) -> pd.DataFrame:
    """
    Converts AI-generated nested hierarchy dict to a flat DataFrame.

    Args:
        nested_hierarchy: Nested dictionary structure representing the hierarchy
        hierarchy_levels: List of hierarchy level names. If None, uses DEFAULT_HIERARCHY_LEVELS from config.

    Returns:
        DataFrame with flattened hierarchy
    """
    # Use provided hierarchy levels or default from config
    if hierarchy_levels is None:
        hierarchy_levels = config.DEFAULT_HIERARCHY_LEVELS.copy()

    # Ensure we have at least 1 level
    if len(hierarchy_levels) < 1:
        logger.error(f"Flatten Hierarchy Error: Need at least 1 hierarchy level, got {len(hierarchy_levels)}")
        return pd.DataFrame(columns=hierarchy_levels + ['Keywords'])

    rows = []
    required_cols = hierarchy_levels + ['Keywords']

    if not nested_hierarchy:
        logger.warning("Flatten Hierarchy: Input is None.")
        return pd.DataFrame(columns=required_cols)

    # Find the root level key dynamically
    root_level_key = None
    for key, value in nested_hierarchy.items():
        if isinstance(value, list) and value:
            root_level_key = key
            break

    if not root_level_key:
        logger.warning("Flatten Hierarchy: No valid root level found in nested hierarchy.")
        return pd.DataFrame(columns=required_cols)

    try:
        # Recursive function to traverse the nested hierarchy
        def traverse_hierarchy(node, path, level_index=0):
            # Special case for single level hierarchy
            if len(hierarchy_levels) == 1:
                # Process subsegments directly from the theme
                for subsegment in node.get('subsegments', []):
                    subseg_name = str(subsegment.get('name', '')).strip()
                    if not subseg_name:
                        continue

                    # Get keywords
                    keywords_list = [str(k).strip() for k in subsegment.get('keywords', []) if str(k).strip()]
                    keywords_str = ', '.join(keywords_list)

                    # Create a complete path with just the single level
                    full_path = path.copy()
                    full_path['Keywords'] = keywords_str

                    rows.append(full_path)
                return

            # If we've reached the leaf level (last level before keywords)
            if level_index == len(hierarchy_levels) - 1:
                # Process subsegments (leaf nodes)
                for subsegment in node.get('subsegments', []):
                    subseg_name = str(subsegment.get('name', '')).strip()
                    if not subseg_name:
                        continue

                    # Get keywords
                    keywords_list = [str(k).strip() for k in subsegment.get('keywords', []) if str(k).strip()]
                    keywords_str = ', '.join(keywords_list)

                    # Create a complete path including this leaf node
                    full_path = path.copy()
                    full_path[hierarchy_levels[level_index]] = subseg_name
                    full_path['Keywords'] = keywords_str

                    rows.append(full_path)
            else:
                # Get the key for the next level
                next_level = hierarchy_levels[level_index + 1]
                next_level_key = next_level.lower() + 's'

                # For the last non-leaf level, the next level key is always 'subsegments'
                if level_index == len(hierarchy_levels) - 2:
                    next_level_key = 'subsegments'

                # Process each item at this level
                for item in node.get(next_level_key, []):
                    item_name = str(item.get('name', '')).strip()
                    if not item_name:
                        continue

                    # Update the path with this level's value
                    new_path = path.copy()
                    new_path[hierarchy_levels[level_index + 1]] = item_name

                    # Recursively process the next level
                    traverse_hierarchy(item, new_path, level_index + 1)

        # Start traversal from the root level (dynamically determined)
        for root_item in nested_hierarchy.get(root_level_key, []):
            root_name = str(root_item.get('name', '')).strip()
            if not root_name:
                continue

            # Initialize path with the root item
            path = {level: '' for level in hierarchy_levels}
            path[hierarchy_levels[0]] = root_name

            # Traverse the hierarchy starting from this root item
            traverse_hierarchy(root_item, path)

    except Exception as e:
        logger.error(f"Error during hierarchy flattening: {e}", exc_info=True)
        return pd.DataFrame(columns=required_cols)  # Return empty on error

    return pd.DataFrame(rows, columns=required_cols)
