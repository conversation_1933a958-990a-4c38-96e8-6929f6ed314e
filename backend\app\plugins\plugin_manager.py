"""Plugin Manager for ClassyWeb Universal Platform."""

import logging
import asyncio
from typing import Dict, List, Any, Optional, Type
from datetime import datetime, timezone

from .plugin_registry import PluginRegistry
from .base_plugin import BasePlugin, PluginType, ClassificationPlugin, WorkflowPlugin
from ..database import SessionLocal
from ..models.plugin import Plugin as PluginModel

logger = logging.getLogger(__name__)


class PluginManager:
    """Manages plugin lifecycle, execution, and coordination."""
    
    def __init__(self, registry: PluginRegistry = None):
        self.registry = registry or PluginRegistry()
        self.active_plugins: Dict[str, BasePlugin] = {}
        self.plugin_health: Dict[str, Dict[str, Any]] = {}
        
    async def initialize(self):
        """Initialize the plugin manager and load plugins from database."""
        logger.info("Initializing Plugin Manager...")
        
        # Load plugins from database
        await self._load_plugins_from_db()
        
        # Initialize all active plugins
        await self.registry.initialize_all_plugins()
        
        # Start health monitoring
        await self._start_health_monitoring()
        
        logger.info(f"Plugin Manager initialized with {len(self.active_plugins)} active plugins")
    
    async def execute_classification(
        self,
        plugin_name: str,
        texts: List[str],
        hierarchy_config: Optional[Dict[str, Any]] = None,
        confidence_threshold: float = 0.5
    ) -> Dict[str, Any]:
        """Execute classification using a specific plugin."""
        try:
            plugin = self.registry.get_plugin(plugin_name)
            if not plugin:
                raise ValueError(f"Plugin {plugin_name} not found")
            
            if not isinstance(plugin, ClassificationPlugin):
                raise ValueError(f"Plugin {plugin_name} is not a classification plugin")
            
            if not plugin.is_initialized:
                success = await plugin.initialize()
                if not success:
                    raise RuntimeError(f"Failed to initialize plugin {plugin_name}")
            
            # Execute classification
            start_time = datetime.now(timezone.utc)
            results = await plugin.classify(texts, hierarchy_config, confidence_threshold)
            end_time = datetime.now(timezone.utc)
            
            # Update usage statistics
            await self._update_plugin_usage(plugin_name, True, end_time - start_time)
            
            return {
                "plugin_name": plugin_name,
                "results": results,
                "execution_time_ms": (end_time - start_time).total_seconds() * 1000,
                "timestamp": end_time.isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error executing classification with plugin {plugin_name}: {str(e)}")
            await self._update_plugin_usage(plugin_name, False)
            raise
    
    async def execute_workflow_step(
        self,
        plugin_name: str,
        step_config: Dict[str, Any],
        input_data: Any,
        context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Execute a workflow step using a specific plugin."""
        try:
            plugin = self.registry.get_plugin(plugin_name)
            if not plugin:
                raise ValueError(f"Plugin {plugin_name} not found")
            
            if not isinstance(plugin, WorkflowPlugin):
                raise ValueError(f"Plugin {plugin_name} is not a workflow plugin")
            
            if not plugin.is_initialized:
                success = await plugin.initialize()
                if not success:
                    raise RuntimeError(f"Failed to initialize plugin {plugin_name}")
            
            # Execute workflow step
            start_time = datetime.now(timezone.utc)
            result = await plugin.execute_step(step_config, input_data, context)
            end_time = datetime.now(timezone.utc)
            
            # Update usage statistics
            await self._update_plugin_usage(plugin_name, True, end_time - start_time)
            
            return {
                "plugin_name": plugin_name,
                "result": result,
                "execution_time_ms": (end_time - start_time).total_seconds() * 1000,
                "timestamp": end_time.isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error executing workflow step with plugin {plugin_name}: {str(e)}")
            await self._update_plugin_usage(plugin_name, False)
            raise
    
    async def get_available_plugins(
        self,
        plugin_type: Optional[PluginType] = None,
        enterprise_only: bool = False
    ) -> List[Dict[str, Any]]:
        """Get list of available plugins with filtering."""
        plugins = self.registry.list_plugins()
        
        filtered_plugins = []
        for name, plugin_info in plugins.items():
            metadata = plugin_info["metadata"]
            
            # Filter by type if specified
            if plugin_type and metadata["plugin_type"] != plugin_type.value:
                continue
            
            # Filter by enterprise if specified
            if enterprise_only and not metadata.get("enterprise_only", False):
                continue
            
            # Add health information
            plugin_info["health"] = self.plugin_health.get(name, {"status": "unknown"})
            
            filtered_plugins.append({
                "name": name,
                **plugin_info
            })
        
        return filtered_plugins
    
    async def install_plugin_from_marketplace(
        self,
        plugin_id: str,
        marketplace_url: str,
        config: Dict[str, Any] = None
    ) -> bool:
        """Install a plugin from the marketplace."""
        try:
            logger.info(f"Installing plugin {plugin_id} from marketplace")
            
            # TODO: Implement marketplace integration
            # This would involve:
            # 1. Download plugin package from marketplace
            # 2. Validate plugin signature and security
            # 3. Install dependencies
            # 4. Register plugin with configuration
            # 5. Initialize plugin
            
            # For now, return placeholder
            logger.warning("Marketplace integration not yet implemented")
            return False
            
        except Exception as e:
            logger.error(f"Error installing plugin {plugin_id}: {str(e)}")
            return False
    
    async def _load_plugins_from_db(self):
        """Load active plugins from database."""
        try:
            plugins_data = self.registry.load_plugins_from_db()
            logger.info(f"Loaded {len(plugins_data)} plugins from database")
            
            # TODO: Dynamically load plugin classes based on stored information
            # This would require a plugin discovery mechanism
            
        except Exception as e:
            logger.error(f"Error loading plugins from database: {str(e)}")
    
    async def _update_plugin_usage(
        self,
        plugin_name: str,
        success: bool,
        execution_time: Optional[datetime] = None
    ):
        """Update plugin usage statistics."""
        try:
            with SessionLocal() as db:
                plugin = db.query(PluginModel).filter(PluginModel.name == plugin_name).first()
                if plugin:
                    plugin.update_usage_stats(success)
                    db.commit()
                    
        except Exception as e:
            logger.error(f"Error updating plugin usage for {plugin_name}: {str(e)}")
    
    async def _start_health_monitoring(self):
        """Start background health monitoring for plugins."""
        logger.info("Starting plugin health monitoring...")
        
        async def monitor_health():
            while True:
                try:
                    for name, plugin in self.registry.plugins.items():
                        health_status = plugin.get_health_status()
                        self.plugin_health[name] = {
                            **health_status,
                            "last_check": datetime.now(timezone.utc).isoformat()
                        }
                    
                    # Sleep for 5 minutes between health checks
                    await asyncio.sleep(300)
                    
                except Exception as e:
                    logger.error(f"Error in plugin health monitoring: {str(e)}")
                    await asyncio.sleep(60)  # Shorter sleep on error
        
        # Start monitoring task in background
        asyncio.create_task(monitor_health())


# Global plugin manager instance
plugin_manager = PluginManager()
