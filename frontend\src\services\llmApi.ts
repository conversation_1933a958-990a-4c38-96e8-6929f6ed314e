// frontend/src/services/llmApi.ts
import apiClient from './apiClient';
import {
  ProviderListResponse,
  FetchModelsRequest,
  ModelListResponse,
  HierarchySuggestRequest,
  HierarchySuggestResponse,
  ClassifyLLMRequest,
  TaskStatus
} from '../types';

// Unified API endpoints
const ENDPOINTS = {
  providers: '/api/v2/classification/llm/providers',
  models: '/api/v2/classification/llm/models',
  hierarchySuggest: '/api/v2/classification/llm/hierarchy/suggest',
  llmClassify: '/api/v2/classification/llm/classify-file',
} as const;

/**
 * Fetches the list of available LLM providers
 * @returns ProviderListResponse with providers and their default configurations
 */
export const getLLMProviders = async (): Promise<ProviderListResponse> => {
  try {
    console.log(`Fetching LLM providers from: ${ENDPOINTS.providers}`);
    const response = await apiClient.get<ProviderListResponse>(ENDPOINTS.providers);
    return response.data;
  } catch (error: any) {
    console.error("Error fetching LLM providers:", error);
    throw error;
  }
};

/**
 * Fetches the list of available models for a given provider
 * @param requestData Provider, endpoint, and API key information
 * @returns ModelListResponse with available models
 */
export const fetchLLMModels = async (requestData: FetchModelsRequest): Promise<ModelListResponse> => {
  try {
    console.log(`Fetching LLM models from: ${ENDPOINTS.models}`);
    const response = await apiClient.post<ModelListResponse>(ENDPOINTS.models, requestData);
    return response.data;
  } catch (error: any) {
    console.error("Error fetching LLM models:", error);
    throw error;
  }
};

/**
 * Requests a hierarchy suggestion based on sample texts using the unified API
 * @param requestData Request data including sample texts and LLM configuration
 * @returns HierarchySuggestResponse with the suggested hierarchy or error
 */
export const suggestHierarchy = async (requestData: HierarchySuggestRequest): Promise<HierarchySuggestResponse> => {
  if (!requestData.llm_config) {
    console.error("LLM configuration is missing in suggestHierarchy request.");
    return { suggestion: null, error: "LLM configuration is required." };
  }

  try {
    console.log(`Requesting hierarchy suggestion from: ${ENDPOINTS.hierarchySuggest}`);
    console.log('Request data:', JSON.stringify(requestData, null, 2));

    const response = await apiClient.post<HierarchySuggestResponse>(ENDPOINTS.hierarchySuggest, requestData);

    if (response.data.error) {
      console.warn(`Hierarchy suggestion failed (backend reported): ${response.data.error}`);
    }

    return response.data;
  } catch (error: any) {
    console.error("Error fetching hierarchy suggestion:", error);
    const errorMessage = error?.detail || error?.message || 'Failed to fetch suggestion due to network or server error.';
    return { suggestion: null, error: errorMessage };
  }
};

/**
 * Starts an LLM classification task using the unified API
 * @param requestData Classification request data
 * @returns TaskStatus with the initial task status
 */
export const startLLMClassification = async (requestData: ClassifyLLMRequest): Promise<TaskStatus> => {
  // Validate required fields
  if (!requestData.file_id || !requestData.text_columns || requestData.text_columns.length === 0 || !requestData.hierarchy || !requestData.llm_config) {
    console.error("Missing required fields for LLM classification request:", requestData);
    throw new Error("Missing required fields to start LLM classification.");
  }

  try {
    console.log(`Starting LLM classification via: ${ENDPOINTS.llmClassify}`);
    console.log('LLM Classification Request:', JSON.stringify(requestData, null, 2));
    console.log('Hierarchy structure in request:', JSON.stringify(requestData.hierarchy, null, 2));

    // Wrap the request data in a 'request' field as expected by the unified backend
    const wrappedRequest = { request: requestData };
    const response = await apiClient.post<TaskStatus>(ENDPOINTS.llmClassify, wrappedRequest);

    console.log('LLM Classification Response:', response.data);
    return response.data;
  } catch (error: any) {
    console.error('LLM Classification Error:', error);
    if (error.response) {
      console.error('Error Response:', error.response.data);
    }
    throw error;
  }
};

/**
 * Get information about the current API configuration
 */
export const getAPIInfo = () => {
  return {
    version: 'unified-v2',
    basePath: '/api/v2/classification',
    endpoints: ENDPOINTS,
    description: 'Using unified classification API v2 with enhanced features'
  };
};
