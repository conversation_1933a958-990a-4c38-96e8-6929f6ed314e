# Core dependencies with pinned versions
pandas==2.2.0
langchain==0.1.0
langchain-groq==0.1.0
python-dotenv==1.0.0
openpyxl==3.1.2
langchain-community==0.0.13
transformers[torch]==4.36.2
scikit-learn==1.3.2
accelerate==0.25.0
pydantic==2.5.3
XlsxWriter==3.1.9
fastapi==0.109.0
uvicorn[standard]==0.25.0
langchain-openai==0.0.5
langchain-google-genai==0.0.6
sqlalchemy==2.0.25
alembic==1.13.1

# Advanced ML libraries for Phase 2
unsloth[cu121]==2024.1
torch>=2.1.0
datasets==2.16.1
peft==0.8.2
trl==0.7.10
bitsandbytes==0.42.0

# Phase 2 specific dependencies
psutil==5.9.6
GPUtil==1.4.0
matplotlib==3.8.2
seaborn==0.13.0
plotly==5.17.0
websockets==12.0
imbalanced-learn==0.11.0

# Authentication dependencies
python-jose[cryptography]==3.3.0
python-multipart==0.0.6
email-validator==2.1.0
authlib==1.2.1
requests==2.31.0
bcrypt==4.0.1
passlib==1.7.4
redis==5.0.1

# Security and performance
httpx==0.26.0
cryptography==41.0.7
certifi==2023.11.17
urllib3==2.1.0

# Development and testing
pytest==7.4.3
pytest-asyncio==0.23.2
pytest-cov==4.1.0
black==23.12.0
isort==5.13.2
mypy==1.8.0