/**
 * Phase 3 Demo Component for ClassyWeb ML Platform
 * 
 * This component demonstrates all Phase 3 backend integration features:
 * - API v2 integration with classification engine service
 * - Real-time WebSocket monitoring with training progress
 * - Enhanced workflow management
 * - Comprehensive error handling and recovery
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Play, 
  Square, 
  RefreshCw, 
  Zap, 
  Activity, 
  CheckCircle, 
  AlertTriangle,
  Settings,
  BarChart3
} from 'lucide-react';

// Import Phase 3 services and components
import { 
  ClassificationType, 
  TrainingMethod,
  getEnginesForType,
  startTrainingV2,
  getEngineRecommendations,
  validateTrainingConfig
} from '@/services/classificationEngineService';
import { useTrainingMonitor } from '@/hooks/useTrainingMonitor';
import { EnhancedTrainingMonitor } from '@/components/training/EnhancedTrainingMonitor';
import { RealTimeProgressBar } from '@/components/training/RealTimeProgressBar';
import { enhancedWorkflowService, WorkflowStatus } from '@/services/enhancedWorkflowService';
import { errorHandlingService } from '@/services/errorHandlingService';

interface Phase3DemoProps {
  fileId?: string;
  onComplete?: (results: any) => void;
}

export const Phase3Demo: React.FC<Phase3DemoProps> = ({
  fileId = 'demo_file_123',
  onComplete
}) => {
  // State management
  const [selectedTab, setSelectedTab] = useState('engines');
  const [engines, setEngines] = useState<any[]>([]);
  const [recommendations, setRecommendations] = useState<any[]>([]);
  const [workflowSession, setWorkflowSession] = useState<any>(null);
  const [trainingSessionId, setTrainingSessionId] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string>('');
  const [demoConfig, setDemoConfig] = useState({
    classificationType: ClassificationType.BINARY,
    trainingMethod: TrainingMethod.CUSTOM,
    textColumn: 'text',
    labelColumns: ['label']
  });

  // Training monitor hook
  const {
    isConnected,
    progress,
    metrics,
    systemMetrics,
    events,
    isTraining,
    isCompleted,
    progressPercentage
  } = useTrainingMonitor(trainingSessionId, {
    autoConnect: !!trainingSessionId,
    onComplete: () => {
      console.log('Training completed!');
      onComplete?.({ status: 'completed', progress, metrics });
    },
    onError: (error) => {
      setError(error.message);
    }
  });

  // Load engines on mount
  useEffect(() => {
    loadEngines();
    loadRecommendations();
  }, [demoConfig.classificationType]);

  // Load available engines
  const loadEngines = async () => {
    try {
      setIsLoading(true);
      const engineList = await getEnginesForType(demoConfig.classificationType);
      setEngines(engineList);
    } catch (err) {
      setError(`Failed to load engines: ${err}`);
    } finally {
      setIsLoading(false);
    }
  };

  // Load engine recommendations
  const loadRecommendations = async () => {
    try {
      const recs = await getEngineRecommendations(fileId, demoConfig.classificationType);
      setRecommendations(recs);
    } catch (err) {
      console.warn('Failed to load recommendations:', err);
    }
  };

  // Create workflow session
  const createWorkflowSession = async () => {
    try {
      setIsLoading(true);
      const session = await enhancedWorkflowService.createWorkflowSession(
        demoConfig.classificationType,
        demoConfig.trainingMethod,
        fileId,
        {
          userType: 'expert',
          enableRealTimeMonitoring: true,
          autoAdvance: false
        }
      );
      setWorkflowSession(session);
      setError('');
    } catch (err) {
      setError(`Failed to create workflow: ${err}`);
    } finally {
      setIsLoading(false);
    }
  };

  // Start training demonstration
  const startTrainingDemo = async () => {
    try {
      setIsLoading(true);
      setError('');

      // Create workflow if not exists
      if (!workflowSession) {
        await createWorkflowSession();
        return;
      }

      // Configure training
      const trainingConfig = {
        file_id: fileId,
        classification_type: demoConfig.classificationType,
        training_method: demoConfig.trainingMethod,
        text_column: demoConfig.textColumn,
        label_columns: demoConfig.labelColumns,
        training_params: {
          num_epochs: 10,
          batch_size: 32,
          learning_rate: 0.001,
          validation_split: 0.2
        }
      };

      // Validate configuration
      const validation = await validateTrainingConfig(trainingConfig);
      if (!validation.valid) {
        setError(`Configuration invalid: ${validation.errors.join(', ')}`);
        return;
      }

      // Start training
      const response = await startTrainingV2(trainingConfig);
      setTrainingSessionId(response.session_id);

      // Update workflow session
      enhancedWorkflowService.updateWorkflowSession(workflowSession.id, {
        status: WorkflowStatus.RUNNING
      });

    } catch (err) {
      setError(`Failed to start training: ${err}`);
    } finally {
      setIsLoading(false);
    }
  };

  // Stop training
  const stopTraining = () => {
    if (workflowSession) {
      enhancedWorkflowService.cancelWorkflow(workflowSession.id);
      setTrainingSessionId('');
    }
  };

  // Reset demo
  const resetDemo = () => {
    setWorkflowSession(null);
    setTrainingSessionId('');
    setError('');
    errorHandlingService.clearErrors();
  };

  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Phase 3 Integration Demo</h1>
          <p className="text-gray-600">
            Demonstrating API v2, WebSocket monitoring, and enhanced workflows
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={resetDemo}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Reset Demo
          </Button>
          {!isTraining ? (
            <Button onClick={startTrainingDemo} disabled={isLoading}>
              <Play className="h-4 w-4 mr-2" />
              {isLoading ? 'Starting...' : 'Start Training'}
            </Button>
          ) : (
            <Button variant="destructive" onClick={stopTraining}>
              <Square className="h-4 w-4 mr-2" />
              Stop Training
            </Button>
          )}
        </div>
      </div>

      {/* Error Alert */}
      {error && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Status Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4 flex items-center gap-3">
            <Activity className="h-8 w-8 text-blue-500" />
            <div>
              <div className="text-sm font-medium text-gray-600">WebSocket</div>
              <div className="text-lg font-bold">
                {isConnected ? 'Connected' : 'Disconnected'}
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4 flex items-center gap-3">
            <Settings className="h-8 w-8 text-green-500" />
            <div>
              <div className="text-sm font-medium text-gray-600">Workflow</div>
              <div className="text-lg font-bold">
                {workflowSession?.status || 'Not Created'}
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4 flex items-center gap-3">
            <Zap className="h-8 w-8 text-purple-500" />
            <div>
              <div className="text-sm font-medium text-gray-600">Training</div>
              <div className="text-lg font-bold">
                {isTraining ? 'Active' : isCompleted ? 'Completed' : 'Idle'}
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4 flex items-center gap-3">
            <BarChart3 className="h-8 w-8 text-orange-500" />
            <div>
              <div className="text-sm font-medium text-gray-600">Progress</div>
              <div className="text-lg font-bold">
                {progressPercentage.toFixed(1)}%
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Real-time Progress */}
      {trainingSessionId && (
        <RealTimeProgressBar
          sessionId={trainingSessionId}
          showStopButton={false}
          showMetrics={true}
          onComplete={() => console.log('Training completed via progress bar')}
        />
      )}

      {/* Main Content Tabs */}
      <Tabs value={selectedTab} onValueChange={setSelectedTab}>
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="engines">Engines</TabsTrigger>
          <TabsTrigger value="recommendations">Recommendations</TabsTrigger>
          <TabsTrigger value="workflow">Workflow</TabsTrigger>
          <TabsTrigger value="monitoring">Monitoring</TabsTrigger>
          <TabsTrigger value="errors">Error Handling</TabsTrigger>
        </TabsList>

        <TabsContent value="engines" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Available Classification Engines</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {engines.map((engine) => (
                  <Card key={engine.id} className="border-2">
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between mb-2">
                        <h3 className="font-semibold">{engine.name}</h3>
                        <Badge variant="outline">{engine.type}</Badge>
                      </div>
                      <p className="text-sm text-gray-600 mb-3">{engine.description}</p>
                      <div className="flex flex-wrap gap-1">
                        {engine.supported_methods.map((method: string) => (
                          <Badge key={method} variant="secondary" className="text-xs">
                            {method}
                          </Badge>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="recommendations" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Engine Recommendations</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recommendations.map((rec, index) => (
                  <Card key={index} className="border-l-4 border-l-blue-500">
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between mb-2">
                        <h3 className="font-semibold">Engine: {rec.engine_id}</h3>
                        <Badge variant="outline">
                          {(rec.confidence * 100).toFixed(1)}% confidence
                        </Badge>
                      </div>
                      <p className="text-sm text-gray-600 mb-2">{rec.reasoning}</p>
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <span className="font-medium">Expected Accuracy:</span>
                          <span className="ml-2">
                            {(rec.estimated_performance.accuracy * 100).toFixed(1)}%
                          </span>
                        </div>
                        <div>
                          <span className="font-medium">Training Time:</span>
                          <span className="ml-2">
                            {rec.estimated_performance.training_time}s
                          </span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="workflow" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Workflow Management</CardTitle>
            </CardHeader>
            <CardContent>
              {workflowSession ? (
                <div className="space-y-4">
                  <div className="flex items-center gap-2">
                    <Badge variant="outline">ID: {workflowSession.id.slice(-8)}</Badge>
                    <Badge className={
                      workflowSession.status === WorkflowStatus.RUNNING ? 'bg-green-500' :
                      workflowSession.status === WorkflowStatus.COMPLETED ? 'bg-blue-500' :
                      'bg-gray-500'
                    }>
                      {workflowSession.status}
                    </Badge>
                  </div>
                  
                  <div className="space-y-2">
                    <h4 className="font-medium">Workflow Steps:</h4>
                    {workflowSession.steps.map((step: any, index: number) => (
                      <div key={step.id} className="flex items-center gap-3 p-2 bg-gray-50 rounded">
                        <div className={`w-6 h-6 rounded-full flex items-center justify-center text-xs ${
                          step.status === 'completed' ? 'bg-green-500 text-white' :
                          step.status === 'in_progress' ? 'bg-blue-500 text-white' :
                          'bg-gray-300'
                        }`}>
                          {step.status === 'completed' ? <CheckCircle className="h-3 w-3" /> : index + 1}
                        </div>
                        <div className="flex-1">
                          <div className="font-medium">{step.name}</div>
                          <div className="text-sm text-gray-600">{step.description}</div>
                        </div>
                        <div className="text-sm font-medium">
                          {step.progress}%
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              ) : (
                <div className="text-center py-8">
                  <p className="text-gray-600 mb-4">No workflow session created</p>
                  <Button onClick={createWorkflowSession} disabled={isLoading}>
                    Create Workflow Session
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="monitoring" className="space-y-4">
          {trainingSessionId ? (
            <EnhancedTrainingMonitor
              sessionId={trainingSessionId}
              showSystemMetrics={true}
              showAdvancedMetrics={true}
              onComplete={() => console.log('Enhanced monitor: Training completed')}
            />
          ) : (
            <Card>
              <CardContent className="p-8 text-center">
                <p className="text-gray-600">Start training to see real-time monitoring</p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="errors" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Error Handling & Recovery</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Card className="border-2">
                    <CardContent className="p-4 text-center">
                      <div className="text-2xl font-bold text-green-600">
                        {errorHandlingService.getAllErrors().filter(e => e.type === 'network_error').length}
                      </div>
                      <div className="text-sm text-gray-600">Network Errors</div>
                    </CardContent>
                  </Card>
                  <Card className="border-2">
                    <CardContent className="p-4 text-center">
                      <div className="text-2xl font-bold text-blue-600">
                        {errorHandlingService.getAllErrors().filter(e => e.type === 'websocket_error').length}
                      </div>
                      <div className="text-sm text-gray-600">WebSocket Errors</div>
                    </CardContent>
                  </Card>
                  <Card className="border-2">
                    <CardContent className="p-4 text-center">
                      <div className="text-2xl font-bold text-purple-600">
                        {errorHandlingService.getAllErrors().filter(e => e.type === 'api_error').length}
                      </div>
                      <div className="text-sm text-gray-600">API Errors</div>
                    </CardContent>
                  </Card>
                </div>

                <div className="space-y-2">
                  <h4 className="font-medium">Recent Errors:</h4>
                  {errorHandlingService.getAllErrors().slice(-5).map((error) => (
                    <div key={error.id} className="p-3 bg-red-50 border border-red-200 rounded">
                      <div className="flex items-center justify-between">
                        <Badge variant="destructive">{error.type}</Badge>
                        <span className="text-xs text-gray-500">
                          {error.timestamp.toLocaleTimeString()}
                        </span>
                      </div>
                      <p className="text-sm mt-1">{error.message}</p>
                    </div>
                  ))}
                  {errorHandlingService.getAllErrors().length === 0 && (
                    <p className="text-gray-600 text-center py-4">No errors recorded</p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};
