import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Target } from "lucide-react";

export const HeroSection = () => {
  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden bg-background">
      <div className="absolute inset-0 bg-ml-primary/5" />
      
      <div className="relative z-10 max-w-7xl mx-auto px-6 py-20 text-center">
        <div className="mb-6 flex justify-center">
          <div className="bg-card border-2 border-border rounded-full px-6 py-2">
            <div className="flex items-center gap-2 text-sm font-medium text-foreground">
              <Zap className="w-4 h-4 text-ml-secondary" />
              <span>Powered by Advanced ML & LLMs</span>
            </div>
          </div>
        </div>
        
        <h1 className="text-6xl md:text-7xl font-bold mb-6 text-foreground">
          ClassyWeb
        </h1>

        <p className="text-xl md:text-2xl mb-4 text-foreground max-w-3xl mx-auto leading-relaxed">
          The comprehensive machine learning platform for
        </p>

        <h2 className="text-3xl md:text-4xl font-bold mb-8 text-ml-primary">
          Advanced Classification Tasks
        </h2>

        <p className="text-lg mb-12 text-muted-foreground max-w-2xl mx-auto">
          Train custom models with GPU acceleration or leverage powerful LLMs.
          From binary to hierarchical classification - we've got you covered.
        </p>
        
        <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-16">
          <Button variant="hero" size="lg" className="group" asChild>
            <a href="/get-started">
              Get Started
              <ArrowRight className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" />
            </a>
          </Button>
          <Button variant="outline-hero" size="lg" asChild>
            <a href="/dashboard">View Demo</a>
          </Button>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
          <div className="bg-card border-2 border-border rounded-2xl p-6 hover:shadow-card transition-all duration-300">
            <Brain className="w-8 h-8 text-ml-secondary mb-4 mx-auto" />
            <h3 className="text-lg font-semibold mb-2 text-foreground">Custom Models</h3>
            <p className="text-muted-foreground text-sm">Train with Unsloth GPU acceleration</p>
          </div>

          <div className="bg-card border-2 border-border rounded-2xl p-6 hover:shadow-card transition-all duration-300">
            <Target className="w-8 h-8 text-ml-accent mb-4 mx-auto" />
            <h3 className="text-lg font-semibold mb-2 text-foreground">5 Classification Types</h3>
            <p className="text-muted-foreground text-sm">From binary to hierarchical classification</p>
          </div>

          <div className="bg-card border-2 border-border rounded-2xl p-6 hover:shadow-card transition-all duration-300">
            <Zap className="w-8 h-8 text-ml-primary mb-4 mx-auto" />
            <h3 className="text-lg font-semibold mb-2 text-foreground">LLM Integration</h3>
            <p className="text-muted-foreground text-sm">Groq, OpenAI, Gemini, and more</p>
          </div>
        </div>
      </div>
    </section>
  );
};
