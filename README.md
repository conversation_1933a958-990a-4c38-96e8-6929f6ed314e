# ClassyWeb - Secure AI Text Classification Application

ClassyWeb is a modern, secure web application for classifying text data using both Large Language Models (LLMs) and custom-trained Hugging Face transformer models. It features a React+TypeScript frontend and a FastAPI Python backend with database integration, designed with security, maintainability, and extensibility in mind.

## Features

- **Data Processing**: Upload and process CSV/Excel files with automatic column detection
- **Hierarchical Classification**: Define and use multi-level classification structures
- **Multiple LLM Providers**: Classify text using various LLM providers:
  - Groq
  - OpenAI
  - Google Gemini
  - Ollama (local models)
  - OpenRouter
- **Custom Model Training**: Train and use custom Hugging Face transformer models
- **Results Management**: Download classification results as CSV/Excel
- **Secure Authentication**:
  - JWT-based authentication with refresh tokens
  - Google OAuth integration
  - Email/password with secure password hashing
  - Token blacklisting and rotation
  - Protection against common attacks (CSRF, XSS, etc.)
- **Responsive UI**: Modern, responsive interface that works on desktop and mobile
- **Database Integration**: SQLite database (configurable for PostgreSQL/MySQL)
- **Security Features**:
  - Parameterized database queries to prevent SQL injection
  - Secure file handling with path validation
  - CORS protection
  - Rate limiting for sensitive endpoints
  - Comprehensive input validation

## Getting Started

### Prerequisites

- Python 3.8+
- Node.js 16+
- npm or yarn

### Installation

1. Clone the repository:
   ```
   git clone https://github.com/yourusername/classyweb.git
   cd classyweb
   ```

2. Set up the backend:
   ```
   cd backend
   pip install -r requirements.txt
   ```

3. Set up the frontend:
   ```
   cd frontend
   npm install
   ```

4. Create a `.env` file in the backend directory with your API keys and configuration:
   ```
   # LLM API Keys
   GROQ_API_KEY=your_groq_api_key
   OPENAI_API_KEY=your_openai_api_key
   GEMINI_API_KEY=your_gemini_api_key
   OPENROUTER_API_KEY=your_openrouter_api_key

   # Authentication Configuration
   JWT_SECRET_KEY=your_secret_key_for_jwt_tokens
   GOOGLE_CLIENT_ID=your_google_client_id
   GOOGLE_CLIENT_SECRET=your_google_client_secret
   GOOGLE_REDIRECT_URI=http://localhost:5174/auth/google/callback

   # Database Configuration (optional)
   # DATABASE_URL=postgresql://username:password@localhost:5433/classyweb
   # DATABASE_ECHO=false
   ```

5. Initialize the database:
   ```
   cd backend
   python init_db.py create
   ```

   Or on Windows, run the `initialize_db.bat` script.

### Running the Application

1. Start the backend server:
   ```
   cd backend
   python server.py --reload
   ```

   You can customize the server settings with additional options:
   ```
   python server.py --host 0.0.0.0 --port 8001 --reload --workers 4 --log-level debug
   ```

2. Start the frontend development server:
   ```
   cd frontend
   npm run dev
   ```

3. Open your browser and navigate to `http://localhost:5174`

4. Register a new account or use Google Sign-In to access the application

## Troubleshooting

### Database Issues

If you encounter database errors (e.g., "users table doesn't exist"), you can reset the database:

1. Run the reset script:
   ```
   cd backend
   python init_db.py reset
   ```

   Or on Windows, double-click the `reset_db.bat` file.

2. Restart the backend server.

### Authentication Issues

If you're experiencing authentication issues:

1. Make sure the database is properly initialized (see above)
2. Check that your Google OAuth credentials are correctly configured in the `.env` file
3. Ensure your system clock is synchronized (important for Google OAuth)
4. See the `AUTH_TROUBLESHOOTING.md` file for detailed troubleshooting steps

### API Key Issues

If you're having trouble with LLM providers:

1. Verify that your API keys are correctly set in the `.env` file
2. For Ollama, ensure the Ollama server is running locally
3. Check the backend logs for any API-related errors

## User Journey

1. **Authentication**: Sign in with Google or register with email/password
2. **Data Setup**: Upload your data file (CSV/Excel) and select the text column to classify
3. **Hierarchy Definition**: Define your classification hierarchy (themes, categories, segments, etc.)
4. **LLM Configuration**: Configure your LLM provider and model
5. **Classification**: Run the classification process and view the results
6. **Download**: Download the classification results as CSV/Excel

## Development

### Project Structure

```
classyweb/
├── backend/              # Backend directory
│   ├── app/              # Main application package
│   │   ├── __init__.py
│   │   ├── main.py       # FastAPI application
│   │   ├── config.py     # Application configuration with Pydantic validation
│   │   ├── database.py   # Database models and utilities with security features
│   │   ├── models/       # Pydantic models for request/response validation
│   │   ├── api/          # API routes with input validation
│   │   ├── tasks/        # Background tasks for long-running operations
│   │   ├── utils/        # Utility functions
│   │   ├── auth/         # Enhanced authentication with token security
│   │   │   ├── __init__.py  # Core authentication utilities
│   │   │   └── cookies.py   # Secure cookie handling
│   │   ├── redis_client.py  # Redis client for token blacklisting
│   │   ├── cache.py         # Caching utilities
│   │   ├── retry.py         # Retry utilities for resilience
│   │   ├── llm_classifier.py # LLM classification logic
│   │   └── hf_classifier.py  # HF classification logic
│   └── server.py         # Server script to run the application
├── frontend/             # React+TypeScript frontend
│   ├── src/              # Source code
│   │   ├── components/   # Reusable UI components
│   │   ├── features/     # Feature-specific components
│   │   ├── services/     # API service functions
│   │   ├── store/        # State management
│   │   ├── styles/       # Global styles
│   │   └── types/        # TypeScript type definitions
│   ├── public/           # Static assets
│   └── index.html        # HTML entry point
├── initialize_db.bat     # Database initialization script
├── reset_db.bat          # Database reset script
└── .env                  # Environment variables
```

### Adding New Features

1. **Backend**: Add new endpoints in the appropriate file in `backend/app/api/`
2. **Frontend**: Add new components in `frontend/src/components` or `frontend/src/features`
3. **API Integration**: Add new API service functions in `frontend/src/services`

## Deployment

### Backend Deployment

1. Install production dependencies:
   ```
   pip install uvicorn gunicorn
   ```

2. Run with the server script (for production):
   ```
   cd backend
   python server.py --host 0.0.0.0 --port 8001 --workers 4 --log-level warning
   ```

3. Alternatively, run with Gunicorn (for production):
   ```
   cd backend
   gunicorn -w 4 -k uvicorn.workers.UvicornWorker app.main:app
   ```

### Frontend Deployment

1. Build the frontend:
   ```
   cd frontend
   npm run build
   ```

2. Serve the built files with a web server like Nginx or Apache

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Security Best Practices

ClassyWeb implements the following security best practices:

### Authentication & Authorization
- JWT tokens with secure algorithms and proper validation
- Refresh token rotation to prevent token reuse
- Token blacklisting for revoked tokens
- HttpOnly cookies for refresh tokens
- CSRF protection
- Secure password hashing with bcrypt

### Data Protection
- Input validation on all API endpoints
- Parameterized queries to prevent SQL injection
- Secure file handling with path validation
- Content Security Policy headers
- Rate limiting for sensitive endpoints

### Infrastructure Security
- Database connection pooling with security settings
- Secure environment variable handling
- Dependency pinning to prevent supply chain attacks
- Comprehensive error logging without sensitive data exposure

## Acknowledgements

- [FastAPI](https://fastapi.tiangolo.com/) - Backend framework
- [React](https://reactjs.org/) - Frontend library
- [Material-UI](https://mui.com/) - UI component library
- [Hugging Face Transformers](https://huggingface.co/transformers/) - ML models
- [SQLAlchemy](https://www.sqlalchemy.org/) - Database ORM
- [Pydantic](https://pydantic-docs.helpmanual.io/) - Data validation
- [Python-Jose](https://python-jose.readthedocs.io/) - JWT implementation
