"""
Cookie management utilities for authentication.
"""
import logging
from datetime import datetime, timedelta
from typing import Optional

from fastapi import Response, Request
from starlette.datastructures import MutableHeaders

from .. import config

logger = logging.getLogger(__name__)

# <PERSON>ie names
REFRESH_TOKEN_COOKIE_NAME = "refresh_token"

def set_refresh_token_cookie(response: Response, token: str, expires_delta: Optional[int] = None) -> None:
    """
    Set a refresh token cookie.
    
    Args:
        response: FastAPI response object
        token: Refresh token
        expires_delta: Expiration time in seconds (default: REFRESH_TOKEN_EXPIRE_DAYS)
    """
    if expires_delta is None:
        expires_delta = config.REFRESH_TOKEN_EXPIRE_DAYS * 24 * 60 * 60  # Convert days to seconds
    
    expires = datetime.utcnow() + timedelta(seconds=expires_delta)
    
    # Set secure cookie attributes
    response.set_cookie(
        key=REFRESH_TOKEN_COOKIE_NAME,
        value=token,
        httponly=True,  # Not accessible via JavaScript
        secure=not config.DEBUG,  # Secure in production, allow HTTP in development
        samesite="lax",  # Protects against CSRF while allowing normal navigation
        expires=expires.strftime("%a, %d %b %Y %H:%M:%S GMT"),
        max_age=expires_delta,
        path="/auth",  # Restrict to auth endpoints only
    )
    logger.debug(f"Set refresh token cookie, expires in {expires_delta} seconds")

def get_refresh_token_from_cookies(request: Request) -> Optional[str]:
    """
    Get refresh token from cookies.
    
    Args:
        request: FastAPI request object
        
    Returns:
        Refresh token or None
    """
    return request.cookies.get(REFRESH_TOKEN_COOKIE_NAME)

def clear_refresh_token_cookie(response: Response) -> None:
    """
    Clear the refresh token cookie.
    
    Args:
        response: FastAPI response object
    """
    response.delete_cookie(
        key=REFRESH_TOKEN_COOKIE_NAME,
        httponly=True,
        secure=not config.DEBUG,
        samesite="lax",
        path="/auth",
    )
    logger.debug("Cleared refresh token cookie")
