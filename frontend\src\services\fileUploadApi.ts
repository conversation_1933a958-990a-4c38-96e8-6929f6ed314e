// frontend/src/services/fileUploadApi.ts
// API service for file upload and management

import apiClient from './apiClient';

export interface UploadedFile {
  file_id: string;
  filename: string;
  columns: string[];
  num_rows: number;
  preview: Record<string, any>[];
}

export interface FileUploadProgress {
  uploaded: number;
  total: number;
  percentage: number;
  status: 'uploading' | 'processing' | 'complete' | 'error';
  message?: string;
}

/**
 * Upload a file to the server
 */
export const uploadFile = async (
  file: File,
  onProgress?: (progress: FileUploadProgress) => void
): Promise<UploadedFile> => {
  const formData = new FormData();
  formData.append('file', file);

  try {
    const response = await apiClient.post('/files/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress: (progressEvent) => {
        if (onProgress && progressEvent.total) {
          const percentage = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          onProgress({
            uploaded: progressEvent.loaded,
            total: progressEvent.total,
            percentage,
            status: percentage === 100 ? 'processing' : 'uploading',
            message: percentage === 100 ? 'Processing file...' : 'Uploading...'
          });
        }
      },
    });

    return response.data;
  } catch (error: any) {
    console.error('Error uploading file:', error);
    throw new Error(error.response?.data?.detail || 'Failed to upload file');
  }
};

/**
 * Get file information by ID
 */
export const getFileInfo = async (fileId: string): Promise<UploadedFile> => {
  try {
    const response = await apiClient.get(`/files/${fileId}`);
    return response.data;
  } catch (error: any) {
    console.error('Error getting file info:', error);
    throw new Error(error.response?.data?.detail || 'Failed to get file information');
  }
};

/**
 * Delete a file
 */
export const deleteFile = async (fileId: string): Promise<void> => {
  try {
    await apiClient.delete(`/files/${fileId}`);
  } catch (error: any) {
    console.error('Error deleting file:', error);
    throw new Error(error.response?.data?.detail || 'Failed to delete file');
  }
};

/**
 * Get file preview data
 */
export const getFilePreview = async (
  fileId: string,
  rows: number = 10
): Promise<{
  columns: string[];
  data: Record<string, any>[];
  total_rows: number;
  total_columns: number;
}> => {
  try {
    const response = await apiClient.get(`/files/${fileId}/preview`, {
      params: { rows }
    });
    return response.data;
  } catch (error: any) {
    console.error('Error getting file preview:', error);
    throw new Error(error.response?.data?.detail || 'Failed to get file preview');
  }
};

/**
 * Validate file format and structure
 */
export const validateFile = async (fileId: string): Promise<{
  is_valid: boolean;
  errors: string[];
  warnings: string[];
  suggestions: string[];
  file_info: {
    format: string;
    encoding: string;
    delimiter?: string;
    has_header: boolean;
    total_rows: number;
    total_columns: number;
  };
}> => {
  try {
    const response = await apiClient.post(`/files/${fileId}/validate`);
    return response.data;
  } catch (error: any) {
    console.error('Error validating file:', error);
    throw new Error(error.response?.data?.detail || 'Failed to validate file');
  }
};

/**
 * Get column analysis for a file
 */
export const analyzeFileColumns = async (fileId: string): Promise<{
  columns: Record<string, {
    name: string;
    type: string;
    unique_count: number;
    null_count: number;
    sample_values: any[];
    is_potential_text: boolean;
    is_potential_label: boolean;
    statistics?: {
      mean?: number;
      median?: number;
      std?: number;
      min?: any;
      max?: any;
    };
  }>;
  recommendations: {
    text_columns: string[];
    label_columns: string[];
    id_columns: string[];
    numeric_columns: string[];
  };
}> => {
  try {
    const response = await apiClient.post(`/files/${fileId}/analyze-columns`);
    return response.data;
  } catch (error: any) {
    console.error('Error analyzing file columns:', error);
    throw new Error(error.response?.data?.detail || 'Failed to analyze file columns');
  }
};

/**
 * Get detailed file analysis including column information for hierarchical classification
 */
export const analyzeFileStructure = async (fileId: string, textColumn?: string): Promise<{
  detected_structure: string;
  confidence: number;
  suggestions: any[];
  preview: any;
  column_analysis: Record<string, any>;
  data_quality: any;
}> => {
  try {
    const response = await apiClient.post(`/files/${fileId}/analyze`, {
      text_column: textColumn
    });
    return response.data;
  } catch (error: any) {
    console.error('Error analyzing file structure:', error);
    throw new Error(error.response?.data?.detail || 'Failed to analyze file structure');
  }
};

/**
 * Process file for machine learning (extract features, clean data, etc.)
 */
export const processFileForML = async (
  fileId: string,
  config: {
    text_column: string;
    label_columns: string[];
    remove_duplicates?: boolean;
    handle_missing_values?: 'drop' | 'fill' | 'ignore';
    encoding?: string;
  }
): Promise<{
  processed_file_id: string;
  processing_stats: {
    original_rows: number;
    processed_rows: number;
    removed_duplicates: number;
    handled_missing: number;
    text_samples: string[];
    label_distribution: Record<string, number>;
  };
  warnings: string[];
  recommendations: string[];
}> => {
  try {
    const response = await apiClient.post(`/files/${fileId}/process-for-ml`, config);
    return response.data;
  } catch (error: any) {
    console.error('Error processing file for ML:', error);
    throw new Error(error.response?.data?.detail || 'Failed to process file for machine learning');
  }
};
