// frontend/src/services/exportApi.ts
// API service for exporting classification results

import apiClient from './apiClient';

/**
 * Download classification results as CSV file
 */
export const downloadResultsCSV = async (taskId: string): Promise<void> => {
  try {
    const response = await apiClient.get(`/results/${taskId}/excel`, {
      responseType: 'blob',
    });

    // Create blob URL and trigger download
    const blob = new Blob([response.data], { 
      type: 'text/csv' 
    });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `classification_results_${taskId}.csv`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  } catch (error: any) {
    console.error('Error downloading CSV:', error);
    throw new Error(error.response?.data?.detail || 'Failed to download CSV file');
  }
};

/**
 * Download classification results as Excel file
 */
export const downloadResultsExcel = async (taskId: string): Promise<void> => {
  try {
    const response = await apiClient.get(`/results/${taskId}/excel`, {
      responseType: 'blob',
    });

    // Create blob URL and trigger download
    const blob = new Blob([response.data], { 
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
    });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `classification_results_${taskId}.xlsx`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  } catch (error: any) {
    console.error('Error downloading Excel:', error);
    throw new Error(error.response?.data?.detail || 'Failed to download Excel file');
  }
};

/**
 * Download classification results using the generic download endpoint
 */
export const downloadResults = async (taskId: string, filename?: string): Promise<void> => {
  try {
    const response = await apiClient.get(`/results/${taskId}/download`, {
      responseType: 'blob',
    });

    // Get filename from response headers or use default
    const contentDisposition = response.headers['content-disposition'];
    let downloadFilename = filename || `classification_results_${taskId}.csv`;
    
    if (contentDisposition) {
      const filenameMatch = contentDisposition.match(/filename="?([^"]+)"?/);
      if (filenameMatch) {
        downloadFilename = filenameMatch[1];
      }
    }

    // Create blob URL and trigger download
    const blob = new Blob([response.data]);
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = downloadFilename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  } catch (error: any) {
    console.error('Error downloading results:', error);
    throw new Error(error.response?.data?.detail || 'Failed to download results file');
  }
};

/**
 * Export results data as JSON (client-side)
 */
export const exportResultsJSON = (data: Record<string, any>[], filename?: string): void => {
  try {
    const jsonString = JSON.stringify(data, null, 2);
    const blob = new Blob([jsonString], { type: 'application/json' });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename || 'classification_results.json';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  } catch (error: any) {
    console.error('Error exporting JSON:', error);
    throw new Error('Failed to export JSON file');
  }
};

/**
 * Download hierarchical classification results with enhanced formatting
 */
export const downloadHierarchicalResults = async (
  taskId: string,
  format: 'json' | 'csv' | 'excel' = 'json',
  options: {
    includeTree?: boolean;
    includeViolations?: boolean;
  } = {}
): Promise<any> => {
  try {
    const params = new URLSearchParams({
      format,
      include_tree: (options.includeTree ?? true).toString(),
      include_violations: (options.includeViolations ?? true).toString()
    });

    if (format === 'json') {
      // For JSON, return the data directly
      const response = await apiClient.get(`/results/${taskId}/hierarchical?${params}`);
      return response.data;
    } else {
      // For CSV/Excel, trigger download
      const response = await apiClient.get(`/results/${taskId}/hierarchical?${params}`, {
        responseType: 'blob',
      });

      const blob = new Blob([response.data], {
        type: format === 'csv' ? 'text/csv' : 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      });

      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `hierarchical_results_${taskId}.${format === 'csv' ? 'csv' : 'xlsx'}`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    }
  } catch (error: any) {
    console.error('Error downloading hierarchical results:', error);
    throw new Error(error.response?.data?.detail || 'Failed to download hierarchical results');
  }
};

/**
 * Export results data as CSV (client-side)
 */
export const exportResultsCSVClientSide = (data: Record<string, any>[], filename?: string): void => {
  try {
    if (!data || data.length === 0) {
      throw new Error('No data to export');
    }

    // Get all unique column names
    const allColumns = new Set<string>();
    data.forEach(row => {
      Object.keys(row).forEach(key => allColumns.add(key));
    });
    const columns = Array.from(allColumns);

    // Create CSV content
    const csvContent = [
      // Header row
      columns.map(col => `"${col.replace(/"/g, '""')}"`).join(','),
      // Data rows
      ...data.map(row => 
        columns.map(col => {
          const value = row[col];
          if (value === null || value === undefined) {
            return '';
          }
          // Escape quotes and wrap in quotes if necessary
          const stringValue = String(value);
          if (stringValue.includes(',') || stringValue.includes('"') || stringValue.includes('\n')) {
            return `"${stringValue.replace(/"/g, '""')}"`;
          }
          return stringValue;
        }).join(',')
      )
    ].join('\n');

    // Create and download file
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename || 'classification_results.csv';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  } catch (error: any) {
    console.error('Error exporting CSV:', error);
    throw new Error('Failed to export CSV file');
  }
};
