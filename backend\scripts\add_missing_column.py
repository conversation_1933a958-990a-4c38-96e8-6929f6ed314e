"""
<PERSON><PERSON><PERSON> to add the missing confidence_threshold column to the hf_rules table.
"""
import os
import sys
import logging
import sqlite3

# Add the parent directory to the path so we can import from app
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Import database engine and config
from app.database import engine
from app.config import DATABASE_URL

def get_db_path():
    """Get the database path from the DATABASE_URL."""
    if DATABASE_URL.startswith('sqlite:///'):
        # Extract the path part after sqlite:///
        db_path = DATABASE_URL.replace('sqlite:///', '')
        # If it's a relative path, make it absolute
        if not os.path.isabs(db_path):
            # Make path relative to the backend directory
            db_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), db_path)
        return db_path
    return None

def add_missing_column():
    """Add the missing confidence_threshold column to the hf_rules table."""
    logger.info("Adding missing confidence_threshold column to hf_rules table...")

    db_path = get_db_path()
    if not db_path:
        logger.error("Could not determine database path from DATABASE_URL")
        return False

    logger.info(f"Database path: {db_path}")

    try:
        # Connect to the database
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # Check if the column already exists
        cursor.execute("PRAGMA table_info(hf_rules)")
        columns = [row[1] for row in cursor.fetchall()]
        logger.info(f"Existing columns in hf_rules table: {columns}")

        if 'confidence_threshold' in columns:
            logger.info("Column confidence_threshold already exists in hf_rules table")
            conn.close()
            return True

        # Add the column
        logger.info("Adding confidence_threshold column to hf_rules table...")
        cursor.execute("ALTER TABLE hf_rules ADD COLUMN confidence_threshold FLOAT")

        # Set default value for existing rows
        logger.info("Setting default value (0.5) for confidence_threshold in existing rows...")
        cursor.execute("UPDATE hf_rules SET confidence_threshold = 0.5 WHERE confidence_threshold IS NULL")

        # Commit the changes
        conn.commit()
        logger.info("Successfully added confidence_threshold column to hf_rules table")

        # Verify the column was added
        cursor.execute("PRAGMA table_info(hf_rules)")
        columns = [row[1] for row in cursor.fetchall()]
        logger.info(f"Updated columns in hf_rules table: {columns}")

        conn.close()
        return True
    except Exception as e:
        logger.error(f"Error adding confidence_threshold column: {e}", exc_info=True)
        return False

if __name__ == "__main__":
    print("Starting database schema update script")
    logger.info("Starting database schema update script")

    # Print database path
    db_path = get_db_path()
    print(f"Database path: {db_path}")

    # Check if the database file exists
    if os.path.exists(db_path):
        print(f"Database file exists at: {db_path}")
    else:
        print(f"Database file does not exist at: {db_path}")

    # Connect to the database and check tables
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        print(f"Tables in database: {[t[0] for t in tables]}")

        # Check if hf_rules table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='hf_rules'")
        if cursor.fetchone():
            print("hf_rules table exists")

            # Check columns in hf_rules table
            cursor.execute("PRAGMA table_info(hf_rules)")
            columns = cursor.fetchall()
            print(f"Columns in hf_rules table: {[c[1] for c in columns]}")

            # Check if confidence_threshold column exists
            if 'confidence_threshold' in [c[1] for c in columns]:
                print("confidence_threshold column already exists")
            else:
                print("confidence_threshold column does not exist")
        else:
            print("hf_rules table does not exist")

        conn.close()
    except Exception as e:
        print(f"Error checking database: {e}")

    success = add_missing_column()

    if success:
        print("Database schema update completed successfully")
        logger.info("Database schema update completed successfully")
    else:
        print("Database schema update failed")
        logger.error("Database schema update failed")
