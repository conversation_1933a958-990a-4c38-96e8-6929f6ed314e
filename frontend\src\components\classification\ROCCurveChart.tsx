/**
 * ROCCurveChart.tsx
 * 
 * Interactive ROC curve visualization component with AUC display and threshold selection.
 * Supports multiple models comparison and detailed performance analysis.
 */

import React, { useState, useMemo } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Slider } from "@/components/ui/slider";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  TrendingUp, 
  Target, 
  Info, 
  Download,
  Maximize2,
  Settings,
  Eye,
  BarChart3
} from "lucide-react";
import { 
  Line<PERSON>hart, 
  Line, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  ReferenceLine,
  Legend,
  Scatter<PERSON>hart,
  Scatter
} from 'recharts';

interface ROCPoint {
  fpr: number; // False Positive Rate
  tpr: number; // True Positive Rate
  threshold: number;
}

interface ROCData {
  modelId: string;
  modelName: string;
  points: ROCPoint[];
  auc: number;
  color: string;
  optimalThreshold?: number;
  optimalPoint?: ROCPoint;
}

interface PRPoint {
  recall: number;
  precision: number;
  threshold: number;
}

interface PRData {
  modelId: string;
  modelName: string;
  points: PRPoint[];
  auc: number;
  color: string;
}

interface ROCCurveChartProps {
  rocData: ROCData[];
  prData?: PRData[];
  selectedThreshold?: number;
  onThresholdChange?: (threshold: number) => void;
  showPRCurve?: boolean;
  showOptimalPoint?: boolean;
  interactive?: boolean;
  height?: number;
}

const DEFAULT_COLORS = [
  '#3b82f6', '#ef4444', '#22c55e', '#f59e0b', '#8b5cf6',
  '#06b6d4', '#f97316', '#84cc16', '#ec4899', '#6366f1'
];

export const ROCCurveChart: React.FC<ROCCurveChartProps> = ({
  rocData,
  prData,
  selectedThreshold = 0.5,
  onThresholdChange,
  showPRCurve = true,
  showOptimalPoint = true,
  interactive = true,
  height = 400
}) => {
  const [activeView, setActiveView] = useState<'roc' | 'pr' | 'both'>('roc');
  const [selectedModels, setSelectedModels] = useState<string[]>(rocData.map(d => d.modelId));
  const [hoveredPoint, setHoveredPoint] = useState<{x: number, y: number, data: any} | null>(null);

  // Prepare ROC curve data for chart
  const rocChartData = useMemo(() => {
    if (rocData.length === 0) return [];

    // Get all unique FPR values and sort them
    const allFPRs = Array.from(new Set(
      rocData.flatMap(model => model.points.map(p => p.fpr))
    )).sort((a, b) => a - b);

    return allFPRs.map(fpr => {
      const point: any = { fpr };
      
      rocData.forEach(model => {
        if (selectedModels.includes(model.modelId)) {
          // Find the closest point for this FPR
          const closestPoint = model.points.reduce((prev, curr) => 
            Math.abs(curr.fpr - fpr) < Math.abs(prev.fpr - fpr) ? curr : prev
          );
          point[model.modelId] = closestPoint.tpr;
        }
      });

      return point;
    });
  }, [rocData, selectedModels]);

  // Prepare PR curve data for chart
  const prChartData = useMemo(() => {
    if (!prData || prData.length === 0) return [];

    const allRecalls = Array.from(new Set(
      prData.flatMap(model => model.points.map(p => p.recall))
    )).sort((a, b) => a - b);

    return allRecalls.map(recall => {
      const point: any = { recall };
      
      prData.forEach(model => {
        if (selectedModels.includes(model.modelId)) {
          const closestPoint = model.points.reduce((prev, curr) => 
            Math.abs(curr.recall - recall) < Math.abs(prev.recall - recall) ? curr : prev
          );
          point[model.modelId] = closestPoint.precision;
        }
      });

      return point;
    });
  }, [prData, selectedModels]);

  // Find points at selected threshold
  const thresholdPoints = useMemo(() => {
    return rocData.map(model => {
      const point = model.points.reduce((prev, curr) => 
        Math.abs(curr.threshold - selectedThreshold) < Math.abs(prev.threshold - selectedThreshold) 
          ? curr : prev
      );
      return {
        modelId: model.modelId,
        modelName: model.modelName,
        fpr: point.fpr,
        tpr: point.tpr,
        threshold: point.threshold,
        color: model.color
      };
    });
  }, [rocData, selectedThreshold]);

  // Calculate Youden's J statistic for optimal threshold
  const youdenOptimal = useMemo(() => {
    return rocData.map(model => {
      const optimal = model.points.reduce((best, current) => {
        const currentJ = current.tpr - current.fpr; // Youden's J
        const bestJ = best.tpr - best.fpr;
        return currentJ > bestJ ? current : best;
      });
      
      return {
        modelId: model.modelId,
        modelName: model.modelName,
        point: optimal,
        jStatistic: optimal.tpr - optimal.fpr
      };
    });
  }, [rocData]);

  const formatAUC = (auc: number) => auc.toFixed(3);

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-background border rounded-lg p-3 shadow-lg">
          <p className="font-medium">{`FPR: ${label?.toFixed(3)}`}</p>
          {payload.map((entry: any, index: number) => {
            const model = rocData.find(m => m.modelId === entry.dataKey);
            return (
              <p key={index} style={{ color: entry.color }}>
                {`${model?.modelName}: ${entry.value?.toFixed(3)}`}
              </p>
            );
          })}
        </div>
      );
    }
    return null;
  };

  const PRTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-background border rounded-lg p-3 shadow-lg">
          <p className="font-medium">{`Recall: ${label?.toFixed(3)}`}</p>
          {payload.map((entry: any, index: number) => {
            const model = prData?.find(m => m.modelId === entry.dataKey);
            return (
              <p key={index} style={{ color: entry.color }}>
                {`${model?.modelName}: ${entry.value?.toFixed(3)}`}
              </p>
            );
          })}
        </div>
      );
    }
    return null;
  };

  if (rocData.length === 0) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <TrendingUp className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">No ROC Data Available</h3>
          <p className="text-muted-foreground">
            Train a binary classification model to see ROC curves.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Model Selection and Controls */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="w-5 h-5" />
            ROC Curve Analysis
          </CardTitle>
          <CardDescription>
            Receiver Operating Characteristic curves and performance metrics
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Model Selection */}
          <div className="space-y-2">
            <label className="text-sm font-medium">Models to Display</label>
            <div className="flex flex-wrap gap-2">
              {rocData.map((model) => (
                <Button
                  key={model.modelId}
                  variant={selectedModels.includes(model.modelId) ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => {
                    setSelectedModels(prev => 
                      prev.includes(model.modelId)
                        ? prev.filter(id => id !== model.modelId)
                        : [...prev, model.modelId]
                    );
                  }}
                  className="flex items-center gap-2"
                >
                  <div 
                    className="w-3 h-3 rounded-full" 
                    style={{ backgroundColor: model.color }}
                  />
                  {model.modelName}
                  <Badge variant="secondary" className="ml-1">
                    AUC: {formatAUC(model.auc)}
                  </Badge>
                </Button>
              ))}
            </div>
          </div>

          {/* Threshold Control */}
          {interactive && (
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <label className="text-sm font-medium">Classification Threshold</label>
                <Badge variant="outline">{selectedThreshold.toFixed(2)}</Badge>
              </div>
              <Slider
                value={[selectedThreshold]}
                onValueChange={(value) => onThresholdChange?.(value[0])}
                min={0}
                max={1}
                step={0.01}
                className="w-full"
              />
              <div className="flex justify-between text-xs text-muted-foreground">
                <span>0.00</span>
                <span>0.50</span>
                <span>1.00</span>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Charts */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Performance Curves</CardTitle>
              <CardDescription>
                ROC and Precision-Recall curves for model comparison
              </CardDescription>
            </div>
            <div className="flex gap-2">
              <Button variant="outline" size="sm">
                <Download className="w-4 h-4 mr-2" />
                Export
              </Button>
              <Button variant="outline" size="sm">
                <Maximize2 className="w-4 h-4 mr-2" />
                Fullscreen
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Tabs value={activeView} onValueChange={(value) => setActiveView(value as any)}>
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="roc">ROC Curve</TabsTrigger>
              <TabsTrigger value="pr" disabled={!prData}>PR Curve</TabsTrigger>
              <TabsTrigger value="both" disabled={!prData}>Both</TabsTrigger>
            </TabsList>

            <TabsContent value="roc" className="space-y-4">
              <ResponsiveContainer width="100%" height={height}>
                <LineChart data={rocChartData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis 
                    dataKey="fpr" 
                    label={{ value: 'False Positive Rate', position: 'insideBottom', offset: -10 }}
                  />
                  <YAxis 
                    label={{ value: 'True Positive Rate', angle: -90, position: 'insideLeft' }}
                  />
                  <Tooltip content={<CustomTooltip />} />
                  <Legend />
                  
                  {/* Diagonal reference line (random classifier) */}
                  <ReferenceLine 
                    segment={[{ x: 0, y: 0 }, { x: 1, y: 1 }]} 
                    stroke="#94a3b8" 
                    strokeDasharray="5 5"
                  />
                  
                  {/* ROC curves for each selected model */}
                  {rocData
                    .filter(model => selectedModels.includes(model.modelId))
                    .map((model) => (
                      <Line
                        key={model.modelId}
                        type="monotone"
                        dataKey={model.modelId}
                        stroke={model.color}
                        strokeWidth={2}
                        dot={false}
                        name={`${model.modelName} (AUC: ${formatAUC(model.auc)})`}
                      />
                    ))
                  }
                </LineChart>
              </ResponsiveContainer>

              {/* Threshold Points */}
              {interactive && (
                <div className="mt-4">
                  <h4 className="font-medium mb-2">Performance at Threshold {selectedThreshold.toFixed(2)}</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {thresholdPoints.map((point) => (
                      <div key={point.modelId} className="p-3 bg-muted/50 rounded-lg">
                        <div className="flex items-center gap-2 mb-2">
                          <div 
                            className="w-3 h-3 rounded-full" 
                            style={{ backgroundColor: point.color }}
                          />
                          <span className="font-medium">{point.modelName}</span>
                        </div>
                        <div className="space-y-1 text-sm">
                          <div className="flex justify-between">
                            <span>TPR:</span>
                            <span className="font-medium">{point.tpr.toFixed(3)}</span>
                          </div>
                          <div className="flex justify-between">
                            <span>FPR:</span>
                            <span className="font-medium">{point.fpr.toFixed(3)}</span>
                          </div>
                          <div className="flex justify-between">
                            <span>Specificity:</span>
                            <span className="font-medium">{(1 - point.fpr).toFixed(3)}</span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </TabsContent>

            <TabsContent value="pr" className="space-y-4">
              {prData && (
                <ResponsiveContainer width="100%" height={height}>
                  <LineChart data={prChartData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis 
                      dataKey="recall" 
                      label={{ value: 'Recall', position: 'insideBottom', offset: -10 }}
                    />
                    <YAxis 
                      label={{ value: 'Precision', angle: -90, position: 'insideLeft' }}
                    />
                    <Tooltip content={<PRTooltip />} />
                    <Legend />
                    
                    {prData
                      .filter(model => selectedModels.includes(model.modelId))
                      .map((model) => (
                        <Line
                          key={model.modelId}
                          type="monotone"
                          dataKey={model.modelId}
                          stroke={model.color}
                          strokeWidth={2}
                          dot={false}
                          name={`${model.modelName} (AUC: ${formatAUC(model.auc)})`}
                        />
                      ))
                    }
                  </LineChart>
                </ResponsiveContainer>
              )}
            </TabsContent>

            <TabsContent value="both" className="space-y-4">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-medium mb-3">ROC Curve</h4>
                  <ResponsiveContainer width="100%" height={300}>
                    <LineChart data={rocChartData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="fpr" />
                      <YAxis />
                      <Tooltip content={<CustomTooltip />} />
                      <ReferenceLine 
                        segment={[{ x: 0, y: 0 }, { x: 1, y: 1 }]} 
                        stroke="#94a3b8" 
                        strokeDasharray="5 5"
                      />
                      {rocData
                        .filter(model => selectedModels.includes(model.modelId))
                        .map((model) => (
                          <Line
                            key={model.modelId}
                            type="monotone"
                            dataKey={model.modelId}
                            stroke={model.color}
                            strokeWidth={2}
                            dot={false}
                          />
                        ))
                      }
                    </LineChart>
                  </ResponsiveContainer>
                </div>

                {prData && (
                  <div>
                    <h4 className="font-medium mb-3">Precision-Recall Curve</h4>
                    <ResponsiveContainer width="100%" height={300}>
                      <LineChart data={prChartData}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="recall" />
                        <YAxis />
                        <Tooltip content={<PRTooltip />} />
                        {prData
                          .filter(model => selectedModels.includes(model.modelId))
                          .map((model) => (
                            <Line
                              key={model.modelId}
                              type="monotone"
                              dataKey={model.modelId}
                              stroke={model.color}
                              strokeWidth={2}
                              dot={false}
                            />
                          ))
                        }
                      </LineChart>
                    </ResponsiveContainer>
                  </div>
                )}
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Optimal Thresholds */}
      {showOptimalPoint && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Target className="w-5 h-5" />
              Optimal Thresholds
            </CardTitle>
            <CardDescription>
              Recommended thresholds based on different optimization criteria
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {youdenOptimal.map((optimal) => (
                <div key={optimal.modelId} className="p-4 border rounded-lg">
                  <div className="flex items-center justify-between mb-3">
                    <h4 className="font-medium">{optimal.modelName}</h4>
                    <Badge variant="outline">
                      J = {optimal.jStatistic.toFixed(3)}
                    </Badge>
                  </div>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div>
                      <span className="text-muted-foreground">Threshold:</span>
                      <div className="font-medium">{optimal.point.threshold.toFixed(3)}</div>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Sensitivity:</span>
                      <div className="font-medium">{optimal.point.tpr.toFixed(3)}</div>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Specificity:</span>
                      <div className="font-medium">{(1 - optimal.point.fpr).toFixed(3)}</div>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Youden's J:</span>
                      <div className="font-medium">{optimal.jStatistic.toFixed(3)}</div>
                    </div>
                  </div>
                  {interactive && (
                    <Button 
                      size="sm" 
                      variant="outline" 
                      className="mt-3"
                      onClick={() => onThresholdChange?.(optimal.point.threshold)}
                    >
                      Use This Threshold
                    </Button>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default ROCCurveChart;
