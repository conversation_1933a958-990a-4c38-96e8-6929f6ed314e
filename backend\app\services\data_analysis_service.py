"""Smart Data Structure Detection Service for ClassyWeb.

This service analyzes uploaded data to automatically detect whether it contains
hierarchical, flat, mixed, or unclear classification structures. It provides
confidence scores and suggestions to guide users toward the optimal workflow.
"""

import logging
import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from collections import Counter, defaultdict
import re
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
from sklearn.cluster import KMeans
import json

logger = logging.getLogger(__name__)

@dataclass
class DataStructureSuggestion:
    """Suggestion for data structure configuration."""
    structure_type: str  # 'hierarchical', 'flat', 'mixed', 'unclear'
    confidence: float  # 0.0 to 1.0
    reasoning: str
    suggested_config: Dict[str, Any]
    preview_data: Dict[str, Any]

@dataclass
class SmartDataAnalysis:
    """Complete analysis of uploaded data structure."""
    detected_structure: str  # 'hierarchical', 'flat', 'mixed', 'unclear'
    confidence: float
    suggestions: List[DataStructureSuggestion]
    preview: Dict[str, Any]
    column_analysis: Dict[str, Any]
    data_quality: Dict[str, Any]

class DataStructureDetector:
    """Analyzes data to detect optimal classification structure."""
    
    def __init__(self):
        self.hierarchical_indicators = [
            'theme', 'category', 'segment', 'subsegment', 'level', 'tier',
            'department', 'team', 'role', 'division', 'section', 'group',
            'class', 'subclass', 'type', 'subtype', 'parent', 'child',
            'main', 'sub', 'primary', 'secondary', 'major', 'minor'
        ]
        
        self.flat_indicators = [
            'tag', 'label', 'keyword', 'topic', 'sentiment', 'emotion',
            'intent', 'skill', 'feature', 'attribute', 'property'
        ]
    
    def analyze_data_structure(self, df: pd.DataFrame, text_column: str = None) -> SmartDataAnalysis:
        """Perform comprehensive analysis of data structure."""
        try:
            logger.info(f"Analyzing data structure for DataFrame with shape {df.shape}")
            
            # Basic data quality analysis
            data_quality = self._analyze_data_quality(df)
            
            # Column analysis
            column_analysis = self._analyze_columns(df)
            
            # Detect text column if not provided
            if not text_column:
                text_column = self._detect_text_column(df)
            
            # Analyze label structure
            label_columns = self._detect_label_columns(df, text_column)
            structure_analysis = self._analyze_label_structure(df, label_columns)
            
            # Generate suggestions
            suggestions = self._generate_suggestions(df, structure_analysis, label_columns, text_column)
            
            # Determine primary structure type
            primary_suggestion = max(suggestions, key=lambda x: x.confidence) if suggestions else None
            detected_structure = primary_suggestion.structure_type if primary_suggestion else 'unclear'
            confidence = primary_suggestion.confidence if primary_suggestion else 0.0
            
            # Create preview data
            preview = self._create_preview_data(df, text_column, label_columns)
            
            return SmartDataAnalysis(
                detected_structure=detected_structure,
                confidence=confidence,
                suggestions=suggestions,
                preview=preview,
                column_analysis=column_analysis,
                data_quality=data_quality
            )
            
        except Exception as e:
            logger.error(f"Error analyzing data structure: {e}", exc_info=True)
            return self._create_fallback_analysis(df)
    
    def _analyze_data_quality(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Analyze data quality metrics."""
        total_cells = df.shape[0] * df.shape[1]
        missing_cells = df.isnull().sum().sum()
        
        return {
            'total_rows': int(len(df)),
            'total_columns': int(len(df.columns)),
            'missing_data_percentage': float((missing_cells / total_cells) * 100 if total_cells > 0 else 0),
            'duplicate_rows': int(df.duplicated().sum()),
            'empty_rows': int((df.isnull().all(axis=1)).sum()),
            'data_types': df.dtypes.astype(str).to_dict()
        }
    
    def _analyze_columns(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Analyze column characteristics."""
        column_info = {}
        
        for col in df.columns:
            col_data = df[col].dropna()
            
            # Basic statistics
            unique_count = col_data.nunique()
            total_count = len(col_data)
            
            # Detect column type
            col_type = self._detect_column_type(col_data)
            
            # Calculate uniqueness ratio
            uniqueness_ratio = unique_count / total_count if total_count > 0 else 0
            
            column_info[col] = {
                'type': col_type,
                'unique_count': int(unique_count),  # Convert numpy int to Python int
                'total_count': int(total_count),    # Convert numpy int to Python int
                'uniqueness_ratio': float(uniqueness_ratio),  # Ensure it's a Python float
                'sample_values': col_data.head(5).tolist(),
                'is_potential_text': col_type == 'text',
                'is_potential_label': col_type in ['categorical', 'text'] and uniqueness_ratio < 0.5
            }
        
        return column_info
    
    def _detect_column_type(self, series: pd.Series) -> str:
        """Detect the type of a column."""
        # Check if numeric
        if pd.api.types.is_numeric_dtype(series):
            return 'numeric'
        
        # Check if datetime
        try:
            pd.to_datetime(series.head(10))
            return 'datetime'
        except:
            pass
        
        # Check if boolean
        unique_values = set(str(v).lower() for v in series.unique()[:10])
        if unique_values.issubset({'true', 'false', '1', '0', 'yes', 'no'}):
            return 'boolean'
        
        # Check if text (long strings)
        avg_length = series.astype(str).str.len().mean()
        if avg_length > 50:
            return 'text'
        
        # Default to categorical
        return 'categorical'
    
    def _detect_text_column(self, df: pd.DataFrame) -> Optional[str]:
        """Detect the most likely text column for classification."""
        text_candidates = []
        
        for col in df.columns:
            col_data = df[col].dropna()
            if len(col_data) == 0:
                continue
                
            # Calculate average text length
            avg_length = col_data.astype(str).str.len().mean()
            
            # Check for text-like column names
            col_lower = col.lower()
            text_keywords = ['text', 'content', 'description', 'comment', 'message', 'review', 'feedback']
            name_score = sum(1 for keyword in text_keywords if keyword in col_lower)
            
            # Combined score
            score = avg_length + (name_score * 20)
            text_candidates.append((col, score, avg_length))
        
        if text_candidates:
            # Return column with highest score and reasonable length
            text_candidates.sort(key=lambda x: x[1], reverse=True)
            best_candidate = text_candidates[0]
            if best_candidate[2] > 20:  # Minimum average length
                return best_candidate[0]
        
        return None
    
    def _detect_label_columns(self, df: pd.DataFrame, text_column: str) -> List[str]:
        """Detect columns that likely contain classification labels."""
        label_columns = []
        
        for col in df.columns:
            if col == text_column:
                continue
                
            col_data = df[col].dropna()
            if len(col_data) == 0:
                continue
            
            # Check uniqueness ratio (labels should have limited unique values)
            uniqueness_ratio = col_data.nunique() / len(col_data)
            
            # Check for label-like column names
            col_lower = col.lower()
            is_hierarchical_name = any(indicator in col_lower for indicator in self.hierarchical_indicators)
            is_flat_name = any(indicator in col_lower for indicator in self.flat_indicators)
            
            # Check data characteristics
            avg_length = col_data.astype(str).str.len().mean()
            
            # Score as potential label column
            if (uniqueness_ratio < 0.3 and avg_length < 50) or is_hierarchical_name or is_flat_name:
                label_columns.append(col)
        
        return label_columns

    def _analyze_label_structure(self, df: pd.DataFrame, label_columns: List[str]) -> Dict[str, Any]:
        """Analyze the structure of label columns to detect hierarchy patterns."""
        if not label_columns:
            return {'type': 'unclear', 'confidence': 0.0, 'details': {}}

        # Analyze each label column
        column_analyses = {}
        for col in label_columns:
            col_analysis = self._analyze_single_label_column(df, col)
            column_analyses[col] = col_analysis

        # Detect overall structure
        structure_type, confidence, details = self._determine_structure_type(column_analyses, df)

        return {
            'type': structure_type,
            'confidence': confidence,
            'details': details,
            'column_analyses': column_analyses
        }

    def _analyze_single_label_column(self, df: pd.DataFrame, col: str) -> Dict[str, Any]:
        """Analyze a single label column for patterns."""
        col_data = df[col].dropna().astype(str)

        # Basic statistics
        unique_values = col_data.unique()
        value_counts = col_data.value_counts()

        # Check for hierarchical patterns
        hierarchical_score = self._calculate_hierarchical_score(unique_values, col.lower())

        # Check for flat label patterns
        flat_score = self._calculate_flat_score(unique_values, col.lower())

        # Analyze value relationships
        relationships = self._analyze_value_relationships(unique_values)

        return {
            'unique_count': int(len(unique_values)),
            'most_common': {str(k): int(v) for k, v in value_counts.head(5).to_dict().items()},
            'hierarchical_score': float(hierarchical_score),
            'flat_score': float(flat_score),
            'relationships': relationships,
            'avg_value_length': float(col_data.str.len().mean()),
            'has_separators': any(sep in ' '.join(unique_values) for sep in ['>', '->', '/', '|', ':']),
            'has_nested_structure': self._detect_nested_structure(unique_values)
        }

    def _calculate_hierarchical_score(self, values: np.ndarray, col_name: str) -> float:
        """Calculate how likely this column represents hierarchical data."""
        score = 0.0

        # Check column name
        if any(indicator in col_name for indicator in self.hierarchical_indicators):
            score += 0.3

        # Check for hierarchical separators
        separators = ['>', '->', '/', '|', ':']
        for value in values[:20]:  # Check first 20 values
            if any(sep in str(value) for sep in separators):
                score += 0.1
                break

        # Check for level-like naming patterns
        level_patterns = [r'level\s*\d+', r'tier\s*\d+', r'l\d+', r't\d+']
        for pattern in level_patterns:
            if any(re.search(pattern, str(value).lower()) for value in values[:10]):
                score += 0.2
                break

        # Check for parent-child relationships
        if self._has_parent_child_relationships(values):
            score += 0.3

        return min(score, 1.0)

    def _calculate_flat_score(self, values: np.ndarray, col_name: str) -> float:
        """Calculate how likely this column represents flat multi-label data."""
        score = 0.0

        # Check column name
        if any(indicator in col_name for indicator in self.flat_indicators):
            score += 0.3

        # Check for multi-label separators
        multi_separators = [',', ';', '|']
        for value in values[:20]:
            if any(sep in str(value) for sep in multi_separators):
                score += 0.4
                break

        # Check for tag-like patterns
        tag_patterns = [r'#\w+', r'@\w+', r'\[\w+\]']
        for pattern in tag_patterns:
            if any(re.search(pattern, str(value)) for value in values[:10]):
                score += 0.2
                break

        return min(score, 1.0)

    def _analyze_value_relationships(self, values: np.ndarray) -> Dict[str, Any]:
        """Analyze relationships between values."""
        relationships = {
            'has_common_prefixes': False,
            'has_common_suffixes': False,
            'has_nested_patterns': False,
            'common_patterns': []
        }

        if len(values) < 2:
            return relationships

        # Check for common prefixes/suffixes
        str_values = [str(v) for v in values]

        # Common prefixes
        prefixes = Counter()
        for value in str_values:
            words = value.split()
            if len(words) > 1:
                prefixes[words[0]] += 1

        if prefixes and prefixes.most_common(1)[0][1] > len(values) * 0.3:
            relationships['has_common_prefixes'] = True
            relationships['common_patterns'].append(f"Common prefix: {prefixes.most_common(1)[0][0]}")

        return relationships

    def _has_parent_child_relationships(self, values: np.ndarray) -> bool:
        """Check if values show parent-child hierarchical relationships."""
        str_values = [str(v).lower() for v in values]

        # Look for values that are substrings of others (indicating hierarchy)
        for i, val1 in enumerate(str_values):
            for j, val2 in enumerate(str_values):
                if i != j and val1 in val2 and len(val1) < len(val2):
                    return True

        return False

    def _detect_nested_structure(self, values: np.ndarray) -> bool:
        """Detect if values have nested structure patterns."""
        for value in values[:10]:
            str_val = str(value)
            # Check for nested patterns like "A > B > C" or "A/B/C"
            if any(sep in str_val for sep in ['>', '->', '/', '\\', '|']):
                parts = re.split(r'[>\/\\|]|->|--', str_val)
                if len(parts) > 1:
                    return True
        return False

    def _determine_structure_type(self, column_analyses: Dict[str, Any], df: pd.DataFrame) -> Tuple[str, float, Dict[str, Any]]:
        """Determine overall structure type from column analyses."""
        if not column_analyses:
            return 'unclear', 0.0, {}

        # Calculate scores for each structure type
        hierarchical_scores = [analysis['hierarchical_score'] for analysis in column_analyses.values()]
        flat_scores = [analysis['flat_score'] for analysis in column_analyses.values()]

        avg_hierarchical = np.mean(hierarchical_scores)
        avg_flat = np.mean(flat_scores)

        # Check for multiple label columns (indicates potential hierarchy)
        num_label_columns = len(column_analyses)

        details = {
            'num_label_columns': int(num_label_columns),
            'avg_hierarchical_score': float(avg_hierarchical),
            'avg_flat_score': float(avg_flat),
            'column_scores': column_analyses
        }

        # Decision logic
        if num_label_columns >= 3 and avg_hierarchical > 0.4:
            return 'hierarchical', avg_hierarchical, details
        elif num_label_columns == 1 and avg_flat > 0.3:
            return 'flat', avg_flat, details
        elif avg_hierarchical > avg_flat and avg_hierarchical > 0.3:
            return 'hierarchical', avg_hierarchical, details
        elif avg_flat > avg_hierarchical and avg_flat > 0.3:
            return 'flat', avg_flat, details
        elif num_label_columns > 1:
            return 'mixed', max(avg_hierarchical, avg_flat) * 0.7, details
        else:
            return 'unclear', 0.2, details

    def _generate_suggestions(self, df: pd.DataFrame, structure_analysis: Dict[str, Any],
                            label_columns: List[str], text_column: str) -> List[DataStructureSuggestion]:
        """Generate configuration suggestions based on analysis."""
        suggestions = []

        structure_type = structure_analysis['type']
        confidence = structure_analysis['confidence']

        if structure_type == 'hierarchical':
            suggestions.append(self._create_hierarchical_suggestion(df, label_columns, text_column, confidence))

        if structure_type == 'flat':
            suggestions.append(self._create_flat_suggestion(df, label_columns, text_column, confidence))

        if structure_type == 'mixed':
            # Suggest both approaches
            suggestions.append(self._create_hierarchical_suggestion(df, label_columns, text_column, confidence * 0.8))
            suggestions.append(self._create_flat_suggestion(df, label_columns, text_column, confidence * 0.8))

        if structure_type == 'unclear' or not suggestions:
            # Provide fallback suggestions
            suggestions.extend(self._create_fallback_suggestions(df, label_columns, text_column))

        return suggestions

    def _create_hierarchical_suggestion(self, df: pd.DataFrame, label_columns: List[str],
                                      text_column: str, confidence: float) -> DataStructureSuggestion:
        """Create suggestion for hierarchical classification."""
        # Suggest hierarchy levels based on label columns
        suggested_levels = []
        for col in label_columns:
            # Clean column name for hierarchy level
            clean_name = col.replace('_', ' ').title()
            suggested_levels.append(clean_name)

        # Ensure we have at least 2 levels
        if len(suggested_levels) < 2:
            suggested_levels = ['Level 1', 'Level 2']

        # Limit to reasonable number of levels
        suggested_levels = suggested_levels[:6]

        suggested_config = {
            'workflow_type': 'hierarchical',
            'hierarchy_levels': suggested_levels,
            'text_column': text_column,
            'label_columns': label_columns,
            'recommended_approach': 'LLM' if len(df) < 1000 else 'HF'
        }

        preview_data = {
            'sample_hierarchy': self._create_sample_hierarchy(df, label_columns),
            'estimated_complexity': int(len(suggested_levels)),
            'data_size': int(len(df))
        }

        reasoning = f"Detected {len(label_columns)} label columns with hierarchical patterns. " \
                   f"Suggested {len(suggested_levels)} hierarchy levels based on column structure."

        return DataStructureSuggestion(
            structure_type='hierarchical',
            confidence=confidence,
            reasoning=reasoning,
            suggested_config=suggested_config,
            preview_data=preview_data
        )

    def _create_flat_suggestion(self, df: pd.DataFrame, label_columns: List[str],
                              text_column: str, confidence: float) -> DataStructureSuggestion:
        """Create suggestion for flat multi-label classification."""
        # Analyze labels for flat structure
        all_labels = set()
        for col in label_columns:
            col_data = df[col].dropna().astype(str)
            for value in col_data:
                # Split on common separators for multi-label
                labels = re.split(r'[,;|]', value)
                all_labels.update(label.strip() for label in labels if label.strip())

        suggested_config = {
            'workflow_type': 'flat',
            'text_column': text_column,
            'label_columns': label_columns,
            'estimated_labels': list(all_labels)[:20],  # Show first 20 labels
            'total_unique_labels': int(len(all_labels)),
            'recommended_approach': 'HF' if len(df) > 100 else 'LLM'
        }

        preview_data = {
            'sample_labels': list(all_labels)[:10],
            'multi_label_examples': self._find_multi_label_examples(df, label_columns),
            'label_distribution': self._calculate_label_distribution(df, label_columns)
        }

        reasoning = f"Detected {len(all_labels)} unique labels across {len(label_columns)} columns. " \
                   f"Data appears suitable for independent multi-label classification."

        return DataStructureSuggestion(
            structure_type='flat',
            confidence=confidence,
            reasoning=reasoning,
            suggested_config=suggested_config,
            preview_data=preview_data
        )

    def _create_fallback_suggestions(self, df: pd.DataFrame, label_columns: List[str],
                                   text_column: str) -> List[DataStructureSuggestion]:
        """Create fallback suggestions when structure is unclear."""
        suggestions = []

        # Always suggest hierarchical as an option
        hierarchical_suggestion = DataStructureSuggestion(
            structure_type='hierarchical',
            confidence=0.5,
            reasoning="Default hierarchical approach - suitable for most classification tasks",
            suggested_config={
                'workflow_type': 'hierarchical',
                'hierarchy_levels': ['Level 1', 'Level 2'],
                'text_column': text_column,
                'label_columns': label_columns or [],
                'recommended_approach': 'LLM'
            },
            preview_data={
                'note': 'Generic hierarchical structure - can be customized'
            }
        )
        suggestions.append(hierarchical_suggestion)

        # Suggest flat if we have potential labels
        if label_columns:
            flat_suggestion = DataStructureSuggestion(
                structure_type='flat',
                confidence=0.4,
                reasoning="Alternative flat multi-label approach - suitable for independent labels",
                suggested_config={
                    'workflow_type': 'flat',
                    'text_column': text_column,
                    'label_columns': label_columns,
                    'recommended_approach': 'HF'
                },
                preview_data={
                    'note': 'Independent multi-label classification'
                }
            )
            suggestions.append(flat_suggestion)

        return suggestions

    def _create_sample_hierarchy(self, df: pd.DataFrame, label_columns: List[str]) -> Dict[str, Any]:
        """Create a sample hierarchy structure from the data."""
        if not label_columns:
            return {}

        sample_data = {}
        for i, col in enumerate(label_columns[:4]):  # Max 4 levels for preview
            col_data = df[col].dropna().astype(str)
            unique_values = col_data.unique()[:5]  # Top 5 values
            sample_data[f'Level_{i+1}'] = unique_values.tolist()

        return sample_data

    def _find_multi_label_examples(self, df: pd.DataFrame, label_columns: List[str]) -> List[str]:
        """Find examples of multi-label entries."""
        examples = []

        for col in label_columns:
            col_data = df[col].dropna().astype(str)
            for value in col_data:
                # Check if value contains multiple labels
                if any(sep in value for sep in [',', ';', '|']):
                    examples.append(value)
                    if len(examples) >= 3:
                        return examples

        return examples

    def _calculate_label_distribution(self, df: pd.DataFrame, label_columns: List[str]) -> Dict[str, int]:
        """Calculate distribution of labels across columns."""
        distribution = {}

        for col in label_columns:
            col_data = df[col].dropna().astype(str)
            unique_count = col_data.nunique()
            distribution[col] = int(unique_count)

        return distribution

    def _create_preview_data(self, df: pd.DataFrame, text_column: str, label_columns: List[str]) -> Dict[str, Any]:
        """Create preview data for the frontend."""
        preview = {
            'sample_rows': df.head(5).to_dict('records'),
            'text_column': text_column,
            'label_columns': label_columns,
            'total_rows': int(len(df)),
            'total_columns': int(len(df.columns))
        }

        # Add text samples if text column is detected
        if text_column and text_column in df.columns:
            text_samples = df[text_column].dropna().head(3).tolist()
            preview['text_samples'] = text_samples

        return preview

    def _create_fallback_analysis(self, df: pd.DataFrame) -> SmartDataAnalysis:
        """Create fallback analysis when detection fails."""
        return SmartDataAnalysis(
            detected_structure='unclear',
            confidence=0.0,
            suggestions=[],
            preview={'sample_rows': df.head(3).to_dict('records') if not df.empty else []},
            column_analysis={},
            data_quality={'error': 'Analysis failed'}
        )

# Global instance
data_structure_detector = DataStructureDetector()
