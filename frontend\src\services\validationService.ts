// frontend/src/services/validationService.ts
import { HierarchyConfig, HierarchyRow } from '../types';

export interface ValidationRule {
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  pattern?: string;
  customValidator?: (value: any) => string | null;
}

export interface ValidationError {
  field: string;
  message: string;
  rowIndex?: number;
}

export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
  warnings?: ValidationError[];
}

export class DynamicValidationService {
  private hierarchyConfig: HierarchyConfig;

  constructor(hierarchyConfig: HierarchyConfig) {
    this.hierarchyConfig = hierarchyConfig;
  }

  /**
   * Get validation rules for a specific field
   */
  getFieldValidationRules(fieldName: string): ValidationRule {
    const validationRules = this.hierarchyConfig.validation_rules || {};
    const fieldRules = validationRules[fieldName] || {};

    // Default rules
    const defaultRules: ValidationRule = {
      required: true,
      minLength: 1,
      maxLength: 100,
      pattern: '^[a-zA-Z0-9\\s\\-_\\.]+$'
    };

    return { ...defaultRules, ...fieldRules };
  }

  /**
   * Validate a single field value
   */
  validateField(fieldName: string, value: any): ValidationError | null {
    const rules = this.getFieldValidationRules(fieldName);
    const stringValue = value?.toString().trim() || '';

    // Required validation
    if (rules.required && !stringValue) {
      return {
        field: fieldName,
        message: `${this.getDisplayName(fieldName)} is required`
      };
    }

    // Skip other validations if field is empty and not required
    if (!stringValue && !rules.required) {
      return null;
    }

    // Length validations
    if (rules.minLength && stringValue.length < rules.minLength) {
      return {
        field: fieldName,
        message: `${this.getDisplayName(fieldName)} must be at least ${rules.minLength} characters`
      };
    }

    if (rules.maxLength && stringValue.length > rules.maxLength) {
      return {
        field: fieldName,
        message: `${this.getDisplayName(fieldName)} must be at most ${rules.maxLength} characters`
      };
    }

    // Pattern validation
    if (rules.pattern) {
      try {
        const regex = new RegExp(rules.pattern);
        if (!regex.test(stringValue)) {
          return {
            field: fieldName,
            message: `${this.getDisplayName(fieldName)} contains invalid characters`
          };
        }
      } catch (error) {
        console.warn(`Invalid regex pattern for field ${fieldName}:`, rules.pattern);
      }
    }

    // Custom validation
    if (rules.customValidator) {
      const customError = rules.customValidator(value);
      if (customError) {
        return {
          field: fieldName,
          message: customError
        };
      }
    }

    return null;
  }

  /**
   * Validate a single hierarchy row
   */
  validateRow(row: HierarchyRow, rowIndex?: number): ValidationResult {
    const errors: ValidationError[] = [];

    // Validate hierarchy levels
    this.hierarchyConfig.hierarchy_levels.forEach(level => {
      const error = this.validateField(level, row[level]);
      if (error) {
        errors.push({
          ...error,
          rowIndex
        });
      }
    });

    // Validate Keywords field (usually optional)
    const keywordsError = this.validateField('Keywords', row.Keywords);
    if (keywordsError) {
      errors.push({
        ...keywordsError,
        rowIndex
      });
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Validate multiple hierarchy rows
   */
  validateRows(rows: HierarchyRow[]): ValidationResult {
    const allErrors: ValidationError[] = [];

    rows.forEach((row, index) => {
      const rowResult = this.validateRow(row, index);
      allErrors.push(...rowResult.errors);
    });

    // Check for duplicate combinations
    const duplicateErrors = this.checkForDuplicates(rows);
    allErrors.push(...duplicateErrors);

    return {
      isValid: allErrors.length === 0,
      errors: allErrors
    };
  }

  /**
   * Check for duplicate hierarchy combinations
   */
  private checkForDuplicates(rows: HierarchyRow[]): ValidationError[] {
    const errors: ValidationError[] = [];
    const seen = new Set<string>();

    rows.forEach((row, index) => {
      // Create a key from all hierarchy levels
      const key = this.hierarchyConfig.hierarchy_levels
        .map(level => row[level]?.toString().trim().toLowerCase() || '')
        .join('|');

      if (key && seen.has(key)) {
        errors.push({
          field: 'duplicate',
          message: 'Duplicate hierarchy combination found',
          rowIndex: index
        });
      } else if (key) {
        seen.add(key);
      }
    });

    return errors;
  }

  /**
   * Get display name for a field
   */
  private getDisplayName(fieldName: string): string {
    const uiConfig = this.hierarchyConfig.ui_config || {};
    const displayNames = uiConfig.display_names || {};
    return displayNames[fieldName] || fieldName.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());
  }

  /**
   * Generate form validation schema for use with form libraries
   */
  generateFormValidationSchema(): Record<string, any> {
    const schema: Record<string, any> = {};

    this.hierarchyConfig.hierarchy_levels.forEach(level => {
      const rules = this.getFieldValidationRules(level);
      schema[level] = {
        required: rules.required,
        minLength: rules.minLength,
        maxLength: rules.maxLength,
        pattern: rules.pattern
      };
    });

    // Add Keywords validation (usually optional)
    schema.Keywords = {
      required: false,
      maxLength: 500
    };

    return schema;
  }

  /**
   * Get validation message for a specific error type
   */
  getValidationMessage(fieldName: string, errorType: string, _value?: any): string {
    const displayName = this.getDisplayName(fieldName);
    const rules = this.getFieldValidationRules(fieldName);

    switch (errorType) {
      case 'required':
        return `${displayName} is required`;
      case 'minLength':
        return `${displayName} must be at least ${rules.minLength} characters`;
      case 'maxLength':
        return `${displayName} must be at most ${rules.maxLength} characters`;
      case 'pattern':
        return `${displayName} contains invalid characters`;
      case 'duplicate':
        return 'This hierarchy combination already exists';
      default:
        return `${displayName} is invalid`;
    }
  }

  /**
   * Validate hierarchy configuration itself
   */
  static validateHierarchyConfig(config: Partial<HierarchyConfig>): ValidationResult {
    const errors: ValidationError[] = [];

    // Name validation
    if (!config.name?.trim()) {
      errors.push({
        field: 'name',
        message: 'Configuration name is required'
      });
    }

    // Hierarchy levels validation
    if (!config.hierarchy_levels || config.hierarchy_levels.length === 0) {
      errors.push({
        field: 'hierarchy_levels',
        message: 'At least one hierarchy level is required'
      });
    } else {
      // Check for empty level names
      config.hierarchy_levels.forEach((level, index) => {
        if (!level?.trim()) {
          errors.push({
            field: 'hierarchy_levels',
            message: `Hierarchy level ${index + 1} cannot be empty`
          });
        }
      });

      // Check for duplicate level names
      const levelNames = config.hierarchy_levels.map(l => l?.toLowerCase().trim());
      const duplicates = levelNames.filter((name, index) => 
        name && levelNames.indexOf(name) !== index
      );
      
      if (duplicates.length > 0) {
        errors.push({
          field: 'hierarchy_levels',
          message: 'Hierarchy level names must be unique'
        });
      }
    }

    // Confidence thresholds validation
    if (config.confidence_thresholds) {
      Object.entries(config.confidence_thresholds).forEach(([level, threshold]) => {
        if (typeof threshold !== 'number' || threshold < 0.1 || threshold > 0.9) {
          errors.push({
            field: 'confidence_thresholds',
            message: `Confidence threshold for ${level} must be between 0.1 and 0.9`
          });
        }
      });
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}

/**
 * Create a validation service instance for a hierarchy configuration
 */
export const createValidationService = (hierarchyConfig: HierarchyConfig): DynamicValidationService => {
  return new DynamicValidationService(hierarchyConfig);
};

/**
 * Utility function to format validation errors for display
 */
export const formatValidationErrors = (errors: ValidationError[]): string[] => {
  return errors.map(error => {
    if (error.rowIndex !== undefined) {
      return `Row ${error.rowIndex + 1}: ${error.message}`;
    }
    return error.message;
  });
};

/**
 * Group validation errors by field
 */
export const groupErrorsByField = (errors: ValidationError[]): Record<string, ValidationError[]> => {
  const grouped: Record<string, ValidationError[]> = {};
  
  errors.forEach(error => {
    if (!grouped[error.field]) {
      grouped[error.field] = [];
    }
    grouped[error.field].push(error);
  });
  
  return grouped;
};

export default DynamicValidationService;
