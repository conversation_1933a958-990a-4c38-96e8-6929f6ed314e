/**
 * unifiedProgressMonitor.ts
 * 
 * Centralized progress monitoring system for file uploads and processing
 * across all workflows with real-time updates and error handling.
 */

// Browser-compatible EventEmitter implementation
class EventEmitter {
  private events: { [key: string]: Function[] } = {};
  private maxListeners: number = 10;

  on(event: string, listener: Function) {
    if (!this.events[event]) {
      this.events[event] = [];
    }
    this.events[event].push(listener);
  }

  off(event: string, listener: Function) {
    if (!this.events[event]) return;
    this.events[event] = this.events[event].filter(l => l !== listener);
  }

  emit(event: string, ...args: any[]) {
    if (!this.events[event]) return;
    this.events[event].forEach(listener => listener(...args));
  }

  removeAllListeners(event?: string) {
    if (event) {
      delete this.events[event];
    } else {
      this.events = {};
    }
  }

  setMaxListeners(n: number) {
    this.maxListeners = n;
    return this;
  }

  getMaxListeners() {
    return this.maxListeners;
  }
}

export interface ProgressEvent {
  id: string;
  type: 'upload' | 'processing' | 'analysis' | 'training' | 'inference';
  status: 'pending' | 'in_progress' | 'completed' | 'error' | 'cancelled';
  progress: number; // 0-100
  message?: string;
  details?: {
    fileName?: string;
    fileSize?: number;
    bytesUploaded?: number;
    estimatedTimeRemaining?: number;
    currentStep?: string;
    totalSteps?: number;
  };
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  startTime: Date;
  endTime?: Date;
}

export interface ProgressSubscription {
  id: string;
  callback: (event: ProgressEvent) => void;
  filter?: {
    types?: ProgressEvent['type'][];
    statuses?: ProgressEvent['status'][];
  };
}

class UnifiedProgressMonitor extends EventEmitter {
  private static instance: UnifiedProgressMonitor;
  private activeOperations: Map<string, ProgressEvent> = new Map();
  private subscriptions: Map<string, ProgressSubscription> = new Map();
  private operationHistory: ProgressEvent[] = [];
  private maxHistorySize = 100;

  private constructor() {
    super();
    this.setMaxListeners(50); // Allow many subscribers
  }

  public static getInstance(): UnifiedProgressMonitor {
    if (!UnifiedProgressMonitor.instance) {
      UnifiedProgressMonitor.instance = new UnifiedProgressMonitor();
    }
    return UnifiedProgressMonitor.instance;
  }

  /**
   * Start tracking a new operation
   */
  startOperation(
    id: string,
    type: ProgressEvent['type'],
    message?: string,
    details?: ProgressEvent['details']
  ): void {
    const event: ProgressEvent = {
      id,
      type,
      status: 'pending',
      progress: 0,
      message,
      details,
      startTime: new Date()
    };

    this.activeOperations.set(id, event);
    this.emitEvent(event);
  }

  /**
   * Update operation progress
   */
  updateProgress(
    id: string,
    progress: number,
    message?: string,
    details?: Partial<ProgressEvent['details']>
  ): void {
    const operation = this.activeOperations.get(id);
    if (!operation) {
      console.warn(`Operation ${id} not found for progress update`);
      return;
    }

    const updatedEvent: ProgressEvent = {
      ...operation,
      status: progress >= 100 ? 'completed' : 'in_progress',
      progress: Math.min(100, Math.max(0, progress)),
      message: message || operation.message,
      details: details ? { ...operation.details, ...details } : operation.details,
      endTime: progress >= 100 ? new Date() : undefined
    };

    this.activeOperations.set(id, updatedEvent);
    this.emitEvent(updatedEvent);

    // Move to history if completed
    if (updatedEvent.status === 'completed') {
      this.completeOperation(id);
    }
  }

  /**
   * Mark operation as completed
   */
  completeOperation(id: string, message?: string): void {
    const operation = this.activeOperations.get(id);
    if (!operation) {
      console.warn(`Operation ${id} not found for completion`);
      return;
    }

    const completedEvent: ProgressEvent = {
      ...operation,
      status: 'completed',
      progress: 100,
      message: message || operation.message,
      endTime: new Date()
    };

    this.activeOperations.delete(id);
    this.addToHistory(completedEvent);
    this.emitEvent(completedEvent);
  }

  /**
   * Mark operation as failed
   */
  failOperation(id: string, error: ProgressEvent['error'], message?: string): void {
    const operation = this.activeOperations.get(id);
    if (!operation) {
      console.warn(`Operation ${id} not found for failure`);
      return;
    }

    const failedEvent: ProgressEvent = {
      ...operation,
      status: 'error',
      message: message || operation.message,
      error,
      endTime: new Date()
    };

    this.activeOperations.delete(id);
    this.addToHistory(failedEvent);
    this.emitEvent(failedEvent);
  }

  /**
   * Cancel an operation
   */
  cancelOperation(id: string, message?: string): void {
    const operation = this.activeOperations.get(id);
    if (!operation) {
      console.warn(`Operation ${id} not found for cancellation`);
      return;
    }

    const cancelledEvent: ProgressEvent = {
      ...operation,
      status: 'cancelled',
      message: message || 'Operation cancelled',
      endTime: new Date()
    };

    this.activeOperations.delete(id);
    this.addToHistory(cancelledEvent);
    this.emitEvent(cancelledEvent);
  }

  /**
   * Subscribe to progress events
   */
  subscribe(
    callback: (event: ProgressEvent) => void,
    filter?: ProgressSubscription['filter']
  ): string {
    const subscriptionId = `sub_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const subscription: ProgressSubscription = {
      id: subscriptionId,
      callback,
      filter
    };

    this.subscriptions.set(subscriptionId, subscription);
    return subscriptionId;
  }

  /**
   * Unsubscribe from progress events
   */
  unsubscribe(subscriptionId: string): void {
    this.subscriptions.delete(subscriptionId);
  }

  /**
   * Get current active operations
   */
  getActiveOperations(): ProgressEvent[] {
    return Array.from(this.activeOperations.values());
  }

  /**
   * Get operation history
   */
  getHistory(limit?: number): ProgressEvent[] {
    const history = [...this.operationHistory].reverse();
    return limit ? history.slice(0, limit) : history;
  }

  /**
   * Get specific operation status
   */
  getOperation(id: string): ProgressEvent | null {
    return this.activeOperations.get(id) || 
           this.operationHistory.find(op => op.id === id) || 
           null;
  }

  /**
   * Clear completed operations from history
   */
  clearHistory(): void {
    this.operationHistory = [];
  }

  /**
   * Get statistics about operations
   */
  getStatistics(): {
    active: number;
    completed: number;
    failed: number;
    cancelled: number;
    averageDuration: number;
    successRate: number;
  } {
    const active = this.activeOperations.size;
    const completed = this.operationHistory.filter(op => op.status === 'completed').length;
    const failed = this.operationHistory.filter(op => op.status === 'error').length;
    const cancelled = this.operationHistory.filter(op => op.status === 'cancelled').length;

    const completedOps = this.operationHistory.filter(op => 
      op.status === 'completed' && op.endTime && op.startTime
    );
    
    const averageDuration = completedOps.length > 0 
      ? completedOps.reduce((sum, op) => 
          sum + (op.endTime!.getTime() - op.startTime.getTime()), 0
        ) / completedOps.length / 1000 // Convert to seconds
      : 0;

    const total = completed + failed + cancelled;
    const successRate = total > 0 ? (completed / total) * 100 : 0;

    return {
      active,
      completed,
      failed,
      cancelled,
      averageDuration,
      successRate
    };
  }

  /**
   * Emit event to subscribers
   */
  private emitEvent(event: ProgressEvent): void {
    this.subscriptions.forEach(subscription => {
      // Apply filters
      if (subscription.filter) {
        const { types, statuses } = subscription.filter;
        
        if (types && !types.includes(event.type)) return;
        if (statuses && !statuses.includes(event.status)) return;
      }

      try {
        subscription.callback(event);
      } catch (error) {
        console.error(`Error in progress subscription ${subscription.id}:`, error);
      }
    });

    // Emit to EventEmitter listeners
    this.emit('progress', event);
    this.emit(`progress:${event.type}`, event);
    this.emit(`progress:${event.status}`, event);
  }

  /**
   * Add event to history
   */
  private addToHistory(event: ProgressEvent): void {
    this.operationHistory.push(event);
    
    // Maintain history size limit
    if (this.operationHistory.length > this.maxHistorySize) {
      this.operationHistory = this.operationHistory.slice(-this.maxHistorySize);
    }
  }
}

// Export singleton instance
export const unifiedProgressMonitor = UnifiedProgressMonitor.getInstance();

// Utility functions for common operations
export const ProgressUtils = {
  /**
   * Generate unique operation ID
   */
  generateId: (prefix: string = 'op'): string => {
    return `${prefix}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  },

  /**
   * Calculate estimated time remaining
   */
  calculateETA: (startTime: Date, progress: number): number => {
    if (progress <= 0) return 0;
    
    const elapsed = Date.now() - startTime.getTime();
    const rate = progress / elapsed;
    const remaining = (100 - progress) / rate;
    
    return Math.round(remaining / 1000); // Return in seconds
  },

  /**
   * Format duration for display
   */
  formatDuration: (seconds: number): string => {
    if (seconds < 60) return `${seconds}s`;
    if (seconds < 3600) return `${Math.floor(seconds / 60)}m ${seconds % 60}s`;
    return `${Math.floor(seconds / 3600)}h ${Math.floor((seconds % 3600) / 60)}m`;
  },

  /**
   * Format file size for display
   */
  formatFileSize: (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
};
