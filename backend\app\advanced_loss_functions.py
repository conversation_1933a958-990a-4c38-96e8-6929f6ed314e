"""Advanced Loss Functions for Hierarchical Multi-Label Classification.

This module implements sophisticated loss functions that enforce hierarchical
constraints and improve multi-label classification accuracy.
"""

import logging
import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import List, Dict, Any, Optional, Tuple
from collections import defaultdict

logger = logging.getLogger(__name__)

class HierarchicalLoss(nn.Module):
    """Hierarchical loss function that enforces parent-child relationships."""
    
    def __init__(
        self,
        hierarchy_matrix: torch.Tensor,
        alpha: float = 0.5,
        beta: float = 0.3,
        temperature: float = 1.0
    ):
        """
        Initialize hierarchical loss.
        
        Args:
            hierarchy_matrix: Binary matrix where hierarchy_matrix[i,j] = 1 if label i is parent of label j
            alpha: Weight for hierarchical constraint loss
            beta: Weight for consistency loss
            temperature: Temperature for softmax normalization
        """
        super().__init__()
        self.hierarchy_matrix = hierarchy_matrix
        self.alpha = alpha
        self.beta = beta
        self.temperature = temperature
        
    def forward(self, predictions: torch.Tensor, targets: torch.Tensor) -> torch.Tensor:
        """
        Compute hierarchical loss.
        
        Args:
            predictions: Model predictions [batch_size, num_labels]
            targets: Ground truth labels [batch_size, num_labels]
        
        Returns:
            Combined loss value
        """
        # Standard multi-label loss
        base_loss = F.binary_cross_entropy_with_logits(predictions, targets)
        
        # Hierarchical constraint loss
        hierarchy_loss = self._compute_hierarchy_violations(predictions, targets)
        
        # Consistency loss
        consistency_loss = self._compute_consistency_loss(predictions, targets)
        
        # Combine losses
        total_loss = base_loss + self.alpha * hierarchy_loss + self.beta * consistency_loss
        
        return total_loss
    
    def _compute_hierarchy_violations(
        self, 
        predictions: torch.Tensor, 
        targets: torch.Tensor
    ) -> torch.Tensor:
        """Compute loss for hierarchical constraint violations."""
        # Apply sigmoid to get probabilities
        pred_probs = torch.sigmoid(predictions / self.temperature)
        
        # For each child label, ensure parent labels have higher or equal probability
        violation_loss = 0.0
        
        for child_idx in range(self.hierarchy_matrix.size(1)):
            parent_indices = torch.where(self.hierarchy_matrix[:, child_idx] == 1)[0]
            
            if len(parent_indices) > 0:
                child_probs = pred_probs[:, child_idx]
                parent_probs = pred_probs[:, parent_indices]
                
                # Parent probabilities should be >= child probabilities
                max_parent_probs = torch.max(parent_probs, dim=1)[0]
                violations = F.relu(child_probs - max_parent_probs)
                violation_loss += torch.mean(violations)
        
        return violation_loss
    
    def _compute_consistency_loss(
        self, 
        predictions: torch.Tensor, 
        targets: torch.Tensor
    ) -> torch.Tensor:
        """Compute consistency loss to encourage coherent predictions."""
        pred_probs = torch.sigmoid(predictions / self.temperature)
        
        # Encourage consistency between related labels
        consistency_loss = 0.0
        
        # For labels that frequently co-occur, encourage similar probabilities
        for i in range(predictions.size(1)):
            for j in range(i + 1, predictions.size(1)):
                # Check if labels i and j are related (co-occur in targets)
                cooccurrence = torch.sum(targets[:, i] * targets[:, j])
                total_i = torch.sum(targets[:, i])
                
                if total_i > 0 and cooccurrence / total_i > 0.5:  # Strong co-occurrence
                    prob_diff = torch.abs(pred_probs[:, i] - pred_probs[:, j])
                    consistency_loss += torch.mean(prob_diff) * 0.1
        
        return consistency_loss

class FocalLoss(nn.Module):
    """Focal loss for handling class imbalance in multi-label classification."""
    
    def __init__(self, alpha: float = 1.0, gamma: float = 2.0, reduction: str = 'mean'):
        """
        Initialize focal loss.
        
        Args:
            alpha: Weighting factor for rare class
            gamma: Focusing parameter
            reduction: Reduction method ('mean', 'sum', 'none')
        """
        super().__init__()
        self.alpha = alpha
        self.gamma = gamma
        self.reduction = reduction
        
    def forward(self, predictions: torch.Tensor, targets: torch.Tensor) -> torch.Tensor:
        """Compute focal loss."""
        # Compute binary cross entropy
        bce_loss = F.binary_cross_entropy_with_logits(predictions, targets, reduction='none')
        
        # Compute probabilities
        probs = torch.sigmoid(predictions)
        
        # Compute focal weight
        pt = torch.where(targets == 1, probs, 1 - probs)
        focal_weight = self.alpha * (1 - pt) ** self.gamma
        
        # Apply focal weight
        focal_loss = focal_weight * bce_loss
        
        if self.reduction == 'mean':
            return torch.mean(focal_loss)
        elif self.reduction == 'sum':
            return torch.sum(focal_loss)
        else:
            return focal_loss

class AsymmetricLoss(nn.Module):
    """Asymmetric loss for multi-label classification with different penalties for FP and FN."""
    
    def __init__(
        self, 
        gamma_neg: float = 4.0, 
        gamma_pos: float = 1.0, 
        clip: float = 0.05, 
        eps: float = 1e-8
    ):
        """
        Initialize asymmetric loss.
        
        Args:
            gamma_neg: Focusing parameter for negative samples
            gamma_pos: Focusing parameter for positive samples
            clip: Probability clipping value
            eps: Small epsilon for numerical stability
        """
        super().__init__()
        self.gamma_neg = gamma_neg
        self.gamma_pos = gamma_pos
        self.clip = clip
        self.eps = eps
        
    def forward(self, predictions: torch.Tensor, targets: torch.Tensor) -> torch.Tensor:
        """Compute asymmetric loss."""
        # Compute probabilities
        probs = torch.sigmoid(predictions)
        
        # Probability clipping
        if self.clip is not None and self.clip > 0:
            probs = torch.clamp(probs, self.clip, 1 - self.clip)
        
        # Compute positive and negative components
        pos_loss = targets * torch.log(probs + self.eps) * (1 - probs) ** self.gamma_pos
        neg_loss = (1 - targets) * torch.log(1 - probs + self.eps) * probs ** self.gamma_neg
        
        loss = -pos_loss - neg_loss
        
        return torch.mean(loss)

class LabelSmoothingLoss(nn.Module):
    """Label smoothing loss for multi-label classification."""
    
    def __init__(self, smoothing: float = 0.1):
        """
        Initialize label smoothing loss.
        
        Args:
            smoothing: Smoothing factor (0.0 = no smoothing, 1.0 = maximum smoothing)
        """
        super().__init__()
        self.smoothing = smoothing
        
    def forward(self, predictions: torch.Tensor, targets: torch.Tensor) -> torch.Tensor:
        """Compute label smoothing loss."""
        # Apply label smoothing
        smoothed_targets = targets * (1 - self.smoothing) + 0.5 * self.smoothing
        
        # Compute binary cross entropy with smoothed targets
        loss = F.binary_cross_entropy_with_logits(predictions, smoothed_targets)
        
        return loss

class ContrastiveLoss(nn.Module):
    """Contrastive loss for learning better label representations."""
    
    def __init__(self, margin: float = 1.0, temperature: float = 0.1):
        """
        Initialize contrastive loss.
        
        Args:
            margin: Margin for negative pairs
            temperature: Temperature for similarity computation
        """
        super().__init__()
        self.margin = margin
        self.temperature = temperature
        
    def forward(
        self, 
        predictions: torch.Tensor, 
        targets: torch.Tensor,
        label_embeddings: Optional[torch.Tensor] = None
    ) -> torch.Tensor:
        """Compute contrastive loss."""
        if label_embeddings is None:
            # Use predictions as embeddings
            label_embeddings = predictions
        
        batch_size, num_labels = targets.shape
        
        # Compute pairwise similarities
        similarities = torch.matmul(label_embeddings, label_embeddings.t()) / self.temperature
        
        # Create positive and negative masks
        positive_mask = torch.matmul(targets, targets.t()) > 0  # Labels that co-occur
        negative_mask = ~positive_mask
        
        # Remove diagonal (self-similarity)
        mask = torch.eye(batch_size, device=targets.device).bool()
        positive_mask = positive_mask & ~mask
        negative_mask = negative_mask & ~mask
        
        # Compute positive and negative losses
        pos_similarities = similarities[positive_mask]
        neg_similarities = similarities[negative_mask]
        
        if len(pos_similarities) > 0 and len(neg_similarities) > 0:
            pos_loss = -torch.log(torch.sigmoid(pos_similarities) + 1e-8).mean()
            neg_loss = -torch.log(torch.sigmoid(-neg_similarities) + 1e-8).mean()
            
            return pos_loss + neg_loss
        else:
            return torch.tensor(0.0, device=targets.device, requires_grad=True)

class CombinedLoss(nn.Module):
    """Combined loss function that integrates multiple loss components."""
    
    def __init__(
        self,
        hierarchy_matrix: Optional[torch.Tensor] = None,
        loss_weights: Optional[Dict[str, float]] = None,
        **kwargs
    ):
        """
        Initialize combined loss.
        
        Args:
            hierarchy_matrix: Hierarchy matrix for hierarchical loss
            loss_weights: Weights for different loss components
            **kwargs: Additional parameters for individual losses
        """
        super().__init__()
        
        # Default loss weights
        self.loss_weights = loss_weights or {
            'bce': 1.0,
            'focal': 0.5,
            'hierarchical': 0.3,
            'asymmetric': 0.2,
            'label_smoothing': 0.1
        }
        
        # Initialize loss functions
        self.bce_loss = nn.BCEWithLogitsLoss()
        self.focal_loss = FocalLoss(**kwargs.get('focal_params', {}))
        self.asymmetric_loss = AsymmetricLoss(**kwargs.get('asymmetric_params', {}))
        self.label_smoothing_loss = LabelSmoothingLoss(**kwargs.get('smoothing_params', {}))
        
        if hierarchy_matrix is not None:
            self.hierarchical_loss = HierarchicalLoss(
                hierarchy_matrix, **kwargs.get('hierarchical_params', {})
            )
        else:
            self.hierarchical_loss = None
    
    def forward(self, predictions: torch.Tensor, targets: torch.Tensor) -> Dict[str, torch.Tensor]:
        """Compute combined loss and return individual components."""
        losses = {}
        
        # Binary cross entropy loss
        losses['bce'] = self.bce_loss(predictions, targets)
        
        # Focal loss
        losses['focal'] = self.focal_loss(predictions, targets)
        
        # Asymmetric loss
        losses['asymmetric'] = self.asymmetric_loss(predictions, targets)
        
        # Label smoothing loss
        losses['label_smoothing'] = self.label_smoothing_loss(predictions, targets)
        
        # Hierarchical loss (if hierarchy matrix is provided)
        if self.hierarchical_loss is not None:
            losses['hierarchical'] = self.hierarchical_loss(predictions, targets)
        
        # Compute weighted total loss
        total_loss = sum(
            self.loss_weights.get(name, 0.0) * loss 
            for name, loss in losses.items()
        )
        
        losses['total'] = total_loss
        
        return losses

def create_hierarchy_matrix(hierarchy_data: List[Dict[str, str]], all_labels: List[str]) -> torch.Tensor:
    """Create hierarchy matrix from hierarchy data."""
    num_labels = len(all_labels)
    hierarchy_matrix = torch.zeros(num_labels, num_labels)
    
    # Build parent-child relationships
    for row in hierarchy_data:
        labels_in_row = [label for label in row.values() if label.strip()]
        
        # Each label is parent of the next level labels
        for i in range(len(labels_in_row) - 1):
            parent_label = labels_in_row[i]
            child_label = labels_in_row[i + 1]
            
            if parent_label in all_labels and child_label in all_labels:
                parent_idx = all_labels.index(parent_label)
                child_idx = all_labels.index(child_label)
                hierarchy_matrix[parent_idx, child_idx] = 1
    
    return hierarchy_matrix

def get_loss_function(
    loss_type: str,
    hierarchy_data: Optional[List[Dict[str, str]]] = None,
    all_labels: Optional[List[str]] = None,
    **kwargs
) -> nn.Module:
    """Factory function to create loss functions."""
    
    if loss_type == 'bce':
        return nn.BCEWithLogitsLoss()
    
    elif loss_type == 'focal':
        return FocalLoss(**kwargs)
    
    elif loss_type == 'asymmetric':
        return AsymmetricLoss(**kwargs)
    
    elif loss_type == 'hierarchical':
        if hierarchy_data is None or all_labels is None:
            raise ValueError("Hierarchy data and labels required for hierarchical loss")
        
        hierarchy_matrix = create_hierarchy_matrix(hierarchy_data, all_labels)
        return HierarchicalLoss(hierarchy_matrix, **kwargs)
    
    elif loss_type == 'label_smoothing':
        return LabelSmoothingLoss(**kwargs)
    
    elif loss_type == 'combined':
        hierarchy_matrix = None
        if hierarchy_data is not None and all_labels is not None:
            hierarchy_matrix = create_hierarchy_matrix(hierarchy_data, all_labels)
        
        return CombinedLoss(hierarchy_matrix=hierarchy_matrix, **kwargs)
    
    else:
        raise ValueError(f"Unknown loss type: {loss_type}")

# Example usage and testing functions
def test_loss_functions():
    """Test the loss functions with sample data."""
    batch_size, num_labels = 4, 6
    
    # Create sample data
    predictions = torch.randn(batch_size, num_labels)
    targets = torch.randint(0, 2, (batch_size, num_labels)).float()
    
    # Test different loss functions
    losses = {}
    
    # BCE Loss
    bce_loss = nn.BCEWithLogitsLoss()
    losses['bce'] = bce_loss(predictions, targets)
    
    # Focal Loss
    focal_loss = FocalLoss()
    losses['focal'] = focal_loss(predictions, targets)
    
    # Asymmetric Loss
    asymmetric_loss = AsymmetricLoss()
    losses['asymmetric'] = asymmetric_loss(predictions, targets)
    
    # Label Smoothing Loss
    smoothing_loss = LabelSmoothingLoss()
    losses['smoothing'] = smoothing_loss(predictions, targets)
    
    # Print results
    for name, loss in losses.items():
        print(f"{name}: {loss.item():.4f}")
    
    return losses

if __name__ == "__main__":
    test_loss_functions()
