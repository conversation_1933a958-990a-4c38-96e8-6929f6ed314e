"""Large Dataset Performance Optimization System for ClassyWeb ML Platform.

This module provides comprehensive performance optimization for large datasets including
batch processing, memory management, distributed training capabilities, and advanced
streaming data processing for machine learning workflows.
"""

import logging
import time
import psutil
import gc
import torch
import asyncio
import multiprocessing as mp
from typing import Dict, List, Any, Optional, Callable, Union, Iterator, Generator
from functools import wraps
import threading
from dataclasses import dataclass
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor, as_completed
import numpy as np
import pandas as pd
from queue import Queue, Empty
import pickle
import tempfile
import os
from pathlib import Path
import mmap
import json

logger = logging.getLogger(__name__)


@dataclass
class LargeDatasetConfig:
    """Configuration for large dataset processing."""
    chunk_size: int = 10000
    batch_size: int = 1000
    max_workers: int = 4
    use_multiprocessing: bool = True
    memory_limit_gb: float = 8.0
    enable_streaming: bool = True
    cache_dir: Optional[str] = None
    compression: bool = True
    prefetch_factor: int = 2
    enable_memory_mapping: bool = True
    disk_cache_size_gb: float = 2.0


@dataclass
class DistributedConfig:
    """Configuration for distributed training."""
    world_size: int = 1
    rank: int = 0
    backend: str = "nccl"  # nccl, gloo, mpi
    init_method: str = "env://"
    enable_ddp: bool = False
    gradient_accumulation_steps: int = 1
    sync_bn: bool = False


@dataclass
class PerformanceMetrics:
    """Performance metrics for optimization tracking."""
    processing_time: float
    memory_usage_mb: float
    gpu_memory_mb: float
    throughput_samples_per_sec: float
    cpu_utilization: float
    gpu_utilization: float
    cache_hit_ratio: float
    io_wait_time: float
    disk_io_mb_per_sec: float


class StreamingDataLoader:
    """Memory-efficient streaming data loader for large datasets."""
    
    def __init__(self, data_source: Union[str, pd.DataFrame], config: LargeDatasetConfig):
        self.data_source = data_source
        self.config = config
        self.cache_dir = config.cache_dir or tempfile.mkdtemp()
        self.current_chunk = 0
        self.total_chunks = 0
        self._prepare_streaming()
    
    def _prepare_streaming(self):
        """Prepare data for streaming processing."""
        if isinstance(self.data_source, str):
            # File-based data source
            if self.data_source.endswith('.csv'):
                # Count total rows for chunk calculation
                with open(self.data_source, 'r') as f:
                    total_rows = sum(1 for _ in f) - 1  # Exclude header
                self.total_chunks = (total_rows + self.config.chunk_size - 1) // self.config.chunk_size
            else:
                raise ValueError(f"Unsupported file format: {self.data_source}")
        elif isinstance(self.data_source, pd.DataFrame):
            # DataFrame data source
            total_rows = len(self.data_source)
            self.total_chunks = (total_rows + self.config.chunk_size - 1) // self.config.chunk_size
        else:
            raise ValueError("Data source must be file path or DataFrame")
    
    def __iter__(self) -> Generator[pd.DataFrame, None, None]:
        """Iterate through data chunks."""
        self.current_chunk = 0
        
        if isinstance(self.data_source, str):
            # Stream from file
            chunk_reader = pd.read_csv(
                self.data_source,
                chunksize=self.config.chunk_size,
                iterator=True
            )
            
            for chunk in chunk_reader:
                yield chunk
                self.current_chunk += 1
                
                # Memory cleanup
                if self.current_chunk % 10 == 0:
                    gc.collect()
        
        elif isinstance(self.data_source, pd.DataFrame):
            # Stream from DataFrame
            for i in range(0, len(self.data_source), self.config.chunk_size):
                chunk = self.data_source.iloc[i:i + self.config.chunk_size]
                yield chunk
                self.current_chunk += 1
                
                # Memory cleanup
                if self.current_chunk % 10 == 0:
                    gc.collect()
    
    def get_progress(self) -> float:
        """Get current processing progress."""
        if self.total_chunks == 0:
            return 0.0
        return self.current_chunk / self.total_chunks


class BatchProcessor:
    """Optimized batch processor for large-scale operations."""
    
    def __init__(self, config: LargeDatasetConfig):
        self.config = config
        self.process_pool = None
        self.thread_pool = None
        self.memory_monitor = MemoryMonitor(config.memory_limit_gb)
    
    def __enter__(self):
        if self.config.use_multiprocessing:
            self.process_pool = ProcessPoolExecutor(max_workers=self.config.max_workers)
        else:
            self.thread_pool = ThreadPoolExecutor(max_workers=self.config.max_workers)
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.process_pool:
            self.process_pool.shutdown(wait=True)
        if self.thread_pool:
            self.thread_pool.shutdown(wait=True)
    
    async def process_batches_async(
        self,
        data_loader: StreamingDataLoader,
        process_func: Callable,
        progress_callback: Optional[Callable] = None
    ) -> List[Any]:
        """Process batches asynchronously with memory management."""
        results = []
        
        for chunk in data_loader:
            # Check memory before processing
            if self.memory_monitor.should_cleanup():
                gc.collect()
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()
            
            # Split chunk into batches
            batches = self._split_into_batches(chunk)
            
            # Process batches
            if self.config.use_multiprocessing and self.process_pool:
                # Multiprocessing
                futures = [
                    self.process_pool.submit(process_func, batch)
                    for batch in batches
                ]
                
                for future in as_completed(futures):
                    try:
                        result = future.result()
                        results.append(result)
                    except Exception as e:
                        logger.error(f"Batch processing error: {e}")
            
            else:
                # Threading or sequential
                if self.thread_pool:
                    futures = [
                        self.thread_pool.submit(process_func, batch)
                        for batch in batches
                    ]
                    
                    for future in as_completed(futures):
                        try:
                            result = future.result()
                            results.append(result)
                        except Exception as e:
                            logger.error(f"Batch processing error: {e}")
                else:
                    # Sequential processing
                    for batch in batches:
                        try:
                            result = process_func(batch)
                            results.append(result)
                        except Exception as e:
                            logger.error(f"Batch processing error: {e}")
            
            # Progress callback
            if progress_callback:
                progress = data_loader.get_progress()
                await progress_callback({"progress": progress, "chunk": data_loader.current_chunk})
        
        return results
    
    def _split_into_batches(self, chunk: pd.DataFrame) -> List[pd.DataFrame]:
        """Split chunk into smaller batches."""
        batches = []
        for i in range(0, len(chunk), self.config.batch_size):
            batch = chunk.iloc[i:i + self.config.batch_size]
            batches.append(batch)
        return batches


class MemoryMonitor:
    """Advanced memory monitoring and management."""
    
    def __init__(self, memory_limit_gb: float):
        self.memory_limit_bytes = memory_limit_gb * 1024 * 1024 * 1024
        self.process = psutil.Process()
        self.cleanup_threshold = 0.8  # Cleanup at 80% of limit
    
    def get_current_usage(self) -> Dict[str, float]:
        """Get current memory usage statistics."""
        memory_info = self.process.memory_info()
        virtual_memory = psutil.virtual_memory()
        
        return {
            'process_memory_mb': memory_info.rss / 1024 / 1024,
            'process_memory_percent': self.process.memory_percent(),
            'system_memory_percent': virtual_memory.percent,
            'available_memory_mb': virtual_memory.available / 1024 / 1024,
            'gpu_memory_mb': self._get_gpu_memory() if torch.cuda.is_available() else 0
        }
    
    def should_cleanup(self) -> bool:
        """Check if memory cleanup is needed."""
        current_usage = self.process.memory_info().rss
        return current_usage > (self.memory_limit_bytes * self.cleanup_threshold)
    
    def force_cleanup(self):
        """Force memory cleanup."""
        gc.collect()
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
            torch.cuda.synchronize()
    
    def _get_gpu_memory(self) -> float:
        """Get GPU memory usage in MB."""
        try:
            return torch.cuda.memory_allocated() / 1024 / 1024
        except Exception:
            return 0.0


class DiskCache:
    """Disk-based caching system for large datasets."""
    
    def __init__(self, cache_dir: str, max_size_gb: float = 2.0):
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        self.max_size_bytes = max_size_gb * 1024 * 1024 * 1024
        self.index_file = self.cache_dir / "cache_index.json"
        self.index = self._load_index()
    
    def _load_index(self) -> Dict[str, Any]:
        """Load cache index from disk."""
        if self.index_file.exists():
            with open(self.index_file, 'r') as f:
                return json.load(f)
        return {}
    
    def _save_index(self):
        """Save cache index to disk."""
        with open(self.index_file, 'w') as f:
            json.dump(self.index, f)
    
    def get(self, key: str) -> Optional[Any]:
        """Get item from cache."""
        if key not in self.index:
            return None
        
        cache_file = self.cache_dir / f"{key}.pkl"
        if not cache_file.exists():
            # Remove from index if file doesn't exist
            del self.index[key]
            self._save_index()
            return None
        
        try:
            with open(cache_file, 'rb') as f:
                return pickle.load(f)
        except Exception as e:
            logger.error(f"Error loading from cache: {e}")
            return None
    
    def put(self, key: str, value: Any):
        """Put item in cache."""
        cache_file = self.cache_dir / f"{key}.pkl"
        
        try:
            with open(cache_file, 'wb') as f:
                pickle.dump(value, f)
            
            # Update index
            self.index[key] = {
                'size': cache_file.stat().st_size,
                'timestamp': time.time()
            }
            self._save_index()
            
            # Check if cleanup is needed
            self._cleanup_if_needed()
            
        except Exception as e:
            logger.error(f"Error saving to cache: {e}")
    
    def _cleanup_if_needed(self):
        """Cleanup cache if size limit exceeded."""
        total_size = sum(item['size'] for item in self.index.values())
        
        if total_size > self.max_size_bytes:
            # Remove oldest items
            sorted_items = sorted(
                self.index.items(),
                key=lambda x: x[1]['timestamp']
            )
            
            for key, _ in sorted_items:
                cache_file = self.cache_dir / f"{key}.pkl"
                if cache_file.exists():
                    cache_file.unlink()
                del self.index[key]
                
                # Check if we're under the limit
                remaining_size = sum(item['size'] for item in self.index.values())
                if remaining_size <= self.max_size_bytes * 0.8:  # 80% of limit
                    break
            
            self._save_index()


class LargeDatasetOptimizer:
    """Main optimizer class for large dataset processing."""
    
    def __init__(self, config: LargeDatasetConfig):
        self.config = config
        self.memory_monitor = MemoryMonitor(config.memory_limit_gb)
        self.disk_cache = DiskCache(
            config.cache_dir or tempfile.mkdtemp(),
            config.disk_cache_size_gb
        ) if config.cache_dir else None
        self.performance_metrics = []
    
    def create_data_loader(self, data_source: Union[str, pd.DataFrame]) -> StreamingDataLoader:
        """Create optimized data loader for large datasets."""
        return StreamingDataLoader(data_source, self.config)
    
    def create_batch_processor(self) -> BatchProcessor:
        """Create optimized batch processor."""
        return BatchProcessor(self.config)
    
    async def optimize_training_pipeline(
        self,
        data_source: Union[str, pd.DataFrame],
        training_func: Callable,
        progress_callback: Optional[Callable] = None
    ) -> Dict[str, Any]:
        """Optimize entire training pipeline for large datasets."""
        start_time = time.time()
        
        # Create optimized components
        data_loader = self.create_data_loader(data_source)
        
        with self.create_batch_processor() as batch_processor:
            # Process data in optimized batches
            results = await batch_processor.process_batches_async(
                data_loader,
                training_func,
                progress_callback
            )
        
        # Calculate performance metrics
        total_time = time.time() - start_time
        memory_stats = self.memory_monitor.get_current_usage()
        
        metrics = PerformanceMetrics(
            processing_time=total_time,
            memory_usage_mb=memory_stats['process_memory_mb'],
            gpu_memory_mb=memory_stats['gpu_memory_mb'],
            throughput_samples_per_sec=len(results) / total_time if total_time > 0 else 0,
            cpu_utilization=psutil.cpu_percent(),
            gpu_utilization=self._get_gpu_utilization(),
            cache_hit_ratio=0.0,  # TODO: Implement cache hit tracking
            io_wait_time=0.0,  # TODO: Implement IO wait tracking
            disk_io_mb_per_sec=0.0  # TODO: Implement disk IO tracking
        )
        
        self.performance_metrics.append(metrics)
        
        return {
            'results': results,
            'metrics': metrics,
            'total_chunks_processed': data_loader.total_chunks,
            'memory_peak_mb': memory_stats['process_memory_mb']
        }
    
    def _get_gpu_utilization(self) -> float:
        """Get GPU utilization percentage."""
        try:
            import pynvml
            pynvml.nvmlInit()
            handle = pynvml.nvmlDeviceGetHandleByIndex(0)
            utilization = pynvml.nvmlDeviceGetUtilizationRates(handle)
            return float(utilization.gpu)
        except Exception:
            return 0.0
    
    def get_optimization_report(self) -> Dict[str, Any]:
        """Generate comprehensive optimization report."""
        if not self.performance_metrics:
            return {"error": "No performance metrics available"}
        
        latest_metrics = self.performance_metrics[-1]
        
        return {
            'configuration': {
                'chunk_size': self.config.chunk_size,
                'batch_size': self.config.batch_size,
                'max_workers': self.config.max_workers,
                'memory_limit_gb': self.config.memory_limit_gb,
                'use_multiprocessing': self.config.use_multiprocessing
            },
            'performance': {
                'processing_time': latest_metrics.processing_time,
                'throughput_samples_per_sec': latest_metrics.throughput_samples_per_sec,
                'memory_usage_mb': latest_metrics.memory_usage_mb,
                'gpu_memory_mb': latest_metrics.gpu_memory_mb,
                'cpu_utilization': latest_metrics.cpu_utilization,
                'gpu_utilization': latest_metrics.gpu_utilization
            },
            'recommendations': self._generate_recommendations()
        }
    
    def _generate_recommendations(self) -> List[str]:
        """Generate optimization recommendations based on metrics."""
        recommendations = []
        
        if not self.performance_metrics:
            return recommendations
        
        latest_metrics = self.performance_metrics[-1]
        
        # Memory recommendations
        if latest_metrics.memory_usage_mb > self.config.memory_limit_gb * 1024 * 0.9:
            recommendations.append("Consider reducing chunk_size or batch_size to lower memory usage")
        
        # CPU recommendations
        if latest_metrics.cpu_utilization < 50:
            recommendations.append("Consider increasing max_workers to better utilize CPU")
        
        # GPU recommendations
        if latest_metrics.gpu_utilization < 50 and torch.cuda.is_available():
            recommendations.append("Consider increasing batch_size to better utilize GPU")
        
        # Throughput recommendations
        if latest_metrics.throughput_samples_per_sec < 100:
            recommendations.append("Consider enabling multiprocessing or increasing batch_size")
        
        return recommendations
