/**
 * DataPurposeSelector.tsx
 * 
 * Component for selecting data purposes with smart recommendations
 * and visual guidance for optimal data usage patterns.
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { Progress } from "@/components/ui/progress";
import {
  Database,
  Brain,
  Target,
  CheckCircle2,
  AlertCircle,
  Info,
  Lightbulb,
  TrendingUp,
  Users,
  Zap,
  FileText,
  BarChart3
} from "lucide-react";

import { DataPurpose, DataPurposeSuggestion } from "@/services/unifiedDataManager";
import { UploadedFile } from "@/services/fileUploadApi";

interface DataPurposeSelectorProps {
  fileInfo?: UploadedFile;
  suggestions?: DataPurposeSuggestion;
  selectedPurposes: DataPurpose[];
  onPurposesChange: (purposes: DataPurpose[]) => void;
  requiredPurposes?: DataPurpose[];
  className?: string;
  showRecommendations?: boolean;
  showDataQualityMetrics?: boolean;
}

interface PurposeInfo {
  id: DataPurpose;
  title: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  color: string;
  bgColor: string;
  requirements: string[];
  benefits: string[];
  minDataSize: number;
  optimalDataSize: number;
}

const purposeDefinitions: Record<DataPurpose, PurposeInfo> = {
  analysis: {
    id: 'analysis',
    title: 'Data Analysis',
    description: 'Explore data structure, detect patterns, and understand your dataset',
    icon: Database,
    color: 'text-blue-600',
    bgColor: 'bg-blue-50 border-blue-200',
    requirements: ['Any structured data format', 'Column headers recommended'],
    benefits: ['Smart column detection', 'Data quality assessment', 'Pattern recognition'],
    minDataSize: 10,
    optimalDataSize: 100
  },
  training: {
    id: 'training',
    title: 'Model Training',
    description: 'Train custom machine learning models on your labeled data',
    icon: Brain,
    color: 'text-green-600',
    bgColor: 'bg-green-50 border-green-200',
    requirements: ['Labeled data required', 'Text and label columns', 'Minimum 50 samples per class'],
    benefits: ['Custom model creation', 'High accuracy potential', 'Domain-specific optimization'],
    minDataSize: 100,
    optimalDataSize: 1000
  },
  classification: {
    id: 'classification',
    title: 'Classification Inference',
    description: 'Apply trained models or LLMs to classify new unlabeled data',
    icon: Target,
    color: 'text-purple-600',
    bgColor: 'bg-purple-50 border-purple-200',
    requirements: ['Text data for classification', 'No labels required'],
    benefits: ['Instant predictions', 'Batch processing', 'Export results'],
    minDataSize: 1,
    optimalDataSize: 10000
  }
};

export const DataPurposeSelector: React.FC<DataPurposeSelectorProps> = ({
  fileInfo,
  suggestions,
  selectedPurposes,
  onPurposesChange,
  requiredPurposes = [],
  className = '',
  showRecommendations = true,
  showDataQualityMetrics = true
}) => {
  const [hoveredPurpose, setHoveredPurpose] = useState<DataPurpose | null>(null);

  const handlePurposeToggle = (purpose: DataPurpose, checked: boolean) => {
    if (checked) {
      onPurposesChange([...selectedPurposes, purpose]);
    } else {
      // Don't allow removing required purposes
      if (!requiredPurposes.includes(purpose)) {
        onPurposesChange(selectedPurposes.filter(p => p !== purpose));
      }
    }
  };

  const isPurposeRecommended = (purpose: DataPurpose): boolean => {
    if (!suggestions) return false;
    return suggestions.suggestedPurposes.includes(purpose);
  };

  const isPurposeOptimal = (purpose: DataPurpose): boolean => {
    if (!fileInfo) return false;
    const purposeInfo = purposeDefinitions[purpose];
    return fileInfo.num_rows >= purposeInfo.optimalDataSize;
  };

  const isPurposeViable = (purpose: DataPurpose): boolean => {
    if (!fileInfo) return true;
    const purposeInfo = purposeDefinitions[purpose];
    return fileInfo.num_rows >= purposeInfo.minDataSize;
  };

  const getDataQualityScore = (): number => {
    if (!fileInfo || !suggestions) return 0;
    
    let score = 0;
    
    // Base score from data size
    if (fileInfo.num_rows >= 1000) score += 30;
    else if (fileInfo.num_rows >= 100) score += 20;
    else if (fileInfo.num_rows >= 10) score += 10;
    
    // Score from having labels
    if (suggestions.hasLabels) score += 25;
    
    // Score from data structure
    if (fileInfo.columns && fileInfo.columns.length >= 2) score += 15;
    
    // Score from recommendations
    if (suggestions.canUseForTraining) score += 20;
    if (suggestions.canUseForClassification) score += 10;
    
    return Math.min(score, 100);
  };

  const getQualityColor = (score: number): string => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getQualityLabel = (score: number): string => {
    if (score >= 80) return 'Excellent';
    if (score >= 60) return 'Good';
    if (score >= 40) return 'Fair';
    return 'Poor';
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Data Quality Overview */}
      {showDataQualityMetrics && fileInfo && suggestions && (
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-lg">
              <BarChart3 className="w-5 h-5" />
              Data Quality Assessment
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-primary">
                  {fileInfo.num_rows.toLocaleString()}
                </div>
                <div className="text-sm text-muted-foreground">Total Rows</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-primary">
                  {fileInfo.columns?.length || 0}
                </div>
                <div className="text-sm text-muted-foreground">Columns</div>
              </div>
              <div className="text-center">
                <div className={`text-2xl font-bold ${getQualityColor(getDataQualityScore())}`}>
                  {getDataQualityScore()}%
                </div>
                <div className="text-sm text-muted-foreground">
                  Quality Score ({getQualityLabel(getDataQualityScore())})
                </div>
              </div>
            </div>
            
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Data Quality</span>
                <span className={getQualityColor(getDataQualityScore())}>
                  {getQualityLabel(getDataQualityScore())}
                </span>
              </div>
              <Progress value={getDataQualityScore()} className="h-2" />
            </div>
          </CardContent>
        </Card>
      )}

      {/* Purpose Selection */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="w-5 h-5" />
            Select Data Usage Purposes
          </CardTitle>
          <CardDescription>
            Choose how you want to use your data. You can select multiple purposes.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid gap-4">
            {(Object.values(purposeDefinitions) as PurposeInfo[]).map((purposeInfo) => {
              const isSelected = selectedPurposes.includes(purposeInfo.id);
              const isRequired = requiredPurposes.includes(purposeInfo.id);
              const isRecommended = isPurposeRecommended(purposeInfo.id);
              const isOptimal = isPurposeOptimal(purposeInfo.id);
              const isViable = isPurposeViable(purposeInfo.id);
              const Icon = purposeInfo.icon;

              return (
                <div
                  key={purposeInfo.id}
                  className={`
                    relative p-4 border rounded-lg transition-all cursor-pointer
                    ${isSelected 
                      ? `${purposeInfo.bgColor} border-current` 
                      : 'border-border hover:border-primary/50 hover:bg-muted/50'
                    }
                    ${!isViable ? 'opacity-60' : ''}
                  `}
                  onMouseEnter={() => setHoveredPurpose(purposeInfo.id)}
                  onMouseLeave={() => setHoveredPurpose(null)}
                  onClick={() => handlePurposeToggle(purposeInfo.id, !isSelected)}
                >
                  {/* Badges */}
                  <div className="absolute top-2 right-2 flex gap-1">
                    {isRequired && (
                      <Badge variant="secondary" className="text-xs">
                        Required
                      </Badge>
                    )}
                    {isRecommended && (
                      <Badge variant="default" className="text-xs bg-green-100 text-green-800">
                        Recommended
                      </Badge>
                    )}
                    {isOptimal && (
                      <Badge variant="outline" className="text-xs border-green-500 text-green-700">
                        Optimal
                      </Badge>
                    )}
                  </div>

                  <div className="flex items-start gap-4">
                    {/* Checkbox and Icon */}
                    <div className="flex items-center gap-3 pt-1">
                      <Checkbox
                        checked={isSelected}
                        disabled={isRequired || !isViable}
                        onCheckedChange={(checked) => 
                          handlePurposeToggle(purposeInfo.id, checked as boolean)
                        }
                      />
                      <div className={`w-10 h-10 rounded-lg ${purposeInfo.bgColor} flex items-center justify-center`}>
                        <Icon className={`w-5 h-5 ${purposeInfo.color}`} />
                      </div>
                    </div>

                    {/* Content */}
                    <div className="flex-1 space-y-2">
                      <div>
                        <h4 className="font-semibold">{purposeInfo.title}</h4>
                        <p className="text-sm text-muted-foreground">
                          {purposeInfo.description}
                        </p>
                      </div>

                      {/* Data Size Indicator */}
                      {fileInfo && (
                        <div className="flex items-center gap-2 text-xs">
                          <Users className="w-3 h-3" />
                          <span>
                            Your data: {fileInfo.num_rows.toLocaleString()} rows
                            {isOptimal && <span className="text-green-600 ml-1">(Optimal)</span>}
                            {!isViable && <span className="text-red-600 ml-1">(Insufficient)</span>}
                          </span>
                        </div>
                      )}

                      {/* Expanded Details on Hover */}
                      {hoveredPurpose === purposeInfo.id && (
                        <div className="mt-3 pt-3 border-t space-y-2">
                          <div>
                            <h5 className="text-xs font-medium text-muted-foreground mb-1">
                              REQUIREMENTS
                            </h5>
                            <ul className="text-xs space-y-1">
                              {purposeInfo.requirements.map((req, index) => (
                                <li key={index} className="flex items-start gap-1">
                                  <CheckCircle2 className="w-3 h-3 text-green-500 mt-0.5 flex-shrink-0" />
                                  {req}
                                </li>
                              ))}
                            </ul>
                          </div>
                          <div>
                            <h5 className="text-xs font-medium text-muted-foreground mb-1">
                              BENEFITS
                            </h5>
                            <ul className="text-xs space-y-1">
                              {purposeInfo.benefits.map((benefit, index) => (
                                <li key={index} className="flex items-start gap-1">
                                  <TrendingUp className="w-3 h-3 text-blue-500 mt-0.5 flex-shrink-0" />
                                  {benefit}
                                </li>
                              ))}
                            </ul>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Smart Recommendations */}
      {showRecommendations && suggestions && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Lightbulb className="w-5 h-5" />
              Smart Recommendations
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Positive Recommendations */}
            {suggestions.recommendations.length > 0 && (
              <Alert>
                <Info className="h-4 w-4" />
                <AlertDescription>
                  <div className="space-y-2">
                    <p className="font-medium">Based on your data analysis:</p>
                    <ul className="text-sm space-y-1">
                      {suggestions.recommendations.map((rec, index) => (
                        <li key={index} className="flex items-start gap-2">
                          <CheckCircle2 className="w-3 h-3 text-green-500 mt-0.5 flex-shrink-0" />
                          {rec}
                        </li>
                      ))}
                    </ul>
                  </div>
                </AlertDescription>
              </Alert>
            )}

            {/* Warnings */}
            {suggestions.warnings.length > 0 && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  <div className="space-y-2">
                    <p className="font-medium">Important considerations:</p>
                    <ul className="text-sm space-y-1">
                      {suggestions.warnings.map((warning, index) => (
                        <li key={index} className="flex items-start gap-2">
                          <AlertCircle className="w-3 h-3 text-amber-500 mt-0.5 flex-shrink-0" />
                          {warning}
                        </li>
                      ))}
                    </ul>
                  </div>
                </AlertDescription>
              </Alert>
            )}

            {/* Usage Suggestions */}
            <div className="p-4 bg-muted/50 rounded-lg">
              <h4 className="font-medium mb-2 flex items-center gap-2">
                <Zap className="w-4 h-4" />
                Optimal Usage Pattern
              </h4>
              <div className="text-sm text-muted-foreground space-y-1">
                {suggestions.canUseForTraining ? (
                  <p>✅ Your data is suitable for training custom models for high accuracy</p>
                ) : (
                  <p>💡 Consider using LLM inference for unlabeled data classification</p>
                )}
                {suggestions.canUseForClassification && (
                  <p>✅ Perfect for batch classification of new data</p>
                )}
                <p>📊 Always start with data analysis to understand your dataset</p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Selected Purposes Summary */}
      {selectedPurposes.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Selected Purposes</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-2">
              {selectedPurposes.map((purpose) => {
                const purposeInfo = purposeDefinitions[purpose];
                const Icon = purposeInfo.icon;
                return (
                  <Badge
                    key={purpose}
                    variant="outline"
                    className={`${purposeInfo.bgColor} ${purposeInfo.color} border-current px-3 py-1`}
                  >
                    <Icon className="w-3 h-3 mr-1" />
                    {purposeInfo.title}
                  </Badge>
                );
              })}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
