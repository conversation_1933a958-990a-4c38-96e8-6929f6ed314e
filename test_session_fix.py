#!/usr/bin/env python3
"""
Simple test to verify the SQLAlchemy session fix for LLM classification.

This script simulates the exact scenario that was causing the DetachedInstanceError.
"""

import sys
import os
from pathlib import Path

# Add backend to path
backend_dir = Path(__file__).parent / "backend"
sys.path.insert(0, str(backend_dir))

def test_session_fix():
    """Test the session fix by simulating the problematic scenario."""
    print("🔧 Testing SQLAlchemy Session Fix")
    print("=" * 40)
    
    try:
        # Set minimal environment
        os.environ["TESTING"] = "true"
        os.environ["DATABASE_URL"] = "sqlite:///./test_fix.db"
        os.environ["JWT_SECRET_KEY"] = "test-key"
        
        # Import after setting environment
        from app.database import SessionLocal, get_file
        from app.models.file import File
        
        print("✅ Imports successful")
        
        # Simulate the problematic scenario
        with SessionLocal() as db:
            # Create a mock file object (this simulates getting a file from the database)
            mock_file = File(
                id="test-file-id",
                filename="test.csv",
                file_path="/tmp/test.csv",
                columns=["text", "label"],
                num_rows=10,
                user_id=1
            )
            
            # Add to session (this simulates the file being fetched from DB)
            db.add(mock_file)
            db.commit()
            db.refresh(mock_file)
            
            print("✅ Mock file created in session")
            
            # Now simulate what happens in the background task
            # The old code would try to access mock_file.file_path outside the session
            # The new code re-fetches the file within the background task session
            
            def simulate_background_task():
                """Simulate the background task that was failing."""
                with SessionLocal() as bg_db:
                    # OLD CODE (would fail):
                    # file_path = mock_file.file_path  # DetachedInstanceError!
                    
                    # NEW CODE (works):
                    bg_file = get_file(bg_db, "test-file-id")
                    if bg_file:
                        file_path = bg_file.file_path  # This works!
                        print(f"✅ Successfully accessed file path: {file_path}")
                        return True
                    else:
                        print("❌ File not found in background session")
                        return False
            
            # Test the fix
            success = simulate_background_task()
            
            if success:
                print("✅ Session fix working correctly!")
                print("   - File can be accessed in background task")
                print("   - No DetachedInstanceError occurred")
                return True
            else:
                print("❌ Session fix not working")
                return False
                
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        return False
    finally:
        # Cleanup
        try:
            test_db = Path("test_fix.db")
            if test_db.exists():
                test_db.unlink()
        except:
            pass

def main():
    """Main function."""
    print("🚀 SQLAlchemy Session Fix Test")
    print("=" * 50)
    
    success = test_session_fix()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 SUCCESS: The session fix is working!")
        print("\nThe LLM classification should now work without")
        print("DetachedInstanceError. You can test it by:")
        print("1. Starting the backend server")
        print("2. Uploading a file")
        print("3. Running LLM classification")
        return 0
    else:
        print("💥 FAILED: The session fix needs more work")
        return 1

if __name__ == "__main__":
    sys.exit(main())
