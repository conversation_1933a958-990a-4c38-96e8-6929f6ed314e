/**
 * workflowProgressManager.ts
 * 
 * Progress persistence and resume functionality for ClassyWeb workflows
 * Implements Phase 4 requirements for workflow state management and recovery
 */

import { UploadedFile } from './fileUploadApi';

export interface WorkflowProgressState {
  workflowId: string;
  workflowType: 'binary' | 'multiclass' | 'multilabel' | 'hierarchical' | 'flat' | 'beginner' | 'expert';
  currentStep: number;
  totalSteps: number;
  completedSteps: number[];
  lastUpdated: string;
  
  // File data
  uploadedFiles: {
    [purpose: string]: UploadedFile;
  };
  
  // Configuration state
  configuration: {
    selectedColumns?: {
      text?: string;
      label?: string | string[];
      hierarchy?: Record<string, string>;
    };
    trainingMethod?: string;
    llmConfig?: {
      provider?: string;
      model?: string;
      customPrompt?: string;
    };
    trainingParams?: Record<string, any>;
    categories?: string[];
    thresholds?: Record<string, number>;
  };
  
  // Results and analysis
  analysis?: any;
  recommendations?: any;
  trainingResults?: any;
  classificationResults?: any;
  
  // UI state
  uiState: {
    expandedSections?: string[];
    selectedTabs?: Record<string, string>;
    formData?: Record<string, any>;
  };
  
  // Metadata
  metadata: {
    userAgent: string;
    sessionId: string;
    startTime: string;
    estimatedCompletion?: string;
    autoSaveEnabled: boolean;
  };
}

export interface WorkflowResumeInfo {
  workflowId: string;
  workflowType: string;
  currentStep: number;
  lastUpdated: string;
  progressPercentage: number;
  canResume: boolean;
  hasFiles: boolean;
  hasConfiguration: boolean;
  estimatedTimeRemaining?: string;
}

/**
 * Workflow progress manager for persistence and resume functionality
 */
export class WorkflowProgressManager {
  private static instance: WorkflowProgressManager;
  private readonly STORAGE_KEY_PREFIX = 'classyweb-workflow-';
  private readonly MAX_STORED_WORKFLOWS = 10;
  private readonly AUTO_SAVE_INTERVAL = 30000; // 30 seconds
  
  private autoSaveTimers: Map<string, NodeJS.Timeout> = new Map();
  private currentWorkflowId: string | null = null;

  private constructor() {
    // Clean up old workflows on initialization
    this.cleanupOldWorkflows();
  }

  static getInstance(): WorkflowProgressManager {
    if (!WorkflowProgressManager.instance) {
      WorkflowProgressManager.instance = new WorkflowProgressManager();
    }
    return WorkflowProgressManager.instance;
  }

  /**
   * Start a new workflow session
   */
  startWorkflow(
    workflowType: WorkflowProgressState['workflowType'],
    totalSteps: number,
    sessionId?: string
  ): string {
    const workflowId = `${workflowType}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    
    const initialState: WorkflowProgressState = {
      workflowId,
      workflowType,
      currentStep: 1,
      totalSteps,
      completedSteps: [],
      lastUpdated: new Date().toISOString(),
      uploadedFiles: {},
      configuration: {},
      uiState: {},
      metadata: {
        userAgent: navigator.userAgent,
        sessionId: sessionId || `session-${Date.now()}`,
        startTime: new Date().toISOString(),
        autoSaveEnabled: true
      }
    };

    this.saveWorkflowState(workflowId, initialState);
    this.currentWorkflowId = workflowId;
    this.enableAutoSave(workflowId);
    
    return workflowId;
  }

  /**
   * Update workflow progress
   */
  updateProgress(
    workflowId: string,
    updates: Partial<WorkflowProgressState>
  ): boolean {
    try {
      const currentState = this.getWorkflowState(workflowId);
      if (!currentState) {
        console.warn(`Workflow ${workflowId} not found`);
        return false;
      }

      const updatedState: WorkflowProgressState = {
        ...currentState,
        ...updates,
        lastUpdated: new Date().toISOString(),
        // Deep merge configuration and uiState
        configuration: {
          ...currentState.configuration,
          ...updates.configuration
        },
        uiState: {
          ...currentState.uiState,
          ...updates.uiState
        }
      };

      this.saveWorkflowState(workflowId, updatedState);
      return true;
    } catch (error) {
      console.error('Failed to update workflow progress:', error);
      return false;
    }
  }

  /**
   * Get workflow state
   */
  getWorkflowState(workflowId: string): WorkflowProgressState | null {
    try {
      const stored = localStorage.getItem(`${this.STORAGE_KEY_PREFIX}${workflowId}`);
      if (!stored) return null;

      return JSON.parse(stored);
    } catch (error) {
      console.error('Failed to get workflow state:', error);
      return null;
    }
  }

  /**
   * Get all workflow states
   */
  getAllWorkflowStates(): WorkflowProgressState[] {
    const states: WorkflowProgressState[] = [];

    try {
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key?.startsWith(this.STORAGE_KEY_PREFIX)) {
          const workflowId = key.replace(this.STORAGE_KEY_PREFIX, '');
          const state = this.getWorkflowState(workflowId);
          if (state) {
            states.push(state);
          }
        }
      }
    } catch (error) {
      console.error('Error getting all workflow states:', error);
    }

    return states;
  }

  /**
   * Get all resumable workflows
   */
  getResumableWorkflows(): WorkflowResumeInfo[] {
    const workflows: WorkflowResumeInfo[] = [];
    
    try {
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key?.startsWith(this.STORAGE_KEY_PREFIX)) {
          const workflowId = key.replace(this.STORAGE_KEY_PREFIX, '');
          const state = this.getWorkflowState(workflowId);
          
          if (state) {
            const progressPercentage = (state.completedSteps.length / state.totalSteps) * 100;
            
            workflows.push({
              workflowId,
              workflowType: state.workflowType,
              currentStep: state.currentStep,
              lastUpdated: state.lastUpdated,
              progressPercentage,
              canResume: progressPercentage > 0 && progressPercentage < 100,
              hasFiles: Object.keys(state.uploadedFiles).length > 0,
              hasConfiguration: Object.keys(state.configuration).length > 0,
              estimatedTimeRemaining: this.calculateEstimatedTime(state)
            });
          }
        }
      }
      
      // Sort by last updated (most recent first)
      return workflows.sort((a, b) => 
        new Date(b.lastUpdated).getTime() - new Date(a.lastUpdated).getTime()
      );
    } catch (error) {
      console.error('Failed to get resumable workflows:', error);
      return [];
    }
  }

  /**
   * Check if any workflow can be resumed
   */
  canResumeWorkflow(): boolean {
    const states = this.getAllWorkflowStates();
    return states.some(state =>
      state.status === 'in_progress' ||
      (state.status === 'paused' && state.currentStep < state.totalSteps)
    );
  }

  /**
   * Resume a workflow
   */
  resumeWorkflow(workflowId: string): WorkflowProgressState | null {
    const state = this.getWorkflowState(workflowId);
    if (!state) return null;

    this.currentWorkflowId = workflowId;
    this.enableAutoSave(workflowId);
    
    // Update last accessed time
    this.updateProgress(workflowId, {
      lastUpdated: new Date().toISOString()
    });

    return state;
  }

  /**
   * Delete a workflow
   */
  deleteWorkflow(workflowId: string): boolean {
    try {
      localStorage.removeItem(`${this.STORAGE_KEY_PREFIX}${workflowId}`);
      this.disableAutoSave(workflowId);
      
      if (this.currentWorkflowId === workflowId) {
        this.currentWorkflowId = null;
      }
      
      return true;
    } catch (error) {
      console.error('Failed to delete workflow:', error);
      return false;
    }
  }

  /**
   * Complete a workflow
   */
  completeWorkflow(workflowId: string): boolean {
    return this.updateProgress(workflowId, {
      currentStep: -1, // Mark as completed
      completedSteps: Array.from({ length: this.getWorkflowState(workflowId)?.totalSteps || 0 }, (_, i) => i + 1),
      metadata: {
        ...this.getWorkflowState(workflowId)?.metadata,
        autoSaveEnabled: false
      } as any
    });
  }

  /**
   * Enable auto-save for a workflow
   */
  private enableAutoSave(workflowId: string): void {
    // Clear existing timer
    this.disableAutoSave(workflowId);
    
    const timer = setInterval(() => {
      const state = this.getWorkflowState(workflowId);
      if (state && state.metadata.autoSaveEnabled) {
        // Update last updated timestamp to indicate activity
        this.updateProgress(workflowId, {
          lastUpdated: new Date().toISOString()
        });
      }
    }, this.AUTO_SAVE_INTERVAL);
    
    this.autoSaveTimers.set(workflowId, timer);
  }

  /**
   * Disable auto-save for a workflow
   */
  private disableAutoSave(workflowId: string): void {
    const timer = this.autoSaveTimers.get(workflowId);
    if (timer) {
      clearInterval(timer);
      this.autoSaveTimers.delete(workflowId);
    }
  }

  /**
   * Save workflow state to localStorage
   */
  private saveWorkflowState(workflowId: string, state: WorkflowProgressState): void {
    try {
      localStorage.setItem(
        `${this.STORAGE_KEY_PREFIX}${workflowId}`,
        JSON.stringify(state)
      );
    } catch (error) {
      console.error('Failed to save workflow state:', error);
      // Try to free up space by removing old workflows
      this.cleanupOldWorkflows();
      // Retry save
      try {
        localStorage.setItem(
          `${this.STORAGE_KEY_PREFIX}${workflowId}`,
          JSON.stringify(state)
        );
      } catch (retryError) {
        console.error('Failed to save workflow state after cleanup:', retryError);
      }
    }
  }

  /**
   * Clean up old workflows to free storage space
   */
  private cleanupOldWorkflows(): void {
    const workflows = this.getResumableWorkflows();
    
    // Keep only the most recent workflows
    if (workflows.length > this.MAX_STORED_WORKFLOWS) {
      const toDelete = workflows.slice(this.MAX_STORED_WORKFLOWS);
      toDelete.forEach(workflow => {
        this.deleteWorkflow(workflow.workflowId);
      });
    }
    
    // Remove workflows older than 30 days
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    
    workflows.forEach(workflow => {
      if (new Date(workflow.lastUpdated) < thirtyDaysAgo) {
        this.deleteWorkflow(workflow.workflowId);
      }
    });
  }

  /**
   * Calculate estimated time remaining
   */
  private calculateEstimatedTime(state: WorkflowProgressState): string | undefined {
    if (state.completedSteps.length === 0) return undefined;
    
    const startTime = new Date(state.metadata.startTime);
    const currentTime = new Date();
    const elapsedTime = currentTime.getTime() - startTime.getTime();
    
    const progressRatio = state.completedSteps.length / state.totalSteps;
    if (progressRatio === 0) return undefined;
    
    const estimatedTotalTime = elapsedTime / progressRatio;
    const remainingTime = estimatedTotalTime - elapsedTime;
    
    if (remainingTime <= 0) return 'Almost done';
    
    const minutes = Math.ceil(remainingTime / (1000 * 60));
    if (minutes < 60) return `${minutes} min`;
    
    const hours = Math.ceil(minutes / 60);
    return `${hours} hr`;
  }

  /**
   * Export workflow data for backup
   */
  exportWorkflow(workflowId: string): string | null {
    const state = this.getWorkflowState(workflowId);
    if (!state) return null;
    
    return JSON.stringify(state, null, 2);
  }

  /**
   * Import workflow data from backup
   */
  importWorkflow(data: string): string | null {
    try {
      const state: WorkflowProgressState = JSON.parse(data);
      const newWorkflowId = `imported-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
      
      const importedState: WorkflowProgressState = {
        ...state,
        workflowId: newWorkflowId,
        lastUpdated: new Date().toISOString()
      };
      
      this.saveWorkflowState(newWorkflowId, importedState);
      return newWorkflowId;
    } catch (error) {
      console.error('Failed to import workflow:', error);
      return null;
    }
  }
}

// Export singleton instance
export const workflowProgressManager = WorkflowProgressManager.getInstance();
