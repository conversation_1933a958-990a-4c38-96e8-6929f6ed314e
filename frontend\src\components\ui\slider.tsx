/**
 * Slider component for ClassyWeb
 * A customizable range slider component built with React
 */

import * as React from "react"
import { cn } from "@/lib/utils"

export interface SliderProps extends React.HTMLAttributes<HTMLDivElement> {
  value?: number[]
  defaultValue?: number[]
  onValueChange?: (value: number[]) => void
  min?: number
  max?: number
  step?: number
  disabled?: boolean
  orientation?: "horizontal" | "vertical"
}

const Slider = React.forwardRef<HTMLDivElement, SliderProps>(
  ({ 
    className, 
    value, 
    defaultValue = [0], 
    onValueChange, 
    min = 0, 
    max = 100, 
    step = 1, 
    disabled = false,
    orientation = "horizontal",
    ...props 
  }, ref) => {
    const [internalValue, setInternalValue] = React.useState(value || defaultValue)
    const [isDragging, setIsDragging] = React.useState(false)
    const [activeThumb, setActiveThumb] = React.useState<number | null>(null)
    const sliderRef = React.useRef<HTMLDivElement>(null)

    // Use controlled value if provided, otherwise use internal state
    const currentValue = value || internalValue

    const updateValue = React.useCallback((newValue: number[]) => {
      const clampedValue = newValue.map(v => Math.max(min, Math.min(max, v)))
      
      if (!value) {
        setInternalValue(clampedValue)
      }
      onValueChange?.(clampedValue)
    }, [min, max, value, onValueChange])

    const getValueFromPosition = React.useCallback((clientX: number, clientY: number) => {
      if (!sliderRef.current) return 0

      const rect = sliderRef.current.getBoundingClientRect()
      const isHorizontal = orientation === "horizontal"
      
      const position = isHorizontal 
        ? (clientX - rect.left) / rect.width
        : 1 - (clientY - rect.top) / rect.height

      const clampedPosition = Math.max(0, Math.min(1, position))
      return min + (max - min) * clampedPosition
    }, [min, max, orientation])

    const handleMouseDown = React.useCallback((event: React.MouseEvent, thumbIndex: number) => {
      if (disabled) return
      
      event.preventDefault()
      setIsDragging(true)
      setActiveThumb(thumbIndex)
    }, [disabled])

    const handleMouseMove = React.useCallback((event: MouseEvent) => {
      if (!isDragging || activeThumb === null || disabled) return

      const newValue = getValueFromPosition(event.clientX, event.clientY)
      const stepValue = Math.round(newValue / step) * step
      
      const updatedValue = [...currentValue]
      updatedValue[activeThumb] = stepValue
      
      updateValue(updatedValue)
    }, [isDragging, activeThumb, disabled, getValueFromPosition, step, currentValue, updateValue])

    const handleMouseUp = React.useCallback(() => {
      setIsDragging(false)
      setActiveThumb(null)
    }, [])

    const handleTrackClick = React.useCallback((event: React.MouseEvent) => {
      if (disabled || isDragging) return

      const newValue = getValueFromPosition(event.clientX, event.clientY)
      const stepValue = Math.round(newValue / step) * step
      
      // Find the closest thumb to update
      let closestThumbIndex = 0
      let closestDistance = Math.abs(currentValue[0] - stepValue)
      
      for (let i = 1; i < currentValue.length; i++) {
        const distance = Math.abs(currentValue[i] - stepValue)
        if (distance < closestDistance) {
          closestDistance = distance
          closestThumbIndex = i
        }
      }
      
      const updatedValue = [...currentValue]
      updatedValue[closestThumbIndex] = stepValue
      updateValue(updatedValue)
    }, [disabled, isDragging, getValueFromPosition, step, currentValue, updateValue])

    // Handle keyboard navigation
    const handleKeyDown = React.useCallback((event: React.KeyboardEvent, thumbIndex: number) => {
      if (disabled) return

      let delta = 0
      const largeStep = (max - min) / 10

      switch (event.key) {
        case 'ArrowRight':
        case 'ArrowUp':
          delta = step
          break
        case 'ArrowLeft':
        case 'ArrowDown':
          delta = -step
          break
        case 'PageUp':
          delta = largeStep
          break
        case 'PageDown':
          delta = -largeStep
          break
        case 'Home':
          delta = min - currentValue[thumbIndex]
          break
        case 'End':
          delta = max - currentValue[thumbIndex]
          break
        default:
          return
      }

      event.preventDefault()
      const updatedValue = [...currentValue]
      updatedValue[thumbIndex] = Math.max(min, Math.min(max, currentValue[thumbIndex] + delta))
      updateValue(updatedValue)
    }, [disabled, step, max, min, currentValue, updateValue])

    // Mouse event listeners
    React.useEffect(() => {
      if (isDragging) {
        document.addEventListener('mousemove', handleMouseMove)
        document.addEventListener('mouseup', handleMouseUp)
        
        return () => {
          document.removeEventListener('mousemove', handleMouseMove)
          document.removeEventListener('mouseup', handleMouseUp)
        }
      }
    }, [isDragging, handleMouseMove, handleMouseUp])

    const getThumbPosition = (thumbValue: number) => {
      return ((thumbValue - min) / (max - min)) * 100
    }

    const isHorizontal = orientation === "horizontal"

    return (
      <div
        ref={ref}
        className={cn(
          "relative flex touch-none select-none items-center",
          isHorizontal ? "w-full" : "h-full flex-col",
          className
        )}
        {...props}
      >
        <div
          ref={sliderRef}
          className={cn(
            "relative bg-secondary rounded-full",
            isHorizontal ? "h-2 w-full" : "w-2 h-full",
            disabled && "opacity-50 cursor-not-allowed"
          )}
          onClick={handleTrackClick}
        >
          {/* Track fill */}
          {currentValue.length === 1 && (
            <div
              className="absolute bg-primary rounded-full"
              style={{
                [isHorizontal ? 'width' : 'height']: `${getThumbPosition(currentValue[0])}%`,
                [isHorizontal ? 'height' : 'width']: '100%',
              }}
            />
          )}
          
          {/* Range fill for multiple thumbs */}
          {currentValue.length === 2 && (
            <div
              className="absolute bg-primary rounded-full"
              style={{
                [isHorizontal ? 'left' : 'bottom']: `${getThumbPosition(Math.min(...currentValue))}%`,
                [isHorizontal ? 'width' : 'height']: `${Math.abs(getThumbPosition(currentValue[1]) - getThumbPosition(currentValue[0]))}%`,
                [isHorizontal ? 'height' : 'width']: '100%',
              }}
            />
          )}

          {/* Thumbs */}
          {currentValue.map((thumbValue, index) => (
            <div
              key={index}
              className={cn(
                "absolute w-5 h-5 bg-background border-2 border-primary rounded-full shadow-lg transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",
                "hover:bg-accent cursor-grab active:cursor-grabbing",
                disabled && "cursor-not-allowed opacity-50",
                isDragging && activeThumb === index && "cursor-grabbing scale-110"
              )}
              style={{
                [isHorizontal ? 'left' : 'bottom']: `${getThumbPosition(thumbValue)}%`,
                transform: isHorizontal ? 'translateX(-50%)' : 'translateY(50%)',
              }}
              onMouseDown={(e) => handleMouseDown(e, index)}
              onKeyDown={(e) => handleKeyDown(e, index)}
              tabIndex={disabled ? -1 : 0}
              role="slider"
              aria-valuemin={min}
              aria-valuemax={max}
              aria-valuenow={thumbValue}
              aria-orientation={orientation}
              aria-disabled={disabled}
            />
          ))}
        </div>
      </div>
    )
  }
)

Slider.displayName = "Slider"

export { Slider }
