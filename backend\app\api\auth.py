"""
Authentication routes for the ClassyWeb application.
"""
import logging
import urllib.parse
import time
from datetime import datetime, timedelta, timezone
from typing import Optional

import requests
from fastapi import APIRouter, Depends, HTTPException, status, Response, Request, Cookie
from fastapi.security import <PERSON>A<PERSON><PERSON><PERSON><PERSON><PERSON>RequestForm, OAuth2<PERSON><PERSON>word<PERSON>earer
from jose import jwt
from sqlalchemy.orm import Session

from .. import config # Adjusted import path
from ..utils import email_utils # Adjusted import path
from ..auth import ( # Adjusted import path
    authenticate_user, create_access_token, create_refresh_token, get_current_active_user,
    get_password_hash, generate_verification_token, verify_password
)
from ..auth.cookies import set_refresh_token_cookie, get_refresh_token_from_cookies, clear_refresh_token_cookie
from ..database import (
    get_db, get_user_by_email, get_user_by_oauth, get_user_by_reset_token,
    get_user_by_verification_token, create_user, update_user, User,
    get_tasks, get_files, File, Task, get_user_by_username
)
from ..redis_client import blacklist_token, validate_refresh_token, invalidate_refresh_token, invalidate_all_user_tokens, find_user_by_refresh_token
# Adjusted import path to be more specific
from ..models.auth import (
    EmailVerification, GoogleAuthRequest, MessageResponse, PasswordChange,
    PasswordReset, PasswordResetConfirm, Token, UserCreate, UserLogin,
    UserResponse, UserUpdate, UserActivityResponse, UserPreferencesUpdate,
    RefreshTokenRequest, FileActivityItem, TaskActivityItem
)

# Set up logging
logger = logging.getLogger(__name__)

# Security audit logger
security_logger = logging.getLogger("security_audit")

def log_security_event(event_type: str, user_id: int = None, email: str = None,
                      ip_address: str = None, details: str = None, success: bool = True):
    """Log security-related events for audit purposes."""
    event_data = {
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "event_type": event_type,
        "user_id": user_id,
        "email": email,
        "ip_address": ip_address,
        "success": success,
        "details": details
    }

    if success:
        security_logger.info(f"Security Event: {event_type}", extra=event_data)
    else:
        security_logger.warning(f"Security Event Failed: {event_type}", extra=event_data)

# Create router
router = APIRouter(prefix="/auth", tags=["Authentication"])

# OAuth2 scheme for token extraction
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="auth/token")

@router.get("/test-google-client", response_model=dict)
async def test_google_client():
    """Test Google OAuth client credentials with a minimal request."""
    try:
        # Prepare a minimal token request to test client credentials
        token_url = "https://oauth2.googleapis.com/token"
        test_data = {
            "client_id": config.GOOGLE_CLIENT_ID,
            "client_secret": config.GOOGLE_CLIENT_SECRET,
            "grant_type": "client_credentials"
        }

        # Add headers to ensure proper content type
        headers = {
            'Content-Type': 'application/x-www-form-urlencoded'
        }

        # Make the request
        response = requests.post(token_url, data=urllib.parse.urlencode(test_data), headers=headers)

        # Get the response
        status_code = response.status_code
        try:
            response_json = response.json()
        except:
            response_json = {"text": response.text}

        # Return the result
        return {
            "status": "success",
            "test_result": {
                "status_code": status_code,
                "response": response_json
            },
            "message": "This endpoint tests if your Google OAuth client credentials are valid. Note that 'invalid_grant' errors are expected here since we're not using a real authorization code."
        }
    except Exception as e:
        return {
            "status": "error",
            "error": str(e)
        }

@router.get("/oauth-debug", response_model=dict)
async def oauth_debug():
    """Debug endpoint to check OAuth configuration."""
    # Mask sensitive information
    client_id = config.GOOGLE_CLIENT_ID
    client_secret = config.GOOGLE_CLIENT_SECRET
    redirect_uri = config.GOOGLE_REDIRECT_URI

    masked_client_id = f"{client_id[:8]}...{client_id[-8:]}" if client_id and len(client_id) > 16 else "Not set"
    masked_client_secret = f"{client_secret[:5]}...{client_secret[-5:]}" if client_secret and len(client_secret) > 10 else "Not set"

    # Get timezone information
    tz_info = get_local_timezone_info()

    # Test Google API connectivity
    google_api_status = "Unknown"
    try:
        response = requests.head('https://accounts.google.com', timeout=5)
        google_api_status = f"OK ({response.status_code})"
    except Exception as e:
        google_api_status = f"Error: {str(e)}"

    # Test OAuth endpoint connectivity
    oauth_endpoint_status = "Unknown"
    try:
        response = requests.head('https://oauth2.googleapis.com/token', timeout=5)
        oauth_endpoint_status = f"OK ({response.status_code})"
    except Exception as e:
        oauth_endpoint_status = f"Error: {str(e)}"

    return {
        "google_oauth": {
            "client_id": masked_client_id,
            "client_secret_set": bool(client_secret),
            "redirect_uri": redirect_uri,
        },
        "connectivity": {
            "google_accounts": google_api_status,
            "oauth_endpoint": oauth_endpoint_status
        },
        "timezone_info": tz_info,
        "server_time": datetime.now(timezone.utc).isoformat(),
    }

# Function to get Google's server time
def get_google_server_time():
    """Get an approximation of Google's server time by making a request to Google."""
    try:
        # Make a HEAD request to Google
        response = requests.head('https://www.google.com')
        # Get the date from the response headers
        if 'date' in response.headers:
            # Parse the date string to a datetime object
            server_time_str = response.headers['date']
            try:
                # Try the standard format first
                server_time = datetime.strptime(server_time_str, '%a, %d %b %Y %H:%M:%S %Z')
            except ValueError:
                # Try alternative format with GMT
                try:
                    server_time = datetime.strptime(server_time_str, '%a, %d %b %Y %H:%M:%S GMT')
                except ValueError:
                    # Try parsing with email.utils.parsedate_to_datetime
                    from email.utils import parsedate_to_datetime
                    server_time = parsedate_to_datetime(server_time_str)

            # Convert to UTC if not already
            if server_time.tzinfo is None:
                server_time = server_time.replace(tzinfo=timezone.utc)
            logger.info(f"Google server time: {server_time.isoformat()}")
            return server_time
    except Exception as e:
        logger.error(f"Error getting Google server time: {e}")
    return None

# Function to get local timezone information
def get_local_timezone_info():
    """Get information about the local timezone."""
    try:
        import platform
        import time as time_module

        # Get local timezone name
        local_tz_name = time_module.tzname[0] if hasattr(time_module, 'tzname') else 'Unknown'

        # Get local timezone offset
        utc_offset = -time_module.timezone // 3600  # Convert seconds to hours
        utc_offset_str = f"UTC{'+' if utc_offset >= 0 else ''}{utc_offset}"

        # Get daylight saving time status
        is_dst = time_module.daylight and time_module.localtime().tm_isdst > 0
        dst_status = "DST active" if is_dst else "DST inactive"

        # Get system time
        system_time = datetime.now().isoformat()

        # Get system info
        system_info = f"{platform.system()} {platform.release()}"

        logger.info(f"Local timezone: {local_tz_name} ({utc_offset_str}), {dst_status}")
        logger.info(f"System time: {system_time}")
        logger.info(f"System info: {system_info}")

        return {
            "timezone_name": local_tz_name,
            "utc_offset": utc_offset,
            "utc_offset_str": utc_offset_str,
            "dst_active": is_dst,
            "system_time": system_time,
            "system_info": system_info
        }
    except Exception as e:
        logger.error(f"Error getting local timezone info: {e}")
        return {}

# Helper function to convert User model to UserResponse
def user_to_response(user: User) -> UserResponse:
    """Convert User model to UserResponse."""
    return UserResponse(
        id=user.id,
        email=user.email,
        username=user.username,
        first_name=user.first_name,
        last_name=user.last_name,
        profile_picture=user.profile_picture,
        is_active=user.is_active,
        is_verified=user.is_verified,
        oauth_provider=user.oauth_provider,
        created_at=user.created_at.isoformat(),
        last_login=user.last_login.isoformat() if user.last_login else None,
        bio=user.bio,
        job_title=user.job_title,
        company=user.company,
        website=user.website,
        location=user.location,
        theme_preference=user.theme_preference
    )

@router.post("/register", response_model=UserResponse, status_code=status.HTTP_201_CREATED)
async def register(user_data: UserCreate, db: Session = Depends(get_db)):
    """
    Register a new user with email and password.
    """
    # Check if email already exists
    db_user = get_user_by_email(db, user_data.email)
    if db_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Email already registered"
        )

    # Check if username already exists (if provided)
    if user_data.username:
        # Use the imported function directly
        db_user_by_username = get_user_by_username(db, user_data.username)
        if db_user_by_username:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Username already taken"
            )

    # Create verification token
    verification_token = generate_verification_token()
    verification_token_expires = datetime.now(timezone.utc) + timedelta(hours=config.VERIFICATION_TOKEN_EXPIRE_HOURS)

    # Create user
    new_user = create_user(db, {
        "email": user_data.email,
        "username": user_data.username,
        "hashed_password": get_password_hash(user_data.password),
        "first_name": user_data.first_name,
        "last_name": user_data.last_name,
        "verification_token": verification_token,
        "verification_token_expires": verification_token_expires,
        "is_verified": False
    })

    # Send verification email
    if config.EMAIL_ENABLED:
        email_utils.send_verification_email(user_data.email, verification_token)
        logger.info(f"Verification email sent to {user_data.email}")
    else:
        logger.warning(f"Email sending disabled. Verification token: {verification_token}")

    return user_to_response(new_user)

@router.post("/token", response_model=Token)
async def login_for_access_token(response: Response, request: Request, form_data: OAuth2PasswordRequestForm = Depends(), db: Session = Depends(get_db)):
    """
    OAuth2 compatible token login, get an access token for future requests.
    """
    # Note: form_data.username corresponds to the email field in this context
    user, error_code = authenticate_user(db, form_data.username, form_data.password)
    if not user:
        error_detail = "Incorrect email or password"
        if error_code == "user_not_found":
            error_detail = "No account found with this email. Please sign up first."
        elif error_code == "oauth_only_user":
            error_detail = "This account uses social login. Please sign in with your social provider."
        elif error_code == "invalid_password":
            error_detail = "Incorrect password. Please try again."

        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=error_detail,
            headers={"WWW-Authenticate": "Bearer"},
        )

    # Check if user is active
    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Your account is inactive. Please contact support."
        )

    # Update last login time
    update_user(db, user.id, {"last_login": datetime.now(timezone.utc)})

    # Create access token with enhanced security
    access_token_expires = timedelta(minutes=config.ACCESS_TOKEN_EXPIRE_MINUTES)

    # Collect metadata for token
    metadata = {
        "ip": request.client.host,
        "user_agent": request.headers.get("user-agent", "Unknown"),
        "source": "token_endpoint"
    }

    # Create token with user ID and metadata
    access_token = create_access_token(
        data={"sub": user.email},
        expires_delta=access_token_expires,
        user_id=user.id,
        metadata=metadata
    )

    # Create refresh token with metadata
    metadata = {
        "ip": request.client.host,
        "user_agent": request.headers.get("user-agent", "Unknown"),
        "issued_at": datetime.now(timezone.utc).isoformat(),
        "source": "token_endpoint"
    }
    refresh_token = create_refresh_token(user.id, metadata)

    # Set refresh token in HttpOnly cookie
    set_refresh_token_cookie(response, refresh_token)
    logger.info(f"Set refresh token cookie for user {user.id} (token endpoint)")

    return Token(
        access_token=access_token,
        refresh_token=refresh_token,  # Still include in response for backward compatibility
        expires_in=config.ACCESS_TOKEN_EXPIRE_MINUTES * 60
    )

@router.post("/login", response_model=Token)
async def login(user_data: UserLogin, response: Response, request: Request, db: Session = Depends(get_db)):
    """
    Login with email and password.
    """
    user, error_code = authenticate_user(db, user_data.email, user_data.password)
    if not user:
        error_detail = "Incorrect email or password"
        if error_code == "user_not_found":
            error_detail = "No account found with this email. Please sign up first."
        elif error_code == "oauth_only_user":
            error_detail = "This account uses social login. Please sign in with your social provider."
        elif error_code == "invalid_password":
            error_detail = "Incorrect password. Please try again."

        # Log failed login attempt
        log_security_event(
            event_type="login_failed",
            email=user_data.email,
            ip_address=request.client.host,
            details=f"Login failed: {error_code}",
            success=False
        )

        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=error_detail
        )

    # Check if user is active
    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Your account is inactive. Please contact support."
        )

    # Update last login time and track login attempt
    login_metadata = {
        "last_login": datetime.now(timezone.utc),
        "login_ip": request.client.host,
        "login_user_agent": request.headers.get("user-agent", "Unknown")
    }
    update_user(db, user.id, login_metadata)

    # Log successful login for security monitoring
    logger.info(f"Successful login for user {user.email} from IP {request.client.host}")
    log_security_event(
        event_type="login_success",
        user_id=user.id,
        email=user.email,
        ip_address=request.client.host,
        details="Successful password login",
        success=True
    )

    # Create access token with enhanced security
    access_token_expires = timedelta(minutes=config.ACCESS_TOKEN_EXPIRE_MINUTES)

    # Collect metadata for token
    metadata = {
        "ip": request.client.host,
        "user_agent": request.headers.get("user-agent", "Unknown"),
        "source": "login_endpoint"
    }

    # Create token with user ID and metadata
    access_token = create_access_token(
        data={"sub": user.email},
        expires_delta=access_token_expires,
        user_id=user.id,
        metadata=metadata
    )

    # Create refresh token with metadata
    metadata = {
        "ip": request.client.host,
        "user_agent": request.headers.get("user-agent", "Unknown"),
        "issued_at": datetime.now(timezone.utc).isoformat(),
        "source": "login_endpoint"
    }
    refresh_token = create_refresh_token(user.id, metadata)

    # Set refresh token in HttpOnly cookie
    set_refresh_token_cookie(response, refresh_token)
    logger.info(f"Set refresh token cookie for user {user.id}")

    return Token(
        access_token=access_token,
        refresh_token=refresh_token,  # Still include in response for backward compatibility
        expires_in=config.ACCESS_TOKEN_EXPIRE_MINUTES * 60
    )

@router.post("/google", response_model=Token)
async def google_auth(auth_data: GoogleAuthRequest, response: Response, request: Request, db: Session = Depends(get_db)):
    """
    Authenticate with Google OAuth.
    """
    # Exchange authorization code for access token
    token_url = "https://oauth2.googleapis.com/token"

    # Log the received code (partially masked for security)
    code_length = len(auth_data.code)
    masked_code = auth_data.code[:4] + '*' * (code_length - 8) + auth_data.code[-4:] if code_length > 8 else '****'
    logger.info(f"Received authorization code: {masked_code} (length: {code_length})")

    # Log client timezone information if provided
    if auth_data.tzInfo:
        tz_info = auth_data.tzInfo
        logger.info(f"Client timezone: {tz_info.timezone} ({tz_info.offsetStr}), DST: {'active' if tz_info.isDST else 'inactive'}")
        logger.info(f"Client local time: {tz_info.localTime}")
        logger.info(f"Client timestamp: {tz_info.timestamp}")

        # Log browser information if provided
        if tz_info.browserInfo:
            logger.info(f"Client browser: {tz_info.browserInfo}")

    # Get redirect URI from config
    redirect_uri = config.GOOGLE_REDIRECT_URI

    # Validate redirect URI
    if not redirect_uri:
        logger.error("GOOGLE_REDIRECT_URI is not set in the configuration")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Google OAuth is not properly configured"
        )

    # Log configuration values (partially masked)
    logger.info(f"Using client_id: {config.GOOGLE_CLIENT_ID[:5]}...{config.GOOGLE_CLIENT_ID[-5:] if len(config.GOOGLE_CLIENT_ID) > 10 else ''}")
    logger.info(f"Using redirect_uri: {redirect_uri}")

    token_data = {
        "code": auth_data.code,
        "client_id": config.GOOGLE_CLIENT_ID,
        "client_secret": config.GOOGLE_CLIENT_SECRET,
        "redirect_uri": redirect_uri,
        "grant_type": "authorization_code"
    }

    try:
        # Log the token request data (excluding client secret)
        log_data = token_data.copy()
        log_data['client_secret'] = '***REDACTED***'
        log_data['code'] = masked_code
        logger.info(f"Google OAuth token request: {log_data}")

        # Log the exact client ID and redirect URI being used (for debugging)
        logger.info(f"Exact client ID being used: {token_data['client_id']}")
        logger.info(f"Exact redirect URI being used: {token_data['redirect_uri']}")

        # Get and log local timezone information
        server_tz_info = get_local_timezone_info()
        if server_tz_info:
            logger.info(f"Server timezone: {server_tz_info.get('timezone_name', 'Unknown')} ({server_tz_info.get('utc_offset_str', 'Unknown')}), DST: {'active' if server_tz_info.get('dst_active') else 'inactive'}")

        # Get Google's server time and log time differences
        google_time = get_google_server_time()
        current_utc = datetime.now(timezone.utc)

        # Compare client and server time if client time was provided
        if auth_data.tzInfo:
            try:
                # Parse client timestamp
                client_time = datetime.fromisoformat(auth_data.tzInfo.timestamp.replace('Z', '+00:00'))

                # Calculate time difference between client and server
                client_server_diff = (current_utc - client_time).total_seconds()

                logger.info(f"Client-server time difference: {client_server_diff:.2f} seconds")

                # Check if time difference is significant (more than 5 minutes)
                if abs(client_server_diff) > 300:
                    logger.warning(f"Client-server time difference is significant: {client_server_diff:.2f} seconds")
                    logger.warning("This may cause OAuth authentication issues")
            except Exception as e:
                logger.error(f"Error comparing client and server time: {e}")

        # Compare server and Google time
        if google_time:
            time_diff = (current_utc - google_time).total_seconds()

            logger.info(f"Server time (UTC): {current_utc.isoformat()}")
            logger.info(f"Google time (UTC): {google_time.isoformat()}")
            logger.info(f"Server-Google time difference: {time_diff:.2f} seconds")

            # Check if time difference is significant (more than 5 minutes)
            if abs(time_diff) > 300:
                logger.warning(f"Server-Google time difference is significant: {time_diff:.2f} seconds")
                logger.warning("This may cause OAuth authentication issues")
        else:
            logger.warning("Could not get Google server time for comparison")

        # Add headers to ensure proper content type
        headers = {
            'Content-Type': 'application/x-www-form-urlencoded'
        }

        # Make the token request with URL-encoded data
        encoded_token_data = urllib.parse.urlencode(token_data)

        # Try the request with a retry mechanism
        max_retries = 2
        retry_count = 0
        token_response = None

        while retry_count <= max_retries:
            if retry_count > 0:
                logger.info(f"Retry attempt {retry_count} for Google OAuth token request")
                # Add a small delay before retrying
                time.sleep(1)

            token_response = requests.post(token_url, data=encoded_token_data, headers=headers)

            # If successful, break out of the retry loop
            if token_response.status_code == 200:
                break

            retry_count += 1

        # Log response status and content for debugging
        logger.info(f"Google OAuth token response status: {token_response.status_code}")

        if token_response.status_code != 200:
            logger.error(f"Google OAuth error response: {token_response.text}")

            # Log the full request details for debugging (excluding client secret)
            debug_token_data = token_data.copy()
            debug_token_data['client_secret'] = '***REDACTED***'
            logger.error(f"Full token request data: {debug_token_data}")
            logger.error(f"Request headers: {headers}")

            # Raise HTTPException directly instead of using raise_for_status
            error_detail = "Failed to authenticate with Google"
            try:
                error_json = token_response.json()
                if 'error_description' in error_json:
                    error_detail = f"Google OAuth error: {error_json['error_description']}"
                elif 'error' in error_json:
                    error_detail = f"Google OAuth error: {error_json['error']}"

                # Check for specific error types
                if error_json.get('error') == 'invalid_grant':
                    # Get local timezone information
                    server_tz_info = get_local_timezone_info()

                    # Get time difference with Google again
                    google_time = get_google_server_time()
                    current_utc = datetime.now(timezone.utc)

                    time_sync_messages = []

                    # Check client-server time difference if client time was provided
                    client_server_message = ""
                    if auth_data.tzInfo:
                        try:
                            # Parse client timestamp
                            client_time = datetime.fromisoformat(auth_data.tzInfo.timestamp.replace('Z', '+00:00'))

                            # Calculate time difference between client and server
                            client_server_diff = (current_utc - client_time).total_seconds()

                            if abs(client_server_diff) > 60:  # More than 1 minute difference
                                client_server_message = f"\nDetected time difference between client and server: {client_server_diff:.2f} seconds. This may be causing the issue."
                                time_sync_messages.append(client_server_message)
                        except Exception as e:
                            logger.error(f"Error comparing client and server time: {e}")

                    # Check server-Google time difference
                    google_time_message = ""
                    if google_time:
                        time_diff = (current_utc - google_time).total_seconds()

                        if abs(time_diff) > 60:  # More than 1 minute difference
                            google_time_message = f"\nDetected time difference between server and Google: {time_diff:.2f} seconds. This is likely causing the issue."
                            time_sync_messages.append(google_time_message)
                            # Update error detail to be more user-friendly
                            error_detail = "Google authentication failed due to a time synchronization issue. Please try again."
                    else:
                        time_sync_messages.append("\nCould not determine time difference with Google. This might be a network issue.")

                    # Add timezone information to the log
                    tz_messages = []

                    # Server timezone info
                    if server_tz_info:
                        server_tz_message = f"\nServer timezone: {server_tz_info.get('timezone_name', 'Unknown')} ({server_tz_info.get('utc_offset_str', 'Unknown')})"
                        if server_tz_info.get('dst_active') is not None:
                            server_tz_message += f", DST {'active' if server_tz_info.get('dst_active') else 'inactive'}"
                        tz_messages.append(server_tz_message)

                    # Client timezone info
                    if auth_data.tzInfo:
                        client_tz_message = f"\nClient timezone: {auth_data.tzInfo.timezone} ({auth_data.tzInfo.offsetStr}), DST: {'active' if auth_data.tzInfo.isDST else 'inactive'}"
                        tz_messages.append(client_tz_message)

                    # Combine all messages
                    time_sync_message = "\n".join(time_sync_messages)
                    tz_message = "\n".join(tz_messages)

                    logger.error("This may be caused by:"
                                "\n1. The authorization code has expired (they typically expire after a few minutes)"
                                "\n2. The code has already been used"
                                "\n3. There's a time synchronization issue between your server and Google's servers"
                                "\n4. The redirect URI doesn't match what's registered in Google Cloud Console"
                                f"{time_sync_message}"
                                f"{tz_message}")
            except Exception as json_err:
                logger.error(f"Error parsing error response: {json_err}")

            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=error_detail
            )

        token_json = token_response.json()

        # Get user info from Google
        user_info_url = "https://www.googleapis.com/oauth2/v2/userinfo"
        headers = {"Authorization": f"Bearer {token_json['access_token']}"}
        user_info_response = requests.get(user_info_url, headers=headers)

        # Check for errors in user info response
        if user_info_response.status_code != 200:
            logger.error(f"Google user info error response: {user_info_response.text}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Failed to retrieve user information from Google"
            )

        user_info = user_info_response.json()

        # Check if user exists
        google_user_id = user_info["id"]
        email = user_info["email"]

        # Try to find user by OAuth provider and ID
        user = get_user_by_oauth(db, "google", google_user_id)

        # If not found, try to find by email
        if not user:
            user = get_user_by_email(db, email)

            # If user exists but doesn't have Google OAuth, update it
            if user:
                update_user(db, user.id, {
                    "oauth_provider": "google",
                    "oauth_user_id": google_user_id
                })
            # If user doesn't exist, create a new one
            else:
                user = create_user(db, {
                    "email": email,
                    "username": None,  # Generate username later if needed
                    "first_name": user_info.get("given_name"),
                    "last_name": user_info.get("family_name"),
                    "profile_picture": user_info.get("picture"),
                    "oauth_provider": "google",
                    "oauth_user_id": google_user_id,
                    "is_verified": True  # Google users are already verified
                })

        # Update last login time
        update_user(db, user.id, {"last_login": datetime.now(timezone.utc)})

        # Create access token with enhanced security
        access_token_expires = timedelta(minutes=config.ACCESS_TOKEN_EXPIRE_MINUTES)

        # Collect metadata for token
        metadata = {
            "ip": request.client.host,
            "user_agent": request.headers.get("user-agent", "Unknown"),
            "source": "google_oauth",
            "google_user_id": user_info.get("id", "unknown")
        }

        # Create token with user ID and metadata
        access_token = create_access_token(
            data={"sub": user.email},
            expires_delta=access_token_expires,
            user_id=user.id,
            metadata=metadata
        )

        # Create refresh token with metadata
        metadata = {
            "ip": request.client.host,
            "user_agent": request.headers.get("user-agent", "Unknown"),
            "issued_at": datetime.now(timezone.utc).isoformat(),
            "source": "google_oauth",
            "google_user_id": user_info.get("id", "unknown")
        }
        refresh_token = create_refresh_token(user.id, metadata)

        # Set refresh token in HttpOnly cookie
        set_refresh_token_cookie(response, refresh_token)
        logger.info(f"Set refresh token cookie for user {user.id} (Google OAuth)")

        return Token(
            access_token=access_token,
            refresh_token=refresh_token,  # Still include in response for backward compatibility
            expires_in=config.ACCESS_TOKEN_EXPIRE_MINUTES * 60
        )

    except requests.RequestException as e:
        logger.error(f"Google OAuth error: {e}", exc_info=True)

        # Provide more specific error message based on the exception
        error_detail = "Failed to authenticate with Google"
        if hasattr(e, 'response') and e.response is not None:
            try:
                error_json = e.response.json()
                if 'error_description' in error_json:
                    error_detail = f"Google OAuth error: {error_json['error_description']}"

                    # Check for specific error types
                    if error_json.get('error') == 'invalid_grant':
                        # Get local timezone information
                        server_tz_info = get_local_timezone_info()

                        # Get time difference with Google again
                        google_time = get_google_server_time()
                        current_utc = datetime.now(timezone.utc)

                        time_sync_messages = []

                        # Check client-server time difference if client time was provided
                        client_server_message = ""
                        if auth_data.tzInfo:
                            try:
                                # Parse client timestamp
                                client_time = datetime.fromisoformat(auth_data.tzInfo.timestamp.replace('Z', '+00:00'))

                                # Calculate time difference between client and server
                                client_server_diff = (current_utc - client_time).total_seconds()

                                if abs(client_server_diff) > 60:  # More than 1 minute difference
                                    client_server_message = f"\nDetected time difference between client and server: {client_server_diff:.2f} seconds. This may be causing the issue."
                                    time_sync_messages.append(client_server_message)
                            except Exception as e:
                                logger.error(f"Error comparing client and server time: {e}")

                        # Check server-Google time difference
                        google_time_message = ""
                        if google_time:
                            time_diff = (current_utc - google_time).total_seconds()

                            if abs(time_diff) > 60:  # More than 1 minute difference
                                google_time_message = f"\nDetected time difference between server and Google: {time_diff:.2f} seconds. This is likely causing the issue."
                                time_sync_messages.append(google_time_message)
                                # Update error detail to be more user-friendly
                                error_detail = "Google authentication failed due to a time synchronization issue. Please try again."
                        else:
                            time_sync_messages.append("\nCould not determine time difference with Google. This might be a network issue.")

                        # Add timezone information to the log
                        tz_messages = []

                        # Server timezone info
                        if server_tz_info:
                            server_tz_message = f"\nServer timezone: {server_tz_info.get('timezone_name', 'Unknown')} ({server_tz_info.get('utc_offset_str', 'Unknown')})"
                            if server_tz_info.get('dst_active') is not None:
                                server_tz_message += f", DST {'active' if server_tz_info.get('dst_active') else 'inactive'}"
                            tz_messages.append(server_tz_message)

                        # Client timezone info
                        if auth_data.tzInfo:
                            client_tz_message = f"\nClient timezone: {auth_data.tzInfo.timezone} ({auth_data.tzInfo.offsetStr}), DST: {'active' if auth_data.tzInfo.isDST else 'inactive'}"
                            tz_messages.append(client_tz_message)

                        # Combine all messages
                        time_sync_message = "\n".join(time_sync_messages)
                        tz_message = "\n".join(tz_messages)

                        logger.error("This may be caused by:"
                                    "\n1. The authorization code has expired (they typically expire after a few minutes)"
                                    "\n2. The code has already been used"
                                    "\n3. There's a time synchronization issue between your server and Google's servers"
                                    "\n4. The redirect URI doesn't match what's registered in Google Cloud Console"
                                    f"{time_sync_message}"
                                    f"{tz_message}")

                elif 'error' in error_json:
                    error_detail = f"Google OAuth error: {error_json['error']}"
            except Exception as json_err:
                logger.error(f"Error parsing error response: {json_err}")
                logger.error(f"Raw response: {e.response.text if hasattr(e.response, 'text') else 'No text available'}")

        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=error_detail
        )

@router.post("/logout", response_model=MessageResponse)
async def logout(
    response: Response,
    current_user: User = Depends(get_current_active_user),
    token: str = Depends(oauth2_scheme),
    all_devices: bool = False
):
    """
    Logout the current user by blacklisting their token.

    Args:
        response: FastAPI response object
        current_user: Current authenticated user
        token: JWT token
        all_devices: If True, invalidate all refresh tokens for this user

    Returns:
        MessageResponse with success message
    """
    try:
        # If all_devices is true, invalidate all refresh tokens for this user
        if all_devices:
            # Invalidate all refresh tokens for this user
            invalidate_all_user_tokens(current_user.id)
            logger.info(f"Invalidated all refresh tokens for user {current_user.id}")

            # Also blacklist the current token
            payload = jwt.decode(token, config.JWT_SECRET_KEY, algorithms=[config.JWT_ALGORITHM])
            exp = payload.get("exp")

            if exp:
                # Calculate remaining time until token expiration
                current_time = datetime.now(timezone.utc).timestamp()
                remaining_time = int(exp - current_time)

                if remaining_time > 0:
                    # Blacklist the token until it expires
                    blacklist_token(token, remaining_time)
                    logger.info(f"Current token blacklisted for user {current_user.id}")

            # Clear the refresh token cookie
            clear_refresh_token_cookie(response)
            logger.info(f"Cleared refresh token cookie for user {current_user.id}")

            return MessageResponse(message="Successfully logged out from all devices")
        else:
            # Just logout from the current device
            # Decode token to get expiration time
            payload = jwt.decode(token, config.JWT_SECRET_KEY, algorithms=[config.JWT_ALGORITHM])
            exp = payload.get("exp")

            if exp:
                # Calculate remaining time until token expiration
                current_time = datetime.now(timezone.utc).timestamp()
                remaining_time = int(exp - current_time)

                if remaining_time > 0:
                    # Blacklist the token until it expires
                    blacklist_token(token, remaining_time)
                    logger.info(f"Token blacklisted for user {current_user.id} with {remaining_time} seconds remaining")
                else:
                    logger.info(f"Token already expired for user {current_user.id}")
            else:
                logger.warning(f"Token has no expiration claim for user {current_user.id}")

            # Clear the refresh token cookie
            clear_refresh_token_cookie(response)
            logger.info(f"Cleared refresh token cookie for user {current_user.id}")

            return MessageResponse(message="Successfully logged out")
    except Exception as e:
        logger.error(f"Error during logout: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred during logout"
        )

@router.post("/refresh", response_model=Token)
async def refresh_token_endpoint(
    response: Response,
    request: Request,
    refresh_request: RefreshTokenRequest = None,
    refresh_token_cookie: str = Cookie(None, alias="refresh_token"),
    db: Session = Depends(get_db)
):
    """
    Refresh an access token using a refresh token.
    """
    try:
        # Get refresh token from cookie or request body
        refresh_token = refresh_token_cookie

        # If no cookie, try to get from request body
        if not refresh_token and refresh_request:
            refresh_token = refresh_request.refresh_token

        # If still no token, return error
        if not refresh_token:
            logger.warning("No refresh token provided in cookie or request body")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No refresh token provided"
            )

        # Since refresh tokens are random strings stored in Redis, find the user ID
        user_id = find_user_by_refresh_token(refresh_token)

        if user_id is None:
            logger.warning(f"Refresh token not found in Redis: {refresh_token[:10]}...")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid refresh token"
            )

        # Validate the refresh token
        if not validate_refresh_token(user_id, refresh_token):
            logger.warning(f"Invalid refresh token for user {user_id}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid refresh token"
            )

        # Get the user from the database
        user = db.query(User).filter(User.id == user_id).first()
        if not user:
            logger.warning(f"User {user_id} not found during token refresh")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="User not found"
            )

        # Check if user is active
        if not user.is_active:
            logger.warning(f"Inactive user {user_id} attempted to refresh token")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Inactive user"
            )

        # Create a new access token with enhanced security
        access_token_expires = timedelta(minutes=config.ACCESS_TOKEN_EXPIRE_MINUTES)

        # Collect metadata for token
        metadata = {
            "ip": request.client.host,
            "user_agent": request.headers.get("user-agent", "Unknown"),
            "source": "token_refresh"
        }

        # Create token with user ID and metadata
        access_token = create_access_token(
            data={"sub": user.email},
            expires_delta=access_token_expires,
            user_id=user.id,
            metadata=metadata
        )

        # Invalidate the old refresh token and create a new one (token rotation)
        invalidate_refresh_token(user_id, refresh_token)

        # Create new refresh token with metadata
        metadata = {
            "ip": request.client.host,
            "user_agent": request.headers.get("user-agent", "Unknown"),
            "issued_at": datetime.now(timezone.utc).isoformat(),
            "source": "token_refresh",
            "previous_token": refresh_token[:8] + "..."  # Store just a hint of the previous token
        }
        new_refresh_token = create_refresh_token(user_id, metadata)

        # Set the new refresh token in HttpOnly cookie
        set_refresh_token_cookie(response, new_refresh_token)

        logger.info(f"Token refreshed for user {user_id}, new refresh token set in cookie")

        return Token(
            access_token=access_token,
            refresh_token=new_refresh_token,  # Still include in response for backward compatibility
            expires_in=config.ACCESS_TOKEN_EXPIRE_MINUTES * 60
        )
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Error during token refresh: {e}", exc_info=True)

        # Clear any potentially corrupted refresh token cookie
        response.delete_cookie(
            key="refresh_token",
            path="/",
            domain=None,
            secure=True,
            httponly=True,
            samesite="lax"
        )

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred during token refresh"
        )

@router.get("/me", response_model=UserResponse)
async def get_current_user_info(current_user: User = Depends(get_current_active_user)):
    """
    Get current user information.
    """
    return user_to_response(current_user)

@router.put("/me", response_model=UserResponse)
async def update_current_user(
    user_data: UserUpdate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Update current user information.
    """
    # Check if username already exists (if provided and different from current)
    if user_data.username and user_data.username != current_user.username:
        # Use the imported function directly
        db_user_by_username = get_user_by_username(db, user_data.username)
        if db_user_by_username:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Username already taken"
            )

    # Update user
    update_data = user_data.model_dump(exclude_unset=True)
    updated_user = update_user(db, current_user.id, update_data)

    return user_to_response(updated_user)

@router.post("/change-password", response_model=MessageResponse)
async def change_password(
    password_data: PasswordChange,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Change current user's password.
    """
    # Check if current password is correct
    if not current_user.hashed_password: # Check if user has a password (might be OAuth only)
         raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot change password for OAuth-only user"
        )
    if not verify_password(password_data.current_password, current_user.hashed_password):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Incorrect current password"
        )

    # Update password
    update_user(db, current_user.id, {
        "hashed_password": get_password_hash(password_data.new_password)
    })

    return MessageResponse(message="Password changed successfully")

@router.post("/reset-password", response_model=MessageResponse)
async def reset_password(password_data: PasswordReset, db: Session = Depends(get_db)):
    """
    Request password reset.
    """
    # Check if user exists
    user = get_user_by_email(db, password_data.email)
    if not user:
        # Don't reveal that the user doesn't exist
        logger.info(f"Password reset requested for non-existent email: {password_data.email}")
        return MessageResponse(message="If your email is registered, you will receive a password reset link")

    # Check if user has a password (might be OAuth only)
    if not user.hashed_password:
        logger.info(f"Password reset requested for OAuth-only user: {password_data.email}")
        return MessageResponse(message="Password reset is not available for accounts created via Google login.")


    # Create reset token
    reset_token = generate_verification_token()
    reset_token_expires = datetime.now(timezone.utc) + timedelta(hours=config.RESET_TOKEN_EXPIRE_HOURS)

    # Update user with reset token
    update_user(db, user.id, {
        "reset_token": reset_token,
        "reset_token_expires": reset_token_expires
    })

    # Send reset email
    if config.EMAIL_ENABLED:
        email_utils.send_password_reset_email(user.email, reset_token)
        logger.info(f"Password reset email sent to {user.email}")
    else:
        logger.warning(f"Email sending disabled. Reset token: {reset_token}")

    return MessageResponse(message="If your email is registered, you will receive a password reset link")

@router.post("/reset-password-confirm", response_model=MessageResponse)
async def reset_password_confirm(password_data: PasswordResetConfirm, db: Session = Depends(get_db)):
    """
    Confirm password reset with token.
    """
    # Check if token is valid
    user = get_user_by_reset_token(db, password_data.token)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid or expired token"
        )

    # Update password and clear token
    update_user(db, user.id, {
        "hashed_password": get_password_hash(password_data.new_password),
        "reset_token": None,
        "reset_token_expires": None
    })

    return MessageResponse(message="Password reset successfully")

@router.post("/verify-email", response_model=MessageResponse)
async def verify_email(verification_data: EmailVerification, db: Session = Depends(get_db)):
    """
    Verify email with token.
    """
    # Check if token is valid
    user = get_user_by_verification_token(db, verification_data.token)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid or expired token"
        )

    # Update user as verified and clear token
    update_user(db, user.id, {
        "is_verified": True,
        "verification_token": None,
        "verification_token_expires": None
    })

    return MessageResponse(message="Email verified successfully")


@router.get("/activity", response_model=UserActivityResponse)
async def get_user_activity(current_user: User = Depends(get_current_active_user), db: Session = Depends(get_db)):
    """
    Get current user's activity data including recent files and tasks.
    """
    # Get user's recent files and tasks with pagination
    recent_files = get_files(db, limit=5, user_id=current_user.id)
    # Use the updated get_tasks function
    recent_tasks = get_tasks(db, user_id=current_user.id, limit=5)

    # Count total files and tasks efficiently using database queries
    files_count = db.query(File).filter(File.user_id == current_user.id).count()
    tasks_count = db.query(Task).filter(Task.user_id == current_user.id).count()

    # Convert to response format using the new models
    files_data = [
        FileActivityItem(
            id=file.id,
            filename=file.filename,
            created_at=file.created_at.isoformat(),
            file_size=file.file_size,
            num_rows=file.num_rows,
            columns=file.columns
        )
        for file in recent_files
    ]

    tasks_data = [
        TaskActivityItem(
            id=task.id,
            task_type=task.task_type,
            status=task.status,
            created_at=task.created_at.isoformat(),
            completed_at=task.completed_at.isoformat() if task.completed_at else None,
            input_file_id=task.input_file_id,
            result_file_path=task.result_file_path,
            message=task.message
        )
        for task in recent_tasks
    ]

    return UserActivityResponse(
        files_count=files_count,
        tasks_count=tasks_count,
        recent_files=files_data,
        recent_tasks=tasks_data
    )


@router.put("/preferences", response_model=UserResponse)
async def update_user_preferences(
    preferences: UserPreferencesUpdate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Update user preferences like theme and notification settings.
    """
    # Extract update data
    update_data = preferences.model_dump(exclude_unset=True)

    # Update user
    updated_user = update_user(db, current_user.id, update_data)

    return user_to_response(updated_user)
