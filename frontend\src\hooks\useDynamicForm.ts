// frontend/src/hooks/useDynamicForm.ts
import { useState, useCallback, useMemo } from 'react';
import { HierarchyConfig, HierarchyRow } from '../types';
import { 
  DynamicValidationService, 
  ValidationError, 
  ValidationResult,
  createValidationService 
} from '../services/validationService';
import { 
  DynamicFormGeneratorService, 
  FormConfig, 
  FormFieldConfig,
  createFormGeneratorService 
} from '../services/formGeneratorService';

export interface UseDynamicFormOptions {
  hierarchyConfig: HierarchyConfig;
  initialData?: HierarchyRow[];
  validateOnChange?: boolean;
  validateOnBlur?: boolean;
  autoSave?: boolean;
  autoSaveDelay?: number;
}

export interface UseDynamicFormReturn {
  // Data management
  data: HierarchyRow[];
  setData: (data: HierarchyRow[]) => void;
  addRow: () => void;
  removeRow: (index: number) => void;
  updateRow: (index: number, field: string, value: any) => void;
  clearData: () => void;
  
  // Validation
  validationService: DynamicValidationService;
  validationErrors: ValidationError[];
  isValid: boolean;
  validateAll: () => ValidationResult;
  validateRow: (index: number) => ValidationResult;
  validateField: (field: string, value: any, rowIndex?: number) => ValidationError | null;
  clearValidationErrors: () => void;
  
  // Form generation
  formGeneratorService: DynamicFormGeneratorService;
  generateFormConfig: (type: 'hierarchy' | 'config' | 'classification' | 'search') => FormConfig;
  generateFieldConfig: (fieldName: string, overrides?: Partial<FormFieldConfig>) => FormFieldConfig;
  
  // UI helpers
  getDisplayName: (fieldName: string) => string;
  getPlaceholder: (fieldName: string) => string;
  getFieldError: (field: string, rowIndex?: number) => string | undefined;
  hasFieldError: (field: string, rowIndex?: number) => boolean;
  
  // State
  isDirty: boolean;
  isValidating: boolean;
  
  // Actions
  reset: () => void;
  submit: (onSubmit: (data: HierarchyRow[]) => Promise<void>) => Promise<boolean>;
}

export const useDynamicForm = (options: UseDynamicFormOptions): UseDynamicFormReturn => {
  const {
    hierarchyConfig,
    initialData = [],
    validateOnChange = true,
    validateOnBlur: _validateOnBlur = true,
    autoSave: _autoSave = false,
    autoSaveDelay: _autoSaveDelay = 1000
  } = options;

  // State
  const [data, setDataState] = useState<HierarchyRow[]>(initialData);
  const [validationErrors, setValidationErrors] = useState<ValidationError[]>([]);
  const [isDirty, setIsDirty] = useState(false);
  const [isValidating, setIsValidating] = useState(false);

  // Services
  const validationService = useMemo(
    () => createValidationService(hierarchyConfig),
    [hierarchyConfig]
  );

  const formGeneratorService = useMemo(
    () => createFormGeneratorService(hierarchyConfig),
    [hierarchyConfig]
  );

  // Computed values
  const isValid = useMemo(() => validationErrors.length === 0, [validationErrors]);

  // Data management
  const setData = useCallback((newData: HierarchyRow[]) => {
    setDataState(newData);
    setIsDirty(true);
    
    if (validateOnChange) {
      const result = validationService.validateRows(newData);
      setValidationErrors(result.errors);
    }
  }, [validationService, validateOnChange]);

  const addRow = useCallback(() => {
    const newRow: HierarchyRow = {
      id: `new-${Date.now()}-${Math.random()}`,
      Keywords: ''
    };

    // Initialize all hierarchy levels
    hierarchyConfig.hierarchy_levels.forEach(level => {
      newRow[level] = '';
    });

    setData([...data, newRow]);
  }, [data, hierarchyConfig.hierarchy_levels, setData]);

  const removeRow = useCallback((index: number) => {
    const newData = data.filter((_, i) => i !== index);
    setData(newData);
  }, [data, setData]);

  const updateRow = useCallback((index: number, field: string, value: any) => {
    const newData = [...data];
    newData[index] = { ...newData[index], [field]: value };
    setData(newData);
  }, [data, setData]);

  const clearData = useCallback(() => {
    setData([]);
    setIsDirty(false);
  }, [setData]);

  // Validation
  const validateAll = useCallback((): ValidationResult => {
    setIsValidating(true);
    const result = validationService.validateRows(data);
    setValidationErrors(result.errors);
    setIsValidating(false);
    return result;
  }, [validationService, data]);

  const validateRow = useCallback((index: number): ValidationResult => {
    if (index < 0 || index >= data.length) {
      return { isValid: false, errors: [{ field: 'index', message: 'Invalid row index' }] };
    }
    
    return validationService.validateRow(data[index], index);
  }, [validationService, data]);

  const validateField = useCallback((field: string, value: any, rowIndex?: number): ValidationError | null => {
    const error = validationService.validateField(field, value);
    if (error && rowIndex !== undefined) {
      error.rowIndex = rowIndex;
    }
    return error;
  }, [validationService]);

  const clearValidationErrors = useCallback(() => {
    setValidationErrors([]);
  }, []);

  // Form generation
  const generateFormConfig = useCallback((type: 'hierarchy' | 'config' | 'classification' | 'search'): FormConfig => {
    switch (type) {
      case 'hierarchy':
        return formGeneratorService.generateHierarchyFormConfig();
      case 'config':
        return formGeneratorService.generateConfigFormConfig();
      case 'classification':
        return formGeneratorService.generateClassificationFormConfig();
      case 'search':
        return formGeneratorService.generateSearchFormConfig([]);
      default:
        return formGeneratorService.generateHierarchyFormConfig();
    }
  }, [formGeneratorService]);

  const generateFieldConfig = useCallback((fieldName: string, overrides?: Partial<FormFieldConfig>): FormFieldConfig => {
    return formGeneratorService.generateFieldConfig(fieldName, 'text', overrides);
  }, [formGeneratorService]);

  // UI helpers
  const getDisplayName = useCallback((fieldName: string): string => {
    const uiConfig = hierarchyConfig.ui_config || {};
    const displayNames = uiConfig.display_names || {};
    return displayNames[fieldName] || fieldName.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());
  }, [hierarchyConfig]);

  const getPlaceholder = useCallback((fieldName: string): string => {
    const uiConfig = hierarchyConfig.ui_config || {};
    const placeholders = uiConfig.placeholders || {};
    return placeholders[fieldName] || `Enter ${getDisplayName(fieldName).toLowerCase()}`;
  }, [hierarchyConfig, getDisplayName]);

  const getFieldError = useCallback((field: string, rowIndex?: number): string | undefined => {
    const error = validationErrors.find(e => 
      e.field === field && 
      (rowIndex === undefined || e.rowIndex === rowIndex)
    );
    return error?.message;
  }, [validationErrors]);

  const hasFieldError = useCallback((field: string, rowIndex?: number): boolean => {
    return !!getFieldError(field, rowIndex);
  }, [getFieldError]);

  // Actions
  const reset = useCallback(() => {
    setDataState(initialData);
    setValidationErrors([]);
    setIsDirty(false);
  }, [initialData]);

  const submit = useCallback(async (onSubmit: (data: HierarchyRow[]) => Promise<void>): Promise<boolean> => {
    const result = validateAll();
    
    if (!result.isValid) {
      return false;
    }

    try {
      await onSubmit(data);
      setIsDirty(false);
      return true;
    } catch (error) {
      console.error('Form submission error:', error);
      return false;
    }
  }, [data, validateAll]);

  return {
    // Data management
    data,
    setData,
    addRow,
    removeRow,
    updateRow,
    clearData,
    
    // Validation
    validationService,
    validationErrors,
    isValid,
    validateAll,
    validateRow,
    validateField,
    clearValidationErrors,
    
    // Form generation
    formGeneratorService,
    generateFormConfig,
    generateFieldConfig,
    
    // UI helpers
    getDisplayName,
    getPlaceholder,
    getFieldError,
    hasFieldError,
    
    // State
    isDirty,
    isValidating,
    
    // Actions
    reset,
    submit
  };
};

export default useDynamicForm;
