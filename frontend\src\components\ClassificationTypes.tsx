import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  Binary, 
  Layers3, 
  Tags, 
  TreePine, 
  LayoutGrid,
  CheckCircle2,
  ArrowRight
} from "lucide-react";

const classificationTypes = [
  {
    id: "binary",
    icon: Binary,
    title: "Binary Classification",
    description: "Two-class problems with clear yes/no decisions",
    examples: ["Spam vs. Not Spam", "Positive vs. Negative", "Fraud Detection"],
    color: "text-foreground",
    bgColor: "bg-primary/10"
  },
  {
    id: "multiclass",
    icon: Layers3,
    title: "Multi-class Classification",
    description: "Multiple mutually exclusive classes",
    examples: ["Sentiment Analysis", "Image Recognition", "Document Classification"],
    color: "text-foreground",
    bgColor: "bg-secondary/20"
  },
  {
    id: "multilabel",
    icon: Tags,
    title: "Multi-label Classification",
    description: "Multiple non-exclusive labels per instance",
    examples: ["Topic Tagging", "Movie Genres", "Medical Diagnosis"],
    color: "text-foreground",
    bgColor: "bg-accent/20"
  },
  {
    id: "hierarchical",
    icon: TreePine,
    title: "Hierarchical Classification",
    description: "Tree-structured class relationships",
    examples: ["Product Categories", "Taxonomies", "Organizational Structure"],
    color: "text-foreground",
    bgColor: "bg-muted/40"
  },
  {
    id: "flat",
    icon: LayoutGrid,
    title: "Flat Classification",
    description: "Non-hierarchical multi-class problems",
    examples: ["Language Detection", "Genre Classification", "Priority Levels"],
    color: "text-foreground",
    bgColor: "bg-border/20"
  }
];

export const ClassificationTypes = () => {
  return (
    <section id="classification" className="py-20 bg-muted/30">
      <div className="max-w-7xl mx-auto px-6">
        <div className="text-center mb-16">
          <Badge variant="outline" className="mb-4">
            <LayoutGrid className="w-4 h-4 mr-2" />
            Classification Types
          </Badge>
          <h2 className="text-4xl md:text-5xl font-bold mb-6 text-foreground">
            Support for All
            <span className="text-primary font-bold"> Classification Types</span>
          </h2>
          <p className="text-xl text-foreground/90 max-w-3xl mx-auto font-medium">
            From simple binary decisions to complex hierarchical structures,
            ClassyWeb handles every type of classification problem with ease.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {classificationTypes.map((type) => {
            const Icon = type.icon;
            return (
              <Card 
                key={type.id}
                className="group hover:shadow-card transition-all duration-300 border-border/50 hover:border-primary/20 bg-background relative overflow-hidden cursor-pointer"
              >
                <div className={`absolute top-0 right-0 w-20 h-20 ${type.bgColor} rounded-full -mr-10 -mt-10 opacity-50`} />
                
                <CardHeader className="relative z-10">
                  <div className={`w-12 h-12 rounded-lg ${type.bgColor} flex items-center justify-center mb-4 group-hover:scale-110 transition-transform`}>
                    <Icon className={`w-6 h-6 ${type.color}`} />
                  </div>
                  <CardTitle className="text-xl group-hover:text-primary transition-colors">
                    {type.title}
                  </CardTitle>
                  <CardDescription>
                    {type.description}
                  </CardDescription>
                </CardHeader>
                
                <CardContent className="pt-0 relative z-10">
                  <div className="space-y-2 mb-4">
                    <p className="text-sm font-medium text-foreground/80">Use Cases:</p>
                    {type.examples.map((example, idx) => (
                      <div key={idx} className="flex items-center gap-2 text-sm text-muted-foreground">
                        <CheckCircle2 className={`w-3 h-3 ${type.color} flex-shrink-0`} />
                        {example}
                      </div>
                    ))}
                  </div>
                  
                  <div className="flex items-center text-sm font-medium text-primary group-hover:text-primary/80 transition-colors">
                    Learn More
                    <ArrowRight className="w-4 h-4 ml-1 group-hover:translate-x-1 transition-transform" />
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>
      </div>
    </section>
  );
};
