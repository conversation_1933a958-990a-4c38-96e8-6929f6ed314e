/**
 * Test script for hierarchy detection functionality
 * Run this in browser console to test the hierarchy detection service
 */

// Simulate training data with hierarchy columns
const mockTrainingData = {
  fileId: 'test-training-123',
  fileInfo: {
    file_id: 'test-training-123',
    filename: 'training_data.csv',
    num_rows: 1000,
    columns: ['text', 'category', 'subcategory', 'type', 'level1', 'level2', 'domain', 'theme'],
    preview: [
      {
        text: 'Sample text content 1',
        category: 'Technology',
        subcategory: 'Software',
        type: 'Web Development',
        level1: 'Tech',
        level2: 'Programming',
        domain: 'IT',
        theme: 'Development'
      },
      {
        text: 'Sample text content 2',
        category: 'Business',
        subcategory: 'Marketing',
        type: 'Digital Marketing',
        level1: 'Business',
        level2: 'Promotion',
        domain: 'Commerce',
        theme: 'Advertising'
      },
      {
        text: 'Sample text content 3',
        category: 'Technology',
        subcategory: 'Hardware',
        type: 'Networking',
        level1: 'Tech',
        level2: 'Infrastructure',
        domain: 'IT',
        theme: 'Systems'
      }
    ]
  },
  purpose: 'training'
};

const mockClassificationData = {
  fileId: 'test-classification-456',
  fileInfo: {
    file_id: 'test-classification-456',
    filename: 'classification_data.csv',
    num_rows: 500,
    columns: ['text', 'title', 'author', 'date', 'url'],
    preview: [
      {
        text: 'Text to be classified 1',
        title: 'Article Title 1',
        author: 'Author 1',
        date: '2024-01-01',
        url: 'https://example.com/1'
      },
      {
        text: 'Text to be classified 2',
        title: 'Article Title 2',
        author: 'Author 2',
        date: '2024-01-02',
        url: 'https://example.com/2'
      }
    ]
  },
  purpose: 'classification'
};

const mockDualData = {
  trainingData: mockTrainingData,
  classificationData: mockClassificationData,
  dualUpload: true
};

// Function to test hierarchy detection
async function testHierarchyDetection() {
  console.log('Testing hierarchy detection...');
  
  // Store mock dual data in session storage
  sessionStorage.setItem('expertWorkflowDualData', JSON.stringify(mockDualData));
  
  console.log('Mock dual data stored in session storage');
  console.log('Training data columns:', mockTrainingData.fileInfo.columns);
  console.log('Classification data columns:', mockClassificationData.fileInfo.columns);
  
  // The hierarchy detection should detect these columns as hierarchy levels:
  // - category, subcategory, type (direct hierarchy indicators)
  // - level1, level2 (numbered levels)
  // - domain, theme (business hierarchy terms)
  
  console.log('Expected hierarchy columns to be detected:');
  console.log('- category (direct indicator)');
  console.log('- subcategory (direct indicator)');
  console.log('- type (direct indicator)');
  console.log('- level1 (numbered level)');
  console.log('- level2 (numbered level)');
  console.log('- domain (business term)');
  console.log('- theme (business term)');
  
  console.log('\nNow navigate to the hierarchical classification workflow to see the detection in action!');
  console.log('The manual configuration should show columns from TRAINING data, not classification data.');
}

// Function to clear test data
function clearTestData() {
  sessionStorage.removeItem('expertWorkflowDualData');
  console.log('Test data cleared from session storage');
}

// Export functions for testing
window.testHierarchyDetection = testHierarchyDetection;
window.clearTestData = clearTestData;

console.log('Hierarchy detection test functions loaded!');
console.log('Run testHierarchyDetection() to set up test data');
console.log('Run clearTestData() to clean up');
