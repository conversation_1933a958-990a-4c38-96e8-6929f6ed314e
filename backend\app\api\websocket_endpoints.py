"""WebSocket API Endpoints for Real-time Training Monitoring.

This module provides WebSocket endpoints for real-time communication
between the backend and frontend during training sessions.
"""

import logging
import json
from typing import Optional
from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
import jwt

from ..auth import get_current_user_from_token, get_current_user, SECRET_KEY, ALGORITHM
from ..websocket_manager import connection_manager, training_monitor, system_monitor, get_websocket_stats
from ..database import User

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/ws", tags=["websocket"])
security = HTTPBearer()


async def get_user_from_websocket_token(token: str) -> User:
    """Extract user from WebSocket token."""
    try:
        # Use the existing auth function
        from ..database import get_db
        from sqlalchemy.orm import Session

        with Session(bind=get_db().bind) as db:
            user = await get_current_user_from_token(token, db)
            return user

    except Exception as e:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid token")


@router.websocket("/training/{session_id}")
async def websocket_training_monitor(websocket: WebSocket, session_id: str, token: Optional[str] = None):
    """WebSocket endpoint for monitoring a specific training session."""
    try:
        # Authenticate user
        if not token:
            await websocket.close(code=4001, reason="Authentication token required")
            return
        
        try:
            user = await get_user_from_websocket_token(token)
        except HTTPException:
            await websocket.close(code=4001, reason="Invalid authentication token")
            return
        
        # Verify user has access to this session
        from ..database import get_db, TrainingSession
        from sqlalchemy.orm import Session
        
        with Session(bind=get_db().bind) as db:
            session = db.query(TrainingSession).filter(
                TrainingSession.id == session_id,
                TrainingSession.user_id == user.id
            ).first()
            
            if not session:
                await websocket.close(code=4004, reason="Session not found or access denied")
                return
        
        # Connect to WebSocket manager
        await connection_manager.connect(websocket, user.id, session_id)
        
        # Start monitoring this session
        await training_monitor.start_monitoring(session_id, user.id)
        
        try:
            # Keep connection alive and handle incoming messages
            while True:
                try:
                    # Wait for messages from client
                    data = await websocket.receive_text()
                    message = json.loads(data)
                    
                    # Handle different message types
                    if message.get('type') == 'ping':
                        await connection_manager.send_personal_message({
                            'type': 'pong',
                            'timestamp': message.get('timestamp')
                        }, websocket)
                    
                    elif message.get('type') == 'request_status':
                        # Send current session status
                        with Session(bind=get_db().bind) as db:
                            session = db.query(TrainingSession).filter(
                                TrainingSession.id == session_id
                            ).first()
                            
                            if session:
                                status_update = {
                                    'type': 'training_status',
                                    'session_id': session_id,
                                    'status': session.status.value,
                                    'progress_percentage': session.progress_percentage,
                                    'current_stage': session.current_stage,
                                    'progress_data': session.progress_data
                                }
                                await connection_manager.send_personal_message(status_update, websocket)
                    
                except json.JSONDecodeError:
                    logger.warning(f"Invalid JSON received from WebSocket for session {session_id}")
                except Exception as e:
                    logger.error(f"Error processing WebSocket message for session {session_id}: {e}")
                    break
                    
        except WebSocketDisconnect:
            logger.info(f"WebSocket disconnected for session {session_id}")
        
    except Exception as e:
        logger.error(f"WebSocket error for session {session_id}: {e}")
    finally:
        # Clean up
        connection_manager.disconnect(websocket)
        training_monitor.stop_monitoring(session_id)


@router.websocket("/system")
async def websocket_system_monitor(websocket: WebSocket, token: Optional[str] = None):
    """WebSocket endpoint for monitoring system-wide metrics."""
    try:
        # Authenticate user
        if not token:
            await websocket.close(code=4001, reason="Authentication token required")
            return
        
        try:
            user = await get_user_from_websocket_token(token)
        except HTTPException:
            await websocket.close(code=4001, reason="Invalid authentication token")
            return
        
        # Connect to WebSocket manager
        await connection_manager.connect(websocket, user.id)
        
        # Start system monitoring if not already active
        await system_monitor.start_system_monitoring()
        
        try:
            # Keep connection alive and handle incoming messages
            while True:
                try:
                    data = await websocket.receive_text()
                    message = json.loads(data)
                    
                    if message.get('type') == 'ping':
                        await connection_manager.send_personal_message({
                            'type': 'pong',
                            'timestamp': message.get('timestamp')
                        }, websocket)
                    
                    elif message.get('type') == 'request_metrics':
                        # Send current system metrics
                        from ..metrics_system_v2 import metrics_system
                        current_metrics = metrics_system.system_monitor.get_current_metrics()
                        
                        metrics_update = {
                            'type': 'system_metrics',
                            'metrics': {
                                'cpu_percent': current_metrics.cpu_percent,
                                'memory_percent': current_metrics.memory_percent,
                                'memory_used_gb': current_metrics.memory_used_gb,
                                'memory_total_gb': current_metrics.memory_total_gb,
                                'gpu_metrics': current_metrics.gpu_metrics
                            }
                        }
                        await connection_manager.send_personal_message(metrics_update, websocket)
                
                except json.JSONDecodeError:
                    logger.warning("Invalid JSON received from system WebSocket")
                except Exception as e:
                    logger.error(f"Error processing system WebSocket message: {e}")
                    break
                    
        except WebSocketDisconnect:
            logger.info("System WebSocket disconnected")
        
    except Exception as e:
        logger.error(f"System WebSocket error: {e}")
    finally:
        # Clean up
        connection_manager.disconnect(websocket)


@router.websocket("/user")
async def websocket_user_notifications(websocket: WebSocket, token: Optional[str] = None):
    """WebSocket endpoint for user-specific notifications and updates."""
    try:
        # Authenticate user
        if not token:
            await websocket.close(code=4001, reason="Authentication token required")
            return
        
        try:
            user = await get_user_from_websocket_token(token)
        except HTTPException:
            await websocket.close(code=4001, reason="Invalid authentication token")
            return
        
        # Connect to WebSocket manager
        await connection_manager.connect(websocket, user.id)
        
        # Send welcome message
        await connection_manager.send_personal_message({
            'type': 'welcome',
            'user_id': user.id,
            'username': user.username
        }, websocket)
        
        try:
            # Keep connection alive and handle incoming messages
            while True:
                try:
                    data = await websocket.receive_text()
                    message = json.loads(data)
                    
                    if message.get('type') == 'ping':
                        await connection_manager.send_personal_message({
                            'type': 'pong',
                            'timestamp': message.get('timestamp')
                        }, websocket)
                    
                    elif message.get('type') == 'subscribe_session':
                        # Subscribe to a specific training session
                        session_id = message.get('session_id')
                        if session_id:
                            # Verify access to session
                            from ..database import get_db, TrainingSession
                            from sqlalchemy.orm import Session
                            
                            with Session(bind=get_db().bind) as db:
                                session = db.query(TrainingSession).filter(
                                    TrainingSession.id == session_id,
                                    TrainingSession.user_id == user.id
                                ).first()
                                
                                if session:
                                    # Add to session connections
                                    if session_id not in connection_manager.active_connections:
                                        connection_manager.active_connections[session_id] = set()
                                    connection_manager.active_connections[session_id].add(websocket)
                                    
                                    # Update connection metadata
                                    connection_manager.connection_metadata[websocket]['session_id'] = session_id
                                    
                                    await connection_manager.send_personal_message({
                                        'type': 'subscribed',
                                        'session_id': session_id
                                    }, websocket)
                                else:
                                    await connection_manager.send_personal_message({
                                        'type': 'error',
                                        'message': 'Session not found or access denied'
                                    }, websocket)
                
                except json.JSONDecodeError:
                    logger.warning(f"Invalid JSON received from user WebSocket for user {user.id}")
                except Exception as e:
                    logger.error(f"Error processing user WebSocket message for user {user.id}: {e}")
                    break
                    
        except WebSocketDisconnect:
            logger.info(f"User WebSocket disconnected for user {user.id}")
        
    except Exception as e:
        logger.error(f"User WebSocket error: {e}")
    finally:
        # Clean up
        connection_manager.disconnect(websocket)


# REST endpoint for WebSocket statistics (for debugging/monitoring)
@router.get("/stats")
async def get_websocket_statistics(current_user: User = Depends(get_current_user)):
    """Get WebSocket connection and monitoring statistics."""
    return get_websocket_stats()
