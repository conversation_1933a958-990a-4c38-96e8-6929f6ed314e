// frontend/src/config.ts
// Frontend configuration constants

export const config = {
  // HF Training Defaults
  DEFAULT_BASE_MODEL: "bert-base-uncased",
  DEFAULT_EPOCHS: 3,
  DEFAULT_VALIDATION_SPLIT: 0.15,
  DEFAULT_HF_THRESHOLD: 0.5,

  // Default Hierarchy Levels (can be customized by users)
  DEFAULT_HIERARCHY_LEVELS: ["Level_1", "Level_2", "Level_3", "Level_4"],

  // Legacy hierarchy levels for backward compatibility
  LEGACY_HIERARCHY_LEVELS: ["Theme", "Category", "Segment", "Subsegment"],

  // Maximum number of hierarchy levels allowed
  MAX_HIERARCHY_LEVELS: 10,

  // API Defaults
  DEFAULT_OLLAMA_ENDPOINT: "http://localhost:11435",
  DEFAULT_GROQ_ENDPOINT: "https://api.groq.com/openai/v1",
  DEFAULT_OPENAI_ENDPOINT: "https://api.openai.com/v1",
  DEFAULT_GEMINI_ENDPOINT: "https://generativelanguage.googleapis.com",
  DEFAULT_OPENROUTER_ENDPOINT: "https://openrouter.ai/api/v1",

  // Model Defaults
  DEFAULT_GROQ_MODEL: "llama3-70b-8192",
  DEFAULT_OLLAMA_MODEL: "llama3:latest",
  DEFAULT_OPENAI_MODEL: "gpt-3.5-turbo",
  DEFAULT_GEMINI_MODEL: "gemini-pro",
  DEFAULT_OPENROUTER_MODEL: "openai/gpt-3.5-turbo",

  // Classification Defaults
  DEFAULT_LLM_TEMPERATURE: 0.1,

  // UI Defaults
  DEFAULT_LLM_SAMPLE_SIZE: 200,
  MIN_LLM_SAMPLE_SIZE: 50,
  MAX_LLM_SAMPLE_SIZE: 1000,

  // Provider List
  SUPPORTED_PROVIDERS: ["Groq", "Ollama", "OpenAI", "Gemini", "Openrouter"]
};

export default config;
