"""
Database setup script for ClassyWeb.

This script handles the initial database setup and migrations.
It checks if the database exists and runs the appropriate migrations.
"""
import os
import sys
import logging
import subprocess
from pathlib import Path
import sqlite3
from sqlalchemy import inspect

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Import our database modules using relative imports
from . import config # Correct relative import (now inside app)
from .database import Base, engine # Correct relative import (now inside app)

def get_db_path():
    """Get the database path from the DATABASE_URL."""
    db_url = config.DATABASE_URL
    if db_url.startswith('sqlite:///'):
        # Extract the path part after sqlite:///
        db_path = db_url.replace('sqlite:///', '')
        # If it's a relative path, make it absolute
        if not os.path.isabs(db_path):
            # Make path relative to the backend directory (where setup_db.py lives)
            db_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), db_path)
        return db_path
    return None

def check_if_db_exists():
    """Check if the database file exists."""
    db_path = get_db_path()
    if db_path and os.path.exists(db_path):
        return True
    return False

def check_if_tables_exist():
    """Check if the database tables exist."""
    db_path = get_db_path()
    if not db_path:
        return False

    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        conn.close()

        # Check if we have any tables
        return len(tables) > 0
    except sqlite3.Error as e:
        logger.error(f"SQLite error: {e}")
        return False

def create_tables_directly():
    """Create database tables directly using SQLAlchemy."""
    try:
        logger.info("Creating database tables directly using SQLAlchemy...")
        Base.metadata.create_all(bind=engine)
        logger.info("Database tables created successfully.")
        return True
    except Exception as e:
        logger.error(f"Error creating database tables: {e}")
        return False

def run_alembic_command(command, *args):
    """Run an Alembic command with arguments."""
    try:
        # Ensure alembic runs from the backend directory where alembic.ini is located
        backend_dir = os.path.dirname(os.path.abspath(__file__))
        cmd = ["alembic", command]
        if args:
            cmd.extend(args)

        logger.info(f"Running Alembic command: {' '.join(cmd)} in {backend_dir}")

        result = subprocess.run(
            cmd,
            check=True,
            capture_output=True,
            text=True,
            cwd=backend_dir # Set current working directory for alembic
        )
        logger.info(f"Alembic command output: {result.stdout}")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"Alembic command failed: {e.stderr}")
        return False
    except FileNotFoundError:
        logger.error("Alembic command not found. Make sure Alembic is installed and in your PATH.")
        return False


def check_and_fix_hf_rules_schema():
    """Check if the hf_rules table has the confidence_threshold column and add it if missing."""
    logger.info("Checking if hf_rules table has confidence_threshold column...")
    db_path = get_db_path()
    if not db_path:
        logger.error("Could not determine database path")
        return False

    try:
        # Connect to the database
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # Check if the hf_rules table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='hf_rules';")
        if not cursor.fetchone():
            logger.info("hf_rules table does not exist yet, will be created with proper schema")
            conn.close()
            return True

        # Check if the confidence_threshold column exists
        cursor.execute("PRAGMA table_info(hf_rules)")
        columns = [row[1] for row in cursor.fetchall()]

        if 'confidence_threshold' in columns:
            logger.info("confidence_threshold column already exists in hf_rules table")
            conn.close()
            return True

        # Add the missing column
        logger.warning("confidence_threshold column is missing from hf_rules table, adding it now...")
        cursor.execute("ALTER TABLE hf_rules ADD COLUMN confidence_threshold FLOAT")

        # Set default value for existing rows
        cursor.execute("UPDATE hf_rules SET confidence_threshold = 0.5 WHERE confidence_threshold IS NULL")

        # Commit the changes
        conn.commit()
        logger.info("Successfully added confidence_threshold column to hf_rules table")

        # Verify the column was added
        cursor.execute("PRAGMA table_info(hf_rules)")
        columns = [row[1] for row in cursor.fetchall()]
        logger.info(f"Updated columns in hf_rules table: {columns}")

        conn.close()
        return True
    except Exception as e:
        logger.error(f"Error checking/fixing hf_rules schema: {e}", exc_info=True)
        return False

def setup_database():
    """Set up the database."""
    # Ensure model artifacts directory exists
    try:
        config.MODEL_ARTIFACTS_DIR.mkdir(parents=True, exist_ok=True)
        logger.info(f"Model artifacts directory ensured: {config.MODEL_ARTIFACTS_DIR.resolve()}")
    except Exception as e:
        logger.error(f"Failed to create model artifacts directory: {e}", exc_info=True)

    # Check if database exists
    db_exists = check_if_db_exists()
    tables_exist = check_if_tables_exist()

    # Create database directory if needed for SQLite
    db_path = get_db_path()
    if db_path and not os.path.exists(os.path.dirname(os.path.abspath(db_path))):
        logger.info(f"Creating database directory: {os.path.dirname(os.path.abspath(db_path))}")
        os.makedirs(os.path.dirname(os.path.abspath(db_path)), exist_ok=True)

    # Check if users table specifically exists (most important for auth)
    users_table_exists = False
    if db_exists:
        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='users';")
            result = cursor.fetchone()
            conn.close()
            users_table_exists = result is not None
            logger.info(f"Users table exists: {users_table_exists}")
        except sqlite3.Error as e:
            logger.error(f"SQLite error checking for users table: {e}")

    if db_exists and tables_exist and users_table_exists:
        logger.info("Database already exists with tables including users table. Running migrations...")
        # Try running migrations
        try:
            if run_alembic_command("upgrade", "head"):
                logger.info("Database migrations completed successfully.")
                # Check and fix HF rules schema
                check_and_fix_hf_rules_schema()
                return True
            else:
                logger.warning("Alembic migrations failed, falling back to direct table creation.")
                result = create_tables_directly()
                # Check and fix HF rules schema
                check_and_fix_hf_rules_schema()
                return result
        except Exception as e:
            logger.warning(f"Error running Alembic migrations: {e}. Falling back to direct table creation.")
            result = create_tables_directly()
            # Check and fix HF rules schema
            check_and_fix_hf_rules_schema()
            return result
    elif db_exists and tables_exist:
        logger.warning("Database exists with some tables but users table is missing. Creating tables directly...")
        # Create tables directly to ensure users table is created
        result = create_tables_directly()
        # Check and fix HF rules schema
        check_and_fix_hf_rules_schema()
        return result
    elif db_exists:
        logger.info("Database exists but has no tables. Creating tables directly...")
        # Create tables directly
        result = create_tables_directly()
        # Check and fix HF rules schema
        check_and_fix_hf_rules_schema()
        return result
    else:
        logger.info("Database does not exist. Creating database and tables...")
        # Create database directory if needed
        if db_path:
            os.makedirs(os.path.dirname(os.path.abspath(db_path)), exist_ok=True)

        # Try running migrations, fall back to direct table creation
        try:
            if run_alembic_command("upgrade", "head"):
                logger.info("Database created and migrations completed successfully.")
                # Check and fix HF rules schema
                check_and_fix_hf_rules_schema()
                return True
            else:
                logger.warning("Alembic migrations failed, falling back to direct table creation.")
                result = create_tables_directly()
                # Check and fix HF rules schema
                check_and_fix_hf_rules_schema()
                return result
        except Exception as e:
            logger.warning(f"Error running Alembic migrations: {e}. Falling back to direct table creation.")
            result = create_tables_directly()
            # Check and fix HF rules schema
            check_and_fix_hf_rules_schema()
            return result

if __name__ == "__main__":
    setup_database()

