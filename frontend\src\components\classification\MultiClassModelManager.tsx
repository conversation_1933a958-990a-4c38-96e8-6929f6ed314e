/**
 * MultiClassModelManager.tsx
 *
 * Model management component for multi-class classification models.
 * Features strategy metadata, performance metrics, and model reuse functionality.
 */

import React, { useState, useEffect, useMemo } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useToast } from "@/hooks/use-toast";
import {
  Brain,
  Calendar,
  MoreVertical,
  Play,
  Trash2,
  Download,
  Eye,
  Grid3X3,
  Target,
  BarChart3,
  AlertCircle,
  Info,
  Loader2,
  Search,
  Filter,
  SortAsc,
  SortDesc,
  RefreshCw,
  CheckCircle2,
  XCircle,
  Clock,
  Settings
} from "lucide-react";
import { formatDistanceToNow } from 'date-fns';
import {
  listMultiClassModels,
  deleteMultiClassModel,
  MultiClassModel as ApiMultiClassModel,
  ListModelsParams
} from "@/services/multiClassApi";

// Use the API interface but create a local alias for compatibility
export type MultiClassModel = ApiMultiClassModel;

interface MultiClassModelManagerProps {
  onModelSelect: (model: MultiClassModel) => void;
  onModelUse: (modelId: string) => void;
  onModelDelete: (modelId: string) => void;
  selectedModelId?: string;
  showActions?: boolean;
}



export const MultiClassModelManager: React.FC<MultiClassModelManagerProps> = ({
  onModelSelect,
  onModelUse,
  onModelDelete,
  selectedModelId,
  showActions = true
}) => {
  const { toast } = useToast();

  // Enhanced state management
  const [models, setModels] = useState<MultiClassModel[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | 'training' | 'completed' | 'failed' | 'deployed'>('all');
  const [strategyFilter, setStrategyFilter] = useState<'all' | 'softmax' | 'ovr' | 'ovo'>('all');
  const [sortBy, setSortBy] = useState<'name' | 'created_at' | 'accuracy' | 'f1_score'>('created_at');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalModels, setTotalModels] = useState(0);

  // Load models with enhanced filtering and pagination
  const loadModels = async (params: Partial<ListModelsParams> = {}) => {
    try {
      setIsLoading(true);
      setError(null);

      const requestParams: ListModelsParams = {
        page: currentPage,
        limit: 10,
        sort_by: sortBy,
        sort_order: sortOrder,
        status: statusFilter,
        strategy: strategyFilter !== 'all' ? strategyFilter : undefined,
        search: searchTerm || undefined,
        ...params
      };

      const response = await listMultiClassModels(requestParams);
      setModels(response.models);
      setTotalModels(response.total);
    } catch (error: any) {
      console.error('Failed to load models:', error);
      setError(error.message || 'Failed to load multi-class models. Please check your connection and try again.');

      // Set empty state for production
      setModels([]);
      setTotalModels(0);

      // Only show error toast if it's not a fallback scenario
      if (!error.message?.includes('fallback')) {
        toast({
          title: "Models unavailable",
          description: "Model service is currently unavailable. You can still create new models.",
          variant: "default"
        });
      }
    } finally {
      setIsLoading(false);
    }
  };

  // Enhanced delete function with API integration
  const handleDeleteModel = async (modelId: string) => {
    try {
      await deleteMultiClassModel(modelId);
      setModels(prev => prev.filter(model => model.id !== modelId));
      onModelDelete(modelId);

      toast({
        title: "Model deleted",
        description: "Multi-class model has been deleted successfully"
      });
    } catch (error: any) {
      console.error('Failed to delete model:', error);
      toast({
        title: "Delete failed",
        description: error.message || "Failed to delete model",
        variant: "destructive"
      });
    }
  };

  // Enhanced model selection with API integration
  const handleUseModel = (modelId: string) => {
    const model = models.find(m => m.id === modelId);
    if (model) {
      onModelUse(modelId);
      onModelSelect(model);

      toast({
        title: "Model selected",
        description: `Using ${model.name} for classification`
      });
    }
  };

  // Reload models when filters change
  useEffect(() => {
    loadModels();
  }, [searchTerm, statusFilter, sortBy, sortOrder, currentPage]);

  // Filtered and sorted models for display
  const filteredModels = useMemo(() => {
    let filtered = [...models];

    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter(model =>
        model.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        model.metadata.class_names.some(className =>
          className.toLowerCase().includes(searchTerm.toLowerCase())
        ) ||
        model.base_model.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Apply status filter
    if (statusFilter !== 'all') {
      filtered = filtered.filter(model => model.status === statusFilter);
    }

    return filtered;
  }, [models, searchTerm, statusFilter]);

  // Handle search with debouncing
  const handleSearch = (value: string) => {
    setSearchTerm(value);
    setCurrentPage(1); // Reset to first page when searching
  };

  // Handle sort change
  const handleSortChange = (newSortBy: typeof sortBy) => {
    if (newSortBy === sortBy) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(newSortBy);
      setSortOrder('desc');
    }
    setCurrentPage(1);
  };

  // Handle filter change
  const handleFilterChange = (newFilter: typeof statusFilter) => {
    setStatusFilter(newFilter);
    setCurrentPage(1);
  };

  // Helper functions for UI styling
  const getStrategyBadgeColor = (strategy: string) => {
    switch (strategy) {
      case 'softmax': return 'default';
      case 'ovr': return 'secondary';
      case 'ovo': return 'outline';
      case 'auto': return 'destructive';
      default: return 'default';
    }
  };

  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case 'completed': return 'default';
      case 'training': return 'secondary';
      case 'failed': return 'destructive';
      case 'deployed': return 'outline';
      default: return 'default';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return CheckCircle2;
      case 'training': return Loader2;
      case 'failed': return XCircle;
      case 'deployed': return Target;
      default: return Clock;
    }
  };

  const formatModelSize = (sizeInMB: number) => {
    if (sizeInMB < 1024) {
      return `${sizeInMB.toFixed(1)} MB`;
    } else {
      return `${(sizeInMB / 1024).toFixed(1)} GB`;
    }
  };

  const formatTrainingTime = (timeInSeconds: number) => {
    const hours = Math.floor(timeInSeconds / 3600);
    const minutes = Math.floor((timeInSeconds % 3600) / 60);

    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    } else {
      return `${minutes}m`;
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <div className="flex items-center gap-2">
            <Loader2 className="w-4 h-4 animate-spin" />
            <span>Loading multi-class models...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          {error}
          <Button
            variant="outline"
            size="sm"
            className="ml-2"
            onClick={() => loadModels()}
          >
            Retry
          </Button>
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold flex items-center gap-2">
            <Brain className="w-5 h-5" />
            Multi-Class Model Manager
          </h3>
          <p className="text-sm text-muted-foreground">
            Manage and reuse your trained multi-class classification models ({totalModels} total)
          </p>
        </div>
        <Button variant="outline" onClick={() => loadModels()}>
          <RefreshCw className="w-4 h-4 mr-2" />
          Refresh
        </Button>
      </div>

      {/* Enhanced Filters and Search */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col sm:flex-row gap-4">
            {/* Search */}
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                <Input
                  placeholder="Search models by name, classes, or base model..."
                  value={searchTerm}
                  onChange={(e) => handleSearch(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            {/* Status Filter */}
            <Select value={statusFilter} onValueChange={handleFilterChange}>
              <SelectTrigger className="w-[180px]">
                <Filter className="w-4 h-4 mr-2" />
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
                <SelectItem value="training">Training</SelectItem>
                <SelectItem value="failed">Failed</SelectItem>
                <SelectItem value="deployed">Deployed</SelectItem>
              </SelectContent>
            </Select>

            {/* Sort Options */}
            <Select value={sortBy} onValueChange={handleSortChange}>
              <SelectTrigger className="w-[180px]">
                {sortOrder === 'asc' ? <SortAsc className="w-4 h-4 mr-2" /> : <SortDesc className="w-4 h-4 mr-2" />}
                <SelectValue placeholder="Sort by" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="created_at">Created Date</SelectItem>
                <SelectItem value="name">Name</SelectItem>
                <SelectItem value="accuracy">Accuracy</SelectItem>
                <SelectItem value="f1_score">F1 Score</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Models List */}
      {filteredModels.length === 0 ? (
        <Alert>
          <Info className="h-4 w-4" />
          <AlertDescription>
            {searchTerm || statusFilter !== 'all'
              ? "No models match your current filters. Try adjusting your search or filter criteria."
              : "No multi-class models found. Train your first model to see it here."
            }
          </AlertDescription>
        </Alert>
      ) : (
        <Card>
          <CardHeader>
            <CardTitle>Available Models ({filteredModels.length})</CardTitle>
            <CardDescription>
              Select a model to reuse or manage your trained models
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Model</TableHead>
                  <TableHead>Strategy</TableHead>
                  <TableHead>Classes</TableHead>
                  <TableHead>Performance</TableHead>
                  <TableHead>Training</TableHead>
                  <TableHead>Status</TableHead>
                  {showActions && <TableHead>Actions</TableHead>}
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredModels.map((model) => {
                  const StatusIcon = getStatusIcon(model.status);
                  return (
                  <TableRow
                    key={model.id}
                    className={`cursor-pointer ${
                      selectedModelId === model.id ? 'bg-muted/50' : ''
                    }`}
                    onClick={() => onModelSelect(model)}
                  >
                    <TableCell>
                      <div className="space-y-1">
                        <div className="font-medium">{model.name}</div>
                        <div className="text-xs text-muted-foreground">
                          {model.base_model}
                        </div>
                        {model.metadata.description && (
                          <div className="text-xs text-muted-foreground italic">
                            {model.metadata.description}
                          </div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant={getStrategyBadgeColor(model.training_config.strategy)}>
                        {model.training_config.strategy.toUpperCase()}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="space-y-1">
                        <div className="font-medium">{model.metadata.num_classes} classes</div>
                        <div className="text-xs text-muted-foreground">
                          {model.metadata.class_names.slice(0, 2).join(', ')}
                          {model.metadata.class_names.length > 2 && `, +${model.metadata.class_names.length - 2} more`}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="space-y-1">
                        <div className="text-sm">
                          Acc: {((model.metrics.accuracy || 0) * 100).toFixed(1)}%
                        </div>
                        <div className="text-xs text-muted-foreground">
                          F1: {((model.metrics.f1_score || 0) * 100).toFixed(1)}%
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="space-y-1">
                        <div className="text-sm">
                          {formatDistanceToNow(new Date(model.created_at), { addSuffix: true })}
                        </div>
                        <div className="text-xs text-muted-foreground">
                          {model.metrics.training_time && formatTrainingTime(model.metrics.training_time)}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <StatusIcon className="w-4 h-4" />
                        <Badge variant={getStatusBadgeColor(model.status)}>
                          {model.status}
                        </Badge>
                      </div>
                    </TableCell>
                    {showActions && (
                      <TableCell>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm">
                              <MoreVertical className="w-4 h-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => handleUseModel(model.id)}>
                              <Play className="w-4 h-4 mr-2" />
                              Use Model
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <Eye className="w-4 h-4 mr-2" />
                              View Details
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <Download className="w-4 h-4 mr-2" />
                              Export Model
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              onClick={() => handleDeleteModel(model.id)}
                              className="text-destructive"
                            >
                              <Trash2 className="w-4 h-4 mr-2" />
                              Delete
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    )}
                  </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      )}

      {/* Selected Model Details */}
      {selectedModelId && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Target className="w-4 h-4" />
              Model Details
            </CardTitle>
          </CardHeader>
          <CardContent>
            {(() => {
              const selectedModel = models.find(m => m.id === selectedModelId);
              if (!selectedModel) return <div>Model not found</div>;

              return (
                <div className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                      <h4 className="font-medium mb-3 flex items-center gap-2">
                        <Brain className="w-4 h-4" />
                        Model Information
                      </h4>
                      <div className="space-y-2 text-sm">
                        <div><strong>Strategy:</strong> {selectedModel.training_config.strategy.toUpperCase()}</div>
                        <div><strong>Base Model:</strong> {selectedModel.base_model}</div>
                        <div><strong>Classes:</strong> {selectedModel.metadata.num_classes}</div>
                        <div><strong>Model Size:</strong> {formatModelSize(selectedModel.metadata.model_size)}</div>
                        <div><strong>Total Samples:</strong> {selectedModel.metadata.total_samples.toLocaleString()}</div>
                      </div>
                    </div>
                    <div>
                      <h4 className="font-medium mb-3 flex items-center gap-2">
                        <BarChart3 className="w-4 h-4" />
                        Performance Metrics
                      </h4>
                      <div className="space-y-2 text-sm">
                        <div><strong>Accuracy:</strong> {((selectedModel.metrics.accuracy || 0) * 100).toFixed(2)}%</div>
                        <div><strong>F1 Score:</strong> {((selectedModel.metrics.f1_score || 0) * 100).toFixed(2)}%</div>
                        <div><strong>Precision:</strong> {((selectedModel.metrics.precision || 0) * 100).toFixed(2)}%</div>
                        <div><strong>Recall:</strong> {((selectedModel.metrics.recall || 0) * 100).toFixed(2)}%</div>
                        {selectedModel.metrics.training_time && (
                          <div><strong>Training Time:</strong> {formatTrainingTime(selectedModel.metrics.training_time)}</div>
                        )}
                      </div>
                    </div>
                    <div>
                      <h4 className="font-medium mb-3 flex items-center gap-2">
                        <Settings className="w-4 h-4" />
                        Training Config
                      </h4>
                      <div className="space-y-2 text-sm">
                        <div><strong>Epochs:</strong> {selectedModel.training_config.epochs}</div>
                        <div><strong>Batch Size:</strong> {selectedModel.training_config.batch_size}</div>
                        <div><strong>Learning Rate:</strong> {selectedModel.training_config.learning_rate}</div>
                        <div><strong>Loss Function:</strong> {selectedModel.training_config.loss_function}</div>
                        <div><strong>Validation Split:</strong> {(selectedModel.training_config.validation_split * 100).toFixed(0)}%</div>
                      </div>
                    </div>
                  </div>

                  {/* Per-class metrics */}
                  {selectedModel.metrics.per_class_metrics && selectedModel.metrics.per_class_metrics.length > 0 && (
                    <div>
                      <h4 className="font-medium mb-3 flex items-center gap-2">
                        <Target className="w-4 h-4" />
                        Per-Class Performance
                      </h4>
                      <div className="grid grid-cols-1 gap-2">
                        {selectedModel.metrics.per_class_metrics.map((classMetric) => (
                          <div key={classMetric.class} className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                            <span className="font-medium">{classMetric.class}</span>
                            <div className="flex gap-4 text-sm">
                              <span>P: {(classMetric.precision * 100).toFixed(1)}%</span>
                              <span>R: {(classMetric.recall * 100).toFixed(1)}%</span>
                              <span>F1: {(classMetric.f1_score * 100).toFixed(1)}%</span>
                              <span className="text-muted-foreground">({classMetric.support} samples)</span>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Class Names */}
                  <div>
                    <h4 className="font-medium mb-3">Class Names</h4>
                    <div className="flex flex-wrap gap-2">
                      {selectedModel.metadata.class_names.map((className) => (
                        <Badge key={className} variant="outline">
                          {className}
                        </Badge>
                      ))}
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="flex gap-2 pt-4">
                    <Button onClick={() => handleUseModel(selectedModel.id)}>
                      <Play className="w-4 h-4 mr-2" />
                      Use This Model
                    </Button>
                    <Button variant="outline">
                      <Download className="w-4 h-4 mr-2" />
                      Export Model
                    </Button>
                  </div>
                </div>
              );
            })()}
          </CardContent>
        </Card>
      )}
    </div>
  );
};
