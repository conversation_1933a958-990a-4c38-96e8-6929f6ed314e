"""
File management API endpoints with enhanced security.
"""
import os
import uuid
import shutil
import logging
import mimetypes
import hashlib
from pathlib import Path
from typing import Any, Optional

from fastapi import APIRouter, UploadFile, File, HTTPException, Depends, Path as FastApiPath
from sqlalchemy.orm import Session

# Import models and database functions
from ..models.common import FileInfo
from ..models.file import FileListResponse, FileUpdateRequest
from ..models.auth import User, MessageResponse # Import Pydantic User model for type hinting
from ..config import UPLOAD_DIR
from ..utils.helpers import load_data
from ..services.data_analysis_service import DataStructureDetector

# Corrected imports
from ..database import get_db, get_file, get_files, create_file, update_file, delete_file # Use absolute import
from ..auth import get_optional_current_user, get_current_active_user # Import the user dependencies
from ..services.data_analysis_service import data_structure_detector

# Configure logging
logger = logging.getLogger(__name__)

# Security constants
MAX_FILE_SIZE = 100 * 1024 * 1024  # 100MB
ALLOWED_EXTENSIONS = {'.csv', '.xlsx', '.xls', '.json', '.txt'}
ALLOWED_MIME_TYPES = {
    'text/csv',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'application/json',
    'text/plain'
}

def validate_file_security(file: UploadFile) -> None:
    """Validate file for security issues."""
    if not file.filename:
        raise HTTPException(status_code=400, detail="Filename is required")

    # Check file extension
    file_ext = Path(file.filename).suffix.lower()
    if file_ext not in ALLOWED_EXTENSIONS:
        raise HTTPException(
            status_code=400,
            detail=f"File type not allowed. Allowed types: {', '.join(ALLOWED_EXTENSIONS)}"
        )

    # Check file size
    if hasattr(file, 'size') and file.size and file.size > MAX_FILE_SIZE:
        raise HTTPException(
            status_code=400,
            detail=f"File too large. Maximum size: {MAX_FILE_SIZE // (1024*1024)}MB"
        )

    # Validate filename for path traversal
    if '..' in file.filename or '/' in file.filename or '\\' in file.filename:
        raise HTTPException(status_code=400, detail="Invalid filename")

def sanitize_filename(filename: str) -> str:
    """Sanitize filename to prevent security issues."""
    # Remove path components
    filename = os.path.basename(filename)
    # Remove dangerous characters
    dangerous_chars = '<>:"/\\|?*'
    for char in dangerous_chars:
        filename = filename.replace(char, '_')
    return filename

# Create router
router = APIRouter(prefix="/files", tags=["Data Handling"])

@router.post("/upload", response_model=FileInfo)
async def upload_data(
    file: UploadFile = File(...),
    db: Session = Depends(get_db),
    current_user: Optional[User] = Depends(get_optional_current_user) # Use the proper dependency
):
    """
    Uploads a CSV or Excel file, saves it temporarily, loads it into a DataFrame,
    and returns file metadata including a preview.
    """
    # Security validation
    validate_file_security(file)

    # Generate a unique ID for this upload
    file_id = str(uuid.uuid4())
    # Define the path where the file will be saved temporarily
    file_location = UPLOAD_DIR / file_id
    original_filename = sanitize_filename(file.filename or "unknown_file")

    logger.info(f"Received upload request for '{original_filename}'. Assigning ID: {file_id}")

    try:
        # Ensure upload directory exists and is secure
        UPLOAD_DIR.mkdir(parents=True, exist_ok=True)

        # Read file content with size validation
        content = await file.read()
        if len(content) > MAX_FILE_SIZE:
            raise HTTPException(
                status_code=400,
                detail=f"File too large. Maximum size: {MAX_FILE_SIZE // (1024*1024)}MB"
            )

        # Validate MIME type
        detected_type = mimetypes.guess_type(original_filename)[0]
        if detected_type and detected_type not in ALLOWED_MIME_TYPES:
            raise HTTPException(
                status_code=400,
                detail=f"File type not allowed: {detected_type}"
            )

        # Calculate file hash for integrity
        file_hash = hashlib.sha256(content).hexdigest()

        # Save the uploaded file to the temporary location securely
        logger.info(f"Saving uploaded file to: {file_location}")
        with open(file_location, "wb") as file_object:
            file_object.write(content)

        # Set secure file permissions (read-only for owner)
        os.chmod(file_location, 0o600)
        logger.info(f"Successfully saved '{original_filename}' to {file_location} with hash {file_hash[:8]}...")

        # Process the saved file using the refactored utility function
        # Pass both the saved file path AND the original filename
        df = load_data(file_path=file_location, original_filename=original_filename)

        if df is None:
            logger.error(f"Failed to load DataFrame from uploaded file: {file_location} (Original: '{original_filename}')")
            if file_location.exists():
                os.remove(file_location)
            raise HTTPException(status_code=400, detail=f"Could not process uploaded file '{original_filename}'. Unsupported format or file error.")
        # Prepare the preview data
        preview_records = df.head(5).to_dict('records') # Get first 5 rows as list of dicts

        # Store file information in the database
        file_data = {
            "id": file_id,
            "filename": original_filename,
            "file_path": str(file_location),
            "file_size": os.path.getsize(file_location),
            "num_rows": len(df),
            "columns": df.columns.tolist(),
            "user_id": current_user.id if current_user else None
        }

        # Create file record in database
        create_file(db, file_data)
        logger.info(f"Saved file information to database with ID: {file_id}")

        # Prepare the response model
        file_info = FileInfo(
            file_id=file_id,
            filename=original_filename,
            columns=df.columns.tolist(), # Get column names as list of strings
            num_rows=len(df),
            preview=preview_records
        )
        logger.info(f"Successfully processed file ID {file_id}. Rows: {len(df)}, Cols: {len(df.columns)}")
        return file_info

    except HTTPException as http_exc:
         # Re-raise HTTPExceptions directly
         raise http_exc
    except Exception as e:
        logger.error(f"Error during file upload processing for '{original_filename}': {e}", exc_info=True)
        # Clean up the potentially corrupted file
        if file_location.exists():
            try:
                os.remove(file_location)
                logger.info(f"Cleaned up failed upload file: {file_location}")
            except Exception as cleanup_e:
                 logger.error(f"Failed to cleanup upload file {file_location}: {cleanup_e}")
        # Return a generic server error
        raise HTTPException(status_code=500, detail=f"An unexpected error occurred during file upload: {e}")
    finally:
         # Ensure the uploaded file handle is closed
         if hasattr(file, 'file') and hasattr(file.file, 'close'):
              file.file.close()

@router.get("", response_model=FileListResponse)
async def list_files(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
    current_user: Optional[User] = Depends(get_optional_current_user)
):
    """
    Retrieves a list of uploaded files with pagination.
    """
    logger.info(f"Request received to list files with skip={skip}, limit={limit}")

    # Get files from database, filtered by user if authenticated
    user_id = current_user.id if current_user else None
    db_files = get_files(db, skip=skip, limit=limit, user_id=user_id)

    # Convert to response model
    files = []
    for file in db_files:
        # Create a preview with empty list since we don't load the actual data here
        files.append(FileInfo(
            file_id=file.id,
            filename=file.filename,
            columns=file.columns,
            num_rows=file.num_rows,
            preview=[]
        ))

    return FileListResponse(files=files)

@router.get("/{file_id}", response_model=FileInfo)
async def get_file_details(
    file_id: str = FastApiPath(..., description="ID of the file to retrieve"),
    db: Session = Depends(get_db),
    current_user: Optional[User] = Depends(get_optional_current_user)
):
    """
    Retrieves detailed information about a specific file, including a preview of its contents.
    """
    logger.info(f"Request received to get details for file ID: {file_id}")

    # Get file from database
    db_file = get_file(db, file_id)
    if not db_file:
        logger.warning(f"File ID not found: {file_id}")
        raise HTTPException(status_code=404, detail="File not found")

    # Check if the user has access to this file
    if current_user and db_file.user_id and db_file.user_id != current_user.id:
        logger.warning(f"User {current_user.id} attempted to access file {file_id} owned by user {db_file.user_id}")
        raise HTTPException(status_code=403, detail="You don't have permission to access this file")

    # Load the file to get a preview
    file_path = Path(db_file.file_path)
    if not file_path.exists():
        logger.error(f"File not found on disk: {file_path}")
        raise HTTPException(status_code=404, detail="File not found on disk")

    try:
        # Load the file data
        df = load_data(file_path=file_path, original_filename=db_file.filename)
        if df is None:
            raise ValueError(f"Failed to load data from file: {file_path}")

        # Get preview
        preview_records = df.head(5).to_dict('records')

        # Return file info with preview
        return FileInfo(
            file_id=db_file.id,
            filename=db_file.filename,
            columns=db_file.columns,
            num_rows=db_file.num_rows,
            preview=preview_records
        )
    except Exception as e:
        logger.error(f"Error loading file {file_id}: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Error loading file: {e}")


@router.put("/{file_id}", response_model=FileInfo)
async def update_file_details(
    file_id: str = FastApiPath(..., description="ID of the file to update"),
    file_update: FileUpdateRequest = File(...),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Updates file information such as filename.
    """
    logger.info(f"Request received to update file ID: {file_id}")

    # Get file from database
    db_file = get_file(db, file_id)
    if not db_file:
        logger.warning(f"File ID not found: {file_id}")
        raise HTTPException(status_code=404, detail="File not found")

    # Check if the user has permission to update this file
    if db_file.user_id and db_file.user_id != current_user.id:
        logger.warning(f"User {current_user.id} attempted to update file {file_id} owned by user {db_file.user_id}")
        raise HTTPException(status_code=403, detail="You don't have permission to update this file")

    # Update the file record
    update_data = {}
    if file_update.filename:
        update_data["filename"] = file_update.filename

    if not update_data:
        # No changes requested
        logger.info(f"No changes requested for file {file_id}")

        # Return current file info
        try:
            # Load the file data for preview
            file_path = Path(db_file.file_path)
            df = load_data(file_path=file_path, original_filename=db_file.filename)
            preview_records = df.head(5).to_dict('records') if df is not None else []

            return FileInfo(
                file_id=db_file.id,
                filename=db_file.filename,
                columns=db_file.columns,
                num_rows=db_file.num_rows,
                preview=preview_records
            )
        except Exception as e:
            logger.error(f"Error loading file {file_id} for preview: {e}", exc_info=True)
            # Return without preview if loading fails
            return FileInfo(
                file_id=db_file.id,
                filename=db_file.filename,
                columns=db_file.columns,
                num_rows=db_file.num_rows,
                preview=[]
            )

    try:
        # Update the file record
        updated_file = update_file(db, file_id, update_data)
        logger.info(f"Successfully updated file {file_id}")

        # Return updated file info with preview
        try:
            # Load the file data for preview
            file_path = Path(updated_file.file_path)
            df = load_data(file_path=file_path, original_filename=updated_file.filename)
            preview_records = df.head(5).to_dict('records') if df is not None else []

            return FileInfo(
                file_id=updated_file.id,
                filename=updated_file.filename,
                columns=updated_file.columns,
                num_rows=updated_file.num_rows,
                preview=preview_records
            )
        except Exception as e:
            logger.error(f"Error loading updated file {file_id} for preview: {e}", exc_info=True)
            # Return without preview if loading fails
            return FileInfo(
                file_id=updated_file.id,
                filename=updated_file.filename,
                columns=updated_file.columns,
                num_rows=updated_file.num_rows,
                preview=[]
            )
    except Exception as e:
        logger.error(f"Error updating file {file_id}: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Error updating file: {e}")


@router.delete("/{file_id}", response_model=MessageResponse)
async def delete_file_endpoint(
    file_id: str = FastApiPath(..., description="ID of the file to delete"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Deletes a file from the database and filesystem.
    """
    logger.info(f"Request received to delete file ID: {file_id}")

    # Get file from database
    db_file = get_file(db, file_id)
    if not db_file:
        logger.warning(f"File ID not found: {file_id}")
        raise HTTPException(status_code=404, detail="File not found")

    # Check if the user has permission to delete this file
    if db_file.user_id and db_file.user_id != current_user.id:
        logger.warning(f"User {current_user.id} attempted to delete file {file_id} owned by user {db_file.user_id}")
        raise HTTPException(status_code=403, detail="You don't have permission to delete this file")

    # Check if the file is associated with any tasks
    if db_file.tasks and len(db_file.tasks) > 0:
        logger.warning(f"File {file_id} is associated with {len(db_file.tasks)} tasks and cannot be deleted")
        raise HTTPException(
            status_code=409,
            detail="This file is associated with one or more tasks and cannot be deleted. Delete the tasks first."
        )

    # Delete the file
    success = delete_file(db, file_id)
    if not success:
        logger.error(f"Failed to delete file {file_id}")
        raise HTTPException(status_code=500, detail="Failed to delete file")

    logger.info(f"Successfully deleted file {file_id}")
    return MessageResponse(message=f"File '{db_file.filename}' deleted successfully")


@router.post("/{file_id}/analyze")
async def analyze_file_structure(
    file_id: str = FastApiPath(..., description="ID of the file to analyze"),
    text_column: Optional[str] = None,
    db: Session = Depends(get_db),
    current_user: Optional[User] = Depends(get_optional_current_user)
):
    """
    Analyze the structure of an uploaded file to detect optimal classification approach.
    Returns suggestions for hierarchical vs flat classification with confidence scores.
    """
    logger.info(f"Request received to analyze structure for file ID: {file_id}")

    # Get file from database
    db_file = get_file(db, file_id)
    if not db_file:
        logger.warning(f"File ID not found: {file_id}")
        raise HTTPException(status_code=404, detail="File not found")

    # Check file access permissions
    if current_user and db_file.user_id and db_file.user_id != current_user.id:
        logger.warning(f"User {current_user.id} attempted to access file {file_id} owned by user {db_file.user_id}")
        raise HTTPException(status_code=403, detail="Access denied")

    # Load file data
    file_path = Path(db_file.file_path)
    if not file_path.exists():
        logger.error(f"File not found on disk: {file_path}")
        raise HTTPException(status_code=404, detail="File not found on disk")

    try:
        # Load the file data
        df = load_data(file_path=file_path, original_filename=db_file.filename)
        if df is None:
            raise ValueError(f"Failed to load data from file: {file_path}")

        # Perform smart data structure analysis
        analysis = data_structure_detector.analyze_data_structure(df, text_column)

        # Convert dataclass to dict for JSON response
        response_data = {
            'detected_structure': analysis.detected_structure,
            'confidence': analysis.confidence,
            'suggestions': [
                {
                    'structure_type': s.structure_type,
                    'confidence': s.confidence,
                    'reasoning': s.reasoning,
                    'suggested_config': s.suggested_config,
                    'preview_data': s.preview_data
                }
                for s in analysis.suggestions
            ],
            'preview': analysis.preview,
            'column_analysis': analysis.column_analysis,
            'data_quality': analysis.data_quality
        }

        logger.info(f"Successfully analyzed file {file_id}: {analysis.detected_structure} (confidence: {analysis.confidence:.2f})")
        return response_data

    except Exception as e:
        logger.error(f"Error analyzing file {file_id}: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Error analyzing file: {e}")


@router.post("/{file_id}/validate-configuration")
async def validate_data_configuration(
    configuration: dict,
    file_id: str = FastApiPath(..., description="ID of the file to validate"),
    db: Session = Depends(get_db),
    current_user: Optional[User] = Depends(get_optional_current_user)
):
    """
    Validate a data configuration and provide performance estimates.
    """
    logger.info(f"Request received to validate configuration for file ID: {file_id}")

    # Get file from database
    db_file = get_file(db, file_id)
    if not db_file:
        raise HTTPException(status_code=404, detail="File not found")

    # Check file access permissions
    if current_user and db_file.user_id and db_file.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="Access denied")

    try:
        # Load file data
        file_path = Path(db_file.file_path)
        df = load_data(file_path=file_path, original_filename=db_file.filename)
        if df is None:
            raise ValueError(f"Failed to load data from file: {file_path}")

        # Validate configuration
        warnings = []
        suggestions = []
        is_valid = True

        # Check if specified columns exist
        text_column = configuration.get('text_column')
        label_columns = configuration.get('label_columns', [])

        if text_column not in df.columns:
            warnings.append(f"Text column '{text_column}' not found in data")
            is_valid = False

        for col in label_columns:
            if col not in df.columns:
                warnings.append(f"Label column '{col}' not found in data")
                is_valid = False

        # Check data quality
        if text_column and text_column in df.columns:
            text_data = df[text_column].dropna()
            avg_length = text_data.astype(str).str.len().mean()

            if avg_length < 10:
                warnings.append("Text column has very short content (avg < 10 chars)")
                suggestions.append("Consider using a different text column with more descriptive content")

            if len(text_data) < 50:
                warnings.append("Very small dataset (< 50 samples)")
                suggestions.append("Consider collecting more data for better model performance")

        # Estimate performance
        data_size = len(df)
        complexity_score = len(label_columns) + (len(configuration.get('hierarchy_levels', [])) * 2)

        # Simple heuristic for accuracy estimation
        base_accuracy = 0.7
        if data_size > 1000:
            base_accuracy += 0.1
        if data_size > 5000:
            base_accuracy += 0.1
        if complexity_score < 5:
            base_accuracy += 0.05

        accuracy_estimate = min(base_accuracy, 0.95)

        # Training time estimation
        if configuration.get('training_parameters', {}).get('approach') == 'LLM':
            time_estimate = "2-5 minutes"
        else:
            base_time = 10 if data_size < 1000 else 20 if data_size < 5000 else 30
            if configuration.get('training_parameters', {}).get('use_unsloth'):
                base_time = int(base_time * 0.5)
            time_estimate = f"{base_time}-{base_time + 10} minutes"

        return {
            'is_valid': is_valid,
            'warnings': warnings,
            'suggestions': suggestions,
            'estimated_performance': {
                'accuracy_estimate': accuracy_estimate,
                'training_time_estimate': time_estimate,
                'complexity_score': complexity_score
            }
        }

    except Exception as e:
        logger.error(f"Error validating configuration for file {file_id}: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Error validating configuration: {e}")


@router.post("/{file_id}/column-mapping-suggestions")
async def get_column_mapping_suggestions(
    request_data: dict,
    file_id: str = FastApiPath(..., description="ID of the file to analyze"),
    db: Session = Depends(get_db),
    current_user: Optional[User] = Depends(get_optional_current_user)
):
    """
    Get smart suggestions for column mapping based on target structure.
    """
    logger.info(f"Request received for column mapping suggestions for file ID: {file_id}")

    # Get file from database
    db_file = get_file(db, file_id)
    if not db_file:
        raise HTTPException(status_code=404, detail="File not found")

    # Check file access permissions
    if current_user and db_file.user_id and db_file.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="Access denied")

    try:
        # Load file data
        file_path = Path(db_file.file_path)
        df = load_data(file_path=file_path, original_filename=db_file.filename)
        if df is None:
            raise ValueError(f"Failed to load data from file: {file_path}")

        # Analyze data structure
        analysis = data_structure_detector.analyze_data_structure(df)
        target_structure = request_data.get('target_structure', 'hierarchical')

        # Generate text column suggestions
        text_suggestions = []
        for col, info in analysis.column_analysis.items():
            if info['is_potential_text']:
                confidence = 0.9 if info['type'] == 'text' else 0.6
                reasoning = f"Column contains text data with avg length {info['sample_values']}"
                text_suggestions.append({
                    'column': col,
                    'confidence': confidence,
                    'reasoning': reasoning
                })

        # Generate label column suggestions
        label_suggestions = []
        for col, info in analysis.column_analysis.items():
            if info['is_potential_label']:
                confidence = 0.8 if info['uniqueness_ratio'] < 0.2 else 0.6
                role = 'hierarchy_level' if target_structure == 'hierarchical' else 'label'
                reasoning = f"Column has {info['unique_count']} unique values ({info['uniqueness_ratio']:.1%} uniqueness)"
                label_suggestions.append({
                    'column': col,
                    'suggested_role': role,
                    'confidence': confidence,
                    'reasoning': reasoning
                })

        response = {
            'text_column_suggestions': sorted(text_suggestions, key=lambda x: x['confidence'], reverse=True),
            'label_column_suggestions': sorted(label_suggestions, key=lambda x: x['confidence'], reverse=True)
        }

        # Add hierarchy suggestions for hierarchical structure
        if target_structure == 'hierarchical':
            hierarchy_suggestions = []
            for i, col in enumerate(analysis.preview['label_columns'][:6]):
                level_name = f"Level {i + 1}"
                hierarchy_suggestions.append({
                    'level_name': level_name,
                    'suggested_columns': [col],
                    'confidence': 0.7
                })
            response['hierarchy_suggestions'] = hierarchy_suggestions

        return response

    except Exception as e:
        logger.error(f"Error getting column mapping suggestions for file {file_id}: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Error getting suggestions: {e}")


@router.post("/{file_id}/analyze")
async def analyze_file_structure_endpoint(
    file_id: str = FastApiPath(..., description="ID of the file to analyze"),
    request_data: dict = {},
    db: Session = Depends(get_db),
    current_user: Optional[User] = Depends(get_optional_current_user)
):
    """Analyze file structure and detect classification patterns."""
    try:
        # Initialize data structure detector
        data_structure_detector = DataStructureDetector()

        # Get file from database
        file_record = get_file(db, file_id)
        if not file_record:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="File not found"
            )

        # Analyze file structure
        analysis = data_structure_detector.analyze_file_structure(
            file_record.file_path,
            sample_size=request_data.get('sample_size', 1000)
        )

        return analysis

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error analyzing file structure for file {file_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to analyze file structure"
        )
