/**
 * unifiedErrorHandler.ts
 * 
 * Unified error handling system for ClassyWeb Phase 4 implementation
 * Provides consistent error handling and user feedback across all workflows
 */

import { toast } from '@/hooks/use-toast';

export type ErrorSeverity = 'low' | 'medium' | 'high' | 'critical';
export type ErrorCategory = 'network' | 'validation' | 'file' | 'training' | 'inference' | 'system' | 'user';

export interface ClassyWebError {
  id: string;
  code: string;
  message: string;
  details?: string;
  category: ErrorCategory;
  severity: ErrorSeverity;
  timestamp: string;
  context?: {
    workflowId?: string;
    step?: number;
    fileId?: string;
    userId?: string;
    sessionId?: string;
  };
  originalError?: Error;
  recoveryActions?: ErrorRecoveryAction[];
  userMessage?: string;
  technicalMessage?: string;
}

export interface ErrorRecoveryAction {
  id: string;
  label: string;
  description: string;
  action: () => Promise<boolean>;
  primary?: boolean;
}

export interface ErrorHandlerConfig {
  enableLogging: boolean;
  enableToasts: boolean;
  enableRetry: boolean;
  maxRetryAttempts: number;
  retryDelay: number;
  enableReporting: boolean;
}

/**
 * Unified error handler for consistent error management
 */
export class UnifiedErrorHandler {
  private static instance: UnifiedErrorHandler;
  private config: ErrorHandlerConfig;
  private errorLog: ClassyWebError[] = [];
  private readonly MAX_LOG_SIZE = 100;

  private constructor(config?: Partial<ErrorHandlerConfig>) {
    this.config = {
      enableLogging: true,
      enableToasts: true,
      enableRetry: true,
      maxRetryAttempts: 3,
      retryDelay: 1000,
      enableReporting: false,
      ...config
    };
  }

  static getInstance(config?: Partial<ErrorHandlerConfig>): UnifiedErrorHandler {
    if (!UnifiedErrorHandler.instance) {
      UnifiedErrorHandler.instance = new UnifiedErrorHandler(config);
    }
    return UnifiedErrorHandler.instance;
  }

  /**
   * Handle an error with unified processing
   */
  async handleError(
    error: Error | ClassyWebError,
    context?: ClassyWebError['context'],
    recoveryActions?: ErrorRecoveryAction[]
  ): Promise<ClassyWebError> {
    let classyError: ClassyWebError;

    if (this.isClassyWebError(error)) {
      classyError = error;
    } else {
      classyError = this.createClassyWebError(error, context, recoveryActions);
    }

    // Log the error
    if (this.config.enableLogging) {
      this.logError(classyError);
    }

    // Show user notification
    if (this.config.enableToasts) {
      this.showErrorToast(classyError);
    }

    // Report error if enabled
    if (this.config.enableReporting) {
      await this.reportError(classyError);
    }

    return classyError;
  }

  /**
   * Create a ClassyWebError from a generic Error
   */
  private createClassyWebError(
    error: Error,
    context?: ClassyWebError['context'],
    recoveryActions?: ErrorRecoveryAction[]
  ): ClassyWebError {
    const category = this.categorizeError(error);
    const severity = this.determineSeverity(error, category);
    const { userMessage, technicalMessage } = this.generateMessages(error, category);

    return {
      id: `error-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      code: this.generateErrorCode(error, category),
      message: error.message,
      details: error.stack,
      category,
      severity,
      timestamp: new Date().toISOString(),
      context,
      originalError: error,
      recoveryActions: recoveryActions || this.generateRecoveryActions(error, category),
      userMessage,
      technicalMessage
    };
  }

  /**
   * Categorize error based on type and message
   */
  private categorizeError(error: Error): ErrorCategory {
    const message = error.message.toLowerCase();
    
    if (message.includes('network') || message.includes('fetch') || message.includes('connection')) {
      return 'network';
    }
    if (message.includes('validation') || message.includes('invalid') || message.includes('required')) {
      return 'validation';
    }
    if (message.includes('file') || message.includes('upload') || message.includes('download')) {
      return 'file';
    }
    if (message.includes('training') || message.includes('model') || message.includes('epoch')) {
      return 'training';
    }
    if (message.includes('inference') || message.includes('prediction') || message.includes('classification')) {
      return 'inference';
    }
    if (message.includes('permission') || message.includes('unauthorized') || message.includes('forbidden')) {
      return 'user';
    }
    
    return 'system';
  }

  /**
   * Determine error severity
   */
  private determineSeverity(error: Error, category: ErrorCategory): ErrorSeverity {
    const message = error.message.toLowerCase();
    
    // Critical errors
    if (message.includes('critical') || message.includes('fatal') || category === 'system') {
      return 'critical';
    }
    
    // High severity errors
    if (message.includes('failed') || message.includes('error') || category === 'training') {
      return 'high';
    }
    
    // Medium severity errors
    if (category === 'network' || category === 'file' || category === 'inference') {
      return 'medium';
    }
    
    // Low severity errors
    return 'low';
  }

  /**
   * Generate user-friendly and technical messages
   */
  private generateMessages(error: Error, category: ErrorCategory): {
    userMessage: string;
    technicalMessage: string;
  } {
    const userMessages: Record<ErrorCategory, string> = {
      network: 'Connection issue detected. Please check your internet connection and try again.',
      validation: 'Please check your input and ensure all required fields are filled correctly.',
      file: 'There was an issue with your file. Please check the file format and try uploading again.',
      training: 'Model training encountered an issue. This might be due to data quality or system resources.',
      inference: 'Classification failed. Please check your data and model configuration.',
      system: 'A system error occurred. Our team has been notified and will investigate.',
      user: 'Access denied. Please check your permissions or contact support.'
    };

    return {
      userMessage: userMessages[category],
      technicalMessage: `${category.toUpperCase()}_ERROR: ${error.message}`
    };
  }

  /**
   * Generate error code
   */
  private generateErrorCode(error: Error, category: ErrorCategory): string {
    const timestamp = Date.now().toString(36);
    const categoryCode = category.toUpperCase().substr(0, 3);
    return `CW_${categoryCode}_${timestamp}`;
  }

  /**
   * Generate recovery actions based on error type
   */
  private generateRecoveryActions(error: Error, category: ErrorCategory): ErrorRecoveryAction[] {
    const actions: ErrorRecoveryAction[] = [];

    switch (category) {
      case 'network':
        actions.push({
          id: 'retry',
          label: 'Retry',
          description: 'Try the operation again',
          action: async () => {
            // Retry logic would be implemented by the caller
            return false;
          },
          primary: true
        });
        actions.push({
          id: 'check-connection',
          label: 'Check Connection',
          description: 'Verify your internet connection',
          action: async () => {
            window.open('https://www.google.com', '_blank');
            return false;
          }
        });
        break;

      case 'file':
        actions.push({
          id: 'reupload',
          label: 'Upload Again',
          description: 'Try uploading the file again',
          action: async () => false,
          primary: true
        });
        actions.push({
          id: 'check-format',
          label: 'Check Format',
          description: 'Verify your file format is supported',
          action: async () => false
        });
        break;

      case 'validation':
        actions.push({
          id: 'review-input',
          label: 'Review Input',
          description: 'Check and correct your input',
          action: async () => false,
          primary: true
        });
        break;

      case 'training':
        actions.push({
          id: 'retry-training',
          label: 'Retry Training',
          description: 'Start training again with current settings',
          action: async () => false,
          primary: true
        });
        actions.push({
          id: 'adjust-params',
          label: 'Adjust Parameters',
          description: 'Modify training parameters and try again',
          action: async () => false
        });
        break;

      default:
        actions.push({
          id: 'retry',
          label: 'Try Again',
          description: 'Retry the operation',
          action: async () => false,
          primary: true
        });
    }

    // Always add contact support option for high/critical errors
    if (category === 'system') {
      actions.push({
        id: 'contact-support',
        label: 'Contact Support',
        description: 'Get help from our support team',
        action: async () => {
          window.open('mailto:<EMAIL>', '_blank');
          return false;
        }
      });
    }

    return actions;
  }

  /**
   * Log error to internal log
   */
  private logError(error: ClassyWebError): void {
    this.errorLog.unshift(error);
    
    // Keep log size manageable
    if (this.errorLog.length > this.MAX_LOG_SIZE) {
      this.errorLog = this.errorLog.slice(0, this.MAX_LOG_SIZE);
    }

    // Console logging for development
    if (process.env.NODE_ENV === 'development') {
      console.group(`🚨 ClassyWeb Error [${error.severity.toUpperCase()}]`);
      console.error('Code:', error.code);
      console.error('Category:', error.category);
      console.error('Message:', error.message);
      console.error('User Message:', error.userMessage);
      if (error.context) {
        console.error('Context:', error.context);
      }
      if (error.originalError) {
        console.error('Original Error:', error.originalError);
      }
      console.groupEnd();
    }
  }

  /**
   * Show error toast notification
   */
  private showErrorToast(error: ClassyWebError): void {
    const variant = error.severity === 'critical' || error.severity === 'high' ? 'destructive' : 'default';
    
    toast({
      variant,
      title: `${error.category.charAt(0).toUpperCase() + error.category.slice(1)} Error`,
      description: error.userMessage || error.message,
      duration: error.severity === 'critical' ? 0 : 5000, // Critical errors don't auto-dismiss
    });
  }

  /**
   * Report error to external service
   */
  private async reportError(error: ClassyWebError): Promise<void> {
    try {
      // In a real implementation, this would send to an error reporting service
      // like Sentry, LogRocket, or a custom endpoint
      console.log('Reporting error:', error.code);
    } catch (reportingError) {
      console.warn('Failed to report error:', reportingError);
    }
  }

  /**
   * Check if error is already a ClassyWebError
   */
  private isClassyWebError(error: any): error is ClassyWebError {
    return error && typeof error === 'object' && 'id' in error && 'code' in error && 'category' in error;
  }

  /**
   * Get error log
   */
  getErrorLog(): ClassyWebError[] {
    return [...this.errorLog];
  }

  /**
   * Clear error log
   */
  clearErrorLog(): void {
    this.errorLog = [];
  }

  /**
   * Get errors by category
   */
  getErrorsByCategory(category: ErrorCategory): ClassyWebError[] {
    return this.errorLog.filter(error => error.category === category);
  }

  /**
   * Get errors by severity
   */
  getErrorsBySeverity(severity: ErrorSeverity): ClassyWebError[] {
    return this.errorLog.filter(error => error.severity === severity);
  }

  /**
   * Update configuration
   */
  updateConfig(config: Partial<ErrorHandlerConfig>): void {
    this.config = { ...this.config, ...config };
  }
}

// Export singleton instance
export const unifiedErrorHandler = UnifiedErrorHandler.getInstance();

// Export utility functions
export const handleError = (
  error: Error | ClassyWebError,
  context?: ClassyWebError['context'],
  recoveryActions?: ErrorRecoveryAction[]
) => unifiedErrorHandler.handleError(error, context, recoveryActions);

export const createRecoveryAction = (
  id: string,
  label: string,
  description: string,
  action: () => Promise<boolean>,
  primary = false
): ErrorRecoveryAction => ({
  id,
  label,
  description,
  action,
  primary
});
