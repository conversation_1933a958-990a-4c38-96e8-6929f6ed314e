import { useState, useEffect, useCallback } from 'react';

export interface WorkflowStep {
  id: number;
  title: string;
  description: string;
  icon: any;
  status: 'pending' | 'current' | 'complete';
}

export interface UseWorkflowNavigationProps {
  totalSteps: number;
  initialStep?: number;
  onStepChange?: (step: number) => void;
  isStepComplete?: (step: number) => boolean;
  enableKeyboardNavigation?: boolean;
}

export interface UseWorkflowNavigationReturn {
  currentStep: number;
  setCurrentStep: (step: number) => void;
  handleNextStep: () => void;
  handlePreviousStep: () => void;
  handleStepClick: (stepId: number) => void;
  canNavigateToStep: (stepId: number) => boolean;
  isStepComplete: (stepId: number) => boolean;
  calculateProgress: () => number;
  isFirstStep: boolean;
  isLastStep: boolean;
  canGoNext: boolean;
  canGoPrevious: boolean;
}

/**
 * Hook for managing workflow navigation with step validation and keyboard support
 */
export const useWorkflowNavigation = ({
  totalSteps,
  initialStep = 1,
  onStepChange,
  isStepComplete,
  enableKeyboardNavigation = true
}: UseWorkflowNavigationProps): UseWorkflowNavigationReturn => {
  const [currentStep, setCurrentStepState] = useState(initialStep);

  const setCurrentStep = useCallback((step: number) => {
    if (step >= 1 && step <= totalSteps) {
      setCurrentStepState(step);
      onStepChange?.(step);
    }
  }, [totalSteps, onStepChange]);

  const defaultIsStepComplete = useCallback((stepId: number) => {
    // Default implementation - can be overridden
    return stepId < currentStep;
  }, [currentStep]);

  const isStepCompleteFunc = isStepComplete || defaultIsStepComplete;

  const handleNextStep = useCallback(() => {
    if (currentStep < totalSteps && isStepCompleteFunc(currentStep)) {
      setCurrentStep(currentStep + 1);
    }
  }, [currentStep, totalSteps, isStepCompleteFunc, setCurrentStep]);

  const handlePreviousStep = useCallback(() => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  }, [currentStep, setCurrentStep]);

  const canNavigateToStep = useCallback((stepId: number) => {
    // Can navigate to completed steps, current step, or next step if current is complete
    return stepId <= currentStep || (stepId === currentStep + 1 && isStepCompleteFunc(currentStep));
  }, [currentStep, isStepCompleteFunc]);

  const handleStepClick = useCallback((stepId: number) => {
    if (canNavigateToStep(stepId)) {
      setCurrentStep(stepId);
    }
  }, [canNavigateToStep, setCurrentStep]);

  const calculateProgress = useCallback(() => {
    return Math.round((currentStep / totalSteps) * 100);
  }, [currentStep, totalSteps]);

  // Keyboard navigation
  useEffect(() => {
    if (!enableKeyboardNavigation) return;

    const handleKeyDown = (event: KeyboardEvent) => {
      // Only handle keyboard navigation if not typing in an input
      if (event.target instanceof HTMLInputElement || 
          event.target instanceof HTMLTextAreaElement ||
          event.target instanceof HTMLSelectElement) {
        return;
      }

      if (event.key === 'ArrowLeft' && currentStep > 1) {
        event.preventDefault();
        handlePreviousStep();
      } else if (event.key === 'ArrowRight' && currentStep < totalSteps && isStepCompleteFunc(currentStep)) {
        event.preventDefault();
        handleNextStep();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [currentStep, totalSteps, isStepCompleteFunc, handleNextStep, handlePreviousStep, enableKeyboardNavigation]);

  return {
    currentStep,
    setCurrentStep,
    handleNextStep,
    handlePreviousStep,
    handleStepClick,
    canNavigateToStep,
    isStepComplete: isStepCompleteFunc,
    calculateProgress,
    isFirstStep: currentStep === 1,
    isLastStep: currentStep === totalSteps,
    canGoNext: currentStep < totalSteps && isStepCompleteFunc(currentStep),
    canGoPrevious: currentStep > 1,
  };
};

export default useWorkflowNavigation;
