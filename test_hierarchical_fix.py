#!/usr/bin/env python3
"""
Test script to verify the hierarchical classification fixes.
This script tests the tensor handling improvements and memory management.
"""

import sys
import os
import logging
import pandas as pd
import torch

# Add the backend directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_tensor_handling():
    """Test the improved tensor handling in the dataset."""
    logger.info("Testing tensor handling improvements...")
    
    try:
        # Create sample data
        sample_texts = [
            "This is a test product in electronics category",
            "Another product in clothing and fashion",
            "A book in education and learning",
            "Software in technology and computers",
            "Food item in grocery and consumables"
        ]
        
        sample_labels = [
            ["Electronics", "Gadgets", "Phones"],
            ["Clothing", "Fashion", "Shirts"],
            ["Books", "Education", "Textbooks"],
            ["Software", "Technology", "Applications"],
            ["Food", "Grocery", "Snacks"]
        ]
        
        # Test the dataset creation with the new tensor handling
        from app.classification_engines.hierarchical_engine import HierarchicalEngine
        from app.classification_engines.base_engine import TrainingConfig, ClassificationType, TrainingMethod
        
        # Create engine
        engine = HierarchicalEngine(ClassificationType.HIERARCHICAL)
        
        # Create minimal config
        config = TrainingConfig(
            classification_type=ClassificationType.HIERARCHICAL,
            training_method=TrainingMethod.CUSTOM,
            text_columns=["text"],
            label_columns=["level1", "level2", "level3"],
            validation_split=0.2,
            base_model="distilbert-base-uncased",
            max_length=128,
            learning_rate=2e-5,
            batch_size=2,  # Small batch size for testing
            num_epochs=1,  # Single epoch for testing
            warmup_steps=10,
            weight_decay=0.01,
            use_unsloth=False,  # Disable Unsloth for testing
            fp16=False,  # Disable FP16 for testing
            gradient_accumulation_steps=1,
            gradient_checkpointing=False
        )
        
        # Create DataFrame
        data = pd.DataFrame({
            'text': sample_texts,
            'level1': [labels[0] for labels in sample_labels],
            'level2': [labels[1] for labels in sample_labels],
            'level3': [labels[2] for labels in sample_labels]
        })
        
        logger.info("Created test data successfully")
        logger.info(f"Data shape: {data.shape}")
        logger.info(f"Sample data:\n{data.head()}")
        
        # Test the tensor handling by creating a small dataset
        from transformers import AutoTokenizer
        
        tokenizer = AutoTokenizer.from_pretrained(config.base_model)
        
        # Tokenize a small sample
        test_texts = sample_texts[:2]  # Just 2 samples for testing
        
        encodings = tokenizer(
            test_texts,
            truncation=True,
            padding=True,
            max_length=config.max_length,
            return_tensors="pt"
        )
        
        logger.info("Tokenization successful")
        logger.info(f"Encoding keys: {list(encodings.keys())}")
        logger.info(f"Input IDs shape: {encodings['input_ids'].shape}")
        
        # Test the improved dataset class
        from sklearn.preprocessing import MultiLabelBinarizer
        
        # Create hierarchical labels
        hierarchical_labels = [sample_labels[0], sample_labels[1]]  # Just 2 samples
        
        # Flatten labels for multi-label encoding
        all_labels = set()
        for label_path in hierarchical_labels:
            all_labels.update(label_path)
        all_labels = sorted(list(all_labels))
        
        mlb = MultiLabelBinarizer()
        encoded_labels = mlb.fit_transform(hierarchical_labels)
        
        logger.info(f"Label encoding successful. All labels: {all_labels}")
        logger.info(f"Encoded labels shape: {encoded_labels.shape}")
        
        # Test the improved dataset class
        class TestHierarchicalDataset(torch.utils.data.Dataset):
            def __init__(self, encodings, labels):
                self.encodings = encodings
                self.labels = labels

            def __getitem__(self, idx):
                try:
                    item = {}
                    for key, val in self.encodings.items():
                        if isinstance(val, torch.Tensor):
                            # Use proper indexing for tensors
                            tensor_slice = val[idx]
                            if tensor_slice.dim() == 0:
                                # Handle scalar tensors
                                item[key] = tensor_slice.clone().detach()
                            else:
                                item[key] = tensor_slice.clone().detach()
                        else:
                            item[key] = torch.tensor(val[idx])

                    # Handle labels safely
                    label_data = self.labels[idx]
                    if isinstance(label_data, torch.Tensor):
                        item['labels'] = label_data.clone().detach().float()
                    else:
                        item['labels'] = torch.tensor(label_data, dtype=torch.float)
                    
                    return item
                except Exception as e:
                    logger.error(f"Error in dataset __getitem__ at index {idx}: {e}")
                    # Return a safe fallback
                    return {
                        'input_ids': torch.zeros(config.max_length, dtype=torch.long),
                        'attention_mask': torch.zeros(config.max_length, dtype=torch.long),
                        'labels': torch.zeros(len(encoded_labels[0]), dtype=torch.float)
                    }

            def __len__(self):
                return len(self.labels)
        
        # Test dataset creation
        dataset = TestHierarchicalDataset(encodings, encoded_labels)
        logger.info(f"Dataset created successfully with {len(dataset)} samples")
        
        # Test dataset access
        for i in range(len(dataset)):
            item = dataset[i]
            logger.info(f"Sample {i}: input_ids shape: {item['input_ids'].shape}, labels shape: {item['labels'].shape}")
            
            # Verify tensor types
            assert isinstance(item['input_ids'], torch.Tensor), "input_ids should be tensor"
            assert isinstance(item['attention_mask'], torch.Tensor), "attention_mask should be tensor"
            assert isinstance(item['labels'], torch.Tensor), "labels should be tensor"
            
        logger.info("✅ All tensor handling tests passed!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Tensor handling test failed: {e}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        return False

def test_memory_management():
    """Test memory management improvements."""
    logger.info("Testing memory management...")
    
    try:
        # Test CUDA memory management if available
        if torch.cuda.is_available():
            logger.info("CUDA available, testing GPU memory management")
            
            # Create some tensors
            test_tensor = torch.randn(1000, 1000).cuda()
            logger.info(f"Created test tensor on GPU: {test_tensor.shape}")
            
            # Test memory clearing
            torch.cuda.empty_cache()
            logger.info("GPU memory cache cleared successfully")
            
            # Clean up
            del test_tensor
            torch.cuda.empty_cache()
            
        else:
            logger.info("CUDA not available, testing CPU memory management")
            
            # Create some tensors on CPU
            test_tensor = torch.randn(1000, 1000)
            logger.info(f"Created test tensor on CPU: {test_tensor.shape}")
            
            # Clean up
            del test_tensor
            
        logger.info("✅ Memory management tests passed!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Memory management test failed: {e}")
        return False

def main():
    """Run all tests."""
    logger.info("Starting hierarchical classification fix tests...")
    
    # Import traceback for error handling
    import traceback
    
    tests_passed = 0
    total_tests = 2
    
    # Test 1: Tensor handling
    if test_tensor_handling():
        tests_passed += 1
    
    # Test 2: Memory management
    if test_memory_management():
        tests_passed += 1
    
    # Summary
    logger.info(f"\n{'='*50}")
    logger.info(f"Test Results: {tests_passed}/{total_tests} tests passed")
    
    if tests_passed == total_tests:
        logger.info("🎉 All tests passed! The hierarchical classification fixes are working.")
        return True
    else:
        logger.error(f"❌ {total_tests - tests_passed} tests failed. Please check the errors above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
