import { useState, useEffect } from "react";
import { Navigation } from "@/components/Navigation";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { UnifiedFileUploadZone } from "@/components/UnifiedFileUploadZone";
import { DataPurposeSelector } from "@/components/DataPurposeSelector";
import { FileReuseManager } from "@/components/FileReuseManager";
import { SmartDetection } from "@/components/SmartDetection";
import { EnhancedRecommendationEngine } from "@/components/EnhancedRecommendationEngine";
import { ColumnSelector } from "@/components/ColumnSelector";

import { CustomizationPanel, CustomizationSettings } from "@/components/CustomizationPanel";
import { StepNavigation } from "@/components/StepNavigation";
import { ResultsDataTable } from "@/components/ResultsDataTable";
import { SmartDataAnalysis } from "@/services/dataAnalysisApi";
import { UploadedFile } from "@/services/fileUploadApi";
import {
  unifiedDataManager,
  DataPurpose,
  DataPurposeSuggestion
} from "@/services/unifiedDataManager";
import { downloadResultsCSV, downloadResultsExcel } from "@/services/exportApi";
import { getResultData } from "@/services/taskApi";
import {

  startLLMClassificationOnFile,
  startCustomTrainingOnFile,
  getUniversalTaskStatus,

} from "@/services/universalApi";
import apiClient from "@/services/apiClient";
import { useNavigate } from "react-router-dom";
import { useToast } from "@/hooks/use-toast";
import {
  Upload,
  Brain,
  Lightbulb,
  Zap,
  BarChart3,
  Download,
  ArrowRight,
  CheckCircle2,
  GraduationCap,
  Settings,
  Rocket,
  ChevronLeft,
  ChevronRight,
  FileSpreadsheet
} from "lucide-react";

const BeginnerWorkflow = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [currentStep, setCurrentStep] = useState(1);

  // Unified data management state
  const [selectedFileId, setSelectedFileId] = useState<string | null>(null);
  const [selectedFileInfo, setSelectedFileInfo] = useState<UploadedFile | null>(null);
  const [selectedPurposes, setSelectedPurposes] = useState<DataPurpose[]>(['analysis']);
  const [dataSuggestions, setDataSuggestions] = useState<DataPurposeSuggestion | null>(null);
  const [showFileReuse, setShowFileReuse] = useState(false);

  // Classification data state (unified system)
  const [classificationFileId, setClassificationFileId] = useState<string | null>(null);
  const [classificationFileInfo, setClassificationFileInfo] = useState<UploadedFile | null>(null);
  const [classificationPurposes, setClassificationPurposes] = useState<DataPurpose[]>(['classification']);
  const [analysis, setAnalysis] = useState<SmartDataAnalysis | null>(null);
  const [selectedRecommendation, setSelectedRecommendation] = useState<any>(null);

  // Column selection state to persist across steps
  const [workflowTextColumns, setWorkflowTextColumns] = useState<string[]>([]);
  const [workflowLabelColumns, setWorkflowLabelColumns] = useState<string[]>([]);
  const [showColumnSelection, setShowColumnSelection] = useState(false);

  // Configuration status
  const [isConfigurationComplete, setIsConfigurationComplete] = useState(false);

  const [showCustomization, setShowCustomization] = useState(false);
  const [customSettings, setCustomSettings] = useState<Partial<CustomizationSettings>>({
    textColumns: [],
    labelColumns: [],
    trainingMethod: 'custom',
    modelType: 'distilbert-base-uncased',
    hyperparameters: {},
    preprocessing: {
      removeStopwords: false,
      lowercase: true,
      removePunctuation: false,
      maxSequenceLength: 512
    },
    advanced: {
      useGpu: true,
      enableUnsloth: true,
      customPrompt: ''
    }
  });

  // Training and classification state
  const [isTraining, setIsTraining] = useState(false);
  const [trainingProgress, setTrainingProgress] = useState(0);
  const [trainingTaskId, setTrainingTaskId] = useState<string | null>(null);
  const [trainingError, setTrainingError] = useState<string | null>(null);
  const [trainingResults, setTrainingResults] = useState<any>(null);
  const [isClassifying, setIsClassifying] = useState(false);
  const [classificationTaskId, setClassificationTaskId] = useState<string | null>(null);
  const [classificationResults, setClassificationResults] = useState<any>(null);

  // Results data table state
  const [resultsData, setResultsData] = useState<any[]>([]);
  const [isLoadingResultsData, setIsLoadingResultsData] = useState(false);

  const steps = [
    {
      id: 1,
      icon: Upload,
      title: "Upload Sample Data",
      description: "Upload a sample of your labeled data for analysis",
      status: currentStep > 1 ? "complete" : currentStep === 1 ? "current" : "pending"
    },
    {
      id: 2,
      icon: Brain,
      title: "Smart Detection",
      description: "AI analyzes your data and detects patterns",
      status: currentStep > 2 ? "complete" : currentStep === 2 ? "current" : "pending"
    },
    {
      id: 3,
      icon: Lightbulb,
      title: "Configure & Recommend",
      description: "Configure classification type and method with AI recommendations",
      status: currentStep > 3 ? "complete" : currentStep === 3 ? "current" : "pending"
    },
    {
      id: 4,
      icon: Upload,
      title: "Upload Classification Data",
      description: "Upload new data you want to classify (optional - skip if training only)",
      status: currentStep > 4 ? "complete" : currentStep === 4 ? "current" : "pending"
    },
    {
      id: 5,
      icon: Rocket,
      title: "Training/Inference",
      description: "Execute your chosen method with progress tracking",
      status: currentStep > 5 ? "complete" : currentStep === 5 ? "current" : "pending"
    },
    {
      id: 6,
      icon: BarChart3,
      title: "View Results",
      description: "Analyze model performance and accuracy metrics",
      status: currentStep > 6 ? "complete" : currentStep === 6 ? "current" : "pending"
    },
    {
      id: 7,
      icon: Download,
      title: "Export & Deploy",
      description: "Download your trained model or deploy to production",
      status: currentStep > 7 ? "complete" : currentStep === 7 ? "current" : "pending"
    }
  ];

  // Training file upload is now handled in EnhancedRecommendationEngine

  // Legacy handlers removed - using unified system only

  // Unified file remove handler
  const handleFileRemove = () => {
    setSelectedFileInfo(null);
    setAnalysis(null);
    setSelectedRecommendation(null);
    setCurrentStep(1);
    setShowColumnSelection(false);
    setWorkflowTextColumns([]);
    setWorkflowLabelColumns([]);

    toast({
      title: "Sample data removed",
      description: "You can upload a new sample file to start over",
    });
  };

  const handleClassificationFileRemove = () => {
    setClassificationFile(null);
    setClassificationFileInfo(null);
    setClassificationUploadError(null);
    setClassificationUploadProgress(0);

    toast({
      title: "Classification data removed",
      description: "You can upload a new classification file",
    });
  };

  // Unified file upload handlers
  const handleUnifiedFileSelected = (fileId: string, fileInfo: UploadedFile, purposes: DataPurpose[]) => {
    setSelectedFileId(fileId);
    setSelectedFileInfo(fileInfo);
    setSelectedPurposes(purposes);

    // Generate suggestions for the selected file
    const suggestions = unifiedDataManager.suggestDataPurposes(fileInfo);
    setDataSuggestions(suggestions);

    setShowColumnSelection(true);

    // Auto-advance to next step if analysis purpose is selected
    if (purposes.includes('analysis') && currentStep === 1) {
      setTimeout(() => setCurrentStep(2), 1000);
    }

    toast({
      title: "File selected successfully",
      description: `${fileInfo.filename} is ready for ${purposes.join(', ')}`
    });
  };

  const handleUnifiedFileRemoved = () => {
    setSelectedFileId(null);
    setSelectedFileInfo(null);
    setSelectedPurposes(['analysis']);
    setDataSuggestions(null);

    // Clear legacy state
    handleFileRemove();
  };

  const handlePurposesChange = (purposes: DataPurpose[]) => {
    setSelectedPurposes(purposes);

    // Update purposes in unified data manager if file is selected
    if (selectedFileId) {
      unifiedDataManager.updatePurposes(selectedFileId, purposes);
    }
  };

  // Classification file unified handlers
  const handleClassificationFileSelected = (fileId: string, fileInfo: UploadedFile, purposes: DataPurpose[]) => {
    setClassificationFileId(fileId);
    setClassificationFileInfo(fileInfo);
    setClassificationPurposes(purposes);

    toast({
      title: "Classification file selected",
      description: `${fileInfo.filename} is ready for classification`
    });
  };

  const handleClassificationFileRemovedUnified = () => {
    setClassificationFileId(null);
    setClassificationFileInfo(null);
    setClassificationPurposes(['classification']);

    // Clear legacy state
    handleClassificationFileRemove();
  };

  const handleClassificationPurposesChange = (purposes: DataPurpose[]) => {
    setClassificationPurposes(purposes);

    // Update purposes in unified data manager if file is selected
    if (classificationFileId) {
      unifiedDataManager.updatePurposes(classificationFileId, purposes);
    }
  };

  const handleAnalysisComplete = (analysisResult: SmartDataAnalysis) => {
    setAnalysis(analysisResult);

    // Initialize workflow-level column selections from analysis (only if not already set)
    if (workflowTextColumns.length === 0) {
      if (analysisResult.preview.text_column) {
        setWorkflowTextColumns([analysisResult.preview.text_column]);
      } else {
        // Fallback to first detected text column
        const textColumns = Object.keys(analysisResult.column_analysis).filter(
          col => analysisResult.column_analysis[col].is_potential_text
        );
        if (textColumns.length > 0) {
          setWorkflowTextColumns([textColumns[0]]);
        }
      }
    }

    if (workflowLabelColumns.length === 0) {
      if (analysisResult.preview.label_columns && analysisResult.preview.label_columns.length > 0) {
        setWorkflowLabelColumns(analysisResult.preview.label_columns);
      } else {
        // Fallback to detected label columns
        const labelColumns = Object.keys(analysisResult.column_analysis).filter(
          col => analysisResult.column_analysis[col].is_potential_label
        );
        setWorkflowLabelColumns(labelColumns);
      }
    }

    setCurrentStep(3);
  };

  const handleAnalysisError = (error: string) => {
    setCurrentStep(1);

    toast({
      title: "Analysis Failed",
      description: error || "Failed to analyze your data. Please check your file format and try again.",
      variant: "destructive"
    });
  };

  const handleRecommendationSelect = (recommendation: any) => {
    setSelectedRecommendation(recommendation);

    // Update workflow-level column selections from the recommendation
    if (recommendation.suggested_config?.text_column) {
      setWorkflowTextColumns([recommendation.suggested_config.text_column]);
    }
    if (recommendation.suggested_config?.label_columns) {
      setWorkflowLabelColumns(recommendation.suggested_config.label_columns);
    }

    // Show success message but don't auto-advance - let user use navigation buttons
    if (recommendation.suggested_config?.approach === 'llm_inference') {
      toast({
        title: "LLM Configuration Complete",
        description: `Ready to classify with ${recommendation.suggested_config.llm_config?.provider} ${recommendation.suggested_config.llm_config?.model}`,
      });
    } else {
      toast({
        title: "Configuration Complete",
        description: `${recommendation.suggested_config?.classification_type} classification configured for custom training`,
      });
    }
  };

  const handleConfigurationStatusChange = (isComplete: boolean) => {
    setIsConfigurationComplete(isComplete);
  };

  const handleNextStep = () => {
    if (currentStep < steps.length) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handleStartTraining = async () => {
    if (!selectedRecommendation) {
      toast({
        title: "Configuration Error",
        description: "Please select a recommendation before starting training",
        variant: "destructive"
      });
      return;
    }

    const config = selectedRecommendation.suggested_config;
    if (!config) {
      toast({
        title: "Configuration Error",
        description: "Invalid recommendation configuration",
        variant: "destructive"
      });
      return;
    }

    const isLLMInference = config.approach === 'llm_inference';

    // Use workflow-level column selections as fallback
    const textColumn = config.text_column || workflowTextColumns[0];
    const labelColumns = config.label_columns?.length > 0 ? config.label_columns : workflowLabelColumns;

    // Validate required configuration fields
    if (!textColumn || !labelColumns || labelColumns.length === 0) {
      toast({
        title: "Configuration Error",
        description: "Missing text column or label columns configuration. Please go back to the configuration step and select columns.",
        variant: "destructive"
      });
      return;
    }

    // For custom training, we need the training file from the recommendation
    if (!isLLMInference && !config.training_file_id) {
      toast({
        title: "Training Data Error",
        description: "Missing training data for custom training. Please upload training data in the configuration step.",
        variant: "destructive"
      });
      return;
    }

    // For LLM inference, validate LLM configuration
    if (isLLMInference) {
      if (!config.llm_config || !config.llm_config.provider || !config.llm_config.model) {
        toast({
          title: "LLM Configuration Error",
          description: "Missing LLM provider or model configuration",
          variant: "destructive"
        });
        return;
      }
    }

    try {
      if (isLLMInference) {
        // Start LLM classification
        setIsClassifying(true);
        setClassificationTaskId(null);
        setTrainingError(null);

        // Use classification file if available, otherwise use the sample file
        const fileToClassify = classificationFileInfo || selectedFileInfo;
        if (!fileToClassify) {
          toast({
            title: "Error",
            description: "No data file available for classification",
            variant: "destructive"
          });
          return;
        }

        const config = {
          classification_type: selectedRecommendation.suggested_config.classification_type,
          text_column: textColumn,
          label_columns: labelColumns,
          hierarchy_levels: selectedRecommendation.suggested_config.hierarchy_levels,
          custom_prompt: selectedRecommendation.suggested_config.llm_config?.customPrompt,
          llm_provider: selectedRecommendation.suggested_config.llm_config?.provider,
          llm_model: selectedRecommendation.suggested_config.llm_config?.model,
          original_filename: fileToClassify.filename
        };

        const result = await startLLMClassificationOnFile(fileToClassify.file_id, config);
        setClassificationTaskId(result.task_id);

        toast({
          title: "Classification Started",
          description: "LLM classification is now running. You can monitor progress in the next step.",
        });

        // Move to results step
        setCurrentStep(6);
      } else {
        // Start custom model training
        setIsTraining(true);
        setTrainingTaskId(null);
        setTrainingError(null);
        setTrainingProgress(0);

        const config = {
          classification_type: selectedRecommendation.suggested_config.classification_type,
          text_column: textColumn,
          label_columns: labelColumns,
          hierarchy_levels: selectedRecommendation.suggested_config.hierarchy_levels,
          training_params: {
            ...selectedRecommendation.suggested_config.training_params,
            ...customSettings.hyperparameters,
            use_unsloth: customSettings.advanced?.enableUnsloth,
            use_gpu: customSettings.advanced?.useGpu
          }
        };

        const result = await startCustomTrainingOnFile(selectedRecommendation.suggested_config.training_file_id, config);
        setTrainingTaskId(result.task_id);

        toast({
          title: "Training Started",
          description: "Model training is now running. You can monitor progress in the next step.",
        });

        // Start polling for training progress
        pollTrainingProgress(result.task_id);

        // Move to results step
        setCurrentStep(6);
      }
    } catch (error: any) {
      console.error('Error starting training/classification:', error);
      setTrainingError(error.message);
      setIsTraining(false);
      setIsClassifying(false);

      toast({
        title: "Error",
        description: error.message || "Failed to start training/classification",
        variant: "destructive"
      });
    }
  };

  const checkForCompletedTasks = async () => {
    try {
      // Check if there are any completed tasks we should load
      // Use the authenticated API client instead of raw fetch
      const response = await apiClient.get('/tasks');
      const tasks = response.data;

      // Find the most recent successful LLM classification task
      const completedTask = tasks.find((task: any) =>
        task.status === 'SUCCESS' &&
        task.result_data_url &&
        !trainingResults &&
        !classificationResults
      );

      if (completedTask) {
        console.log('🎉 Found completed task, loading results:', completedTask.task_id);

        // Load the results
        const resultsResponse = await apiClient.get(completedTask.result_data_url);
        const resultsData = resultsResponse.data;

        setTrainingResults(resultsData);
        setClassificationResults(resultsData);
        setIsTraining(false);
        setIsClassifying(false);

        toast({
          title: "Results Loaded",
          description: "Found and loaded completed classification results!",
        });
      }
    } catch (error) {
      console.error('Error checking for completed tasks:', error);
    }
  };

  const pollTrainingProgress = async (taskId: string) => {
    console.log(`🔄 Starting polling for task: ${taskId}`);

    const pollInterval = setInterval(async () => {
      try {
        const status = await getUniversalTaskStatus(taskId);
        console.log(`📊 Task ${taskId} status:`, status);

        if (status.progress !== undefined) {
          setTrainingProgress(status.progress);
        }

        if (status.status === 'SUCCESS' || status.status === 'completed') {
          console.log(`✅ Task ${taskId} completed successfully!`);
          console.log(`📁 Result data URL: ${status.result_data_url}`);
          setIsTraining(false);
          setIsClassifying(false);

          // Fetch results if available
          if (status.result_data_url) {
            try {
              const resultsResponse = await apiClient.get(status.result_data_url);
              const resultsData = resultsResponse.data;
              setTrainingResults(resultsData);
              setClassificationResults(resultsData);
            } catch (error) {
              console.error('Error fetching results:', error);
              setTrainingResults({ message: 'Results available but could not be loaded' });
            }
          } else {
            setTrainingResults({ message: 'Training completed successfully' });
          }

          clearInterval(pollInterval);

          toast({
            title: selectedRecommendation?.suggested_config?.approach === 'llm_inference' ? "Classification Complete" : "Training Complete",
            description: selectedRecommendation?.suggested_config?.approach === 'llm_inference'
              ? "Your data has been successfully classified!"
              : "Your model has been successfully trained!",
          });
        } else if (status.status === 'FAILED' || status.status === 'failed') {
          console.log(`❌ Task ${taskId} failed:`, status.error || status.message);
          setIsTraining(false);
          setIsClassifying(false);
          setTrainingError(status.error || status.message || 'Process failed');
          clearInterval(pollInterval);

          toast({
            title: selectedRecommendation?.suggested_config?.approach === 'llm_inference' ? "Classification Failed" : "Training Failed",
            description: status.error || status.message || "Process encountered an error",
            variant: "destructive"
          });
        } else {
          console.log(`⏳ Task ${taskId} still in progress. Status: ${status.status}, Message: ${status.message}`);
        }
      } catch (error) {
        console.error(`❌ Error polling task ${taskId}:`, error);

        // Check if it's a network error or API error
        if (error instanceof Error) {
          // If it's a persistent error (e.g., network issues), stop polling after several attempts
          const currentTime = Date.now();
          const pollingStartTime = currentTime - 30000; // Assume polling started 30 seconds ago

          if (currentTime - pollingStartTime > 5 * 60 * 1000) { // 5 minutes of errors
            clearInterval(pollInterval);
            setIsTraining(false);
            setIsClassifying(false);
            setTrainingError('Connection lost. Please check your internet connection and try again.');

            toast({
              title: "Connection Error",
              description: "Lost connection while monitoring progress. Please refresh and check your results.",
              variant: "destructive"
            });
          }
        }
      }
    }, 2000); // Poll every 2 seconds

    // Clean up interval after 30 minutes to prevent infinite polling
    setTimeout(() => {
      clearInterval(pollInterval);
    }, 30 * 60 * 1000);
  };

  const handlePreviousStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleStepClick = (stepId: number) => {
    // Allow navigation to completed steps or the next step
    if (stepId <= currentStep || stepId === currentStep + 1) {
      setCurrentStep(stepId);
    }
  };

  const canNavigateToStep = (stepId: number) => {
    // Can navigate to completed steps, current step, or next step if current is complete
    return stepId <= currentStep || (stepId === currentStep + 1 && isStepComplete(currentStep));
  };

  const isStepComplete = (stepId: number) => {
    switch (stepId) {
      case 1:
        return selectedFileInfo !== null;
      case 2:
        return analysis !== null;
      case 3:
        return selectedRecommendation !== null;
      case 4:
        // Classification data is optional, so this step is always complete
        return true;
      case 5:
        // Training/inference step is complete when we have results or are in progress
        return trainingResults !== null || classificationResults !== null || isTraining || isClassifying;
      default:
        return false;
    }
  };



  const handleContinueToAnalysis = () => {
    if (selectedFileInfo) {
      setCurrentStep(2);
      setShowColumnSelection(false);
      toast({
        title: "Starting analysis",
        description: "AI is now analyzing your data structure and patterns",
      });
    }
  };

  const handleColumnSelectionContinue = () => {
    if (workflowTextColumns.length > 0 && workflowLabelColumns.length > 0) {
      handleContinueToAnalysis();
    }
  };

  const handleSkipColumnSelection = () => {
    // Skip column selection and proceed to analysis
    // The analysis step will detect columns automatically
    handleContinueToAnalysis();
  };

  const handleCustomizeRecommendation = () => {
    setShowCustomization(true);
    toast({
      title: "Customization mode",
      description: "You can now adjust all parameters to fit your specific needs",
    });
  };

  const handleSaveCustomization = (newSettings: CustomizationSettings) => {
    setCustomSettings(newSettings);
    setShowCustomization(false);
    toast({
      title: "Settings saved",
      description: "Your custom configuration has been applied",
    });
  };

  const handleCancelCustomization = () => {
    setShowCustomization(false);
  };

  // Load results data for the data table
  const loadResultsData = async (taskId: string) => {
    if (!taskId) return;

    setIsLoadingResultsData(true);
    try {
      const data = await getResultData(taskId);
      setResultsData(data || []);
    } catch (error: any) {
      console.error('Error loading results data:', error);
      toast({
        title: "Error loading results data",
        description: error.message || 'Failed to load classification results',
        variant: "destructive",
      });
    } finally {
      setIsLoadingResultsData(false);
    }
  };

  // Export handlers
  const handleExportCSV = async () => {
    const taskId = trainingTaskId || classificationTaskId;
    if (!taskId) {
      toast({
        title: "No task ID available",
        description: "Cannot export results without a valid task ID",
        variant: "destructive",
      });
      return;
    }

    try {
      await downloadResultsCSV(taskId);
      toast({
        title: "Export successful",
        description: "CSV file has been downloaded",
      });
    } catch (error: any) {
      toast({
        title: "Export failed",
        description: error.message || 'Failed to export CSV file',
        variant: "destructive",
      });
    }
  };

  const handleExportExcel = async () => {
    const taskId = trainingTaskId || classificationTaskId;
    if (!taskId) {
      toast({
        title: "No task ID available",
        description: "Cannot export results without a valid task ID",
        variant: "destructive",
      });
      return;
    }

    try {
      await downloadResultsExcel(taskId);
      toast({
        title: "Export successful",
        description: "Excel file has been downloaded",
      });
    } catch (error: any) {
      toast({
        title: "Export failed",
        description: error.message || 'Failed to export Excel file',
        variant: "destructive",
      });
    }
  };

  const calculateProgress = () => {
    return Math.round((currentStep / steps.length) * 100);
  };

  // Keyboard navigation
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Only handle keyboard navigation if not typing in an input
      if (event.target instanceof HTMLInputElement || event.target instanceof HTMLTextAreaElement) {
        return;
      }

      if (event.key === 'ArrowLeft' && currentStep > 1) {
        event.preventDefault();
        handlePreviousStep();
      } else if (event.key === 'ArrowRight' && currentStep < steps.length && isStepComplete(currentStep)) {
        event.preventDefault();
        handleNextStep();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [currentStep, steps.length]);

  // Check for completed tasks when on results step
  useEffect(() => {
    if (currentStep === 6 && !trainingResults && !classificationResults && !isTraining && !isClassifying) {
      console.log('🔍 On results step without results, checking for completed tasks...');
      checkForCompletedTasks();
    }
  }, [currentStep, trainingResults, classificationResults, isTraining, isClassifying]);

  // Load results data when we have completed results
  useEffect(() => {
    const taskId = trainingTaskId || classificationTaskId;
    if (taskId && (trainingResults || classificationResults) && !isTraining && !isClassifying) {
      console.log('🔍 Loading results data for task:', taskId);
      loadResultsData(taskId);
    }
  }, [trainingResults, classificationResults, trainingTaskId, classificationTaskId, isTraining, isClassifying]);

  return (
    <div className="min-h-screen bg-background">
      <Navigation />
      
      <main className="pt-20">
        {/* Header */}
        <section className="py-8 bg-card border-b-2 border-border">
          <div className="max-w-4xl mx-auto px-6">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-12 h-12 rounded-full bg-ml-secondary/10 flex items-center justify-center">
                <GraduationCap className="w-6 h-6 text-ml-secondary" />
              </div>
              <div>
                <h1 className="text-3xl font-bold">Beginner Workflow</h1>
                <p className="text-muted-foreground">Guided step-by-step machine learning journey</p>
              </div>
            </div>
            
            <div className="mt-6">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium">Progress</span>
                <div className="flex items-center gap-2">
                  <span className="text-sm text-muted-foreground">Step {currentStep} of {steps.length}</span>
                  <div className="flex gap-1">
                    {currentStep > 1 && (
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={handlePreviousStep}
                        className="h-6 px-2 text-xs"
                      >
                        <ChevronLeft className="w-3 h-3" />
                      </Button>
                    )}
                    {currentStep < steps.length && (
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={handleNextStep}
                        disabled={!isStepComplete(currentStep)}
                        className="h-6 px-2 text-xs"
                      >
                        <ChevronRight className="w-3 h-3" />
                      </Button>
                    )}
                  </div>
                </div>
              </div>
              <Progress value={calculateProgress()} className="h-2" />
              <div className="flex justify-between mt-1">
                <span className="text-xs text-muted-foreground">
                  {currentStep > 1 ? 'Click steps to navigate • Use ← → keys' : 'Upload data to continue'}
                </span>
                <span className="text-xs text-muted-foreground">
                  {calculateProgress()}% complete
                </span>
              </div>
            </div>
          </div>
        </section>

        <div className="max-w-6xl mx-auto px-6 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Steps Sidebar */}
            <div className="lg:col-span-1">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Workflow Steps</CardTitle>
                  <CardDescription>
                    Follow these steps to complete your project. Click on completed steps to navigate back.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {steps.map((step) => {
                      const Icon = step.icon;
                      const isClickable = canNavigateToStep(step.id);
                      const isCurrentStep = step.status === 'current';
                      const isCompleted = step.status === 'complete';

                      return (
                        <div
                          key={step.id}
                          onClick={() => isClickable ? handleStepClick(step.id) : undefined}
                          className={`flex items-start gap-3 p-3 rounded-lg transition-all ${
                            isCurrentStep ? 'bg-ml-secondary/10 border border-ml-secondary/20' :
                            isCompleted ? 'bg-ml-success/10' : 'bg-muted/30'
                          } ${
                            isClickable ? 'cursor-pointer hover:bg-opacity-80' : 'cursor-default'
                          }`}
                        >
                          <div className={`w-8 h-8 rounded-lg flex items-center justify-center flex-shrink-0 ${
                            isCurrentStep ? 'bg-ml-secondary text-white' :
                            isCompleted ? 'bg-ml-success text-white' : 'bg-muted'
                          }`}>
                            {isCompleted ? (
                              <CheckCircle2 className="w-4 h-4" />
                            ) : (
                              <Icon className="w-4 h-4" />
                            )}
                          </div>
                          <div className="flex-1">
                            <h4 className={`font-medium text-sm ${
                              isCurrentStep ? 'text-ml-secondary' : ''
                            }`}>
                              {step.title}
                            </h4>
                            <p className="text-xs text-muted-foreground mt-1">
                              {step.description}
                            </p>
                            {isClickable && !isCurrentStep && (
                              <p className="text-xs text-ml-secondary mt-1">
                                Click to navigate
                              </p>
                            )}
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Main Content */}
            <div className="lg:col-span-2">
              {/* Step 1: Upload Data */}
              {currentStep === 1 && (
                <div className="space-y-6">
                  <Card>
                    <CardHeader>
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 rounded-lg bg-primary/10 flex items-center justify-center">
                          <Upload className="w-5 h-5 text-primary" />
                        </div>
                        <div>
                          <CardTitle>Upload Classification Data</CardTitle>
                          <CardDescription>
                            Upload new data you want to classify
                          </CardDescription>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <UnifiedFileUploadZone
                        onFileSelected={handleUnifiedFileSelected}
                        onFileRemoved={handleUnifiedFileRemoved}
                        onContinue={showColumnSelection ? undefined : handleContinueToAnalysis}
                        requiredPurposes={['analysis']}
                        suggestedPurposes={dataSuggestions?.suggestedPurposes || []}
                        allowMultiplePurposes={true}
                        showFileReuse={true}
                        title="Upload Sample Data"
                        description="Upload a sample of your labeled data for analysis and smart detection"
                        className="mb-6"
                      />

                      {/* Data Purpose Selector - shown after file selection */}
                      {selectedFileInfo && dataSuggestions && (
                        <DataPurposeSelector
                          fileInfo={selectedFileInfo}
                          suggestions={dataSuggestions}
                          selectedPurposes={selectedPurposes}
                          onPurposesChange={handlePurposesChange}
                          requiredPurposes={['analysis']}
                          showRecommendations={true}
                          showDataQualityMetrics={true}
                          className="mb-6"
                        />
                      )}

                      <StepNavigation
                        showPrevious={false}
                        nextLabel="Continue to Analysis"
                        nextDisabled={!selectedFileId || (showColumnSelection && (workflowTextColumns.length === 0 || workflowLabelColumns.length === 0))}
                        onNext={showColumnSelection ? handleColumnSelectionContinue : handleContinueToAnalysis}
                        currentStep={currentStep}
                        totalSteps={steps.length}
                      />
                    </CardContent>
                  </Card>

                  {/* Column Selection - shown after file upload */}
                  {showColumnSelection && selectedFileInfo && (
                    <ColumnSelector
                      columns={selectedFileInfo.columns.reduce((acc, col) => {
                        // Create a simple column info structure with basic heuristics
                        const colLower = col.toLowerCase();
                        const isTextLike = colLower.includes('text') || colLower.includes('content') ||
                                          colLower.includes('message') || colLower.includes('description') ||
                                          colLower.includes('comment') || colLower.includes('review');
                        const isLabelLike = colLower.includes('label') || colLower.includes('category') ||
                                           colLower.includes('class') || colLower.includes('type') ||
                                           colLower.includes('sentiment') || colLower.includes('tag');

                        acc[col] = {
                          type: isTextLike ? 'text' : 'categorical',
                          unique_count: 0,
                          total_count: selectedFileInfo.num_rows,
                          uniqueness_ratio: 0,
                          sample_values: [],
                          is_potential_text: isTextLike || !isLabelLike, // Default to text if not clearly a label
                          is_potential_label: isLabelLike || !isTextLike // Default to label if not clearly text
                        };
                        return acc;
                      }, {} as Record<string, any>)}
                      selectedTextColumns={workflowTextColumns}
                      selectedLabelColumns={workflowLabelColumns}
                      onTextColumnsChange={setWorkflowTextColumns}
                      onLabelColumnsChange={setWorkflowLabelColumns}
                      onContinue={handleColumnSelectionContinue}
                      onSkip={handleSkipColumnSelection}
                      showContinueButton={true}
                      showSkipButton={true}
                    />
                  )}
                </div>
              )}

              {/* Step 2: Smart Detection */}
              {currentStep === 2 && selectedFileInfo && (
                <div className="space-y-6">
                  <SmartDetection
                    fileInfo={selectedFileInfo}
                    onAnalysisComplete={handleAnalysisComplete}
                    onError={handleAnalysisError}
                  />

                  <Card>
                    <CardContent className="pt-6">
                      <StepNavigation
                        nextLabel="Continue to Recommendations"
                        nextDisabled={!analysis}
                        onNext={() => analysis && handleNextStep()}
                        currentStep={currentStep}
                        totalSteps={steps.length}
                      />
                    </CardContent>
                  </Card>
                </div>
              )}

              {/* Step 3: Recommendations */}
              {currentStep === 3 && analysis && !showCustomization && (
                <div className="space-y-6">
                  <EnhancedRecommendationEngine
                    analysis={analysis}
                    onRecommendationSelect={handleRecommendationSelect}
                    onCustomize={handleCustomizeRecommendation}
                    initialTextColumns={workflowTextColumns}
                    initialLabelColumns={workflowLabelColumns}
                    onConfigurationStatusChange={handleConfigurationStatusChange}
                  />

                  {/* Navigation for Configure & Recommend step */}
                  <Card>
                    <CardContent className="pt-6">
                      <StepNavigation
                        nextLabel={
                          !selectedRecommendation
                            ? "Select a Recommendation"
                            : !isConfigurationComplete
                              ? "Complete Configuration"
                              : "Continue to Classification Data"
                        }
                        nextDisabled={!selectedRecommendation || !isConfigurationComplete}
                        onNext={() => {
                          if (selectedRecommendation?.suggested_config?.approach === 'llm_inference') {
                            setCurrentStep(4); // Go to classification data
                          } else {
                            setCurrentStep(5); // Go directly to training for custom training
                          }
                        }}
                        onPrevious={() => setCurrentStep(2)}
                        previousLabel="Back to Analysis"
                        currentStep={currentStep}
                        totalSteps={steps.length}
                      />
                    </CardContent>
                  </Card>

                  {/* Advanced Settings option for custom training */}
                  {selectedRecommendation && selectedRecommendation.suggested_config?.approach !== 'llm_inference' && (
                    <Card>
                      <CardContent className="pt-6">
                        <div className="flex justify-center">
                          <Button
                            variant="outline"
                            size="lg"
                            onClick={handleCustomizeRecommendation}
                          >
                            <Settings className="w-4 h-4 mr-2" />
                            Advanced Settings
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  )}
                </div>
              )}

              {/* Customization Panel */}
              {currentStep === 3 && analysis && showCustomization && (
                <div className="space-y-6">
                  <CustomizationPanel
                    analysis={analysis}
                    onSave={handleSaveCustomization}
                    onCancel={handleCancelCustomization}
                  />

                  <Card>
                    <CardContent className="pt-6">
                      <StepNavigation
                        nextLabel="Continue to Setup"
                        onNext={handleNextStep}
                        onPrevious={() => setShowCustomization(false)}
                        previousLabel="Back to Recommendations"
                        currentStep={currentStep}
                        totalSteps={steps.length}
                      />
                    </CardContent>
                  </Card>
                </div>
              )}

              {/* Step 4: Upload Classification Data */}
              {currentStep === 4 && selectedRecommendation && (
                <div className="space-y-6">
                  <Card>
                    <CardHeader>
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 rounded-lg bg-primary/10 flex items-center justify-center">
                          <Upload className="w-5 h-5 text-primary" />
                        </div>
                        <div>
                          <CardTitle>Upload Classification Data</CardTitle>
                          <CardDescription>
                            Upload new data you want to classify. This is optional if you only want to train a model.
                          </CardDescription>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-6">
                        {/* Information about file types */}
                        <div className="p-4 bg-muted/50 rounded-lg">
                          <h4 className="font-semibold mb-2">What to upload here:</h4>
                          <ul className="text-sm text-muted-foreground space-y-1">
                            <li>• New data you want to classify (unlabeled)</li>
                            <li>• Should have the same text column structure as your training data</li>
                            <li>• Labels are not required - the model will predict them</li>
                            <li>• Skip this step if you only want to train a model for later use</li>
                          </ul>
                        </div>

                        <UnifiedFileUploadZone
                          onFileSelected={handleClassificationFileSelected}
                          onFileRemoved={handleClassificationFileRemovedUnified}
                          requiredPurposes={['classification']}
                          suggestedPurposes={['classification']}
                          allowMultiplePurposes={false}
                          showFileReuse={true}
                          title="Upload Classification Data"
                          description="Upload new data you want to classify (optional - skip if training only)"
                          showContinueButton={false}
                        />

                        {/* Show current files summary */}
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div className="p-3 bg-muted/30 rounded-lg">
                            <h5 className="font-medium text-sm mb-1">Sample Data</h5>
                            <p className="text-xs text-muted-foreground">
                              {selectedFileInfo
                                ? `${selectedFileInfo.filename} (${selectedFileInfo.num_rows.toLocaleString()} rows)`
                                : 'No sample uploaded'}
                            </p>
                            {selectedPurposes.length > 0 && (
                              <div className="flex flex-wrap gap-1 mt-1">
                                {selectedPurposes.map(purpose => (
                                  <span key={purpose} className="text-xs bg-primary/10 text-primary px-1 py-0.5 rounded">
                                    {purpose}
                                  </span>
                                ))}
                              </div>
                            )}
                          </div>
                          <div className="p-3 bg-muted/30 rounded-lg">
                            <h5 className="font-medium text-sm mb-1">Classification Data</h5>
                            <p className="text-xs text-muted-foreground">
                              {classificationFileInfo
                                ? `${classificationFileInfo.filename} (${classificationFileInfo.num_rows.toLocaleString()} rows)`
                                : 'Optional - skip if training only'}
                            </p>
                            {classificationFileId && classificationPurposes.length > 0 && (
                              <div className="flex flex-wrap gap-1 mt-1">
                                {classificationPurposes.map(purpose => (
                                  <span key={purpose} className="text-xs bg-purple-100 text-purple-800 px-1 py-0.5 rounded">
                                    {purpose}
                                  </span>
                                ))}
                              </div>
                            )}
                          </div>
                        </div>

                        <StepNavigation
                          nextLabel="Continue to Training"
                          onNext={() => setCurrentStep(5)}
                          onPrevious={() => setCurrentStep(3)}
                          previousLabel="Back to Configuration"
                          currentStep={currentStep}
                          totalSteps={steps.length}
                        />
                      </div>
                    </CardContent>
                  </Card>
                </div>
              )}

              {/* Step 5: Training/Inference */}
              {currentStep === 5 && selectedRecommendation && !showCustomization && (
                <Card>
                  <CardHeader>
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 rounded-lg bg-primary/10 flex items-center justify-center">
                        <Rocket className="w-5 h-5 text-primary" />
                      </div>
                      <div>
                        <CardTitle>Training/Inference</CardTitle>
                        <CardDescription>
                          {selectedRecommendation.suggested_config?.approach === 'llm_inference'
                            ? 'Execute LLM-based classification'
                            : 'Train your custom model'}
                        </CardDescription>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-6">
                      {/* Configuration Summary */}
                      <div className="p-4 bg-muted/50 rounded-lg">
                        <h4 className="font-semibold mb-3">Configuration Summary</h4>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                          <div>
                            <span className="text-muted-foreground">Classification Type:</span>
                            <Badge className="ml-2" variant="secondary">
                              {selectedRecommendation.suggested_config?.classification_type}
                            </Badge>
                          </div>
                          <div>
                            <span className="text-muted-foreground">Method:</span>
                            <Badge className="ml-2" variant="secondary">
                              {selectedRecommendation.suggested_config?.approach === 'llm_inference' ? 'LLM Inference' : 'Custom Training'}
                            </Badge>
                          </div>
                          {selectedRecommendation.suggested_config?.approach === 'llm_inference' && selectedRecommendation.suggested_config?.llm_config && (
                            <>
                              <div>
                                <span className="text-muted-foreground">Provider:</span>
                                <span className="ml-2 font-medium">
                                  {selectedRecommendation.suggested_config.llm_config.provider}
                                </span>
                              </div>
                              <div>
                                <span className="text-muted-foreground">Model:</span>
                                <span className="ml-2 font-medium">
                                  {selectedRecommendation.suggested_config.llm_config.model}
                                </span>
                              </div>
                            </>
                          )}
                          <div>
                            <span className="text-muted-foreground">Text Column:</span>
                            <span className="ml-2 font-medium">
                              {selectedRecommendation.suggested_config?.text_column ||
                               workflowTextColumns[0] ||
                               'Not selected'}
                            </span>
                          </div>
                          <div>
                            <span className="text-muted-foreground">Label Columns:</span>
                            <span className="ml-2 font-medium">
                              {(selectedRecommendation.suggested_config?.label_columns?.length > 0
                                ? selectedRecommendation.suggested_config.label_columns
                                : workflowLabelColumns.length > 0
                                ? workflowLabelColumns
                                : []
                              ).join(', ') || 'Not selected'}
                            </span>
                          </div>
                          <div>
                            <span className="text-muted-foreground">Data Samples:</span>
                            <span className="ml-2 font-medium">{analysis?.preview.total_rows.toLocaleString()}</span>
                          </div>
                          <div>
                            <span className="text-muted-foreground">Data Quality:</span>
                            <Badge className="ml-2 bg-ml-success/10 text-ml-success">
                              {analysis && analysis.data_quality.missing_data_percentage < 5 ? 'Good' : 'Fair'}
                            </Badge>
                          </div>
                        </div>
                      </div>

                      {/* Execution Interface */}
                      <div className="p-4 border-2 border-dashed border-primary/30 rounded-lg text-center">
                        <Rocket className="w-12 h-12 text-primary mx-auto mb-4" />
                        <h4 className="font-semibold mb-2">Ready to Execute</h4>
                        <p className="text-sm text-muted-foreground mb-4">
                          {selectedRecommendation.suggested_config?.approach === 'llm_inference'
                            ? 'Your LLM configuration is ready. Click below to start classification.'
                            : 'Your training configuration is ready. Click below to start training.'}
                        </p>
                        <Button
                          size="lg"
                          className="bg-ml-secondary hover:bg-ml-secondary-dark"
                          onClick={handleStartTraining}
                          disabled={isTraining || isClassifying}
                        >
                          <Zap className="w-4 h-4 mr-2" />
                          {isTraining || isClassifying ? 'Processing...' :
                            selectedRecommendation.suggested_config?.approach === 'llm_inference'
                              ? 'Start Classification'
                              : 'Start Training'}
                        </Button>
                      </div>

                      <div className="flex gap-3 mb-6">
                        <Button variant="outline" onClick={() => setCurrentStep(4)}>
                          Back to Classification Data
                        </Button>
                        {selectedRecommendation.suggested_config?.approach !== 'llm_inference' && (
                          <Button variant="outline" onClick={() => setShowCustomization(true)}>
                            <Settings className="w-4 h-4 mr-2" />
                            Advanced Settings
                          </Button>
                        )}
                      </div>

                      <StepNavigation
                        nextLabel={selectedRecommendation.suggested_config?.approach === 'llm_inference'
                          ? 'Start Classification'
                          : 'Start Training'}
                        onNext={handleStartTraining}
                        nextDisabled={isTraining || isClassifying}
                        onPrevious={() => setCurrentStep(4)}
                        previousLabel="Back to Classification Data"
                        currentStep={currentStep}
                        totalSteps={steps.length}
                      />
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Customization Panel for Step 3 or 5 */}
              {(currentStep === 3 || currentStep === 5) && showCustomization && analysis && (
                <CustomizationPanel
                  analysis={analysis}
                  onSave={handleSaveCustomization}
                  onCancel={handleCancelCustomization}
                />
              )}

              {/* Step 6: View Results */}
              {currentStep === 6 && (
                <Card>
                  <CardHeader>
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 rounded-lg bg-ml-success/10 flex items-center justify-center">
                        <BarChart3 className="w-5 h-5 text-ml-success" />
                      </div>
                      <div>
                        <CardTitle>Results & Performance</CardTitle>
                        <CardDescription>
                          Analyze your model's performance and accuracy metrics
                        </CardDescription>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-6">
                      {/* Show progress if training/classification is in progress */}
                      {(isTraining || isClassifying) && (
                        <div className="text-center py-8">
                          <div className="w-16 h-16 rounded-full bg-primary/10 flex items-center justify-center mx-auto mb-4">
                            <Rocket className="w-8 h-8 text-primary animate-pulse" />
                          </div>
                          <h3 className="text-lg font-semibold mb-2">
                            {isTraining ? 'Training in Progress...' : 'Classification in Progress...'}
                          </h3>
                          <p className="text-muted-foreground mb-4">
                            {isTraining
                              ? 'Your custom model is being trained. This may take several minutes.'
                              : 'Your data is being classified using LLM. This may take a few minutes.'}
                          </p>

                          {/* Progress Bar */}
                          {isTraining && (
                            <div className="max-w-md mx-auto mb-4">
                              <div className="flex justify-between text-sm text-muted-foreground mb-2">
                                <span>Training Progress</span>
                                <span>{trainingProgress.toFixed(1)}%</span>
                              </div>
                              <Progress value={trainingProgress} className="h-2" />
                            </div>
                          )}

                          {/* Task ID for reference */}
                          {(trainingTaskId || classificationTaskId) && (
                            <div className="text-xs text-muted-foreground space-y-1">
                              <p>Task ID: {trainingTaskId || classificationTaskId}</p>
                              <p>Status: {isTraining ? 'Training' : isClassifying ? 'Classifying' : 'Unknown'}</p>
                              <p>Progress: {trainingProgress.toFixed(1)}%</p>
                              <p>Has Results: {trainingResults ? 'Training✓' : ''} {classificationResults ? 'Classification✓' : ''}</p>
                            </div>
                          )}

                          {/* Manual check button for debugging */}
                          <div className="mt-4">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={checkForCompletedTasks}
                              className="text-xs"
                            >
                              Check for Completed Tasks
                            </Button>
                          </div>

                          {/* Error display */}
                          {trainingError && (
                            <div className="mt-4 p-3 bg-destructive/10 border border-destructive/20 rounded-lg">
                              <p className="text-sm text-destructive">{trainingError}</p>
                            </div>
                          )}
                        </div>
                      )}

                      {/* Show results when complete */}
                      {(() => {
                        console.log('🔍 Results display check:', {
                          isTraining,
                          isClassifying,
                          hasTrainingResults: !!trainingResults,
                          hasClassificationResults: !!classificationResults,
                          trainingResults,
                          classificationResults
                        });
                        return null;
                      })()}
                      {!isTraining && !isClassifying && (trainingResults || classificationResults) && (
                        <div className="text-center py-8">
                          <div className="w-16 h-16 rounded-full bg-ml-success/10 flex items-center justify-center mx-auto mb-4">
                            <CheckCircle2 className="w-8 h-8 text-ml-success" />
                          </div>
                          <h3 className="text-lg font-semibold mb-2">
                            {selectedRecommendation.suggested_config?.approach === 'llm_inference'
                              ? 'Classification Complete!'
                              : 'Training Complete!'}
                          </h3>
                          <p className="text-muted-foreground mb-4">
                            Your {selectedRecommendation.suggested_config?.classification_type} {selectedRecommendation.suggested_config?.approach === 'llm_inference' ? 'classification' : 'model'} has been successfully executed.
                          </p>

                          {/* Performance Metrics */}
                          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                            <div className="p-4 bg-muted/30 rounded-lg">
                              <div className="text-2xl font-bold text-ml-success">
                                {trainingResults?.final_metrics?.accuracy ?
                                  `${(trainingResults.final_metrics.accuracy * 100).toFixed(1)}%` :
                                  classificationResults?.total_processed ?
                                    `${classificationResults.total_processed}` : 'N/A'}
                              </div>
                              <div className="text-xs text-muted-foreground">
                                {selectedRecommendation.suggested_config?.approach === 'llm_inference' ? 'Processed' : 'Accuracy'}
                              </div>
                            </div>
                            <div className="p-4 bg-muted/30 rounded-lg">
                              <div className="text-2xl font-bold text-ml-primary">
                                {(classificationFileInfo || selectedFileInfo)?.num_rows?.toLocaleString() || 'N/A'}
                              </div>
                              <div className="text-xs text-muted-foreground">
                                {classificationFileInfo ? 'Classification Rows' : 'Sample Rows'}
                              </div>
                            </div>
                            <div className="p-4 bg-muted/30 rounded-lg">
                              <div className="text-2xl font-bold text-ml-accent">
                                {trainingResults?.training_time ?
                                  `${trainingResults.training_time.toFixed(1)}s` :
                                  classificationResults?.processing_time ?
                                    `${classificationResults.processing_time}s` : 'N/A'}
                              </div>
                              <div className="text-xs text-muted-foreground">Processing Time</div>
                            </div>
                          </div>

                          {/* Results Data Table */}
                          {(trainingTaskId || classificationTaskId) && (
                            <div className="mt-6">
                              <ResultsDataTable
                                data={resultsData}
                                taskId={trainingTaskId || classificationTaskId || ''}
                                filename={(classificationFileInfo || selectedFileInfo)?.filename}
                                onExportCSV={handleExportCSV}
                                onExportExcel={handleExportExcel}
                              />
                            </div>
                          )}

                        <div className="flex gap-3 mb-6">
                          <Button
                            onClick={() => setCurrentStep(7)}
                            className="flex-1 bg-ml-success hover:bg-ml-success-dark"
                          >
                            <Download className="w-4 h-4 mr-2" />
                            Export & Deploy
                          </Button>
                          <Button variant="outline" onClick={() => setCurrentStep(3)}>
                            New Classification
                          </Button>
                        </div>

                        <StepNavigation
                          nextLabel="Export & Deploy"
                          onNext={() => setCurrentStep(7)}
                          onPrevious={() => setCurrentStep(5)}
                          previousLabel="Back to Training"
                          currentStep={currentStep}
                          totalSteps={steps.length}
                        />
                        </div>
                      )}

                      {/* Show default message if no training/classification has been started */}
                      {!isTraining && !isClassifying && !trainingResults && !classificationResults && (
                        <div className="text-center py-8">
                          <div className="w-16 h-16 rounded-full bg-muted/20 flex items-center justify-center mx-auto mb-4">
                            <BarChart3 className="w-8 h-8 text-muted-foreground" />
                          </div>
                          <h3 className="text-lg font-semibold mb-2">Ready for Results</h3>
                          <p className="text-muted-foreground mb-4">
                            Complete the training/classification step to see your results here.
                          </p>
                          <Button variant="outline" onClick={() => setCurrentStep(5)}>
                            <ArrowRight className="w-4 h-4 mr-2" />
                            Go to Training
                          </Button>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Step 7: Export & Deploy */}
              {currentStep === 7 && (
                <Card>
                  <CardHeader>
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 rounded-lg bg-ml-success/10 flex items-center justify-center">
                        <Download className="w-5 h-5 text-ml-success" />
                      </div>
                      <div>
                        <CardTitle>Export & Deploy</CardTitle>
                        <CardDescription>
                          Download your model or deploy it to production
                        </CardDescription>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-6">
                      {/* Mock Results Display */}
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <Card className="bg-ml-success/5">
                          <CardContent className="p-4 text-center">
                            <div className="text-2xl font-bold text-ml-success">94.2%</div>
                            <div className="text-sm text-muted-foreground">Accuracy</div>
                          </CardContent>
                        </Card>
                        <Card className="bg-ml-primary/5">
                          <CardContent className="p-4 text-center">
                            <div className="text-2xl font-bold text-ml-primary">91.8%</div>
                            <div className="text-sm text-muted-foreground">Precision</div>
                          </CardContent>
                        </Card>
                        <Card className="bg-ml-secondary/5">
                          <CardContent className="p-4 text-center">
                            <div className="text-2xl font-bold text-ml-secondary">93.5%</div>
                            <div className="text-sm text-muted-foreground">Recall</div>
                          </CardContent>
                        </Card>
                      </div>

                      {/* Export Options */}
                      <div className="space-y-4">
                        <h4 className="font-semibold">Export Your Model</h4>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <Button variant="outline" className="h-auto p-4">
                            <div className="text-left">
                              <div className="font-medium">Download Model</div>
                              <div className="text-sm text-muted-foreground">Get the trained model files</div>
                            </div>
                            <Download className="w-5 h-5 ml-auto" />
                          </Button>
                          <Button variant="outline" className="h-auto p-4">
                            <div className="text-left">
                              <div className="font-medium">API Integration</div>
                              <div className="text-sm text-muted-foreground">Get API endpoints and keys</div>
                            </div>
                            <Zap className="w-5 h-5 ml-auto" />
                          </Button>
                        </div>
                      </div>

                      {/* Export Results */}
                      {(trainingTaskId || classificationTaskId) && (
                        <div className="space-y-4">
                          <h4 className="font-semibold">Export Results</h4>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <Button
                              variant="outline"
                              className="h-auto p-4"
                              onClick={handleExportCSV}
                            >
                              <div className="text-left">
                                <div className="font-medium">Download as CSV</div>
                                <div className="text-sm text-muted-foreground">Export classification results to CSV</div>
                              </div>
                              <FileSpreadsheet className="w-5 h-5 ml-auto" />
                            </Button>
                            <Button
                              variant="outline"
                              className="h-auto p-4"
                              onClick={handleExportExcel}
                            >
                              <div className="text-left">
                                <div className="font-medium">Download as Excel</div>
                                <div className="text-sm text-muted-foreground">Export classification results to Excel</div>
                              </div>
                              <FileSpreadsheet className="w-5 h-5 ml-auto" />
                            </Button>
                          </div>
                        </div>
                      )}

                      {/* Navigation */}
                      <div className="flex gap-3 mb-6">
                        <Button
                          onClick={() => navigate('/dashboard')}
                          className="flex-1 bg-ml-success hover:bg-ml-success-dark"
                        >
                          <CheckCircle2 className="w-4 h-4 mr-2" />
                          Complete Workflow
                        </Button>
                      </div>

                      <StepNavigation
                        showNext={false}
                        onPrevious={() => setCurrentStep(6)}
                        previousLabel="Back to Results"
                        currentStep={currentStep}
                        totalSteps={steps.length}
                      />
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default BeginnerWorkflow;
