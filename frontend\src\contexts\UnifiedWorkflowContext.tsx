/**
 * UnifiedWorkflowContext.tsx
 * 
 * Unified workflow context provider for Phase 4 implementation
 * Integrates workflow routing, progress persistence, error handling, and guidance
 */

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useNavigate, useLocation, useSearchParams } from 'react-router-dom';
import { 
  workflowRouter, 
  ClassificationType, 
  UserExperienceLevel, 
  WorkflowContext as RouterWorkflowContext 
} from '@/services/workflowRouter';
import { 
  workflowProgressManager, 
  WorkflowProgressState 
} from '@/services/workflowProgressManager';
import { 
  unifiedErrorHandler, 
  ClassyWebError, 
  ErrorRecoveryAction 
} from '@/services/unifiedErrorHandler';
import { WorkflowStep } from '@/components/workflow/UnifiedWorkflowNavigation';
import { GuidanceTour, SmartRecommendation } from '@/components/guidance/EnhancedGuidanceSystem';

interface UnifiedWorkflowContextType {
  // Current workflow state
  currentWorkflowId: string | null;
  workflowType: ClassificationType | null;
  experienceLevel: UserExperienceLevel | null;
  currentStep: number;
  totalSteps: number;
  completedSteps: number[];
  
  // Navigation
  steps: WorkflowStep[];
  canNavigateToStep: (step: number) => boolean;
  navigateToStep: (step: number) => void;
  nextStep: () => void;
  previousStep: () => void;
  
  // Progress persistence
  saveProgress: (updates: Partial<WorkflowProgressState>) => void;
  loadProgress: (workflowId: string) => WorkflowProgressState | null;
  canResume: boolean;
  resumeWorkflow: (workflowId: string) => void;
  
  // Error handling
  handleError: (error: Error, context?: any, recoveryActions?: ErrorRecoveryAction[]) => Promise<ClassyWebError>;
  clearErrors: () => void;
  recentErrors: ClassyWebError[];
  
  // Guidance system
  availableTours: GuidanceTour[];
  activeTour: string | null;
  startTour: (tourId: string) => void;
  completeTour: (tourId: string) => void;
  exitTour: () => void;
  recommendations: SmartRecommendation[];
  dismissRecommendation: (recommendationId: string) => void;
  
  // Workflow management
  startNewWorkflow: (
    type: ClassificationType, 
    level: UserExperienceLevel, 
    steps: WorkflowStep[]
  ) => string;
  completeWorkflow: () => void;
  resetWorkflow: () => void;
  
  // UI state
  isLoading: boolean;
  error: string | null;
}

const UnifiedWorkflowContext = createContext<UnifiedWorkflowContextType | undefined>(undefined);

interface UnifiedWorkflowProviderProps {
  children: ReactNode;
}

export const UnifiedWorkflowProvider: React.FC<UnifiedWorkflowProviderProps> = ({ children }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const [searchParams] = useSearchParams();
  
  // Core state
  const [currentWorkflowId, setCurrentWorkflowId] = useState<string | null>(null);
  const [workflowType, setWorkflowType] = useState<ClassificationType | null>(null);
  const [experienceLevel, setExperienceLevel] = useState<UserExperienceLevel | null>(null);
  const [currentStep, setCurrentStep] = useState(1);
  const [totalSteps, setTotalSteps] = useState(0);
  const [completedSteps, setCompletedSteps] = useState<number[]>([]);
  const [steps, setSteps] = useState<WorkflowStep[]>([]);
  
  // UI state
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // Guidance state
  const [availableTours, setAvailableTours] = useState<GuidanceTour[]>([]);
  const [activeTour, setActiveTour] = useState<string | null>(null);
  const [recommendations, setRecommendations] = useState<SmartRecommendation[]>([]);
  
  // Error state
  const [recentErrors, setRecentErrors] = useState<ClassyWebError[]>([]);

  // Initialize workflow from URL parameters
  useEffect(() => {
    const resumeId = searchParams.get('resume');
    const stepParam = searchParams.get('step');
    const modeParam = searchParams.get('mode');
    
    if (resumeId) {
      // Resume existing workflow
      resumeWorkflow(resumeId);
    } else if (stepParam) {
      // Navigate to specific step
      const step = parseInt(stepParam, 10);
      if (!isNaN(step)) {
        setCurrentStep(step);
      }
    }
    
    // Determine workflow type from URL
    const pathType = getWorkflowTypeFromPath(location.pathname);
    if (pathType) {
      setWorkflowType(pathType);
      setExperienceLevel(modeParam === 'expert' ? 'expert' : 'beginner');
    }
  }, [location.pathname, searchParams]);

  // Auto-save progress
  useEffect(() => {
    if (currentWorkflowId && workflowType) {
      const updates: Partial<WorkflowProgressState> = {
        currentStep,
        completedSteps,
        lastUpdated: new Date().toISOString()
      };
      
      workflowProgressManager.updateProgress(currentWorkflowId, updates);
    }
  }, [currentWorkflowId, currentStep, completedSteps, workflowType]);

  const getWorkflowTypeFromPath = (pathname: string): ClassificationType | null => {
    if (pathname.includes('/binary')) return 'binary';
    if (pathname.includes('/multiclass')) return 'multiclass';
    if (pathname.includes('/multilabel')) return 'multilabel';
    if (pathname.includes('/hierarchical')) return 'hierarchical';
    if (pathname.includes('/flat')) return 'flat';
    return null;
  };

  const canNavigateToStep = (step: number): boolean => {
    // Can navigate to completed steps, current step, or next step if current is complete
    return step <= currentStep || (step === currentStep + 1 && completedSteps.includes(currentStep));
  };

  const navigateToStep = (step: number) => {
    if (canNavigateToStep(step)) {
      setCurrentStep(step);
      
      // Update URL
      const newSearchParams = new URLSearchParams(searchParams);
      newSearchParams.set('step', step.toString());
      navigate(`${location.pathname}?${newSearchParams.toString()}`, { replace: true });
    }
  };

  const nextStep = () => {
    if (currentStep < totalSteps) {
      // Mark current step as completed
      if (!completedSteps.includes(currentStep)) {
        setCompletedSteps(prev => [...prev, currentStep]);
      }
      navigateToStep(currentStep + 1);
    }
  };

  const previousStep = () => {
    if (currentStep > 1) {
      navigateToStep(currentStep - 1);
    }
  };

  const saveProgress = (updates: Partial<WorkflowProgressState>) => {
    if (currentWorkflowId) {
      workflowProgressManager.updateProgress(currentWorkflowId, updates);
    }
  };

  const loadProgress = (workflowId: string): WorkflowProgressState | null => {
    return workflowProgressManager.getWorkflowState(workflowId);
  };

  const resumeWorkflow = (workflowId: string) => {
    const state = workflowProgressManager.resumeWorkflow(workflowId);
    if (state) {
      setCurrentWorkflowId(workflowId);
      setWorkflowType(state.workflowType as ClassificationType);
      setCurrentStep(state.currentStep);
      setTotalSteps(state.totalSteps);
      setCompletedSteps(state.completedSteps);
      
      // Update URL to reflect resumed state
      const newSearchParams = new URLSearchParams();
      newSearchParams.set('resume', workflowId);
      newSearchParams.set('step', state.currentStep.toString());
      navigate(`${location.pathname}?${newSearchParams.toString()}`, { replace: true });
    }
  };

  const startNewWorkflow = (
    type: ClassificationType,
    level: UserExperienceLevel,
    workflowSteps: WorkflowStep[]
  ): string => {
    const workflowId = workflowProgressManager.startWorkflow(type, workflowSteps.length);
    
    setCurrentWorkflowId(workflowId);
    setWorkflowType(type);
    setExperienceLevel(level);
    setCurrentStep(1);
    setTotalSteps(workflowSteps.length);
    setCompletedSteps([]);
    setSteps(workflowSteps);
    
    // Set workflow context for router
    workflowRouter.setWorkflowContext({
      fileId: undefined,
      step: 1,
      config: {},
      resumeData: {}
    });
    
    return workflowId;
  };

  const completeWorkflow = () => {
    if (currentWorkflowId) {
      workflowProgressManager.completeWorkflow(currentWorkflowId);
      setCompletedSteps(Array.from({ length: totalSteps }, (_, i) => i + 1));
      workflowRouter.clearWorkflowContext();
    }
  };

  const resetWorkflow = () => {
    if (currentWorkflowId) {
      workflowProgressManager.deleteWorkflow(currentWorkflowId);
    }
    
    setCurrentWorkflowId(null);
    setWorkflowType(null);
    setExperienceLevel(null);
    setCurrentStep(1);
    setTotalSteps(0);
    setCompletedSteps([]);
    setSteps([]);
    workflowRouter.clearWorkflowContext();
  };

  const handleError = async (
    error: Error,
    context?: any,
    recoveryActions?: ErrorRecoveryAction[]
  ): Promise<ClassyWebError> => {
    const classyError = await unifiedErrorHandler.handleError(
      error,
      {
        workflowId: currentWorkflowId || undefined,
        step: currentStep,
        ...context
      },
      recoveryActions
    );
    
    setRecentErrors(prev => [classyError, ...prev.slice(0, 9)]); // Keep last 10 errors
    return classyError;
  };

  const clearErrors = () => {
    setRecentErrors([]);
    unifiedErrorHandler.clearErrorLog();
  };

  const startTour = (tourId: string) => {
    setActiveTour(tourId);
  };

  const completeTour = (tourId: string) => {
    setActiveTour(null);
    // Could track completed tours for user progress
  };

  const exitTour = () => {
    setActiveTour(null);
  };

  const dismissRecommendation = (recommendationId: string) => {
    setRecommendations(prev => prev.filter(rec => rec.id !== recommendationId));
  };

  const canResume = workflowProgressManager.canResumeWorkflow();

  const contextValue: UnifiedWorkflowContextType = {
    // Current workflow state
    currentWorkflowId,
    workflowType,
    experienceLevel,
    currentStep,
    totalSteps,
    completedSteps,
    
    // Navigation
    steps,
    canNavigateToStep,
    navigateToStep,
    nextStep,
    previousStep,
    
    // Progress persistence
    saveProgress,
    loadProgress,
    canResume,
    resumeWorkflow,
    
    // Error handling
    handleError,
    clearErrors,
    recentErrors,
    
    // Guidance system
    availableTours,
    activeTour,
    startTour,
    completeTour,
    exitTour,
    recommendations,
    dismissRecommendation,
    
    // Workflow management
    startNewWorkflow,
    completeWorkflow,
    resetWorkflow,
    
    // UI state
    isLoading,
    error
  };

  return (
    <UnifiedWorkflowContext.Provider value={contextValue}>
      {children}
    </UnifiedWorkflowContext.Provider>
  );
};

// Custom hook to use the unified workflow context
export const useUnifiedWorkflow = (): UnifiedWorkflowContextType => {
  const context = useContext(UnifiedWorkflowContext);
  if (context === undefined) {
    throw new Error('useUnifiedWorkflow must be used within a UnifiedWorkflowProvider');
  }
  return context;
};
