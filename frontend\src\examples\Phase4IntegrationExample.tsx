/**
 * Phase4IntegrationExample.tsx
 * 
 * Example showing how to integrate Phase 4 features into any existing workflow
 * This demonstrates the minimal code needed to add all Phase 4 enhancements
 */

import React, { useState, useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';
import { UnifiedWorkflowNavigation, WorkflowStep } from '@/components/workflow/UnifiedWorkflowNavigation';
import { EnhancedGuidanceSystem, GuidanceTour, SmartRecommendation } from '@/components/guidance/EnhancedGuidanceSystem';
import { WorkflowResumeDialog } from '@/components/workflow/WorkflowResumeDialog';
import { useUnifiedWorkflow } from '@/contexts/UnifiedWorkflowContext';
import { useToast } from '@/hooks/use-toast';
import { Upload, Settings, Brain, BarChart3 } from 'lucide-react';

// Example: Binary Classification Workflow with Phase 4 Integration
const BinaryClassificationWithPhase4 = () => {
  const [searchParams] = useSearchParams();
  const { toast } = useToast();
  const {
    currentWorkflowId,
    currentStep,
    completedSteps,
    canNavigateToStep,
    navigateToStep,
    nextStep,
    previousStep,
    startNewWorkflow,
    resumeWorkflow,
    saveProgress,
    handleError,
    canResume
  } = useUnifiedWorkflow();

  const [activeTour, setActiveTour] = useState<string | null>(null);

  // Step 1: Define your workflow steps (same as before, but with Phase 4 enhancements)
  const workflowSteps: WorkflowStep[] = [
    {
      id: 1,
      title: "Upload Data",
      description: "Upload your binary classification dataset",
      icon: Upload,
      status: currentStep === 1 ? 'current' : completedSteps.includes(1) ? 'complete' : 'pending',
      estimatedTime: '2-3 min',
      helpText: "Upload a CSV file with text data and binary labels (0/1, yes/no, positive/negative)"
    },
    {
      id: 2,
      title: "Configure Model",
      description: "Set up your binary classification model",
      icon: Settings,
      status: currentStep === 2 ? 'current' : completedSteps.includes(2) ? 'complete' : 'pending',
      estimatedTime: '3-5 min',
      helpText: "Choose model type, set hyperparameters, and configure training options"
    },
    {
      id: 3,
      title: "Train Model",
      description: "Train your binary classifier",
      icon: Brain,
      status: currentStep === 3 ? 'current' : completedSteps.includes(3) ? 'complete' : 'pending',
      estimatedTime: '5-15 min',
      helpText: "Monitor training progress and adjust parameters if needed"
    },
    {
      id: 4,
      title: "View Results",
      description: "Analyze model performance",
      icon: BarChart3,
      status: currentStep === 4 ? 'current' : completedSteps.includes(4) ? 'complete' : 'pending',
      estimatedTime: '3-5 min',
      helpText: "Review accuracy, precision, recall, and other performance metrics"
    }
  ];

  // Step 2: Define guided tours (optional but recommended)
  const availableTours: GuidanceTour[] = [
    {
      id: 'binary-basics',
      title: 'Binary Classification Basics',
      description: 'Learn the fundamentals of binary classification',
      category: 'beginner',
      estimatedTime: '3 minutes',
      steps: [
        {
          id: 'intro',
          title: 'Welcome to Binary Classification',
          content: 'Binary classification helps you categorize data into two distinct groups.',
          position: 'bottom'
        },
        {
          id: 'data-requirements',
          title: 'Data Requirements',
          content: 'You need labeled data with exactly two categories for binary classification.',
          position: 'top'
        }
      ]
    }
  ];

  // Step 3: Define smart recommendations (optional)
  const recommendations: SmartRecommendation[] = [
    {
      id: 'balance-check',
      title: 'Check Class Balance',
      description: 'Your dataset might be imbalanced. Consider using balanced sampling techniques.',
      confidence: 0.8,
      category: 'data',
      action: {
        label: 'Apply Balancing',
        onClick: () => {
          // Apply class balancing logic
          toast({ title: "Class balancing applied" });
        }
      }
    }
  ];

  // Step 4: Initialize workflow (this handles resume automatically)
  useEffect(() => {
    const resumeId = searchParams.get('resume');
    
    if (resumeId) {
      resumeWorkflow(resumeId);
    } else if (!currentWorkflowId) {
      startNewWorkflow('binary', 'beginner', workflowSteps);
    }
  }, []);

  // Step 5: Handle step changes with auto-save
  const handleStepChange = (step: number) => {
    navigateToStep(step);
    saveProgress({
      currentStep: step,
      lastUpdated: new Date().toISOString()
    });
  };

  // Step 6: Handle errors with unified error handling
  const handleWorkflowError = async (error: Error) => {
    await handleError(error, {
      workflowType: 'binary',
      step: currentStep
    });
  };

  // Step 7: Your existing workflow logic (unchanged)
  const renderCurrentStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="p-6 border rounded-lg">
            <h3 className="text-lg font-semibold mb-4">Upload Your Data</h3>
            <p>Upload your binary classification dataset here...</p>
            {/* Your existing upload component */}
            <button 
              onClick={nextStep}
              className="mt-4 px-4 py-2 bg-blue-600 text-white rounded"
            >
              Continue to Configuration
            </button>
          </div>
        );
      
      case 2:
        return (
          <div className="p-6 border rounded-lg">
            <h3 className="text-lg font-semibold mb-4">Configure Model</h3>
            <p>Set up your binary classification model...</p>
            {/* Your existing configuration component */}
            <div className="mt-4 flex gap-2">
              <button 
                onClick={previousStep}
                className="px-4 py-2 border rounded"
              >
                Back
              </button>
              <button 
                onClick={nextStep}
                className="px-4 py-2 bg-blue-600 text-white rounded"
              >
                Start Training
              </button>
            </div>
          </div>
        );
      
      case 3:
        return (
          <div className="p-6 border rounded-lg">
            <h3 className="text-lg font-semibold mb-4">Training in Progress</h3>
            <p>Your model is being trained...</p>
            {/* Your existing training component */}
            <button 
              onClick={nextStep}
              className="mt-4 px-4 py-2 bg-blue-600 text-white rounded"
            >
              View Results
            </button>
          </div>
        );
      
      case 4:
        return (
          <div className="p-6 border rounded-lg">
            <h3 className="text-lg font-semibold mb-4">Results</h3>
            <p>Your binary classification results...</p>
            {/* Your existing results component */}
            <button 
              onClick={() => toast({ title: "Workflow completed!" })}
              className="mt-4 px-4 py-2 bg-green-600 text-white rounded"
            >
              Complete Workflow
            </button>
          </div>
        );
      
      default:
        return <div>Invalid step</div>;
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <div className="max-w-4xl mx-auto px-6 py-8">
        
        {/* Step 8: Add resume dialog (shows automatically if workflows exist) */}
        {canResume && <WorkflowResumeDialog autoShow={true} />}
        
        {/* Step 9: Add enhanced guidance system */}
        <div className="mb-6">
          <EnhancedGuidanceSystem
            availableTours={availableTours}
            activeTour={activeTour}
            onTourStart={setActiveTour}
            onTourComplete={() => setActiveTour(null)}
            onTourExit={() => setActiveTour(null)}
            recommendations={recommendations}
            showRecommendations={true}
          />
        </div>

        {/* Step 10: Add unified navigation */}
        <div className="mb-8">
          <UnifiedWorkflowNavigation
            steps={workflowSteps}
            currentStep={currentStep}
            onStepChange={handleStepChange}
            onNext={nextStep}
            onPrevious={previousStep}
            canNavigateToStep={canNavigateToStep}
            showProgress={true}
            showStepList={true}
            showKeyboardHints={true}
            showEstimatedTime={true}
            showHelpTooltips={true}
          />
        </div>

        {/* Step 11: Your existing workflow content (unchanged) */}
        <div className="workflow-content">
          {renderCurrentStep()}
        </div>
      </div>
    </div>
  );
};

export default BinaryClassificationWithPhase4;

/**
 * INTEGRATION CHECKLIST:
 * 
 * ✅ 1. Wrap app with UnifiedWorkflowProvider in App.tsx
 * ✅ 2. Define WorkflowStep[] array with your steps
 * ✅ 3. Use useUnifiedWorkflow() hook for state management
 * ✅ 4. Add UnifiedWorkflowNavigation component
 * ✅ 5. Add EnhancedGuidanceSystem component (optional)
 * ✅ 6. Add WorkflowResumeDialog component
 * ✅ 7. Handle URL parameters for resume functionality
 * ✅ 8. Call saveProgress() when state changes
 * ✅ 9. Use handleError() for error handling
 * ✅ 10. Keep your existing workflow logic unchanged
 * 
 * THAT'S IT! Your workflow now has:
 * - ✅ Progress persistence and resume
 * - ✅ Unified navigation with keyboard shortcuts
 * - ✅ Contextual help and tooltips
 * - ✅ Smart recommendations
 * - ✅ Guided tours
 * - ✅ Consistent error handling
 * - ✅ Progress indicators
 * - ✅ Estimated time display
 */
