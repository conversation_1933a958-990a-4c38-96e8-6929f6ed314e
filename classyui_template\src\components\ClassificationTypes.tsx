import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  Binary, 
  Layers3, 
  Tags, 
  TreePine, 
  LayoutGrid,
  CheckCircle2,
  ArrowRight
} from "lucide-react";

const classificationTypes = [
  {
    id: "binary",
    icon: Binary,
    title: "Binary Classification",
    description: "Two-class problems with clear yes/no decisions",
    examples: ["Spam vs. Not Spam", "Positive vs. Negative", "Fraud Detection"],
    color: "text-ml-primary",
    bgColor: "bg-ml-primary/10"
  },
  {
    id: "multiclass",
    icon: Layers3,
    title: "Multi-class Classification",
    description: "Multiple mutually exclusive classes",
    examples: ["Sentiment Analysis", "Image Recognition", "Document Classification"],
    color: "text-ml-secondary",
    bgColor: "bg-ml-secondary/10"
  },
  {
    id: "multilabel",
    icon: Tags,
    title: "Multi-label Classification",
    description: "Multiple non-exclusive labels per instance",
    examples: ["Topic Tagging", "Movie Genres", "Medical Diagnosis"],
    color: "text-ml-accent",
    bgColor: "bg-ml-accent/10"
  },
  {
    id: "hierarchical",
    icon: TreePine,
    title: "Hierarchical Classification",
    description: "Tree-structured class relationships",
    examples: ["Product Categories", "Taxonomies", "Organizational Structure"],
    color: "text-ml-success",
    bgColor: "bg-ml-success/10"
  },
  {
    id: "flat",
    icon: LayoutGrid,
    title: "Flat Classification",
    description: "Standard single-level classification",
    examples: ["Standard Categories", "Simple Grouping", "Basic Sorting"],
    color: "text-ml-warning",
    bgColor: "bg-ml-warning/10"
  }
];

export const ClassificationTypes = () => {
  return (
    <section className="py-20 bg-background">
      <div className="max-w-7xl mx-auto px-6">
        <div className="text-center mb-16">
          <Badge variant="outline" className="mb-4">
            <CheckCircle2 className="w-4 h-4 mr-2" />
            Classification Types
          </Badge>
          <h2 className="text-4xl md:text-5xl font-bold mb-6">
            Five Powerful 
            <span className="bg-gradient-primary bg-clip-text text-transparent"> Classification Methods</span>
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Choose the perfect classification approach for your machine learning project. 
            Each type is optimized for specific use cases and data structures.
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {classificationTypes.map((type, index) => {
            const Icon = type.icon;
            return (
              <Card 
                key={type.id}
                className="group hover:shadow-card transition-all duration-300 border-border/50 hover:border-primary/20 bg-gradient-card"
              >
                <CardHeader className="pb-4">
                  <div className={`w-12 h-12 rounded-lg ${type.bgColor} flex items-center justify-center mb-4`}>
                    <Icon className={`w-6 h-6 ${type.color}`} />
                  </div>
                  <CardTitle className="text-xl group-hover:text-primary transition-colors">
                    {type.title}
                  </CardTitle>
                  <CardDescription className="text-muted-foreground">
                    {type.description}
                  </CardDescription>
                </CardHeader>
                
                <CardContent className="pt-0">
                  <div className="space-y-2">
                    <h4 className="text-sm font-semibold text-foreground/80 mb-3">Use Cases:</h4>
                    {type.examples.map((example, idx) => (
                      <div key={idx} className="flex items-center gap-2 text-sm text-muted-foreground">
                        <ArrowRight className="w-3 h-3 text-primary" />
                        <span>{example}</span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>
      </div>
    </section>
  );
};