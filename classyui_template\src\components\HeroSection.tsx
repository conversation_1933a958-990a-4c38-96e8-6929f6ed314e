import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON><PERSON>, <PERSON>ap, <PERSON>, Target } from "lucide-react";
import heroImage from "@/assets/hero-ml-platform.jpg";

export const HeroSection = () => {
  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-hero">
      <div className="absolute inset-0 bg-gradient-hero opacity-90" />
      <div className="absolute inset-0">
        <img 
          src={heroImage} 
          alt="ML Classification Platform" 
          className="w-full h-full object-cover opacity-20"
        />
      </div>
      
      <div className="relative z-10 max-w-7xl mx-auto px-6 py-20 text-center">
        <div className="mb-6 flex justify-center">
          <div className="bg-gradient-card backdrop-blur-sm rounded-full px-6 py-2 border border-white/20">
            <div className="flex items-center gap-2 text-sm font-medium">
              <Zap className="w-4 h-4 text-ml-secondary" />
              <span>Powered by Advanced ML & LLMs</span>
            </div>
          </div>
        </div>
        
        <h1 className="text-6xl md:text-7xl font-bold mb-6 bg-gradient-to-r from-white to-white/80 bg-clip-text text-transparent">
          ClassyWeb
        </h1>
        
        <p className="text-xl md:text-2xl mb-4 text-white/90 max-w-3xl mx-auto leading-relaxed">
          The comprehensive machine learning platform for
        </p>
        
        <h2 className="text-3xl md:text-4xl font-bold mb-8 bg-gradient-to-r from-ml-secondary to-ml-accent bg-clip-text text-transparent">
          Advanced Classification Tasks
        </h2>
        
        <p className="text-lg mb-12 text-white/80 max-w-2xl mx-auto">
          Train custom models with GPU acceleration or leverage powerful LLMs. 
          From binary to hierarchical classification - we've got you covered.
        </p>
        
        <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-16">
          <Button variant="hero" size="lg" className="group" asChild>
            <a href="/get-started">
              Get Started
              <ArrowRight className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" />
            </a>
          </Button>
          <Button variant="outline-hero" size="lg" asChild>
            <a href="/dashboard">View Demo</a>
          </Button>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
          <div className="bg-gradient-card backdrop-blur-md rounded-2xl p-6 border border-white/20 hover:shadow-glow transition-all duration-300">
            <Brain className="w-8 h-8 text-ml-secondary mb-4 mx-auto" />
            <h3 className="text-lg font-semibold mb-2 text-white">Custom Models</h3>
            <p className="text-white/70 text-sm">Train with Unsloth GPU acceleration</p>
          </div>
          
          <div className="bg-gradient-card backdrop-blur-md rounded-2xl p-6 border border-white/20 hover:shadow-glow transition-all duration-300">
            <Target className="w-8 h-8 text-ml-accent mb-4 mx-auto" />
            <h3 className="text-lg font-semibold mb-2 text-white">5 Classification Types</h3>
            <p className="text-white/70 text-sm">From binary to hierarchical classification</p>
          </div>
          
          <div className="bg-gradient-card backdrop-blur-md rounded-2xl p-6 border border-white/20 hover:shadow-glow transition-all duration-300">
            <Zap className="w-8 h-8 text-ml-primary mb-4 mx-auto" />
            <h3 className="text-lg font-semibold mb-2 text-white">LLM Integration</h3>
            <p className="text-white/70 text-sm">Groq, OpenAI, Gemini, and more</p>
          </div>
        </div>
      </div>
    </section>
  );
};