#!/usr/bin/env python3
"""
Find a specific task by partial ID.
"""

import sys
import os
from pathlib import Path

# Add backend to path
backend_dir = Path(__file__).parent / "backend"
sys.path.insert(0, str(backend_dir))

def find_task(partial_id: str):
    """Find task by partial ID."""
    print(f"🔍 Searching for task with partial ID: {partial_id}")
    print("=" * 50)
    
    try:
        # Set environment
        os.environ["DATABASE_URL"] = "sqlite:///./backend/classyweb.db"
        
        # Import after setting environment
        from app.database import SessionLocal, Task
        
        with SessionLocal() as db:
            # Search for tasks that start with the partial ID
            tasks = db.query(Task).filter(
                Task.id.like(f"{partial_id}%")
            ).all()
            
            if tasks:
                print(f"Found {len(tasks)} matching tasks:")
                for task in tasks:
                    status_icon = {
                        "PENDING": "⏳",
                        "RUNNING": "🔄", 
                        "SUCCESS": "✅",
                        "FAILED": "❌"
                    }.get(task.status, "❓")
                    
                    print(f"{status_icon} {task.id}")
                    print(f"   Status: {task.status}")
                    print(f"   Type: {task.task_type}")
                    print(f"   Message: {task.message}")
                    print(f"   Created: {task.created_at}")
                    print(f"   Result Path: {task.result_file_path}")
                    
                    if task.result_file_path:
                        result_path = Path(task.result_file_path)
                        exists = "✓" if result_path.exists() else "✗"
                        print(f"   File Exists: {exists}")
                    print()
                    
                return tasks
            else:
                print(f"❌ No tasks found starting with '{partial_id}'")
                
                # Show all recent tasks for reference
                print("\nRecent tasks for reference:")
                recent_tasks = db.query(Task).order_by(Task.created_at.desc()).limit(5).all()
                for task in recent_tasks:
                    print(f"   {task.id[:8]}... | {task.status} | {task.created_at}")
                
                return None
                
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

def main():
    """Main function."""
    if len(sys.argv) > 1:
        partial_id = sys.argv[1]
        find_task(partial_id)
    else:
        print("Usage: python find_task.py <partial_task_id>")
        print("Example: python find_task.py 8864576")

if __name__ == "__main__":
    main()
