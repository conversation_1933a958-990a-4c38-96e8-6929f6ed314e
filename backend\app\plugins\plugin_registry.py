"""Plugin Registry for ClassyWeb Universal Platform."""

import logging
import json
import os
from typing import Dict, List, Any, Optional, Type
from datetime import datetime, timezone
from pathlib import Path

from .base_plugin import BasePlugin, PluginType, PluginMetadata
from ..database import SessionLocal
from ..models.plugin import Plugin as PluginModel

logger = logging.getLogger(__name__)


class PluginRegistry:
    """Central registry for managing ClassyWeb plugins."""
    
    def __init__(self, plugins_directory: str = "plugins"):
        self.plugins: Dict[str, BasePlugin] = {}
        self.plugin_metadata: Dict[str, PluginMetadata] = {}
        self.plugins_directory = Path(plugins_directory)
        self.plugins_directory.mkdir(exist_ok=True)
        
        # Plugin type mappings
        self.plugins_by_type: Dict[PluginType, List[str]] = {
            plugin_type: [] for plugin_type in PluginType
        }
        
        logger.info(f"Plugin registry initialized with directory: {self.plugins_directory}")
    
    async def register_plugin(
        self,
        plugin_name: str,
        plugin_class: Type[BasePlugin],
        config: Dict[str, Any] = None,
        auto_initialize: bool = True
    ) -> bool:
        """Register a new plugin."""
        try:
            logger.info(f"Registering plugin: {plugin_name}")
            
            # Create plugin instance
            plugin_instance = plugin_class(config)
            metadata = plugin_instance.get_metadata()
            
            # Validate plugin
            if not self._validate_plugin(plugin_instance):
                logger.error(f"Plugin validation failed for: {plugin_name}")
                return False
            
            # Check for conflicts
            if plugin_name in self.plugins:
                logger.warning(f"Plugin {plugin_name} already registered, replacing...")
            
            # Register plugin
            self.plugins[plugin_name] = plugin_instance
            self.plugin_metadata[plugin_name] = metadata
            
            # Add to type mapping
            if metadata.plugin_type not in self.plugins_by_type:
                self.plugins_by_type[metadata.plugin_type] = []
            
            if plugin_name not in self.plugins_by_type[metadata.plugin_type]:
                self.plugins_by_type[metadata.plugin_type].append(plugin_name)
            
            # Initialize if requested
            if auto_initialize:
                success = await plugin_instance.initialize()
                if not success:
                    logger.error(f"Plugin initialization failed for: {plugin_name}")
                    self.unregister_plugin(plugin_name)
                    return False
            
            # Save to database
            self._save_plugin_to_db(plugin_name, metadata, config)
            
            logger.info(f"Successfully registered plugin: {plugin_name}")
            return True
            
        except Exception as e:
            logger.error(f"Error registering plugin {plugin_name}: {str(e)}")
            return False
    
    async def unregister_plugin(self, plugin_name: str) -> bool:
        """Unregister a plugin."""
        try:
            if plugin_name not in self.plugins:
                logger.warning(f"Plugin {plugin_name} not found for unregistration")
                return False
            
            plugin = self.plugins[plugin_name]
            metadata = self.plugin_metadata[plugin_name]
            
            # Cleanup plugin
            await plugin.cleanup()
            
            # Remove from registry
            del self.plugins[plugin_name]
            del self.plugin_metadata[plugin_name]
            
            # Remove from type mapping
            if plugin_name in self.plugins_by_type[metadata.plugin_type]:
                self.plugins_by_type[metadata.plugin_type].remove(plugin_name)
            
            # Remove from database
            self._remove_plugin_from_db(plugin_name)
            
            logger.info(f"Successfully unregistered plugin: {plugin_name}")
            return True
            
        except Exception as e:
            logger.error(f"Error unregistering plugin {plugin_name}: {str(e)}")
            return False
    
    def get_plugin(self, plugin_name: str) -> Optional[BasePlugin]:
        """Get a registered plugin by name."""
        return self.plugins.get(plugin_name)
    
    def get_plugins_by_type(self, plugin_type: PluginType) -> List[BasePlugin]:
        """Get all plugins of a specific type."""
        plugin_names = self.plugins_by_type.get(plugin_type, [])
        return [self.plugins[name] for name in plugin_names if name in self.plugins]
    
    def list_plugins(self) -> Dict[str, Dict[str, Any]]:
        """List all registered plugins with their metadata."""
        result = {}
        for name, plugin in self.plugins.items():
            metadata = self.plugin_metadata[name]
            result[name] = {
                "metadata": metadata.__dict__,
                "capabilities": plugin.get_capabilities().__dict__,
                "health": plugin.get_health_status(),
                "is_initialized": plugin.is_initialized
            }
        return result
    
    def get_plugin_capabilities(self, plugin_name: str) -> Optional[Dict[str, Any]]:
        """Get capabilities of a specific plugin."""
        plugin = self.get_plugin(plugin_name)
        if plugin:
            return plugin.get_capabilities().__dict__
        return None
    
    async def initialize_all_plugins(self) -> Dict[str, bool]:
        """Initialize all registered plugins."""
        results = {}
        for name, plugin in self.plugins.items():
            if not plugin.is_initialized:
                try:
                    success = await plugin.initialize()
                    results[name] = success
                    if success:
                        logger.info(f"Plugin {name} initialized successfully")
                    else:
                        logger.error(f"Plugin {name} initialization failed")
                except Exception as e:
                    logger.error(f"Error initializing plugin {name}: {str(e)}")
                    results[name] = False
            else:
                results[name] = True
        return results
    
    async def cleanup_all_plugins(self) -> Dict[str, bool]:
        """Cleanup all registered plugins."""
        results = {}
        for name, plugin in self.plugins.items():
            try:
                success = await plugin.cleanup()
                results[name] = success
                if success:
                    logger.info(f"Plugin {name} cleaned up successfully")
                else:
                    logger.error(f"Plugin {name} cleanup failed")
            except Exception as e:
                logger.error(f"Error cleaning up plugin {name}: {str(e)}")
                results[name] = False
        return results
    
    def _validate_plugin(self, plugin: BasePlugin) -> bool:
        """Validate a plugin before registration."""
        try:
            # Check required methods
            metadata = plugin.get_metadata()
            capabilities = plugin.get_capabilities()
            
            # Validate metadata
            if not metadata.name or not metadata.version:
                logger.error("Plugin metadata missing name or version")
                return False
            
            # Validate plugin type
            if not isinstance(metadata.plugin_type, PluginType):
                logger.error("Invalid plugin type")
                return False
            
            # Validate configuration if provided
            if hasattr(plugin, 'config') and plugin.config:
                if not plugin.validate_config(plugin.config):
                    logger.error("Plugin configuration validation failed")
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"Plugin validation error: {str(e)}")
            return False
    
    def _save_plugin_to_db(self, name: str, metadata: PluginMetadata, config: Dict[str, Any]):
        """Save plugin information to database."""
        try:
            with SessionLocal() as db:
                # Check if plugin already exists
                existing_plugin = db.query(PluginModel).filter(PluginModel.name == name).first()
                
                plugin_data = {
                    "name": name,
                    "version": metadata.version,
                    "description": metadata.description,
                    "author": metadata.author,
                    "plugin_type": metadata.plugin_type.value,
                    "capabilities": metadata.capabilities or [],
                    "dependencies": metadata.dependencies or [],
                    "supported_formats": metadata.supported_formats or [],
                    "enterprise_only": metadata.enterprise_only,
                    "license": metadata.license,
                    "documentation_url": metadata.documentation_url,
                    "support_url": metadata.support_url,
                    "config": config or {},
                    "is_active": True,
                    "registered_at": datetime.now(timezone.utc)
                }
                
                if existing_plugin:
                    # Update existing plugin
                    for key, value in plugin_data.items():
                        if key != "registered_at":  # Don't update registration time
                            setattr(existing_plugin, key, value)
                    existing_plugin.updated_at = datetime.now(timezone.utc)
                else:
                    # Create new plugin
                    new_plugin = PluginModel(**plugin_data)
                    db.add(new_plugin)
                
                db.commit()
                logger.info(f"Plugin {name} saved to database")
                
        except Exception as e:
            logger.error(f"Error saving plugin {name} to database: {str(e)}")
    
    def _remove_plugin_from_db(self, name: str):
        """Remove plugin from database."""
        try:
            with SessionLocal() as db:
                plugin = db.query(PluginModel).filter(PluginModel.name == name).first()
                if plugin:
                    db.delete(plugin)
                    db.commit()
                    logger.info(f"Plugin {name} removed from database")
                
        except Exception as e:
            logger.error(f"Error removing plugin {name} from database: {str(e)}")
    
    def load_plugins_from_db(self) -> List[Dict[str, Any]]:
        """Load plugin configurations from database."""
        try:
            with SessionLocal() as db:
                plugins = db.query(PluginModel).filter(PluginModel.is_active == True).all()
                return [
                    {
                        "name": plugin.name,
                        "version": plugin.version,
                        "description": plugin.description,
                        "author": plugin.author,
                        "plugin_type": plugin.plugin_type,
                        "capabilities": plugin.capabilities,
                        "dependencies": plugin.dependencies,
                        "config": plugin.config,
                        "registered_at": plugin.registered_at,
                        "updated_at": plugin.updated_at
                    }
                    for plugin in plugins
                ]
        except Exception as e:
            logger.error(f"Error loading plugins from database: {str(e)}")
            return []


# Global plugin registry instance
plugin_registry = PluginRegistry()
