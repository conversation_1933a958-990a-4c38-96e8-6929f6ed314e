"""Engine Factory for ClassyWeb ML Platform.

This module provides factory methods for creating and managing classification engines.
"""

import logging
from typing import Dict, Type, Optional, List
from .base_engine import BaseClassificationEngine, ClassificationType
from .binary_engine import BinaryClassificationEngine
from .multiclass_engine import MultiClassEngine
from .multilabel_engine import MultiLabelEngine
from .hierarchical_engine import HierarchicalEngine
from .flat_engine import FlatClassificationEngine

logger = logging.getLogger(__name__)


class ClassificationEngineFactory:
    """Factory class for creating classification engines."""
    
    # Registry of available engines
    _engines: Dict[ClassificationType, Type[BaseClassificationEngine]] = {
        ClassificationType.BINARY: BinaryClassificationEngine,
        ClassificationType.MULTICLASS: MultiClassEngine,
        ClassificationType.MULTILABEL: MultiLabelEngine,
        ClassificationType.HIERARCHICAL: HierarchicalEngine,
        ClassificationType.FLAT: FlatClassificationEngine,
    }
    
    @classmethod
    def create_engine(
        self,
        classification_type: ClassificationType,
        **kwargs
    ) -> BaseClassificationEngine:
        """Create a classification engine of the specified type.
        
        Args:
            classification_type: Type of classification engine to create
            **kwargs: Additional arguments to pass to the engine constructor
            
        Returns:
            Instance of the requested classification engine
            
        Raises:
            ValueError: If the classification type is not supported
        """
        if classification_type not in self._engines:
            available_types = list(self._engines.keys())
            raise ValueError(
                f"Unsupported classification type: {classification_type}. "
                f"Available types: {available_types}"
            )
        
        engine_class = self._engines[classification_type]
        logger.info(f"Creating {classification_type} engine: {engine_class.__name__}")
        
        try:
            engine = engine_class(classification_type, **kwargs)
            logger.info(f"Successfully created {classification_type} engine")
            return engine
        except Exception as e:
            logger.error(f"Failed to create {classification_type} engine: {e}")
            raise
    
    @classmethod
    def get_available_types(cls) -> List[ClassificationType]:
        """Get list of available classification types.
        
        Returns:
            List of supported classification types
        """
        return list(cls._engines.keys())
    
    @classmethod
    def register_engine(
        cls,
        classification_type: ClassificationType,
        engine_class: Type[BaseClassificationEngine]
    ) -> None:
        """Register a new engine type.
        
        Args:
            classification_type: Classification type identifier
            engine_class: Engine class to register
        """
        if not issubclass(engine_class, BaseClassificationEngine):
            raise ValueError(
                f"Engine class must inherit from BaseClassificationEngine"
            )
        
        cls._engines[classification_type] = engine_class
        logger.info(f"Registered engine {engine_class.__name__} for type {classification_type}")
    
    @classmethod
    def get_engine_info(cls, classification_type: ClassificationType) -> Dict[str, any]:
        """Get information about a specific engine type.
        
        Args:
            classification_type: Type to get information for
            
        Returns:
            Dictionary with engine information
        """
        if classification_type not in cls._engines:
            return {}
        
        engine_class = cls._engines[classification_type]
        
        # Create temporary instance to get capabilities
        try:
            temp_engine = engine_class(classification_type)
            return {
                "name": engine_class.__name__,
                "classification_type": classification_type,
                "supported_training_methods": temp_engine.supported_training_methods,
                "default_metrics": temp_engine.default_metrics,
                "description": engine_class.__doc__ or "No description available"
            }
        except Exception as e:
            logger.warning(f"Could not get info for {classification_type}: {e}")
            return {
                "name": engine_class.__name__,
                "classification_type": classification_type,
                "error": str(e)
            }


def get_engine_for_type(classification_type: ClassificationType, **kwargs) -> BaseClassificationEngine:
    """Convenience function to create an engine for a specific type.
    
    Args:
        classification_type: Type of classification engine needed
        **kwargs: Additional arguments for engine creation
        
    Returns:
        Instance of the requested classification engine
    """
    return ClassificationEngineFactory.create_engine(classification_type, **kwargs)


def get_all_engine_info() -> Dict[str, Dict[str, any]]:
    """Get information about all available engines.
    
    Returns:
        Dictionary mapping classification types to engine information
    """
    info = {}
    for classification_type in ClassificationEngineFactory.get_available_types():
        info[classification_type.value] = ClassificationEngineFactory.get_engine_info(classification_type)
    return info


def validate_classification_type(classification_type: str) -> ClassificationType:
    """Validate and convert string to ClassificationType enum.
    
    Args:
        classification_type: String representation of classification type
        
    Returns:
        ClassificationType enum value
        
    Raises:
        ValueError: If the classification type is invalid
    """
    try:
        return ClassificationType(classification_type.lower())
    except ValueError:
        available_types = [t.value for t in ClassificationType]
        raise ValueError(
            f"Invalid classification type: {classification_type}. "
            f"Available types: {available_types}"
        )
