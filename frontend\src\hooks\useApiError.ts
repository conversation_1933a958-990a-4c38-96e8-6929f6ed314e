// frontend/src/hooks/useApiError.ts
import { useState, useCallback } from 'react';
import axios from 'axios';

/**
 * Custom hook for handling API errors
 * @returns Object with error state and error handling functions
 */
export const useApiError = () => {
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  /**
   * Clears the current error
   */
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  /**
   * Handles API errors and extracts error messages
   * @param error The error object from the API call
   */
  const handleApiError = useCallback((error: any) => {
    // Extract error message from axios error or use default
    let message = 'An unknown error occurred';
    
    if (axios.isAxiosError(error)) {
      // Handle Axios errors
      if (error.response) {
        // The request was made and the server responded with a status code
        // that falls out of the range of 2xx
        message = error.response.data?.detail || 
                  error.response.data?.message || 
                  error.response.data?.error || 
                  `Error ${error.response.status}: ${error.response.statusText}`;
      } else if (error.request) {
        // The request was made but no response was received
        message = 'No response received from server. Please check your connection.';
      } else {
        // Something happened in setting up the request
        message = error.message || 'Error setting up the request';
      }
    } else if (error instanceof Error) {
      // Handle regular JS errors
      message = error.message;
    } else if (typeof error === 'string') {
      // Handle string errors
      message = error;
    } else if (typeof error === 'object' && error !== null) {
      // Handle error objects
      message = error.message || error.error || error.detail || JSON.stringify(error);
    }

    setError(message);
    // Log to console for debugging
    console.error('API Error:', message, error);
    
    // Return the message for potential further handling
    return message;
  }, []);

  /**
   * Wraps an async function with loading state and error handling
   * @param asyncFn The async function to wrap
   * @returns A function that handles loading state and errors
   */
  const withErrorHandling = useCallback(<T extends any[], R>(
    asyncFn: (...args: T) => Promise<R>
  ) => {
    return async (...args: T): Promise<R | null> => {
      setIsLoading(true);
      clearError();
      try {
        const result = await asyncFn(...args);
        setIsLoading(false);
        return result;
      } catch (err) {
        handleApiError(err);
        setIsLoading(false);
        return null;
      }
    };
  }, [handleApiError, clearError]);

  return { 
    error, 
    setError, 
    clearError, 
    handleApiError, 
    withErrorHandling,
    isLoading,
    setIsLoading
  };
};

export default useApiError;
