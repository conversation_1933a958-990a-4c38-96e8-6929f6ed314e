.env
temp_uploads/
# .gitignore

# Python
backend/venv/
backend/__pycache__/
backend/.env
backend/temp_uploads/ # If you don't want to commit temp files
backend/results_hf_training/
backend/saved_hf_models/
backend/logs_hf_training/
backend/temp_uploads/
backend/model_artifacts
backend/classyweb.db
*.pyc
.DS_Store

# Node
frontend/node_modules/
frontend/dist/
frontend/.env*
frontend/.eslintcache
npm-debug.log*
yarn-debug.log*
yarn-error.log*
*.local

# IDE/OS files
.idea/
.vscode/

*.classyweb.db
*.example
