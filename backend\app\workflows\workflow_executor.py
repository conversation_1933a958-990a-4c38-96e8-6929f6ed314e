"""Workflow Executor for ClassyWeb Universal Platform.

Executes workflow steps and manages step-by-step execution.
"""

import logging
import asyncio
from typing import Dict, List, Optional, Any, Callable
from enum import Enum
from dataclasses import dataclass

from .workflow_engine import WorkflowStep, WorkflowStepType
from .workflow_manager import WorkflowManager, WorkflowStatus

logger = logging.getLogger(__name__)


class ExecutionResult(Enum):
    """Step execution results."""
    SUCCESS = "success"
    FAILURE = "failure"
    SKIPPED = "skipped"
    RETRY_NEEDED = "retry_needed"


@dataclass
class StepExecutionResult:
    """Result of step execution."""
    step_id: str
    result: ExecutionResult
    output_data: Dict[str, Any]
    error_message: Optional[str] = None
    execution_time: Optional[float] = None


class WorkflowExecutor:
    """Executes workflow steps with proper error handling and state management."""
    
    def __init__(self, workflow_manager: WorkflowManager):
        """Initialize the workflow executor."""
        self.workflow_manager = workflow_manager
        self.step_handlers: Dict[WorkflowStepType, Callable] = {}
        
        # Register default step handlers
        self._register_default_handlers()
        
        logger.info("Workflow Executor initialized")
    
    def _register_default_handlers(self):
        """Register default step execution handlers."""
        self.step_handlers[WorkflowStepType.DATA_UPLOAD] = self._handle_data_input
        self.step_handlers[WorkflowStepType.DATA_ANALYSIS] = self._handle_data_input
        self.step_handlers[WorkflowStepType.DATA_PREPROCESSING] = self._handle_configuration
        self.step_handlers[WorkflowStepType.HIERARCHY_CONFIGURATION] = self._handle_configuration
        self.step_handlers[WorkflowStepType.MODEL_SELECTION] = self._handle_configuration
        self.step_handlers[WorkflowStepType.TRAINING] = self._handle_training
        self.step_handlers[WorkflowStepType.CLASSIFICATION] = self._handle_training
        self.step_handlers[WorkflowStepType.VALIDATION] = self._handle_validation
        self.step_handlers[WorkflowStepType.EXPORT] = self._handle_deployment
        self.step_handlers[WorkflowStepType.CUSTOM] = self._handle_custom
    
    async def execute_step(
        self,
        instance_id: str,
        step: WorkflowStep,
        context: Dict[str, Any]
    ) -> StepExecutionResult:
        """Execute a single workflow step."""
        try:
            import time
            start_time = time.time()
            
            logger.info(f"Executing step {step.step_id} for workflow {instance_id}")
            
            # Get handler for step type
            handler = self.step_handlers.get(step.step_type)
            if not handler:
                raise ValueError(f"No handler registered for step type: {step.step_type}")
            
            # Execute step
            result = await handler(step, context)
            
            execution_time = time.time() - start_time
            
            logger.info(f"Step {step.step_id} completed in {execution_time:.2f}s")
            
            return StepExecutionResult(
                step_id=step.step_id,
                result=ExecutionResult.SUCCESS,
                output_data=result,
                execution_time=execution_time
            )
            
        except Exception as e:
            logger.error(f"Step {step.step_id} failed: {e}", exc_info=True)
            
            return StepExecutionResult(
                step_id=step.step_id,
                result=ExecutionResult.FAILURE,
                output_data={},
                error_message=str(e)
            )
    
    async def execute_workflow(self, instance_id: str) -> bool:
        """Execute an entire workflow."""
        try:
            # Get workflow instance
            instance = self.workflow_manager.get_workflow_status(instance_id)
            if not instance:
                raise ValueError(f"Workflow instance not found: {instance_id}")
            
            # Get template
            template = self.workflow_manager.workflow_engine.get_template(instance.template_id)
            if not template:
                raise ValueError(f"Template not found: {instance.template_id}")
            
            # Start workflow if not already running
            if instance.status == WorkflowStatus.CREATED:
                self.workflow_manager.start_workflow(instance_id)
            
            # Execute steps
            for i, step in enumerate(template.steps[instance.current_step:], instance.current_step):
                # Check if workflow is still running
                current_instance = self.workflow_manager.get_workflow_status(instance_id)
                if not current_instance or current_instance.status != WorkflowStatus.RUNNING:
                    logger.info(f"Workflow {instance_id} stopped at step {i}")
                    break
                
                # Execute step
                result = await self.execute_step(instance_id, step, current_instance.context)
                
                # Update context with step results
                context_updates = {
                    f"step_{step.step_id}_result": result.output_data,
                    f"step_{step.step_id}_status": result.result.value
                }
                self.workflow_manager.update_workflow_context(instance_id, context_updates)
                
                # Handle step result
                if result.result == ExecutionResult.FAILURE:
                    self.workflow_manager.fail_workflow(instance_id, result.error_message or "Step failed")
                    return False
                elif result.result == ExecutionResult.RETRY_NEEDED:
                    # Implement retry logic here
                    logger.warning(f"Step {step.step_id} needs retry - not implemented yet")
                    continue
                
                # Advance to next step
                self.workflow_manager.advance_workflow_step(instance_id)
            
            logger.info(f"Workflow {instance_id} execution completed")
            return True
            
        except Exception as e:
            logger.error(f"Workflow execution failed for {instance_id}: {e}", exc_info=True)
            self.workflow_manager.fail_workflow(instance_id, str(e))
            return False
    
    async def _handle_data_input(self, step: WorkflowStep, context: Dict[str, Any]) -> Dict[str, Any]:
        """Handle data input step."""
        logger.info(f"Processing data input step: {step.step_id}")
        
        # Simulate data input processing
        await asyncio.sleep(1)  # Simulate processing time
        
        return {
            "data_loaded": True,
            "data_format": step.config.get("accepted_formats", ["csv"])[0],
            "validation_passed": True
        }
    
    async def _handle_configuration(self, step: WorkflowStep, context: Dict[str, Any]) -> Dict[str, Any]:
        """Handle configuration step."""
        logger.info(f"Processing configuration step: {step.step_id}")
        
        # Simulate configuration processing
        await asyncio.sleep(0.5)
        
        return {
            "configuration_applied": True,
            "config_values": step.config
        }
    
    async def _handle_training(self, step: WorkflowStep, context: Dict[str, Any]) -> Dict[str, Any]:
        """Handle training step."""
        logger.info(f"Processing training step: {step.step_id}")
        
        # Simulate training process
        await asyncio.sleep(5)  # Simulate longer training time
        
        return {
            "training_completed": True,
            "model_accuracy": 0.85,
            "training_method": step.config.get("training_method", "hf"),
            "epochs": step.config.get("epochs", 3)
        }
    
    async def _handle_validation(self, step: WorkflowStep, context: Dict[str, Any]) -> Dict[str, Any]:
        """Handle validation step."""
        logger.info(f"Processing validation step: {step.step_id}")
        
        # Simulate validation process
        await asyncio.sleep(2)
        
        return {
            "validation_completed": True,
            "validation_passed": True,
            "metrics": {
                "accuracy": 0.85,
                "precision": 0.82,
                "recall": 0.88
            }
        }
    
    async def _handle_deployment(self, step: WorkflowStep, context: Dict[str, Any]) -> Dict[str, Any]:
        """Handle deployment step."""
        logger.info(f"Processing deployment step: {step.step_id}")
        
        # Simulate deployment process
        await asyncio.sleep(3)
        
        return {
            "deployment_completed": True,
            "deployment_url": "https://api.classyweb.com/models/deployed-model",
            "scaling_enabled": step.config.get("scaling", "manual") == "auto"
        }
    
    async def _handle_custom(self, step: WorkflowStep, context: Dict[str, Any]) -> Dict[str, Any]:
        """Handle custom step."""
        logger.info(f"Processing custom step: {step.step_id}")
        
        # Simulate custom processing
        await asyncio.sleep(1)
        
        return {
            "custom_step_completed": True,
            "custom_config": step.config
        }
    
    def register_step_handler(self, step_type: WorkflowStepType, handler: Callable) -> None:
        """Register a custom step handler."""
        self.step_handlers[step_type] = handler
        logger.info(f"Registered custom handler for step type: {step_type}")
    
    def get_step_handler(self, step_type: WorkflowStepType) -> Optional[Callable]:
        """Get step handler for a given type."""
        return self.step_handlers.get(step_type)


# Global workflow executor instance
workflow_executor = None

def get_workflow_executor() -> WorkflowExecutor:
    """Get the global workflow executor instance."""
    global workflow_executor
    if workflow_executor is None:
        from .workflow_manager import workflow_manager
        workflow_executor = WorkflowExecutor(workflow_manager)
    return workflow_executor
