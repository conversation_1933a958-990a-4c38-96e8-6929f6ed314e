/**
 * FlatResults.tsx
 *
 * Results visualization for flat classification with performance metrics,
 * scalability analysis, and optimization recommendations.
 */

import React, { useState } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import { useToast } from "@/hooks/use-toast";
import {
  BarChart3,
  Database,
  Zap,
  TrendingUp,
  Download,
  AlertCircle,
  CheckCircle2,
  Info,
  Activity,
  HardDrive,
  Cpu,
  MemoryStick,
  Clock
} from "lucide-react";

export interface FlatResults {
  // Overall metrics
  accuracy: number;
  f1Score: number;
  precision: number;
  recall: number;
  
  // Performance metrics
  performanceMetrics: {
    trainingTime: number;
    inferenceTime: number;
    throughput: number; // samples per second
    memoryUsage: number; // GB
    modelSize: number; // MB
    cpuUtilization: number; // percentage
  };
  
  // Scalability analysis
  scalabilityAnalysis: {
    maxDatasetSize: number;
    recommendedBatchSize: number;
    memoryEfficiency: number;
    scalabilityScore: number; // 0-100
    bottlenecks: string[];
  };
  
  // Dataset complexity
  datasetComplexity: {
    totalSamples: number;
    avgTextLength: number;
    vocabularySize: number;
    complexityScore: number; // 0-100
  };
  
  // Optimization recommendations
  optimizationRecommendations: Array<{
    type: 'performance' | 'memory' | 'scalability';
    title: string;
    description: string;
    impact: 'low' | 'medium' | 'high';
    effort: 'low' | 'medium' | 'high';
  }>;
  
  // Sample predictions
  samplePredictions?: Array<{
    text: string;
    actualClass: string;
    predictedClass: string;
    confidence: number;
    processingTime: number;
    correct: boolean;
  }>;
}

interface FlatResultsProps {
  results: FlatResults;
  onExport?: (format: string) => void;
  onRetrain?: () => void;
  onOptimize?: (recommendation: any) => void;
  showExportOptions?: boolean;
}

export const FlatResults: React.FC<FlatResultsProps> = ({
  results,
  onExport,
  onRetrain,
  onOptimize,
  showExportOptions = true
}) => {
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState('overview');

  const handleExport = (format: string) => {
    if (onExport) {
      onExport(format);
      toast({
        title: "Export started",
        description: `Exporting results in ${format.toUpperCase()} format`
      });
    }
  };

  const getImpactColor = (impact: string) => {
    switch (impact) {
      case 'high': return 'text-green-600';
      case 'medium': return 'text-yellow-600';
      case 'low': return 'text-blue-600';
      default: return 'text-gray-600';
    }
  };

  const getEffortColor = (effort: string) => {
    switch (effort) {
      case 'low': return 'bg-green-100 text-green-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'high': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold flex items-center gap-2">
            <BarChart3 className="w-5 h-5" />
            Flat Classification Results
          </h3>
          <p className="text-sm text-muted-foreground">
            Performance analysis and scalability insights
          </p>
        </div>
        <div className="flex gap-2">
          {showExportOptions && (
            <Button variant="outline" onClick={() => handleExport('csv')}>
              <Download className="w-4 h-4 mr-2" />
              Export Results
            </Button>
          )}
          {onRetrain && (
            <Button variant="outline" onClick={onRetrain}>
              Retrain Model
            </Button>
          )}
        </div>
      </div>

      {/* Overall Performance */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-green-600">
              {(results.accuracy * 100).toFixed(1)}%
            </div>
            <div className="text-sm text-muted-foreground">Accuracy</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-blue-600">
              {results.performanceMetrics.throughput}
            </div>
            <div className="text-sm text-muted-foreground">Samples/sec</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-purple-600">
              {results.performanceMetrics.memoryUsage.toFixed(1)}GB
            </div>
            <div className="text-sm text-muted-foreground">Memory Usage</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-orange-600">
              {results.scalabilityAnalysis.scalabilityScore}
            </div>
            <div className="text-sm text-muted-foreground">Scalability Score</div>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Results */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="scalability">Scalability</TabsTrigger>
          <TabsTrigger value="recommendations">Recommendations</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-sm flex items-center gap-2">
                  <BarChart3 className="w-4 h-4" />
                  Classification Metrics
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between">
                  <span>Accuracy</span>
                  <span className="font-medium">{(results.accuracy * 100).toFixed(2)}%</span>
                </div>
                <div className="flex justify-between">
                  <span>F1 Score</span>
                  <span className="font-medium">{(results.f1Score * 100).toFixed(2)}%</span>
                </div>
                <div className="flex justify-between">
                  <span>Precision</span>
                  <span className="font-medium">{(results.precision * 100).toFixed(2)}%</span>
                </div>
                <div className="flex justify-between">
                  <span>Recall</span>
                  <span className="font-medium">{(results.recall * 100).toFixed(2)}%</span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-sm flex items-center gap-2">
                  <Database className="w-4 h-4" />
                  Dataset Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between">
                  <span>Total Samples</span>
                  <span className="font-medium">{results.datasetComplexity.totalSamples.toLocaleString()}</span>
                </div>
                <div className="flex justify-between">
                  <span>Avg Text Length</span>
                  <span className="font-medium">{results.datasetComplexity.avgTextLength} chars</span>
                </div>
                <div className="flex justify-between">
                  <span>Vocabulary Size</span>
                  <span className="font-medium">{results.datasetComplexity.vocabularySize.toLocaleString()}</span>
                </div>
                <div className="flex justify-between">
                  <span>Complexity Score</span>
                  <span className="font-medium">{results.datasetComplexity.complexityScore}/100</span>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="performance" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Zap className="w-4 h-4" />
                Performance Metrics
              </CardTitle>
              <CardDescription>
                Detailed performance analysis and resource utilization
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <Clock className="w-4 h-4" />
                    <span className="font-medium">Timing Metrics</span>
                  </div>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Training Time</span>
                      <span>{Math.round(results.performanceMetrics.trainingTime / 60)} minutes</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Avg Inference Time</span>
                      <span>{(results.performanceMetrics.inferenceTime * 1000).toFixed(1)}ms</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Throughput</span>
                      <span>{results.performanceMetrics.throughput} samples/sec</span>
                    </div>
                  </div>
                </div>

                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <MemoryStick className="w-4 h-4" />
                    <span className="font-medium">Resource Usage</span>
                  </div>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Memory Usage</span>
                      <span>{results.performanceMetrics.memoryUsage.toFixed(1)}GB</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Model Size</span>
                      <span>{results.performanceMetrics.modelSize}MB</span>
                    </div>
                    <div className="flex justify-between">
                      <span>CPU Utilization</span>
                      <span>{results.performanceMetrics.cpuUtilization}%</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Performance visualization */}
              <div className="space-y-3">
                <h4 className="font-medium">Resource Utilization</h4>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Memory Usage</span>
                    <span>{results.performanceMetrics.memoryUsage.toFixed(1)}GB / 8GB</span>
                  </div>
                  <Progress value={(results.performanceMetrics.memoryUsage / 8) * 100} />
                </div>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>CPU Utilization</span>
                    <span>{results.performanceMetrics.cpuUtilization}%</span>
                  </div>
                  <Progress value={results.performanceMetrics.cpuUtilization} />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="scalability" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Database className="w-4 h-4" />
                Scalability Analysis
              </CardTitle>
              <CardDescription>
                Analysis of model scalability and performance at scale
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-3">
                  <h4 className="font-medium">Scalability Metrics</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Max Dataset Size</span>
                      <span>{(results.scalabilityAnalysis.maxDatasetSize / 1000000).toFixed(1)}M samples</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Recommended Batch Size</span>
                      <span>{results.scalabilityAnalysis.recommendedBatchSize}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Memory Efficiency</span>
                      <span>{(results.scalabilityAnalysis.memoryEfficiency * 100).toFixed(1)}%</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Scalability Score</span>
                      <span className="font-medium">{results.scalabilityAnalysis.scalabilityScore}/100</span>
                    </div>
                  </div>
                </div>

                <div className="space-y-3">
                  <h4 className="font-medium">Performance Bottlenecks</h4>
                  <div className="space-y-2">
                    {results.scalabilityAnalysis.bottlenecks.length > 0 ? (
                      results.scalabilityAnalysis.bottlenecks.map((bottleneck, index) => (
                        <Alert key={index} variant="destructive">
                          <AlertCircle className="h-4 w-4" />
                          <AlertDescription className="text-sm">
                            {bottleneck}
                          </AlertDescription>
                        </Alert>
                      ))
                    ) : (
                      <Alert>
                        <CheckCircle2 className="h-4 w-4" />
                        <AlertDescription className="text-sm">
                          No performance bottlenecks detected. Your model is well-optimized!
                        </AlertDescription>
                      </Alert>
                    )}
                  </div>
                </div>
              </div>

              {/* Scalability Score Visualization */}
              <div className="space-y-3">
                <h4 className="font-medium">Scalability Score Breakdown</h4>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Overall Scalability</span>
                    <span>{results.scalabilityAnalysis.scalabilityScore}/100</span>
                  </div>
                  <Progress value={results.scalabilityAnalysis.scalabilityScore} />
                </div>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Memory Efficiency</span>
                    <span>{(results.scalabilityAnalysis.memoryEfficiency * 100).toFixed(1)}%</span>
                  </div>
                  <Progress value={results.scalabilityAnalysis.memoryEfficiency * 100} />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="recommendations" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="w-4 h-4" />
                Optimization Recommendations
              </CardTitle>
              <CardDescription>
                Suggestions to improve performance and scalability
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {results.optimizationRecommendations.map((recommendation, index) => (
                  <Card key={index} className="border-l-4 border-l-primary">
                    <CardContent className="p-4">
                      <div className="flex items-start justify-between">
                        <div className="space-y-2">
                          <div className="flex items-center gap-2">
                            <h4 className="font-medium">{recommendation.title}</h4>
                            <Badge variant="outline">{recommendation.type}</Badge>
                          </div>
                          <p className="text-sm text-muted-foreground">
                            {recommendation.description}
                          </p>
                          <div className="flex gap-2">
                            <Badge className={getImpactColor(recommendation.impact)}>
                              {recommendation.impact} impact
                            </Badge>
                            <Badge variant="outline" className={getEffortColor(recommendation.effort)}>
                              {recommendation.effort} effort
                            </Badge>
                          </div>
                        </div>
                        {onOptimize && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => onOptimize(recommendation)}
                          >
                            Apply
                          </Button>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Export Options */}
      {showExportOptions && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Download className="w-4 h-4" />
              Export Results
            </CardTitle>
            <CardDescription>
              Download your flat classification results and analysis
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex gap-2">
              <Button variant="outline" onClick={() => handleExport('csv')}>
                Export as CSV
              </Button>
              <Button variant="outline" onClick={() => handleExport('json')}>
                Export as JSON
              </Button>
              <Button variant="outline" onClick={() => handleExport('pdf')}>
                Export Report (PDF)
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
