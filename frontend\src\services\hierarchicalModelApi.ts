/**
 * API service for hierarchical model management
 */

import apiClient from './apiClient';

export interface HierarchicalModelMetrics {
  hierarchical_f1: number;
  path_accuracy: number;
  level_wise_accuracy: number[];
  training_time: number;
  validation_loss: number;
}

export interface HierarchicalModelMetadata {
  hierarchy_levels: number;
  total_samples: number;
  model_size: number;
  description?: string;
  constraints_used: boolean;
  level_wise_training: boolean;
}

export interface HierarchicalModelTrainingConfig {
  num_epochs: number;
  batch_size: number;
  learning_rate: number;
  validation_split: number;
  use_unsloth: boolean;
  base_model: string;
  warmup_steps: number;
  weight_decay: number;
  gradient_accumulation_steps: number;
  fp16: boolean;
  gradient_checkpointing: boolean;
  enable_early_stopping: boolean;
  patience?: number;
  min_delta?: number;
  hierarchy_weights: number[];
  constraint_enforcement: boolean;
  level_wise_training: boolean;
}

export interface HierarchicalModel {
  id: string;
  name: string;
  base_model: string;
  created_at: string;
  status: 'training' | 'completed' | 'failed' | 'ready';
  training_config: HierarchicalModelTrainingConfig;
  metrics?: HierarchicalModelMetrics;
  metadata: HierarchicalModelMetadata;
}

export interface HierarchicalModelListResponse {
  models: HierarchicalModel[];
  total_count: number;
  page: number;
  page_size: number;
}

export interface ModelSelectionRequest {
  model_id: string;
  description?: string;
}

export interface ModelSelectionResponse {
  success: boolean;
  message: string;
  model_info?: HierarchicalModel;
}

export interface ModelStatsResponse {
  total_models: number;
  completed_models: number;
  training_models: number;
  failed_models: number;
  best_performance?: {
    model_id: string;
    model_name: string;
    hierarchical_f1: number;
    path_accuracy: number;
  };
}

export interface ListModelsParams {
  page?: number;
  page_size?: number;
  status_filter?: string;
  sort_by?: string;
  sort_order?: 'asc' | 'desc';
  search?: string;
}

/**
 * List hierarchical classification models
 */
export const listHierarchicalModels = async (
  params: ListModelsParams = {}
): Promise<HierarchicalModelListResponse> => {
  const searchParams = new URLSearchParams();
  
  if (params.page) searchParams.append('page', params.page.toString());
  if (params.page_size) searchParams.append('page_size', params.page_size.toString());
  if (params.status_filter) searchParams.append('status_filter', params.status_filter);
  if (params.sort_by) searchParams.append('sort_by', params.sort_by);
  if (params.sort_order) searchParams.append('sort_order', params.sort_order);
  if (params.search) searchParams.append('search', params.search);

  const response = await apiClient.get(`/api/hierarchical-models/list?${searchParams.toString()}`);
  return response.data;
};

/**
 * Get detailed information about a specific hierarchical model
 */
export const getHierarchicalModel = async (modelId: string): Promise<HierarchicalModel> => {
  const response = await apiClient.get(`/api/hierarchical-models/${modelId}`);
  return response.data;
};

/**
 * Select a hierarchical model for inference
 */
export const selectHierarchicalModel = async (
  request: ModelSelectionRequest
): Promise<ModelSelectionResponse> => {
  const response = await apiClient.post('/api/hierarchical-models/select', request);
  return response.data;
};

/**
 * Delete a hierarchical model
 */
export const deleteHierarchicalModel = async (modelId: string): Promise<{ success: boolean; message: string }> => {
  const response = await apiClient.delete(`/api/hierarchical-models/${modelId}`);
  return response.data;
};

/**
 * Get summary statistics for hierarchical models
 */
export const getModelStats = async (): Promise<ModelStatsResponse> => {
  const response = await apiClient.get('/api/hierarchical-models/stats/summary');
  return response.data;
};

/**
 * Enhanced training configuration for hierarchical models
 */
export interface EnhancedTrainingRequest {
  // Model Identity
  model_name: string;
  description?: string;
  
  // File and Data Configuration
  file_id: string;
  classification_type: 'hierarchical';
  model_type: 'custom';
  text_column: string;
  label_columns: string[];
  hierarchy_levels: string[];
  
  // Enhanced Training Parameters
  training_params: {
    // Core Parameters
    max_epochs: number;
    batch_size: number;
    learning_rate: number;
    validation_split: number;
    
    // Model Configuration
    base_model: string;
    max_length: number;
    
    // Advanced Hyperparameters
    warmup_steps: number;
    weight_decay: number;
    gradient_accumulation_steps: number;
    
    // Hardware Optimization
    use_unsloth: boolean;
    fp16: boolean;
    gradient_checkpointing: boolean;
    
    // Early Stopping
    enable_early_stopping: boolean;
    patience?: number;
    min_delta?: number;
    
    // Hierarchical Specific
    hierarchy_weights: number[];
    constraint_enforcement: boolean;
    level_wise_training: boolean;
    
    // Existing hierarchical parameters
    constraints?: any;
    validation_rules?: any;
  };
  
  // Dual data support
  dual_data_setup?: boolean;
  classification_file_id?: string;
  training_file_id?: string;
}

/**
 * Start enhanced hierarchical training with comprehensive configuration
 */
export const startEnhancedHierarchicalTraining = async (
  request: EnhancedTrainingRequest
): Promise<{ task_id: string; session_id: string; message: string }> => {
  const response = await apiClient.post('/api/v2/classification/universal/train', request);
  return response.data;
};

/**
 * Save training configuration for later use
 */
export interface SavedTrainingConfig {
  id?: string;
  name: string;
  description?: string;
  config: Partial<EnhancedTrainingRequest>;
  created_at?: string;
  updated_at?: string;
}

/**
 * Save a training configuration
 */
export const saveTrainingConfiguration = async (
  config: Omit<SavedTrainingConfig, 'id' | 'created_at' | 'updated_at'>
): Promise<SavedTrainingConfig> => {
  const response = await apiClient.post('/api/hierarchical-models/configs', config);
  return response.data;
};

/**
 * List saved training configurations
 */
export const listTrainingConfigurations = async (): Promise<SavedTrainingConfig[]> => {
  const response = await apiClient.get('/api/hierarchical-models/configs');
  return response.data;
};

/**
 * Load a saved training configuration
 */
export const loadTrainingConfiguration = async (configId: string): Promise<SavedTrainingConfig> => {
  const response = await apiClient.get(`/api/hierarchical-models/configs/${configId}`);
  return response.data;
};

/**
 * Delete a saved training configuration
 */
export const deleteTrainingConfiguration = async (configId: string): Promise<{ success: boolean }> => {
  const response = await apiClient.delete(`/api/hierarchical-models/configs/${configId}`);
  return response.data;
};

/**
 * Validate training configuration
 */
export interface ValidationResult {
  valid: boolean;
  errors: string[];
  warnings: string[];
  estimated_time?: number;
  estimated_memory?: number;
}

/**
 * Validate a training configuration before starting training
 */
export const validateTrainingConfiguration = async (
  config: Partial<EnhancedTrainingRequest>
): Promise<ValidationResult> => {
  const response = await apiClient.post('/api/hierarchical-models/validate-config', config);
  return response.data;
};

/**
 * Get training recommendations based on data characteristics
 */
export interface TrainingRecommendations {
  recommended_base_model: string;
  recommended_epochs: number;
  recommended_batch_size: number;
  recommended_learning_rate: number;
  reasoning: string[];
  estimated_training_time: number;
}

/**
 * Get training recommendations for hierarchical classification
 */
export const getTrainingRecommendations = async (
  fileId: string,
  hierarchyLevels: number,
  totalSamples: number
): Promise<TrainingRecommendations> => {
  const response = await apiClient.post('/api/hierarchical-models/recommendations', {
    file_id: fileId,
    hierarchy_levels: hierarchyLevels,
    total_samples: totalSamples
  });
  return response.data;
};

/**
 * Export model for download
 */
export const exportHierarchicalModel = async (
  modelId: string,
  format: 'pytorch' | 'onnx' | 'huggingface' = 'pytorch'
): Promise<{ download_url: string; expires_at: string }> => {
  const response = await apiClient.post(`/api/hierarchical-models/${modelId}/export`, { format });
  return response.data;
};

/**
 * Get model performance comparison
 */
export interface ModelComparison {
  models: Array<{
    id: string;
    name: string;
    metrics: HierarchicalModelMetrics;
    training_config: Partial<HierarchicalModelTrainingConfig>;
  }>;
  best_model_id: string;
  comparison_metrics: string[];
}

/**
 * Compare multiple hierarchical models
 */
export const compareHierarchicalModels = async (
  modelIds: string[]
): Promise<ModelComparison> => {
  const response = await apiClient.post('/api/hierarchical-models/compare', { model_ids: modelIds });
  return response.data;
};
