#!/usr/bin/env python3
# backend/migrations/init_hierarchy_configs.py

"""
Migration script to initialize the default hierarchy configuration in the database.
This script should be run after the database schema has been created.
"""

import sys
import os
import logging
from datetime import datetime

# Add the parent directory to the path so we can import from the backend package
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.database import SessionLocal, HierarchyConfig, get_default_hierarchy_config
from app import config

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def init_hierarchy_configs():
    """Initialize the default hierarchy configuration in the database."""
    db = SessionLocal()
    try:
        # Check if a default configuration already exists
        default_config = get_default_hierarchy_config(db)

        if default_config:
            logger.info(f"Default hierarchy configuration already exists: {default_config.name}")
            return

        # Create the default configuration
        default_config = HierarchyConfig(
            name="Default Hierarchy",
            hierarchy_levels=config.DEFAULT_HIERARCHY_LEVELS,
            is_default=True,
            user_id=None,  # No user association for the default configuration
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )

        db.add(default_config)
        db.commit()

        logger.info(f"Created default hierarchy configuration: {default_config.name}")
    except Exception as e:
        db.rollback()
        logger.error(f"Error initializing hierarchy configurations: {e}")
    finally:
        db.close()

if __name__ == "__main__":
    logger.info("Initializing hierarchy configurations...")
    init_hierarchy_configs()
    logger.info("Done.")
