# Multi-Class Classification Examples

This document provides practical examples and use cases for multi-class classification in ClassyWeb.

## Table of Contents

1. [Basic Examples](#basic-examples)
2. [Advanced Use Cases](#advanced-use-cases)
3. [Industry Applications](#industry-applications)
4. [Code Examples](#code-examples)
5. [Best Practices by Use Case](#best-practices-by-use-case)

## Basic Examples

### Example 1: Sentiment Analysis (3 Classes)

**Use Case**: Classify customer reviews into positive, negative, or neutral sentiment.

**Data Structure**:
```csv
review_text,sentiment
"This product is amazing!",positive
"Poor quality, not recommended",negative
"It's okay, nothing special",neutral
"Excellent customer service",positive
"Terrible delivery experience",negative
```

**Recommended Configuration**:
- **Strategy**: Softmax (balanced classes)
- **Model**: DistilBERT (fast and efficient)
- **Epochs**: 3-5
- **Batch Size**: 16-32

**Expected Performance**: 85-95% accuracy with balanced data

### Example 2: Topic Classification (5 Classes)

**Use Case**: Classify news articles into categories.

**Data Structure**:
```csv
article_text,category
"The stock market reached new highs today...",business
"Scientists discover new species in Amazon...",science
"Local team wins championship game...",sports
"New healthcare policy announced...",politics
"Latest smartphone features revealed...",technology
```

**Recommended Configuration**:
- **Strategy**: Softmax or OvR (depending on balance)
- **Model**: BERT Base (better for longer texts)
- **Epochs**: 3-5
- **Batch Size**: 8-16 (longer texts)

**Expected Performance**: 80-90% accuracy

### Example 3: Intent Classification (10 Classes)

**Use Case**: Classify customer service inquiries by intent.

**Data Structure**:
```csv
customer_message,intent
"I want to cancel my subscription",cancel_subscription
"How do I reset my password?",password_reset
"When will my order arrive?",order_status
"I need a refund for my purchase",refund_request
"Can you help me upgrade my plan?",upgrade_plan
```

**Recommended Configuration**:
- **Strategy**: OvR (many classes, potentially imbalanced)
- **Model**: RoBERTa (better understanding)
- **Epochs**: 5-8
- **Batch Size**: 16-32

**Expected Performance**: 75-85% accuracy

## Advanced Use Cases

### Example 4: Document Classification (20+ Classes)

**Use Case**: Classify legal documents into specific categories.

**Challenges**:
- Many classes (20+ categories)
- Potentially imbalanced data
- Long documents
- Domain-specific language

**Recommended Approach**:
```python
# Configuration for many classes
config = {
    "strategy": "ovr",  # Better for many classes
    "model_name": "roberta-base",
    "num_epochs": 8,
    "batch_size": 8,  # Smaller for long documents
    "learning_rate": 1e-5,
    "max_length": 512,
    "class_weight_strategy": "balanced"
}
```

**Best Practices**:
- Use hierarchical labeling if possible
- Implement class weighting for imbalanced data
- Consider document chunking for very long texts
- Use domain-specific pre-trained models if available

### Example 5: Multi-Language Classification

**Use Case**: Classify customer feedback in multiple languages.

**Challenges**:
- Mixed languages in dataset
- Different class distributions per language
- Cultural context differences

**Recommended Approach**:
```python
# Multi-language configuration
config = {
    "model_name": "distilbert-base-multilingual-cased",
    "strategy": "ovr",
    "num_epochs": 5,
    "batch_size": 16,
    "learning_rate": 2e-5,
    "validation_split": 0.2
}
```

**Best Practices**:
- Use multilingual models (mBERT, XLM-R)
- Ensure balanced language representation
- Consider language-specific fine-tuning
- Validate performance per language

### Example 6: Imbalanced Dataset (Rare Classes)

**Use Case**: Classify support tickets where some categories are very rare.

**Data Distribution**:
- General inquiry: 60%
- Technical issue: 25%
- Billing question: 10%
- Account security: 3%
- Feature request: 2%

**Recommended Approach**:
```python
# Imbalanced data configuration
config = {
    "strategy": "ovr",  # Better for imbalanced data
    "model_name": "bert-base-uncased",
    "num_epochs": 10,
    "batch_size": 16,
    "learning_rate": 2e-5,
    "class_weight_strategy": "balanced",
    "enable_early_stopping": True,
    "patience": 3
}
```

**Best Practices**:
- Use One-vs-Rest strategy
- Apply class weighting
- Consider data augmentation for rare classes
- Use stratified sampling
- Monitor per-class metrics carefully

## Industry Applications

### E-commerce

#### Product Categorization
```csv
product_description,category
"Wireless Bluetooth headphones with noise cancellation",electronics
"Organic cotton t-shirt, size medium",clothing
"Stainless steel kitchen knife set",home_kitchen
"Mystery thriller novel by bestselling author",books
```

**Configuration**:
- **Strategy**: Softmax (usually balanced)
- **Model**: DistilBERT (fast for large catalogs)
- **Focus**: Speed and scalability

#### Review Classification
```csv
review_text,rating_category
"Product broke after one week",1_star
"Good value for money",4_star
"Exceeded my expectations!",5_star
"Average quality, nothing special",3_star
```

**Configuration**:
- **Strategy**: Softmax (ordinal classes)
- **Model**: BERT Base (better understanding)
- **Focus**: Accuracy and nuanced understanding

### Healthcare

#### Medical Text Classification
```csv
clinical_note,department
"Patient presents with chest pain and shortness of breath",cardiology
"Routine eye examination, no abnormalities found",ophthalmology
"Skin lesion biopsy results pending",dermatology
```

**Special Considerations**:
- High accuracy requirements
- Regulatory compliance
- Privacy and security
- Domain expertise needed

### Finance

#### Transaction Categorization
```csv
transaction_description,category
"AMAZON.COM PURCHASE",shopping
"SALARY DEPOSIT COMPANY XYZ",income
"MORTGAGE PAYMENT BANK ABC",housing
"RESTAURANT DOWNTOWN",dining
```

**Configuration**:
- **Strategy**: OvR (many categories)
- **Model**: RoBERTa (better pattern recognition)
- **Focus**: Precision and consistency

### Media & Content

#### Content Moderation
```csv
user_comment,moderation_action
"Great article, thanks for sharing!",approve
"This is completely wrong and misleading",review
"[Inappropriate content removed]",reject
```

**Special Considerations**:
- Real-time processing requirements
- High precision needed
- Continuous model updates
- Bias and fairness concerns

## Code Examples

### Complete Training Pipeline

```python
import pandas as pd
from classyweb import ClassyWebClient

# Initialize client
client = ClassyWebClient(api_key="your-api-key")

# 1. Upload and prepare data
data = pd.read_csv("customer_reviews.csv")
file_response = client.upload_file(data, "customer_reviews.csv")

# 2. Configure training
training_config = {
    "file_id": file_response["file_id"],
    "text_column": "review_text",
    "label_column": "sentiment",
    "classification_type": "multi-class",
    "config": {
        "model_name": "distilbert-base-uncased",
        "strategy": "softmax",
        "num_epochs": 3,
        "batch_size": 16,
        "learning_rate": 2e-5,
        "validation_split": 0.2
    }
}

# 3. Start training
training_response = client.start_training(training_config)
task_id = training_response["task_id"]

# 4. Monitor training progress
while True:
    status = client.get_task_status(task_id)
    print(f"Training progress: {status['progress']}%")
    
    if status["status"] == "SUCCESS":
        model_id = status["result"]["model_id"]
        print(f"Training completed! Model ID: {model_id}")
        break
    elif status["status"] == "FAILURE":
        print(f"Training failed: {status['message']}")
        break
    
    time.sleep(10)

# 5. Classify new data
new_data = pd.read_csv("new_reviews.csv")
new_file_response = client.upload_file(new_data, "new_reviews.csv")

classification_config = {
    "model_id": model_id,
    "file_id": new_file_response["file_id"],
    "text_column": "review_text",
    "classification_type": "multi-class"
}

classification_response = client.start_classification(classification_config)
results = classification_response["predictions"]

# 6. Analyze results
for prediction in results[:5]:
    print(f"Text: {prediction['text']}")
    print(f"Predicted: {prediction['predicted_class']}")
    print(f"Confidence: {prediction['confidence']:.2f}")
    print("---")
```

### Batch Processing Example

```python
# Process large datasets in batches
def process_large_dataset(client, model_id, data_file, batch_size=1000):
    """Process large datasets in manageable batches"""
    
    # Read data in chunks
    chunk_results = []
    
    for chunk in pd.read_csv(data_file, chunksize=batch_size):
        # Upload chunk
        file_response = client.upload_file(chunk, f"chunk_{len(chunk_results)}.csv")
        
        # Classify chunk
        classification_config = {
            "model_id": model_id,
            "file_id": file_response["file_id"],
            "text_column": "text",
            "classification_type": "multi-class"
        }
        
        result = client.start_classification(classification_config)
        chunk_results.append(result["predictions"])
        
        print(f"Processed chunk {len(chunk_results)}")
    
    # Combine results
    all_predictions = []
    for chunk_result in chunk_results:
        all_predictions.extend(chunk_result)
    
    return all_predictions
```

### Real-time Classification API

```python
from flask import Flask, request, jsonify

app = Flask(__name__)
client = ClassyWebClient(api_key="your-api-key")
model_id = "your-trained-model-id"

@app.route('/classify', methods=['POST'])
def classify_text():
    """Real-time text classification endpoint"""
    
    try:
        # Get text from request
        data = request.get_json()
        text = data.get('text', '')
        
        if not text:
            return jsonify({"error": "No text provided"}), 400
        
        # Create temporary file for single text
        temp_data = pd.DataFrame([{"text": text}])
        file_response = client.upload_file(temp_data, "temp_classification.csv")
        
        # Classify
        classification_config = {
            "model_id": model_id,
            "file_id": file_response["file_id"],
            "text_column": "text",
            "classification_type": "multi-class"
        }
        
        result = client.start_classification(classification_config)
        prediction = result["predictions"][0]
        
        return jsonify({
            "predicted_class": prediction["predicted_class"],
            "confidence": prediction["confidence"],
            "all_probabilities": prediction.get("probabilities", {})
        })
        
    except Exception as e:
        return jsonify({"error": str(e)}), 500

if __name__ == '__main__':
    app.run(debug=True)
```

## Best Practices by Use Case

### High-Volume Applications

**Characteristics**: Millions of classifications per day
**Recommendations**:
- Use DistilBERT for speed
- Implement caching for common inputs
- Use batch processing
- Deploy with auto-scaling
- Monitor performance metrics

### High-Accuracy Applications

**Characteristics**: Critical decisions, low error tolerance
**Recommendations**:
- Use RoBERTa or BERT Large
- Implement ensemble methods
- Use confidence thresholds
- Human review for low-confidence predictions
- Extensive validation testing

### Real-time Applications

**Characteristics**: Low latency requirements (< 100ms)
**Recommendations**:
- Use quantized models
- Implement model caching
- Use edge deployment
- Optimize preprocessing
- Monitor response times

### Multi-tenant Applications

**Characteristics**: Multiple customers, different domains
**Recommendations**:
- Train separate models per tenant
- Use transfer learning
- Implement model versioning
- Provide customization options
- Monitor per-tenant performance

### Evolving Domains

**Characteristics**: New classes appear over time
**Recommendations**:
- Plan for model retraining
- Implement active learning
- Use confidence-based routing
- Monitor for concept drift
- Maintain training pipelines

---

For more advanced examples and custom implementations, see the [Advanced Techniques Guide](./advanced-techniques.md).
