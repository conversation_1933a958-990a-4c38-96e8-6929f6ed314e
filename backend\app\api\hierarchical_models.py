"""Hierarchical Model Management API for ClassyWeb ML Platform.

This module provides specialized endpoints for managing hierarchical classification models,
including model listing, metadata retrieval, model selection, and performance tracking.
"""

import logging
import traceback
from typing import List, Dict, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from sqlalchemy import desc, asc, and_, or_
from pydantic import BaseModel, Field
from datetime import datetime

from ..database import (
    get_db, TrainingSession, ModelPerformance, ClassificationConfig,
    TrainingStatusEnum, ClassificationTypeEnum, TrainingMethodEnum
)
from ..models.auth import User
from ..auth import get_current_user

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/api/hierarchical-models", tags=["hierarchical-models"])


class HierarchicalModelMetrics(BaseModel):
    """Hierarchical-specific model metrics."""
    hierarchical_f1: float = Field(..., description="Hierarchical F1 score")
    path_accuracy: float = Field(..., description="Path accuracy score")
    level_wise_accuracy: List[float] = Field(..., description="Accuracy for each hierarchy level")
    training_time: int = Field(..., description="Training time in minutes")
    validation_loss: float = Field(..., description="Final validation loss")


class HierarchicalModelMetadata(BaseModel):
    """Model metadata specific to hierarchical classification."""
    hierarchy_levels: int = Field(..., description="Number of hierarchy levels")
    total_samples: int = Field(..., description="Total training samples")
    model_size: float = Field(..., description="Model size in MB")
    description: Optional[str] = Field(None, description="Model description")
    constraints_used: bool = Field(False, description="Whether hierarchical constraints were enforced")
    level_wise_training: bool = Field(False, description="Whether level-wise training was used")


class HierarchicalModelTrainingConfig(BaseModel):
    """Training configuration for hierarchical models."""
    num_epochs: int
    batch_size: int
    learning_rate: float
    validation_split: float
    use_unsloth: bool
    base_model: str
    warmup_steps: int
    weight_decay: float
    gradient_accumulation_steps: int
    fp16: bool
    gradient_checkpointing: bool
    enable_early_stopping: bool
    patience: Optional[int] = None
    min_delta: Optional[float] = None
    hierarchy_weights: List[float] = Field(default_factory=list)
    constraint_enforcement: bool = False
    level_wise_training: bool = False


class HierarchicalModelResponse(BaseModel):
    """Response model for hierarchical classification models."""
    id: str
    name: str
    base_model: str
    created_at: str
    status: str
    training_config: HierarchicalModelTrainingConfig
    metrics: Optional[HierarchicalModelMetrics] = None
    metadata: HierarchicalModelMetadata


class HierarchicalModelListResponse(BaseModel):
    """Response for listing hierarchical models."""
    models: List[HierarchicalModelResponse]
    total_count: int
    page: int
    page_size: int


class ModelSelectionRequest(BaseModel):
    """Request for selecting a model for inference."""
    model_id: str
    description: Optional[str] = None


class ModelSelectionResponse(BaseModel):
    """Response for model selection."""
    success: bool
    message: str
    model_info: Optional[HierarchicalModelResponse] = None


@router.get("/list", response_model=HierarchicalModelListResponse)
async def list_hierarchical_models(
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(10, ge=1, le=50, description="Items per page"),
    status_filter: Optional[str] = Query(None, description="Filter by status"),
    sort_by: str = Query("created_at", description="Sort field"),
    sort_order: str = Query("desc", description="Sort order (asc/desc)"),
    search: Optional[str] = Query(None, description="Search term"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """List hierarchical classification models for the current user."""
    try:
        # Base query for hierarchical models
        query = db.query(TrainingSession).filter(
            and_(
                TrainingSession.user_id == current_user.id,
                TrainingSession.classification_type == ClassificationTypeEnum.HIERARCHICAL,
                TrainingSession.training_method == TrainingMethodEnum.CUSTOM
            )
        )

        # Apply status filter
        if status_filter:
            if status_filter == "completed":
                query = query.filter(TrainingSession.status == TrainingStatusEnum.COMPLETED)
            elif status_filter == "training":
                query = query.filter(TrainingSession.status == TrainingStatusEnum.RUNNING)
            elif status_filter == "failed":
                query = query.filter(TrainingSession.status == TrainingStatusEnum.FAILED)

        # Apply search filter
        if search:
            search_term = f"%{search}%"
            query = query.filter(
                or_(
                    TrainingSession.session_name.ilike(search_term),
                    TrainingSession.training_config['base_model'].astext.ilike(search_term)
                )
            )

        # Apply sorting
        sort_column = getattr(TrainingSession, sort_by, TrainingSession.created_at)
        if sort_order.lower() == "desc":
            query = query.order_by(desc(sort_column))
        else:
            query = query.order_by(asc(sort_column))

        # Get total count
        total_count = query.count()

        # Apply pagination
        offset = (page - 1) * page_size
        models = query.offset(offset).limit(page_size).all()

        # Convert to response format
        model_responses = []
        for model in models:
            try:
                # Extract training config
                config_data = model.training_config or {}
                training_config = HierarchicalModelTrainingConfig(
                    num_epochs=config_data.get('max_epochs', 3),
                    batch_size=config_data.get('batch_size', 16),
                    learning_rate=config_data.get('learning_rate', 2e-5),
                    validation_split=config_data.get('validation_split', 0.2),
                    use_unsloth=config_data.get('use_unsloth', True),
                    base_model=config_data.get('base_model', 'distilbert-base-uncased'),
                    warmup_steps=config_data.get('warmup_steps', 500),
                    weight_decay=config_data.get('weight_decay', 0.01),
                    gradient_accumulation_steps=config_data.get('gradient_accumulation_steps', 1),
                    fp16=config_data.get('fp16', True),
                    gradient_checkpointing=config_data.get('gradient_checkpointing', False),
                    enable_early_stopping=config_data.get('enable_early_stopping', True),
                    patience=config_data.get('patience'),
                    min_delta=config_data.get('min_delta'),
                    hierarchy_weights=config_data.get('hierarchy_weights', []),
                    constraint_enforcement=config_data.get('constraint_enforcement', False),
                    level_wise_training=config_data.get('level_wise_training', False)
                )

                # Extract metrics if available
                metrics = None
                if model.final_metrics and model.status == TrainingStatusEnum.COMPLETED:
                    metrics_data = model.final_metrics
                    metrics = HierarchicalModelMetrics(
                        hierarchical_f1=metrics_data.get('hierarchical_f1', 0.0),
                        path_accuracy=metrics_data.get('path_accuracy', 0.0),
                        level_wise_accuracy=metrics_data.get('level_wise_accuracy', []),
                        training_time=metrics_data.get('training_time', 0),
                        validation_loss=metrics_data.get('validation_loss', 0.0)
                    )

                # Extract metadata
                metadata = HierarchicalModelMetadata(
                    hierarchy_levels=len(config_data.get('hierarchy_levels', [])),
                    total_samples=config_data.get('total_samples', 0),
                    model_size=config_data.get('model_size', 0.0),
                    description=config_data.get('description'),
                    constraints_used=config_data.get('constraint_enforcement', False),
                    level_wise_training=config_data.get('level_wise_training', False)
                )

                model_response = HierarchicalModelResponse(
                    id=model.id,
                    name=model.session_name or f"Model {model.id[:8]}",
                    base_model=config_data.get('base_model', 'distilbert-base-uncased'),
                    created_at=model.created_at.isoformat(),
                    status=model.status.value.lower(),
                    training_config=training_config,
                    metrics=metrics,
                    metadata=metadata
                )
                model_responses.append(model_response)

            except Exception as e:
                logger.warning(f"Error processing model {model.id}: {e}")
                continue

        return HierarchicalModelListResponse(
            models=model_responses,
            total_count=total_count,
            page=page,
            page_size=page_size
        )

    except Exception as e:
        logger.error(f"Error listing hierarchical models: {e}")
        logger.error(traceback.format_exc())
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve models"
        )


@router.get("/{model_id}", response_model=HierarchicalModelResponse)
async def get_hierarchical_model(
    model_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get detailed information about a specific hierarchical model."""
    try:
        model = db.query(TrainingSession).filter(
            and_(
                TrainingSession.id == model_id,
                TrainingSession.user_id == current_user.id,
                TrainingSession.classification_type == ClassificationTypeEnum.HIERARCHICAL
            )
        ).first()

        if not model:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Model not found"
            )

        # Convert to response format (similar to list endpoint)
        config_data = model.training_config or {}
        training_config = HierarchicalModelTrainingConfig(
            num_epochs=config_data.get('max_epochs', 3),
            batch_size=config_data.get('batch_size', 16),
            learning_rate=config_data.get('learning_rate', 2e-5),
            validation_split=config_data.get('validation_split', 0.2),
            use_unsloth=config_data.get('use_unsloth', True),
            base_model=config_data.get('base_model', 'distilbert-base-uncased'),
            warmup_steps=config_data.get('warmup_steps', 500),
            weight_decay=config_data.get('weight_decay', 0.01),
            gradient_accumulation_steps=config_data.get('gradient_accumulation_steps', 1),
            fp16=config_data.get('fp16', True),
            gradient_checkpointing=config_data.get('gradient_checkpointing', False),
            enable_early_stopping=config_data.get('enable_early_stopping', True),
            patience=config_data.get('patience'),
            min_delta=config_data.get('min_delta'),
            hierarchy_weights=config_data.get('hierarchy_weights', []),
            constraint_enforcement=config_data.get('constraint_enforcement', False),
            level_wise_training=config_data.get('level_wise_training', False)
        )

        # Extract metrics if available
        metrics = None
        if model.final_metrics and model.status == TrainingStatusEnum.COMPLETED:
            metrics_data = model.final_metrics
            metrics = HierarchicalModelMetrics(
                hierarchical_f1=metrics_data.get('hierarchical_f1', 0.0),
                path_accuracy=metrics_data.get('path_accuracy', 0.0),
                level_wise_accuracy=metrics_data.get('level_wise_accuracy', []),
                training_time=metrics_data.get('training_time', 0),
                validation_loss=metrics_data.get('validation_loss', 0.0)
            )

        # Extract metadata
        metadata = HierarchicalModelMetadata(
            hierarchy_levels=len(config_data.get('hierarchy_levels', [])),
            total_samples=config_data.get('total_samples', 0),
            model_size=config_data.get('model_size', 0.0),
            description=config_data.get('description'),
            constraints_used=config_data.get('constraint_enforcement', False),
            level_wise_training=config_data.get('level_wise_training', False)
        )

        return HierarchicalModelResponse(
            id=model.id,
            name=model.session_name or f"Model {model.id[:8]}",
            base_model=config_data.get('base_model', 'distilbert-base-uncased'),
            created_at=model.created_at.isoformat(),
            status=model.status.value.lower(),
            training_config=training_config,
            metrics=metrics,
            metadata=metadata
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving model {model_id}: {e}")
        logger.error(traceback.format_exc())
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve model"
        )


@router.post("/select", response_model=ModelSelectionResponse)
async def select_model_for_inference(
    request: ModelSelectionRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Select a hierarchical model for inference."""
    try:
        model = db.query(TrainingSession).filter(
            and_(
                TrainingSession.id == request.model_id,
                TrainingSession.user_id == current_user.id,
                TrainingSession.classification_type == ClassificationTypeEnum.HIERARCHICAL,
                TrainingSession.status == TrainingStatusEnum.COMPLETED
            )
        ).first()

        if not model:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Model not found or not ready for inference"
            )

        # Convert to response format
        config_data = model.training_config or {}
        training_config = HierarchicalModelTrainingConfig(
            num_epochs=config_data.get('max_epochs', 3),
            batch_size=config_data.get('batch_size', 16),
            learning_rate=config_data.get('learning_rate', 2e-5),
            validation_split=config_data.get('validation_split', 0.2),
            use_unsloth=config_data.get('use_unsloth', True),
            base_model=config_data.get('base_model', 'distilbert-base-uncased'),
            warmup_steps=config_data.get('warmup_steps', 500),
            weight_decay=config_data.get('weight_decay', 0.01),
            gradient_accumulation_steps=config_data.get('gradient_accumulation_steps', 1),
            fp16=config_data.get('fp16', True),
            gradient_checkpointing=config_data.get('gradient_checkpointing', False),
            enable_early_stopping=config_data.get('enable_early_stopping', True),
            patience=config_data.get('patience'),
            min_delta=config_data.get('min_delta'),
            hierarchy_weights=config_data.get('hierarchy_weights', []),
            constraint_enforcement=config_data.get('constraint_enforcement', False),
            level_wise_training=config_data.get('level_wise_training', False)
        )

        # Extract metrics
        metrics = None
        if model.final_metrics:
            metrics_data = model.final_metrics
            metrics = HierarchicalModelMetrics(
                hierarchical_f1=metrics_data.get('hierarchical_f1', 0.0),
                path_accuracy=metrics_data.get('path_accuracy', 0.0),
                level_wise_accuracy=metrics_data.get('level_wise_accuracy', []),
                training_time=metrics_data.get('training_time', 0),
                validation_loss=metrics_data.get('validation_loss', 0.0)
            )

        # Extract metadata
        metadata = HierarchicalModelMetadata(
            hierarchy_levels=len(config_data.get('hierarchy_levels', [])),
            total_samples=config_data.get('total_samples', 0),
            model_size=config_data.get('model_size', 0.0),
            description=config_data.get('description'),
            constraints_used=config_data.get('constraint_enforcement', False),
            level_wise_training=config_data.get('level_wise_training', False)
        )

        model_info = HierarchicalModelResponse(
            id=model.id,
            name=model.session_name or f"Model {model.id[:8]}",
            base_model=config_data.get('base_model', 'distilbert-base-uncased'),
            created_at=model.created_at.isoformat(),
            status=model.status.value.lower(),
            training_config=training_config,
            metrics=metrics,
            metadata=metadata
        )

        return ModelSelectionResponse(
            success=True,
            message=f"Model '{model.session_name or model.id[:8]}' selected for inference",
            model_info=model_info
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error selecting model {request.model_id}: {e}")
        logger.error(traceback.format_exc())
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to select model"
        )


@router.delete("/{model_id}")
async def delete_hierarchical_model(
    model_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Delete a hierarchical classification model."""
    try:
        model = db.query(TrainingSession).filter(
            and_(
                TrainingSession.id == model_id,
                TrainingSession.user_id == current_user.id,
                TrainingSession.classification_type == ClassificationTypeEnum.HIERARCHICAL
            )
        ).first()

        if not model:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Model not found"
            )

        # Delete associated model files if they exist
        if model.model_path:
            try:
                import os
                if os.path.exists(model.model_path):
                    import shutil
                    shutil.rmtree(model.model_path)
                    logger.info(f"Deleted model files at {model.model_path}")
            except Exception as e:
                logger.warning(f"Failed to delete model files: {e}")

        # Delete the database record
        db.delete(model)
        db.commit()

        return {"success": True, "message": f"Model {model_id} deleted successfully"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting model {model_id}: {e}")
        logger.error(traceback.format_exc())
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete model"
        )


@router.get("/stats/summary")
async def get_model_stats_summary(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get summary statistics for user's hierarchical models."""
    try:
        # Count models by status
        total_models = db.query(TrainingSession).filter(
            and_(
                TrainingSession.user_id == current_user.id,
                TrainingSession.classification_type == ClassificationTypeEnum.HIERARCHICAL
            )
        ).count()

        completed_models = db.query(TrainingSession).filter(
            and_(
                TrainingSession.user_id == current_user.id,
                TrainingSession.classification_type == ClassificationTypeEnum.HIERARCHICAL,
                TrainingSession.status == TrainingStatusEnum.COMPLETED
            )
        ).count()

        training_models = db.query(TrainingSession).filter(
            and_(
                TrainingSession.user_id == current_user.id,
                TrainingSession.classification_type == ClassificationTypeEnum.HIERARCHICAL,
                TrainingSession.status == TrainingStatusEnum.RUNNING
            )
        ).count()

        failed_models = db.query(TrainingSession).filter(
            and_(
                TrainingSession.user_id == current_user.id,
                TrainingSession.classification_type == ClassificationTypeEnum.HIERARCHICAL,
                TrainingSession.status == TrainingStatusEnum.FAILED
            )
        ).count()

        # Get best performing model
        best_model = db.query(TrainingSession).filter(
            and_(
                TrainingSession.user_id == current_user.id,
                TrainingSession.classification_type == ClassificationTypeEnum.HIERARCHICAL,
                TrainingSession.status == TrainingStatusEnum.COMPLETED,
                TrainingSession.final_metrics.isnot(None)
            )
        ).order_by(
            desc(TrainingSession.final_metrics['hierarchical_f1'].astext.cast(db.Float))
        ).first()

        best_performance = None
        if best_model and best_model.final_metrics:
            best_performance = {
                "model_id": best_model.id,
                "model_name": best_model.session_name or f"Model {best_model.id[:8]}",
                "hierarchical_f1": best_model.final_metrics.get('hierarchical_f1', 0.0),
                "path_accuracy": best_model.final_metrics.get('path_accuracy', 0.0)
            }

        return {
            "total_models": total_models,
            "completed_models": completed_models,
            "training_models": training_models,
            "failed_models": failed_models,
            "best_performance": best_performance
        }

    except Exception as e:
        logger.error(f"Error getting model stats: {e}")
        logger.error(traceback.format_exc())
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve model statistics"
        )
