# SQLAlchemy Session Fix for LLM Classification

## 🐛 Problem Description

The LLM classification endpoint was failing with the following error:

```
sqlalchemy.orm.exc.DetachedInstanceError: Instance <File at 0x216cc4587c0> is not bound to a Session; attribute refresh operation cannot proceed
```

This error occurred when trying to access database object attributes (like `db_file.file_path`) in background tasks that run outside the original database session context.

## 🔍 Root Cause

The issue was in the `v2_start_llm_classification` function in `backend/app/api/classification_v2.py`. The code was:

1. Fetching a `File` object from the database in the main request handler
2. Passing that object to a background task
3. Trying to access the object's attributes in the background task

When the background task tried to access `db_file.file_path`, SQLAlchemy couldn't load the attribute because the object was no longer bound to an active session.

## ✅ Solution

The fix involves re-fetching the database object within the background task's own database session:

### Before (Problematic Code):
```python
# In main request handler
db_file = get_file(db, req.file_id)

async def process_task(task_id: str):
    with SessionLocal() as bg_db:
        # This fails - db_file is from a different session!
        df = utils.load_data(file_path=db_file.file_path, original_filename=req.original_filename)
```

### After (Fixed Code):
```python
# In main request handler
db_file = get_file(db, req.file_id)  # Still needed for validation

async def process_task(task_id: str):
    with SessionLocal() as bg_db:
        # Re-fetch the file within this session
        bg_file = get_file(bg_db, req.file_id)
        if not bg_file:
            raise ValueError(f"File not found: {req.file_id}")
        
        # Now this works - bg_file is bound to the current session
        df = utils.load_data(file_path=bg_file.file_path, original_filename=req.original_filename)
```

## 📝 Changes Made

### 1. Fixed `backend/app/api/classification_v2.py`

- **Line 1067-1072**: Added re-fetching of file object within background task session
- **Line 13-16**: Added missing imports (`uuid`, `os`)

### 2. Added Test Scripts

- **`test_session_fix.py`**: Simple test to verify the fix works
- **`backend/test_llm_classification_fix.py`**: Comprehensive test for the LLM classification fix

## 🧪 Testing the Fix

### Quick Test:
```bash
python test_session_fix.py
```

### Comprehensive Test:
```bash
cd backend
python test_llm_classification_fix.py
```

### Manual Test:
1. Start the backend server:
   ```bash
   cd backend
   source classyweb_env/bin/activate  # or classyweb_env\Scripts\activate on Windows
   python -m uvicorn app.main:app --reload
   ```

2. Upload a CSV file with text and label columns

3. Start an LLM classification task - it should no longer crash with DetachedInstanceError

## 🔧 Technical Details

### Why This Happens
SQLAlchemy uses lazy loading for relationships and some attributes. When an object is accessed outside its original session, SQLAlchemy tries to load missing data but can't because there's no active session.

### Best Practices to Avoid This Issue
1. **Always re-fetch objects in background tasks**: Don't pass database objects between sessions
2. **Use session-per-task pattern**: Each background task should have its own database session
3. **Eager load data if needed**: Use `joinedload()` or similar if you need to pass data between sessions
4. **Consider using DTOs**: Pass simple data objects instead of ORM objects between contexts

### Other Potential Issues
This pattern should be checked in other background tasks throughout the codebase. Common places to look:
- Training pipeline background tasks
- File processing tasks
- Any async functions that access database objects

## 🎯 Verification

The fix is working correctly if:
- ✅ LLM classification tasks start without DetachedInstanceError
- ✅ Background tasks can access file paths and other database attributes
- ✅ The task status updates correctly during processing
- ✅ Results are saved properly when classification completes

## 📚 Related Documentation

- [SQLAlchemy Session Basics](https://docs.sqlalchemy.org/en/20/orm/session_basics.html)
- [Working with Sessions](https://docs.sqlalchemy.org/en/20/orm/session_transaction.html)
- [DetachedInstanceError](https://docs.sqlalchemy.org/en/20/errors.html#error-bhk3)

---

**Status**: ✅ **FIXED** - The LLM classification should now work without session errors.
