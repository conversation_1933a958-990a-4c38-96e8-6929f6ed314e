"""Enterprise API endpoints for ClassyWeb Universal Platform."""

import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timezone, timedelta
from fastapi import APIRouter, Depends, HTTPException, status, Body, Query
from sqlalchemy.orm import Session
from pydantic import BaseModel

from ..database import get_db
from ..auth import get_current_user
from ..models.auth import User
from ..enterprise.tenant_manager import tenant_manager, TenantTier
from ..enterprise.analytics_manager import enterprise_analytics_manager, AnalyticsQuery, AnalyticsMetric, ReportType
from ..enterprise.security_manager import enterprise_security_manager

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/enterprise", tags=["enterprise"])


# --- Request/Response Models ---

class CreateTenantRequest(BaseModel):
    name: str
    tier: str  # free, professional, enterprise, custom
    settings: Optional[Dict[str, Any]] = None


class TenantResponse(BaseModel):
    id: str
    name: str
    tier: str
    is_active: bool
    created_at: str
    user_count: int
    quota_usage: Dict[str, Any]


class AnalyticsRequest(BaseModel):
    metric_types: List[str]  # usage, performance, accuracy, cost, user_behavior, resource_utilization
    start_date: str  # ISO format
    end_date: str  # ISO format
    granularity: str = "daily"  # hourly, daily, weekly, monthly
    filters: Optional[Dict[str, Any]] = None
    group_by: Optional[List[str]] = None


class SecurityReportRequest(BaseModel):
    start_date: str
    end_date: str
    include_details: bool = False


# --- Tenant Management Endpoints ---

@router.post("/tenants", response_model=Dict[str, Any])
async def create_tenant(
    request: CreateTenantRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Create a new tenant (admin only)."""
    try:
        # TODO: Add admin permission check
        
        # Validate tier
        try:
            tier = TenantTier(request.tier)
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid tier: {request.tier}"
            )
        
        # Create tenant
        tenant_id = await tenant_manager.create_tenant(
            name=request.name,
            tier=tier,
            admin_user_id=current_user.id,
            settings=request.settings
        )
        
        return {
            "tenant_id": tenant_id,
            "message": f"Tenant '{request.name}' created successfully"
        }
        
    except Exception as e:
        logger.error(f"Error creating tenant: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create tenant"
        )


@router.get("/tenants/current", response_model=TenantResponse)
async def get_current_tenant(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get current user's tenant information."""
    try:
        tenant = await tenant_manager.get_user_tenant(current_user.id)
        if not tenant:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="No tenant found for current user"
            )
        
        # Get analytics
        analytics = await tenant_manager.get_tenant_analytics(tenant.id)
        
        return TenantResponse(
            id=tenant.id,
            name=tenant.name,
            tier=tenant.tier.value,
            is_active=tenant.is_active,
            created_at=tenant.created_at.isoformat(),
            user_count=analytics.get("user_count", 0),
            quota_usage=analytics.get("quota_usage", {})
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting current tenant: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get tenant information"
        )


@router.post("/tenants/{tenant_id}/users/{user_id}")
async def add_user_to_tenant(
    tenant_id: str,
    user_id: int,
    role: str = "member",
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Add a user to a tenant (admin only)."""
    try:
        # TODO: Add admin permission check for the tenant
        
        success = await tenant_manager.add_user_to_tenant(tenant_id, user_id, role)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Failed to add user to tenant"
            )
        
        return {"message": f"User {user_id} added to tenant {tenant_id} with role {role}"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error adding user to tenant: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to add user to tenant"
        )


# --- Analytics Endpoints ---

@router.post("/analytics/report")
async def generate_analytics_report(
    request: AnalyticsRequest,
    report_type: ReportType = Query(ReportType.DASHBOARD),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Generate analytics report for current tenant."""
    try:
        # Get user's tenant
        tenant = await tenant_manager.get_user_tenant(current_user.id)
        if not tenant:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="No tenant found for current user"
            )
        
        # Parse dates
        start_date = datetime.fromisoformat(request.start_date.replace('Z', '+00:00'))
        end_date = datetime.fromisoformat(request.end_date.replace('Z', '+00:00'))
        
        # Convert metric types
        metric_types = []
        for metric_str in request.metric_types:
            try:
                metric_types.append(AnalyticsMetric(metric_str))
            except ValueError:
                logger.warning(f"Invalid metric type: {metric_str}")
        
        # Create analytics query
        query = AnalyticsQuery(
            tenant_id=tenant.id,
            metric_types=metric_types,
            start_date=start_date,
            end_date=end_date,
            granularity=request.granularity,
            filters=request.filters,
            group_by=request.group_by
        )
        
        # Generate report
        result = await enterprise_analytics_manager.generate_analytics_report(query, report_type)
        
        return {
            "report": result.data,
            "metadata": result.metadata,
            "generated_at": result.generated_at.isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error generating analytics report: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to generate analytics report"
        )


@router.get("/analytics/real-time")
async def get_real_time_metrics(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get real-time metrics for current tenant."""
    try:
        # Get user's tenant
        tenant = await tenant_manager.get_user_tenant(current_user.id)
        if not tenant:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="No tenant found for current user"
            )
        
        # Get real-time metrics
        metrics = await enterprise_analytics_manager.get_real_time_metrics(tenant.id)
        
        return metrics
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting real-time metrics: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get real-time metrics"
        )


# --- Security Endpoints ---

@router.post("/security/api-keys")
async def generate_api_key(
    permissions: List[str] = Body(...),
    expires_in_days: int = Body(365),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Generate a new API key for the current user."""
    try:
        # Get user's tenant
        tenant = await tenant_manager.get_user_tenant(current_user.id)
        if not tenant:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="No tenant found for current user"
            )
        
        # Generate API key
        api_key_data = await enterprise_security_manager.generate_api_key(
            tenant_id=tenant.id,
            user_id=current_user.id,
            permissions=permissions,
            expires_in_days=expires_in_days
        )
        
        return api_key_data
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error generating API key: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to generate API key"
        )


@router.post("/security/reports")
async def generate_security_report(
    request: SecurityReportRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Generate security report for current tenant."""
    try:
        # Get user's tenant
        tenant = await tenant_manager.get_user_tenant(current_user.id)
        if not tenant:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="No tenant found for current user"
            )
        
        # Parse dates
        start_date = datetime.fromisoformat(request.start_date.replace('Z', '+00:00'))
        end_date = datetime.fromisoformat(request.end_date.replace('Z', '+00:00'))
        
        # Generate security report
        report = await enterprise_security_manager.get_security_report(
            tenant_id=tenant.id,
            start_date=start_date,
            end_date=end_date
        )
        
        return report
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error generating security report: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to generate security report"
        )
