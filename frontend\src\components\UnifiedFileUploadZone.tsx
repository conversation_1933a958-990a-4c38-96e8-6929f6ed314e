/**
 * UnifiedFileUploadZone.tsx
 * 
 * Enhanced file upload component that integrates with UnifiedDataManager
 * to eliminate redundant uploads and provide smart data reuse capabilities.
 */

import React, { useState, useCallback, useRef, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Progress } from "@/components/ui/progress";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import {
  Upload,
  File,
  CheckCircle2,
  AlertCircle,
  Info,
  Trash2,
  RefreshCw,
  Database,
  Brain,
  Target,
  Clock,
  FileText,
  Zap
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";

// Import services
import {
  unifiedDataManager,
  DataPurpose,
  UnifiedDataUpload,
  DataPurposeSuggestion
} from "@/services/unifiedDataManager";
import { UploadedFile, FileUploadProgress } from "@/services/fileUploadApi";
import { unifiedProgressMonitor, ProgressUtils } from "@/services/unifiedProgressMonitor";

interface UnifiedFileUploadZoneProps {
  onFileSelected?: (fileId: string, fileInfo: UploadedFile, purposes: DataPurpose[]) => void;
  onFileRemoved?: () => void;
  onContinue?: () => void;
  acceptedTypes?: string[];
  maxSize?: number;
  className?: string;
  showContinueButton?: boolean;
  requiredPurposes?: DataPurpose[];
  suggestedPurposes?: DataPurpose[];
  allowMultiplePurposes?: boolean;
  showFileReuse?: boolean;
  title?: string;
  description?: string;
}

export const UnifiedFileUploadZone: React.FC<UnifiedFileUploadZoneProps> = ({
  onFileSelected,
  onFileRemoved,
  onContinue,
  acceptedTypes = ['.csv', '.json', '.xlsx', '.xls', '.tsv'],
  maxSize = 100,
  className = '',
  showContinueButton = true,
  requiredPurposes = ['analysis'],
  suggestedPurposes = [],
  allowMultiplePurposes = true,
  showFileReuse = true,
  title = "Upload Data File",
  description = "Upload your data file or reuse a previously uploaded file"
}) => {
  // State management
  const [isDragOver, setIsDragOver] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [selectedFileId, setSelectedFileId] = useState<string | null>(null);
  const [selectedPurposes, setSelectedPurposes] = useState<DataPurpose[]>(requiredPurposes);
  const [suggestions, setSuggestions] = useState<DataPurposeSuggestion | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'upload' | 'reuse'>('upload');
  const [existingFiles, setExistingFiles] = useState<UnifiedDataUpload[]>([]);
  const [selectedExistingFile, setSelectedExistingFile] = useState<string | null>(null);
  const [isLoadingFiles, setIsLoadingFiles] = useState(false);
  const [noFilesMessage, setNoFilesMessage] = useState<string | null>(null);

  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();

  // Load existing files on component mount
  useEffect(() => {
    loadExistingFiles();
  }, []);

  // Update suggested purposes when props change
  useEffect(() => {
    if (suggestedPurposes.length > 0) {
      setSelectedPurposes([...new Set([...requiredPurposes, ...suggestedPurposes])]);
    }
  }, [suggestedPurposes, requiredPurposes]);

  const loadExistingFiles = async () => {
    try {
      setIsLoadingFiles(true);
      const result = await unifiedDataManager.getValidatedFiles();
      setExistingFiles(result.files);

      // Show notification if files were cleaned up
      if (result.cleanedCount > 0) {
        toast({
          title: "Files Updated",
          description: `Removed ${result.cleanedCount} files that no longer exist in the database.`,
          variant: "default"
        });
      }

      // Show message if no files are available
      if (!result.hasFiles) {
        setNoFilesMessage("No files available. Upload a file first to enable reuse.");
      } else {
        setNoFilesMessage(null);
      }
    } catch (error) {
      console.error('Failed to load files:', error);
      setExistingFiles([]);
      setNoFilesMessage("Failed to load files. Please try again.");
    } finally {
      setIsLoadingFiles(false);
    }
  };

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    
    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      handleFileSelection(files[0]);
    }
  }, []);

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      handleFileSelection(files[0]);
    }
  };

  const validateFile = (file: File): string | null => {
    // Check file size
    if (file.size > maxSize * 1024 * 1024) {
      return `File size exceeds ${maxSize}MB limit`;
    }

    // Check file type
    const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();
    if (!acceptedTypes.includes(fileExtension)) {
      return `File type ${fileExtension} is not supported. Accepted types: ${acceptedTypes.join(', ')}`;
    }

    return null;
  };

  const handleFileSelection = async (file: File) => {
    const validationError = validateFile(file);
    if (validationError) {
      setError(validationError);
      toast({
        title: "Invalid file",
        description: validationError,
        variant: "destructive"
      });
      return;
    }

    setSelectedFile(file);
    setError(null);
    setIsUploading(true);
    setUploadProgress(0);

    // Start progress monitoring
    const operationId = ProgressUtils.generateId('upload');
    unifiedProgressMonitor.startOperation(operationId, 'upload', 'Uploading file...', {
      fileName: file.name,
      fileSize: file.size
    });

    // Subscribe to progress updates
    const progressSubscription = unifiedProgressMonitor.subscribe((event) => {
      if (event.id === operationId) {
        setUploadProgress(event.progress);
      }
    }, { types: ['upload'] });

    try {
      // Upload file with unified data manager
      const fileId = await unifiedDataManager.uploadFile(file, selectedPurposes);
      const fileInfo = unifiedDataManager.getDataForPurpose(fileId, 'analysis');

      if (fileInfo) {
        // Complete upload progress
        unifiedProgressMonitor.completeOperation(operationId, 'File uploaded successfully');

        // Generate suggestions for the uploaded file
        const fileSuggestions = unifiedDataManager.suggestDataPurposes(fileInfo);
        setSuggestions(fileSuggestions);

        setSelectedFileId(fileId);
        setIsUploading(false);

        // Reload existing files list
        loadExistingFiles();

        // Notify parent component
        if (onFileSelected) {
          onFileSelected(fileId, fileInfo, selectedPurposes);
        }

        toast({
          title: "File uploaded successfully",
          description: `${file.name} is ready for ${selectedPurposes.join(', ')}`
        });
      }
    } catch (error: any) {
      // Fail upload progress
      unifiedProgressMonitor.failOperation(operationId, {
        code: 'UPLOAD_FAILED',
        message: error.message || 'Failed to upload file'
      });

      setError(error.message || 'Failed to upload file');
      setIsUploading(false);
      toast({
        title: "Upload failed",
        description: error.message || 'Failed to upload file. Please try again.',
        variant: "destructive"
      });
    } finally {
      // Clean up subscription
      unifiedProgressMonitor.unsubscribe(progressSubscription);
    }
  };

  const handlePurposeChange = (purpose: DataPurpose, checked: boolean) => {
    if (checked) {
      setSelectedPurposes([...selectedPurposes, purpose]);
    } else {
      // Don't allow removing required purposes
      if (!requiredPurposes.includes(purpose)) {
        setSelectedPurposes(selectedPurposes.filter(p => p !== purpose));
      }
    }
  };

  const handleExistingFileSelect = (fileId: string) => {
    const fileData = unifiedDataManager.getDataForPurpose(fileId, 'analysis');
    if (fileData) {
      setSelectedExistingFile(fileId);
      setSelectedFileId(fileId);
      
      // Update purposes for the existing file
      unifiedDataManager.updatePurposes(fileId, selectedPurposes);
      
      if (onFileSelected) {
        onFileSelected(fileId, fileData, selectedPurposes);
      }

      toast({
        title: "File selected",
        description: `Reusing ${fileData.filename} for ${selectedPurposes.join(', ')}`
      });
    }
  };

  const handleRemoveFile = () => {
    setSelectedFile(null);
    setSelectedFileId(null);
    setSelectedExistingFile(null);
    setSuggestions(null);
    setError(null);
    
    if (onFileRemoved) {
      onFileRemoved();
    }
  };

  const getPurposeIcon = (purpose: DataPurpose) => {
    switch (purpose) {
      case 'analysis': return <Database className="w-4 h-4" />;
      case 'training': return <Brain className="w-4 h-4" />;
      case 'classification': return <Target className="w-4 h-4" />;
      default: return <FileText className="w-4 h-4" />;
    }
  };

  const getPurposeColor = (purpose: DataPurpose) => {
    switch (purpose) {
      case 'analysis': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'training': return 'bg-green-100 text-green-800 border-green-200';
      case 'classification': return 'bg-purple-100 text-purple-800 border-purple-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (date: Date) => {
    return new Intl.RelativeTimeFormat('en', { numeric: 'auto' }).format(
      Math.ceil((date.getTime() - Date.now()) / (1000 * 60 * 60 * 24)),
      'day'
    );
  };

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Upload className="w-5 h-5" />
          {title}
        </CardTitle>
        <CardDescription>{description}</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Purpose Selection */}
        {allowMultiplePurposes && (
          <div className="space-y-3">
            <Label className="text-sm font-medium">Data Usage Purposes</Label>
            <div className="flex flex-wrap gap-3">
              {(['analysis', 'training', 'classification'] as DataPurpose[]).map((purpose) => (
                <div key={purpose} className="flex items-center space-x-2">
                  <Checkbox
                    id={purpose}
                    checked={selectedPurposes.includes(purpose)}
                    onCheckedChange={(checked) => handlePurposeChange(purpose, checked as boolean)}
                    disabled={requiredPurposes.includes(purpose)}
                  />
                  <Label 
                    htmlFor={purpose} 
                    className="flex items-center gap-2 text-sm cursor-pointer"
                  >
                    {getPurposeIcon(purpose)}
                    {purpose.charAt(0).toUpperCase() + purpose.slice(1)}
                    {requiredPurposes.includes(purpose) && (
                      <Badge variant="secondary" className="text-xs">Required</Badge>
                    )}
                  </Label>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* File Upload/Reuse Tabs */}
        <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'upload' | 'reuse')}>
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="upload" className="flex items-center gap-2">
              <Upload className="w-4 h-4" />
              Upload New File
            </TabsTrigger>
            {showFileReuse && (
              <TabsTrigger value="reuse" className="flex items-center gap-2">
                <RefreshCw className="w-4 h-4" />
                Reuse Existing ({existingFiles.length})
              </TabsTrigger>
            )}
          </TabsList>

          {/* Upload Tab */}
          <TabsContent value="upload" className="space-y-4">
            {!selectedFileId ? (
              <div
                className={`
                  border-2 border-dashed rounded-lg p-8 text-center transition-colors cursor-pointer
                  ${isDragOver ? 'border-primary bg-primary/5' : 'border-muted-foreground/25'}
                  ${isUploading ? 'pointer-events-none opacity-50' : 'hover:border-primary hover:bg-primary/5'}
                `}
                onDragOver={handleDragOver}
                onDragLeave={handleDragLeave}
                onDrop={handleDrop}
                onClick={() => fileInputRef.current?.click()}
              >
                <input
                  ref={fileInputRef}
                  type="file"
                  accept={acceptedTypes.join(',')}
                  onChange={handleFileInputChange}
                  className="hidden"
                />
                
                {isUploading ? (
                  <div className="space-y-4">
                    <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center mx-auto">
                      <RefreshCw className="w-6 h-6 text-primary animate-spin" />
                    </div>
                    <div className="space-y-2">
                      <p className="text-sm font-medium">Uploading file...</p>
                      <Progress value={uploadProgress} className="w-full max-w-xs mx-auto" />
                      <p className="text-xs text-muted-foreground">{uploadProgress}% complete</p>
                    </div>
                  </div>
                ) : (
                  <div className="space-y-4">
                    <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center mx-auto">
                      <Upload className="w-6 h-6 text-primary" />
                    </div>
                    <div className="space-y-2">
                      <p className="text-sm font-medium">
                        Drop your file here or click to browse
                      </p>
                      <p className="text-xs text-muted-foreground">
                        Supported formats: {acceptedTypes.join(', ')} • Max size: {maxSize}MB
                      </p>
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <div className="space-y-4">
                {/* Selected File Display */}
                <div className="flex items-center justify-between p-4 bg-muted/50 rounded-lg">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 rounded-lg bg-primary/10 flex items-center justify-center">
                      <File className="w-5 h-5 text-primary" />
                    </div>
                    <div>
                      <p className="font-medium">{selectedFile?.name}</p>
                      <p className="text-sm text-muted-foreground">
                        {selectedFile && formatFileSize(selectedFile.size)}
                      </p>
                    </div>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleRemoveFile}
                    className="text-destructive hover:text-destructive"
                  >
                    <Trash2 className="w-4 h-4" />
                  </Button>
                </div>

                {/* Purpose Badges */}
                <div className="flex flex-wrap gap-2">
                  {selectedPurposes.map((purpose) => (
                    <Badge
                      key={purpose}
                      variant="outline"
                      className={getPurposeColor(purpose)}
                    >
                      {getPurposeIcon(purpose)}
                      <span className="ml-1">{purpose}</span>
                    </Badge>
                  ))}
                </div>
              </div>
            )}
          </TabsContent>

          {/* Reuse Tab */}
          {showFileReuse && (
            <TabsContent value="reuse" className="space-y-4">
              {isLoadingFiles ? (
                <div className="text-center py-8 text-muted-foreground">
                  <div className="animate-spin w-8 h-8 border-2 border-primary border-t-transparent rounded-full mx-auto mb-4" />
                  <p>Loading files...</p>
                  <p className="text-sm">Validating file availability</p>
                </div>
              ) : existingFiles.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <Database className="w-12 h-12 mx-auto mb-4 opacity-50" />
                  <p>{noFilesMessage || "No previously uploaded files found"}</p>
                  <p className="text-sm">Upload a file first to enable reuse</p>
                </div>
              ) : (
                <div className="space-y-3 max-h-64 overflow-y-auto">
                  {existingFiles.map((fileData) => (
                    <div
                      key={fileData.fileInfo.file_id}
                      className={`
                        p-4 border rounded-lg cursor-pointer transition-colors
                        ${selectedExistingFile === fileData.fileInfo.file_id 
                          ? 'border-primary bg-primary/5' 
                          : 'border-border hover:border-primary/50 hover:bg-muted/50'
                        }
                      `}
                      onClick={() => handleExistingFileSelect(fileData.fileInfo.file_id)}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div className="w-8 h-8 rounded bg-primary/10 flex items-center justify-center">
                            <File className="w-4 h-4 text-primary" />
                          </div>
                          <div>
                            <p className="font-medium text-sm">{fileData.fileInfo.filename}</p>
                            <p className="text-xs text-muted-foreground">
                              {fileData.fileInfo.num_rows.toLocaleString()} rows • 
                              {formatDate(fileData.lastUsed)}
                            </p>
                          </div>
                        </div>
                        <div className="flex flex-wrap gap-1">
                          {Object.entries(fileData.purposes)
                            .filter(([_, enabled]) => enabled)
                            .map(([purpose]) => (
                              <Badge
                                key={purpose}
                                variant="secondary"
                                className="text-xs"
                              >
                                {purpose}
                              </Badge>
                            ))}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </TabsContent>
          )}
        </Tabs>

        {/* Smart Suggestions */}
        {suggestions && (
          <Alert>
            <Zap className="h-4 w-4" />
            <AlertDescription>
              <div className="space-y-2">
                <p className="font-medium">Smart Analysis Results:</p>
                <ul className="text-sm space-y-1">
                  {suggestions.recommendations.map((rec, index) => (
                    <li key={index} className="flex items-start gap-2">
                      <CheckCircle2 className="w-3 h-3 text-green-500 mt-0.5 flex-shrink-0" />
                      {rec}
                    </li>
                  ))}
                </ul>
                {suggestions.warnings.length > 0 && (
                  <div className="mt-2 pt-2 border-t">
                    <p className="font-medium text-amber-600">Considerations:</p>
                    <ul className="text-sm space-y-1">
                      {suggestions.warnings.map((warning, index) => (
                        <li key={index} className="flex items-start gap-2">
                          <AlertCircle className="w-3 h-3 text-amber-500 mt-0.5 flex-shrink-0" />
                          {warning}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            </AlertDescription>
          </Alert>
        )}

        {/* Error Display */}
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Continue Button */}
        {showContinueButton && selectedFileId && onContinue && (
          <div className="flex justify-end">
            <Button onClick={onContinue} className="px-6">
              Continue
              <CheckCircle2 className="w-4 h-4 ml-2" />
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
