// frontend/src/utils/hierarchyUtils.ts
import { HierarchyRow, NestedHierarchySuggestion } from '../types'; // Added NestedHierarchySuggestion
import config from '../config'; // Added config import

// --- Helper Functions ---

// Basic validation: Check if all required fields in a row are filled based on dynamic levels
export const isRowComplete = (row: HierarchyRow, hierarchyLevels: string[]): boolean => {
  // Check if all provided hierarchy levels have a non-empty value in the row
  return hierarchyLevels.every(level => !!row[level]?.toString().trim());
};

// Flatten nested suggestion based on dynamic hierarchy levels
export const flattenNestedSuggestion = (nested: NestedHierarchySuggestion, hierarchyLevels: string[] = config.DEFAULT_HIERARCHY_LEVELS): HierarchyRow[] => {
  const rows: HierarchyRow[] = [];
  try {
    // Assuming the nested structure follows a pattern like themes > categories > segments > subsegments
    // This part might need adjustment if the backend suggestion structure is different or more dynamic
    nested?.themes?.forEach((theme: any, themeIndex: number) => {
      const themeName = theme?.name || '';
      theme?.categories?.forEach((category: any, catIndex: number) => {
        const catName = category?.name || '';
        category?.segments?.forEach((segment: any, segIndex: number) => {
          const segName = segment?.name || '';
          segment?.subsegments?.forEach((subSegment: any, subSegIndex: number) => {
            const subSegName = subSegment?.name || '';
            const keywords = (subSegment?.keywords || []).join(', ');
            const id = `sugg-${themeIndex}-${catIndex}-${segIndex}-${subSegIndex}`;

            // Only proceed if we have values for the expected base levels
            if (themeName && catName && segName && subSegName) {
              const row: HierarchyRow = { id, Keywords: keywords };
              const values = [themeName, catName, segName, subSegName]; // Base values

              // Map values to the defined hierarchy levels
              hierarchyLevels.forEach((level, index) => {
                row[level] = values[index] ?? ''; // Assign value or empty string
              });

              rows.push(row);
            }
          });
        });
      });
    });
  } catch (error) {
    console.error("Error flattening suggestion:", error);
    return []; // Return empty on error
  }
  return rows;
};

// Basic check if the hierarchy structure (list of rows) is valid
// A more robust check might ensure no duplicate paths exist.
export const validateHierarchyRows = (rows: HierarchyRow[], hierarchyLevels: string[]): boolean => {
    if (!rows || rows.length === 0) {
        return false; // Empty is not valid for classification
    }
    // Check if at least one row has all required hierarchy levels filled
    return rows.some(row => isRowComplete(row, hierarchyLevels));
};


// --- Structure Conversion ---

// This function mirrors the backend logic for converting flat rows to nested structure
// Needed by ClassificationRunner to send the correct format to the backend.

// Dynamic hierarchy interfaces that can adapt to any level structure
interface HierarchyNode {
    name: string;
    keywords?: string[];
    children?: HierarchyNode[];
}

interface DynamicNestedHierarchy {
    [key: string]: HierarchyNode[];
}

// Legacy interfaces for backward compatibility
interface SubSegment {
    name: string;
    keywords: string[];
}

interface Segment {
    name: string;
    subsegments: SubSegment[];
}

interface Category {
    name: string;
    segments: Segment[];
}

interface Theme {
    name: string;
    categories: Category[];
}

interface NestedHierarchy {
    themes: Theme[];
}

// Dynamic hierarchy builder that works with any hierarchy structure
export const buildDynamicHierarchyFromDf = (rows: HierarchyRow[], hierarchyLevels: string[]): DynamicNestedHierarchy => {
    if (!rows || rows.length === 0 || !hierarchyLevels || hierarchyLevels.length === 0) {
        return {};
    }

    const result: DynamicNestedHierarchy = {};
    const rootLevelKey = hierarchyLevels[0].toLowerCase() + 's'; // e.g., "level_1s" or "themes"
    result[rootLevelKey] = [];

    // Build nested structure recursively
    const buildLevel = (currentRows: HierarchyRow[], levelIndex: number, parentPath: string[] = []): HierarchyNode[] => {
        if (levelIndex >= hierarchyLevels.length) return [];

        const currentLevel = hierarchyLevels[levelIndex];
        const groupedByLevel: { [key: string]: HierarchyRow[] } = {};

        // Group rows by current level value
        currentRows.forEach(row => {
            const levelValue = row[currentLevel]?.toString().trim();
            if (levelValue) {
                if (!groupedByLevel[levelValue]) {
                    groupedByLevel[levelValue] = [];
                }
                groupedByLevel[levelValue].push(row);
            }
        });

        // Create nodes for this level
        return Object.entries(groupedByLevel).map(([levelValue, levelRows]) => {
            const node: HierarchyNode = { name: levelValue };

            if (levelIndex === hierarchyLevels.length - 1) {
                // This is the deepest level, add keywords
                const keywords = levelRows
                    .map(row => row.Keywords?.toString().trim())
                    .filter(k => k)
                    .join(', ')
                    .split(',')
                    .map(k => k.trim())
                    .filter(k => k);

                if (keywords.length > 0) {
                    node.keywords = [...new Set(keywords)]; // Remove duplicates
                }
            } else {
                // Build children for next level
                node.children = buildLevel(levelRows, levelIndex + 1, [...parentPath, levelValue]);
            }

            return node;
        });
    };

    result[rootLevelKey] = buildLevel(rows, 0);
    return result;
};

// Legacy function for backward compatibility
export const buildHierarchyFromDf = (rows: HierarchyRow[], _activeHierarchyConfig?: any): NestedHierarchy | null => {
    if (!rows || rows.length === 0) {
        return { themes: [] }; // Return empty structure if no rows
    }

    // Get the hierarchy levels from the first row's keys (excluding 'id' and 'Keywords')
    const hierarchyLevels = Object.keys(rows[0])
        .filter(key => key !== 'id' && key !== 'Keywords')
        .filter(key => rows.some(row => row[key])); // Only include levels that have values

    if (hierarchyLevels.length === 0) {
        console.warn("buildHierarchyFromDf: No hierarchy levels found in rows.");
        return null;
    }

    console.log("Building hierarchy with levels:", hierarchyLevels);

    // Include the hierarchy configuration if available (for potential future use)
    // const config = activeHierarchyConfig ? {
    //     id: activeHierarchyConfig.id,
    //     name: activeHierarchyConfig.name,
    //     hierarchy_levels: activeHierarchyConfig.hierarchy_levels
    // } : {
    //     hierarchy_levels: hierarchyLevels
    // };

    // Special case for single-level hierarchy
    if (hierarchyLevels.length === 1) {
        const level = hierarchyLevels[0];
        const themes: Theme[] = [];
        let hasValidRow = false;

        // Group by the single level
        const groupedByTheme: Record<string, {name: string, keywords: string[]}[]> = {};

        rows.forEach(row => {
            const themeName = String(row[level] || '').trim();
            if (!themeName) return;

            hasValidRow = true;
            const keywords = (row.Keywords || '')
                .split(',')
                .map(k => k.trim())
                .filter(k => k !== '');

            if (!groupedByTheme[themeName]) {
                groupedByTheme[themeName] = [];
            }

            // Add as a subsegment with the same name as the theme
            groupedByTheme[themeName].push({
                name: themeName,
                keywords: keywords
            });
        });

        if (!hasValidRow) {
            console.warn("buildHierarchyFromDf: No valid rows found for single-level hierarchy.");
            return null;
        }

        // Convert to the expected structure
        Object.entries(groupedByTheme).forEach(([themeName, subsegments]) => {
            themes.push({
                name: themeName,
                categories: [{
                    name: "Category", // Placeholder
                    segments: [{
                        name: "Segment", // Placeholder
                        subsegments: subsegments
                    }]
                }]
            });
        });

        return { themes };
    }

    // Handle multi-level hierarchies (2 or more levels)
    // Create a dynamic nested dictionary structure
    const themesDict: any = {};
    let hasValidRow = false;

    rows.forEach(row => {
        // Extract values for each level
        const levelValues = hierarchyLevels.map(level => String(row[level] || '').trim());

        // Skip if any required level is empty
        if (levelValues.some(val => !val)) {
            return;
        }

        hasValidRow = true;

        const keywords = (row.Keywords || '')
            .split(',')
            .map(k => k.trim())
            .filter(k => k !== '');

        // Build the nested structure dynamically
        let current = themesDict;

        // Process all levels except the last one
        for (let i = 0; i < hierarchyLevels.length - 1; i++) {
            const levelValue = levelValues[i];
            const levelKey = i === 0 ? 'themes' :
                             i === 1 ? 'categories' :
                             i === 2 ? 'segments' : `level${i}s`;

            if (!current[levelKey]) {
                current[levelKey] = {};
            }

            if (!current[levelKey][levelValue]) {
                current[levelKey][levelValue] = { name: levelValue };

                // Initialize the next level container
                const nextLevelKey = i + 1 === 1 ? 'categories' :
                                   i + 1 === 2 ? 'segments' :
                                   i + 1 === hierarchyLevels.length - 1 ? 'subsegments' : `level${i+1}s`;

                if (i + 1 === hierarchyLevels.length - 1) {
                    // Last level before leaf - initialize as array
                    current[levelKey][levelValue][nextLevelKey] = [];
                } else {
                    // Not the last level - initialize as object
                    current[levelKey][levelValue][nextLevelKey] = {};
                }
            }

            current = current[levelKey][levelValue];
        }

        // Handle the last level (leaf level)
        const lastLevelIndex = hierarchyLevels.length - 1;
        const lastLevelValue = levelValues[lastLevelIndex];
        const lastLevelKey = 'subsegments';

        if (!current[lastLevelKey]) {
            current[lastLevelKey] = [];
        }

        // Add subsegment if it doesn't already exist
        if (!current[lastLevelKey].some((ss: any) => ss.name === lastLevelValue)) {
            current[lastLevelKey].push({
                name: lastLevelValue,
                keywords: keywords
            });
        }
    });

    if (!hasValidRow) {
        console.warn("buildHierarchyFromDf: No valid rows found to build hierarchy.");
        return null;
    }

    // Convert the dynamic dictionary to the expected nested structure
    const finalThemes: Theme[] = [];

    // Process themes
    Object.entries(themesDict.themes || {}).forEach(([_themeName, themeData]: [string, any]) => {
        const theme: Theme = {
            name: themeData.name,
            categories: []
        };

        // Process categories
        Object.entries(themeData.categories || {}).forEach(([_catName, catData]: [string, any]) => {
            const category: Category = {
                name: catData.name,
                segments: []
            };

            // Process segments
            Object.entries(catData.segments || {}).forEach(([_segName, segData]: [string, any]) => {
                const segment: Segment = {
                    name: segData.name,
                    subsegments: segData.subsegments || []
                };

                if (segment.subsegments.length > 0) {
                    category.segments.push(segment);
                }
            });

            if (category.segments.length > 0) {
                theme.categories.push(category);
            }
        });

        if (theme.categories.length > 0) {
            finalThemes.push(theme);
        }
    });

    return { themes: finalThemes };
};
