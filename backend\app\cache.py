"""
Cache module for the application.
Provides caching functionality for database queries and other expensive operations.
"""
import logging
import time
from typing import Dict, Any, Optional, Callable, TypeVar, Tuple
from functools import wraps

logger = logging.getLogger(__name__)

# Type variable for generic function return type
T = TypeVar('T')

# Simple in-memory cache
_cache: Dict[str, Tuple[Any, float]] = {}
_DEFAULT_TTL = 300  # Default TTL in seconds (5 minutes)

def clear_cache():
    """Clear the entire cache."""
    global _cache
    _cache = {}
    logger.info("Cache cleared")

def get_from_cache(key: str) -> Optional[Any]:
    """
    Get a value from the cache.

    Args:
        key: The cache key

    Returns:
        The cached value or None if not found or expired
    """
    logger.debug(f"Attempting to get value from cache with key: {key}")

    if key not in _cache:
        logger.debug(f"Cache miss: Key '{key}' not found in cache")
        return None

    value, expiry = _cache[key]
    if time.time() > expiry:
        # Expired, remove from cache
        logger.debug(f"Cache entry expired for key: {key}")
        del _cache[key]
        return None

    logger.debug(f"Cache hit for key: {key}")
    return value

def set_in_cache(key: str, value: Any, ttl: int = _DEFAULT_TTL):
    """
    Set a value in the cache.

    Args:
        key: The cache key
        value: The value to cache
        ttl: Time to live in seconds
    """
    logger.debug(f"Setting cache value for key: {key} with TTL: {ttl}s")

    # Log information about the value being cached
    if isinstance(value, list):
        logger.debug(f"Caching list with {len(value)} items")
    else:
        logger.debug(f"Caching value of type: {type(value).__name__}")

    expiry = time.time() + ttl
    _cache[key] = (value, expiry)
    logger.debug(f"Value cached successfully for key: {key}, expires at: {time.ctime(expiry)}")

def cache_result(ttl: int = _DEFAULT_TTL):
    """
    Decorator to cache function results.

    Args:
        ttl: Time to live in seconds

    Returns:
        Decorated function
    """
    def decorator(func: Callable[..., T]) -> Callable[..., T]:
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Create a cache key from function name and arguments
            # Skip the first argument (db session) for database functions
            cache_key = f"{func.__name__}:{str(args[1:])}{str(kwargs)}"
            logger.debug(f"Cache decorator called for function: {func.__name__} with key: {cache_key}")

            # Check if result is in cache
            cached_result = get_from_cache(cache_key)
            if cached_result is not None:
                logger.debug(f"Using cached result for {func.__name__} with key: {cache_key}")
                return cached_result

            # Execute function and cache result
            logger.debug(f"Cache miss, executing function: {func.__name__}")
            result = func(*args, **kwargs)

            # Log the result type and some info about it
            if result is None:
                logger.debug(f"Function {func.__name__} returned None, not caching")
            elif isinstance(result, list):
                logger.debug(f"Function {func.__name__} returned list with {len(result)} items")
            else:
                logger.debug(f"Function {func.__name__} returned {type(result).__name__}")

            if result is not None:  # Only cache non-None results
                set_in_cache(cache_key, result, ttl)
                logger.debug(f"Cached result for {func.__name__} with key: {cache_key} (TTL: {ttl}s)")

            return result
        return wrapper
    return decorator

def invalidate_model_cache(model_id: str):
    """
    Invalidate cache entries related to a specific model.

    Args:
        model_id: The model ID to invalidate
    """
    logger.info(f"Invalidating cache for model ID: {model_id}")

    # Log current cache state
    logger.debug(f"Current cache has {len(_cache)} entries")

    keys_to_delete = []
    for key in _cache:
        if f"model:{model_id}" in key:
            keys_to_delete.append(key)
            logger.debug(f"Marked for deletion: {key}")

    if not keys_to_delete:
        logger.warning(f"No cache entries found for model ID: {model_id}")

    for key in keys_to_delete:
        del _cache[key]
        logger.info(f"Invalidated cache for {key}")

    logger.debug(f"Cache now has {len(_cache)} entries after invalidation")
