/**
 * FileReuseManager.tsx
 * 
 * Component for managing and reusing previously uploaded files
 * across different workflows and purposes.
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  File,
  Search,
  Filter,
  Clock,
  Database,
  Brain,
  Target,
  Trash2,
  RefreshCw,
  Download,
  Eye,
  MoreHorizontal,
  Calendar,
  FileText,
  BarChart3,
  CheckCircle2,
  AlertCircle
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";

import { 
  unifiedDataManager, 
  UnifiedDataUpload, 
  DataPurpose 
} from "@/services/unifiedDataManager";
import { UploadedFile } from "@/services/fileUploadApi";

interface FileReuseManagerProps {
  onFileSelected?: (fileId: string, fileInfo: UploadedFile, purposes: DataPurpose[]) => void;
  requiredPurposes?: DataPurpose[];
  className?: string;
  showFileDetails?: boolean;
  allowPurposeUpdate?: boolean;
  maxDisplayFiles?: number;
}

type SortOption = 'recent' | 'name' | 'size' | 'purposes';
type FilterOption = 'all' | 'analysis' | 'training' | 'classification';

export const FileReuseManager: React.FC<FileReuseManagerProps> = ({
  onFileSelected,
  requiredPurposes = [],
  className = '',
  showFileDetails = true,
  allowPurposeUpdate = true,
  maxDisplayFiles = 10
}) => {
  const [files, setFiles] = useState<UnifiedDataUpload[]>([]);
  const [filteredFiles, setFilteredFiles] = useState<UnifiedDataUpload[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState<SortOption>('recent');
  const [filterBy, setFilterBy] = useState<FilterOption>('all');
  const [selectedFile, setSelectedFile] = useState<string | null>(null);
  const [showDeleteDialog, setShowDeleteDialog] = useState<string | null>(null);
  const [showDetailsDialog, setShowDetailsDialog] = useState<string | null>(null);

  const { toast } = useToast();

  useEffect(() => {
    loadFiles();
  }, []);

  useEffect(() => {
    applyFiltersAndSort();
  }, [files, searchQuery, sortBy, filterBy]);

  const loadFiles = async () => {
    try {
      const result = await unifiedDataManager.getValidatedFiles();
      setFiles(result.files);

      // Show notification if files were cleaned up
      if (result.cleanedCount > 0) {
        toast({
          title: "Files Updated",
          description: `Removed ${result.cleanedCount} files that no longer exist.`,
          variant: "default"
        });
      }
    } catch (error) {
      console.error('Failed to load files:', error);
      setFiles([]);
      toast({
        title: "Error",
        description: "Failed to load files. Please try again.",
        variant: "destructive"
      });
    }
  };

  const applyFiltersAndSort = () => {
    let filtered = [...files];

    // Apply search filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(file => 
        file.fileInfo.filename.toLowerCase().includes(query) ||
        file.fileInfo.columns?.some(col => col.toLowerCase().includes(query))
      );
    }

    // Apply purpose filter
    if (filterBy !== 'all') {
      filtered = filtered.filter(file => file.purposes[filterBy as DataPurpose]);
    }

    // Apply sorting
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'recent':
          return b.lastUsed.getTime() - a.lastUsed.getTime();
        case 'name':
          return a.fileInfo.filename.localeCompare(b.fileInfo.filename);
        case 'size':
          return b.fileInfo.num_rows - a.fileInfo.num_rows;
        case 'purposes':
          const aPurposes = Object.values(a.purposes).filter(Boolean).length;
          const bPurposes = Object.values(b.purposes).filter(Boolean).length;
          return bPurposes - aPurposes;
        default:
          return 0;
      }
    });

    // Limit display count
    if (maxDisplayFiles > 0) {
      filtered = filtered.slice(0, maxDisplayFiles);
    }

    setFilteredFiles(filtered);
  };

  const handleFileSelect = (fileData: UnifiedDataUpload) => {
    const activePurposes = Object.entries(fileData.purposes)
      .filter(([_, enabled]) => enabled)
      .map(([purpose]) => purpose as DataPurpose);

    setSelectedFile(fileData.fileInfo.file_id);
    
    if (onFileSelected) {
      onFileSelected(fileData.fileInfo.file_id, fileData.fileInfo, activePurposes);
    }

    toast({
      title: "File selected",
      description: `Using ${fileData.fileInfo.filename} for ${activePurposes.join(', ')}`
    });
  };

  const handleDeleteFile = async (fileId: string) => {
    const success = unifiedDataManager.removeFile(fileId);
    if (success) {
      loadFiles();
      toast({
        title: "File removed",
        description: "File has been removed from the system"
      });
    } else {
      toast({
        title: "Error",
        description: "Failed to remove file",
        variant: "destructive"
      });
    }
    setShowDeleteDialog(null);
  };

  const updateFilePurposes = (fileId: string, purposes: DataPurpose[]) => {
    const success = unifiedDataManager.updatePurposes(fileId, purposes);
    if (success) {
      loadFiles();
      toast({
        title: "Purposes updated",
        description: "File purposes have been updated successfully"
      });
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (date: Date) => {
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    
    if (diffDays === 0) return 'Today';
    if (diffDays === 1) return 'Yesterday';
    if (diffDays < 7) return `${diffDays} days ago`;
    if (diffDays < 30) return `${Math.floor(diffDays / 7)} weeks ago`;
    return date.toLocaleDateString();
  };

  const getPurposeIcon = (purpose: DataPurpose) => {
    switch (purpose) {
      case 'analysis': return <Database className="w-3 h-3" />;
      case 'training': return <Brain className="w-3 h-3" />;
      case 'classification': return <Target className="w-3 h-3" />;
      default: return <FileText className="w-3 h-3" />;
    }
  };

  const getPurposeColor = (purpose: DataPurpose) => {
    switch (purpose) {
      case 'analysis': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'training': return 'bg-green-100 text-green-800 border-green-200';
      case 'classification': return 'bg-purple-100 text-purple-800 border-purple-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const isFileCompatible = (fileData: UnifiedDataUpload): boolean => {
    if (requiredPurposes.length === 0) return true;
    return requiredPurposes.some(purpose => fileData.purposes[purpose]);
  };

  const getUsageStats = () => {
    return unifiedDataManager.getUsageStats();
  };

  const stats = getUsageStats();

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header with Stats */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <RefreshCw className="w-5 h-5" />
            File Reuse Manager
          </CardTitle>
          <CardDescription>
            Manage and reuse previously uploaded files across different workflows
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-primary">{stats.totalFiles}</div>
              <div className="text-sm text-muted-foreground">Total Files</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{stats.purposeBreakdown.analysis}</div>
              <div className="text-sm text-muted-foreground">Analysis</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{stats.purposeBreakdown.training}</div>
              <div className="text-sm text-muted-foreground">Training</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">{stats.purposeBreakdown.classification}</div>
              <div className="text-sm text-muted-foreground">Classification</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Search and Filter Controls */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <Label htmlFor="search" className="sr-only">Search files</Label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
                <Input
                  id="search"
                  placeholder="Search files by name or column..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="flex gap-2">
              <Select value={sortBy} onValueChange={(value) => setSortBy(value as SortOption)}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="recent">Recent</SelectItem>
                  <SelectItem value="name">Name</SelectItem>
                  <SelectItem value="size">Size</SelectItem>
                  <SelectItem value="purposes">Purposes</SelectItem>
                </SelectContent>
              </Select>
              <Select value={filterBy} onValueChange={(value) => setFilterBy(value as FilterOption)}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Files</SelectItem>
                  <SelectItem value="analysis">Analysis</SelectItem>
                  <SelectItem value="training">Training</SelectItem>
                  <SelectItem value="classification">Classification</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Files List */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Available Files ({filteredFiles.length})</CardTitle>
        </CardHeader>
        <CardContent>
          {filteredFiles.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <File className="w-12 h-12 mx-auto mb-4 opacity-50" />
              <p className="text-lg font-medium">No files found</p>
              <p className="text-sm">
                {files.length === 0 
                  ? "Upload a file first to enable reuse" 
                  : "Try adjusting your search or filter criteria"
                }
              </p>
            </div>
          ) : (
            <div className="space-y-3">
              {filteredFiles.map((fileData) => {
                const isCompatible = isFileCompatible(fileData);
                const isSelected = selectedFile === fileData.fileInfo.file_id;
                const activePurposes = Object.entries(fileData.purposes)
                  .filter(([_, enabled]) => enabled)
                  .map(([purpose]) => purpose as DataPurpose);

                return (
                  <div
                    key={fileData.fileInfo.file_id}
                    className={`
                      p-4 border rounded-lg transition-all
                      ${isSelected 
                        ? 'border-primary bg-primary/5' 
                        : isCompatible 
                          ? 'border-border hover:border-primary/50 hover:bg-muted/50 cursor-pointer'
                          : 'border-border opacity-60'
                      }
                    `}
                    onClick={() => isCompatible && handleFileSelect(fileData)}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3 flex-1">
                        <div className="w-10 h-10 rounded-lg bg-primary/10 flex items-center justify-center">
                          <File className="w-5 h-5 text-primary" />
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-1">
                            <h4 className="font-medium truncate">{fileData.fileInfo.filename}</h4>
                            {!isCompatible && (
                              <Badge variant="secondary" className="text-xs">
                                Incompatible
                              </Badge>
                            )}
                          </div>
                          <div className="flex items-center gap-4 text-sm text-muted-foreground">
                            <span className="flex items-center gap-1">
                              <BarChart3 className="w-3 h-3" />
                              {fileData.fileInfo.num_rows.toLocaleString()} rows
                            </span>
                            <span className="flex items-center gap-1">
                              <Clock className="w-3 h-3" />
                              {formatDate(fileData.lastUsed)}
                            </span>
                          </div>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-3">
                        {/* Purpose Badges */}
                        <div className="flex flex-wrap gap-1">
                          {activePurposes.map((purpose) => (
                            <Badge
                              key={purpose}
                              variant="outline"
                              className={`text-xs ${getPurposeColor(purpose)}`}
                            >
                              {getPurposeIcon(purpose)}
                              <span className="ml-1">{purpose}</span>
                            </Badge>
                          ))}
                        </div>

                        {/* Action Buttons */}
                        <div className="flex items-center gap-1">
                          {showFileDetails && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={(e) => {
                                e.stopPropagation();
                                setShowDetailsDialog(fileData.fileInfo.file_id);
                              }}
                            >
                              <Eye className="w-4 h-4" />
                            </Button>
                          )}
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation();
                              setShowDeleteDialog(fileData.fileInfo.file_id);
                            }}
                            className="text-destructive hover:text-destructive"
                          >
                            <Trash2 className="w-4 h-4" />
                          </Button>
                        </div>
                      </div>
                    </div>

                    {/* Required Purposes Warning */}
                    {requiredPurposes.length > 0 && !isCompatible && (
                      <Alert className="mt-3">
                        <AlertCircle className="h-4 w-4" />
                        <AlertDescription className="text-xs">
                          This file doesn't support the required purposes: {requiredPurposes.join(', ')}
                        </AlertDescription>
                      </Alert>
                    )}
                  </div>
                );
              })}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Delete Confirmation Dialog */}
      <Dialog open={!!showDeleteDialog} onOpenChange={() => setShowDeleteDialog(null)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete File</DialogTitle>
            <DialogDescription>
              Are you sure you want to remove this file from the system? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowDeleteDialog(null)}>
              Cancel
            </Button>
            <Button 
              variant="destructive" 
              onClick={() => showDeleteDialog && handleDeleteFile(showDeleteDialog)}
            >
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* File Details Dialog */}
      <Dialog open={!!showDetailsDialog} onOpenChange={() => setShowDetailsDialog(null)}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>File Details</DialogTitle>
          </DialogHeader>
          {showDetailsDialog && (
            <div className="space-y-4">
              {(() => {
                const fileData = files.find(f => f.fileInfo.file_id === showDetailsDialog);
                if (!fileData) return null;

                return (
                  <>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label className="text-sm font-medium">Filename</Label>
                        <p className="text-sm">{fileData.fileInfo.filename}</p>
                      </div>
                      <div>
                        <Label className="text-sm font-medium">Rows</Label>
                        <p className="text-sm">{fileData.fileInfo.num_rows.toLocaleString()}</p>
                      </div>
                      <div>
                        <Label className="text-sm font-medium">Uploaded</Label>
                        <p className="text-sm">{fileData.uploadedAt.toLocaleString()}</p>
                      </div>
                      <div>
                        <Label className="text-sm font-medium">Last Used</Label>
                        <p className="text-sm">{fileData.lastUsed.toLocaleString()}</p>
                      </div>
                    </div>
                    
                    <div>
                      <Label className="text-sm font-medium">Columns</Label>
                      <div className="flex flex-wrap gap-1 mt-1">
                        {fileData.fileInfo.columns?.map((col, index) => (
                          <Badge key={index} variant="outline" className="text-xs">
                            {col}
                          </Badge>
                        ))}
                      </div>
                    </div>

                    <div>
                      <Label className="text-sm font-medium">Current Purposes</Label>
                      <div className="flex flex-wrap gap-2 mt-1">
                        {Object.entries(fileData.purposes)
                          .filter(([_, enabled]) => enabled)
                          .map(([purpose]) => (
                            <Badge
                              key={purpose}
                              variant="outline"
                              className={getPurposeColor(purpose as DataPurpose)}
                            >
                              {getPurposeIcon(purpose as DataPurpose)}
                              <span className="ml-1">{purpose}</span>
                            </Badge>
                          ))}
                      </div>
                    </div>

                    {fileData.fileInfo.preview && (
                      <div>
                        <Label className="text-sm font-medium">Data Preview</Label>
                        <div className="mt-1 p-3 bg-muted rounded-lg text-xs font-mono overflow-x-auto">
                          <pre>{JSON.stringify(fileData.fileInfo.preview.slice(0, 3), null, 2)}</pre>
                        </div>
                      </div>
                    )}
                  </>
                );
              })()}
            </div>
          )}
          <DialogFooter>
            <Button onClick={() => setShowDetailsDialog(null)}>Close</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};
