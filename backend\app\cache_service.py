"""
Caching service for ClassyWeb dynamic hierarchy system.

Provides model caching, hierarchy configuration caching, and query result caching
to improve performance of the dynamic system.
"""

import logging
import pickle
import json
import hashlib
from typing import Any, Dict, List, Optional, Union, Tuple
from datetime import datetime, timedelta
from sqlalchemy.orm import Session

from . import config
from .hierarchy_manager import HierarchySchema

# Import redis conditionally to avoid import errors if not installed
try:
    import redis
except ImportError:
    redis = None

logger = logging.getLogger(__name__)


class CacheService:
    """
    Centralized caching service for ClassyWeb.
    
    Supports multiple cache backends and provides automatic cache invalidation
    for dynamic hierarchy configurations.
    """
    
    def __init__(self, redis_url: Optional[str] = None):
        self.redis_client = None
        self.memory_cache: Dict[str, Tuple[Any, datetime]] = {}
        self.max_memory_cache_size = 1000
        
        # Initialize Redis if available
        if redis and (redis_url or hasattr(config, 'REDIS_URL')):
            try:
                self.redis_client = redis.from_url(redis_url or config.REDIS_URL)
                self.redis_client.ping()  # Test connection
                logger.info("Redis cache initialized successfully")
            except Exception as e:
                logger.warning(f"Failed to initialize Redis cache: {e}")
                self.redis_client = None
        elif not redis:
            logger.info("Redis not available, using memory cache only")
    
    def _generate_cache_key(self, prefix: str, *args, **kwargs) -> str:
        """Generate a consistent cache key from arguments."""
        key_data = f"{prefix}:{':'.join(str(arg) for arg in args)}"
        if kwargs:
            key_data += f":{json.dumps(kwargs, sort_keys=True)}"
        
        # Hash long keys to avoid Redis key length limits
        if len(key_data) > 200:
            key_data = f"{prefix}:{hashlib.md5(key_data.encode()).hexdigest()}"
        
        return key_data
    
    def get(self, key: str) -> Optional[Any]:
        """Get value from cache."""
        # Try Redis first
        if self.redis_client:
            try:
                cached_data = self.redis_client.get(key)
                if cached_data:
                    return pickle.loads(cached_data)
            except Exception as e:
                logger.warning(f"Redis get error for key {key}: {e}")
        
        # Fallback to memory cache
        if key in self.memory_cache:
            value, expiry = self.memory_cache[key]
            if datetime.now() < expiry:
                return value
            else:
                del self.memory_cache[key]
        
        return None
    
    def set(self, key: str, value: Any, ttl_seconds: int = 3600) -> bool:
        """Set value in cache with TTL."""
        # Try Redis first
        if self.redis_client:
            try:
                serialized_value = pickle.dumps(value)
                self.redis_client.setex(key, ttl_seconds, serialized_value)
                return True
            except Exception as e:
                logger.warning(f"Redis set error for key {key}: {e}")
        
        # Fallback to memory cache
        expiry = datetime.now() + timedelta(seconds=ttl_seconds)
        self.memory_cache[key] = (value, expiry)
        
        # Cleanup memory cache if too large
        if len(self.memory_cache) > self.max_memory_cache_size:
            # Remove oldest entries
            sorted_items = sorted(self.memory_cache.items(), key=lambda x: x[1][1])
            for old_key, _ in sorted_items[:100]:  # Remove 100 oldest
                del self.memory_cache[old_key]
        
        return True
    
    def delete(self, key: str) -> bool:
        """Delete value from cache."""
        deleted = False
        
        # Delete from Redis
        if self.redis_client:
            try:
                deleted = bool(self.redis_client.delete(key))
            except Exception as e:
                logger.warning(f"Redis delete error for key {key}: {e}")
        
        # Delete from memory cache
        if key in self.memory_cache:
            del self.memory_cache[key]
            deleted = True
        
        return deleted
    
    def clear_pattern(self, pattern: str) -> int:
        """Clear all keys matching a pattern."""
        deleted_count = 0
        
        # Clear from Redis
        if self.redis_client:
            try:
                keys = self.redis_client.keys(pattern)
                if keys:
                    deleted_count += self.redis_client.delete(*keys)
            except Exception as e:
                logger.warning(f"Redis pattern delete error for pattern {pattern}: {e}")
        
        # Clear from memory cache
        keys_to_delete = [key for key in self.memory_cache.keys() if pattern.replace('*', '') in key]
        for key in keys_to_delete:
            del self.memory_cache[key]
            deleted_count += 1
        
        return deleted_count


class ModelCacheService:
    """Specialized caching service for ML models and tokenizers."""
    
    def __init__(self, cache_service: CacheService):
        self.cache = cache_service
        self.model_ttl = 7200  # 2 hours
        self.tokenizer_ttl = 7200  # 2 hours
    
    def cache_hf_model(self, model_name: str, model: Any, tokenizer: Any, label_map: Dict[str, int]) -> bool:
        """Cache a Hugging Face model with its components."""
        try:
            model_key = self._generate_model_key("hf_model", model_name)
            tokenizer_key = self._generate_model_key("hf_tokenizer", model_name)
            label_map_key = self._generate_model_key("hf_label_map", model_name)
            
            # Cache model components separately to avoid large serialization
            model_data = {
                'model_name': model_name,
                'cached_at': datetime.now().isoformat(),
                'model_config': model.config.to_dict() if hasattr(model, 'config') else None
            }
            
            tokenizer_data = {
                'tokenizer_name': model_name,
                'vocab_size': tokenizer.vocab_size if hasattr(tokenizer, 'vocab_size') else None,
                'cached_at': datetime.now().isoformat()
            }
            
            # Store metadata and label map (actual model/tokenizer objects are too large for Redis)
            self.cache.set(model_key, model_data, self.model_ttl)
            self.cache.set(tokenizer_key, tokenizer_data, self.tokenizer_ttl)
            self.cache.set(label_map_key, label_map, self.model_ttl)
            
            logger.info(f"Cached HF model metadata for {model_name}")
            return True
            
        except Exception as e:
            logger.error(f"Error caching HF model {model_name}: {e}")
            return False
    
    def get_cached_hf_model_info(self, model_name: str) -> Optional[Dict[str, Any]]:
        """Get cached Hugging Face model information."""
        model_key = self._generate_model_key("hf_model", model_name)
        return self.cache.get(model_key)
    
    def get_cached_label_map(self, model_name: str) -> Optional[Dict[str, int]]:
        """Get cached label map for a model."""
        label_map_key = self._generate_model_key("hf_label_map", model_name)
        return self.cache.get(label_map_key)
    
    def invalidate_model_cache(self, model_name: str) -> int:
        """Invalidate all cached data for a specific model."""
        pattern = f"model:*:{model_name}:*"
        return self.cache.clear_pattern(pattern)
    
    def _generate_model_key(self, model_type: str, model_name: str) -> str:
        """Generate cache key for model components."""
        return self.cache._generate_cache_key("model", model_type, model_name)


class HierarchyCacheService:
    """Specialized caching service for hierarchy configurations and schemas."""
    
    def __init__(self, cache_service: CacheService):
        self.cache = cache_service
        self.config_ttl = 1800  # 30 minutes
        self.schema_ttl = 3600  # 1 hour
    
    def cache_hierarchy_config(self, config_id: int, config_data: Dict[str, Any]) -> bool:
        """Cache hierarchy configuration data."""
        key = self._generate_config_key("hierarchy_config", config_id)
        return self.cache.set(key, config_data, self.config_ttl)
    
    def get_cached_hierarchy_config(self, config_id: int) -> Optional[Dict[str, Any]]:
        """Get cached hierarchy configuration."""
        key = self._generate_config_key("hierarchy_config", config_id)
        return self.cache.get(key)
    
    def cache_hierarchy_schema(self, config_id: int, schema: HierarchySchema) -> bool:
        """Cache hierarchy schema object."""
        key = self._generate_config_key("hierarchy_schema", config_id)
        schema_dict = {
            'id': schema.id,
            'name': schema.name,
            'levels': schema.levels,
            'depth': schema.depth,
            'validation_rules': schema.validation_rules,
            'ui_config': schema.ui_config,
            'confidence_thresholds': schema.confidence_thresholds,
            'cached_at': datetime.now().isoformat()
        }
        return self.cache.set(key, schema_dict, self.schema_ttl)
    
    def get_cached_hierarchy_schema(self, config_id: int) -> Optional[Dict[str, Any]]:
        """Get cached hierarchy schema."""
        key = self._generate_config_key("hierarchy_schema", config_id)
        return self.cache.get(key)
    
    def invalidate_hierarchy_cache(self, config_id: int) -> int:
        """Invalidate all cached data for a specific hierarchy configuration."""
        pattern = f"hierarchy:*:{config_id}:*"
        return self.cache.clear_pattern(pattern)
    
    def invalidate_user_hierarchy_cache(self, user_id: int) -> int:
        """Invalidate all hierarchy cache for a specific user."""
        pattern = f"hierarchy:*:user:{user_id}:*"
        return self.cache.clear_pattern(pattern)
    
    def _generate_config_key(self, config_type: str, config_id: int) -> str:
        """Generate cache key for hierarchy configurations."""
        return self.cache._generate_cache_key("hierarchy", config_type, config_id)


class QueryCacheService:
    """Specialized caching service for database query results."""
    
    def __init__(self, cache_service: CacheService):
        self.cache = cache_service
        self.query_ttl = 600  # 10 minutes
    
    def cache_query_result(self, query_hash: str, result: Any, ttl: Optional[int] = None) -> bool:
        """Cache database query result."""
        key = self._generate_query_key(query_hash)
        return self.cache.set(key, result, ttl or self.query_ttl)
    
    def get_cached_query_result(self, query_hash: str) -> Optional[Any]:
        """Get cached query result."""
        key = self._generate_query_key(query_hash)
        return self.cache.get(key)
    
    def generate_query_hash(self, query: str, params: Optional[Dict] = None) -> str:
        """Generate hash for a database query."""
        query_data = f"{query}:{json.dumps(params or {}, sort_keys=True)}"
        return hashlib.md5(query_data.encode()).hexdigest()
    
    def _generate_query_key(self, query_hash: str) -> str:
        """Generate cache key for query results."""
        return self.cache._generate_cache_key("query", query_hash)


# Global cache service instances
cache_service = CacheService()
model_cache = ModelCacheService(cache_service)
hierarchy_cache = HierarchyCacheService(cache_service)
query_cache = QueryCacheService(cache_service)


def get_cache_service() -> CacheService:
    """Get the global cache service instance."""
    return cache_service


def get_model_cache() -> ModelCacheService:
    """Get the global model cache service instance."""
    return model_cache


def get_hierarchy_cache() -> HierarchyCacheService:
    """Get the global hierarchy cache service instance."""
    return hierarchy_cache


def get_query_cache() -> QueryCacheService:
    """Get the global query cache service instance."""
    return query_cache
