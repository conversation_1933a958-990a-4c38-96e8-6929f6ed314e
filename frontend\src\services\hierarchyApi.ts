// frontend/src/services/hierarchyApi.ts
import apiClient from './apiClient';
import { 
  HierarchyConfigListResponse, 
  HierarchyConfig, 
  HierarchyConfigCreate, 
  HierarchyConfigUpdate 
} from '../types';

/**
 * Fetches all hierarchy configurations
 * @returns HierarchyConfigListResponse with all configurations
 */
export const getHierarchyConfigs = async (): Promise<HierarchyConfigListResponse> => {
  try {
    const response = await apiClient.get<HierarchyConfigListResponse>('/hierarchy/configs');
    return response.data;
  } catch (error: any) {
    console.error("Error fetching hierarchy configurations:", error);
    throw error;
  }
};

/**
 * Fetches a specific hierarchy configuration by ID
 * @param configId ID of the configuration to fetch
 * @returns HierarchyConfig with the requested configuration
 */
export const getHierarchyConfig = async (configId: number): Promise<HierarchyConfig> => {
  try {
    const response = await apiClient.get<HierarchyConfig>(`/hierarchy/configs/${configId}`);
    return response.data;
  } catch (error: any) {
    console.error(`Error fetching hierarchy configuration ${configId}:`, error);
    throw error;
  }
};

/**
 * Creates a new hierarchy configuration
 * @param config Configuration data to create
 * @returns HierarchyConfig with the created configuration
 */
export const createHierarchyConfig = async (config: HierarchyConfigCreate): Promise<HierarchyConfig> => {
  try {
    const response = await apiClient.post<HierarchyConfig>('/hierarchy/configs', config);
    return response.data;
  } catch (error: any) {
    console.error("Error creating hierarchy configuration:", error);
    throw error;
  }
};

/**
 * Updates an existing hierarchy configuration
 * @param configId ID of the configuration to update
 * @param config Configuration data to update
 * @returns HierarchyConfig with the updated configuration
 */
export const updateHierarchyConfig = async (configId: number, config: HierarchyConfigUpdate): Promise<HierarchyConfig> => {
  try {
    const response = await apiClient.put<HierarchyConfig>(`/hierarchy/configs/${configId}`, config);
    return response.data;
  } catch (error: any) {
    console.error(`Error updating hierarchy configuration ${configId}:`, error);
    throw error;
  }
};

/**
 * Deletes a hierarchy configuration
 * @param configId ID of the configuration to delete
 * @returns void
 */
export const deleteHierarchyConfig = async (configId: number): Promise<void> => {
  try {
    await apiClient.delete(`/hierarchy/configs/${configId}`);
  } catch (error: any) {
    console.error(`Error deleting hierarchy configuration ${configId}:`, error);
    throw error;
  }
};
