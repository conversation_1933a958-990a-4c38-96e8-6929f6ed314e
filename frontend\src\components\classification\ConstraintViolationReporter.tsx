/**
 * ConstraintViolationReporter.tsx
 * 
 * Component for reporting and analyzing constraint violations in hierarchical classification
 */

import React, { useState, useMemo } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ScrollArea } from "@/components/ui/scroll-area";
import { 
  Bar<PERSON>hart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  Pie<PERSON>hart,
  Pie,
  Cell
} from 'recharts';
import { 
  AlertTriangle, 
  Shield, 
  FileText, 
  Download,
  Filter,
  Search,
  Eye,
  AlertCircle,
  CheckCircle2,
  XCircle
} from "lucide-react";

interface ConstraintViolation {
  id: string;
  type: 'invalid_transition' | 'orphaned_node' | 'circular_dependency' | 'duplicate_path' | 'missing_parent';
  severity: 'error' | 'warning';
  message: string;
  
  // Violation details
  predicted_path: string[];
  expected_path?: string[];
  actual_path?: string[];
  
  // Location information
  data_index: number;
  level: number;
  parent_value?: string;
  child_value?: string;
  
  // Context
  confidence: number;
  suggestion?: string;
  auto_fixable: boolean;
}

interface ConstraintViolationSummary {
  total_violations: number;
  violations_by_type: Record<string, number>;
  violations_by_level: Record<string, number>;
  violations_by_severity: Record<string, number>;
  violation_rate: number;
  most_common_violations: Array<{
    type: string;
    count: number;
    percentage: number;
  }>;
}

interface ConstraintViolationReporterProps {
  violations: ConstraintViolation[];
  summary: ConstraintViolationSummary;
  hierarchyLevels: string[];
  onViolationSelect?: (violation: ConstraintViolation) => void;
  onExportReport?: () => void;
  onAutoFix?: (violations: ConstraintViolation[]) => void;
  className?: string;
}

export const ConstraintViolationReporter: React.FC<ConstraintViolationReporterProps> = ({
  violations,
  summary,
  hierarchyLevels,
  onViolationSelect,
  onExportReport,
  onAutoFix,
  className
}) => {
  const [selectedSeverity, setSelectedSeverity] = useState<string>('all');
  const [selectedType, setSelectedType] = useState<string>('all');
  const [selectedLevel, setSelectedLevel] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState('');

  // Filter violations
  const filteredViolations = useMemo(() => {
    return violations.filter(violation => {
      const matchesSeverity = selectedSeverity === 'all' || violation.severity === selectedSeverity;
      const matchesType = selectedType === 'all' || violation.type === selectedType;
      const matchesLevel = selectedLevel === 'all' || violation.level.toString() === selectedLevel;
      const matchesSearch = !searchTerm || 
        violation.message.toLowerCase().includes(searchTerm.toLowerCase()) ||
        violation.predicted_path.some(p => p.toLowerCase().includes(searchTerm.toLowerCase()));

      return matchesSeverity && matchesType && matchesLevel && matchesSearch;
    });
  }, [violations, selectedSeverity, selectedType, selectedLevel, searchTerm]);

  // Chart data
  const violationTypeData = useMemo(() => {
    return Object.entries(summary.violations_by_type).map(([type, count]) => ({
      type: type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
      count,
      percentage: (count / summary.total_violations) * 100
    }));
  }, [summary]);

  const violationLevelData = useMemo(() => {
    return Object.entries(summary.violations_by_level).map(([level, count]) => ({
      level: hierarchyLevels[parseInt(level)] || `Level ${parseInt(level) + 1}`,
      count,
      percentage: (count / summary.total_violations) * 100
    }));
  }, [summary, hierarchyLevels]);

  const getViolationIcon = (violation: ConstraintViolation) => {
    if (violation.severity === 'error') {
      return <XCircle className="w-4 h-4 text-red-600" />;
    }
    return <AlertTriangle className="w-4 h-4 text-yellow-600" />;
  };

  const getViolationColor = (violation: ConstraintViolation) => {
    if (violation.severity === 'error') {
      return 'border-red-200 bg-red-50';
    }
    return 'border-yellow-200 bg-yellow-50';
  };

  const getSeverityBadge = (severity: string) => {
    if (severity === 'error') {
      return <Badge variant="destructive">Error</Badge>;
    }
    return <Badge variant="secondary">Warning</Badge>;
  };

  const getTypeBadge = (type: string) => {
    const typeColors: Record<string, string> = {
      'invalid_transition': 'bg-red-100 text-red-800',
      'orphaned_node': 'bg-orange-100 text-orange-800',
      'circular_dependency': 'bg-purple-100 text-purple-800',
      'duplicate_path': 'bg-blue-100 text-blue-800',
      'missing_parent': 'bg-yellow-100 text-yellow-800'
    };

    return (
      <Badge className={typeColors[type] || 'bg-gray-100 text-gray-800'}>
        {type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
      </Badge>
    );
  };

  const autoFixableViolations = violations.filter(v => v.auto_fixable);

  const COLORS = ['#ef4444', '#f59e0b', '#8b5cf6', '#3b82f6', '#10b981'];

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-red-600">{summary.total_violations}</div>
            <div className="text-sm text-muted-foreground">Total Violations</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-orange-600">
              {(summary.violation_rate * 100).toFixed(1)}%
            </div>
            <div className="text-sm text-muted-foreground">Violation Rate</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-red-600">
              {summary.violations_by_severity.error || 0}
            </div>
            <div className="text-sm text-muted-foreground">Errors</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-green-600">
              {autoFixableViolations.length}
            </div>
            <div className="text-sm text-muted-foreground">Auto-fixable</div>
          </CardContent>
        </Card>
      </div>

      {/* Auto-fix Alert */}
      {autoFixableViolations.length > 0 && (
        <Alert>
          <CheckCircle2 className="h-4 w-4" />
          <AlertDescription className="flex items-center justify-between">
            <span>
              {autoFixableViolations.length} violations can be automatically fixed.
            </span>
            <Button 
              size="sm" 
              onClick={() => onAutoFix?.(autoFixableViolations)}
              className="ml-4"
            >
              Auto-fix Now
            </Button>
          </AlertDescription>
        </Alert>
      )}

      {/* Main Content */}
      <Tabs defaultValue="violations" className="space-y-4">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="violations">Violations List</TabsTrigger>
          <TabsTrigger value="analysis">Analysis</TabsTrigger>
          <TabsTrigger value="reports">Reports</TabsTrigger>
        </TabsList>

        {/* Violations List */}
        <TabsContent value="violations" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <AlertTriangle className="w-5 h-5" />
                Constraint Violations ({filteredViolations.length})
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Filters */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                  <label className="text-sm font-medium">Severity</label>
                  <select 
                    value={selectedSeverity}
                    onChange={(e) => setSelectedSeverity(e.target.value)}
                    className="w-full mt-1 p-2 border rounded"
                  >
                    <option value="all">All Severities</option>
                    <option value="error">Errors</option>
                    <option value="warning">Warnings</option>
                  </select>
                </div>

                <div>
                  <label className="text-sm font-medium">Type</label>
                  <select 
                    value={selectedType}
                    onChange={(e) => setSelectedType(e.target.value)}
                    className="w-full mt-1 p-2 border rounded"
                  >
                    <option value="all">All Types</option>
                    {Object.keys(summary.violations_by_type).map(type => (
                      <option key={type} value={type}>
                        {type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="text-sm font-medium">Level</label>
                  <select 
                    value={selectedLevel}
                    onChange={(e) => setSelectedLevel(e.target.value)}
                    className="w-full mt-1 p-2 border rounded"
                  >
                    <option value="all">All Levels</option>
                    {hierarchyLevels.map((level, index) => (
                      <option key={index} value={index.toString()}>
                        {level}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="text-sm font-medium">Search</label>
                  <input
                    type="text"
                    placeholder="Search violations..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full mt-1 p-2 border rounded"
                  />
                </div>
              </div>

              {/* Violations List */}
              <ScrollArea className="h-96">
                <div className="space-y-3">
                  {filteredViolations.map(violation => (
                    <div
                      key={violation.id}
                      className={`p-4 border rounded-lg cursor-pointer hover:shadow-md transition-shadow ${getViolationColor(violation)}`}
                      onClick={() => onViolationSelect?.(violation)}
                    >
                      <div className="flex items-start justify-between mb-2">
                        <div className="flex items-center gap-2">
                          {getViolationIcon(violation)}
                          <span className="font-medium">Row {violation.data_index + 1}</span>
                          {getSeverityBadge(violation.severity)}
                          {getTypeBadge(violation.type)}
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge variant="outline">
                            Level {violation.level + 1}
                          </Badge>
                          {violation.auto_fixable && (
                            <Badge className="bg-green-100 text-green-800">
                              Auto-fixable
                            </Badge>
                          )}
                        </div>
                      </div>

                      <p className="text-sm mb-2">{violation.message}</p>

                      <div className="space-y-1 text-xs">
                        <div>
                          <strong>Predicted:</strong> {violation.predicted_path.join(' → ')}
                        </div>
                        {violation.expected_path && (
                          <div>
                            <strong>Expected:</strong> {violation.expected_path.join(' → ')}
                          </div>
                        )}
                        {violation.actual_path && (
                          <div>
                            <strong>Actual:</strong> {violation.actual_path.join(' → ')}
                          </div>
                        )}
                        <div>
                          <strong>Confidence:</strong> {(violation.confidence * 100).toFixed(1)}%
                        </div>
                      </div>

                      {violation.suggestion && (
                        <div className="mt-2 p-2 bg-blue-50 border border-blue-200 rounded text-xs">
                          <strong>Suggestion:</strong> {violation.suggestion}
                        </div>
                      )}
                    </div>
                  ))}

                  {filteredViolations.length === 0 && (
                    <div className="text-center text-muted-foreground py-8">
                      No violations match your filter criteria
                    </div>
                  )}
                </div>
              </ScrollArea>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Analysis */}
        <TabsContent value="analysis" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>Violations by Type</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={250}>
                  <PieChart>
                    <Pie
                      data={violationTypeData}
                      cx="50%"
                      cy="50%"
                      outerRadius={80}
                      dataKey="count"
                      label={({ type, percentage }) => `${type}: ${percentage.toFixed(1)}%`}
                    >
                      {violationTypeData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Violations by Level</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={250}>
                  <BarChart data={violationLevelData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="level" />
                    <YAxis />
                    <Tooltip />
                    <Bar dataKey="count" fill="#ef4444" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>

          {/* Most Common Violations */}
          <Card>
            <CardHeader>
              <CardTitle>Most Common Violations</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {summary.most_common_violations.map((violation, index) => (
                  <div key={violation.type} className="flex items-center justify-between p-3 border rounded">
                    <div className="flex items-center gap-3">
                      <Badge variant="outline">#{index + 1}</Badge>
                      <span className="font-medium">
                        {violation.type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="text-sm text-muted-foreground">
                        {violation.count} occurrences
                      </span>
                      <Badge variant="destructive">
                        {violation.percentage.toFixed(1)}%
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Reports */}
        <TabsContent value="reports" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="w-5 h-5" />
                Export Reports
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Button variant="outline" onClick={onExportReport} className="h-auto p-4">
                  <div className="text-left">
                    <div className="font-medium">Detailed Violation Report</div>
                    <div className="text-sm text-muted-foreground">
                      Export complete violation analysis as PDF
                    </div>
                  </div>
                  <Download className="w-5 h-5 ml-auto" />
                </Button>

                <Button variant="outline" className="h-auto p-4">
                  <div className="text-left">
                    <div className="font-medium">Violation Summary</div>
                    <div className="text-sm text-muted-foreground">
                      Export summary statistics as CSV
                    </div>
                  </div>
                  <Download className="w-5 h-5 ml-auto" />
                </Button>
              </div>

              {/* Report Preview */}
              <div className="border rounded p-4 bg-muted/30">
                <h4 className="font-semibold mb-2">Report Preview</h4>
                <div className="text-sm space-y-1">
                  <div>• Total violations: {summary.total_violations}</div>
                  <div>• Violation rate: {(summary.violation_rate * 100).toFixed(1)}%</div>
                  <div>• Most common: {summary.most_common_violations[0]?.type.replace(/_/g, ' ')}</div>
                  <div>• Auto-fixable: {autoFixableViolations.length}</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};
