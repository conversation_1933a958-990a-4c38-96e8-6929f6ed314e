"""Step Executors for Workflow Engine.

This module contains step executors for different types of workflow steps
in the ClassyWeb ML Platform.
"""

import logging
from typing import Dict, Any, Optional, List
from abc import ABC, abstractmethod

logger = logging.getLogger(__name__)


class BaseStepExecutor(ABC):
    """Base class for all step executors."""
    
    def __init__(self, step_type: str):
        self.step_type = step_type
    
    @abstractmethod
    async def execute(self, step_config: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """Execute a workflow step.
        
        Args:
            step_config: Configuration for the step
            context: Workflow execution context
            
        Returns:
            Updated context after step execution
        """
        pass
    
    @abstractmethod
    def validate_config(self, step_config: Dict[str, Any]) -> List[str]:
        """Validate step configuration.
        
        Args:
            step_config: Configuration to validate
            
        Returns:
            List of validation errors (empty if valid)
        """
        pass


class DataLoadStepExecutor(BaseStepExecutor):
    """Executor for data loading steps."""
    
    def __init__(self):
        super().__init__("data_load")
    
    async def execute(self, step_config: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """Execute data loading step."""
        try:
            # Placeholder implementation
            logger.info(f"Executing data load step with config: {step_config}")
            
            # Update context with loaded data info
            context['data_loaded'] = True
            context['data_source'] = step_config.get('source', 'unknown')
            
            return context
        except Exception as e:
            logger.error(f"Error in data load step: {e}")
            context['error'] = str(e)
            return context
    
    def validate_config(self, step_config: Dict[str, Any]) -> List[str]:
        """Validate data load step configuration."""
        errors = []
        
        if 'source' not in step_config:
            errors.append("Data load step requires 'source' parameter")
        
        return errors


class ModelTrainingStepExecutor(BaseStepExecutor):
    """Executor for model training steps."""
    
    def __init__(self):
        super().__init__("model_training")
    
    async def execute(self, step_config: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """Execute model training step."""
        try:
            logger.info(f"Executing model training step with config: {step_config}")
            
            # Placeholder implementation
            context['model_trained'] = True
            context['model_type'] = step_config.get('model_type', 'unknown')
            
            return context
        except Exception as e:
            logger.error(f"Error in model training step: {e}")
            context['error'] = str(e)
            return context
    
    def validate_config(self, step_config: Dict[str, Any]) -> List[str]:
        """Validate model training step configuration."""
        errors = []
        
        if 'model_type' not in step_config:
            errors.append("Model training step requires 'model_type' parameter")
        
        return errors


class ModelEvaluationStepExecutor(BaseStepExecutor):
    """Executor for model evaluation steps."""
    
    def __init__(self):
        super().__init__("model_evaluation")
    
    async def execute(self, step_config: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """Execute model evaluation step."""
        try:
            logger.info(f"Executing model evaluation step with config: {step_config}")
            
            # Placeholder implementation
            context['model_evaluated'] = True
            context['evaluation_metrics'] = step_config.get('metrics', [])
            
            return context
        except Exception as e:
            logger.error(f"Error in model evaluation step: {e}")
            context['error'] = str(e)
            return context
    
    def validate_config(self, step_config: Dict[str, Any]) -> List[str]:
        """Validate model evaluation step configuration."""
        errors = []
        
        if 'metrics' not in step_config:
            errors.append("Model evaluation step requires 'metrics' parameter")
        
        return errors


# Registry of available step executors
STEP_EXECUTORS = {
    'data_load': DataLoadStepExecutor(),
    'model_training': ModelTrainingStepExecutor(),
    'model_evaluation': ModelEvaluationStepExecutor(),
}


def get_step_executor(step_type: str) -> Optional[BaseStepExecutor]:
    """Get step executor for a given step type.
    
    Args:
        step_type: Type of step to execute
        
    Returns:
        Step executor instance or None if not found
    """
    return STEP_EXECUTORS.get(step_type)


def get_available_step_types() -> List[str]:
    """Get list of available step types.
    
    Returns:
        List of available step type names
    """
    return list(STEP_EXECUTORS.keys())


def validate_step_config(step_type: str, step_config: Dict[str, Any]) -> List[str]:
    """Validate configuration for a specific step type.
    
    Args:
        step_type: Type of step
        step_config: Configuration to validate
        
    Returns:
        List of validation errors (empty if valid)
    """
    executor = get_step_executor(step_type)
    if not executor:
        return [f"Unknown step type: {step_type}"]
    
    return executor.validate_config(step_config)
