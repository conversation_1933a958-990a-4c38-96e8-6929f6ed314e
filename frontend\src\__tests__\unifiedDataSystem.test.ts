/**
 * unifiedDataSystem.test.ts
 * 
 * Comprehensive integration tests for the unified data management system
 * covering file upload, reuse scenarios, and workflow integration.
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { unifiedDataManager, DataPurpose } from '../services/unifiedDataManager';
import { unifiedProgressMonitor } from '../services/unifiedProgressMonitor';
import { errorHandler } from '../services/errorHandling';
import { SmartDataPurposeDetection } from '../services/smartDataPurposeDetection';

// Mock dependencies
vi.mock('../services/fileUploadApi', () => ({
  uploadFile: vi.fn().mockResolvedValue({
    file_id: 'test-file-id',
    filename: 'test.csv',
    columns: ['text', 'label'],
    num_rows: 100,
    preview: [
      { text: 'Sample text 1', label: 'positive' },
      { text: 'Sample text 2', label: 'negative' }
    ]
  })
}));

vi.mock('../hooks/use-toast', () => ({
  useToast: () => ({
    toast: vi.fn()
  })
}));

describe('Unified Data Management System', () => {
  beforeEach(() => {
    // Clear all data before each test
    unifiedDataManager.clearAll();
    unifiedProgressMonitor.clearHistory();
    errorHandler.clearResolvedErrors();
    
    // Clear localStorage
    localStorage.clear();
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('UnifiedDataManager', () => {
    it('should upload file with single purpose', async () => {
      const mockFile = new File(['test content'], 'test.csv', { type: 'text/csv' });
      
      const fileId = await unifiedDataManager.uploadFile(mockFile, ['analysis']);
      
      expect(fileId).toBe('test-file-id');
      
      const fileData = unifiedDataManager.getDataForPurpose(fileId, 'analysis');
      expect(fileData).toBeTruthy();
      expect(fileData?.filename).toBe('test.csv');
    });

    it('should upload file with multiple purposes', async () => {
      const mockFile = new File(['test content'], 'test.csv', { type: 'text/csv' });
      
      const fileId = await unifiedDataManager.uploadFile(mockFile, ['analysis', 'training']);
      
      const allFiles = unifiedDataManager.getAllFiles();
      expect(allFiles).toHaveLength(1);
      
      const fileData = allFiles[0];
      expect(fileData.purposes.analysis).toBe(true);
      expect(fileData.purposes.training).toBe(true);
      expect(fileData.purposes.classification).toBe(false);
    });

    it('should reuse file for different purposes', async () => {
      const mockFile = new File(['test content'], 'test.csv', { type: 'text/csv' });
      
      // Upload with analysis purpose
      const fileId = await unifiedDataManager.uploadFile(mockFile, ['analysis']);
      
      // Update to add training purpose
      const success = unifiedDataManager.updatePurposes(fileId, ['analysis', 'training']);
      expect(success).toBe(true);
      
      const fileData = unifiedDataManager.getDataForPurpose(fileId, 'training');
      expect(fileData).toBeTruthy();
    });

    it('should generate smart suggestions', async () => {
      const mockFile = new File(['test content'], 'test.csv', { type: 'text/csv' });
      const fileId = await unifiedDataManager.uploadFile(mockFile, ['analysis']);
      
      const fileData = unifiedDataManager.getDataForPurpose(fileId, 'analysis');
      const suggestions = unifiedDataManager.suggestDataPurposes(fileData!);
      
      expect(suggestions.canUseForTraining).toBe(true);
      expect(suggestions.canUseForClassification).toBe(true);
      expect(suggestions.hasLabels).toBe(true);
      expect(suggestions.suggestedPurposes).toContain('training');
    });

    it('should handle data splitting', async () => {
      const mockFile = new File(['test content'], 'test.csv', { type: 'text/csv' });
      const fileId = await unifiedDataManager.uploadFile(mockFile, ['training']);
      
      const splitResult = unifiedDataManager.splitData(fileId, {
        trainRatio: 0.7,
        validationRatio: 0.2,
        testRatio: 0.1,
        stratify: false,
        randomSeed: 42
      });
      
      expect(splitResult.trainingRows).toHaveLength(70);
      expect(splitResult.validationRows).toHaveLength(20);
      expect(splitResult.testRows).toHaveLength(10);
    });

    it('should persist data to localStorage', async () => {
      const mockFile = new File(['test content'], 'test.csv', { type: 'text/csv' });
      await unifiedDataManager.uploadFile(mockFile, ['analysis']);
      
      // Check localStorage
      const stored = localStorage.getItem('unifiedDataManager');
      expect(stored).toBeTruthy();
      
      const parsedData = JSON.parse(stored!);
      expect(parsedData).toHaveLength(1);
      expect(parsedData[0][1].fileInfo.filename).toBe('test.csv');
    });

    it('should provide usage statistics', async () => {
      const mockFile1 = new File(['content1'], 'test1.csv', { type: 'text/csv' });
      const mockFile2 = new File(['content2'], 'test2.csv', { type: 'text/csv' });
      
      await unifiedDataManager.uploadFile(mockFile1, ['analysis', 'training']);
      await unifiedDataManager.uploadFile(mockFile2, ['classification']);
      
      const stats = unifiedDataManager.getUsageStats();
      
      expect(stats.totalFiles).toBe(2);
      expect(stats.purposeBreakdown.analysis).toBe(1);
      expect(stats.purposeBreakdown.training).toBe(1);
      expect(stats.purposeBreakdown.classification).toBe(1);
    });
  });

  describe('SmartDataPurposeDetection', () => {
    it('should detect binary classification', () => {
      const mockFileInfo = {
        file_id: 'test-id',
        filename: 'binary.csv',
        columns: ['text', 'sentiment'],
        num_rows: 100,
        preview: [
          { text: 'Good product', sentiment: 'positive' },
          { text: 'Bad service', sentiment: 'negative' },
          { text: 'Great experience', sentiment: 'positive' }
        ]
      };

      const analysis = SmartDataPurposeDetection.analyzeFile(mockFileInfo);
      
      expect(analysis.classificationTypeDetection?.type).toBe('binary');
      expect(analysis.canUseForTraining).toBe(true);
      expect(analysis.hasLabels).toBe(true);
    });

    it('should detect multi-class classification', () => {
      const mockFileInfo = {
        file_id: 'test-id',
        filename: 'multiclass.csv',
        columns: ['text', 'category'],
        num_rows: 150,
        preview: [
          { text: 'Sports news', category: 'sports' },
          { text: 'Tech update', category: 'technology' },
          { text: 'Weather report', category: 'weather' },
          { text: 'Movie review', category: 'entertainment' }
        ]
      };

      const analysis = SmartDataPurposeDetection.analyzeFile(mockFileInfo);
      
      expect(analysis.classificationTypeDetection?.type).toBe('multiclass');
      expect(analysis.canUseForTraining).toBe(true);
    });

    it('should calculate quality metrics', () => {
      const mockFileInfo = {
        file_id: 'test-id',
        filename: 'quality.csv',
        columns: ['text', 'label', 'id'],
        num_rows: 200,
        preview: Array.from({ length: 10 }, (_, i) => ({
          text: `Sample text ${i}`,
          label: i % 2 === 0 ? 'positive' : 'negative',
          id: i
        }))
      };

      const analysis = SmartDataPurposeDetection.analyzeFile(mockFileInfo);
      
      expect(analysis.qualityMetrics.overall).toBeGreaterThan(0);
      expect(analysis.qualityMetrics.completeness).toBeGreaterThan(90);
    });

    it('should provide optimization recommendations', () => {
      const mockFileInfo = {
        file_id: 'test-id',
        filename: 'small.csv',
        columns: ['text', 'label'],
        num_rows: 50, // Small dataset
        preview: [
          { text: 'Sample 1', label: 'A' },
          { text: 'Sample 2', label: 'A' }
        ]
      };

      const analysis = SmartDataPurposeDetection.analyzeFile(mockFileInfo);
      
      expect(analysis.optimizationRecommendations).toContain(
        expect.stringContaining('Consider collecting more data')
      );
    });
  });

  describe('Progress Monitoring', () => {
    it('should track upload progress', () => {
      const operationId = 'test-upload';
      
      unifiedProgressMonitor.startOperation(operationId, 'upload', 'Starting upload');
      unifiedProgressMonitor.updateProgress(operationId, 50, 'Uploading...');
      unifiedProgressMonitor.completeOperation(operationId, 'Upload complete');
      
      const history = unifiedProgressMonitor.getHistory();
      expect(history).toHaveLength(1);
      expect(history[0].status).toBe('completed');
      expect(history[0].progress).toBe(100);
    });

    it('should handle operation failures', () => {
      const operationId = 'test-fail';
      
      unifiedProgressMonitor.startOperation(operationId, 'upload', 'Starting upload');
      unifiedProgressMonitor.failOperation(operationId, {
        code: 'UPLOAD_FAILED',
        message: 'Network error'
      });
      
      const history = unifiedProgressMonitor.getHistory();
      expect(history).toHaveLength(1);
      expect(history[0].status).toBe('error');
      expect(history[0].error?.code).toBe('UPLOAD_FAILED');
    });

    it('should provide statistics', () => {
      unifiedProgressMonitor.startOperation('op1', 'upload', 'Upload 1');
      unifiedProgressMonitor.completeOperation('op1');
      
      unifiedProgressMonitor.startOperation('op2', 'upload', 'Upload 2');
      unifiedProgressMonitor.failOperation('op2', { code: 'ERROR', message: 'Failed' });
      
      const stats = unifiedProgressMonitor.getStatistics();
      expect(stats.completed).toBe(1);
      expect(stats.failed).toBe(1);
      expect(stats.successRate).toBe(50);
    });
  });

  describe('Error Handling', () => {
    it('should classify network errors', async () => {
      const networkError = new Error('Network request failed');
      networkError.name = 'NetworkError';
      
      const report = await errorHandler.handleError(networkError, {
        operation: 'file_upload',
        component: 'test'
      });
      
      expect(report.error.code).toBe('NETWORK_ERROR');
      expect(report.error.retryable).toBe(true);
      expect(report.recoveryActions).toHaveLength(2); // Retry + fallback
    });

    it('should classify file upload errors', async () => {
      const sizeError = new Error('File size exceeds limit');
      
      const report = await errorHandler.handleError(sizeError, {
        operation: 'file_upload',
        component: 'test',
        fileName: 'large-file.csv'
      });
      
      expect(report.error.code).toBe('FILE_TOO_LARGE');
      expect(report.error.recoverable).toBe(false);
      expect(report.error.retryable).toBe(false);
    });

    it('should provide appropriate recovery actions', async () => {
      const validationError = new Error('Invalid file format');
      validationError.name = 'ValidationError';
      
      const report = await errorHandler.handleError(validationError, {
        operation: 'file_validation',
        component: 'test'
      });
      
      expect(report.recoveryActions.length).toBeGreaterThan(0);
      expect(report.recoveryActions[0].type).toBe('fallback');
    });
  });

  describe('Integration Scenarios', () => {
    it('should handle complete workflow: upload -> analyze -> reuse', async () => {
      // Step 1: Upload file for analysis
      const mockFile = new File(['test content'], 'workflow.csv', { type: 'text/csv' });
      const fileId = await unifiedDataManager.uploadFile(mockFile, ['analysis']);
      
      // Step 2: Get analysis suggestions
      const fileData = unifiedDataManager.getDataForPurpose(fileId, 'analysis');
      const suggestions = unifiedDataManager.suggestDataPurposes(fileData!);
      
      expect(suggestions.canUseForTraining).toBe(true);
      
      // Step 3: Reuse for training
      const success = unifiedDataManager.updatePurposes(fileId, ['analysis', 'training']);
      expect(success).toBe(true);
      
      // Step 4: Verify reuse
      const trainingData = unifiedDataManager.getDataForPurpose(fileId, 'training');
      expect(trainingData).toBeTruthy();
      expect(trainingData?.file_id).toBe(fileId);
    });

    it('should handle multiple files with different purposes', async () => {
      const file1 = new File(['training data'], 'train.csv', { type: 'text/csv' });
      const file2 = new File(['test data'], 'test.csv', { type: 'text/csv' });
      
      const trainFileId = await unifiedDataManager.uploadFile(file1, ['training']);
      const testFileId = await unifiedDataManager.uploadFile(file2, ['classification']);
      
      const allFiles = unifiedDataManager.getAllFiles();
      expect(allFiles).toHaveLength(2);
      
      const stats = unifiedDataManager.getUsageStats();
      expect(stats.purposeBreakdown.training).toBe(1);
      expect(stats.purposeBreakdown.classification).toBe(1);
    });

    it('should handle error recovery in upload workflow', async () => {
      // Mock upload failure
      const { uploadFile } = await import('../services/fileUploadApi');
      vi.mocked(uploadFile).mockRejectedValueOnce(new Error('Network error'));
      
      const mockFile = new File(['test'], 'error.csv', { type: 'text/csv' });
      
      await expect(
        unifiedDataManager.uploadFile(mockFile, ['analysis'])
      ).rejects.toThrow();
      
      // Verify error was handled
      const errorReports = errorHandler.getErrorReports();
      expect(errorReports.length).toBeGreaterThan(0);
    });

    it('should maintain data consistency across browser sessions', async () => {
      // Upload file
      const mockFile = new File(['persistent'], 'persist.csv', { type: 'text/csv' });
      const fileId = await unifiedDataManager.uploadFile(mockFile, ['analysis']);
      
      // Simulate page reload by creating new manager instance
      const newManager = (unifiedDataManager as any).constructor.getInstance();
      
      // Data should be restored from localStorage
      const restoredFiles = newManager.getAllFiles();
      expect(restoredFiles).toHaveLength(1);
      expect(restoredFiles[0].fileInfo.file_id).toBe(fileId);
    });
  });

  describe('Performance and Scalability', () => {
    it('should handle multiple concurrent uploads', async () => {
      const files = Array.from({ length: 5 }, (_, i) => 
        new File([`content ${i}`], `file${i}.csv`, { type: 'text/csv' })
      );
      
      const uploadPromises = files.map(file => 
        unifiedDataManager.uploadFile(file, ['analysis'])
      );
      
      const fileIds = await Promise.all(uploadPromises);
      
      expect(fileIds).toHaveLength(5);
      expect(new Set(fileIds).size).toBe(5); // All unique IDs
      
      const allFiles = unifiedDataManager.getAllFiles();
      expect(allFiles).toHaveLength(5);
    });

    it('should efficiently manage large file lists', async () => {
      // Upload many files
      const uploadPromises = Array.from({ length: 20 }, (_, i) => {
        const file = new File([`content ${i}`], `file${i}.csv`, { type: 'text/csv' });
        return unifiedDataManager.uploadFile(file, ['analysis']);
      });
      
      await Promise.all(uploadPromises);
      
      // Operations should remain fast
      const start = performance.now();
      const stats = unifiedDataManager.getUsageStats();
      const allFiles = unifiedDataManager.getAllFiles();
      const end = performance.now();
      
      expect(end - start).toBeLessThan(100); // Should complete in < 100ms
      expect(stats.totalFiles).toBe(20);
      expect(allFiles).toHaveLength(20);
    });
  });
});
