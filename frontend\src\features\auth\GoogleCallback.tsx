// frontend/src/features/auth/GoogleCallback.tsx
import React, { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, CheckCircle2, XCircle } from 'lucide-react';
import { googleAuth, getCurrentUser } from '../../services/authApi';
import { useAuthStore } from '../../store/authStore';

// Helper function to detect if a date is in Daylight Saving Time
const isDaylightSavingTime = (date: Date): boolean => {
  // Create two dates: one in January (standard time) and the current date
  const jan = new Date(date.getFullYear(), 0, 1);
  const jul = new Date(date.getFullYear(), 6, 1); // July for southern hemisphere

  // For northern hemisphere: if current offset is less than January offset, it's DST
  // For southern hemisphere: if current offset is greater than July offset, it's DST
  // Note: getTimezoneOffset returns minutes, and a smaller number means a later timezone

  // First check if there's any DST in this timezone at all
  if (jan.getTimezoneOffset() === jul.getTimezoneOffset()) {
    return false; // No DST in this timezone
  }

  // Northern hemisphere DST
  if (jan.getTimezoneOffset() > jul.getTimezoneOffset()) {
    return date.getTimezoneOffset() < jan.getTimezoneOffset();
  }
  // Southern hemisphere DST
  else {
    return date.getTimezoneOffset() > jul.getTimezoneOffset();
  }
};

const GoogleCallback: React.FC = () => {
  const [searchParams] = useSearchParams();
  const code = searchParams.get('code');
  const error = searchParams.get('error');

  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  const navigate = useNavigate();
  const { login, setUser } = useAuthStore();

  // Use a ref to track if we've already processed this code
  const [codeProcessed, setCodeProcessed] = useState(false);

  useEffect(() => {
    const handleGoogleCallback = async () => {
      // If we've already processed this code, don't do it again
      if (codeProcessed) {
        console.log('Auth code already processed, skipping');
        return;
      }

      // If there's an error parameter in the URL
      if (error) {
        setStatus('error');
        setErrorMessage(`Google authentication was canceled or failed: ${error}`);
        return;
      }

      // If there's no code parameter in the URL
      if (!code) {
        setStatus('error');
        setErrorMessage('Invalid callback: No authorization code received.');
        return;
      }

      // Mark this code as processed to prevent duplicate requests
      setCodeProcessed(true);

      // Log detailed timezone and time information for debugging
      const now = new Date();

      // Format the offset string properly with leading zeros and minutes
      const offsetMinutes = now.getTimezoneOffset();
      const offsetHours = Math.floor(Math.abs(offsetMinutes) / 60);
      const offsetMins = Math.abs(offsetMinutes) % 60;
      const offsetSign = offsetMinutes > 0 ? '-' : '+';
      const offsetStr = `UTC${offsetSign}${offsetHours.toString().padStart(2, '0')}:${offsetMins.toString().padStart(2, '0')}`;

      const tzInfo = {
        timestamp: now.toISOString(),
        localTime: now.toString(),
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
        offset: offsetMinutes,
        offsetStr: offsetStr,
        isDST: isDaylightSavingTime(now),
        codeLength: code.length,
        browserInfo: navigator.userAgent
      };

      // Always log this information to help with debugging
      console.log('Google OAuth callback timezone info:', tzInfo);
      console.log(`Received auth code (length: ${code.length}) at ${now.toISOString()}`);
      console.log(`Local timezone: ${tzInfo.timezone} (${tzInfo.offsetStr}), DST: ${tzInfo.isDST ? 'active' : 'inactive'}`);
      console.log(`Browser: ${navigator.userAgent}`);

      try {
        // Show a more detailed loading message
        setStatus('loading');

        // Exchange the code for a token with timezone information
        console.log('Sending authentication request to backend...');
        const response = await googleAuth({ code, tzInfo });
        console.log('Received successful response from backend');

        // Login with the token
        login(response);

        // Immediately fetch user data after login
        try {
          const userData = await getCurrentUser();
          console.log('User data fetched after Google login:', userData);
          setUser(userData);
        } catch (userError) {
          console.error('Error fetching user data after Google login:', userError);
          // Continue with the flow even if user data fetch fails
        }

        setStatus('success');

        // Redirect to home page after a short delay
        setTimeout(() => {
          navigate('/');
        }, 1000);
      } catch (err: any) {
        console.error('Google auth error:', err);
        setStatus('error');

        // Extract the most useful error message
        let errorMsg = 'Failed to authenticate with Google. Please try again.';
        let errorDetails = '';

        if (err.response) {
          // Server responded with an error
          const status = err.response.status;
          console.log(`Server responded with status ${status}:`, err.response.data);

          if (err.response.data?.detail) {
            errorMsg = err.response.data.detail;
          }

          // Add time synchronization hint if it looks like a time issue
          if (errorMsg.includes('time') || errorMsg.includes('synchronization') ||
              errorMsg.includes('invalid_grant') || status === 400) {
            errorDetails = '\n\nThis might be due to a time synchronization issue between your device and our servers. ' +
                          'Please check that your system clock is accurate.';
          }
        } else if (err.detail) {
          errorMsg = err.detail;
        } else if (err.message) {
          errorMsg = err.message;
        } else if (typeof err === 'string') {
          errorMsg = err;
        }

        setErrorMessage(errorMsg + errorDetails);
      }
    };

    handleGoogleCallback();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [code, error, navigate, codeProcessed]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-background p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <CardTitle className="text-2xl font-bold">Google Sign In</CardTitle>
        </CardHeader>

        <CardContent className="space-y-4 text-center">
          {status === 'loading' && (
            <div className="space-y-4 py-8">
              <Loader2 className="h-8 w-8 animate-spin mx-auto" />
              <div className="space-y-2">
                <p className="text-base">Completing your sign in with Google...</p>
                <p className="text-sm text-muted-foreground">
                  This may take a few moments. Please don't close this window.
                </p>
              </div>
            </div>
          )}

          {status === 'success' && (
            <div className="space-y-4">
              <Alert>
                <CheckCircle2 className="h-4 w-4" />
                <AlertDescription>Successfully signed in with Google!</AlertDescription>
              </Alert>
              <p>Redirecting you to the application...</p>
            </div>
          )}

          {status === 'error' && (
            <div className="space-y-4">
              <Alert variant="destructive">
                <XCircle className="h-4 w-4" />
                <AlertDescription>
                  {errorMessage || 'An error occurred during Google sign in.'}</AlertDescription>
              </Alert>
              <div className="space-y-2">
                <p className="text-sm">You can try:</p>
                <ul className="text-left text-sm space-y-1 list-disc list-inside">
                  <li>Checking your internet connection</li>
                  <li>Verifying your system clock is accurate</li>
                  <li>Clearing your browser cookies</li>
                  <li>Trying again in a few minutes</li>
                </ul>
              </div>
              <Button
                variant="outline"
                onClick={() => navigate('/login')}
              >
                Return to Login
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default GoogleCallback;
