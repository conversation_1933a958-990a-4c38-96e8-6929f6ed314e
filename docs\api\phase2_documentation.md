# ClassyWeb ML Platform Phase 2 API Documentation

## Overview

Phase 2 introduces enhanced binary and multi-class classification engines with advanced features including threshold optimization, strategy selection, comprehensive metrics, and real-time monitoring via WebSocket connections.

## Enhanced Classification Engines

### Binary Classification Engine

The enhanced binary classification engine provides production-ready binary classification with advanced threshold optimization and class imbalance handling.

#### Key Features

- **Threshold Optimization**: 5 optimization strategies (Youden, F1-optimal, precision-recall balance, cost-sensitive, ROC-optimal)
- **Class Imbalance Handling**: SMOTE, class weights, undersampling, oversampling
- **Advanced Metrics**: ROC curves, precision-recall curves, comprehensive confusion matrix analysis
- **GPU Monitoring**: Real-time GPU utilization and memory tracking

#### API Endpoints

##### Get Binary Engine Information
```http
GET /api/v2/classification/engines/binary
```

**Response:**
```json
{
  "engine_type": "binary",
  "supported_methods": ["custom", "llm"],
  "default_metrics": ["accuracy", "precision", "recall", "f1", "auc_roc", "auc_pr", "sensitivity", "specificity"],
  "threshold_strategies": ["youden", "f1_optimal", "precision_recall_balance", "cost_sensitive", "roc_optimal"],
  "imbalance_strategies": ["class_weights", "smote", "undersampling", "oversampling"]
}
```

##### Train Binary Classification Model
```http
POST /api/v2/classification/train
Content-Type: multipart/form-data
```

**Parameters:**
- `file`: Dataset file (CSV, Excel, JSON)
- `config_id`: Configuration ID from `/api/v2/classification/configs`
- `session_name`: Training session name

**Response:**
```json
{
  "session_id": "session_123",
  "status": "running",
  "progress_percentage": 0,
  "estimated_duration": 300,
  "websocket_url": "/ws/training/session_123"
}
```

### Multi-class Classification Engine

The enhanced multi-class classification engine supports multiple classification strategies and provides detailed per-class analysis.

#### Key Features

- **Strategy Selection**: Softmax, One-vs-Rest, One-vs-One with automatic recommendation
- **Confusion Matrix Analysis**: Interactive heatmaps with per-class metrics
- **Performance Benchmarking**: Comparative analysis across strategies
- **Top-k Accuracy**: Support for top-2, top-3, top-5 accuracy metrics

#### API Endpoints

##### Get Multi-class Engine Information
```http
GET /api/v2/classification/engines/multiclass
```

**Response:**
```json
{
  "engine_type": "multiclass",
  "supported_methods": ["custom", "llm"],
  "default_metrics": ["accuracy", "macro_f1", "weighted_f1", "balanced_accuracy", "top_2_accuracy"],
  "classification_strategies": ["softmax", "one_vs_rest", "one_vs_one"],
  "strategy_recommendations": {
    "softmax": "Large datasets with balanced classes",
    "one_vs_rest": "Imbalanced datasets",
    "one_vs_one": "Small datasets with complex boundaries"
  }
}
```

## Real-time Monitoring via WebSocket

### WebSocket Endpoints

#### Training Session Monitoring
```
WS /ws/training/{session_id}?token={auth_token}
```

**Message Types:**

**Client → Server:**
```json
{
  "type": "ping",
  "timestamp": 1640995200000
}
```

```json
{
  "type": "request_status"
}
```

**Server → Client:**
```json
{
  "type": "training_progress",
  "session_id": "session_123",
  "status": "running",
  "progress_percentage": 45.2,
  "current_stage": "epoch_2_step_150",
  "progress_data": {
    "current_epoch": 2,
    "current_step": 150,
    "current_loss": 0.234,
    "learning_rate": 1.5e-5
  },
  "system_metrics": {
    "cpu_percent": 75.2,
    "memory_percent": 68.1,
    "gpu_metrics": [{
      "utilization": 92.3,
      "memory_percent": 78.5,
      "temperature": 72
    }]
  }
}
```

```json
{
  "type": "training_complete",
  "session_id": "session_123",
  "status": "completed",
  "final_metrics": {
    "accuracy": 0.924,
    "f1": 0.918,
    "auc_roc": 0.967,
    "training_time": 245.6,
    "confusion_matrix_analysis": {...},
    "threshold_optimization": {...}
  }
}
```

#### System Monitoring
```
WS /ws/system?token={auth_token}
```

**Server → Client:**
```json
{
  "type": "system_metrics",
  "metrics": {
    "cpu_percent": 45.2,
    "memory_percent": 62.1,
    "memory_used_gb": 12.4,
    "memory_total_gb": 32.0,
    "gpu_metrics": [{
      "id": 0,
      "name": "NVIDIA RTX 4090",
      "utilization": 85.3,
      "memory_used": 18432,
      "memory_total": 24576,
      "memory_percent": 75.0,
      "temperature": 68
    }]
  }
}
```

## Configuration Management

### Create Training Configuration
```http
POST /api/v2/classification/configs
Content-Type: application/json
```

**Request Body:**
```json
{
  "name": "Binary Sentiment Analysis",
  "classification_type": "binary",
  "training_method": "custom",
  "text_columns": ["review_text"],
  "label_columns": ["sentiment"],
  "base_model": "distilbert-base-uncased",
  "learning_rate": 2e-5,
  "batch_size": 16,
  "num_epochs": 3,
  "validation_split": 0.2,
  "use_unsloth": true,
  "metadata": {
    "threshold_strategy": "youden",
    "imbalance_strategy": "class_weights",
    "classification_strategy": "softmax"
  }
}
```

**Response:**
```json
{
  "id": "config_456",
  "name": "Binary Sentiment Analysis",
  "classification_type": "binary",
  "created_at": "2024-01-15T10:30:00Z",
  "validation_result": {
    "is_valid": true,
    "errors": []
  }
}
```

## Data Analysis and Type Detection

### Automatic Type Detection
```http
POST /api/v2/classification/detect-type
Content-Type: multipart/form-data
```

**Parameters:**
- `file`: Dataset file
- `request_data`: JSON with optional column hints

**Response:**
```json
{
  "classification_type": "binary",
  "confidence": 0.95,
  "data_characteristics": {
    "total_samples": 10000,
    "num_classes": 2,
    "class_names": ["positive", "negative"],
    "class_distribution": {
      "positive": 7000,
      "negative": 3000
    },
    "imbalance_analysis": {
      "imbalance_ratio": 2.33,
      "severity": "moderate",
      "majority_class": "positive",
      "minority_class": "negative"
    },
    "text_stats": {
      "avg_length": 156.7,
      "max_length": 512,
      "min_length": 10
    }
  },
  "recommendations": {
    "text_columns": ["review_text"],
    "label_columns": ["sentiment"],
    "threshold_strategy": "youden",
    "imbalance_strategy": "class_weights",
    "classification_strategy": "softmax"
  }
}
```

## Performance Metrics and Benchmarking

### Get Training Session Metrics
```http
GET /api/v2/classification/sessions/{session_id}/metrics
```

**Response:**
```json
{
  "session_id": "session_123",
  "training_metrics": {
    "duration": 245.6,
    "final_loss": 0.089,
    "best_accuracy": 0.924,
    "convergence_epoch": 2
  },
  "system_metrics": {
    "peak_memory_gb": 14.2,
    "avg_gpu_utilization": 87.3,
    "avg_cpu_utilization": 65.1
  },
  "performance_benchmark": {
    "throughput_samples_per_sec": 156.7,
    "inference_time_ms": 12.4,
    "model_size_mb": 267.8
  }
}
```

### Get Performance Benchmarks
```http
GET /api/v2/classification/benchmarks?classification_type=binary
```

**Response:**
```json
{
  "classification_type": "binary",
  "total_models": 15,
  "benchmarks": {
    "training_time": {
      "avg": 180.5,
      "min": 45.2,
      "max": 420.1
    },
    "inference_time": {
      "avg": 8.7,
      "min": 3.2,
      "max": 15.4
    },
    "accuracy": {
      "avg": 0.887,
      "min": 0.756,
      "max": 0.967
    }
  }
}
```

## Error Handling

All API endpoints return standardized error responses:

```json
{
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid configuration parameters",
    "details": {
      "field": "batch_size",
      "reason": "Must be a positive integer"
    },
    "timestamp": "2024-01-15T10:30:00Z"
  }
}
```

### Common Error Codes

- `VALIDATION_ERROR`: Invalid request parameters
- `AUTHENTICATION_ERROR`: Invalid or missing authentication
- `RESOURCE_NOT_FOUND`: Requested resource doesn't exist
- `TRAINING_ERROR`: Error during model training
- `SYSTEM_ERROR`: Internal server error
- `RATE_LIMIT_EXCEEDED`: Too many requests

## Rate Limiting

API endpoints are rate-limited based on user tier:

- **Free Tier**: 100 requests/hour
- **Pro Tier**: 1000 requests/hour
- **Enterprise Tier**: Unlimited

Rate limit headers are included in responses:
```
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 847
X-RateLimit-Reset: 1640998800
```

## Authentication

All API endpoints require authentication via Bearer token:

```http
Authorization: Bearer your_jwt_token_here
```

WebSocket connections require token as query parameter:
```
wss://api.classyweb.ai/ws/training/session_123?token=your_jwt_token_here
```

## SDK Examples

### Python SDK Usage

```python
from classyweb import ClassyWebClient

# Initialize client
client = ClassyWebClient(api_key="your_api_key")

# Upload and analyze data
analysis = client.analyze_data("dataset.csv")
print(f"Detected type: {analysis.classification_type}")

# Create configuration
config = client.create_config(
    name="Sentiment Analysis",
    classification_type="binary",
    text_columns=["text"],
    label_columns=["sentiment"]
)

# Start training with real-time monitoring
session = client.train_model("dataset.csv", config.id)

# Monitor progress
for update in client.monitor_training(session.id):
    print(f"Progress: {update.progress_percentage}%")
    if update.status == "completed":
        break

# Get final results
results = client.get_training_results(session.id)
print(f"Final accuracy: {results.metrics.accuracy}")
```

### JavaScript SDK Usage

```javascript
import { ClassyWebClient } from '@classyweb/sdk';

// Initialize client
const client = new ClassyWebClient({ apiKey: 'your_api_key' });

// Upload and analyze data
const analysis = await client.analyzeData('dataset.csv');
console.log(`Detected type: ${analysis.classificationType}`);

// Create configuration
const config = await client.createConfig({
  name: 'Sentiment Analysis',
  classificationType: 'binary',
  textColumns: ['text'],
  labelColumns: ['sentiment']
});

// Start training with WebSocket monitoring
const session = await client.trainModel('dataset.csv', config.id);

// Monitor via WebSocket
client.monitorTraining(session.id, (update) => {
  console.log(`Progress: ${update.progressPercentage}%`);
  if (update.status === 'completed') {
    console.log('Training completed!');
  }
});
```
