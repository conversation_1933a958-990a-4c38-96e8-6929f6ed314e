/**
 * Multi-Class Model Management API
 * 
 * Provides API functions for managing multi-class classification models,
 * following the pattern established by the hierarchical model API.
 */

import { apiClient } from './apiClient';

export interface MultiClassModel {
  id: string;
  name: string;
  base_model: string;
  created_at: string;
  status: 'training' | 'completed' | 'failed' | 'deployed';
  
  // Training configuration
  training_config: {
    epochs: number;
    batch_size: number;
    learning_rate: number;
    strategy: 'softmax' | 'ovr' | 'ovo' | 'auto';
    class_weight_strategy: string;
    loss_function: string;
    validation_split: number;
    use_unsloth: boolean;
  };
  
  // Performance metrics
  metrics: {
    accuracy?: number;
    f1_score?: number;
    precision?: number;
    recall?: number;
    macro_avg?: {
      precision: number;
      recall: number;
      f1_score: number;
    };
    weighted_avg?: {
      precision: number;
      recall: number;
      f1_score: number;
    };
    per_class_metrics?: Array<{
      class: string;
      precision: number;
      recall: number;
      f1_score: number;
      support: number;
    }>;
    confusion_matrix?: number[][];
    training_time?: number;
  };
  
  // Multi-class specific metadata
  metadata: {
    num_classes: number;
    class_names: string[];
    strategy_used: string;
    total_samples: number;
    model_size: number;
    description?: string;
    class_distribution?: Record<string, number>;
  };
}

export interface ListModelsParams {
  page?: number;
  page_size?: number;
  sort_by?: 'name' | 'created_at' | 'accuracy' | 'f1_score';
  sort_order?: 'asc' | 'desc';
  status_filter?: 'all' | 'training' | 'completed' | 'failed' | 'deployed';
  search?: string;
}

export interface ListModelsResponse {
  models: MultiClassModel[];
  total: number;
  page: number;
  page_size: number;
  total_pages: number;
}

export interface ModelSelectionRequest {
  model_id: string;
  purpose: 'inference' | 'comparison' | 'deployment';
}

export interface ModelSelectionResponse {
  success: boolean;
  model: MultiClassModel;
  message: string;
}

/**
 * List multi-class models with filtering and pagination
 */
export const listMultiClassModels = async (params: ListModelsParams = {}): Promise<ListModelsResponse> => {
  const queryParams = new URLSearchParams();
  
  if (params.page) queryParams.append('page', params.page.toString());
  if (params.page_size) queryParams.append('page_size', params.page_size.toString());
  if (params.sort_by) queryParams.append('sort_by', params.sort_by);
  if (params.sort_order) queryParams.append('sort_order', params.sort_order);
  if (params.status_filter && params.status_filter !== 'all') {
    queryParams.append('status_filter', params.status_filter);
  }
  if (params.search) queryParams.append('search', params.search);

  const response = await apiClient.get(`/api/models/multiclass?${queryParams.toString()}`);
  return response.data;
};

/**
 * Get detailed information about a specific multi-class model
 */
export const getMultiClassModel = async (modelId: string): Promise<MultiClassModel> => {
  const response = await apiClient.get(`/api/models/multiclass/${modelId}`);
  return response.data;
};

/**
 * Select a multi-class model for use in classification
 */
export const selectMultiClassModel = async (request: ModelSelectionRequest): Promise<ModelSelectionResponse> => {
  const response = await apiClient.post('/api/models/multiclass/select', request);
  return response.data;
};

/**
 * Delete a multi-class model
 */
export const deleteMultiClassModel = async (modelId: string): Promise<{ success: boolean; message: string }> => {
  const response = await apiClient.delete(`/api/models/multiclass/${modelId}`);
  return response.data;
};

/**
 * Get model comparison data for multiple models
 */
export const compareMultiClassModels = async (modelIds: string[]): Promise<{
  models: MultiClassModel[];
  comparison_metrics: Record<string, any>;
}> => {
  const response = await apiClient.post('/api/models/multiclass/compare', { model_ids: modelIds });
  return response.data;
};

/**
 * Get model training history and logs
 */
export const getModelTrainingHistory = async (modelId: string): Promise<{
  training_logs: Array<{
    timestamp: string;
    epoch: number;
    loss: number;
    accuracy: number;
    val_loss?: number;
    val_accuracy?: number;
  }>;
  training_config: any;
  final_metrics: any;
}> => {
  const response = await apiClient.get(`/api/models/multiclass/${modelId}/history`);
  return response.data;
};

/**
 * Export model for deployment
 */
export const exportMultiClassModel = async (modelId: string, format: 'onnx' | 'pytorch' | 'tensorflow'): Promise<{
  download_url: string;
  expires_at: string;
}> => {
  const response = await apiClient.post(`/api/models/multiclass/${modelId}/export`, { format });
  return response.data;
};
