/**
 * HierarchicalWorkflow.tsx
 *
 * Hierarchical classification workflow page - streamlined unified implementation
 */

import React from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { HierarchicalWorkflow as HierarchicalWorkflowComponent } from "@/components/classification/HierarchicalWorkflow";
import { toast } from "@/hooks/use-toast";

const HierarchicalWorkflow = () => {
  const location = useLocation();
  const navigate = useNavigate();

  // Get data passed from expert workflow
  const initialData = location.state?.data;
  const fromExpertWorkflow = location.state?.fromExpertWorkflow;

  const handleComplete = (results: any) => {
    console.log('Hierarchical classification workflow completed:', results);

    toast({
      title: "Workflow Complete",
      description: "Hierarchical classification has been completed successfully!"
    });

    // Navigate to results page or back to expert workflow
    if (fromExpertWorkflow) {
      navigate('/expert', {
        state: {
          completedWorkflow: 'hierarchical',
          results: results
        }
      });
    } else {
      navigate('/dashboard', {
        state: {
          completedWorkflow: 'hierarchical',
          results: results
        }
      });
    }
  };

  return (
    <HierarchicalWorkflowComponent
      initialData={initialData}
      onComplete={handleComplete}
    />
  );
};

export default HierarchicalWorkflow;
