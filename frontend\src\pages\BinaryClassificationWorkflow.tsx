/**
 * BinaryClassificationWorkflow.tsx
 *
 * Binary classification workflow page - streamlined unified implementation
 */

import { BinaryClassificationWorkflow as BinaryWorkflowComponent } from "@/components/classification/BinaryClassificationWorkflow";

const BinaryClassificationWorkflow = () => {
  return (
    <BinaryWorkflowComponent
      onComplete={(results) => {
        console.log('Binary classification workflow completed:', results);
        // Handle completion - could navigate to results page or show success message
      }}
    />
  );
};

export default BinaryClassificationWorkflow;