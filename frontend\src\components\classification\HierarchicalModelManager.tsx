import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Separator } from "@/components/ui/separator";
import {
  Brain,
  Clock,
  Target,
  Download,
  Play,
  Trash2,
  Search,
  Filter,
  SortAsc,
  SortDesc,
  Calendar,
  Zap,
  CheckCircle2,
  AlertCircle,
  Info,
  Loader2
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { formatDistanceToNow } from 'date-fns';
import {
  listHierarchicalModels,
  deleteHierarchicalModel,
  HierarchicalModel as ApiHierarchicalModel,
  ListModelsParams
} from "@/services/hierarchicalModelApi";

// Use the API interface but create a local alias for compatibility
export type HierarchicalModel = ApiHierarchicalModel;

interface HierarchicalModelManagerProps {
  onModelSelect: (model: HierarchicalModel) => void;
  onModelUse: (modelId: string) => void;
  onModelDelete: (modelId: string) => void;
  selectedModelId?: string;
  showActions?: boolean;
}

// Helper function to map API status to component status
const mapApiStatus = (apiStatus: string): 'training' | 'completed' | 'failed' | 'ready' => {
  switch (apiStatus.toLowerCase()) {
    case 'completed':
      return 'completed';
    case 'training':
    case 'in_progress':
      return 'training';
    case 'failed':
    case 'error':
      return 'failed';
    default:
      return 'ready';
  }
};

export const HierarchicalModelManager: React.FC<HierarchicalModelManagerProps> = ({
  onModelSelect,
  onModelUse,
  onModelDelete,
  selectedModelId,
  showActions = true
}) => {
  const { toast } = useToast();
  const [models, setModels] = useState<HierarchicalModel[]>([]);
  const [filteredModels, setFilteredModels] = useState<HierarchicalModel[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [sortBy, setSortBy] = useState<'name' | 'created' | 'performance'>('created');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load models on component mount
  useEffect(() => {
    loadModels();
  }, []);

  // Filter and sort when dependencies change
  useEffect(() => {
    filterAndSortModels();
  }, [models, searchTerm, statusFilter, sortBy, sortOrder]);

  const loadModels = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const params: ListModelsParams = {
        page: 1,
        page_size: 50, // Load more models initially
        sort_by: 'created_at',
        sort_order: 'desc'
      };

      const response = await listHierarchicalModels(params);

      // Map API models to component format
      const mappedModels: HierarchicalModel[] = response.models.map(model => ({
        ...model,
        status: mapApiStatus(model.status)
      }));

      setModels(mappedModels);
    } catch (err: any) {
      console.error('Failed to load models:', err);
      setError(err.message || 'Failed to load models');
      toast({
        title: "Error loading models",
        description: err.message || 'Failed to load hierarchical models',
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const filterAndSortModels = () => {
    let filtered = models.filter(model => {
      const matchesSearch = model.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           model.base_model.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesStatus = statusFilter === 'all' || model.status === statusFilter;
      return matchesSearch && matchesStatus;
    });

    filtered.sort((a, b) => {
      let comparison = 0;

      switch (sortBy) {
        case 'name':
          comparison = a.name.localeCompare(b.name);
          break;
        case 'created':
          comparison = new Date(a.created_at).getTime() - new Date(b.created_at).getTime();
          break;
        case 'performance':
          const aF1 = a.metrics?.hierarchical_f1 || 0;
          const bF1 = b.metrics?.hierarchical_f1 || 0;
          comparison = aF1 - bF1;
          break;
      }

      return sortOrder === 'asc' ? comparison : -comparison;
    });

    setFilteredModels(filtered);
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle2 className="w-4 h-4 text-green-500" />;
      case 'training':
        return <Clock className="w-4 h-4 text-blue-500 animate-spin" />;
      case 'failed':
        return <AlertCircle className="w-4 h-4 text-red-500" />;
      default:
        return <Info className="w-4 h-4 text-gray-500" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const variants = {
      completed: 'default',
      training: 'secondary',
      failed: 'destructive',
      ready: 'outline'
    } as const;
    
    return (
      <Badge variant={variants[status as keyof typeof variants] || 'outline'}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  const handleModelUse = (modelId: string) => {
    const model = models.find(m => m.id === modelId);
    if (model?.status !== 'completed') {
      toast({
        title: "Model Not Ready",
        description: "This model is not ready for use yet.",
        variant: "destructive"
      });
      return;
    }
    onModelUse(modelId);
    toast({
      title: "Model Selected",
      description: `Using model: ${model.name}`,
    });
  };

  const handleModelDelete = async (modelId: string) => {
    const model = models.find(m => m.id === modelId);
    if (!model) return;

    try {
      await deleteHierarchicalModel(modelId);

      // Remove from local state
      setModels(prev => prev.filter(m => m.id !== modelId));
      onModelDelete(modelId);

      toast({
        title: "Model Deleted",
        description: `Successfully deleted model: ${model.name}`,
      });
    } catch (err: any) {
      console.error('Failed to delete model:', err);
      toast({
        title: "Delete Failed",
        description: err.message || 'Failed to delete model',
        variant: "destructive"
      });
    }
  };

  return (
    <Card className="border-2 border-gray-200 dark:border-gray-800">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 rounded-lg bg-gray-100 dark:bg-gray-800 flex items-center justify-center">
              <Brain className="w-5 h-5 text-gray-700 dark:text-gray-300" />
            </div>
            <div>
              <CardTitle>Trained Models</CardTitle>
              <CardDescription>
                Manage and reuse your hierarchical classification models
              </CardDescription>
            </div>
          </div>
          <Badge variant="outline">{filteredModels.length} models</Badge>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Search and Filter Controls */}
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
              <Input
                placeholder="Search models..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>
          
          <div className="flex gap-2">
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-32">
                <Filter className="w-4 h-4 mr-2" />
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
                <SelectItem value="training">Training</SelectItem>
                <SelectItem value="failed">Failed</SelectItem>
              </SelectContent>
            </Select>

            <Select value={`${sortBy}-${sortOrder}`} onValueChange={(value) => {
              const [field, order] = value.split('-');
              setSortBy(field as 'name' | 'created' | 'performance');
              setSortOrder(order as 'asc' | 'desc');
            }}>
              <SelectTrigger className="w-40">
                {sortOrder === 'asc' ? <SortAsc className="w-4 h-4 mr-2" /> : <SortDesc className="w-4 h-4 mr-2" />}
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="created-desc">Newest First</SelectItem>
                <SelectItem value="created-asc">Oldest First</SelectItem>
                <SelectItem value="name-asc">Name A-Z</SelectItem>
                <SelectItem value="name-desc">Name Z-A</SelectItem>
                <SelectItem value="performance-desc">Best Performance</SelectItem>
                <SelectItem value="performance-asc">Worst Performance</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Models List */}
        <div className="space-y-4">
          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-6 w-6 animate-spin mr-2" />
              <span>Loading models...</span>
            </div>
          ) : error ? (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                {error}
                <Button
                  variant="outline"
                  size="sm"
                  className="ml-2"
                  onClick={loadModels}
                >
                  Retry
                </Button>
              </AlertDescription>
            </Alert>
          ) : filteredModels.length === 0 ? (
            <Alert>
              <Info className="h-4 w-4" />
              <AlertDescription>
                {models.length === 0
                  ? "No models found. Train your first hierarchical model to get started."
                  : "No models found matching your criteria."
                }
              </AlertDescription>
            </Alert>
          ) : (
            filteredModels.map((model) => (
              <Card 
                key={model.id} 
                className={`cursor-pointer transition-all duration-200 hover:shadow-md ${
                  selectedModelId === model.id ? 'ring-2 ring-primary' : ''
                }`}
                onClick={() => onModelSelect(model)}
              >
                <CardContent className="p-4">
                  <div className="flex items-start justify-between">
                    <div className="flex-1 space-y-3">
                      {/* Model Header */}
                      <div className="flex items-center gap-3">
                        {getStatusIcon(model.status)}
                        <div className="flex-1">
                          <h3 className="font-semibold text-lg">{model.name}</h3>
                          <p className="text-sm text-muted-foreground">
                            Based on {model.base_model} • {model.metadata.hierarchy_levels} levels
                          </p>
                        </div>
                        {getStatusBadge(model.status)}
                      </div>

                      {/* Model Description */}
                      {model.metadata.description && (
                        <p className="text-sm text-muted-foreground">
                          {model.metadata.description}
                        </p>
                      )}

                      {/* Model Metrics */}
                      {model.status === 'completed' && model.metrics && (
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                          <div>
                            <Label className="text-xs text-muted-foreground">H-F1 Score</Label>
                            <p className="font-medium">{(model.metrics.hierarchical_f1 * 100).toFixed(1)}%</p>
                          </div>
                          <div>
                            <Label className="text-xs text-muted-foreground">Path Accuracy</Label>
                            <p className="font-medium">{(model.metrics.path_accuracy * 100).toFixed(1)}%</p>
                          </div>
                          <div>
                            <Label className="text-xs text-muted-foreground">Training Time</Label>
                            <p className="font-medium">{model.metrics.training_time}m</p>
                          </div>
                          <div>
                            <Label className="text-xs text-muted-foreground">Model Size</Label>
                            <p className="font-medium">{model.metadata.model_size}MB</p>
                          </div>
                        </div>
                      )}

                      {/* Training Info */}
                      <div className="flex items-center gap-4 text-xs text-muted-foreground">
                        <span className="flex items-center gap-1">
                          <Calendar className="w-3 h-3" />
                          {formatDistanceToNow(new Date(model.created_at), { addSuffix: true })}
                        </span>
                        <span>{model.metadata.total_samples.toLocaleString()} samples</span>
                        {model.training_config.use_unsloth && (
                          <span className="flex items-center gap-1">
                            <Zap className="w-3 h-3" />
                            Unsloth
                          </span>
                        )}
                      </div>
                    </div>

                    {/* Action Buttons */}
                    {showActions && (
                      <div className="flex flex-col gap-2 ml-4">
                        <Button
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleModelUse(model.id);
                          }}
                          disabled={model.status !== 'completed'}
                        >
                          <Play className="w-4 h-4 mr-2" />
                          Use Model
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={(e) => {
                            e.stopPropagation();
                            // Handle download
                          }}
                          disabled={model.status !== 'completed'}
                        >
                          <Download className="w-4 h-4 mr-2" />
                          Download
                        </Button>
                        <Button
                          size="sm"
                          variant="destructive"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleModelDelete(model.id);
                          }}
                        >
                          <Trash2 className="w-4 h-4 mr-2" />
                          Delete
                        </Button>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </div>
      </CardContent>
    </Card>
  );
};
