/**
 * Enhanced Workflow Service for ClassyWeb ML Platform Phase 3
 * 
 * This service provides enhanced workflow management with API v2 integration,
 * real-time monitoring, and advanced workflow orchestration.
 */

import { 
  ClassificationType, 
  TrainingMethod, 
  TrainingConfigV2,
  startTrainingV2,
  getEngineRecommendations,
  getEnginesForType,
  performInference,
  getTrainingStatus,
  validateTrainingConfig,
  getOptimalHyperparameters
} from './classificationEngineService';
import { unifiedDataManager, DataPurpose } from './unifiedDataManager';
import { UploadedFile } from './fileUploadApi';

// Enhanced Workflow Types
export interface WorkflowStep {
  id: string;
  name: string;
  description: string;
  status: WorkflowStepStatus;
  progress: number;
  estimatedDuration?: number;
  actualDuration?: number;
  error?: string;
  data?: any;
}

export enum WorkflowStepStatus {
  PENDING = 'pending',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  FAILED = 'failed',
  SKIPPED = 'skipped'
}

export interface WorkflowSession {
  id: string;
  type: ClassificationType;
  method: TrainingMethod;
  steps: WorkflowStep[];
  currentStepId: string;
  status: WorkflowStatus;
  startTime: Date;
  endTime?: Date;
  fileId: string;
  config: TrainingConfigV2;
  results?: any;
  metadata: WorkflowMetadata;
}

export enum WorkflowStatus {
  CREATED = 'created',
  RUNNING = 'running',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled',
  PAUSED = 'paused'
}

export interface WorkflowMetadata {
  userType: 'beginner' | 'expert';
  autoAdvance: boolean;
  skipOptionalSteps: boolean;
  enableRealTimeMonitoring: boolean;
  saveCheckpoints: boolean;
  notificationPreferences: {
    onStepComplete: boolean;
    onError: boolean;
    onCompletion: boolean;
  };
}

export interface WorkflowRecommendation {
  type: ClassificationType;
  method: TrainingMethod;
  confidence: number;
  reasoning: string;
  estimatedDuration: number;
  expectedAccuracy: number;
  complexity: 'low' | 'medium' | 'high';
  requiredSteps: string[];
  optionalSteps: string[];
}

/**
 * Enhanced Workflow Service Class
 */
export class EnhancedWorkflowService {
  private sessions: Map<string, WorkflowSession> = new Map();
  private stepDefinitions: Map<ClassificationType, WorkflowStep[]> = new Map();

  constructor() {
    this.initializeStepDefinitions();
  }

  /**
   * Initialize step definitions for each classification type
   */
  private initializeStepDefinitions(): void {
    // Binary Classification Steps
    this.stepDefinitions.set(ClassificationType.BINARY, [
      { id: 'data_upload', name: 'Data Upload', description: 'Upload and validate dataset', status: WorkflowStepStatus.PENDING, progress: 0 },
      { id: 'data_analysis', name: 'Data Analysis', description: 'Analyze data characteristics', status: WorkflowStepStatus.PENDING, progress: 0 },
      { id: 'class_setup', name: 'Class Setup', description: 'Define positive and negative classes', status: WorkflowStepStatus.PENDING, progress: 0 },
      { id: 'configuration', name: 'Configuration', description: 'Configure training parameters', status: WorkflowStepStatus.PENDING, progress: 0 },
      { id: 'training', name: 'Training', description: 'Train binary classification model', status: WorkflowStepStatus.PENDING, progress: 0 },
      { id: 'roc_analysis', name: 'ROC Analysis', description: 'Analyze ROC curves and AUC', status: WorkflowStepStatus.PENDING, progress: 0 },
      { id: 'threshold_optimization', name: 'Threshold Optimization', description: 'Optimize classification threshold', status: WorkflowStepStatus.PENDING, progress: 0 },
      { id: 'results', name: 'Results & Deploy', description: 'View results and deploy model', status: WorkflowStepStatus.PENDING, progress: 0 }
    ]);

    // Multi-Class Classification Steps
    this.stepDefinitions.set(ClassificationType.MULTI_CLASS, [
      { id: 'data_upload', name: 'Data Upload', description: 'Upload and validate dataset', status: WorkflowStepStatus.PENDING, progress: 0 },
      { id: 'data_analysis', name: 'Data Analysis', description: 'Analyze data characteristics', status: WorkflowStepStatus.PENDING, progress: 0 },
      { id: 'class_setup', name: 'Class Setup', description: 'Define multiple classes', status: WorkflowStepStatus.PENDING, progress: 0 },
      { id: 'configuration', name: 'Configuration', description: 'Configure training parameters', status: WorkflowStepStatus.PENDING, progress: 0 },
      { id: 'training', name: 'Training', description: 'Train multi-class model', status: WorkflowStepStatus.PENDING, progress: 0 },
      { id: 'class_balance', name: 'Class Balance Analysis', description: 'Analyze class distribution', status: WorkflowStepStatus.PENDING, progress: 0 },
      { id: 'confusion_matrix', name: 'Confusion Matrix', description: 'Analyze classification performance', status: WorkflowStepStatus.PENDING, progress: 0 },
      { id: 'results', name: 'Results & Deploy', description: 'View results and deploy model', status: WorkflowStepStatus.PENDING, progress: 0 }
    ]);

    // Flat Classification Steps
    this.stepDefinitions.set(ClassificationType.FLAT, [
      { id: 'data_upload', name: 'Data Upload', description: 'Upload and validate dataset', status: WorkflowStepStatus.PENDING, progress: 0 },
      { id: 'category_setup', name: 'Category Setup', description: 'Define flat categories', status: WorkflowStepStatus.PENDING, progress: 0 },
      { id: 'configuration', name: 'Configuration', description: 'Configure classification parameters', status: WorkflowStepStatus.PENDING, progress: 0 },
      { id: 'method_selection', name: 'Method Selection', description: 'Choose classification method', status: WorkflowStepStatus.PENDING, progress: 0 },
      { id: 'training', name: 'Training/Inference', description: 'Execute classification', status: WorkflowStepStatus.PENDING, progress: 0 },
      { id: 'results', name: 'Results', description: 'View classification results', status: WorkflowStepStatus.PENDING, progress: 0 },
      { id: 'deploy', name: 'Deploy', description: 'Deploy model for production', status: WorkflowStepStatus.PENDING, progress: 0 }
    ]);

    // Add other classification types...
  }

  /**
   * Create a new workflow session
   */
  async createWorkflowSession(
    type: ClassificationType,
    method: TrainingMethod,
    fileId: string,
    metadata: Partial<WorkflowMetadata> = {}
  ): Promise<WorkflowSession> {
    const sessionId = `workflow_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const steps = this.stepDefinitions.get(type) || [];
    const defaultMetadata: WorkflowMetadata = {
      userType: 'beginner',
      autoAdvance: false,
      skipOptionalSteps: false,
      enableRealTimeMonitoring: true,
      saveCheckpoints: true,
      notificationPreferences: {
        onStepComplete: true,
        onError: true,
        onCompletion: true
      },
      ...metadata
    };

    const session: WorkflowSession = {
      id: sessionId,
      type,
      method,
      steps: steps.map(step => ({ ...step })), // Deep copy
      currentStepId: steps[0]?.id || '',
      status: WorkflowStatus.CREATED,
      startTime: new Date(),
      fileId,
      config: {
        file_id: fileId,
        classification_type: type,
        training_method: method,
        text_column: '',
        label_columns: []
      },
      metadata: defaultMetadata
    };

    this.sessions.set(sessionId, session);
    return session;
  }

  /**
   * Get workflow session
   */
  getWorkflowSession(sessionId: string): WorkflowSession | undefined {
    return this.sessions.get(sessionId);
  }

  /**
   * Update workflow session
   */
  updateWorkflowSession(sessionId: string, updates: Partial<WorkflowSession>): WorkflowSession | undefined {
    const session = this.sessions.get(sessionId);
    if (!session) return undefined;

    const updatedSession = { ...session, ...updates };
    this.sessions.set(sessionId, updatedSession);
    return updatedSession;
  }

  /**
   * Advance to next step
   */
  async advanceToNextStep(sessionId: string): Promise<WorkflowSession | undefined> {
    const session = this.sessions.get(sessionId);
    if (!session) return undefined;

    const currentStepIndex = session.steps.findIndex(step => step.id === session.currentStepId);
    if (currentStepIndex === -1 || currentStepIndex >= session.steps.length - 1) {
      // Workflow completed
      session.status = WorkflowStatus.COMPLETED;
      session.endTime = new Date();
      return session;
    }

    // Mark current step as completed
    session.steps[currentStepIndex].status = WorkflowStepStatus.COMPLETED;
    session.steps[currentStepIndex].progress = 100;

    // Move to next step
    const nextStep = session.steps[currentStepIndex + 1];
    session.currentStepId = nextStep.id;
    nextStep.status = WorkflowStepStatus.IN_PROGRESS;

    this.sessions.set(sessionId, session);
    return session;
  }

  /**
   * Update step progress
   */
  updateStepProgress(sessionId: string, stepId: string, progress: number, data?: any): void {
    const session = this.sessions.get(sessionId);
    if (!session) return;

    const step = session.steps.find(s => s.id === stepId);
    if (step) {
      step.progress = Math.max(0, Math.min(100, progress));
      if (data) step.data = { ...step.data, ...data };
      
      if (progress >= 100) {
        step.status = WorkflowStepStatus.COMPLETED;
      } else if (progress > 0) {
        step.status = WorkflowStepStatus.IN_PROGRESS;
      }
    }
  }

  /**
   * Mark step as failed
   */
  markStepFailed(sessionId: string, stepId: string, error: string): void {
    const session = this.sessions.get(sessionId);
    if (!session) return;

    const step = session.steps.find(s => s.id === stepId);
    if (step) {
      step.status = WorkflowStepStatus.FAILED;
      step.error = error;
    }

    session.status = WorkflowStatus.FAILED;
  }

  /**
   * Get workflow recommendations
   */
  async getWorkflowRecommendations(fileId: string): Promise<WorkflowRecommendation[]> {
    try {
      const fileInfo = unifiedDataManager.getDataForPurpose(fileId, DataPurpose.ANALYSIS);
      if (!fileInfo) throw new Error('File not found');

      const recommendations: WorkflowRecommendation[] = [];

      // Get recommendations for each classification type
      for (const type of Object.values(ClassificationType)) {
        try {
          const engineRecommendations = await getEngineRecommendations(fileId, type);
          
          for (const engineRec of engineRecommendations) {
            const steps = this.stepDefinitions.get(type) || [];
            
            recommendations.push({
              type,
              method: TrainingMethod.CUSTOM, // Default to custom, could be determined from engine
              confidence: engineRec.confidence,
              reasoning: engineRec.reasoning,
              estimatedDuration: engineRec.estimated_performance.training_time || 300, // 5 minutes default
              expectedAccuracy: engineRec.estimated_performance.accuracy || 0.8,
              complexity: this.getComplexityLevel(type),
              requiredSteps: steps.filter(s => !this.isOptionalStep(s.id)).map(s => s.id),
              optionalSteps: steps.filter(s => this.isOptionalStep(s.id)).map(s => s.id)
            });
          }
        } catch (error) {
          console.warn(`Failed to get recommendations for ${type}:`, error);
        }
      }

      // Sort by confidence
      return recommendations.sort((a, b) => b.confidence - a.confidence);
    } catch (error) {
      console.error('Failed to get workflow recommendations:', error);
      return [];
    }
  }

  /**
   * Start training with enhanced workflow integration
   */
  async startTraining(sessionId: string): Promise<string> {
    const session = this.sessions.get(sessionId);
    if (!session) throw new Error('Workflow session not found');

    // Validate configuration
    const validation = await validateTrainingConfig(session.config);
    if (!validation.valid) {
      throw new Error(`Invalid configuration: ${validation.errors.join(', ')}`);
    }

    // Start training
    const response = await startTrainingV2(session.config);
    
    // Update session with training info
    session.status = WorkflowStatus.RUNNING;
    const trainingStep = session.steps.find(s => s.id === 'training');
    if (trainingStep) {
      trainingStep.status = WorkflowStepStatus.IN_PROGRESS;
      trainingStep.data = { 
        sessionId: response.session_id,
        taskId: response.task_id 
      };
    }

    return response.session_id;
  }

  /**
   * Get complexity level for classification type
   */
  private getComplexityLevel(type: ClassificationType): 'low' | 'medium' | 'high' {
    switch (type) {
      case ClassificationType.BINARY:
        return 'low';
      case ClassificationType.MULTI_CLASS:
      case ClassificationType.FLAT:
        return 'medium';
      case ClassificationType.MULTI_LABEL:
      case ClassificationType.HIERARCHICAL:
        return 'high';
      default:
        return 'medium';
    }
  }

  /**
   * Check if step is optional
   */
  private isOptionalStep(stepId: string): boolean {
    const optionalSteps = [
      'roc_analysis',
      'threshold_optimization',
      'class_balance',
      'confusion_matrix',
      'deploy'
    ];
    return optionalSteps.includes(stepId);
  }

  /**
   * Get workflow progress
   */
  getWorkflowProgress(sessionId: string): number {
    const session = this.sessions.get(sessionId);
    if (!session) return 0;

    const totalSteps = session.steps.length;
    const completedSteps = session.steps.filter(s => s.status === WorkflowStepStatus.COMPLETED).length;
    const currentStepProgress = session.steps.find(s => s.id === session.currentStepId)?.progress || 0;

    return ((completedSteps + currentStepProgress / 100) / totalSteps) * 100;
  }

  /**
   * Cancel workflow
   */
  cancelWorkflow(sessionId: string): void {
    const session = this.sessions.get(sessionId);
    if (session) {
      session.status = WorkflowStatus.CANCELLED;
      session.endTime = new Date();
    }
  }

  /**
   * Delete workflow session
   */
  deleteWorkflowSession(sessionId: string): boolean {
    return this.sessions.delete(sessionId);
  }
}

// Export singleton instance
export const enhancedWorkflowService = new EnhancedWorkflowService();

export default enhancedWorkflowService;
