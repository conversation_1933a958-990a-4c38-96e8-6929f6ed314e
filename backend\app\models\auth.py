"""
Authentication and Hierarchy Configuration related Pydantic models.
"""
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field, EmailStr, ConfigDict
from datetime import datetime

from .. import config # Corrected relative import

# --- Simple Message Response ---

class MessageResponse(BaseModel):
    """Response model for simple messages."""
    message: str

# --- Authentication API Models ---

class UserCreate(BaseModel):
    """Request model for creating a new user."""
    email: EmailStr = Field(..., description="User email address")
    password: str = Field(..., min_length=8, description="User password (min 8 characters)")
    username: Optional[str] = Field(None, min_length=3, description="Username (optional)")
    first_name: Optional[str] = Field(None, description="User's first name")
    last_name: Optional[str] = Field(None, description="User's last name")

class UserLogin(BaseModel):
    """Request model for user login."""
    email: EmailStr = Field(..., description="User email address")
    password: str = Field(..., description="User password")

class Token(BaseModel):
    """Response model for access token."""
    access_token: str
    refresh_token: Optional[str] = None
    token_type: str = "bearer"
    expires_in: int = Field(default=config.ACCESS_TOKEN_EXPIRE_MINUTES * 60, description="Token expiration time in seconds")

class RefreshTokenRequest(BaseModel):
    """Request model for refreshing an access token."""
    refresh_token: str = Field(..., description="Refresh token")

class User(BaseModel):
    """User model for type annotations and dependency injection.
    This is a Pydantic model that represents the User entity for API purposes.
    It's used for type annotations in API routes and doesn't contain sensitive fields.
    """
    id: int
    email: str
    username: Optional[str] = None
    is_active: bool = True

    model_config = ConfigDict(from_attributes=True)

class UserResponse(BaseModel):
    """Response model for user information."""
    id: int
    email: str
    username: Optional[str] = None
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    profile_picture: Optional[str] = None
    is_active: bool
    is_verified: bool
    oauth_provider: Optional[str] = None
    created_at: str
    last_login: Optional[str] = None
    bio: Optional[str] = None
    job_title: Optional[str] = None
    company: Optional[str] = None
    website: Optional[str] = None
    location: Optional[str] = None
    theme_preference: Optional[str] = Field(None, description="User's UI theme preference (light/dark)")

    model_config = ConfigDict(from_attributes=True)

class PasswordReset(BaseModel):
    """Request model for password reset."""
    email: EmailStr = Field(..., description="User email address")

class PasswordResetConfirm(BaseModel):
    """Request model for confirming password reset."""
    token: str = Field(..., description="Password reset token")
    new_password: str = Field(..., min_length=8, description="New password (min 8 characters)")

class EmailVerification(BaseModel):
    """Request model for email verification."""
    token: str = Field(..., description="Email verification token")

class UserUpdate(BaseModel):
    """Request model for updating user information."""
    username: Optional[str] = Field(None, min_length=3, description="Username")
    first_name: Optional[str] = Field(None, description="User's first name")
    last_name: Optional[str] = Field(None, description="User's last name")
    bio: Optional[str] = Field(None, description="User's bio or about me text")
    job_title: Optional[str] = Field(None, description="User's job title")
    company: Optional[str] = Field(None, description="User's company or organization")
    website: Optional[str] = Field(None, description="User's website URL")
    location: Optional[str] = Field(None, description="User's location")
    theme_preference: Optional[str] = Field(None, description="User's UI theme preference (light/dark)")

class PasswordChange(BaseModel):
    """Request model for changing password."""
    current_password: str = Field(..., description="Current password")
    new_password: str = Field(..., min_length=8, description="New password (min 8 characters)")

class TimezoneInfo(BaseModel):
    """Model for timezone information."""
    timestamp: str = Field(..., description="ISO timestamp")
    localTime: str = Field(..., description="Local time string")
    timezone: str = Field(..., description="Timezone name")
    offset: int = Field(..., description="Timezone offset in minutes")
    offsetStr: str = Field(..., description="Formatted timezone offset string")
    isDST: bool = Field(..., description="Whether daylight saving time is active")
    codeLength: Optional[int] = Field(None, description="Length of the authorization code")
    browserInfo: Optional[str] = Field(None, description="Browser user agent string")

class GoogleAuthRequest(BaseModel):
    """Request model for Google OAuth authentication."""
    code: str = Field(..., description="Authorization code from Google")
    tzInfo: Optional[TimezoneInfo] = Field(None, description="Client timezone information")

class FileActivityItem(BaseModel):
    """Model for file activity item in user activity response."""
    id: str
    filename: str
    created_at: str
    file_size: int
    num_rows: int
    columns: Optional[List[str]] = None

class TaskActivityItem(BaseModel):
    """Model for task activity item in user activity response."""
    id: str
    task_type: str
    status: str
    created_at: str
    completed_at: Optional[str] = None
    input_file_id: str
    result_file_path: Optional[str] = None
    message: Optional[str] = None

class UserActivityResponse(BaseModel):
    """Response model for user activity data."""
    files_count: int
    tasks_count: int
    recent_files: List[FileActivityItem]
    recent_tasks: List[TaskActivityItem]

class UserPreferencesUpdate(BaseModel):
    """Request model for updating user preferences."""
    theme_preference: Optional[str] = Field(None, description="UI theme preference (light/dark)")
    notification_preferences: Optional[Dict[str, bool]] = Field(None, description="Notification preferences")


# --- Hierarchy Configuration API Models ---

class HierarchyConfigCreate(BaseModel):
    """Request model for creating a new hierarchy configuration."""
    name: str = Field(..., description="Name of the hierarchy configuration")
    hierarchy_levels: List[str] = Field(..., min_length=1, max_length=config.MAX_HIERARCHY_LEVELS, description="List of hierarchy level names")
    is_default: bool = Field(default=False, description="Whether this is the default configuration")
    description: Optional[str] = Field(None, description="Optional description of the hierarchy")
    domain: Optional[str] = Field(None, description="Domain/industry this hierarchy is designed for")
    validation_rules: Optional[Dict[str, Any]] = Field(None, description="Custom validation rules per level")
    ui_config: Optional[Dict[str, Any]] = Field(None, description="UI configuration settings")
    confidence_thresholds: Optional[Dict[str, float]] = Field(None, description="Confidence thresholds per level")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")

class HierarchyConfigUpdate(BaseModel):
    """Request model for updating a hierarchy configuration."""
    name: Optional[str] = Field(None, description="Name of the hierarchy configuration")
    hierarchy_levels: Optional[List[str]] = Field(None, min_length=1, max_length=config.MAX_HIERARCHY_LEVELS, description="List of hierarchy level names")
    is_default: Optional[bool] = Field(None, description="Whether this is the default configuration")
    description: Optional[str] = Field(None, description="Optional description of the hierarchy")
    domain: Optional[str] = Field(None, description="Domain/industry this hierarchy is designed for")
    validation_rules: Optional[Dict[str, Any]] = Field(None, description="Custom validation rules per level")
    ui_config: Optional[Dict[str, Any]] = Field(None, description="UI configuration settings")
    confidence_thresholds: Optional[Dict[str, float]] = Field(None, description="Confidence thresholds per level")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")

class HierarchyConfigResponse(BaseModel):
    """Response model for a hierarchy configuration."""
    id: int
    name: str
    hierarchy_levels: List[str]
    is_default: bool
    user_id: Optional[int] = None
    description: Optional[str] = None
    domain: Optional[str] = None
    validation_rules: Optional[Dict[str, Any]] = None
    ui_config: Optional[Dict[str, Any]] = None
    confidence_thresholds: Optional[Dict[str, float]] = None
    extra_metadata: Optional[Dict[str, Any]] = None
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)

class HierarchyConfigListResponse(BaseModel):
    """Response model for a list of hierarchy configurations."""
    configs: List[HierarchyConfigResponse]
