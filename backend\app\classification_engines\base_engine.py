"""Base Classification Engine for ClassyWeb ML Platform.

This module defines the abstract base class and common interfaces for all
classification engines in the ClassyWeb platform.
"""

import logging
import time
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from typing import List, Dict, Any, Optional, Union, Tuple
from enum import Enum
import pandas as pd
import numpy as np

logger = logging.getLogger(__name__)


class ClassificationType(str, Enum):
    """Enumeration of supported classification types."""
    BINARY = "binary"
    MULTICLASS = "multiclass"
    MULTILABEL = "multilabel"
    HIERARCHICAL = "hierarchical"
    FLAT = "flat"


class TrainingMethod(str, Enum):
    """Enumeration of supported training methods."""
    CUSTOM = "custom"  # Custom model training with transformers/Unsloth
    LLM = "llm"       # LLM-based inference


@dataclass
class ClassificationResult:
    """Result from a classification operation."""
    text: str
    predictions: List[str]
    confidence: float
    probabilities: Dict[str, float] = field(default_factory=dict)
    processing_time: float = 0.0
    method_used: str = ""
    reasoning: str = ""
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class TrainingConfig:
    """Configuration for model training."""
    # General settings
    classification_type: ClassificationType
    training_method: TrainingMethod
    
    # Data settings
    text_columns: List[str]
    label_columns: List[str]
    validation_split: float = 0.2
    
    # Model settings
    base_model: str = "distilbert-base-uncased"
    max_length: int = 512
    
    # Training hyperparameters
    learning_rate: float = 2e-5
    batch_size: int = 16
    num_epochs: int = 3
    warmup_steps: int = 500
    weight_decay: float = 0.01
    
    # Hardware optimization
    use_unsloth: bool = True
    fp16: bool = True
    gradient_accumulation_steps: int = 1
    gradient_checkpointing: bool = False
    
    # LLM-specific settings
    llm_provider: Optional[str] = None
    llm_model: Optional[str] = None
    prompt_template: Optional[str] = None
    temperature: float = 0.1
    max_tokens: int = 100
    
    # Advanced settings
    class_weights: Optional[Dict[str, float]] = None
    threshold: float = 0.5
    early_stopping_patience: int = 3
    save_strategy: str = "epoch"
    evaluation_strategy: str = "epoch"
    
    # Metadata
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class TrainingResult:
    """Result from a training operation."""
    model_id: str
    training_time: float
    final_metrics: Dict[str, float]
    training_history: List[Dict[str, float]]
    model_path: Optional[str] = None
    tokenizer_path: Optional[str] = None
    config_path: Optional[str] = None
    error: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)


class BaseClassificationEngine(ABC):
    """Abstract base class for all classification engines."""
    
    def __init__(self, classification_type: ClassificationType):
        """Initialize the base engine.
        
        Args:
            classification_type: The type of classification this engine handles
        """
        self.classification_type = classification_type
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        self.is_trained = False
        self.model = None
        self.tokenizer = None
        self.label_map = {}
        self.config = None
        
    @property
    @abstractmethod
    def supported_training_methods(self) -> List[TrainingMethod]:
        """Return list of training methods supported by this engine."""
        pass
    
    @property
    @abstractmethod
    def default_metrics(self) -> List[str]:
        """Return list of default metrics for this classification type."""
        pass
    
    @abstractmethod
    async def train_custom_model(
        self,
        data: pd.DataFrame,
        config: TrainingConfig,
        progress_callback: Optional[callable] = None
    ) -> TrainingResult:
        """Train a custom model using transformers/Unsloth.
        
        Args:
            data: Training data DataFrame
            config: Training configuration
            progress_callback: Optional callback for progress updates
            
        Returns:
            TrainingResult with model artifacts and metrics
        """
        pass
    
    @abstractmethod
    async def llm_inference(
        self,
        texts: List[str],
        config: TrainingConfig,
        progress_callback: Optional[callable] = None
    ) -> List[ClassificationResult]:
        """Perform classification using LLM inference.
        
        Args:
            texts: List of texts to classify
            config: Configuration including LLM settings
            progress_callback: Optional callback for progress updates
            
        Returns:
            List of classification results
        """
        pass
    
    @abstractmethod
    async def predict(
        self,
        texts: List[str],
        model_id: Optional[str] = None
    ) -> List[ClassificationResult]:
        """Make predictions using a trained model.
        
        Args:
            texts: List of texts to classify
            model_id: Optional specific model ID to use
            
        Returns:
            List of classification results
        """
        pass
    
    @abstractmethod
    def validate_config(self, config: TrainingConfig) -> Tuple[bool, List[str]]:
        """Validate training configuration for this engine.
        
        Args:
            config: Training configuration to validate
            
        Returns:
            Tuple of (is_valid, list_of_errors)
        """
        pass
    
    def get_recommended_config(
        self,
        data: pd.DataFrame,
        training_method: TrainingMethod
    ) -> TrainingConfig:
        """Get recommended configuration based on data characteristics.
        
        Args:
            data: Training data to analyze
            training_method: Preferred training method
            
        Returns:
            Recommended training configuration
        """
        # Base implementation with common defaults
        config = TrainingConfig(
            classification_type=self.classification_type,
            training_method=training_method,
            text_columns=[],  # To be filled by subclasses
            label_columns=[]  # To be filled by subclasses
        )
        
        # Adjust based on data size
        data_size = len(data)
        if data_size < 1000:
            config.learning_rate = 3e-5
            config.batch_size = 8
            config.num_epochs = 5
        elif data_size > 10000:
            config.learning_rate = 1e-5
            config.batch_size = 32
            config.num_epochs = 2
            
        return config
    
    def load_model(self, model_path: str) -> bool:
        """Load a trained model from disk.
        
        Args:
            model_path: Path to the saved model
            
        Returns:
            True if loaded successfully, False otherwise
        """
        try:
            # Base implementation - to be overridden by subclasses
            self.logger.info(f"Loading model from {model_path}")
            # Subclasses should implement actual loading logic
            return True
        except Exception as e:
            self.logger.error(f"Failed to load model: {e}")
            return False
    
    def save_model(self, model_path: str) -> bool:
        """Save the trained model to disk.
        
        Args:
            model_path: Path where to save the model
            
        Returns:
            True if saved successfully, False otherwise
        """
        try:
            # Base implementation - to be overridden by subclasses
            self.logger.info(f"Saving model to {model_path}")
            # Subclasses should implement actual saving logic
            return True
        except Exception as e:
            self.logger.error(f"Failed to save model: {e}")
            return False
