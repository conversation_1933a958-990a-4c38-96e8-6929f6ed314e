"""Workflow Manager for ClassyWeb Universal Platform.

Manages workflow lifecycle, templates, and execution coordination.
"""

import logging
import json
import uuid
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
from enum import Enum
from dataclasses import dataclass, asdict
from sqlalchemy.orm import Session

from .workflow_engine import WorkflowEngine, WorkflowStep, WorkflowTemplate

logger = logging.getLogger(__name__)


class WorkflowStatus(Enum):
    """Workflow execution status."""
    CREATED = "created"
    RUNNING = "running"
    PAUSED = "paused"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


@dataclass
class WorkflowInstance:
    """Represents a workflow instance."""
    instance_id: str
    template_id: str
    user_id: Optional[int]
    tenant_id: Optional[str]
    status: WorkflowStatus
    current_step: int
    total_steps: int
    context: Dict[str, Any]
    created_at: datetime
    updated_at: datetime
    completed_at: Optional[datetime] = None
    error_message: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        data = asdict(self)
        data['status'] = self.status.value
        data['created_at'] = self.created_at.isoformat()
        data['updated_at'] = self.updated_at.isoformat()
        if self.completed_at:
            data['completed_at'] = self.completed_at.isoformat()
        return data


class WorkflowManager:
    """Manages workflow instances and execution."""
    
    def __init__(self):
        """Initialize the workflow manager."""
        self.workflow_engine = WorkflowEngine()
        self.active_workflows: Dict[str, WorkflowInstance] = {}
        self.workflow_history: List[WorkflowInstance] = []
        
        logger.info("Workflow Manager initialized")
    
    def create_workflow_instance(
        self,
        template_id: str,
        user_id: Optional[int] = None,
        tenant_id: Optional[str] = None,
        initial_context: Optional[Dict[str, Any]] = None
    ) -> str:
        """Create a new workflow instance."""
        try:
            # Get template
            template = self.workflow_engine.get_template(template_id)
            if not template:
                raise ValueError(f"Template not found: {template_id}")
            
            # Create instance
            instance_id = str(uuid.uuid4())
            instance = WorkflowInstance(
                instance_id=instance_id,
                template_id=template_id,
                user_id=user_id,
                tenant_id=tenant_id,
                status=WorkflowStatus.CREATED,
                current_step=0,
                total_steps=len(template.steps),
                context=initial_context or {},
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )
            
            self.active_workflows[instance_id] = instance
            
            logger.info(f"Created workflow instance: {instance_id} from template: {template_id}")
            return instance_id
            
        except Exception as e:
            logger.error(f"Failed to create workflow instance: {e}", exc_info=True)
            raise
    
    def start_workflow(self, instance_id: str) -> bool:
        """Start workflow execution."""
        try:
            if instance_id not in self.active_workflows:
                raise ValueError(f"Workflow instance not found: {instance_id}")
            
            instance = self.active_workflows[instance_id]
            if instance.status != WorkflowStatus.CREATED:
                raise ValueError(f"Workflow cannot be started from status: {instance.status}")
            
            # Update status
            instance.status = WorkflowStatus.RUNNING
            instance.updated_at = datetime.utcnow()
            
            logger.info(f"Started workflow instance: {instance_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start workflow {instance_id}: {e}", exc_info=True)
            return False
    
    def pause_workflow(self, instance_id: str) -> bool:
        """Pause workflow execution."""
        try:
            if instance_id not in self.active_workflows:
                raise ValueError(f"Workflow instance not found: {instance_id}")
            
            instance = self.active_workflows[instance_id]
            if instance.status != WorkflowStatus.RUNNING:
                raise ValueError(f"Workflow cannot be paused from status: {instance.status}")
            
            # Update status
            instance.status = WorkflowStatus.PAUSED
            instance.updated_at = datetime.utcnow()
            
            logger.info(f"Paused workflow instance: {instance_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to pause workflow {instance_id}: {e}", exc_info=True)
            return False
    
    def resume_workflow(self, instance_id: str) -> bool:
        """Resume workflow execution."""
        try:
            if instance_id not in self.active_workflows:
                raise ValueError(f"Workflow instance not found: {instance_id}")
            
            instance = self.active_workflows[instance_id]
            if instance.status != WorkflowStatus.PAUSED:
                raise ValueError(f"Workflow cannot be resumed from status: {instance.status}")
            
            # Update status
            instance.status = WorkflowStatus.RUNNING
            instance.updated_at = datetime.utcnow()
            
            logger.info(f"Resumed workflow instance: {instance_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to resume workflow {instance_id}: {e}", exc_info=True)
            return False
    
    def cancel_workflow(self, instance_id: str) -> bool:
        """Cancel workflow execution."""
        try:
            if instance_id not in self.active_workflows:
                raise ValueError(f"Workflow instance not found: {instance_id}")
            
            instance = self.active_workflows[instance_id]
            if instance.status in [WorkflowStatus.COMPLETED, WorkflowStatus.FAILED, WorkflowStatus.CANCELLED]:
                raise ValueError(f"Workflow cannot be cancelled from status: {instance.status}")
            
            # Update status
            instance.status = WorkflowStatus.CANCELLED
            instance.updated_at = datetime.utcnow()
            instance.completed_at = datetime.utcnow()
            
            # Move to history
            self.workflow_history.append(instance)
            del self.active_workflows[instance_id]
            
            logger.info(f"Cancelled workflow instance: {instance_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to cancel workflow {instance_id}: {e}", exc_info=True)
            return False
    
    def get_workflow_status(self, instance_id: str) -> Optional[WorkflowInstance]:
        """Get workflow instance status."""
        if instance_id in self.active_workflows:
            return self.active_workflows[instance_id]
        
        # Check history
        for workflow in self.workflow_history:
            if workflow.instance_id == instance_id:
                return workflow
        
        return None
    
    def list_workflows(
        self,
        user_id: Optional[int] = None,
        tenant_id: Optional[str] = None,
        status: Optional[WorkflowStatus] = None,
        limit: int = 100
    ) -> List[WorkflowInstance]:
        """List workflows with optional filters."""
        workflows = list(self.active_workflows.values()) + self.workflow_history
        
        # Apply filters
        if user_id is not None:
            workflows = [w for w in workflows if w.user_id == user_id]
        
        if tenant_id is not None:
            workflows = [w for w in workflows if w.tenant_id == tenant_id]
        
        if status is not None:
            workflows = [w for w in workflows if w.status == status]
        
        # Sort by creation time (newest first)
        workflows.sort(key=lambda x: x.created_at, reverse=True)
        
        return workflows[:limit]
    
    def update_workflow_context(self, instance_id: str, context_updates: Dict[str, Any]) -> bool:
        """Update workflow context."""
        try:
            if instance_id not in self.active_workflows:
                raise ValueError(f"Workflow instance not found: {instance_id}")
            
            instance = self.active_workflows[instance_id]
            instance.context.update(context_updates)
            instance.updated_at = datetime.utcnow()
            
            logger.debug(f"Updated context for workflow {instance_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to update workflow context {instance_id}: {e}", exc_info=True)
            return False
    
    def advance_workflow_step(self, instance_id: str) -> bool:
        """Advance workflow to next step."""
        try:
            if instance_id not in self.active_workflows:
                raise ValueError(f"Workflow instance not found: {instance_id}")
            
            instance = self.active_workflows[instance_id]
            
            if instance.status != WorkflowStatus.RUNNING:
                raise ValueError(f"Cannot advance workflow from status: {instance.status}")
            
            # Check if workflow is complete
            if instance.current_step >= instance.total_steps - 1:
                # Complete workflow
                instance.status = WorkflowStatus.COMPLETED
                instance.completed_at = datetime.utcnow()
                instance.updated_at = datetime.utcnow()
                
                # Move to history
                self.workflow_history.append(instance)
                del self.active_workflows[instance_id]
                
                logger.info(f"Completed workflow instance: {instance_id}")
            else:
                # Advance to next step
                instance.current_step += 1
                instance.updated_at = datetime.utcnow()
                
                logger.debug(f"Advanced workflow {instance_id} to step {instance.current_step}")
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to advance workflow {instance_id}: {e}", exc_info=True)
            return False
    
    def fail_workflow(self, instance_id: str, error_message: str) -> bool:
        """Mark workflow as failed."""
        try:
            if instance_id not in self.active_workflows:
                raise ValueError(f"Workflow instance not found: {instance_id}")
            
            instance = self.active_workflows[instance_id]
            instance.status = WorkflowStatus.FAILED
            instance.error_message = error_message
            instance.completed_at = datetime.utcnow()
            instance.updated_at = datetime.utcnow()
            
            # Move to history
            self.workflow_history.append(instance)
            del self.active_workflows[instance_id]
            
            logger.error(f"Failed workflow instance {instance_id}: {error_message}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to mark workflow as failed {instance_id}: {e}", exc_info=True)
            return False
    
    def cleanup_old_workflows(self, days_to_keep: int = 30) -> int:
        """Clean up old completed workflows."""
        cutoff_date = datetime.utcnow() - timedelta(days=days_to_keep)
        
        original_count = len(self.workflow_history)
        self.workflow_history = [
            w for w in self.workflow_history 
            if w.completed_at is None or w.completed_at > cutoff_date
        ]
        
        removed_count = original_count - len(self.workflow_history)
        
        if removed_count > 0:
            logger.info(f"Cleaned up {removed_count} old workflow instances")
        
        return removed_count


# Global workflow manager instance
workflow_manager = WorkflowManager()
