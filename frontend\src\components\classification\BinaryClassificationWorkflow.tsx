/**
 * BinaryClassificationWorkflow.tsx
 * 
 * Complete binary classification workflow component connecting to enhanced_binary_engine.
 * Features ROC curves, threshold optimization, and advanced binary classification metrics.
 */

import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Progress } from "@/components/ui/progress";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Separator } from "@/components/ui/separator";
import { 
  Target, 
  Upload, 
  Settings, 
  Brain, 
  Zap, 
  BarChart3, 
  Download,
  CheckCircle2,
  ArrowRight,
  ArrowLeft,
  Play,
  AlertCircle,
  TrendingUp,
  Scale,
  Activity
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";

// Import services
import { uploadFile, UploadedFile } from "@/services/fileUploadApi";
import {
  startUniversalTraining,
  startUniversalInference,
  getUniversalTaskStatus,
  UniversalTrainingRequest,
  UniversalInferenceRequest
} from "@/services/universalApi";
import { unifiedDataManager, DataPurpose } from "@/services/unifiedDataManager";
import { getUserLicense, getLicenseFeatures } from "@/services/licenseApi";

// Import classification components
import { ROCCurveChart } from "./ROCCurveChart";
import { ThresholdOptimizer } from "./ThresholdOptimizer";
import { ClassBalanceAnalyzer } from "./ClassBalanceAnalyzer";
import { TrainingProgressMonitor } from "./TrainingProgressMonitor";
import { EnhancedModelComparisonDashboard } from "./EnhancedModelComparisonDashboard";
import { UnifiedFileUploadZone } from "@/components/UnifiedFileUploadZone";
import { ColumnSelector } from "@/components/ColumnSelector";
import { DeployStep } from "./DeployStep";

interface BinaryClassificationWorkflowProps {
  initialData?: UploadedFile;
  onComplete?: (results: any) => void;
}

interface DualDataUpload {
  trainingData: {
    fileId: string;
    fileInfo: any;
    purpose: string;
  };
  classificationData: {
    fileId: string;
    fileInfo: any;
    purpose: string;
  };
  dualUpload: boolean;
}

interface BinaryTrainingResults {
  task_id: string;
  model_id: string;
  metrics: {
    accuracy: number;
    precision: number;
    recall: number;
    f1_score: number;
    roc_auc: number;
    pr_auc: number;
    confusion_matrix: number[][];
    classification_report: any;
  };
  roc_data: {
    fpr: number[];
    tpr: number[];
    thresholds: number[];
  };
  pr_data: {
    precision: number[];
    recall: number[];
    thresholds: number[];
  };
  optimal_threshold: number;
  training_time: number;
  model_size: number;
}

export const BinaryClassificationWorkflow: React.FC<BinaryClassificationWorkflowProps> = ({
  initialData,
  onComplete
}) => {
  const { toast } = useToast();
  const navigate = useNavigate();
  const location = useLocation();

  // Check if we came from expert workflow
  const fromExpertWorkflow = location.state?.fromExpertWorkflow ||
                            sessionStorage.getItem('expertWorkflowDualData');
  
  // Step management
  const [currentStep, setCurrentStep] = useState(1);
  const [completedSteps, setCompletedSteps] = useState<number[]>([]);
  
  // Data management
  const [uploadedData, setUploadedData] = useState<UploadedFile | null>(initialData || null);
  const [dualData, setDualData] = useState<DualDataUpload | null>(null);
  const [availableColumns, setAvailableColumns] = useState<string[]>([]);
  const [selectedTextColumn, setSelectedTextColumn] = useState<string>('');
  const [selectedLabelColumn, setSelectedLabelColumn] = useState<string>('');
  const [positiveClass, setPositiveClass] = useState<string>('');
  const [negativeClass, setNegativeClass] = useState<string>('');

  // License state
  const [userLicense, setUserLicense] = useState<{
    type: 'personal' | 'professional' | 'enterprise';
    features: string[];
    limits: {
      max_deployments?: number;
      max_api_calls_per_month?: number;
      cloud_deployment?: boolean;
      enterprise_features?: boolean;
    };
  }>({
    type: 'personal',
    features: [],
    limits: {}
  });
  
  // Configuration
  const [trainingMethod, setTrainingMethod] = useState<'llm' | 'custom'>('llm');
  const [llmProvider, setLlmProvider] = useState<string>('openai');
  const [llmModel, setLlmModel] = useState<string>('gpt-3.5-turbo');
  const [customPrompt, setCustomPrompt] = useState<string>('');
  
  // Training state
  const [isTraining, setIsTraining] = useState(false);
  const [trainingProgress, setTrainingProgress] = useState(0);
  const [trainingResults, setTrainingResults] = useState<BinaryTrainingResults | null>(null);
  const [trainingTaskId, setTrainingTaskId] = useState<string>('');
  
  // Threshold optimization
  const [selectedThreshold, setSelectedThreshold] = useState(0.5);
  const [optimizationStrategy, setOptimizationStrategy] = useState<string>('youden');
  
  // Error handling
  const [error, setError] = useState<string | null>(null);

  const steps = [
    { id: 1, title: "Data Upload", icon: Upload, description: "Upload binary classification dataset" },
    { id: 2, title: "Class Setup", icon: Target, description: "Define positive and negative classes" },
    { id: 3, title: "Configuration", icon: Settings, description: "Configure training parameters" },
    { id: 4, title: "Training", icon: Brain, description: "Train binary classification model" },
    { id: 5, title: "ROC Analysis", icon: TrendingUp, description: "Analyze ROC curves and AUC" },
    { id: 6, title: "Threshold Optimization", icon: Scale, description: "Optimize classification threshold" },
    { id: 7, title: "Results & Deploy", icon: Download, description: "View results and deploy model" }
  ];

  // Initialize data from expert workflow or start fresh
  useEffect(() => {
    const checkForDualData = () => {
      const storedDualData = sessionStorage.getItem('expertWorkflowDualData');
      if (storedDualData) {
        try {
          const parsedDualData = JSON.parse(storedDualData);
          setDualData(parsedDualData);
          console.log('Dual data detected from expert workflow:', parsedDualData);

          // Set available columns from training data
          if (parsedDualData.trainingData?.fileInfo?.columns) {
            setAvailableColumns(parsedDualData.trainingData.fileInfo.columns);
          }

          // Skip to step 2 since we have data
          setCurrentStep(2);
          return;
        } catch (error) {
          console.error('Error parsing dual data:', error);
        }
      }
    };

    checkForDualData();

    // Load user license
    loadUserLicense();

    // Only run fallback initialization if no dual data was found
    if (!sessionStorage.getItem('expertWorkflowDualData') && initialData) {
      setUploadedData(initialData);
      if (initialData.columns) {
        setAvailableColumns(initialData.columns);
      }
    } else if (!sessionStorage.getItem('expertWorkflowDualData')) {
      // Try to get data from unified data manager
      const allFiles = unifiedDataManager.getAllFiles();
      if (allFiles.length > 0) {
        const latestFile = allFiles[0];
        const fileData = latestFile.fileInfo;
        setUploadedData(fileData);
        if (fileData.columns) {
          setAvailableColumns(fileData.columns);
        }
      }
    }
  }, [initialData]);

  const loadUserLicense = async () => {
    try {
      const license = await getUserLicense();
      setUserLicense(license);
    } catch (error) {
      console.error('Failed to load user license:', error);
    }
  };

  // Detect binary classes from uploaded data
  const detectedClasses = useMemo(() => {
    if (!selectedLabelColumn) return [];

    let preview = null;
    if (dualData?.trainingData?.fileInfo?.preview) {
      preview = dualData.trainingData.fileInfo.preview;
    } else if (uploadedData?.preview) {
      preview = uploadedData.preview;
    }

    if (!preview) return [];

    const uniqueClasses = [...new Set(
      preview.map(row => row[selectedLabelColumn]).filter(Boolean)
    )];

    return uniqueClasses;
  }, [uploadedData, dualData, selectedLabelColumn]);

  // Prepare ROC data for visualization
  const rocData = useMemo(() => {
    if (!trainingResults?.roc_data) return [];

    return [{
      modelId: trainingResults.model_id,
      modelName: `Binary Classification Model`,
      points: trainingResults.roc_data.fpr.map((fpr, idx) => ({
        fpr,
        tpr: trainingResults.roc_data.tpr[idx],
        threshold: trainingResults.roc_data.thresholds[idx]
      })),
      auc: trainingResults.metrics.roc_auc,
      color: '#3b82f6',
      optimalThreshold: trainingResults.optimal_threshold
    }];
  }, [trainingResults]);

  // Prepare PR data for visualization
  const prData = useMemo(() => {
    if (!trainingResults?.pr_data) return [];

    return [{
      modelId: trainingResults.model_id,
      modelName: `Binary Classification Model`,
      points: trainingResults.pr_data.recall.map((recall, idx) => ({
        recall,
        precision: trainingResults.pr_data.precision[idx],
        threshold: trainingResults.pr_data.thresholds[idx]
      })),
      auc: trainingResults.metrics.pr_auc,
      color: '#22c55e'
    }];
  }, [trainingResults]);

  // Prepare threshold optimization data
  const thresholdData = useMemo(() => {
    if (!trainingResults?.roc_data) return [];

    return trainingResults.roc_data.thresholds.map((threshold, idx) => {
      const tpr = trainingResults.roc_data.tpr[idx];
      const fpr = trainingResults.roc_data.fpr[idx];
      const tnr = 1 - fpr; // Specificity
      const fnr = 1 - tpr;
      
      // Calculate precision and recall from PR data if available
      const prIdx = trainingResults.pr_data?.thresholds.findIndex(t => Math.abs(t - threshold) < 0.001) || idx;
      const precision = trainingResults.pr_data?.precision[prIdx] || (tpr / (tpr + fpr));
      const recall = tpr;
      
      const f1_score = 2 * (precision * recall) / (precision + recall);
      const accuracy = (tpr + tnr) / 2; // Simplified for binary case
      const youden_j = tpr + tnr - 1;

      return {
        threshold,
        tpr,
        fpr,
        tnr,
        fnr,
        precision: isNaN(precision) ? 0 : precision,
        recall,
        f1_score: isNaN(f1_score) ? 0 : f1_score,
        accuracy,
        youden_j,
        tp: Math.round(tpr * 100), // Simplified
        fp: Math.round(fpr * 100),
        tn: Math.round(tnr * 100),
        fn: Math.round(fnr * 100)
      };
    });
  }, [trainingResults]);

  const handleStepComplete = (stepId: number) => {
    if (!completedSteps.includes(stepId)) {
      setCompletedSteps([...completedSteps, stepId]);
    }
  };

  const handleNext = () => {
    if (currentStep < steps.length) {
      handleStepComplete(currentStep);
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleDataUpload = async (file: File) => {
    try {
      const uploadedFile = await uploadFile(file);
      setUploadedData(uploadedFile);
      
      // Auto-select columns
      if (uploadedFile.columns) {
        const textColumn = uploadedFile.columns.find(col => 
          col.toLowerCase().includes('text') || 
          col.toLowerCase().includes('content') ||
          col.toLowerCase().includes('message')
        ) || uploadedFile.columns[0];
        setSelectedTextColumn(textColumn);
        
        const labelColumn = uploadedFile.columns.find(col => 
          col.toLowerCase().includes('label') || 
          col.toLowerCase().includes('category') ||
          col.toLowerCase().includes('class')
        );
        if (labelColumn) {
          setSelectedLabelColumn(labelColumn);
        }
      }
      
      toast({
        title: "Data uploaded successfully",
        description: `${uploadedFile.filename} with ${uploadedFile.num_rows} rows`
      });
    } catch (error) {
      setError(`Upload failed: ${error}`);
      toast({
        title: "Upload failed",
        description: "Please try again with a valid CSV file",
        variant: "destructive"
      });
    }
  };

  const startTraining = async () => {
    if (!uploadedData || !selectedTextColumn || !selectedLabelColumn || !positiveClass || !negativeClass) {
      toast({
        title: "Missing configuration",
        description: "Please ensure all fields are configured",
        variant: "destructive"
      });
      return;
    }

    setIsTraining(true);
    setError(null);

    try {
      const trainingRequest: UniversalTrainingRequest = {
        file_id: uploadedData.file_id,
        classification_type: 'binary',
        model_type: trainingMethod,
        text_column: selectedTextColumn,
        label_columns: [selectedLabelColumn],
        llm_provider: trainingMethod === 'llm' ? llmProvider : undefined,
        llm_model: trainingMethod === 'llm' ? llmModel : undefined,
        custom_prompt: customPrompt || undefined,
        training_params: {
          positive_class: positiveClass,
          negative_class: negativeClass,
          max_epochs: 3,
          batch_size: 16,
          learning_rate: 2e-5
        }
      };

      const response = await startUniversalTraining(trainingRequest);
      setTrainingTaskId(response.task_id);
      
      // Start monitoring training progress
      monitorTrainingProgress(response.task_id);
      
      toast({
        title: "Training started",
        description: "Your binary classification model is being trained."
      });
    } catch (error) {
      setError(`Training failed: ${error}`);
      setIsTraining(false);
      toast({
        title: "Training failed",
        description: "Please check your configuration and try again",
        variant: "destructive"
      });
    }
  };

  const monitorTrainingProgress = async (taskId: string) => {
    const checkProgress = async () => {
      try {
        const status = await getUniversalTaskStatus(taskId);
        
        if (status.progress !== undefined) {
          setTrainingProgress(status.progress);
        }
        
        if (status.status === 'completed') {
          setIsTraining(false);
          setTrainingResults(status.result);
          setSelectedThreshold(status.result.optimal_threshold || 0.5);
          handleStepComplete(4);
          toast({
            title: "Training completed",
            description: "Your binary classification model is ready!"
          });
        } else if (status.status === 'failed') {
          setIsTraining(false);
          setError(status.error || 'Training failed');
          toast({
            title: "Training failed",
            description: status.error || "Unknown error occurred",
            variant: "destructive"
          });
        } else {
          setTimeout(checkProgress, 2000);
        }
      } catch (error) {
        console.error('Error monitoring training:', error);
        setTimeout(checkProgress, 5000);
      }
    };
    
    checkProgress();
  };

  const handleThresholdOptimization = (threshold: number, strategy: string) => {
    setSelectedThreshold(threshold);
    setOptimizationStrategy(strategy);
    toast({
      title: "Threshold updated",
      description: `Applied ${strategy} optimization: ${threshold.toFixed(3)}`
    });
  };

  return (
    <div className="max-w-7xl mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          {fromExpertWorkflow && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => navigate('/expert')}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="w-4 h-4" />
              Back to Expert
            </Button>
          )}
          <div className="flex items-center gap-3">
            <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center">
              <Target className="w-6 h-6 text-primary" />
            </div>
            <div>
              <h1 className="text-3xl font-bold">Binary Classification Workflow</h1>
              <p className="text-muted-foreground">Two-class classification with ROC analysis and threshold optimization</p>
            </div>
          </div>
        </div>
        <Badge variant="outline" className="px-3 py-1">
          Step {currentStep} of {steps.length}
        </Badge>
      </div>

      {/* Progress Bar */}
      <div className="space-y-2">
        <div className="flex justify-between text-sm text-muted-foreground">
          <span>Progress</span>
          <span>{Math.round((completedSteps.length / steps.length) * 100)}% Complete</span>
        </div>
        <Progress value={(completedSteps.length / steps.length) * 100} className="h-2" />
      </div>

      {/* Error Alert */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Steps Sidebar */}
        <div className="lg:col-span-1">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Workflow Steps</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              {steps.map((step) => {
                const Icon = step.icon;
                const isActive = currentStep === step.id;
                const isComplete = completedSteps.includes(step.id);
                
                return (
                  <div
                    key={step.id}
                    className={`flex items-center gap-3 p-3 rounded-lg transition-colors cursor-pointer ${
                      isActive ? 'bg-primary/10 border border-primary/20' : 
                      isComplete ? 'bg-green-50 dark:bg-green-950' : 'bg-muted/30'
                    }`}
                    onClick={() => setCurrentStep(step.id)}
                  >
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                      isActive ? 'bg-primary text-white' :
                      isComplete ? 'bg-green-500 text-white' : 'bg-muted'
                    }`}>
                      {isComplete ? (
                        <CheckCircle2 className="w-4 h-4" />
                      ) : (
                        <Icon className="w-4 h-4" />
                      )}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className={`font-medium text-sm ${
                        isActive ? 'text-primary' : 
                        isComplete ? 'text-green-600 dark:text-green-400' : 'text-muted-foreground'
                      }`}>
                        {step.title}
                      </div>
                      <div className="text-xs text-muted-foreground truncate">
                        {step.description}
                      </div>
                    </div>
                  </div>
                );
              })}
            </CardContent>
          </Card>
        </div>

        {/* Main Content */}
        <div className="lg:col-span-3">
          {/* Step 1: Data Upload */}
          {currentStep === 1 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Upload className="w-5 h-5" />
                  Data Upload
                </CardTitle>
                <CardDescription>
                  Upload your binary classification dataset or use data from the Expert Workflow.
                </CardDescription>
              </CardHeader>
              <CardContent>
                {dualData ? (
                  <div className="space-y-4">
                    <Alert>
                      <CheckCircle2 className="h-4 w-4" />
                      <AlertDescription>
                        Using data from Expert Workflow with dual file setup:
                        <br />
                        <strong>Training Data:</strong> {dualData.trainingData.fileInfo.name}
                        <br />
                        <strong>Classification Data:</strong> {dualData.classificationData.fileInfo.name}
                      </AlertDescription>
                    </Alert>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <Card>
                        <CardHeader>
                          <CardTitle className="text-sm">Training Data</CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="text-sm space-y-1">
                            <div><strong>File:</strong> {dualData.trainingData.fileInfo.name}</div>
                            <div><strong>Rows:</strong> {dualData.trainingData.fileInfo.rows?.toLocaleString()}</div>
                            <div><strong>Columns:</strong> {dualData.trainingData.fileInfo.columns?.length}</div>
                          </div>
                        </CardContent>
                      </Card>

                      <Card>
                        <CardHeader>
                          <CardTitle className="text-sm">Classification Data</CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="text-sm space-y-1">
                            <div><strong>File:</strong> {dualData.classificationData.fileInfo.name}</div>
                            <div><strong>Rows:</strong> {dualData.classificationData.fileInfo.rows?.toLocaleString()}</div>
                            <div><strong>Columns:</strong> {dualData.classificationData.fileInfo.columns?.length}</div>
                          </div>
                        </CardContent>
                      </Card>
                    </div>
                  </div>
                ) : !uploadedData ? (
                  <UnifiedFileUploadZone
                    onFileSelected={(fileId, fileInfo, purposes) => {
                      console.log('File selected:', fileInfo);
                      setUploadedData(fileInfo);

                      if (fileInfo.columns) {
                        setAvailableColumns(fileInfo.columns);
                      }

                      toast({
                        title: "File uploaded successfully",
                        description: `Uploaded ${fileInfo.name} with ${fileInfo.rows} rows`
                      });
                    }}
                    onFileRemoved={() => {
                      setUploadedData(null);
                      setAvailableColumns([]);
                    }}
                    requiredPurposes={['training', 'classification']}
                    allowMultiplePurposes={true}
                    title="Upload Binary Classification Dataset"
                    description="Upload your dataset containing text and binary labels"
                  />
                ) : (
                  <div className="space-y-4">
                    <div className="flex items-center justify-between p-4 bg-green-50 dark:bg-green-950 rounded-lg">
                      <div className="flex items-center gap-3">
                        <CheckCircle2 className="w-5 h-5 text-green-500" />
                        <div>
                          <div className="font-medium">{uploadedData.filename}</div>
                          <div className="text-sm text-muted-foreground">
                            {uploadedData.num_rows} rows, {uploadedData.columns?.length} columns
                          </div>
                        </div>
                      </div>
                      <Button variant="outline" size="sm" onClick={() => setUploadedData(null)}>
                        Remove
                      </Button>
                    </div>
                    
                    {uploadedData.preview && (
                      <div className="border rounded-lg overflow-hidden">
                        <div className="bg-muted p-3 border-b">
                          <h4 className="font-medium">Data Preview</h4>
                        </div>
                        <div className="overflow-x-auto">
                          <table className="w-full text-sm">
                            <thead className="bg-muted/50">
                              <tr>
                                {uploadedData.columns?.map((col) => (
                                  <th key={col} className="text-left p-2 font-medium">
                                    {col}
                                  </th>
                                ))}
                              </tr>
                            </thead>
                            <tbody>
                              {uploadedData.preview.slice(0, 5).map((row, idx) => (
                                <tr key={idx} className="border-t">
                                  {uploadedData.columns?.map((col) => (
                                    <td key={col} className="p-2 max-w-xs truncate">
                                      {row[col]}
                                    </td>
                                  ))}
                                </tr>
                              ))}
                            </tbody>
                          </table>
                        </div>
                      </div>
                    )}
                  </div>
                )}
                
                {(uploadedData || dualData) && (
                  <div className="flex justify-end">
                    <Button
                      onClick={handleNext}
                      className="flex items-center gap-2"
                    >
                      Continue to Class Setup
                      <ArrowRight className="w-4 h-4" />
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Step 2: Class Setup */}
          {currentStep === 2 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Target className="w-5 h-5" />
                  Binary Class Setup
                </CardTitle>
                <CardDescription>
                  Configure the positive and negative classes for binary classification.
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Column Selection */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Text Column</label>
                    <ColumnSelector
                      columns={availableColumns}
                      selectedColumns={selectedTextColumn ? [selectedTextColumn] : []}
                      onSelectionChange={(columns) => setSelectedTextColumn(columns[0] || '')}
                      maxSelections={1}
                      placeholder="Select text column"
                    />
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium">Label Column</label>
                    <ColumnSelector
                      columns={availableColumns}
                      selectedColumns={selectedLabelColumn ? [selectedLabelColumn] : []}
                      onSelectionChange={(columns) => setSelectedLabelColumn(columns[0] || '')}
                      maxSelections={1}
                      placeholder="Select label column"
                    />
                  </div>
                </div>

                {/* Class Assignment */}
                {detectedClasses.length === 2 && (
                  <div className="space-y-4">
                    <h4 className="font-medium">Class Assignment</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <label className="text-sm font-medium">Positive Class</label>
                        <select
                          value={positiveClass}
                          onChange={(e) => setPositiveClass(e.target.value)}
                          className="w-full p-2 border rounded-md"
                        >
                          <option value="">Select positive class...</option>
                          {detectedClasses.map((cls) => (
                            <option key={cls} value={cls}>{cls}</option>
                          ))}
                        </select>
                        <p className="text-xs text-muted-foreground">
                          The class you want to predict (e.g., "spam", "positive", "fraud")
                        </p>
                      </div>

                      <div className="space-y-2">
                        <label className="text-sm font-medium">Negative Class</label>
                        <select
                          value={negativeClass}
                          onChange={(e) => setNegativeClass(e.target.value)}
                          className="w-full p-2 border rounded-md"
                        >
                          <option value="">Select negative class...</option>
                          {detectedClasses.map((cls) => (
                            <option key={cls} value={cls}>{cls}</option>
                          ))}
                        </select>
                        <p className="text-xs text-muted-foreground">
                          The opposite class (e.g., "not spam", "negative", "legitimate")
                        </p>
                      </div>
                    </div>
                  </div>
                )}

                {/* Detected Classes Display */}
                {detectedClasses.length > 0 && (
                  <div className="space-y-3">
                    <h4 className="font-medium">Detected Classes ({detectedClasses.length})</h4>
                    <div className="flex flex-wrap gap-2">
                      {detectedClasses.map((cls, index) => (
                        <Badge
                          key={index}
                          variant={cls === positiveClass ? 'default' : cls === negativeClass ? 'secondary' : 'outline'}
                          className="px-3 py-1"
                        >
                          {cls}
                          {cls === positiveClass && ' (Positive)'}
                          {cls === negativeClass && ' (Negative)'}
                        </Badge>
                      ))}
                    </div>

                    {detectedClasses.length !== 2 && (
                      <Alert variant="destructive">
                        <AlertCircle className="h-4 w-4" />
                        <AlertDescription>
                          Binary classification requires exactly 2 classes. Found {detectedClasses.length} classes.
                          Please check your data or use multi-class classification instead.
                        </AlertDescription>
                      </Alert>
                    )}
                  </div>
                )}

                {/* Class Balance Analysis */}
                {uploadedData && selectedLabelColumn && detectedClasses.length === 2 && (
                  <ClassBalanceAnalyzer
                    data={uploadedData}
                    labelColumn={selectedLabelColumn}
                  />
                )}

                <div className="flex justify-between">
                  <Button variant="outline" onClick={handlePrevious}>
                    <ArrowLeft className="w-4 h-4 mr-2" />
                    Previous
                  </Button>
                  <Button
                    onClick={handleNext}
                    disabled={!selectedTextColumn || !selectedLabelColumn || !positiveClass || !negativeClass || detectedClasses.length !== 2}
                  >
                    Next Step
                    <ArrowRight className="w-4 h-4 ml-2" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Step 3: Configuration */}
          {currentStep === 3 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Settings className="w-5 h-5" />
                  Training Configuration
                </CardTitle>
                <CardDescription>
                  Configure training parameters for your binary classification model.
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <Tabs value={trainingMethod} onValueChange={(value) => setTrainingMethod(value as 'llm' | 'custom')}>
                  <TabsList className="grid w-full grid-cols-2">
                    <TabsTrigger value="llm">LLM Inference</TabsTrigger>
                    <TabsTrigger value="custom">Custom Training</TabsTrigger>
                  </TabsList>

                  <TabsContent value="llm" className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <label className="text-sm font-medium">LLM Provider</label>
                        <select
                          value={llmProvider}
                          onChange={(e) => setLlmProvider(e.target.value)}
                          className="w-full p-2 border rounded-md"
                        >
                          <option value="openai">OpenAI</option>
                          <option value="anthropic">Anthropic</option>
                          <option value="groq">Groq</option>
                          <option value="ollama">Ollama (Local)</option>
                        </select>
                      </div>

                      <div className="space-y-2">
                        <label className="text-sm font-medium">Model</label>
                        <select
                          value={llmModel}
                          onChange={(e) => setLlmModel(e.target.value)}
                          className="w-full p-2 border rounded-md"
                        >
                          {llmProvider === 'openai' && (
                            <>
                              <option value="gpt-3.5-turbo">GPT-3.5 Turbo</option>
                              <option value="gpt-4">GPT-4</option>
                              <option value="gpt-4-turbo">GPT-4 Turbo</option>
                            </>
                          )}
                          {llmProvider === 'anthropic' && (
                            <>
                              <option value="claude-3-haiku">Claude 3 Haiku</option>
                              <option value="claude-3-sonnet">Claude 3 Sonnet</option>
                              <option value="claude-3-opus">Claude 3 Opus</option>
                            </>
                          )}
                          {llmProvider === 'groq' && (
                            <>
                              <option value="llama2-70b">Llama 2 70B</option>
                              <option value="mixtral-8x7b">Mixtral 8x7B</option>
                            </>
                          )}
                          {llmProvider === 'ollama' && (
                            <>
                              <option value="llama2">Llama 2</option>
                              <option value="mistral">Mistral</option>
                            </>
                          )}
                        </select>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <label className="text-sm font-medium">Custom Prompt (Optional)</label>
                      <textarea
                        value={customPrompt}
                        onChange={(e) => setCustomPrompt(e.target.value)}
                        placeholder={`Classify the following text as either "${positiveClass}" or "${negativeClass}". Respond with only the class name.`}
                        className="w-full p-3 border rounded-md h-24 resize-none"
                      />
                      <p className="text-xs text-muted-foreground">
                        Leave empty to use the default binary classification prompt.
                      </p>
                    </div>
                  </TabsContent>

                  <TabsContent value="custom" className="space-y-4">
                    <Alert>
                      <AlertCircle className="h-4 w-4" />
                      <AlertDescription>
                        Custom training will fine-tune a transformer model specifically for your binary classification task.
                        This provides better accuracy but takes longer and includes ROC curve analysis.
                      </AlertDescription>
                    </Alert>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="space-y-2">
                        <label className="text-sm font-medium">Epochs</label>
                        <input
                          type="number"
                          min="1"
                          max="10"
                          defaultValue="3"
                          className="w-full p-2 border rounded-md"
                        />
                      </div>

                      <div className="space-y-2">
                        <label className="text-sm font-medium">Batch Size</label>
                        <select className="w-full p-2 border rounded-md">
                          <option value="8">8</option>
                          <option value="16" selected>16</option>
                          <option value="32">32</option>
                        </select>
                      </div>

                      <div className="space-y-2">
                        <label className="text-sm font-medium">Learning Rate</label>
                        <select className="w-full p-2 border rounded-md">
                          <option value="1e-5">1e-5</option>
                          <option value="2e-5" selected>2e-5</option>
                          <option value="3e-5">3e-5</option>
                          <option value="5e-5">5e-5</option>
                        </select>
                      </div>
                    </div>
                  </TabsContent>
                </Tabs>

                <div className="flex justify-between">
                  <Button variant="outline" onClick={handlePrevious}>
                    <ArrowLeft className="w-4 h-4 mr-2" />
                    Previous
                  </Button>
                  <Button onClick={handleNext}>
                    Start Training
                    <ArrowRight className="w-4 h-4 ml-2" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Step 4: Training */}
          {currentStep === 4 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Brain className="w-5 h-5" />
                  Model Training
                </CardTitle>
                <CardDescription>
                  Training your binary classification model with ROC curve generation.
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {!isTraining && !trainingResults ? (
                  <div className="text-center space-y-4">
                    <div className="w-16 h-16 rounded-full bg-primary/10 flex items-center justify-center mx-auto">
                      <Play className="w-8 h-8 text-primary" />
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold mb-2">Ready to Start Training</h3>
                      <p className="text-muted-foreground mb-4">
                        This will train a binary classification model and generate ROC curves for analysis.
                      </p>
                      <Button onClick={startTraining} size="lg" className="px-8">
                        <Play className="w-4 h-4 mr-2" />
                        Start Training
                      </Button>
                    </div>
                  </div>
                ) : isTraining ? (
                  <div className="space-y-6">
                    <div className="text-center">
                      <div className="w-16 h-16 rounded-full bg-primary/10 flex items-center justify-center mx-auto mb-4">
                        <div className="animate-spin">
                          <Activity className="w-8 h-8 text-primary" />
                        </div>
                      </div>
                      <h3 className="text-lg font-semibold mb-2">Training in Progress</h3>
                      <p className="text-muted-foreground">
                        Training your binary classification model and generating performance metrics...
                      </p>
                    </div>

                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>Progress</span>
                        <span>{trainingProgress}%</span>
                      </div>
                      <Progress value={trainingProgress} className="h-2" />
                    </div>

                    {trainingTaskId && (
                      <TrainingProgressMonitor
                        taskId={trainingTaskId}
                        onProgress={setTrainingProgress}
                        onComplete={(results) => {
                          setTrainingResults(results);
                          setIsTraining(false);
                        }}
                      />
                    )}
                  </div>
                ) : trainingResults ? (
                  <div className="space-y-6">
                    <div className="text-center">
                      <div className="w-16 h-16 rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center mx-auto mb-4">
                        <CheckCircle2 className="w-8 h-8 text-green-600 dark:text-green-400" />
                      </div>
                      <h3 className="text-lg font-semibold mb-2">Training Complete!</h3>
                      <p className="text-muted-foreground">
                        Your binary classification model has been trained successfully.
                      </p>
                    </div>

                    {/* Training Results */}
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                      <div className="text-center p-4 bg-muted/50 rounded-lg">
                        <div className="text-2xl font-bold text-primary">
                          {(trainingResults.metrics.accuracy * 100).toFixed(1)}%
                        </div>
                        <div className="text-sm text-muted-foreground">Accuracy</div>
                      </div>
                      <div className="text-center p-4 bg-muted/50 rounded-lg">
                        <div className="text-2xl font-bold text-green-600">
                          {(trainingResults.metrics.roc_auc * 100).toFixed(1)}%
                        </div>
                        <div className="text-sm text-muted-foreground">ROC AUC</div>
                      </div>
                      <div className="text-center p-4 bg-muted/50 rounded-lg">
                        <div className="text-2xl font-bold text-blue-600">
                          {(trainingResults.metrics.precision * 100).toFixed(1)}%
                        </div>
                        <div className="text-sm text-muted-foreground">Precision</div>
                      </div>
                      <div className="text-center p-4 bg-muted/50 rounded-lg">
                        <div className="text-2xl font-bold text-purple-600">
                          {(trainingResults.metrics.recall * 100).toFixed(1)}%
                        </div>
                        <div className="text-sm text-muted-foreground">Recall</div>
                      </div>
                    </div>
                  </div>
                ) : null}

                <div className="flex justify-between">
                  <Button variant="outline" onClick={handlePrevious} disabled={isTraining}>
                    <ArrowLeft className="w-4 h-4 mr-2" />
                    Previous
                  </Button>
                  <Button
                    onClick={handleNext}
                    disabled={!trainingResults}
                  >
                    Analyze ROC Curves
                    <ArrowRight className="w-4 h-4 ml-2" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Step 5: ROC Analysis */}
          {currentStep === 5 && trainingResults && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="w-5 h-5" />
                  ROC Curve Analysis
                </CardTitle>
                <CardDescription>
                  Analyze ROC curves, AUC scores, and model performance metrics.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ROCCurveChart
                  rocData={rocData}
                  prData={prData}
                  selectedThreshold={selectedThreshold}
                  onThresholdChange={setSelectedThreshold}
                  showPRCurve={true}
                  showOptimalPoint={true}
                  interactive={true}
                />

                <div className="flex justify-between mt-6">
                  <Button variant="outline" onClick={handlePrevious}>
                    <ArrowLeft className="w-4 h-4 mr-2" />
                    Previous
                  </Button>
                  <Button onClick={handleNext}>
                    Optimize Threshold
                    <ArrowRight className="w-4 h-4 ml-2" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Step 6: Threshold Optimization */}
          {currentStep === 6 && trainingResults && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Scale className="w-5 h-5" />
                  Threshold Optimization
                </CardTitle>
                <CardDescription>
                  Find the optimal classification threshold using different optimization strategies.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ThresholdOptimizer
                  thresholdData={thresholdData}
                  currentThreshold={selectedThreshold}
                  onThresholdChange={setSelectedThreshold}
                  onOptimalThresholdSelect={handleThresholdOptimization}
                  showAdvancedMetrics={true}
                />

                <div className="flex justify-between mt-6">
                  <Button variant="outline" onClick={handlePrevious}>
                    <ArrowLeft className="w-4 h-4 mr-2" />
                    Previous
                  </Button>
                  <Button onClick={handleNext}>
                    View Final Results
                    <ArrowRight className="w-4 h-4 ml-2" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Step 7: Results & Deploy */}
          {currentStep === 7 && trainingResults && (
            <div className="space-y-6">
              {/* Results Summary */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <BarChart3 className="w-5 h-5" />
                    Binary Classification Results
                  </CardTitle>
                  <CardDescription>
                    Performance summary and detailed analysis
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div className="text-center p-4 bg-muted/50 rounded-lg">
                      <div className="text-2xl font-bold text-primary">
                        {(trainingResults.metrics.accuracy * 100).toFixed(1)}%
                      </div>
                      <div className="text-sm text-muted-foreground">Accuracy</div>
                    </div>
                    <div className="text-center p-4 bg-muted/50 rounded-lg">
                      <div className="text-2xl font-bold text-green-600">
                        {(trainingResults.metrics.roc_auc * 100).toFixed(1)}%
                      </div>
                      <div className="text-sm text-muted-foreground">ROC AUC</div>
                    </div>
                    <div className="text-center p-4 bg-muted/50 rounded-lg">
                      <div className="text-2xl font-bold text-blue-600">
                        {(trainingResults.metrics.f1_score * 100).toFixed(1)}%
                      </div>
                      <div className="text-sm text-muted-foreground">F1 Score</div>
                    </div>
                    <div className="text-center p-4 bg-muted/50 rounded-lg">
                      <div className="text-2xl font-bold text-purple-600">
                        {selectedThreshold.toFixed(3)}
                      </div>
                      <div className="text-sm text-muted-foreground">Optimal Threshold</div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Enhanced Deployment */}
              <DeployStep
                modelId={trainingResults.model_id}
                modelName={`Binary Model ${trainingResults.model_id ? trainingResults.model_id.slice(-8) : ''}`}
                classificationType="binary"
                trainingMetrics={{
                  accuracy: trainingResults.metrics.accuracy,
                  f1_score: trainingResults.metrics.f1_score,
                  precision: trainingResults.metrics.precision,
                  recall: trainingResults.metrics.recall,
                  training_time: trainingResults.training_time,
                  model_size_mb: trainingResults.model_size
                }}
                userLicense={userLicense}
                onComplete={(deploymentInfo) => {
                  console.log('Deployment completed:', deploymentInfo);
                  if (onComplete) {
                    onComplete({
                      type: 'binary',
                      taskId: trainingResults.model_id,
                      results: trainingResults,
                      metrics: trainingResults.metrics,
                      deployment: deploymentInfo,
                      threshold: selectedThreshold,
                      positiveClass,
                      negativeClass
                    });
                  }
                }}
                onBack={() => setCurrentStep(6)}
              />
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default BinaryClassificationWorkflow;
