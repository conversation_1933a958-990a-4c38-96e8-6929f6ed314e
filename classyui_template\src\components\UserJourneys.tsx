import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { 
  Upload, 
  Brain, 
  Lightbulb, 
  Settings, 
  Zap, 
  BarChart3, 
  Download, 
  Rocket,
  Database,
  Sliders,
  Code,
  Activity,
  GitCompare,
  Cloud,
  ArrowRight,
  User,
  GraduationCap
} from "lucide-react";

const beginnerSteps = [
  {
    icon: Upload,
    title: "Upload Data",
    description: "Simply drag and drop your dataset",
    detail: "Support for CSV, JSON, and other formats"
  },
  {
    icon: Brain,
    title: "Smart Detection",
    description: "AI analyzes your data automatically",
    detail: "Intelligent pattern recognition"
  },
  {
    icon: Lightbulb,
    title: "Get Recommendations",
    description: "Platform suggests optimal classification type",
    detail: "ML-powered suggestions"
  },
  {
    icon: Settings,
    title: "Guided Setup",
    description: "Wizard walks you through configuration",
    detail: "Step-by-step guidance"
  },
  {
    icon: Zap,
    title: "Method Selection",
    description: "Choose custom model or LLM inference",
    detail: "Smart recommendations included"
  },
  {
    icon: Activity,
    title: "Automated Training",
    description: "Model trains with optimal settings",
    detail: "No manual tuning required"
  },
  {
    icon: BarChart3,
    title: "View Results",
    description: "Clear results with explanations",
    detail: "Easy-to-understand metrics"
  },
  {
    icon: Rocket,
    title: "Export & Deploy",
    description: "One-click deployment ready",
    detail: "Multiple export formats"
  }
];

const expertSteps = [
  {
    icon: Database,
    title: "Data Upload",
    description: "Upload with advanced preprocessing options",
    detail: "Full data pipeline control"
  },
  {
    icon: Sliders,
    title: "Manual Selection",
    description: "Choose exact classification methodology",
    detail: "Complete configuration access"
  },
  {
    icon: Code,
    title: "Advanced Configuration",
    description: "Access to all platform parameters",
    detail: "Fine-grained control"
  },
  {
    icon: Brain,
    title: "Custom Architecture",
    description: "Define your own model architecture",
    detail: "Architectural flexibility"
  },
  {
    icon: Settings,
    title: "Hyperparameter Tuning",
    description: "Optimize model performance",
    detail: "Advanced optimization tools"
  },
  {
    icon: Activity,
    title: "Training Monitoring",
    description: "Real-time training insights",
    detail: "Detailed monitoring dashboard"
  },
  {
    icon: GitCompare,
    title: "Model Comparison",
    description: "Compare multiple model variants",
    detail: "A/B testing capabilities"
  },
  {
    icon: Cloud,
    title: "Production Deployment",
    description: "Enterprise-grade deployment",
    detail: "Scalable infrastructure"
  }
];

export const UserJourneys = () => {
  return (
    <section className="py-20 bg-background">
      <div className="max-w-7xl mx-auto px-6">
        <div className="text-center mb-16">
          <Badge variant="outline" className="mb-4">
            <User className="w-4 h-4 mr-2" />
            User Journeys
          </Badge>
          <h2 className="text-4xl md:text-5xl font-bold mb-6">
            Two Paths, 
            <span className="bg-gradient-primary bg-clip-text text-transparent"> One Platform</span>
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Whether you're starting your ML journey or you're a seasoned expert, 
            ClassyWeb adapts to your expertise level with tailored workflows.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          {/* Beginner Journey */}
          <div className="space-y-8">
            <div className="text-center">
              <div className="inline-flex items-center gap-2 bg-ml-success/10 text-ml-success px-4 py-2 rounded-full mb-4">
                <GraduationCap className="w-5 h-5" />
                <span className="font-semibold">Beginner Journey</span>
              </div>
              <h3 className="text-2xl font-bold mb-2">Guided Workflow</h3>
              <p className="text-muted-foreground">
                Perfect for users new to ML with smart defaults and explanations
              </p>
            </div>

            <div className="space-y-4">
              {beginnerSteps.map((step, index) => {
                const Icon = step.icon;
                return (
                  <Card key={index} className="group hover:shadow-card transition-all duration-300 border-border/50 hover:border-ml-success/20">
                    <CardHeader className="pb-3">
                      <div className="flex items-start gap-4">
                        <div className="w-10 h-10 rounded-lg bg-ml-success/10 flex items-center justify-center flex-shrink-0 group-hover:bg-ml-success/20 transition-colors">
                          <Icon className="w-5 h-5 text-ml-success" />
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-1">
                            <span className="text-xs font-bold text-ml-success bg-ml-success/10 px-2 py-1 rounded">
                              {index + 1}
                            </span>
                            <CardTitle className="text-lg">{step.title}</CardTitle>
                          </div>
                          <CardDescription className="text-sm">
                            {step.description}
                          </CardDescription>
                          <p className="text-xs text-ml-success mt-1 font-medium">
                            {step.detail}
                          </p>
                        </div>
                      </div>
                    </CardHeader>
                  </Card>
                );
              })}
            </div>

            <div className="text-center pt-4">
              <Button className="group bg-ml-success hover:bg-ml-success/90" asChild>
                <a href="/beginner">
                  Start Guided Journey
                  <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
                </a>
              </Button>
            </div>
          </div>

          {/* Expert Journey */}
          <div className="space-y-8">
            <div className="text-center">
              <div className="inline-flex items-center gap-2 bg-ml-primary/10 text-ml-primary px-4 py-2 rounded-full mb-4">
                <Brain className="w-5 h-5" />
                <span className="font-semibold">Expert Journey</span>
              </div>
              <h3 className="text-2xl font-bold mb-2">Advanced Controls</h3>
              <p className="text-muted-foreground">
                Full control for data scientists and ML engineers
              </p>
            </div>

            <div className="space-y-4">
              {expertSteps.map((step, index) => {
                const Icon = step.icon;
                return (
                  <Card key={index} className="group hover:shadow-card transition-all duration-300 border-border/50 hover:border-ml-primary/20">
                    <CardHeader className="pb-3">
                      <div className="flex items-start gap-4">
                        <div className="w-10 h-10 rounded-lg bg-ml-primary/10 flex items-center justify-center flex-shrink-0 group-hover:bg-ml-primary/20 transition-colors">
                          <Icon className="w-5 h-5 text-ml-primary" />
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-1">
                            <span className="text-xs font-bold text-ml-primary bg-ml-primary/10 px-2 py-1 rounded">
                              {index + 1}
                            </span>
                            <CardTitle className="text-lg">{step.title}</CardTitle>
                          </div>
                          <CardDescription className="text-sm">
                            {step.description}
                          </CardDescription>
                          <p className="text-xs text-ml-primary mt-1 font-medium">
                            {step.detail}
                          </p>
                        </div>
                      </div>
                    </CardHeader>
                  </Card>
                );
              })}
            </div>

            <div className="text-center pt-4">
              <Button className="group" asChild>
                <a href="/expert">
                  Access Expert Mode
                  <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
                </a>
              </Button>
            </div>
          </div>
        </div>

        {/* Bottom CTA */}
        <div className="text-center mt-16 pt-12 border-t border-border">
          <h3 className="text-2xl font-bold mb-4">Not Sure Which Path to Take?</h3>
          <p className="text-muted-foreground mb-6 max-w-2xl mx-auto">
            Start with our guided workflow and seamlessly switch to expert mode 
            as you become more comfortable with the platform.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button variant="outline" size="lg">
              Take Quick Assessment
            </Button>
            <Button size="lg" className="bg-gradient-primary hover:shadow-glow">
              Try Both Approaches
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
};