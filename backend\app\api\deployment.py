"""Model Deployment API for ClassyWeb ML Platform.

This module provides comprehensive deployment functionality for trained models,
including local export, API generation, cloud deployment, and integration management.
"""

import logging
import traceback
import uuid
import os
import zipfile
import json
import tempfile
from typing import List, Dict, Any, Optional
from datetime import datetime, timezone, timedelta
from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks
from sqlalchemy.orm import Session
from pydantic import BaseModel, Field

from ..database import (
    get_db, User, TrainingSession, ModelPerformance,
    TrainingStatusEnum, get_file
)
from ..auth import get_current_user
from ..license_manager import license_manager
from ..config import MODEL_ARTIFACTS_DIR

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/deployment", tags=["deployment"])


# --- Request/Response Models ---

class DeploymentRequest(BaseModel):
    model_id: str = Field(..., description="ID of the trained model to deploy")
    deployment_type: str = Field(..., description="Type of deployment: local, api, batch, cloud, edge")
    config: Dict[str, Any] = Field(default_factory=dict, description="Deployment configuration")
    name: Optional[str] = Field(None, description="Custom deployment name")
    description: Optional[str] = Field(None, description="Deployment description")


class ExportRequest(BaseModel):
    model_id: str = Field(..., description="ID of the model to export")
    format: str = Field(default="pytorch", description="Export format: pytorch, onnx, huggingface, tensorflow")
    include_tokenizer: bool = Field(default=True, description="Include tokenizer in export")
    include_metadata: bool = Field(default=True, description="Include training metadata")
    compression: str = Field(default="zip", description="Compression format: zip, tar, none")


class APIDeploymentConfig(BaseModel):
    name: str = Field(..., description="API name")
    version: str = Field(default="1.0.0", description="API version")
    description: str = Field(..., description="API description")
    enable_auth: bool = Field(default=True, description="Enable API authentication")
    enable_rate_limit: bool = Field(default=True, description="Enable rate limiting")
    max_requests_per_minute: int = Field(default=100, description="Rate limit")


class DeploymentResponse(BaseModel):
    deployment_id: str
    model_id: str
    deployment_type: str
    status: str
    endpoint: Optional[str] = None
    download_url: Optional[str] = None
    api_key: Optional[str] = None
    created_at: str
    config: Dict[str, Any]


class DeploymentStatus(BaseModel):
    deployment_id: str
    status: str
    progress: int
    message: str
    endpoint: Optional[str] = None
    error: Optional[str] = None


class IntegrationCodeResponse(BaseModel):
    python_code: str
    javascript_code: str
    curl_code: str
    documentation_url: str


# --- Helper Functions ---

def validate_model_access(db: Session, model_id: str, user_id: int) -> TrainingSession:
    """Validate that user has access to the model."""
    training_session = db.query(TrainingSession).filter(
        TrainingSession.id == model_id,
        TrainingSession.user_id == user_id,
        TrainingSession.status == TrainingStatusEnum.COMPLETED
    ).first()
    
    if not training_session:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Model not found or not accessible"
        )
    
    return training_session


def check_deployment_limits(user: User, deployment_type: str) -> bool:
    """Check if user can create this type of deployment based on license."""
    license_info = license_manager.get_user_license(user.id)
    
    if not license_info:
        return deployment_type == 'local'  # Only local export for free users
    
    license_type = license_info.license_type.value
    
    # Define deployment permissions by license type
    permissions = {
        'personal': ['local', 'batch'],
        'professional': ['local', 'batch', 'api'],
        'enterprise': ['local', 'batch', 'api', 'cloud', 'edge']
    }
    
    return deployment_type in permissions.get(license_type, ['local'])


def generate_deployment_id() -> str:
    """Generate unique deployment ID."""
    return f"deploy_{uuid.uuid4().hex[:12]}"


def create_model_export_package(
    model_path: str,
    export_config: ExportRequest,
    output_dir: str
) -> str:
    """Create exportable model package."""
    try:
        # Create temporary directory for package contents
        with tempfile.TemporaryDirectory() as temp_dir:
            package_dir = os.path.join(temp_dir, "model_package")
            os.makedirs(package_dir, exist_ok=True)
            
            # Copy model files
            if os.path.exists(model_path):
                # Copy main model file
                import shutil
                if os.path.isdir(model_path):
                    shutil.copytree(model_path, os.path.join(package_dir, "model"))
                else:
                    shutil.copy2(model_path, os.path.join(package_dir, "model.pt"))
            
            # Add metadata if requested
            if export_config.include_metadata:
                metadata = {
                    "export_format": export_config.format,
                    "export_date": datetime.now(timezone.utc).isoformat(),
                    "include_tokenizer": export_config.include_tokenizer,
                    "classyweb_version": "1.0.0"
                }
                
                with open(os.path.join(package_dir, "metadata.json"), "w") as f:
                    json.dump(metadata, f, indent=2)
            
            # Add README
            readme_content = f"""# ClassyWeb Model Export

This package contains a trained hierarchical classification model exported from ClassyWeb.

## Contents
- model.pt: The trained model file
- metadata.json: Export metadata and configuration
- requirements.txt: Python dependencies

## Usage
```python
import torch
from transformers import AutoTokenizer, AutoModel

# Load the model
model = torch.load('model.pt')
model.eval()

# Use for inference
# ... your inference code here
```

Export Date: {datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S UTC')}
Format: {export_config.format}
"""
            
            with open(os.path.join(package_dir, "README.md"), "w") as f:
                f.write(readme_content)
            
            # Add requirements.txt
            requirements = [
                "torch>=1.9.0",
                "transformers>=4.20.0",
                "numpy>=1.21.0",
                "pandas>=1.3.0"
            ]
            
            with open(os.path.join(package_dir, "requirements.txt"), "w") as f:
                f.write("\n".join(requirements))
            
            # Create archive
            output_file = os.path.join(output_dir, f"model_export_{uuid.uuid4().hex[:8]}")
            
            if export_config.compression == "zip":
                output_file += ".zip"
                with zipfile.ZipFile(output_file, 'w', zipfile.ZIP_DEFLATED) as zipf:
                    for root, dirs, files in os.walk(package_dir):
                        for file in files:
                            file_path = os.path.join(root, file)
                            arcname = os.path.relpath(file_path, package_dir)
                            zipf.write(file_path, arcname)
            elif export_config.compression == "tar":
                import tarfile
                output_file += ".tar.gz"
                with tarfile.open(output_file, "w:gz") as tar:
                    tar.add(package_dir, arcname=".")
            else:
                # No compression - just copy directory
                import shutil
                shutil.copytree(package_dir, output_file)
            
            return output_file
            
    except Exception as e:
        logger.error(f"Failed to create export package: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create export package: {str(e)}"
        )


# --- API Endpoints ---

@router.post("/deploy", response_model=DeploymentResponse)
async def deploy_model(
    deployment_request: DeploymentRequest,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Deploy a trained model using the specified deployment type."""
    try:
        # Validate model access
        training_session = validate_model_access(db, deployment_request.model_id, current_user.id)
        
        # Check deployment limits
        if not check_deployment_limits(current_user, deployment_request.deployment_type):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Deployment type '{deployment_request.deployment_type}' not available with your license"
            )
        
        # Generate deployment ID
        deployment_id = generate_deployment_id()
        
        # Create deployment record (simplified - in real implementation, store in database)
        deployment_info = {
            "deployment_id": deployment_id,
            "model_id": deployment_request.model_id,
            "deployment_type": deployment_request.deployment_type,
            "status": "pending",
            "created_at": datetime.now(timezone.utc).isoformat(),
            "config": deployment_request.config,
            "user_id": current_user.id
        }
        
        # Start deployment process in background
        if deployment_request.deployment_type == "api":
            # Generate API endpoint
            endpoint = f"https://api.classyweb.com/models/{deployment_id}"
            api_key = f"cw_{uuid.uuid4().hex}"
            
            deployment_info.update({
                "status": "active",
                "endpoint": endpoint,
                "api_key": api_key
            })
            
        elif deployment_request.deployment_type == "local":
            # Generate download URL
            download_url = f"https://api.classyweb.com/deployment/{deployment_id}/download"
            
            deployment_info.update({
                "status": "ready",
                "download_url": download_url
            })
        
        # Update usage tracking
        license_manager.update_usage(current_user.id, 'deployment', 1)
        
        return DeploymentResponse(**deployment_info)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Deployment failed: {e}")
        logger.error(traceback.format_exc())
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Deployment failed: {str(e)}"
        )


@router.post("/export", response_model=Dict[str, str])
async def export_model(
    export_request: ExportRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Export a trained model for download."""
    try:
        # Validate model access
        training_session = validate_model_access(db, export_request.model_id, current_user.id)
        
        # Get model path
        model_path = os.path.join(MODEL_ARTIFACTS_DIR, export_request.model_id)
        
        if not os.path.exists(model_path):
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Model files not found"
            )
        
        # Create export package
        export_dir = os.path.join(MODEL_ARTIFACTS_DIR, "exports")
        os.makedirs(export_dir, exist_ok=True)
        
        export_file = create_model_export_package(model_path, export_request, export_dir)
        
        # Generate download URL (in real implementation, use signed URLs)
        download_url = f"https://api.classyweb.com/files/download/{os.path.basename(export_file)}"
        
        # Set expiration time
        expires_at = (datetime.now(timezone.utc) + timedelta(hours=24)).isoformat()
        
        return {
            "download_url": download_url,
            "expires_at": expires_at,
            "file_size": str(os.path.getsize(export_file)),
            "format": export_request.format
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Export failed: {e}")
        logger.error(traceback.format_exc())
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Export failed: {str(e)}"
        )


@router.get("/deployments", response_model=List[DeploymentResponse])
async def list_deployments(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """List all deployments for the current user."""
    try:
        # In real implementation, query from database
        # For now, return mock data
        deployments = [
            {
                "deployment_id": "deploy_example123",
                "model_id": "model_456",
                "deployment_type": "api",
                "status": "active",
                "endpoint": "https://api.classyweb.com/models/deploy_example123",
                "created_at": datetime.now(timezone.utc).isoformat(),
                "config": {"enable_auth": True, "rate_limit": 100}
            }
        ]

        return [DeploymentResponse(**deployment) for deployment in deployments]

    except Exception as e:
        logger.error(f"Failed to list deployments: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve deployments"
        )


@router.get("/deployments/{deployment_id}/status", response_model=DeploymentStatus)
async def get_deployment_status(
    deployment_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get the status of a specific deployment."""
    try:
        # In real implementation, query from database
        # For now, return mock status
        status_info = {
            "deployment_id": deployment_id,
            "status": "active",
            "progress": 100,
            "message": "Deployment is running successfully",
            "endpoint": f"https://api.classyweb.com/models/{deployment_id}"
        }

        return DeploymentStatus(**status_info)

    except Exception as e:
        logger.error(f"Failed to get deployment status: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve deployment status"
        )


@router.delete("/deployments/{deployment_id}")
async def delete_deployment(
    deployment_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Delete a deployment."""
    try:
        # In real implementation, remove from database and cleanup resources
        logger.info(f"Deleting deployment {deployment_id} for user {current_user.id}")

        return {"message": "Deployment deleted successfully"}

    except Exception as e:
        logger.error(f"Failed to delete deployment: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete deployment"
        )


@router.get("/deployments/{deployment_id}/integration-code", response_model=IntegrationCodeResponse)
async def get_integration_code(
    deployment_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Generate integration code examples for a deployment."""
    try:
        # Get deployment info (mock for now)
        endpoint = f"https://api.classyweb.com/models/{deployment_id}"

        # Generate Python code
        python_code = f'''import requests
import json

# ClassyWeb Hierarchical Classification API
API_ENDPOINT = "{endpoint}"
API_KEY = "your-api-key-here"

def classify_text(text, hierarchy_level=None):
    """Classify text using your trained hierarchical model."""
    headers = {{
        "Authorization": f"Bearer {{API_KEY}}",
        "Content-Type": "application/json"
    }}

    payload = {{
        "text": text,
        "hierarchy_level": hierarchy_level,
        "return_probabilities": True
    }}

    response = requests.post(f"{{API_ENDPOINT}}/classify",
                           headers=headers,
                           json=payload)

    if response.status_code == 200:
        return response.json()
    else:
        raise Exception(f"Classification failed: {{response.text}}")

# Example usage
result = classify_text("Your text to classify here")
print(f"Classification: {{result['prediction']}}")
print(f"Confidence: {{result['confidence']:.2f}}")
print(f"Hierarchy path: {{' > '.join(result['hierarchy_path'])}}")'''

        # Generate JavaScript code
        javascript_code = f'''// ClassyWeb Hierarchical Classification API
const API_ENDPOINT = "{endpoint}";
const API_KEY = "your-api-key-here";

async function classifyText(text, hierarchyLevel = null) {{
  const response = await fetch(`${{API_ENDPOINT}}/classify`, {{
    method: 'POST',
    headers: {{
      'Authorization': `Bearer ${{API_KEY}}`,
      'Content-Type': 'application/json'
    }},
    body: JSON.stringify({{
      text: text,
      hierarchy_level: hierarchyLevel,
      return_probabilities: true
    }})
  }});

  if (!response.ok) {{
    throw new Error(`Classification failed: ${{response.statusText}}`);
  }}

  return await response.json();
}}

// Example usage
classifyText("Your text to classify here")
  .then(result => {{
    console.log('Classification:', result.prediction);
    console.log('Confidence:', result.confidence);
    console.log('Hierarchy path:', result.hierarchy_path.join(' > '));
  }})
  .catch(error => console.error('Error:', error));'''

        # Generate cURL code
        curl_code = f'''# ClassyWeb Hierarchical Classification API
curl -X POST "{endpoint}/classify" \\
  -H "Authorization: Bearer your-api-key-here" \\
  -H "Content-Type: application/json" \\
  -d '{{
    "text": "Your text to classify here",
    "hierarchy_level": null,
    "return_probabilities": true
  }}' '''

        return IntegrationCodeResponse(
            python_code=python_code,
            javascript_code=javascript_code,
            curl_code=curl_code,
            documentation_url=f"https://docs.classyweb.com/api/deployments/{deployment_id}"
        )

    except Exception as e:
        logger.error(f"Failed to generate integration code: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to generate integration code"
        )


@router.post("/deployments/{deployment_id}/test")
async def test_deployment(
    deployment_id: str,
    test_data: Dict[str, Any],
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Test a deployment with sample data."""
    try:
        # Mock test response
        test_result = {
            "deployment_id": deployment_id,
            "test_status": "success",
            "response_time_ms": 150,
            "prediction": "Category A > Subcategory 1",
            "confidence": 0.87,
            "hierarchy_path": ["Category A", "Subcategory 1"],
            "probabilities": {
                "Category A": 0.87,
                "Category B": 0.13
            }
        }

        return test_result

    except Exception as e:
        logger.error(f"Failed to test deployment: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to test deployment"
        )


@router.get("/deployment-options")
async def get_deployment_options(
    current_user: User = Depends(get_current_user)
):
    """Get available deployment options based on user license."""
    try:
        license_info = license_manager.get_user_license(current_user.id)
        license_type = license_info.license_type.value if license_info else 'personal'

        # Define options by license type
        all_options = {
            'local': {
                'title': 'Local Model Export',
                'description': 'Download trained model files for offline use',
                'available': True,
                'estimated_time': '2-5 minutes'
            },
            'api': {
                'title': 'API Endpoint',
                'description': 'Generate REST API for real-time inference',
                'available': license_type in ['professional', 'enterprise'],
                'requires_license': 'professional',
                'estimated_time': '5-10 minutes'
            },
            'batch': {
                'title': 'Batch Processing',
                'description': 'Setup for large-scale batch classification',
                'available': True,
                'estimated_time': '3-7 minutes'
            },
            'cloud': {
                'title': 'Cloud Deployment',
                'description': 'Deploy to AWS/GCP/Azure with auto-scaling',
                'available': license_type == 'enterprise',
                'requires_license': 'enterprise',
                'estimated_time': '10-15 minutes'
            },
            'edge': {
                'title': 'Edge Deployment',
                'description': 'Optimize for mobile and edge devices',
                'available': license_type == 'enterprise',
                'requires_license': 'enterprise',
                'estimated_time': '8-12 minutes'
            }
        }

        return {
            'user_license': license_type,
            'options': all_options
        }

    except Exception as e:
        logger.error(f"Failed to get deployment options: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve deployment options"
        )
