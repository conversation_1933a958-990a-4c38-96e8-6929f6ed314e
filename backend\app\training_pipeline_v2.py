"""Unified Training Pipeline v2 for ClassyWeb ML Platform.

This module provides a unified training pipeline that supports both custom model
training (with Unsloth acceleration) and LLM inference approaches across all
classification types.
"""

import logging
import asyncio
import time
import traceback
import json
import os
import uuid
from typing import Dict, Any, Optional, Callable
from sqlalchemy.orm import Session
from datetime import datetime, timezone
import pandas as pd

from .database import (
    SessionLocal, TrainingSession, ModelPerformance, ClassificationConfig,
    TrainingStatusEnum, ClassificationTypeEnum, TrainingMethodEnum,
    update_task, get_file, create_file
)
from .config import MODEL_ARTIFACTS_DIR, UPLOAD_DIR
from .utils.helpers import load_data
from .llm_classifier import initialize_llm_client
from .classification_engines import (
    get_engine_for_type, 
    validate_classification_type,
    TrainingConfig as EngineTrainingConfig,
    ClassificationType
)

logger = logging.getLogger(__name__)


class UnifiedTrainingPipeline:
    """Unified training pipeline supporting all classification types and methods."""
    
    def __init__(self):
        self.active_sessions = {}  # Track active training sessions
        
    async def start_training_session(
        self,
        session_id: str,
        data: pd.DataFrame,
        progress_callback: Optional[Callable] = None
    ) -> bool:
        """Start a training session asynchronously."""
        try:
            # Get session from database
            with SessionLocal() as db:
                session = db.query(TrainingSession).filter(
                    TrainingSession.id == session_id
                ).first()
                
                if not session:
                    logger.error(f"Training session {session_id} not found")
                    return False
                
                # Update session status
                session.status = TrainingStatusEnum.RUNNING
                session.started_at = datetime.now(timezone.utc)
                session.current_stage = "initialization"
                session.progress_percentage = 0.0
                db.commit()
            
            # Store session in active sessions
            self.active_sessions[session_id] = {
                'status': 'running',
                'start_time': time.time()
            }
            
            # Create progress callback that updates database
            def db_progress_callback(progress_data: Dict[str, Any]):
                try:
                    with SessionLocal() as db:
                        session = db.query(TrainingSession).filter(
                            TrainingSession.id == session_id
                        ).first()
                        
                        if session:
                            session.current_stage = progress_data.get('stage', session.current_stage)
                            session.progress_percentage = progress_data.get('progress', session.progress_percentage) * 100
                            session.progress_data = progress_data
                            db.commit()
                            
                    # Call external callback if provided
                    if progress_callback:
                        progress_callback(progress_data)
                        
                except Exception as e:
                    logger.error(f"Failed to update progress: {e}")
            
            # Get training configuration
            with SessionLocal() as db:
                session = db.query(TrainingSession).filter(
                    TrainingSession.id == session_id
                ).first()
                
                # Filter training config to only include valid TrainingConfig fields
                valid_config_fields = {
                    'classification_type', 'training_method', 'text_columns', 'label_columns',
                    'validation_split', 'base_model', 'max_length', 'learning_rate', 'batch_size',
                    'num_epochs', 'warmup_steps', 'weight_decay', 'use_unsloth', 'fp16',
                    'gradient_accumulation_steps', 'gradient_checkpointing', 'llm_provider',
                    'llm_model', 'prompt_template', 'temperature', 'max_tokens', 'class_weights',
                    'threshold', 'early_stopping_patience', 'save_strategy', 'evaluation_strategy',
                    'metadata'
                }

                filtered_config = {k: v for k, v in session.training_config.items()
                                 if k in valid_config_fields}

                logger.info(f"Original config keys: {list(session.training_config.keys())}")
                logger.info(f"Filtered config keys: {list(filtered_config.keys())}")

                # Handle text_column vs text_columns conversion
                if 'text_column' in session.training_config and 'text_columns' not in filtered_config:
                    filtered_config['text_columns'] = [session.training_config['text_column']]
                    logger.info(f"Converted text_column to text_columns: {filtered_config['text_columns']}")

                # Add required fields if missing
                classification_type = validate_classification_type(session.classification_type.value)

                # Convert training method enum to TrainingMethod
                from .classification_engines import TrainingMethod
                if session.training_method.value == "custom":
                    training_method = TrainingMethod.CUSTOM
                elif session.training_method.value == "llm":
                    training_method = TrainingMethod.LLM
                else:
                    training_method = TrainingMethod.CUSTOM  # Default fallback

                filtered_config['classification_type'] = classification_type
                filtered_config['training_method'] = training_method

                # Handle dynamic hierarchy configuration
                hierarchy_levels = session.training_config.get('hierarchy_levels', [])
                hierarchical_config = session.training_config.get('hierarchical_config', {})

                # Ensure required fields have defaults
                if 'text_columns' not in filtered_config:
                    # Try to get from original config first
                    if 'text_column' in session.training_config:
                        filtered_config['text_columns'] = [session.training_config['text_column']]
                    else:
                        filtered_config['text_columns'] = ['text']  # Default fallback

                if 'label_columns' not in filtered_config:
                    # Use hierarchy_levels if available, otherwise use label_columns
                    if hierarchy_levels:
                        filtered_config['label_columns'] = hierarchy_levels
                        logger.info(f"Using hierarchy_levels as label_columns: {hierarchy_levels}")
                    else:
                        filtered_config['label_columns'] = ['label']  # Default fallback

                # Add hierarchical configuration metadata
                if hierarchy_levels or hierarchical_config:
                    filtered_config['metadata'] = {
                        'hierarchy_levels': hierarchy_levels,
                        'hierarchical_config': hierarchical_config,
                        'is_dynamic_hierarchy': True
                    }
                    logger.info(f"Dynamic hierarchy configuration detected: {hierarchy_levels}")

                logger.info(f"Creating TrainingConfig with: {filtered_config}")

                try:
                    training_config = EngineTrainingConfig(**filtered_config)
                    logger.info("TrainingConfig created successfully")
                except Exception as config_error:
                    logger.error(f"Failed to create TrainingConfig: {config_error}")
                    logger.error(f"Config data: {filtered_config}")
                    raise
            
            # Create appropriate engine
            engine = get_engine_for_type(classification_type)
            
            # Start training based on method
            if session.training_method == TrainingMethodEnum.CUSTOM:
                training_result = await engine.train_custom_model(
                    data=data,
                    config=training_config,
                    progress_callback=db_progress_callback
                )
            else:  # LLM inference
                # For LLM inference, validate configuration and prepare for inference
                db_progress_callback({"stage": "llm_validation", "progress": 0.2})

                try:
                    # Validate LLM configuration
                    llm_provider = training_config.llm_provider or 'openai'
                    llm_model = training_config.llm_model or 'gpt-3.5-turbo'

                    # Test LLM connection and validate configuration
                    db_progress_callback({"stage": "llm_connection", "progress": 0.4})

                    # Get API key for the provider
                    import os
                    api_key = None
                    if llm_provider.lower() == 'openai':
                        api_key = os.getenv('OPENAI_API_KEY')
                    elif llm_provider.lower() == 'groq':
                        api_key = os.getenv('GROQ_API_KEY')
                    elif llm_provider.lower() == 'gemini':
                        api_key = os.getenv('GEMINI_API_KEY')

                    if not api_key:
                        raise ValueError(f"API key not configured for provider: {llm_provider}")

                    # Initialize and test LLM client
                    llm_client = initialize_llm_client(
                        provider=llm_provider,
                        api_key=api_key,
                        model_name=llm_model
                    )

                    db_progress_callback({"stage": "llm_ready", "progress": 1.0})

                    # Create successful training result for LLM configuration
                    training_result = type('TrainingResult', (), {
                        'model_id': f"llm_config_{session_id}",
                        'training_time': 2.0,
                        'final_metrics': {
                            'validation': 'llm_ready',
                            'provider': llm_provider,
                            'model': llm_model,
                            'configuration_valid': True
                        },
                        'training_history': [
                            {"stage": "validation", "progress": 0.5, "message": "LLM configuration validated"},
                            {"stage": "ready", "progress": 1.0, "message": "Ready for inference"}
                        ],
                        'model_path': None,
                        'tokenizer_path': None,
                        'config_path': f"llm_config_{session_id}.json",
                        'error': None
                    })()

                except Exception as e:
                    logger.error(f"LLM validation failed: {str(e)}")
                    training_result = type('TrainingResult', (), {
                        'model_id': "",
                        'training_time': 0.0,
                        'final_metrics': {},
                        'training_history': [],
                        'model_path': None,
                        'tokenizer_path': None,
                        'config_path': None,
                        'error': f"LLM validation failed: {str(e)}"
                    })()
            
            # Update session with results
            with SessionLocal() as db:
                try:
                    session = db.query(TrainingSession).filter(
                        TrainingSession.id == session_id
                    ).first()

                    if training_result.error:
                        session.status = TrainingStatusEnum.FAILED
                        session.error_message = training_result.error

                        # Update corresponding task status
                        update_task(db, str(session_id), {
                            "status": "FAILED",
                            "message": training_result.error
                        })
                    else:
                        session.status = TrainingStatusEnum.COMPLETED
                        session.model_id = training_result.model_id
                        session.final_metrics = training_result.final_metrics
                        session.training_history = training_result.training_history
                        session.model_path = training_result.model_path
                        session.tokenizer_path = training_result.tokenizer_path
                        session.config_path = training_result.config_path

                        # Create results file for frontend consumption
                        MODEL_ARTIFACTS_DIR.mkdir(parents=True, exist_ok=True)
                        results_file_path = MODEL_ARTIFACTS_DIR / f"training_results_{session_id}.json"

                        # Prepare results data
                        results_data = {
                            "model_id": training_result.model_id,
                            "training_time": training_result.training_time,
                            "final_metrics": training_result.final_metrics,
                            "training_history": training_result.training_history,
                            "model_path": training_result.model_path,
                            "config_path": training_result.config_path,
                            "session_id": str(session_id),
                            "classification_type": session.classification_type.value,
                            "training_method": session.training_method.value,
                            "completed_at": datetime.now(timezone.utc).isoformat()
                        }

                        # Save results to file
                        with open(results_file_path, 'w') as f:
                            json.dump(results_data, f, indent=2, default=str)

                        # Update corresponding task status
                        update_task(db, str(session_id), {
                            "status": "SUCCESS",
                            "message": "Training completed successfully",
                            "result_file_path": str(results_file_path)
                        })

                    session.completed_at = datetime.now(timezone.utc)
                    session.progress_percentage = 100.0
                    session.current_stage = "completed" if not training_result.error else "failed"

                    # Update config_data with model metadata if training was successful
                    if not training_result.error and training_result.final_metrics:
                        try:
                            # Get the associated config
                            config = db.query(ClassificationConfig).filter(ClassificationConfig.id == session.config_id).first()
                            if config:
                                # Update config_data with model metadata
                                config_data = config.config_data.copy() if config.config_data else {}

                                # Add model metadata
                                config_data['model_size'] = training_result.final_metrics.get('model_size_mb', 0.0)
                                config_data['total_samples'] = training_result.final_metrics.get('training_samples', 0)
                                config_data['base_model'] = training_result.final_metrics.get('base_model', 'distilbert-base-uncased')

                                # Calculate model size from model path if not provided
                                if config_data['model_size'] == 0.0 and training_result.final_metrics.get('model_path'):
                                    from ..services.model_service import get_artifact_size
                                    import os
                                    model_path = training_result.final_metrics.get('model_path')
                                    if os.path.exists(model_path):
                                        size_bytes = sum(
                                            os.path.getsize(os.path.join(dirpath, filename))
                                            for dirpath, dirnames, filenames in os.walk(model_path)
                                            for filename in filenames
                                        )
                                        config_data['model_size'] = size_bytes / (1024 * 1024)  # Convert to MB

                                config.config_data = config_data
                                logger.info(f"Updated config_data with model metadata: size={config_data['model_size']:.2f}MB, samples={config_data['total_samples']}")
                        except Exception as config_error:
                            logger.error(f"Failed to update config with model metadata: {config_error}")

                    db.commit()

                    # Create performance record if training was successful
                    if not training_result.error and training_result.final_metrics:
                        try:
                            # Extract model name from training config or generate one
                            model_name = (
                                session.training_config.get('model_name') or
                                session.training_config.get('modelName') or
                                f"{session.classification_type.value}_model_{int(time.time())}"
                            )

                            # Ensure model_name is never None
                            if not model_name:
                                model_name = f"{session.classification_type.value}_model_{int(time.time())}"

                            logger.info(f"Creating performance record with model_name: '{model_name}'")

                            # Create performance record with all required fields
                            performance = ModelPerformance(
                                config_id=session.config_id,
                                model_id=training_result.model_id,
                                model_name=model_name,
                                classification_type=session.classification_type,
                                training_method=session.training_method,
                                metrics=training_result.final_metrics
                            )
                            db.add(performance)
                            db.commit()
                            logger.info(f"Performance record created for model {training_result.model_id} with name '{model_name}'")
                        except Exception as perf_error:
                            logger.error(f"Failed to create performance record: {perf_error}")
                            # Don't fail the entire training for this - just log and continue
                            db.rollback()  # Rollback the failed transaction

                    # Handle dual data post-processing after successful training
                    if not training_result.error:
                        await self._handle_dual_data_post_processing(session, training_result, db)

                except Exception as session_error:
                    logger.error(f"Error updating training session: {session_error}")
                    db.rollback()
                    raise
            
            # Remove from active sessions
            if session_id in self.active_sessions:
                del self.active_sessions[session_id]
            
            return training_result.error is None
            
        except Exception as e:
            logger.error(f"Training session {session_id} failed: {e}")
            logger.error(f"Full traceback: {traceback.format_exc()}")

            # Update session with error
            try:
                with SessionLocal() as db:
                    session = db.query(TrainingSession).filter(
                        TrainingSession.id == session_id
                    ).first()

                    if session:
                        session.status = TrainingStatusEnum.FAILED
                        session.error_message = str(e)
                        session.completed_at = datetime.now(timezone.utc)
                        session.current_stage = "failed"
                        session.progress_percentage = 0.0
                        db.commit()

                        # Update corresponding task status
                        try:
                            update_task(db, str(session_id), {
                                "status": "FAILED",
                                "message": f"Training failed: {str(e)}"
                            })
                        except Exception as task_update_error:
                            logger.error(f"Failed to update task status: {task_update_error}")
            except Exception as db_error:
                logger.error(f"Failed to update session with error: {db_error}")

            # Remove from active sessions
            if session_id in self.active_sessions:
                del self.active_sessions[session_id]

            return False
    
    def get_session_status(self, session_id: str) -> Dict[str, Any]:
        """Get the current status of a training session."""
        if session_id in self.active_sessions:
            return self.active_sessions[session_id]
        
        # Check database for completed sessions
        try:
            with SessionLocal() as db:
                session = db.query(TrainingSession).filter(
                    TrainingSession.id == session_id
                ).first()
                
                if session:
                    return {
                        'status': session.status.value,
                        'progress': session.progress_percentage,
                        'stage': session.current_stage,
                        'error': session.error_message
                    }
        except Exception as e:
            logger.error(f"Failed to get session status: {e}")
        
        return {'status': 'unknown'}
    
    def cancel_session(self, session_id: str) -> bool:
        """Cancel an active training session."""
        try:
            if session_id in self.active_sessions:
                # Mark as cancelled in active sessions
                self.active_sessions[session_id]['status'] = 'cancelled'
                
                # Update database
                with SessionLocal() as db:
                    session = db.query(TrainingSession).filter(
                        TrainingSession.id == session_id
                    ).first()
                    
                    if session:
                        session.status = TrainingStatusEnum.CANCELLED
                        session.completed_at = datetime.now(timezone.utc)
                        session.current_stage = "cancelled"
                        session.error_message = "Training cancelled by user"
                        db.commit()
                
                # Remove from active sessions
                del self.active_sessions[session_id]
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Failed to cancel session {session_id}: {e}")
            return False
    
    def get_active_sessions(self) -> Dict[str, Dict[str, Any]]:
        """Get all currently active training sessions."""
        return self.active_sessions.copy()

    async def _handle_dual_data_post_processing(self, session, training_result, db):
        """Handle post-processing for dual data setup - apply trained model to classification data."""
        try:
            logger.info(f"Starting dual data post-processing for session {session.id}")

            # Check if this is a dual data setup
            config_data = session.training_config
            if not config_data.get('dual_data_setup'):
                logger.info(f"Session {session.id} is not a dual data setup, skipping post-processing")
                return

            classification_file_id = config_data.get('classification_file_id')
            if not classification_file_id:
                logger.warning(f"Dual data setup detected but no classification_file_id found for session {session.id}")
                return

            logger.info(f"Starting dual data post-processing for session {session.id}")

            # Load classification data
            classification_file = get_file(db, classification_file_id)
            if not classification_file:
                logger.error(f"Classification file {classification_file_id} not found")
                return

            classification_df = load_data(
                file_path=classification_file.file_path,
                original_filename=classification_file.filename
            )
            if classification_df is None:
                logger.error(f"Failed to load classification data from {classification_file_id}")
                return

            # Get text column from config
            text_column = config_data.get('text_column')
            if not text_column or text_column not in classification_df.columns:
                logger.error(f"Text column '{text_column}' not found in classification data")
                return

            # Extract texts to classify
            texts_to_classify = classification_df[text_column].tolist()

            # Apply trained model to classification data
            from .classification_engines.engine_factory import get_engine_for_type, validate_classification_type

            classification_type = validate_classification_type(session.classification_type.value)
            engine = get_engine_for_type(classification_type)

            # Make predictions
            logger.info(f"Applying trained model {training_result.model_id} to {len(texts_to_classify)} texts")
            try:
                predictions = await engine.predict(texts_to_classify, training_result.model_id)
                logger.info(f"Successfully generated {len(predictions)} predictions")
            except Exception as pred_error:
                logger.error(f"Failed to generate predictions: {pred_error}")
                logger.error(traceback.format_exc())
                raise

            # Create results DataFrame
            results_df = classification_df.copy()

            # Add prediction columns based on classification type
            if classification_type.value == 'hierarchical':
                # For hierarchical, add columns for each level
                hierarchy_levels = config_data.get('hierarchy_levels', [])
                for i, level_name in enumerate(hierarchy_levels):
                    level_predictions = []
                    for pred in predictions:
                        if hasattr(pred, 'hierarchy_path') and len(pred.hierarchy_path) > i:
                            level_predictions.append(pred.hierarchy_path[i])
                        else:
                            level_predictions.append('')
                    results_df[f'predicted_{level_name}'] = level_predictions

                # Add confidence scores
                confidences = [pred.confidence if hasattr(pred, 'confidence') else 0.0 for pred in predictions]
                results_df['prediction_confidence'] = confidences
            else:
                # For other types, add predicted labels and confidence
                predicted_labels = [pred.predicted_label if hasattr(pred, 'predicted_label') else '' for pred in predictions]
                confidences = [pred.confidence if hasattr(pred, 'confidence') else 0.0 for pred in predictions]
                results_df['predicted_label'] = predicted_labels
                results_df['prediction_confidence'] = confidences

            # Save results to file
            results_filename = f"classification_results_{session.id}.csv"
            results_path = UPLOAD_DIR / results_filename
            results_df.to_csv(results_path, index=False)

            # Create file record in database
            results_file = create_file(db, {
                'id': str(uuid.uuid4()),
                'filename': results_filename,
                'file_path': str(results_path),
                'file_size': os.path.getsize(results_path),
                'user_id': session.user_id,
                'num_rows': len(results_df),
                'columns': results_df.columns.tolist()
            })

            # Update session with results file info
            session.results_file_id = results_file.id
            session.current_stage = "classification_complete"

            # Update the task with the results file path so frontend can access it
            from .database import update_task
            update_task(db, str(session.id), {
                "status": "SUCCESS",
                "message": "Classification completed successfully",
                "result_file_path": str(results_path)
            })

            db.commit()

            logger.info(f"Dual data post-processing completed for session {session.id}. Results saved to {results_filename}")

        except Exception as e:
            logger.error(f"Error in dual data post-processing for session {session.id}: {e}")
            logger.error(traceback.format_exc())


# Global training pipeline instance
training_pipeline = UnifiedTrainingPipeline()


# Convenience functions for external use

async def start_training(
    session_id: str,
    data: pd.DataFrame,
    progress_callback: Optional[Callable] = None
) -> bool:
    """Start a training session."""
    return await training_pipeline.start_training_session(
        session_id=session_id,
        data=data,
        progress_callback=progress_callback
    )


def get_training_status(session_id: str) -> Dict[str, Any]:
    """Get training session status."""
    return training_pipeline.get_session_status(session_id)


def cancel_training(session_id: str) -> bool:
    """Cancel a training session."""
    return training_pipeline.cancel_session(session_id)


def get_active_training_sessions() -> Dict[str, Dict[str, Any]]:
    """Get all active training sessions."""
    return training_pipeline.get_active_sessions()
