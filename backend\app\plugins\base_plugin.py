"""Base plugin classes for ClassyWeb Universal Platform."""

from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass
from enum import Enum
import logging

logger = logging.getLogger(__name__)


class PluginType(Enum):
    """Types of plugins supported by ClassyWeb."""
    CLASSIFICATION_ENGINE = "classification_engine"
    WORKFLOW_COMPONENT = "workflow_component"
    DATA_PROCESSOR = "data_processor"
    UI_COMPONENT = "ui_component"
    ANALYTICS = "analytics"
    SECURITY = "security"


@dataclass
class PluginMetadata:
    """Metadata for a plugin."""
    name: str
    version: str
    description: str
    author: str
    plugin_type: PluginType
    dependencies: List[str] = None
    capabilities: List[str] = None
    supported_formats: List[str] = None
    enterprise_only: bool = False
    license: str = "MIT"
    documentation_url: str = None
    support_url: str = None


@dataclass
class PluginCapabilities:
    """Capabilities that a plugin can provide."""
    supports_hierarchical: bool = False
    supports_flat: bool = False
    supports_multi_modal: bool = False
    supports_streaming: bool = False
    supports_batch: bool = True
    max_batch_size: int = 1000
    estimated_speed_ms: int = 1000
    memory_requirements_mb: int = 512
    gpu_required: bool = False


class BasePlugin(ABC):
    """Base class for all ClassyWeb plugins."""
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
        self.metadata = self.get_metadata()
        self.capabilities = self.get_capabilities()
        self.is_initialized = False
        
    @abstractmethod
    def get_metadata(self) -> PluginMetadata:
        """Return plugin metadata."""
        pass
    
    @abstractmethod
    def get_capabilities(self) -> PluginCapabilities:
        """Return plugin capabilities."""
        pass
    
    @abstractmethod
    async def initialize(self) -> bool:
        """Initialize the plugin. Return True if successful."""
        pass
    
    @abstractmethod
    async def cleanup(self) -> bool:
        """Cleanup plugin resources. Return True if successful."""
        pass
    
    def validate_config(self, config: Dict[str, Any]) -> bool:
        """Validate plugin configuration."""
        return True
    
    def get_health_status(self) -> Dict[str, Any]:
        """Return plugin health status."""
        return {
            "status": "healthy" if self.is_initialized else "not_initialized",
            "metadata": self.metadata.__dict__,
            "capabilities": self.capabilities.__dict__
        }


class ClassificationPlugin(BasePlugin):
    """Base class for classification engine plugins."""
    
    @abstractmethod
    async def classify(
        self,
        texts: List[str],
        hierarchy_config: Optional[Dict[str, Any]] = None,
        confidence_threshold: float = 0.5
    ) -> List[Dict[str, Any]]:
        """Classify texts and return results."""
        pass
    
    @abstractmethod
    async def train(
        self,
        texts: List[str],
        labels: List[List[str]],
        hierarchy_config: Optional[Dict[str, Any]] = None,
        training_params: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Train the classification model."""
        pass
    
    async def evaluate(
        self,
        texts: List[str],
        true_labels: List[List[str]],
        hierarchy_config: Optional[Dict[str, Any]] = None
    ) -> Dict[str, float]:
        """Evaluate model performance."""
        predictions = await self.classify(texts, hierarchy_config)
        
        # Basic evaluation metrics
        total_samples = len(texts)
        correct_predictions = 0
        
        for i, pred in enumerate(predictions):
            if i < len(true_labels):
                pred_labels = set(pred.get('labels', []))
                true_label_set = set(true_labels[i])
                if pred_labels == true_label_set:
                    correct_predictions += 1
        
        accuracy = correct_predictions / total_samples if total_samples > 0 else 0.0
        
        return {
            "accuracy": accuracy,
            "total_samples": total_samples,
            "correct_predictions": correct_predictions
        }


class WorkflowPlugin(BasePlugin):
    """Base class for workflow component plugins."""
    
    @abstractmethod
    async def execute_step(
        self,
        step_config: Dict[str, Any],
        input_data: Any,
        context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Execute a workflow step."""
        pass
    
    @abstractmethod
    def get_step_schema(self) -> Dict[str, Any]:
        """Return JSON schema for step configuration."""
        pass


class DataProcessorPlugin(BasePlugin):
    """Base class for data processing plugins."""
    
    @abstractmethod
    async def process(
        self,
        data: Any,
        processing_config: Dict[str, Any]
    ) -> Any:
        """Process input data."""
        pass
    
    @abstractmethod
    def get_supported_formats(self) -> List[str]:
        """Return list of supported data formats."""
        pass
    
    @abstractmethod
    def validate_data(self, data: Any) -> bool:
        """Validate input data format."""
        pass


class AnalyticsPlugin(BasePlugin):
    """Base class for analytics and reporting plugins."""
    
    @abstractmethod
    async def generate_report(
        self,
        data: Dict[str, Any],
        report_config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Generate analytics report."""
        pass
    
    @abstractmethod
    def get_available_metrics(self) -> List[str]:
        """Return list of available metrics."""
        pass


class SecurityPlugin(BasePlugin):
    """Base class for security and compliance plugins."""
    
    @abstractmethod
    async def audit_action(
        self,
        action: str,
        user_id: int,
        resource_id: str,
        metadata: Dict[str, Any]
    ) -> bool:
        """Audit a user action."""
        pass
    
    @abstractmethod
    async def check_compliance(
        self,
        data: Any,
        compliance_rules: List[str]
    ) -> Dict[str, Any]:
        """Check data compliance."""
        pass
