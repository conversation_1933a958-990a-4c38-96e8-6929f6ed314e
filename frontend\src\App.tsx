import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { ThemeProvider } from "@/components/ThemeProvider";
import { AuthProvider } from "@/components/AuthProvider";
import { ProtectedRoute } from "@/components/ProtectedRoute";
import { UnifiedDataProvider } from "@/contexts/UnifiedDataContext";
import { UnifiedWorkflowProvider } from "@/contexts/UnifiedWorkflowContext";
import Index from "./pages/Index";
import Login from "./pages/Login";
import Register from "./pages/Register";
import ForgotPassword from "./pages/ForgotPassword";
import GetStarted from "./pages/GetStarted";
import BeginnerWorkflow from "./pages/BeginnerWorkflow";
import ExpertWorkflow from "./pages/ExpertWorkflow";
import Dashboard from "./pages/Dashboard";
import NotFound from "./pages/NotFound";
import BinaryClassificationWorkflow from "./pages/BinaryClassificationWorkflow";
import MultiClassWorkflow from "./pages/MultiClassWorkflow";
import MultiLabelWorkflow from "./pages/MultiLabelWorkflow";
import HierarchicalWorkflow from "./pages/HierarchicalWorkflow";
import FlatWorkflow from "./pages/FlatWorkflow";

const App = () => (
  <ThemeProvider defaultTheme="system" storageKey="classyweb-ui-theme">
    <TooltipProvider>
      <Toaster />
      <Sonner />
      <BrowserRouter>
        <AuthProvider>
          <UnifiedDataProvider>
            <UnifiedWorkflowProvider>
              <Routes>
            {/* Public routes */}
            <Route path="/" element={<Index />} />

            {/* Auth routes - redirect to dashboard if already authenticated */}
            <Route path="/login" element={
              <ProtectedRoute requireAuth={false}>
                <Login />
              </ProtectedRoute>
            } />
            <Route path="/register" element={
              <ProtectedRoute requireAuth={false}>
                <Register />
              </ProtectedRoute>
            } />
            <Route path="/forgot-password" element={
              <ProtectedRoute requireAuth={false}>
                <ForgotPassword />
              </ProtectedRoute>
            } />

            {/* Public workflow selection */}
            <Route path="/get-started" element={<GetStarted />} />

            {/* Protected routes */}
            <Route path="/beginner" element={
              <ProtectedRoute>
                <BeginnerWorkflow />
              </ProtectedRoute>
            } />
            <Route path="/expert" element={
              <ProtectedRoute>
                <ExpertWorkflow />
              </ProtectedRoute>
            } />
            <Route path="/dashboard" element={
              <ProtectedRoute>
                <Dashboard />
              </ProtectedRoute>
            } />

            {/* Classification Workflow Routes */}
            <Route path="/classification/binary" element={
              <ProtectedRoute>
                <BinaryClassificationWorkflow />
              </ProtectedRoute>
            } />
            <Route path="/classification/multiclass" element={
              <ProtectedRoute>
                <MultiClassWorkflow />
              </ProtectedRoute>
            } />
            <Route path="/classification/multilabel" element={
              <ProtectedRoute>
                <MultiLabelWorkflow />
              </ProtectedRoute>
            } />
            <Route path="/classification/hierarchical" element={
              <ProtectedRoute>
                <HierarchicalWorkflow />
              </ProtectedRoute>
            } />
            <Route path="/classification/flat" element={
              <ProtectedRoute>
                <FlatWorkflow />
              </ProtectedRoute>
            } />

            {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
            <Route path="*" element={<NotFound />} />
              </Routes>
            </UnifiedWorkflowProvider>
          </UnifiedDataProvider>
        </AuthProvider>
      </BrowserRouter>
    </TooltipProvider>
  </ThemeProvider>
);

export default App;
