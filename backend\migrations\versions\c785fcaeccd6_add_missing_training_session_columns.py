"""add_missing_training_session_columns

Revision ID: c785fcaeccd6
Revises: 20250422_update_hfmodel_storage
Create Date: 2025-08-15 03:16:38.203252

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'c785fcaeccd6'
down_revision = '20250422_update_hfmodel_storage'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Add missing columns to training_sessions table
    op.add_column('training_sessions', sa.Column('current_epoch', sa.Integer(), nullable=True))
    op.add_column('training_sessions', sa.Column('total_epochs', sa.Integer(), nullable=True))
    op.add_column('training_sessions', sa.Column('system_metrics', sa.JSON(), nullable=True))


def downgrade() -> None:
    # Remove the added columns
    op.drop_column('training_sessions', 'system_metrics')
    op.drop_column('training_sessions', 'total_epochs')
    op.drop_column('training_sessions', 'current_epoch')
