/**
 * errorHandling.ts
 * 
 * Comprehensive error handling and recovery system for the unified data management
 * with automatic retry, fallback mechanisms, and user-friendly error reporting.
 */

import { toast } from "@/hooks/use-toast";
import { unifiedProgressMonitor } from "./unifiedProgressMonitor";

export interface ErrorContext {
  operation: string;
  component: string;
  fileId?: string;
  fileName?: string;
  userId?: string;
  timestamp: Date;
  userAgent: string;
  url: string;
}

export interface ErrorDetails {
  code: string;
  message: string;
  originalError?: any;
  context: ErrorContext;
  severity: 'low' | 'medium' | 'high' | 'critical';
  recoverable: boolean;
  retryable: boolean;
  userMessage: string;
  technicalDetails?: string;
}

export interface RecoveryAction {
  type: 'retry' | 'fallback' | 'manual' | 'ignore';
  label: string;
  description: string;
  action: () => Promise<void> | void;
  priority: number;
}

export interface ErrorReport {
  id: string;
  error: ErrorDetails;
  recoveryActions: RecoveryAction[];
  resolved: boolean;
  resolvedAt?: Date;
  resolutionMethod?: string;
}

class ErrorHandlingService {
  private static instance: ErrorHandlingService;
  private errorReports: Map<string, ErrorReport> = new Map();
  private retryAttempts: Map<string, number> = new Map();
  private maxRetryAttempts = 3;
  private retryDelay = 1000; // Base delay in ms

  private constructor() {
    // Set up global error handlers
    this.setupGlobalErrorHandlers();
  }

  public static getInstance(): ErrorHandlingService {
    if (!ErrorHandlingService.instance) {
      ErrorHandlingService.instance = new ErrorHandlingService();
    }
    return ErrorHandlingService.instance;
  }

  /**
   * Handle an error with automatic classification and recovery suggestions
   */
  async handleError(
    error: any,
    context: Partial<ErrorContext>,
    customRecoveryActions?: RecoveryAction[]
  ): Promise<ErrorReport> {
    const errorDetails = this.classifyError(error, {
      ...context,
      timestamp: new Date(),
      userAgent: navigator.userAgent,
      url: window.location.href
    } as ErrorContext);

    const recoveryActions = customRecoveryActions || this.generateRecoveryActions(errorDetails);
    
    const errorReport: ErrorReport = {
      id: this.generateErrorId(),
      error: errorDetails,
      recoveryActions,
      resolved: false
    };

    this.errorReports.set(errorReport.id, errorReport);

    // Log error for debugging
    console.error('Error handled:', errorDetails);

    // Show user notification
    this.showUserNotification(errorDetails, recoveryActions);

    // Update progress monitor if operation ID is available
    if (context.operation) {
      unifiedProgressMonitor.failOperation(context.operation, {
        code: errorDetails.code,
        message: errorDetails.message,
        details: errorDetails.technicalDetails
      });
    }

    return errorReport;
  }

  /**
   * Attempt automatic retry with exponential backoff
   */
  async retryOperation(
    operationId: string,
    operation: () => Promise<any>,
    context: ErrorContext
  ): Promise<any> {
    const attempts = this.retryAttempts.get(operationId) || 0;
    
    if (attempts >= this.maxRetryAttempts) {
      throw new Error(`Maximum retry attempts (${this.maxRetryAttempts}) exceeded for operation ${operationId}`);
    }

    this.retryAttempts.set(operationId, attempts + 1);

    // Calculate delay with exponential backoff
    const delay = this.retryDelay * Math.pow(2, attempts);
    
    await new Promise(resolve => setTimeout(resolve, delay));

    try {
      const result = await operation();
      this.retryAttempts.delete(operationId); // Clear on success
      return result;
    } catch (error) {
      if (attempts + 1 >= this.maxRetryAttempts) {
        this.retryAttempts.delete(operationId);
        throw error;
      }
      return this.retryOperation(operationId, operation, context);
    }
  }

  /**
   * Classify error and determine appropriate handling
   */
  private classifyError(error: any, context: ErrorContext): ErrorDetails {
    let code = 'UNKNOWN_ERROR';
    let message = 'An unexpected error occurred';
    let severity: ErrorDetails['severity'] = 'medium';
    let recoverable = true;
    let retryable = false;
    let userMessage = 'Something went wrong. Please try again.';
    let technicalDetails = '';

    // Network errors
    if (error.name === 'NetworkError' || error.code === 'NETWORK_ERROR') {
      code = 'NETWORK_ERROR';
      message = 'Network connection failed';
      severity = 'high';
      retryable = true;
      userMessage = 'Connection failed. Please check your internet connection and try again.';
    }
    // File upload errors
    else if (context.operation?.includes('upload')) {
      if (error.message?.includes('size')) {
        code = 'FILE_TOO_LARGE';
        message = 'File size exceeds limit';
        severity = 'medium';
        recoverable = false;
        userMessage = 'The file is too large. Please choose a smaller file or compress it.';
      } else if (error.message?.includes('type') || error.message?.includes('format')) {
        code = 'INVALID_FILE_TYPE';
        message = 'Invalid file type';
        severity = 'medium';
        recoverable = false;
        userMessage = 'This file type is not supported. Please upload a CSV, Excel, or JSON file.';
      } else {
        code = 'UPLOAD_FAILED';
        message = 'File upload failed';
        severity = 'high';
        retryable = true;
        userMessage = 'Failed to upload file. Please try again.';
      }
    }
    // API errors
    else if (error.response) {
      const status = error.response.status;
      if (status >= 400 && status < 500) {
        code = `CLIENT_ERROR_${status}`;
        message = error.response.data?.detail || 'Client error';
        severity = status === 401 ? 'high' : 'medium';
        recoverable = status !== 401;
        userMessage = status === 401 
          ? 'Authentication failed. Please log in again.'
          : 'Invalid request. Please check your input and try again.';
      } else if (status >= 500) {
        code = `SERVER_ERROR_${status}`;
        message = 'Server error';
        severity = 'high';
        retryable = true;
        userMessage = 'Server error occurred. Please try again in a few moments.';
      }
    }
    // Validation errors
    else if (error.name === 'ValidationError') {
      code = 'VALIDATION_ERROR';
      message = error.message || 'Validation failed';
      severity = 'low';
      recoverable = true;
      userMessage = 'Please check your input and correct any errors.';
    }
    // Storage errors
    else if (error.name === 'QuotaExceededError') {
      code = 'STORAGE_QUOTA_EXCEEDED';
      message = 'Storage quota exceeded';
      severity = 'high';
      recoverable = true;
      userMessage = 'Storage is full. Please clear some files and try again.';
    }

    // Extract technical details
    if (error.stack) {
      technicalDetails = error.stack;
    } else if (error.message) {
      technicalDetails = error.message;
    }

    return {
      code,
      message,
      originalError: error,
      context,
      severity,
      recoverable,
      retryable,
      userMessage,
      technicalDetails
    };
  }

  /**
   * Generate appropriate recovery actions
   */
  private generateRecoveryActions(errorDetails: ErrorDetails): RecoveryAction[] {
    const actions: RecoveryAction[] = [];

    // Retry action for retryable errors
    if (errorDetails.retryable) {
      actions.push({
        type: 'retry',
        label: 'Try Again',
        description: 'Attempt the operation again',
        action: () => {
          // This will be overridden by the caller
          window.location.reload();
        },
        priority: 1
      });
    }

    // Fallback actions based on error type
    switch (errorDetails.code) {
      case 'FILE_TOO_LARGE':
        actions.push({
          type: 'manual',
          label: 'Choose Smaller File',
          description: 'Select a file under the size limit',
          action: () => {
            toast({
              title: "File Size Limit",
              description: "Please select a file smaller than the allowed limit.",
            });
          },
          priority: 2
        });
        break;

      case 'INVALID_FILE_TYPE':
        actions.push({
          type: 'manual',
          label: 'Convert File Format',
          description: 'Convert your file to a supported format (CSV, Excel, JSON)',
          action: () => {
            toast({
              title: "File Format Help",
              description: "Supported formats: CSV (.csv), Excel (.xlsx, .xls), JSON (.json)",
            });
          },
          priority: 2
        });
        break;

      case 'NETWORK_ERROR':
        actions.push({
          type: 'manual',
          label: 'Check Connection',
          description: 'Verify your internet connection',
          action: () => {
            toast({
              title: "Connection Check",
              description: "Please check your internet connection and try again.",
            });
          },
          priority: 2
        });
        break;

      case 'STORAGE_QUOTA_EXCEEDED':
        actions.push({
          type: 'manual',
          label: 'Clear Storage',
          description: 'Remove unused files to free up space',
          action: () => {
            // Navigate to file management
            window.location.hash = '#/files';
          },
          priority: 2
        });
        break;
    }

    // Generic fallback action
    actions.push({
      type: 'fallback',
      label: 'Refresh Page',
      description: 'Reload the page to reset the application state',
      action: () => {
        window.location.reload();
      },
      priority: 3
    });

    return actions.sort((a, b) => a.priority - b.priority);
  }

  /**
   * Show user-friendly error notification
   */
  private showUserNotification(errorDetails: ErrorDetails, recoveryActions: RecoveryAction[]) {
    const primaryAction = recoveryActions[0];
    
    toast({
      title: this.getSeverityTitle(errorDetails.severity),
      description: errorDetails.userMessage,
      variant: errorDetails.severity === 'critical' || errorDetails.severity === 'high' 
        ? 'destructive' 
        : 'default',
      action: primaryAction ? {
        altText: primaryAction.label,
        onClick: primaryAction.action
      } : undefined
    });
  }

  /**
   * Get appropriate title based on error severity
   */
  private getSeverityTitle(severity: ErrorDetails['severity']): string {
    switch (severity) {
      case 'critical': return 'Critical Error';
      case 'high': return 'Error';
      case 'medium': return 'Warning';
      case 'low': return 'Notice';
      default: return 'Error';
    }
  }

  /**
   * Set up global error handlers
   */
  private setupGlobalErrorHandlers() {
    // Handle unhandled promise rejections
    window.addEventListener('unhandledrejection', (event) => {
      this.handleError(event.reason, {
        operation: 'unhandled_promise_rejection',
        component: 'global'
      });
    });

    // Handle JavaScript errors
    window.addEventListener('error', (event) => {
      this.handleError(event.error, {
        operation: 'javascript_error',
        component: 'global'
      });
    });
  }

  /**
   * Generate unique error ID
   */
  private generateErrorId(): string {
    return `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Get all error reports
   */
  getErrorReports(): ErrorReport[] {
    return Array.from(this.errorReports.values());
  }

  /**
   * Mark error as resolved
   */
  resolveError(errorId: string, resolutionMethod: string): void {
    const report = this.errorReports.get(errorId);
    if (report) {
      report.resolved = true;
      report.resolvedAt = new Date();
      report.resolutionMethod = resolutionMethod;
    }
  }

  /**
   * Clear resolved errors
   */
  clearResolvedErrors(): void {
    for (const [id, report] of this.errorReports.entries()) {
      if (report.resolved) {
        this.errorReports.delete(id);
      }
    }
  }
}

// Export singleton instance
export const errorHandler = ErrorHandlingService.getInstance();

// Utility functions for common error scenarios
export const ErrorUtils = {
  /**
   * Wrap async operation with error handling
   */
  withErrorHandling: async <T>(
    operation: () => Promise<T>,
    context: Partial<ErrorContext>,
    customActions?: RecoveryAction[]
  ): Promise<T> => {
    try {
      return await operation();
    } catch (error) {
      await errorHandler.handleError(error, context, customActions);
      throw error; // Re-throw to allow caller to handle
    }
  },

  /**
   * Create recovery action
   */
  createRecoveryAction: (
    type: RecoveryAction['type'],
    label: string,
    description: string,
    action: RecoveryAction['action'],
    priority: number = 2
  ): RecoveryAction => ({
    type,
    label,
    description,
    action,
    priority
  })
};
