/**
 * Phase 3 Integration Tests for ClassyWeb ML Platform
 * 
 * Comprehensive tests for backend integration enhancement features:
 * - API v2 integration
 * - WebSocket real-time monitoring
 * - Enhanced workflow service
 * - Error handling and recovery
 */

import { describe, it, expect, beforeEach, afterEach, vi, Mock } from 'vitest';
import { 
  ClassificationType, 
  TrainingMethod,
  getEnginesForType,
  startTrainingV2,
  getTrainingStatus,
  performInference,
  validateTrainingConfig
} from '../services/classificationEngineService';
import { useTrainingMonitor } from '../hooks/useTrainingMonitor';
import { enhancedWorkflowService, WorkflowStatus } from '../services/enhancedWorkflowService';
import { errorHandlingService, ErrorType, ErrorSeverity } from '../services/errorHandlingService';
import { enhancedApiClient } from '../services/enhancedApiClient';

// Mock WebSocket
class MockWebSocket {
  static CONNECTING = 0;
  static OPEN = 1;
  static CLOSING = 2;
  static CLOSED = 3;

  readyState = MockWebSocket.CONNECTING;
  onopen: ((event: Event) => void) | null = null;
  onclose: ((event: CloseEvent) => void) | null = null;
  onmessage: ((event: MessageEvent) => void) | null = null;
  onerror: ((event: Event) => void) | null = null;

  constructor(public url: string) {
    setTimeout(() => {
      this.readyState = MockWebSocket.OPEN;
      this.onopen?.(new Event('open'));
    }, 100);
  }

  send(data: string) {
    // Mock sending data
  }

  close() {
    this.readyState = MockWebSocket.CLOSED;
    this.onclose?.(new CloseEvent('close'));
  }
}

// Mock API responses
const mockApiResponses = {
  engines: [
    {
      id: 'enhanced_binary',
      name: 'Enhanced Binary Engine',
      type: ClassificationType.BINARY,
      description: 'Advanced binary classification',
      capabilities: [],
      supported_methods: [TrainingMethod.CUSTOM, TrainingMethod.LLM]
    }
  ],
  trainingResponse: {
    session_id: 'test_session_123',
    task_id: 'test_task_456',
    status: 'started',
    message: 'Training started successfully',
    estimated_duration: 300,
    websocket_url: 'ws://localhost:8001/ws/training/test_session_123'
  },
  trainingStatus: {
    session_id: 'test_session_123',
    status: 'training',
    progress: {
      current_epoch: 5,
      total_epochs: 10,
      current_step: 50,
      total_steps: 100,
      progress_percentage: 50,
      stage: 'training',
      elapsed_time: 150
    },
    metrics: {
      loss: 0.25,
      accuracy: 0.85,
      validation_loss: 0.30,
      validation_accuracy: 0.82
    }
  },
  inferenceResponse: {
    predictions: [['positive'], ['negative'], ['positive']],
    confidence_scores: [[0.95], [0.88], [0.92]],
    processing_time: 0.15,
    model_info: {
      id: 'test_model_789',
      type: 'binary',
      version: '1.0.0'
    }
  }
};

// Mock enhanced API client
vi.mock('../services/enhancedApiClient', () => ({
  enhancedApiClient: {
    get: vi.fn(),
    post: vi.fn(),
    createWebSocket: vi.fn(),
    closeWebSocket: vi.fn(),
    sendWebSocketMessage: vi.fn()
  }
}));

// Mock global WebSocket
global.WebSocket = MockWebSocket as any;

describe('Phase 3 Backend Integration', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    
    // Setup default mock responses
    (enhancedApiClient.get as Mock).mockImplementation((url: string) => {
      if (url.includes('/api/v2/classification/engines')) {
        return Promise.resolve({ data: { engines: mockApiResponses.engines } });
      }
      if (url.includes('/api/v2/classification/training/')) {
        return Promise.resolve({ data: mockApiResponses.trainingStatus });
      }
      return Promise.reject(new Error('Unknown endpoint'));
    });

    (enhancedApiClient.post as Mock).mockImplementation((url: string) => {
      if (url.includes('/api/v2/classification/train')) {
        return Promise.resolve({ data: mockApiResponses.trainingResponse });
      }
      if (url.includes('/api/v2/classification/inference')) {
        return Promise.resolve({ data: mockApiResponses.inferenceResponse });
      }
      return Promise.reject(new Error('Unknown endpoint'));
    });
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Classification Engine Service', () => {
    it('should fetch engines for classification type', async () => {
      const engines = await getEnginesForType(ClassificationType.BINARY);
      
      expect(enhancedApiClient.get).toHaveBeenCalledWith(
        '/api/v2/classification/engines',
        { params: { type: ClassificationType.BINARY } }
      );
      expect(engines).toEqual(mockApiResponses.engines);
    });

    it('should start training with v2 API', async () => {
      const config = {
        file_id: 'test_file_123',
        classification_type: ClassificationType.BINARY,
        training_method: TrainingMethod.CUSTOM,
        text_column: 'text',
        label_columns: ['label']
      };

      const response = await startTrainingV2(config);
      
      expect(enhancedApiClient.post).toHaveBeenCalledWith('/api/v2/classification/train', config);
      expect(response).toEqual(mockApiResponses.trainingResponse);
    });

    it('should get training status', async () => {
      const sessionId = 'test_session_123';
      const status = await getTrainingStatus(sessionId);
      
      expect(enhancedApiClient.get).toHaveBeenCalledWith(
        `/api/v2/classification/training/${sessionId}/status`
      );
      expect(status).toEqual(mockApiResponses.trainingStatus);
    });

    it('should perform inference', async () => {
      const request = {
        texts: ['Test text 1', 'Test text 2', 'Test text 3'],
        model_id: 'test_model_789',
        confidence_threshold: 0.8
      };

      const response = await performInference(request);
      
      expect(enhancedApiClient.post).toHaveBeenCalledWith('/api/v2/classification/inference', request);
      expect(response).toEqual(mockApiResponses.inferenceResponse);
    });

    it('should handle API errors gracefully', async () => {
      const errorResponse = new Error('Network error');
      (enhancedApiClient.get as Mock).mockRejectedValueOnce(errorResponse);

      await expect(getEnginesForType(ClassificationType.BINARY)).rejects.toThrow('Failed to fetch engines for binary');
    });
  });

  describe('Enhanced Workflow Service', () => {
    it('should create workflow session', async () => {
      const session = await enhancedWorkflowService.createWorkflowSession(
        ClassificationType.BINARY,
        TrainingMethod.CUSTOM,
        'test_file_123'
      );

      expect(session.type).toBe(ClassificationType.BINARY);
      expect(session.method).toBe(TrainingMethod.CUSTOM);
      expect(session.fileId).toBe('test_file_123');
      expect(session.status).toBe(WorkflowStatus.CREATED);
      expect(session.steps.length).toBeGreaterThan(0);
    });

    it('should advance workflow steps', async () => {
      const session = await enhancedWorkflowService.createWorkflowSession(
        ClassificationType.BINARY,
        TrainingMethod.CUSTOM,
        'test_file_123'
      );

      const updatedSession = await enhancedWorkflowService.advanceToNextStep(session.id);
      
      expect(updatedSession).toBeDefined();
      expect(updatedSession!.steps[0].status).toBe('completed');
      expect(updatedSession!.steps[1].status).toBe('in_progress');
    });

    it('should update step progress', () => {
      const session = enhancedWorkflowService.createWorkflowSession(
        ClassificationType.BINARY,
        TrainingMethod.CUSTOM,
        'test_file_123'
      );

      enhancedWorkflowService.updateStepProgress(session.id, session.steps[0].id, 75);
      
      const updatedSession = enhancedWorkflowService.getWorkflowSession(session.id);
      expect(updatedSession!.steps[0].progress).toBe(75);
      expect(updatedSession!.steps[0].status).toBe('in_progress');
    });

    it('should calculate workflow progress', async () => {
      const session = await enhancedWorkflowService.createWorkflowSession(
        ClassificationType.BINARY,
        TrainingMethod.CUSTOM,
        'test_file_123'
      );

      // Complete first step
      enhancedWorkflowService.updateStepProgress(session.id, session.steps[0].id, 100);
      
      // Progress second step to 50%
      enhancedWorkflowService.updateStepProgress(session.id, session.steps[1].id, 50);

      const progress = enhancedWorkflowService.getWorkflowProgress(session.id);
      const expectedProgress = ((1 + 0.5) / session.steps.length) * 100;
      
      expect(progress).toBeCloseTo(expectedProgress, 1);
    });

    it('should handle workflow cancellation', async () => {
      const session = await enhancedWorkflowService.createWorkflowSession(
        ClassificationType.BINARY,
        TrainingMethod.CUSTOM,
        'test_file_123'
      );

      enhancedWorkflowService.cancelWorkflow(session.id);
      
      const cancelledSession = enhancedWorkflowService.getWorkflowSession(session.id);
      expect(cancelledSession!.status).toBe(WorkflowStatus.CANCELLED);
      expect(cancelledSession!.endTime).toBeDefined();
    });
  });

  describe('Error Handling Service', () => {
    it('should create error info correctly', () => {
      const error = errorHandlingService.createErrorInfo({
        type: ErrorType.API_ERROR,
        severity: ErrorSeverity.MEDIUM,
        message: 'Test error message',
        details: { status: 500 },
        retryable: true
      });

      expect(error.id).toBeDefined();
      expect(error.type).toBe(ErrorType.API_ERROR);
      expect(error.severity).toBe(ErrorSeverity.MEDIUM);
      expect(error.message).toBe('Test error message');
      expect(error.retryable).toBe(true);
      expect(error.retryCount).toBe(0);
      expect(error.timestamp).toBeInstanceOf(Date);
    });

    it('should handle API errors with proper classification', () => {
      const apiError = {
        response: { status: 401, statusText: 'Unauthorized' },
        message: 'Authentication failed'
      };

      const errorInfo = errorHandlingService.handleApiError(apiError, {
        apiEndpoint: '/api/v2/classification/train'
      });

      expect(errorInfo.type).toBe(ErrorType.AUTHENTICATION_ERROR);
      expect(errorInfo.severity).toBe(ErrorSeverity.HIGH);
      expect(errorInfo.context?.apiEndpoint).toBe('/api/v2/classification/train');
    });

    it('should handle WebSocket errors', () => {
      const wsError = { code: 1006, reason: 'Connection lost' };
      
      const errorInfo = errorHandlingService.handleWebSocketError(wsError, {
        sessionId: 'test_session_123'
      });

      expect(errorInfo.type).toBe(ErrorType.WEBSOCKET_ERROR);
      expect(errorInfo.context?.sessionId).toBe('test_session_123');
      expect(errorInfo.retryable).toBe(true);
    });

    it('should add and notify error listeners', () => {
      const listener = vi.fn();
      errorHandlingService.addErrorListener(listener);

      const error = errorHandlingService.createErrorInfo({
        type: ErrorType.NETWORK_ERROR,
        severity: ErrorSeverity.HIGH,
        message: 'Network connection lost'
      });

      errorHandlingService.handleError(error);

      expect(listener).toHaveBeenCalledWith(error);
    });

    it('should clear errors', () => {
      const error = errorHandlingService.createErrorInfo({
        type: ErrorType.CLIENT_ERROR,
        severity: ErrorSeverity.LOW,
        message: 'Test error'
      });

      errorHandlingService.handleError(error);
      expect(errorHandlingService.getAllErrors().length).toBe(1);

      errorHandlingService.clearErrors();
      expect(errorHandlingService.getAllErrors().length).toBe(0);
    });
  });

  describe('WebSocket Integration', () => {
    it('should create WebSocket connection with authentication', () => {
      const mockWs = new MockWebSocket('ws://test');
      (enhancedApiClient.createWebSocket as Mock).mockReturnValue(mockWs);

      const ws = enhancedApiClient.createWebSocket('/ws/training/test_session');
      
      expect(enhancedApiClient.createWebSocket).toHaveBeenCalledWith('/ws/training/test_session');
      expect(ws).toBeInstanceOf(MockWebSocket);
    });

    it('should handle WebSocket message sending', () => {
      (enhancedApiClient.sendWebSocketMessage as Mock).mockReturnValue(true);

      const result = enhancedApiClient.sendWebSocketMessage('/ws/training/test', { type: 'ping' });
      
      expect(enhancedApiClient.sendWebSocketMessage).toHaveBeenCalledWith('/ws/training/test', { type: 'ping' });
      expect(result).toBe(true);
    });

    it('should handle WebSocket connection cleanup', () => {
      enhancedApiClient.closeWebSocket('/ws/training/test');
      
      expect(enhancedApiClient.closeWebSocket).toHaveBeenCalledWith('/ws/training/test');
    });
  });

  describe('End-to-End Integration', () => {
    it('should complete full training workflow with monitoring', async () => {
      // Create workflow session
      const session = await enhancedWorkflowService.createWorkflowSession(
        ClassificationType.BINARY,
        TrainingMethod.CUSTOM,
        'test_file_123'
      );

      // Configure training
      session.config.text_column = 'text';
      session.config.label_columns = ['label'];

      // Start training
      const trainingSessionId = await enhancedWorkflowService.startTraining(session.id);
      
      expect(trainingSessionId).toBe('test_session_123');
      expect(enhancedApiClient.post).toHaveBeenCalledWith('/api/v2/classification/train', session.config);

      // Verify workflow status updated
      const updatedSession = enhancedWorkflowService.getWorkflowSession(session.id);
      expect(updatedSession!.status).toBe(WorkflowStatus.RUNNING);
    });

    it('should handle training failure with error recovery', async () => {
      // Mock training failure
      const trainingError = new Error('Training failed');
      (enhancedApiClient.post as Mock).mockRejectedValueOnce(trainingError);

      const session = await enhancedWorkflowService.createWorkflowSession(
        ClassificationType.BINARY,
        TrainingMethod.CUSTOM,
        'test_file_123'
      );

      session.config.text_column = 'text';
      session.config.label_columns = ['label'];

      // Attempt training
      await expect(enhancedWorkflowService.startTraining(session.id)).rejects.toThrow('Training failed');

      // Verify error was handled
      const errors = errorHandlingService.getAllErrors();
      expect(errors.length).toBeGreaterThan(0);
    });
  });
});

describe('Training Monitor Hook Integration', () => {
  it('should connect and receive training updates', async () => {
    // This would require a more complex setup with React Testing Library
    // for hook testing, but the basic structure is here

    const sessionId = 'test_session_123';

    // Mock WebSocket connection
    const mockWs = new MockWebSocket(`ws://localhost/ws/training/${sessionId}`);
    (enhancedApiClient.createWebSocket as Mock).mockReturnValue(mockWs);

    // The actual hook testing would require renderHook from @testing-library/react-hooks
    // and proper React context setup

    expect(mockWs.url).toContain(sessionId);
  });

  it('should handle WebSocket reconnection on connection loss', async () => {
    const sessionId = 'test_session_123';
    const mockWs = new MockWebSocket(`ws://localhost/ws/training/${sessionId}`);

    // Simulate connection loss
    setTimeout(() => {
      mockWs.readyState = MockWebSocket.CLOSED;
      mockWs.onclose?.(new CloseEvent('close', { code: 1006 }));
    }, 200);

    // Verify reconnection attempt would be made
    expect(mockWs).toBeDefined();
  });

  it('should parse and handle training progress messages', () => {
    const progressMessage = {
      type: 'progress_update',
      data: {
        session_id: 'test_session_123',
        current_epoch: 3,
        total_epochs: 10,
        progress_percentage: 30,
        stage: 'training'
      },
      timestamp: new Date().toISOString()
    };

    // This would test the message parsing logic in the hook
    expect(progressMessage.data.progress_percentage).toBe(30);
    expect(progressMessage.data.stage).toBe('training');
  });
});

describe('Performance and Reliability', () => {
  it('should handle high-frequency WebSocket messages', async () => {
    const messages = Array.from({ length: 100 }, (_, i) => ({
      type: 'metrics_update',
      data: { loss: 1 - (i * 0.01), accuracy: i * 0.01 },
      timestamp: new Date().toISOString()
    }));

    // Simulate rapid message processing
    messages.forEach(message => {
      expect(message.data.loss).toBeGreaterThanOrEqual(0);
      expect(message.data.accuracy).toBeLessThanOrEqual(1);
    });
  });

  it('should handle concurrent API requests', async () => {
    const requests = Array.from({ length: 10 }, (_, i) =>
      getEnginesForType(ClassificationType.BINARY)
    );

    const results = await Promise.all(requests);

    expect(results).toHaveLength(10);
    results.forEach(result => {
      expect(result).toEqual(mockApiResponses.engines);
    });
  });

  it('should maintain session state during network interruptions', () => {
    const session = enhancedWorkflowService.createWorkflowSession(
      ClassificationType.BINARY,
      TrainingMethod.CUSTOM,
      'test_file_123'
    );

    // Simulate network interruption
    const originalSession = { ...session };

    // Session should be recoverable
    const recoveredSession = enhancedWorkflowService.getWorkflowSession(session.id);
    expect(recoveredSession).toEqual(originalSession);
  });
});
