/**
 * Test file for hierarchy constraint detection functionality
 */

import { describe, it, expect, vi } from 'vitest';

// Mock data for testing hierarchy constraint detection
const mockHierarchicalData = [
  { category: 'Electronics', subcategory: 'Smartphones', product: 'iPhone 14', text: 'Latest iPhone model' },
  { category: 'Electronics', subcategory: 'Smartphones', product: 'Samsung Galaxy', text: 'Android smartphone' },
  { category: 'Electronics', subcategory: 'Laptops', product: 'MacBook Pro', text: 'Professional laptop' },
  { category: 'Electronics', subcategory: 'Laptops', product: 'Dell XPS', text: 'Windows laptop' },
  { category: 'Clothing', subcategory: 'Shirts', product: 'T-Shirt', text: 'Cotton t-shirt' },
  { category: 'Clothing', subcategory: 'Shirts', product: 'Dress Shirt', text: 'Formal shirt' },
  { category: 'Clothing', subcategory: 'Pants', product: 'Jeans', text: 'Denim jeans' },
];

const mockHierarchyLevels = [
  { name: 'Category', column: 'category', order: 0 },
  { name: 'Subcategory', column: 'subcategory', order: 1 },
  { name: 'Product', column: 'product', order: 2 }
];

describe('Hierarchy Constraint Detection', () => {
  it('should detect parent-child relationships from hierarchical data', () => {
    // Simulate the constraint extraction logic from HierarchyTreeBuilder
    const constraints: Record<string, string[]> = {};
    
    // Build hierarchy structure
    const nodeMap = new Map();
    const rootNodes: any[] = [];
    
    // Process data to build hierarchy
    mockHierarchicalData.forEach(row => {
      let currentLevel = rootNodes;
      let parentId = '';
      
      mockHierarchyLevels.forEach((level, levelIndex) => {
        const value = row[level.column];
        const nodeId = parentId ? `${parentId}-${value}` : value;
        
        let node = nodeMap.get(nodeId);
        if (!node) {
          node = {
            id: nodeId,
            name: value,
            level: levelIndex,
            parentId: parentId || undefined,
            children: []
          };
          nodeMap.set(nodeId, node);
          currentLevel.push(node);
        }
        
        currentLevel = node.children;
        parentId = nodeId;
      });
    });
    
    // Extract constraints
    const extractConstraints = (nodes: any[]) => {
      nodes.forEach(node => {
        if (node.children.length > 0) {
          constraints[node.name] = node.children.map((child: any) => child.name);
          extractConstraints(node.children);
        }
      });
    };
    
    extractConstraints(rootNodes);
    
    // Verify constraints
    expect(constraints).toHaveProperty('Electronics');
    expect(constraints['Electronics']).toContain('Smartphones');
    expect(constraints['Electronics']).toContain('Laptops');
    
    expect(constraints).toHaveProperty('Clothing');
    expect(constraints['Clothing']).toContain('Shirts');
    expect(constraints['Clothing']).toContain('Pants');
    
    expect(constraints).toHaveProperty('Smartphones');
    expect(constraints['Smartphones']).toContain('iPhone 14');
    expect(constraints['Smartphones']).toContain('Samsung Galaxy');
    
    expect(constraints).toHaveProperty('Laptops');
    expect(constraints['Laptops']).toContain('MacBook Pro');
    expect(constraints['Laptops']).toContain('Dell XPS');
  });

  it('should generate constraint rules with proper level mapping', () => {
    const detectedConstraints = {
      'Electronics': ['Smartphones', 'Laptops'],
      'Clothing': ['Shirts', 'Pants'],
      'Smartphones': ['iPhone 14', 'Samsung Galaxy'],
      'Laptops': ['MacBook Pro', 'Dell XPS']
    };
    
    const constraintInfo = [
      { parentValue: 'Electronics', parentLevel: 0, childLevel: 1, allowedChildren: ['Smartphones', 'Laptops'] },
      { parentValue: 'Clothing', parentLevel: 0, childLevel: 1, allowedChildren: ['Shirts', 'Pants'] },
      { parentValue: 'Smartphones', parentLevel: 1, childLevel: 2, allowedChildren: ['iPhone 14', 'Samsung Galaxy'] },
      { parentValue: 'Laptops', parentLevel: 1, childLevel: 2, allowedChildren: ['MacBook Pro', 'Dell XPS'] }
    ];
    
    // Simulate constraint rule generation
    const rules = constraintInfo.map((info, index) => ({
      id: `auto_${info.parentValue}_${index}`,
      parentLevel: info.parentLevel,
      parentValue: info.parentValue,
      childLevel: info.childLevel,
      allowedChildren: info.allowedChildren,
      isStrict: false,
      description: `Auto-detected: ${info.parentValue} (Level ${info.parentLevel + 1} → Level ${info.childLevel + 1})`
    }));
    
    // Verify rule generation
    expect(rules).toHaveLength(4);
    
    const electronicsRule = rules.find(r => r.parentValue === 'Electronics');
    expect(electronicsRule).toBeDefined();
    expect(electronicsRule?.parentLevel).toBe(0);
    expect(electronicsRule?.childLevel).toBe(1);
    expect(electronicsRule?.allowedChildren).toEqual(['Smartphones', 'Laptops']);
    
    const smartphonesRule = rules.find(r => r.parentValue === 'Smartphones');
    expect(smartphonesRule).toBeDefined();
    expect(smartphonesRule?.parentLevel).toBe(1);
    expect(smartphonesRule?.childLevel).toBe(2);
    expect(smartphonesRule?.allowedChildren).toEqual(['iPhone 14', 'Samsung Galaxy']);
  });

  it('should handle empty or invalid data gracefully', () => {
    const emptyConstraints = {};
    const emptyConstraintInfo: any[] = [];
    
    // Simulate constraint generation with empty data
    const rules = emptyConstraintInfo.map((info, index) => ({
      id: `auto_${info.parentValue}_${index}`,
      parentLevel: info.parentLevel,
      parentValue: info.parentValue,
      childLevel: info.childLevel,
      allowedChildren: info.allowedChildren,
      isStrict: false,
      description: `Auto-detected: ${info.parentValue}`
    }));
    
    expect(rules).toHaveLength(0);
    expect(Object.keys(emptyConstraints)).toHaveLength(0);
  });

  it('should validate constraint consistency', () => {
    const constraints = {
      'Electronics': ['Smartphones', 'Laptops'],
      'Smartphones': ['iPhone 14', 'Samsung Galaxy']
    };
    
    // Simulate constraint validation
    const errors: string[] = [];
    
    Object.entries(constraints).forEach(([parent, children]) => {
      if (!parent.trim()) {
        errors.push('Parent value is required');
      }
      
      if (children.length === 0) {
        errors.push('At least one allowed child is required');
      }
      
      // Check for circular dependencies (simplified)
      children.forEach(child => {
        if (child === parent) {
          errors.push('Circular dependency detected');
        }
      });
    });
    
    expect(errors).toHaveLength(0);
  });

  it('should validate comprehensive hierarchy rules', () => {
    // Import the validation service
    const { HierarchyValidationService } = require('../services/hierarchyValidationService');

    const testData = [
      { category: 'Electronics', subcategory: 'Smartphones', product: 'iPhone 14' },
      { category: 'Electronics', subcategory: 'Smartphones', product: 'Samsung Galaxy' },
      { category: 'Electronics', subcategory: '', product: 'Orphaned Product' }, // Missing parent
      { category: 'Electronics', subcategory: 'Laptops', product: 'MacBook Pro' },
      { category: 'Electronics', subcategory: 'Laptops', product: 'MacBook Pro' }, // Duplicate
    ];

    const hierarchyLevels = [
      { name: 'Category', column: 'category', order: 0 },
      { name: 'Subcategory', column: 'subcategory', order: 1 },
      { name: 'Product', column: 'product', order: 2 }
    ];

    const validationRules = [
      { id: 'parent_child_consistency', name: 'Parent-Child Consistency', enabled: true, severity: 'error', description: 'Test' },
      { id: 'duplicate_paths', name: 'Duplicate Paths', enabled: true, severity: 'error', description: 'Test' },
      { id: 'level_completeness', name: 'Level Completeness', enabled: true, severity: 'warning', description: 'Test' }
    ];

    const results = HierarchyValidationService.validateHierarchyData(testData, hierarchyLevels, validationRules);

    // Should find violations
    expect(results.overall_valid).toBe(false);
    expect(results.critical_violations).toBeGreaterThan(0);

    // Should detect parent-child consistency issues
    expect(results.rule_results['parent_child_consistency']).toBeDefined();
    expect(results.rule_results['parent_child_consistency'].violations.length).toBeGreaterThan(0);

    // Should detect duplicate paths
    expect(results.rule_results['duplicate_paths']).toBeDefined();
    expect(results.rule_results['duplicate_paths'].violations.length).toBeGreaterThan(0);
  });
});
