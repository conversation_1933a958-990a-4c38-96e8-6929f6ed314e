"""Enhanced Multi-class Classification Engine for ClassyWeb ML Platform Phase 2.

This module implements production-ready multi-class classification with advanced features:
- Strategy selection (softmax vs one-vs-rest vs one-vs-one)
- Advanced metrics with per-class analysis
- Confusion matrix analysis and visualization
- Class imbalance handling with multiple strategies
- Performance benchmarking and optimization
"""

import logging
import time
import pandas as pd
import numpy as np
from typing import List, Dict, Any, Optional, Tuple, Union
from sklearn.metrics import (
    accuracy_score, precision_score, recall_score, f1_score,
    classification_report, confusion_matrix, balanced_accuracy_score,
    top_k_accuracy_score
)
from sklearn.multiclass import OneVsRestClassifier, OneVsOneClassifier
from sklearn.preprocessing import LabelEncoder
import torch
from transformers import AutoTokenizer, AutoModelForSequenceClassification, TrainingArguments, Trainer
import seaborn as sns
import matplotlib.pyplot as plt
from io import BytesIO
import base64

from .base_engine import (
    BaseClassificationEngine, 
    ClassificationType, 
    TrainingMethod,
    ClassificationResult,
    TrainingConfig,
    TrainingResult
)
from .enhanced_binary_engine import GPUMonitor, ClassImbalanceHandler

logger = logging.getLogger(__name__)


class MultiClassStrategy:
    """Multi-class classification strategy handler."""
    
    def __init__(self):
        self.strategies = {
            'softmax': 'Native multi-class with softmax activation',
            'one_vs_rest': 'One-vs-Rest (OvR) binary classification approach',
            'one_vs_one': 'One-vs-One (OvO) pairwise classification approach'
        }
    
    def get_strategy_info(self, strategy: str) -> Dict[str, Any]:
        """Get information about a classification strategy."""
        if strategy not in self.strategies:
            raise ValueError(f"Unknown strategy: {strategy}")
        
        info = {
            'name': strategy,
            'description': self.strategies[strategy],
            'suitable_for': [],
            'advantages': [],
            'disadvantages': []
        }
        
        if strategy == 'softmax':
            info.update({
                'suitable_for': ['Balanced datasets', 'Large number of classes', 'Probability calibration needed'],
                'advantages': ['Native support', 'Probability outputs', 'Efficient training'],
                'disadvantages': ['Assumes class exclusivity', 'Can struggle with imbalanced data']
            })
        elif strategy == 'one_vs_rest':
            info.update({
                'suitable_for': ['Imbalanced datasets', 'Independent class decisions'],
                'advantages': ['Handles imbalance well', 'Interpretable', 'Parallelizable'],
                'disadvantages': ['More models to train', 'Calibration issues']
            })
        elif strategy == 'one_vs_one':
            info.update({
                'suitable_for': ['Small to medium datasets', 'Complex decision boundaries'],
                'advantages': ['Robust to outliers', 'Good for SVM-like models'],
                'disadvantages': ['Quadratic number of models', 'Voting complexity']
            })
        
        return info
    
    def recommend_strategy(self, num_classes: int, class_distribution: Dict, dataset_size: int) -> str:
        """Recommend the best strategy based on data characteristics."""
        # Calculate imbalance ratio
        counts = list(class_distribution.values())
        imbalance_ratio = max(counts) / min(counts) if min(counts) > 0 else float('inf')
        
        # Decision logic
        if num_classes <= 3 and imbalance_ratio > 5:
            return 'one_vs_rest'  # Good for imbalanced binary-like problems
        elif num_classes > 10 and dataset_size > 10000:
            return 'softmax'  # Efficient for large-scale problems
        elif num_classes <= 5 and dataset_size < 5000:
            return 'one_vs_one'  # Good for small, complex problems
        else:
            return 'softmax'  # Default choice


class ConfusionMatrixAnalyzer:
    """Advanced confusion matrix analysis and visualization."""
    
    def __init__(self):
        self.matrix = None
        self.class_names = None
    
    def compute_confusion_matrix(
        self, 
        y_true: np.ndarray, 
        y_pred: np.ndarray, 
        class_names: List[str]
    ) -> Dict[str, Any]:
        """Compute and analyze confusion matrix."""
        self.matrix = confusion_matrix(y_true, y_pred)
        self.class_names = class_names
        
        # Normalize matrices
        matrix_normalized = self.matrix.astype('float') / self.matrix.sum(axis=1)[:, np.newaxis]
        
        # Per-class analysis
        per_class_metrics = {}
        for i, class_name in enumerate(class_names):
            tp = self.matrix[i, i]
            fp = self.matrix[:, i].sum() - tp
            fn = self.matrix[i, :].sum() - tp
            tn = self.matrix.sum() - tp - fp - fn
            
            precision = tp / (tp + fp) if (tp + fp) > 0 else 0
            recall = tp / (tp + fn) if (tp + fn) > 0 else 0
            f1 = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0
            
            per_class_metrics[class_name] = {
                'precision': float(precision),
                'recall': float(recall),
                'f1': float(f1),
                'support': int(self.matrix[i, :].sum()),
                'true_positives': int(tp),
                'false_positives': int(fp),
                'false_negatives': int(fn),
                'true_negatives': int(tn)
            }
        
        # Overall metrics
        accuracy = np.trace(self.matrix) / np.sum(self.matrix)
        
        return {
            'confusion_matrix': self.matrix.tolist(),
            'confusion_matrix_normalized': matrix_normalized.tolist(),
            'per_class_metrics': per_class_metrics,
            'overall_accuracy': float(accuracy),
            'class_names': class_names
        }
    
    def generate_visualization(self, title: str = "Confusion Matrix") -> str:
        """Generate confusion matrix visualization as base64 encoded image."""
        if self.matrix is None:
            return ""
        
        try:
            fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
            
            # Raw confusion matrix
            sns.heatmap(self.matrix, annot=True, fmt='d', cmap='Blues', 
                       xticklabels=self.class_names, yticklabels=self.class_names, ax=ax1)
            ax1.set_title('Confusion Matrix (Raw Counts)')
            ax1.set_xlabel('Predicted')
            ax1.set_ylabel('Actual')
            
            # Normalized confusion matrix
            matrix_normalized = self.matrix.astype('float') / self.matrix.sum(axis=1)[:, np.newaxis]
            sns.heatmap(matrix_normalized, annot=True, fmt='.2f', cmap='Blues',
                       xticklabels=self.class_names, yticklabels=self.class_names, ax=ax2)
            ax2.set_title('Confusion Matrix (Normalized)')
            ax2.set_xlabel('Predicted')
            ax2.set_ylabel('Actual')
            
            plt.tight_layout()
            
            # Convert to base64
            buffer = BytesIO()
            plt.savefig(buffer, format='png', dpi=150, bbox_inches='tight')
            buffer.seek(0)
            image_base64 = base64.b64encode(buffer.getvalue()).decode()
            plt.close()
            
            return image_base64
            
        except Exception as e:
            logger.warning(f"Failed to generate confusion matrix visualization: {e}")
            return ""


class EnhancedMultiClassEngine(BaseClassificationEngine):
    """Enhanced multi-class classification engine with production-ready features."""
    
    def __init__(self, classification_type: ClassificationType, **kwargs):
        """Initialize enhanced multi-class classification engine."""
        super().__init__(classification_type)
        self.strategy_handler = MultiClassStrategy()
        self.confusion_analyzer = ConfusionMatrixAnalyzer()
        self.imbalance_handler = ClassImbalanceHandler()
        self.gpu_monitor = GPUMonitor()
        
        # Enhanced attributes
        self.classification_strategy = 'softmax'
        self.num_classes = 0
        self.class_names = []
        self.class_weights = None
        self.label_encoder = LabelEncoder()
        
    @property
    def supported_training_methods(self) -> List[TrainingMethod]:
        """Return supported training methods."""
        return [TrainingMethod.CUSTOM, TrainingMethod.LLM]
    
    @property
    def default_metrics(self) -> List[str]:
        """Return enhanced default metrics for multi-class classification."""
        return [
            'accuracy', 'macro_f1', 'weighted_f1', 'balanced_accuracy',
            'top_2_accuracy', 'top_3_accuracy', 'per_class_f1'
        ]
    
    def validate_config(self, config: TrainingConfig) -> Tuple[bool, List[str]]:
        """Validate configuration for enhanced multi-class classification."""
        errors = []
        
        # Check classification type
        if config.classification_type != ClassificationType.MULTICLASS:
            errors.append(f"Expected multiclass classification, got {config.classification_type}")
        
        # Check text columns
        if not config.text_columns:
            errors.append("At least one text column must be specified")
        
        # Check label columns
        if len(config.label_columns) != 1:
            errors.append("Multi-class classification requires exactly one label column")
        
        # Validate classification strategy
        strategy = config.metadata.get('classification_strategy', 'softmax')
        if strategy not in self.strategy_handler.strategies:
            errors.append(f"Invalid classification strategy: {strategy}")
        
        # Check training method specific requirements
        if config.training_method == TrainingMethod.LLM:
            if not config.llm_provider:
                errors.append("LLM provider must be specified for LLM training method")
            if not config.prompt_template:
                errors.append("Prompt template must be specified for LLM training method")
        
        return len(errors) == 0, errors
    
    def analyze_data_characteristics(self, data: pd.DataFrame, config: TrainingConfig) -> Dict[str, Any]:
        """Analyze data characteristics for multi-class classification."""
        text_col = config.text_columns[0]
        label_col = config.label_columns[0]
        
        # Basic data analysis
        total_samples = len(data)
        missing_text = data[text_col].isnull().sum()
        missing_labels = data[label_col].isnull().sum()
        
        # Text analysis
        text_lengths = data[text_col].astype(str).str.len()
        
        # Label analysis
        labels = data[label_col].dropna()
        unique_labels = labels.unique()
        self.num_classes = len(unique_labels)
        self.class_names = sorted(unique_labels.tolist())
        
        if self.num_classes < 3:
            logger.warning(f"Multi-class classification typically requires 3+ classes, found {self.num_classes}")
        
        # Class distribution analysis
        class_counts = labels.value_counts().to_dict()
        imbalance_analysis = self.imbalance_handler.analyze_imbalance(labels.values)
        
        # Strategy recommendation
        recommended_strategy = self.strategy_handler.recommend_strategy(
            self.num_classes, class_counts, total_samples
        )
        
        return {
            'total_samples': total_samples,
            'missing_text': int(missing_text),
            'missing_labels': int(missing_labels),
            'num_classes': self.num_classes,
            'class_names': self.class_names,
            'text_stats': {
                'avg_length': float(text_lengths.mean()),
                'max_length': int(text_lengths.max()),
                'min_length': int(text_lengths.min()),
                'std_length': float(text_lengths.std())
            },
            'class_distribution': class_counts,
            'imbalance_analysis': imbalance_analysis,
            'recommended_strategy': recommended_strategy,
            'strategy_info': self.strategy_handler.get_strategy_info(recommended_strategy)
        }

    def compute_enhanced_metrics(
        self,
        y_true: np.ndarray,
        y_pred: np.ndarray,
        y_probs: Optional[np.ndarray] = None
    ) -> Dict[str, Any]:
        """Compute comprehensive multi-class classification metrics."""
        # Basic metrics
        metrics = {
            'accuracy': float(accuracy_score(y_true, y_pred)),
            'macro_f1': float(f1_score(y_true, y_pred, average='macro')),
            'weighted_f1': float(f1_score(y_true, y_pred, average='weighted')),
            'micro_f1': float(f1_score(y_true, y_pred, average='micro')),
            'balanced_accuracy': float(balanced_accuracy_score(y_true, y_pred)),
            'macro_precision': float(precision_score(y_true, y_pred, average='macro')),
            'macro_recall': float(recall_score(y_true, y_pred, average='macro')),
            'weighted_precision': float(precision_score(y_true, y_pred, average='weighted')),
            'weighted_recall': float(recall_score(y_true, y_pred, average='weighted'))
        }

        # Top-k accuracy if probabilities are available
        if y_probs is not None and y_probs.shape[1] >= 2:
            try:
                metrics['top_2_accuracy'] = float(top_k_accuracy_score(y_true, y_probs, k=2))
                if y_probs.shape[1] >= 3:
                    metrics['top_3_accuracy'] = float(top_k_accuracy_score(y_true, y_probs, k=3))
                if y_probs.shape[1] >= 5:
                    metrics['top_5_accuracy'] = float(top_k_accuracy_score(y_true, y_probs, k=5))
            except Exception as e:
                logger.warning(f"Failed to compute top-k accuracy: {e}")

        # Per-class metrics
        class_report = classification_report(y_true, y_pred, target_names=self.class_names, output_dict=True)
        metrics['per_class_metrics'] = class_report

        # Confusion matrix analysis
        confusion_analysis = self.confusion_analyzer.compute_confusion_matrix(
            y_true, y_pred, self.class_names
        )
        metrics['confusion_matrix_analysis'] = confusion_analysis

        # Generate confusion matrix visualization
        confusion_viz = self.confusion_analyzer.generate_visualization(
            f"Multi-class Confusion Matrix ({self.num_classes} classes)"
        )
        if confusion_viz:
            metrics['confusion_matrix_visualization'] = confusion_viz

        return metrics

    async def train_custom_model(
        self,
        data: pd.DataFrame,
        config: TrainingConfig,
        progress_callback: Optional[callable] = None
    ) -> TrainingResult:
        """Train enhanced multi-class classification model with advanced features."""
        start_time = time.time()

        try:
            # Validate configuration
            is_valid, errors = self.validate_config(config)
            if not is_valid:
                return TrainingResult(
                    model_id="",
                    training_time=0,
                    final_metrics={},
                    training_history=[],
                    error=f"Configuration validation failed: {'; '.join(errors)}"
                )

            if progress_callback:
                progress_callback({"stage": "data_analysis", "progress": 0.05})

            # Analyze data characteristics
            data_characteristics = self.analyze_data_characteristics(data, config)

            # Set classification strategy
            self.classification_strategy = config.metadata.get(
                'classification_strategy',
                data_characteristics['recommended_strategy']
            )

            # Prepare data
            text_col = config.text_columns[0]
            label_col = config.label_columns[0]

            texts = data[text_col].astype(str).tolist()
            labels = data[label_col].tolist()

            # Encode labels
            encoded_labels = self.label_encoder.fit_transform(labels)
            self.num_classes = len(self.label_encoder.classes_)
            self.class_names = self.label_encoder.classes_.tolist()

            if progress_callback:
                progress_callback({"stage": "imbalance_handling", "progress": 0.1})

            # Handle class imbalance
            imbalance_strategy = config.metadata.get('imbalance_strategy', 'class_weights')
            if imbalance_strategy == 'class_weights':
                self.class_weights = self.imbalance_handler.compute_class_weights(encoded_labels)

            if progress_callback:
                progress_callback({"stage": "model_setup", "progress": 0.15})

            # Initialize tokenizer and model
            tokenizer = AutoTokenizer.from_pretrained(config.base_model)
            model = AutoModelForSequenceClassification.from_pretrained(
                config.base_model,
                num_labels=self.num_classes
            )

            # Start GPU monitoring
            self.gpu_monitor.start_monitoring()

            if progress_callback:
                progress_callback({"stage": "tokenization", "progress": 0.2})

            # Tokenize data
            encodings = tokenizer(
                texts,
                truncation=True,
                padding=True,
                max_length=config.max_length,
                return_tensors="pt"
            )

            # Create dataset
            class EnhancedMultiClassDataset(torch.utils.data.Dataset):
                def __init__(self, encodings, labels):
                    self.encodings = encodings
                    self.labels = labels

                def __getitem__(self, idx):
                    item = {key: torch.tensor(val[idx]) for key, val in self.encodings.items()}
                    item['labels'] = torch.tensor(self.labels[idx], dtype=torch.long)
                    return item

                def __len__(self):
                    return len(self.labels)

            # Split data with stratification
            from sklearn.model_selection import train_test_split
            train_texts, val_texts, train_labels, val_labels = train_test_split(
                texts, encoded_labels, test_size=config.validation_split,
                random_state=42, stratify=encoded_labels
            )

            train_encodings = tokenizer(train_texts, truncation=True, padding=True, max_length=config.max_length, return_tensors="pt")
            val_encodings = tokenizer(val_texts, truncation=True, padding=True, max_length=config.max_length, return_tensors="pt")

            train_dataset = EnhancedMultiClassDataset(train_encodings, train_labels)
            val_dataset = EnhancedMultiClassDataset(val_encodings, val_labels)

            if progress_callback:
                progress_callback({"stage": "training_setup", "progress": 0.25})

            # Enhanced training arguments
            training_args = TrainingArguments(
                output_dir='./results',
                num_train_epochs=config.num_epochs,
                per_device_train_batch_size=config.batch_size,
                per_device_eval_batch_size=config.batch_size,
                warmup_steps=config.warmup_steps,
                weight_decay=config.weight_decay,
                logging_dir='./logs',
                logging_steps=10,
                evaluation_strategy=config.evaluation_strategy,
                save_strategy=config.save_strategy,
                fp16=config.fp16,
                gradient_accumulation_steps=config.gradient_accumulation_steps,
                gradient_checkpointing=config.gradient_checkpointing,
                learning_rate=config.learning_rate,
                load_best_model_at_end=True,
                metric_for_best_model='eval_macro_f1',
                greater_is_better=True,
                save_total_limit=2,
                dataloader_num_workers=2 if config.use_unsloth else 0,
            )

            # Custom compute metrics function
            def compute_metrics(eval_pred):
                predictions, labels = eval_pred
                predictions = np.argmax(predictions, axis=1)

                # Record GPU metrics during evaluation
                self.gpu_monitor.record_metrics()

                return {
                    'accuracy': accuracy_score(labels, predictions),
                    'macro_f1': f1_score(labels, predictions, average='macro'),
                    'weighted_f1': f1_score(labels, predictions, average='weighted'),
                    'macro_precision': precision_score(labels, predictions, average='macro'),
                    'macro_recall': recall_score(labels, predictions, average='macro')
                }

            # Initialize trainer
            trainer = Trainer(
                model=model,
                args=training_args,
                train_dataset=train_dataset,
                eval_dataset=val_dataset,
                compute_metrics=compute_metrics,
            )

            if progress_callback:
                progress_callback({"stage": "training", "progress": 0.3})

            # Train model with monitoring
            training_start = time.time()
            trainer.train()
            training_end = time.time()

            if progress_callback:
                progress_callback({"stage": "evaluation", "progress": 0.8})

            # Get predictions for comprehensive evaluation
            val_predictions = trainer.predict(val_dataset)
            val_probs = torch.softmax(torch.tensor(val_predictions.predictions), dim=1).numpy()
            val_preds = np.argmax(val_predictions.predictions, axis=1)

            # Compute comprehensive metrics
            final_metrics = self.compute_enhanced_metrics(val_labels, val_preds, val_probs)

            # Add training metadata
            gpu_summary = self.gpu_monitor.get_monitoring_summary()
            final_metrics.update({
                'data_characteristics': data_characteristics,
                'classification_strategy': self.classification_strategy,
                'strategy_info': self.strategy_handler.get_strategy_info(self.classification_strategy),
                'gpu_monitoring': gpu_summary,
                'training_time': training_end - training_start,
                'class_weights': self.class_weights,
                'label_encoding': {
                    'classes': self.label_encoder.classes_.tolist(),
                    'class_mapping': {cls: int(idx) for idx, cls in enumerate(self.label_encoder.classes_)}
                }
            })

            # Save model artifacts
            model_id = f"enhanced_multiclass_model_{int(time.time())}"
            model_path = f"./model_artifacts/{model_id}"

            trainer.save_model(model_path)
            tokenizer.save_pretrained(model_path)

            # Store model info
            self.model = model
            self.tokenizer = tokenizer
            self.is_trained = True

            training_time = time.time() - start_time

            if progress_callback:
                progress_callback({"stage": "complete", "progress": 1.0})

            return TrainingResult(
                model_id=model_id,
                training_time=training_time,
                final_metrics=final_metrics,
                training_history=[],  # TODO: Extract from trainer logs
                model_path=model_path,
                tokenizer_path=model_path,
                config_path=model_path
            )

        except Exception as e:
            logger.error(f"Enhanced multi-class training failed: {e}")
            return TrainingResult(
                model_id="",
                training_time=time.time() - start_time,
                final_metrics={},
                training_history=[],
                error=str(e)
            )
