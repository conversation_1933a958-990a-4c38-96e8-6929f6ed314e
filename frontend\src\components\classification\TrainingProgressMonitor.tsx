/**
 * TrainingProgressMonitor.tsx
 * 
 * Real-time training monitoring component with WebSocket integration for live progress updates.
 * Provides detailed training metrics, loss curves, and performance monitoring.
 */

import React, { useState, useEffect, useRef } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  Activity, 
  Clock, 
  TrendingUp, 
  TrendingDown, 
  Zap, 
  AlertCircle,
  CheckCircle2,
  Pause,
  Play,
  Square,
  Cpu,
  MemoryStick,
  Thermometer
} from "lucide-react";
import { <PERSON><PERSON>hart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';

interface TrainingMetrics {
  epoch: number;
  step: number;
  loss: number;
  accuracy: number;
  learning_rate: number;
  elapsed_time: number;
  estimated_remaining: number;
  gpu_utilization?: number;
  memory_usage?: number;
  temperature?: number;
}

interface TrainingProgressMonitorProps {
  taskId: string;
  onProgress?: (progress: number) => void;
  onComplete?: (results: any) => void;
  onError?: (error: string) => void;
  showDetailedMetrics?: boolean;
  showSystemMetrics?: boolean;
}

interface WebSocketMessage {
  type: 'progress' | 'metrics' | 'complete' | 'error' | 'system';
  data: any;
  timestamp: string;
}

export const TrainingProgressMonitor: React.FC<TrainingProgressMonitorProps> = ({
  taskId,
  onProgress,
  onComplete,
  onError,
  showDetailedMetrics = true,
  showSystemMetrics = true
}) => {
  // State management
  const [isConnected, setIsConnected] = useState(false);
  const [currentMetrics, setCurrentMetrics] = useState<TrainingMetrics | null>(null);
  const [metricsHistory, setMetricsHistory] = useState<TrainingMetrics[]>([]);
  const [progress, setProgress] = useState(0);
  const [status, setStatus] = useState<'connecting' | 'training' | 'paused' | 'completed' | 'error'>('connecting');
  const [error, setError] = useState<string | null>(null);
  const [startTime, setStartTime] = useState<Date | null>(null);
  
  // WebSocket management
  const wsRef = useRef<WebSocket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const reconnectAttempts = useRef(0);
  const maxReconnectAttempts = 5;

  // Initialize WebSocket connection
  useEffect(() => {
    if (!taskId) return;

    connectWebSocket();
    setStartTime(new Date());

    return () => {
      if (wsRef.current) {
        wsRef.current.close();
      }
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
      }
    };
  }, [taskId]);

  const connectWebSocket = () => {
    try {
      // Get auth token from localStorage or context
      const token = localStorage.getItem('auth_token');
      const wsUrl = `${window.location.protocol === 'https:' ? 'wss:' : 'ws:'}//${window.location.host}/ws/training/${taskId}?token=${token}`;
      
      wsRef.current = new WebSocket(wsUrl);

      wsRef.current.onopen = () => {
        console.log('WebSocket connected for training monitoring');
        setIsConnected(true);
        setStatus('training');
        reconnectAttempts.current = 0;
      };

      wsRef.current.onmessage = (event) => {
        try {
          const message: WebSocketMessage = JSON.parse(event.data);
          handleWebSocketMessage(message);
        } catch (error) {
          console.error('Error parsing WebSocket message:', error);
        }
      };

      wsRef.current.onclose = (event) => {
        console.log('WebSocket disconnected:', event.code, event.reason);
        setIsConnected(false);
        
        // Attempt to reconnect if not a normal closure
        if (event.code !== 1000 && reconnectAttempts.current < maxReconnectAttempts) {
          reconnectAttempts.current++;
          reconnectTimeoutRef.current = setTimeout(() => {
            console.log(`Attempting to reconnect (${reconnectAttempts.current}/${maxReconnectAttempts})`);
            connectWebSocket();
          }, Math.pow(2, reconnectAttempts.current) * 1000); // Exponential backoff
        }
      };

      wsRef.current.onerror = (error) => {
        console.error('WebSocket error:', error);
        setError('Connection error occurred');
      };

    } catch (error) {
      console.error('Failed to create WebSocket connection:', error);
      setError('Failed to establish real-time connection');
    }
  };

  const handleWebSocketMessage = (message: WebSocketMessage) => {
    switch (message.type) {
      case 'progress':
        const progressValue = message.data.progress || 0;
        setProgress(progressValue);
        onProgress?.(progressValue);
        break;

      case 'metrics':
        const metrics: TrainingMetrics = message.data;
        setCurrentMetrics(metrics);
        setMetricsHistory(prev => [...prev, metrics].slice(-100)); // Keep last 100 points
        setProgress(metrics.step ? (metrics.step / (metrics.step + metrics.estimated_remaining)) * 100 : 0);
        break;

      case 'complete':
        setStatus('completed');
        setProgress(100);
        onComplete?.(message.data);
        break;

      case 'error':
        setStatus('error');
        setError(message.data.error || 'Training failed');
        onError?.(message.data.error || 'Training failed');
        break;

      case 'system':
        // Handle system metrics if needed
        if (showSystemMetrics && currentMetrics) {
          setCurrentMetrics(prev => prev ? {
            ...prev,
            gpu_utilization: message.data.gpu_utilization,
            memory_usage: message.data.memory_usage,
            temperature: message.data.temperature
          } : null);
        }
        break;

      default:
        console.log('Unknown message type:', message.type);
    }
  };

  const formatTime = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes}m ${secs}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${secs}s`;
    } else {
      return `${secs}s`;
    }
  };

  const getStatusColor = () => {
    switch (status) {
      case 'training': return 'text-blue-600';
      case 'completed': return 'text-green-600';
      case 'error': return 'text-red-600';
      case 'paused': return 'text-yellow-600';
      default: return 'text-muted-foreground';
    }
  };

  const getStatusIcon = () => {
    switch (status) {
      case 'training': return <Activity className="w-4 h-4 animate-pulse" />;
      case 'completed': return <CheckCircle2 className="w-4 h-4" />;
      case 'error': return <AlertCircle className="w-4 h-4" />;
      case 'paused': return <Pause className="w-4 h-4" />;
      default: return <Clock className="w-4 h-4" />;
    }
  };

  return (
    <div className="space-y-4">
      {/* Connection Status */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`} />
          <span className="text-sm text-muted-foreground">
            {isConnected ? 'Connected' : 'Disconnected'}
          </span>
        </div>
        <Badge variant={status === 'completed' ? 'default' : 'secondary'} className={getStatusColor()}>
          {getStatusIcon()}
          <span className="ml-1 capitalize">{status}</span>
        </Badge>
      </div>

      {/* Error Alert */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Progress Overview */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-lg">Training Progress</CardTitle>
          <CardDescription>Real-time training metrics and progress</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Progress Bar */}
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Overall Progress</span>
              <span>{progress.toFixed(1)}%</span>
            </div>
            <Progress value={progress} className="h-2" />
          </div>

          {/* Current Metrics */}
          {currentMetrics && (
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center p-3 bg-muted/50 rounded-lg">
                <div className="text-lg font-bold text-blue-600">
                  {currentMetrics.epoch}
                </div>
                <div className="text-xs text-muted-foreground">Epoch</div>
              </div>
              <div className="text-center p-3 bg-muted/50 rounded-lg">
                <div className="text-lg font-bold text-green-600">
                  {currentMetrics.loss.toFixed(4)}
                </div>
                <div className="text-xs text-muted-foreground">Loss</div>
              </div>
              <div className="text-center p-3 bg-muted/50 rounded-lg">
                <div className="text-lg font-bold text-purple-600">
                  {(currentMetrics.accuracy * 100).toFixed(1)}%
                </div>
                <div className="text-xs text-muted-foreground">Accuracy</div>
              </div>
              <div className="text-center p-3 bg-muted/50 rounded-lg">
                <div className="text-lg font-bold text-orange-600">
                  {formatTime(currentMetrics.elapsed_time)}
                </div>
                <div className="text-xs text-muted-foreground">Elapsed</div>
              </div>
            </div>
          )}

          {/* Time Estimates */}
          {currentMetrics && (
            <div className="flex items-center justify-between text-sm text-muted-foreground">
              <div className="flex items-center gap-2">
                <Clock className="w-4 h-4" />
                <span>Estimated remaining: {formatTime(currentMetrics.estimated_remaining)}</span>
              </div>
              <div className="flex items-center gap-2">
                <TrendingUp className="w-4 h-4" />
                <span>Learning rate: {currentMetrics.learning_rate.toExponential(2)}</span>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Detailed Metrics Charts */}
      {showDetailedMetrics && metricsHistory.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Training Curves</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Loss Curve */}
              <div>
                <h4 className="font-medium mb-3">Loss</h4>
                <ResponsiveContainer width="100%" height={200}>
                  <LineChart data={metricsHistory}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="step" />
                    <YAxis />
                    <Tooltip />
                    <Line 
                      type="monotone" 
                      dataKey="loss" 
                      stroke="#ef4444" 
                      strokeWidth={2}
                      dot={false}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </div>

              {/* Accuracy Curve */}
              <div>
                <h4 className="font-medium mb-3">Accuracy</h4>
                <ResponsiveContainer width="100%" height={200}>
                  <LineChart data={metricsHistory}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="step" />
                    <YAxis />
                    <Tooltip />
                    <Line 
                      type="monotone" 
                      dataKey="accuracy" 
                      stroke="#22c55e" 
                      strokeWidth={2}
                      dot={false}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* System Metrics */}
      {showSystemMetrics && currentMetrics && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">System Metrics</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {currentMetrics.gpu_utilization !== undefined && (
                <div className="flex items-center gap-3 p-3 bg-muted/50 rounded-lg">
                  <Zap className="w-5 h-5 text-yellow-600" />
                  <div>
                    <div className="font-medium">GPU Utilization</div>
                    <div className="text-sm text-muted-foreground">
                      {currentMetrics.gpu_utilization.toFixed(1)}%
                    </div>
                  </div>
                </div>
              )}
              
              {currentMetrics.memory_usage !== undefined && (
                <div className="flex items-center gap-3 p-3 bg-muted/50 rounded-lg">
                  <MemoryStick className="w-5 h-5 text-blue-600" />
                  <div>
                    <div className="font-medium">Memory Usage</div>
                    <div className="text-sm text-muted-foreground">
                      {(currentMetrics.memory_usage / 1024).toFixed(1)} GB
                    </div>
                  </div>
                </div>
              )}
              
              {currentMetrics.temperature !== undefined && (
                <div className="flex items-center gap-3 p-3 bg-muted/50 rounded-lg">
                  <Thermometer className="w-5 h-5 text-red-600" />
                  <div>
                    <div className="font-medium">Temperature</div>
                    <div className="text-sm text-muted-foreground">
                      {currentMetrics.temperature}°C
                    </div>
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default TrainingProgressMonitor;
