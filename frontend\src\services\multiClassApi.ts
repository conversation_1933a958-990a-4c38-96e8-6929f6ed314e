/**
 * Production-ready API service for multi-class classification operations
 */

import apiClient from './apiClient';

// Types for multi-class operations
export interface MultiClassTrainingRequest {
  file_id: string;
  text_column: string;
  label_column: string;
  classification_type: 'multi-class';
  config: {
    model_name: string;
    strategy: 'softmax' | 'ovr' | 'ovo' | 'auto';
    num_epochs: number;
    batch_size: number;
    learning_rate: number;
    validation_split: number;
    class_weight_strategy?: 'balanced' | 'custom' | 'none';
    custom_class_weights?: Record<string, number>;
    use_unsloth?: boolean;
    fp16?: boolean;
    gradient_checkpointing?: boolean;
    enable_early_stopping?: boolean;
    patience?: number;
    warmup_steps?: number;
  };
}

export interface MultiClassInferenceRequest {
  model_id: string;
  file_id: string;
  text_column: string;
  classification_type: 'multi-class';
  config?: {
    confidence_threshold?: number;
    return_probabilities?: boolean;
    max_predictions?: number;
  };
}

export interface MultiClassModel {
  id: string;
  name: string;
  base_model: string;
  created_at: string;
  status: 'training' | 'completed' | 'failed' | 'deployed';
  training_config: {
    epochs: number;
    batch_size: number;
    learning_rate: number;
    strategy: string;
    class_weight_strategy: string;
    loss_function: string;
    validation_split: number;
    use_unsloth: boolean;
  };
  metrics: {
    accuracy: number;
    f1_score: number;
    precision: number;
    recall: number;
    macro_avg: {
      precision: number;
      recall: number;
      f1_score: number;
    };
    weighted_avg: {
      precision: number;
      recall: number;
      f1_score: number;
    };
    per_class_metrics: Array<{
      class: string;
      precision: number;
      recall: number;
      f1_score: number;
      support: number;
    }>;
    training_time: number;
  };
  metadata: {
    num_classes: number;
    class_names: string[];
    strategy_used: string;
    total_samples: number;
    model_size: number;
    description?: string;
  };
}

export interface ListModelsParams {
  page?: number;
  limit?: number;
  search?: string;
  status?: string;
  strategy?: string;
  sort_by?: string;
  sort_order?: 'asc' | 'desc';
}

export interface ListModelsResponse {
  models: MultiClassModel[];
  total: number;
  page: number;
  limit: number;
  total_pages: number;
}

export interface TaskStatus {
  task_id: string;
  status: 'PENDING' | 'RUNNING' | 'SUCCESS' | 'FAILURE';
  progress?: number;
  message?: string;
  result?: any;
  // Enhanced progress information
  current_epoch?: number;
  total_epochs?: number;
  current_loss?: number;
  current_accuracy?: number;
  estimated_time_remaining?: number;
  stage?: string;
  processed_samples?: number;
  total_samples?: number;
  current_batch?: number;
  total_batches?: number;
  average_confidence?: number;
}

// API Functions

/**
 * Start multi-class model training
 */
export const startMultiClassTraining = async (
  request: MultiClassTrainingRequest
): Promise<{ task_id: string }> => {
  try {
    const response = await apiClient.post('/api/train/multi-class', request);
    return response.data;
  } catch (error: any) {
    throw new Error(
      error.response?.data?.message || 
      error.message || 
      'Failed to start multi-class training'
    );
  }
};

/**
 * Start multi-class inference
 */
export const startMultiClassInference = async (
  request: MultiClassInferenceRequest
): Promise<{ task_id?: string; predictions?: any; summary?: any }> => {
  try {
    const response = await apiClient.post('/api/classify/multi-class', request);
    return response.data;
  } catch (error: any) {
    throw new Error(
      error.response?.data?.message || 
      error.message || 
      'Failed to start multi-class classification'
    );
  }
};

/**
 * Get task status with enhanced progress information
 */
export const getTaskStatus = async (taskId: string): Promise<TaskStatus> => {
  try {
    const response = await apiClient.get(`/api/tasks/${taskId}/status`);
    return response.data;
  } catch (error: any) {
    throw new Error(
      error.response?.data?.message || 
      error.message || 
      'Failed to get task status'
    );
  }
};

/**
 * List multi-class models with filtering and pagination
 */
export const listMultiClassModels = async (
  params: ListModelsParams = {}
): Promise<ListModelsResponse> => {
  try {
    const response = await apiClient.get('/api/models/multi-class', { params });
    return response.data;
  } catch (error: any) {
    console.error('Failed to load multi-class models:', error);

    // Fallback to empty response for development/production environments
    // where the models API might not be available
    const fallbackResponse: ListModelsResponse = {
      models: [],
      total: 0,
      page: params.page || 1,
      limit: params.limit || 10,
      total_pages: 0
    };

    console.warn('Using fallback empty models list due to API error');
    return fallbackResponse;
  }
};

/**
 * Get detailed model information
 */
export const getMultiClassModel = async (modelId: string): Promise<MultiClassModel> => {
  try {
    const response = await apiClient.get(`/api/models/multi-class/${modelId}`);
    return response.data;
  } catch (error: any) {
    throw new Error(
      error.response?.data?.message || 
      error.message || 
      'Failed to get model details'
    );
  }
};

/**
 * Delete a multi-class model
 */
export const deleteMultiClassModel = async (modelId: string): Promise<void> => {
  try {
    await apiClient.delete(`/api/models/multi-class/${modelId}`);
  } catch (error: any) {
    throw new Error(
      error.response?.data?.message || 
      error.message || 
      'Failed to delete model'
    );
  }
};

/**
 * Export a multi-class model
 */
export const exportMultiClassModel = async (
  modelId: string,
  format: 'pytorch' | 'onnx' | 'huggingface' | 'tensorflow' = 'pytorch'
): Promise<{ download_url: string; expires_at: string }> => {
  try {
    const response = await apiClient.post(`/api/models/multi-class/${modelId}/export`, {
      format
    });
    return response.data;
  } catch (error: any) {
    throw new Error(
      error.response?.data?.message || 
      error.message || 
      'Failed to export model'
    );
  }
};

/**
 * Deploy a multi-class model
 */
export const deployMultiClassModel = async (
  modelId: string,
  deploymentConfig: {
    name: string;
    deployment_type: 'api' | 'batch' | 'edge';
    config: Record<string, any>;
  }
): Promise<{ deployment_id: string; endpoint_url?: string }> => {
  try {
    const response = await apiClient.post(`/api/models/multi-class/${modelId}/deploy`, deploymentConfig);
    return response.data;
  } catch (error: any) {
    throw new Error(
      error.response?.data?.message || 
      error.message || 
      'Failed to deploy model'
    );
  }
};

/**
 * Validate multi-class training data
 */
export const validateMultiClassData = async (
  fileId: string,
  textColumn: string,
  labelColumn: string
): Promise<{
  valid: boolean;
  errors: string[];
  warnings: string[];
  stats: {
    num_samples: number;
    num_classes: number;
    class_distribution: Record<string, number>;
    imbalance_ratio: number;
    avg_text_length: number;
  };
}> => {
  try {
    const response = await apiClient.post('/api/validate/multi-class', {
      file_id: fileId,
      text_column: textColumn,
      label_column: labelColumn
    });
    return response.data;
  } catch (error: any) {
    throw new Error(
      error.response?.data?.message || 
      error.message || 
      'Failed to validate data'
    );
  }
};

/**
 * Get strategy recommendations based on data characteristics
 */
export const getStrategyRecommendations = async (
  fileId: string,
  textColumn: string,
  labelColumn: string
): Promise<{
  recommended_strategy: 'softmax' | 'ovr' | 'ovo';
  reasoning: string;
  confidence: number;
  alternatives: Array<{
    strategy: string;
    reasoning: string;
    confidence: number;
  }>;
}> => {
  try {
    const response = await apiClient.post('/api/recommend/strategy/multi-class', {
      file_id: fileId,
      text_column: textColumn,
      label_column: labelColumn
    });
    return response.data;
  } catch (error: any) {
    throw new Error(
      error.response?.data?.message || 
      error.message || 
      'Failed to get strategy recommendations'
    );
  }
};
