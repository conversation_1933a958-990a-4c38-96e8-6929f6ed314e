import { useState } from "react";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Sliders,
  Play,
  Pause,
  Square,
  Settings,
  Target,
  Zap,
  CheckCircle2,
  TrendingUp
} from "lucide-react";

interface HyperparameterTuningProps {
  modelType: string;
  onTuningComplete: (results: any) => void;
}

export const HyperparameterTuning = ({ modelType, onTuningComplete }: HyperparameterTuningProps) => {
  const [isRunning, setIsRunning] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [progress, setProgress] = useState(0);
  const [currentTrial, setCurrentTrial] = useState(0);
  const [totalTrials] = useState(100);
  const [bestScore, setBestScore] = useState(0);
  const [timeElapsed, setTimeElapsed] = useState(0);
  const [estimatedTimeRemaining, setEstimatedTimeRemaining] = useState(0);

  const [tuningConfig, setTuningConfig] = useState({
    searchStrategy: 'bayesian',
    maxTrials: 100,
    maxTime: 3600, // seconds
    cvFolds: 5,
    scoringMetric: 'accuracy',
    earlyStoppingRounds: 10,
    parallelJobs: 4
  });

  const [parameterRanges, setParameterRanges] = useState<Record<string, { min: number; max: number; type: string }>>({
    learning_rate: { min: 0.001, max: 0.3, type: 'log' },
    n_estimators: { min: 50, max: 500, type: 'int' },
    max_depth: { min: 3, max: 15, type: 'int' },
    min_samples_split: { min: 2, max: 20, type: 'int' },
    min_samples_leaf: { min: 1, max: 10, type: 'int' }
  });

  const handleStartTuning = () => {
    setIsRunning(true);
    setIsPaused(false);
    // Simulate tuning process
    simulateTuning();
  };

  const handlePauseTuning = () => {
    setIsPaused(!isPaused);
  };

  const handleStopTuning = () => {
    setIsRunning(false);
    setIsPaused(false);
    setProgress(0);
    setCurrentTrial(0);
  };

  const simulateTuning = async () => {
    for (let trial = 1; trial <= totalTrials; trial++) {
      if (!isRunning || isPaused) break;
      
      setCurrentTrial(trial);
      setProgress((trial / totalTrials) * 100);
      
      // Simulate improving best score
      const newScore = Math.min(0.95, 0.7 + (trial / totalTrials) * 0.25 + Math.random() * 0.05);
      if (newScore > bestScore) {
        setBestScore(newScore);
      }
      
      setTimeElapsed(trial * 30); // 30 seconds per trial
      setEstimatedTimeRemaining((totalTrials - trial) * 30);
      
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    if (isRunning) {
      setIsRunning(false);
      onTuningComplete({
        bestScore,
        bestParams: {
          learning_rate: 0.1,
          n_estimators: 200,
          max_depth: 8
        },
        totalTrials: currentTrial
      });
    }
  };

  const formatTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    return `${hours}h ${minutes}m ${secs}s`;
  };

  return (
    <div className="space-y-6">
      {/* Tuning Status */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 rounded-lg bg-ml-primary/10 flex items-center justify-center">
                <Sliders className="w-5 h-5 text-ml-primary" />
              </div>
              <div>
                <CardTitle>Hyperparameter Tuning</CardTitle>
                <CardDescription>
                  Optimize {modelType} parameters for best performance
                </CardDescription>
              </div>
            </div>
            <div className="flex gap-2">
              {!isRunning ? (
                <Button onClick={handleStartTuning}>
                  <Play className="w-4 h-4 mr-2" />
                  Start Tuning
                </Button>
              ) : (
                <>
                  <Button variant="outline" onClick={handlePauseTuning}>
                    {isPaused ? <Play className="w-4 h-4" /> : <Pause className="w-4 h-4" />}
                  </Button>
                  <Button variant="outline" onClick={handleStopTuning}>
                    <Square className="w-4 h-4" />
                  </Button>
                </>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {isRunning && (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-ml-success rounded-full animate-pulse"></div>
                  <span className="font-medium">
                    {isPaused ? 'Tuning paused' : 'Tuning in progress...'}
                  </span>
                </div>
                <Badge className="bg-ml-primary/10 text-ml-primary">
                  Trial {currentTrial}/{totalTrials}
                </Badge>
              </div>
              
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Progress</span>
                  <span>{Math.round(progress)}%</span>
                </div>
                <Progress value={progress} className="h-2" />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div className="p-3 bg-muted/50 rounded-lg text-center">
                  <div className="text-lg font-bold text-ml-success">
                    {(bestScore * 100).toFixed(1)}%
                  </div>
                  <div className="text-xs text-muted-foreground">Best Score</div>
                </div>
                <div className="p-3 bg-muted/50 rounded-lg text-center">
                  <div className="text-lg font-bold">
                    {currentTrial}
                  </div>
                  <div className="text-xs text-muted-foreground">Trials</div>
                </div>
                <div className="p-3 bg-muted/50 rounded-lg text-center">
                  <div className="text-lg font-bold">
                    {formatTime(timeElapsed)}
                  </div>
                  <div className="text-xs text-muted-foreground">Elapsed</div>
                </div>
                <div className="p-3 bg-muted/50 rounded-lg text-center">
                  <div className="text-lg font-bold">
                    {formatTime(estimatedTimeRemaining)}
                  </div>
                  <div className="text-xs text-muted-foreground">Remaining</div>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Configuration */}
      <Tabs defaultValue="strategy" className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="strategy">Strategy</TabsTrigger>
          <TabsTrigger value="parameters">Parameters</TabsTrigger>
          <TabsTrigger value="validation">Validation</TabsTrigger>
        </TabsList>

        <TabsContent value="strategy" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Target className="w-5 h-5" />
                Search Strategy
              </CardTitle>
              <CardDescription>
                Choose the optimization algorithm for hyperparameter search
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Card className={`cursor-pointer transition-all ${tuningConfig.searchStrategy === 'grid' ? 'border-2 border-ml-primary bg-ml-primary/5' : 'hover:border-primary/20'}`}>
                  <CardContent className="p-4">
                    <div className="text-center space-y-2">
                      <Settings className="w-8 h-8 mx-auto text-muted-foreground" />
                      <h4 className="font-semibold">Grid Search</h4>
                      <p className="text-xs text-muted-foreground">
                        Exhaustive search over parameter grid
                      </p>
                      <Badge variant={tuningConfig.searchStrategy === 'grid' ? 'default' : 'outline'}>
                        Thorough
                      </Badge>
                    </div>
                  </CardContent>
                </Card>

                <Card className={`cursor-pointer transition-all ${tuningConfig.searchStrategy === 'random' ? 'border-2 border-ml-primary bg-ml-primary/5' : 'hover:border-primary/20'}`}>
                  <CardContent className="p-4">
                    <div className="text-center space-y-2">
                      <Zap className="w-8 h-8 mx-auto text-muted-foreground" />
                      <h4 className="font-semibold">Random Search</h4>
                      <p className="text-xs text-muted-foreground">
                        Random sampling of parameter space
                      </p>
                      <Badge variant={tuningConfig.searchStrategy === 'random' ? 'default' : 'outline'}>
                        Fast
                      </Badge>
                    </div>
                  </CardContent>
                </Card>

                <Card className={`cursor-pointer transition-all ${tuningConfig.searchStrategy === 'bayesian' ? 'border-2 border-ml-primary bg-ml-primary/5' : 'hover:border-primary/20'}`}>
                  <CardContent className="p-4">
                    <div className="text-center space-y-2">
                      <TrendingUp className="w-8 h-8 mx-auto text-muted-foreground" />
                      <h4 className="font-semibold">Bayesian Optimization</h4>
                      <p className="text-xs text-muted-foreground">
                        Smart search using past results
                      </p>
                      <Badge variant={tuningConfig.searchStrategy === 'bayesian' ? 'default' : 'outline'}>
                        Efficient
                      </Badge>
                    </div>
                  </CardContent>
                </Card>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                <div className="space-y-4">
                  <h4 className="font-semibold">Search Limits</h4>
                  <div className="space-y-3">
                    <div>
                      <Label htmlFor="max-trials">Maximum Trials</Label>
                      <Input
                        id="max-trials"
                        type="number"
                        value={tuningConfig.maxTrials}
                        onChange={(e) => setTuningConfig(prev => ({ ...prev, maxTrials: parseInt(e.target.value) }))}
                        className="mt-1"
                      />
                    </div>
                    <div>
                      <Label htmlFor="max-time">Maximum Time (seconds)</Label>
                      <Input
                        id="max-time"
                        type="number"
                        value={tuningConfig.maxTime}
                        onChange={(e) => setTuningConfig(prev => ({ ...prev, maxTime: parseInt(e.target.value) }))}
                        className="mt-1"
                      />
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <h4 className="font-semibold">Performance Settings</h4>
                  <div className="space-y-3">
                    <div>
                      <Label htmlFor="parallel-jobs">Parallel Jobs</Label>
                      <Input
                        id="parallel-jobs"
                        type="number"
                        value={tuningConfig.parallelJobs}
                        onChange={(e) => setTuningConfig(prev => ({ ...prev, parallelJobs: parseInt(e.target.value) }))}
                        className="mt-1"
                      />
                    </div>
                    <div>
                      <Label htmlFor="early-stopping">Early Stopping Rounds</Label>
                      <Input
                        id="early-stopping"
                        type="number"
                        value={tuningConfig.earlyStoppingRounds}
                        onChange={(e) => setTuningConfig(prev => ({ ...prev, earlyStoppingRounds: parseInt(e.target.value) }))}
                        className="mt-1"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="parameters" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Sliders className="w-5 h-5" />
                Parameter Ranges
              </CardTitle>
              <CardDescription>
                Define the search space for each hyperparameter
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {Object.entries(parameterRanges).map(([param, config]) => (
                  <div key={param} className="p-4 border rounded-lg">
                    <div className="flex items-center justify-between mb-3">
                      <h4 className="font-semibold capitalize">{param.replace('_', ' ')}</h4>
                      <Badge variant="outline">{config.type}</Badge>
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor={`${param}-min`}>Minimum</Label>
                        <Input
                          id={`${param}-min`}
                          type="number"
                          step={config.type === 'log' ? '0.001' : '1'}
                          value={config.min}
                          onChange={(e) => setParameterRanges(prev => ({
                            ...prev,
                            [param]: { ...prev[param], min: parseFloat(e.target.value) }
                          }))}
                          className="mt-1"
                        />
                      </div>
                      <div>
                        <Label htmlFor={`${param}-max`}>Maximum</Label>
                        <Input
                          id={`${param}-max`}
                          type="number"
                          step={config.type === 'log' ? '0.001' : '1'}
                          value={config.max}
                          onChange={(e) => setParameterRanges(prev => ({
                            ...prev,
                            [param]: { ...prev[param], max: parseFloat(e.target.value) }
                          }))}
                          className="mt-1"
                        />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="validation" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CheckCircle2 className="w-5 h-5" />
                Cross-Validation Settings
              </CardTitle>
              <CardDescription>
                Configure how model performance is evaluated during tuning
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="cv-folds">Number of Folds</Label>
                    <Input
                      id="cv-folds"
                      type="number"
                      min="2"
                      max="10"
                      value={tuningConfig.cvFolds}
                      onChange={(e) => setTuningConfig(prev => ({ ...prev, cvFolds: parseInt(e.target.value) }))}
                      className="mt-1"
                    />
                  </div>
                  <div>
                    <Label htmlFor="scoring-metric">Scoring Metric</Label>
                    <select 
                      id="scoring-metric" 
                      className="w-full mt-1 px-3 py-2 border rounded-md bg-background"
                      value={tuningConfig.scoringMetric}
                      onChange={(e) => setTuningConfig(prev => ({ ...prev, scoringMetric: e.target.value }))}
                    >
                      <option value="accuracy">Accuracy</option>
                      <option value="precision">Precision</option>
                      <option value="recall">Recall</option>
                      <option value="f1">F1 Score</option>
                      <option value="roc_auc">ROC AUC</option>
                    </select>
                  </div>
                </div>
                
                <div className="space-y-4">
                  <div className="p-4 bg-muted/50 rounded-lg">
                    <h4 className="font-semibold mb-2">Validation Strategy</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex items-center gap-2">
                        <CheckCircle2 className="w-4 h-4 text-ml-success" />
                        <span>Stratified K-Fold</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <CheckCircle2 className="w-4 h-4 text-ml-success" />
                        <span>Balanced class distribution</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <CheckCircle2 className="w-4 h-4 text-ml-success" />
                        <span>Reproducible splits</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};
