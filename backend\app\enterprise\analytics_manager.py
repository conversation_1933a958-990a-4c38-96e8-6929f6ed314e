"""Enterprise Analytics Manager for ClassyWeb Universal Platform."""

import logging
import json
from typing import Dict, List, Any, Optional
from datetime import datetime, timezone, timedelta
from dataclasses import dataclass
from enum import Enum
import pandas as pd

from ..database import SessionLocal, Task, File
from ..models.tenant import AuditLog, TenantResource

logger = logging.getLogger(__name__)


class AnalyticsMetric(Enum):
    """Types of analytics metrics."""
    USAGE = "usage"
    PERFORMANCE = "performance"
    ACCURACY = "accuracy"
    COST = "cost"
    USER_BEHAVIOR = "user_behavior"
    RESOURCE_UTILIZATION = "resource_utilization"


class ReportType(Enum):
    """Types of analytics reports."""
    DASHBOARD = "dashboard"
    DETAILED = "detailed"
    EXECUTIVE = "executive"
    COMPLIANCE = "compliance"
    PERFORMANCE = "performance"


@dataclass
class AnalyticsQuery:
    """Query for analytics data."""
    tenant_id: str
    metric_types: List[AnalyticsMetric]
    start_date: datetime
    end_date: datetime
    granularity: str = "daily"  # hourly, daily, weekly, monthly
    filters: Dict[str, Any] = None
    group_by: List[str] = None


@dataclass
class AnalyticsResult:
    """Result from analytics query."""
    query: AnalyticsQuery
    data: Dict[str, Any]
    metadata: Dict[str, Any]
    generated_at: datetime


class EnterpriseAnalyticsManager:
    """Manages enterprise analytics and reporting."""
    
    def __init__(self):
        self.metric_calculators = {
            AnalyticsMetric.USAGE: self._calculate_usage_metrics,
            AnalyticsMetric.PERFORMANCE: self._calculate_performance_metrics,
            AnalyticsMetric.ACCURACY: self._calculate_accuracy_metrics,
            AnalyticsMetric.COST: self._calculate_cost_metrics,
            AnalyticsMetric.USER_BEHAVIOR: self._calculate_user_behavior_metrics,
            AnalyticsMetric.RESOURCE_UTILIZATION: self._calculate_resource_utilization_metrics
        }
        
        logger.info("Enterprise Analytics Manager initialized")
    
    async def generate_analytics_report(
        self,
        query: AnalyticsQuery,
        report_type: ReportType = ReportType.DASHBOARD
    ) -> AnalyticsResult:
        """Generate comprehensive analytics report."""
        try:
            logger.info(f"Generating {report_type.value} analytics report for tenant {query.tenant_id}")
            
            # Calculate metrics
            analytics_data = {}
            for metric_type in query.metric_types:
                calculator = self.metric_calculators.get(metric_type)
                if calculator:
                    metric_data = await calculator(query)
                    analytics_data[metric_type.value] = metric_data
                else:
                    logger.warning(f"No calculator found for metric type: {metric_type}")
            
            # Generate summary based on report type
            if report_type == ReportType.EXECUTIVE:
                analytics_data = await self._generate_executive_summary(analytics_data, query)
            elif report_type == ReportType.COMPLIANCE:
                analytics_data = await self._generate_compliance_report(analytics_data, query)
            
            # Create result
            result = AnalyticsResult(
                query=query,
                data=analytics_data,
                metadata={
                    "report_type": report_type.value,
                    "data_points": sum(len(data) if isinstance(data, list) else 1 for data in analytics_data.values()),
                    "calculation_time_ms": 0  # TODO: Track calculation time
                },
                generated_at=datetime.now(timezone.utc)
            )
            
            logger.info(f"Analytics report generated successfully for tenant {query.tenant_id}")
            return result
            
        except Exception as e:
            logger.error(f"Error generating analytics report: {str(e)}")
            raise
    
    async def get_real_time_metrics(self, tenant_id: str) -> Dict[str, Any]:
        """Get real-time metrics for a tenant."""
        try:
            current_time = datetime.now(timezone.utc)
            last_hour = current_time - timedelta(hours=1)
            
            with SessionLocal() as db:
                # Active tasks in last hour
                active_tasks = db.query(Task).filter(
                    Task.created_at >= last_hour,
                    # TODO: Add tenant filtering when Task model has tenant_id
                ).count()
                
                # API calls in last hour
                api_calls = db.query(AuditLog).filter(
                    AuditLog.tenant_id == tenant_id,
                    AuditLog.timestamp >= last_hour,
                    AuditLog.action.like("%api%")
                ).count()
                
                # Failed operations in last hour
                failed_operations = db.query(AuditLog).filter(
                    AuditLog.tenant_id == tenant_id,
                    AuditLog.timestamp >= last_hour,
                    AuditLog.status == "failure"
                ).count()
                
                # Resource usage
                resources = db.query(TenantResource).filter(
                    TenantResource.tenant_id == tenant_id
                ).all()
                
                resource_usage = {}
                for resource in resources:
                    resource_usage[resource.resource_type] = {
                        "used": resource.used_amount,
                        "limit": resource.limit_amount,
                        "usage_percentage": resource.usage_percentage
                    }
                
                return {
                    "tenant_id": tenant_id,
                    "timestamp": current_time.isoformat(),
                    "active_tasks": active_tasks,
                    "api_calls_last_hour": api_calls,
                    "failed_operations_last_hour": failed_operations,
                    "resource_usage": resource_usage,
                    "system_health": "healthy" if failed_operations < 5 else "degraded"
                }
                
        except Exception as e:
            logger.error(f"Error getting real-time metrics: {str(e)}")
            return {}
    
    async def _calculate_usage_metrics(self, query: AnalyticsQuery) -> Dict[str, Any]:
        """Calculate usage metrics."""
        try:
            with SessionLocal() as db:
                # Get audit logs for the period
                audit_logs = db.query(AuditLog).filter(
                    AuditLog.tenant_id == query.tenant_id,
                    AuditLog.timestamp >= query.start_date,
                    AuditLog.timestamp <= query.end_date
                ).all()
                
                # Calculate metrics
                total_actions = len(audit_logs)
                unique_users = len(set(log.user_id for log in audit_logs if log.user_id))
                
                # Action breakdown
                action_counts = {}
                for log in audit_logs:
                    action_counts[log.action] = action_counts.get(log.action, 0) + 1
                
                # Daily usage
                daily_usage = {}
                for log in audit_logs:
                    date_key = log.timestamp.date().isoformat()
                    daily_usage[date_key] = daily_usage.get(date_key, 0) + 1
                
                return {
                    "total_actions": total_actions,
                    "unique_users": unique_users,
                    "action_breakdown": action_counts,
                    "daily_usage": daily_usage,
                    "avg_daily_usage": sum(daily_usage.values()) / len(daily_usage) if daily_usage else 0
                }
                
        except Exception as e:
            logger.error(f"Error calculating usage metrics: {str(e)}")
            return {}
    
    async def _calculate_performance_metrics(self, query: AnalyticsQuery) -> Dict[str, Any]:
        """Calculate performance metrics."""
        try:
            with SessionLocal() as db:
                # Get tasks for the period
                tasks = db.query(Task).filter(
                    Task.created_at >= query.start_date,
                    Task.created_at <= query.end_date
                    # TODO: Add tenant filtering
                ).all()
                
                if not tasks:
                    return {"message": "No tasks found for the period"}
                
                # Calculate performance metrics
                completed_tasks = [t for t in tasks if t.status == "completed"]
                failed_tasks = [t for t in tasks if t.status == "failed"]
                
                # Execution times
                execution_times = []
                for task in completed_tasks:
                    if task.created_at and task.updated_at:
                        duration = (task.updated_at - task.created_at).total_seconds()
                        execution_times.append(duration)
                
                avg_execution_time = sum(execution_times) / len(execution_times) if execution_times else 0
                
                return {
                    "total_tasks": len(tasks),
                    "completed_tasks": len(completed_tasks),
                    "failed_tasks": len(failed_tasks),
                    "success_rate": len(completed_tasks) / len(tasks) * 100 if tasks else 0,
                    "avg_execution_time_seconds": avg_execution_time,
                    "min_execution_time_seconds": min(execution_times) if execution_times else 0,
                    "max_execution_time_seconds": max(execution_times) if execution_times else 0
                }
                
        except Exception as e:
            logger.error(f"Error calculating performance metrics: {str(e)}")
            return {}
    
    async def _calculate_accuracy_metrics(self, query: AnalyticsQuery) -> Dict[str, Any]:
        """Calculate accuracy metrics."""
        try:
            # TODO: Implement accuracy metrics calculation
            # This would require storing model evaluation results
            return {
                "avg_accuracy": 0.85,
                "avg_precision": 0.82,
                "avg_recall": 0.78,
                "avg_f1_score": 0.80,
                "model_count": 5,
                "evaluation_count": 25
            }
            
        except Exception as e:
            logger.error(f"Error calculating accuracy metrics: {str(e)}")
            return {}
    
    async def _calculate_cost_metrics(self, query: AnalyticsQuery) -> Dict[str, Any]:
        """Calculate cost metrics."""
        try:
            # TODO: Implement cost calculation based on resource usage
            return {
                "total_cost_usd": 125.50,
                "compute_cost_usd": 75.00,
                "storage_cost_usd": 25.50,
                "api_cost_usd": 25.00,
                "cost_per_classification": 0.001,
                "projected_monthly_cost": 500.00
            }
            
        except Exception as e:
            logger.error(f"Error calculating cost metrics: {str(e)}")
            return {}
    
    async def _calculate_user_behavior_metrics(self, query: AnalyticsQuery) -> Dict[str, Any]:
        """Calculate user behavior metrics."""
        try:
            with SessionLocal() as db:
                # Get user activity logs
                user_logs = db.query(AuditLog).filter(
                    AuditLog.tenant_id == query.tenant_id,
                    AuditLog.timestamp >= query.start_date,
                    AuditLog.timestamp <= query.end_date,
                    AuditLog.user_id.isnot(None)
                ).all()
                
                if not user_logs:
                    return {"message": "No user activity found for the period"}
                
                # User activity analysis
                user_activity = {}
                for log in user_logs:
                    user_id = log.user_id
                    if user_id not in user_activity:
                        user_activity[user_id] = {
                            "total_actions": 0,
                            "unique_days": set(),
                            "actions": {}
                        }
                    
                    user_activity[user_id]["total_actions"] += 1
                    user_activity[user_id]["unique_days"].add(log.timestamp.date())
                    
                    action = log.action
                    if action not in user_activity[user_id]["actions"]:
                        user_activity[user_id]["actions"][action] = 0
                    user_activity[user_id]["actions"][action] += 1
                
                # Calculate metrics
                total_users = len(user_activity)
                active_users = sum(1 for activity in user_activity.values() if len(activity["unique_days"]) > 0)
                avg_actions_per_user = sum(activity["total_actions"] for activity in user_activity.values()) / total_users if total_users > 0 else 0
                
                return {
                    "total_users": total_users,
                    "active_users": active_users,
                    "avg_actions_per_user": avg_actions_per_user,
                    "user_retention_rate": active_users / total_users * 100 if total_users > 0 else 0,
                    "most_common_actions": self._get_most_common_actions(user_logs)
                }
                
        except Exception as e:
            logger.error(f"Error calculating user behavior metrics: {str(e)}")
            return {}
    
    async def _calculate_resource_utilization_metrics(self, query: AnalyticsQuery) -> Dict[str, Any]:
        """Calculate resource utilization metrics."""
        try:
            with SessionLocal() as db:
                # Get current resource usage
                resources = db.query(TenantResource).filter(
                    TenantResource.tenant_id == query.tenant_id
                ).all()
                
                utilization_data = {}
                for resource in resources:
                    utilization_data[resource.resource_type] = {
                        "used": resource.used_amount,
                        "limit": resource.limit_amount,
                        "utilization_percentage": resource.usage_percentage,
                        "unit": resource.unit,
                        "is_exceeded": resource.is_exceeded
                    }
                
                # Calculate trends (TODO: implement historical tracking)
                trends = {}
                for resource_type in utilization_data.keys():
                    trends[resource_type] = {
                        "trend": "stable",  # stable, increasing, decreasing
                        "growth_rate": 0.0,
                        "projected_exhaustion_days": None
                    }
                
                return {
                    "current_utilization": utilization_data,
                    "trends": trends,
                    "recommendations": self._generate_resource_recommendations(utilization_data)
                }
                
        except Exception as e:
            logger.error(f"Error calculating resource utilization metrics: {str(e)}")
            return {}
    
    async def _generate_executive_summary(
        self,
        analytics_data: Dict[str, Any],
        query: AnalyticsQuery
    ) -> Dict[str, Any]:
        """Generate executive summary from analytics data."""
        try:
            summary = {
                "period": {
                    "start_date": query.start_date.isoformat(),
                    "end_date": query.end_date.isoformat(),
                    "duration_days": (query.end_date - query.start_date).days
                },
                "key_metrics": {},
                "trends": {},
                "recommendations": [],
                "alerts": []
            }
            
            # Extract key metrics
            usage_data = analytics_data.get("usage", {})
            performance_data = analytics_data.get("performance", {})
            
            if usage_data:
                summary["key_metrics"]["total_actions"] = usage_data.get("total_actions", 0)
                summary["key_metrics"]["active_users"] = usage_data.get("unique_users", 0)
                summary["key_metrics"]["avg_daily_usage"] = usage_data.get("avg_daily_usage", 0)
            
            if performance_data:
                summary["key_metrics"]["success_rate"] = performance_data.get("success_rate", 0)
                summary["key_metrics"]["avg_response_time"] = performance_data.get("avg_execution_time_seconds", 0)
            
            # Generate recommendations
            if performance_data.get("success_rate", 100) < 95:
                summary["recommendations"].append("Investigate and address performance issues")
            
            if usage_data.get("avg_daily_usage", 0) > 1000:
                summary["recommendations"].append("Consider upgrading to higher tier for better performance")
            
            return summary
            
        except Exception as e:
            logger.error(f"Error generating executive summary: {str(e)}")
            return {}
    
    async def _generate_compliance_report(
        self,
        analytics_data: Dict[str, Any],
        query: AnalyticsQuery
    ) -> Dict[str, Any]:
        """Generate compliance-focused report."""
        try:
            with SessionLocal() as db:
                # Get audit logs for compliance analysis
                audit_logs = db.query(AuditLog).filter(
                    AuditLog.tenant_id == query.tenant_id,
                    AuditLog.timestamp >= query.start_date,
                    AuditLog.timestamp <= query.end_date
                ).all()
                
                # Compliance metrics
                data_access_events = [log for log in audit_logs if "data" in log.action]
                export_events = [log for log in audit_logs if "export" in log.action]
                admin_events = [log for log in audit_logs if "admin" in log.action]
                
                compliance_report = {
                    "audit_summary": {
                        "total_events": len(audit_logs),
                        "data_access_events": len(data_access_events),
                        "export_events": len(export_events),
                        "admin_events": len(admin_events)
                    },
                    "data_governance": {
                        "data_retention_compliance": True,  # TODO: Implement actual checks
                        "encryption_compliance": True,
                        "access_control_compliance": True
                    },
                    "security_compliance": {
                        "authentication_events": len([log for log in audit_logs if "auth" in log.action]),
                        "failed_access_attempts": len([log for log in audit_logs if log.status == "failure"]),
                        "suspicious_activity_detected": False  # TODO: Implement detection
                    },
                    "recommendations": [
                        "Regular security audits recommended",
                        "Consider implementing additional access controls"
                    ]
                }
                
                return compliance_report
                
        except Exception as e:
            logger.error(f"Error generating compliance report: {str(e)}")
            return {}
    
    def _get_most_common_actions(self, audit_logs: List[AuditLog]) -> List[Dict[str, Any]]:
        """Get most common actions from audit logs."""
        try:
            action_counts = {}
            for log in audit_logs:
                action_counts[log.action] = action_counts.get(log.action, 0) + 1
            
            # Sort by count and return top 10
            sorted_actions = sorted(action_counts.items(), key=lambda x: x[1], reverse=True)
            
            return [
                {"action": action, "count": count}
                for action, count in sorted_actions[:10]
            ]
            
        except Exception as e:
            logger.error(f"Error getting most common actions: {str(e)}")
            return []
    
    def _generate_resource_recommendations(self, utilization_data: Dict[str, Any]) -> List[str]:
        """Generate recommendations based on resource utilization."""
        recommendations = []
        
        try:
            for resource_type, data in utilization_data.items():
                utilization = data.get("utilization_percentage", 0)
                
                if utilization > 90:
                    recommendations.append(f"Critical: {resource_type} usage is at {utilization:.1f}% - immediate action required")
                elif utilization > 75:
                    recommendations.append(f"Warning: {resource_type} usage is at {utilization:.1f}% - consider upgrading")
                elif utilization < 25:
                    recommendations.append(f"Info: {resource_type} usage is low at {utilization:.1f}% - consider downgrading to save costs")
            
            return recommendations
            
        except Exception as e:
            logger.error(f"Error generating resource recommendations: {str(e)}")
            return []
    
    async def export_analytics_data(
        self,
        query: AnalyticsQuery,
        format: str = "json"
    ) -> Dict[str, Any]:
        """Export analytics data in specified format."""
        try:
            # Generate full analytics report
            result = await self.generate_analytics_report(query, ReportType.DETAILED)
            
            if format.lower() == "json":
                return {
                    "format": "json",
                    "data": result.data,
                    "metadata": result.metadata,
                    "exported_at": datetime.now(timezone.utc).isoformat()
                }
            elif format.lower() == "csv":
                # TODO: Implement CSV export
                return {
                    "format": "csv",
                    "message": "CSV export not yet implemented",
                    "exported_at": datetime.now(timezone.utc).isoformat()
                }
            else:
                raise ValueError(f"Unsupported export format: {format}")
                
        except Exception as e:
            logger.error(f"Error exporting analytics data: {str(e)}")
            raise


# Global enterprise analytics manager instance
enterprise_analytics_manager = EnterpriseAnalyticsManager()
