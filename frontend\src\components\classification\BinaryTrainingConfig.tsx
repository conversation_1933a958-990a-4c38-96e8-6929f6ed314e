/**
 * BinaryTrainingConfig.tsx
 * 
 * Advanced training configuration component for binary classification.
 * Includes threshold optimization, class imbalance handling, ROC curve configuration,
 * and binary-specific training parameters.
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Slider } from "@/components/ui/slider";
import { Switch } from "@/components/ui/switch";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Separator } from "@/components/ui/separator";
import { useToast } from "@/hooks/use-toast";
import {
  Settings,
  Target,
  TrendingUp,
  Scale,
  Zap,
  Brain,
  BarChart3,
  Info,
  AlertCircle,
  CheckCircle2,
  Activity,
  Sliders
} from "lucide-react";

export interface BinaryTrainingConfig {
  // Base training parameters
  modelName: string;
  baseModel: string;
  maxLength: number;
  
  // Training parameters
  numEpochs: number;
  batchSize: number;
  learningRate: number;
  validationSplit: number;
  
  // Advanced parameters
  warmupSteps: number;
  weightDecay: number;
  gradientAccumulationSteps: number;
  
  // Hardware optimization
  useUnsloth: boolean;
  fp16: boolean;
  gradientCheckpointing: boolean;
  
  // Early stopping
  enableEarlyStopping: boolean;
  patience: number;
  minDelta: number;
  
  // Binary-specific parameters
  thresholdStrategy: 'default' | 'optimal_f1' | 'optimal_precision' | 'optimal_recall' | 'youden';
  classWeightStrategy: 'balanced' | 'custom' | 'focal_loss' | 'none';
  calibrationMethod: 'platt' | 'isotonic' | 'none';
  imbalanceHandling: 'smote' | 'undersampling' | 'oversampling' | 'none';
  
  // Advanced binary features
  costSensitive: boolean;
  customThreshold: number;
  probabilityCalibration: boolean;
  rocOptimization: boolean;
  
  // Custom class weights (when classWeightStrategy is 'custom')
  positiveClassWeight: number;
  negativeClassWeight: number;
}

interface BinaryTrainingConfigProps {
  onConfigChange: (config: BinaryTrainingConfig) => void;
  onSave: (config: BinaryTrainingConfig) => void;
  initialConfig?: Partial<BinaryTrainingConfig>;
  userJourney: 'beginner' | 'expert';
  positiveClass: string;
  negativeClass: string;
  classBalance?: {
    positiveCount: number;
    negativeCount: number;
    imbalanceRatio: number;
  };
  estimatedTrainingTime?: string;
}

const DEFAULT_CONFIG: BinaryTrainingConfig = {
  // Base parameters
  modelName: "binary-classifier",
  baseModel: "distilbert-base-uncased",
  maxLength: 512,
  
  // Training parameters
  numEpochs: 3,
  batchSize: 16,
  learningRate: 2e-5,
  validationSplit: 0.2,
  
  // Advanced parameters
  warmupSteps: 500,
  weightDecay: 0.01,
  gradientAccumulationSteps: 1,
  
  // Hardware optimization
  useUnsloth: true,
  fp16: true,
  gradientCheckpointing: true,
  
  // Early stopping
  enableEarlyStopping: true,
  patience: 2,
  minDelta: 0.001,
  
  // Binary specific
  thresholdStrategy: 'youden',
  classWeightStrategy: 'balanced',
  calibrationMethod: 'platt',
  imbalanceHandling: 'smote',
  
  // Advanced binary features
  costSensitive: false,
  customThreshold: 0.5,
  probabilityCalibration: true,
  rocOptimization: true,
  
  // Custom class weights
  positiveClassWeight: 1.0,
  negativeClassWeight: 1.0
};

export const BinaryTrainingConfig: React.FC<BinaryTrainingConfigProps> = ({
  onConfigChange,
  onSave,
  initialConfig,
  userJourney,
  positiveClass,
  negativeClass,
  classBalance,
  estimatedTrainingTime
}) => {
  const { toast } = useToast();
  const [config, setConfig] = useState<BinaryTrainingConfig>({
    ...DEFAULT_CONFIG,
    ...initialConfig
  });

  const [activeTab, setActiveTab] = useState('basic');
  const [isAdvancedMode, setIsAdvancedMode] = useState(userJourney === 'expert');

  useEffect(() => {
    onConfigChange(config);
  }, [config, onConfigChange]);

  const updateConfig = (updates: Partial<BinaryTrainingConfig>) => {
    setConfig(prev => ({ ...prev, ...updates }));
  };

  const handleSave = () => {
    // Validate configuration
    const errors: string[] = [];
    
    if (!config.modelName.trim()) {
      errors.push("Model name is required");
    }
    
    if (config.numEpochs < 1 || config.numEpochs > 100) {
      errors.push("Number of epochs must be between 1 and 100");
    }
    
    if (config.batchSize < 1 || config.batchSize > 128) {
      errors.push("Batch size must be between 1 and 128");
    }
    
    if (config.learningRate <= 0 || config.learningRate > 1) {
      errors.push("Learning rate must be between 0 and 1");
    }

    if (errors.length > 0) {
      toast({
        title: "Configuration Error",
        description: errors.join(", "),
        variant: "destructive"
      });
      return;
    }

    onSave(config);
    toast({
      title: "Configuration Saved",
      description: "Binary training configuration has been saved successfully"
    });
  };

  const getModelOptions = () => [
    { value: "distilbert-base-uncased", label: "DistilBERT Base (Fast)", description: "Lightweight and fast" },
    { value: "bert-base-uncased", label: "BERT Base", description: "Balanced performance" },
    { value: "roberta-base", label: "RoBERTa Base", description: "Better performance" },
    { value: "albert-base-v2", label: "ALBERT Base", description: "Memory efficient" },
    { value: "electra-base-discriminator", label: "ELECTRA Base", description: "High accuracy" }
  ];

  const getThresholdStrategyInfo = (strategy: string) => {
    const info = {
      'default': {
        description: "Standard 0.5 threshold",
        bestFor: "Balanced datasets with equal class importance"
      },
      'optimal_f1': {
        description: "Maximizes F1 score",
        bestFor: "When you want balanced precision and recall"
      },
      'optimal_precision': {
        description: "Maximizes precision",
        bestFor: "When false positives are costly"
      },
      'optimal_recall': {
        description: "Maximizes recall",
        bestFor: "When false negatives are costly"
      },
      'youden': {
        description: "Maximizes sensitivity + specificity",
        bestFor: "Balanced optimization of true positive and true negative rates"
      }
    };
    return info[strategy as keyof typeof info] || info['default'];
  };

  const getImbalanceHandlingInfo = (method: string) => {
    const info = {
      'none': {
        description: "No special handling",
        bestFor: "Balanced datasets"
      },
      'smote': {
        description: "Synthetic Minority Oversampling Technique",
        bestFor: "Moderate imbalance (2:1 to 10:1)"
      },
      'undersampling': {
        description: "Reduce majority class samples",
        bestFor: "Large datasets with severe imbalance"
      },
      'oversampling': {
        description: "Duplicate minority class samples",
        bestFor: "Small datasets with imbalance"
      }
    };
    return info[method as keyof typeof info] || info['none'];
  };

  const isImbalanced = classBalance ? classBalance.imbalanceRatio > 2 : false;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Binary Training Configuration</h2>
          <p className="text-muted-foreground">
            Configure advanced parameters for binary classification training
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Label htmlFor="advanced-mode">Advanced Mode</Label>
          <Switch
            id="advanced-mode"
            checked={isAdvancedMode}
            onCheckedChange={setIsAdvancedMode}
          />
        </div>
      </div>

      {/* Class Balance Alert */}
      {classBalance && isImbalanced && (
        <Alert>
          <Scale className="h-4 w-4" />
          <AlertDescription>
            <strong>Class imbalance detected:</strong> {positiveClass} ({classBalance.positiveCount}) vs {negativeClass} ({classBalance.negativeCount})
            <br />
            Imbalance ratio: {classBalance.imbalanceRatio.toFixed(2)}:1 - Consider using imbalance handling techniques.
          </AlertDescription>
        </Alert>
      )}

      {/* Configuration Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="basic">Basic</TabsTrigger>
          <TabsTrigger value="threshold">Threshold</TabsTrigger>
          <TabsTrigger value="imbalance">Imbalance</TabsTrigger>
          <TabsTrigger value="optimization">Optimization</TabsTrigger>
        </TabsList>

        {/* Basic Configuration */}
        <TabsContent value="basic" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="w-5 h-5" />
                Basic Configuration
              </CardTitle>
              <CardDescription>
                Essential training parameters for binary classification
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="model-name">Model Name</Label>
                  <Input
                    id="model-name"
                    value={config.modelName}
                    onChange={(e) => updateConfig({ modelName: e.target.value })}
                    placeholder="Enter model name"
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="base-model">Base Model</Label>
                  <Select value={config.baseModel} onValueChange={(value) => updateConfig({ baseModel: value })}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select base model" />
                    </SelectTrigger>
                    <SelectContent>
                      {getModelOptions().map(option => (
                        <SelectItem key={option.value} value={option.value}>
                          <div>
                            <div className="font-medium">{option.label}</div>
                            <div className="text-sm text-muted-foreground">{option.description}</div>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="epochs">Epochs</Label>
                  <div className="space-y-2">
                    <Slider
                      value={[config.numEpochs]}
                      onValueChange={([value]) => updateConfig({ numEpochs: value })}
                      max={20}
                      min={1}
                      step={1}
                    />
                    <div className="text-sm text-muted-foreground text-center">
                      {config.numEpochs} epochs
                    </div>
                  </div>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="batch-size">Batch Size</Label>
                  <div className="space-y-2">
                    <Slider
                      value={[config.batchSize]}
                      onValueChange={([value]) => updateConfig({ batchSize: value })}
                      max={64}
                      min={4}
                      step={4}
                    />
                    <div className="text-sm text-muted-foreground text-center">
                      {config.batchSize} samples
                    </div>
                  </div>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="learning-rate">Learning Rate</Label>
                  <div className="space-y-2">
                    <Slider
                      value={[config.learningRate * 100000]}
                      onValueChange={([value]) => updateConfig({ learningRate: value / 100000 })}
                      max={10}
                      min={0.1}
                      step={0.1}
                    />
                    <div className="text-sm text-muted-foreground text-center">
                      {config.learningRate.toExponential(1)}
                    </div>
                  </div>
                </div>
              </div>

              {estimatedTrainingTime && (
                <Alert>
                  <TrendingUp className="h-4 w-4" />
                  <AlertDescription>
                    <strong>Estimated training time:</strong> {estimatedTrainingTime}
                  </AlertDescription>
                </Alert>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Threshold Optimization */}
        <TabsContent value="threshold" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Scale className="w-5 h-5" />
                Threshold Optimization
              </CardTitle>
              <CardDescription>
                Configure classification threshold and optimization strategy
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="threshold-strategy">Threshold Strategy</Label>
                <Select
                  value={config.thresholdStrategy}
                  onValueChange={(value: any) => updateConfig({ thresholdStrategy: value })}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select threshold strategy" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="default">Default (0.5)</SelectItem>
                    <SelectItem value="optimal_f1">Optimal F1 Score</SelectItem>
                    <SelectItem value="optimal_precision">Optimal Precision</SelectItem>
                    <SelectItem value="optimal_recall">Optimal Recall</SelectItem>
                    <SelectItem value="youden">Youden's J Statistic</SelectItem>
                  </SelectContent>
                </Select>
                <div className="text-sm text-muted-foreground">
                  {getThresholdStrategyInfo(config.thresholdStrategy).description}
                  <br />
                  <strong>Best for:</strong> {getThresholdStrategyInfo(config.thresholdStrategy).bestFor}
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="custom-threshold">Custom Threshold</Label>
                <div className="space-y-2">
                  <Slider
                    value={[config.customThreshold]}
                    onValueChange={([value]) => updateConfig({ customThreshold: value })}
                    max={1}
                    min={0}
                    step={0.01}
                  />
                  <div className="text-sm text-muted-foreground text-center">
                    {config.customThreshold.toFixed(2)}
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Switch
                    id="roc-optimization"
                    checked={config.rocOptimization}
                    onCheckedChange={(checked) => updateConfig({ rocOptimization: checked })}
                  />
                  <Label htmlFor="roc-optimization">Enable ROC curve optimization</Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Switch
                    id="probability-calibration"
                    checked={config.probabilityCalibration}
                    onCheckedChange={(checked) => updateConfig({ probabilityCalibration: checked })}
                  />
                  <Label htmlFor="probability-calibration">Enable probability calibration</Label>
                </div>

                {config.probabilityCalibration && (
                  <div className="space-y-2 pl-6">
                    <Label htmlFor="calibration-method">Calibration Method</Label>
                    <Select
                      value={config.calibrationMethod}
                      onValueChange={(value: any) => updateConfig({ calibrationMethod: value })}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select calibration method" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="platt">Platt Scaling</SelectItem>
                        <SelectItem value="isotonic">Isotonic Regression</SelectItem>
                        <SelectItem value="none">None</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Class Imbalance Handling */}
        <TabsContent value="imbalance" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="w-5 h-5" />
                Class Imbalance Handling
              </CardTitle>
              <CardDescription>
                Configure strategies for handling imbalanced datasets
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="imbalance-handling">Imbalance Handling Method</Label>
                <Select
                  value={config.imbalanceHandling}
                  onValueChange={(value: any) => updateConfig({ imbalanceHandling: value })}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select imbalance handling" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="none">None</SelectItem>
                    <SelectItem value="smote">SMOTE</SelectItem>
                    <SelectItem value="undersampling">Undersampling</SelectItem>
                    <SelectItem value="oversampling">Oversampling</SelectItem>
                  </SelectContent>
                </Select>
                <div className="text-sm text-muted-foreground">
                  {getImbalanceHandlingInfo(config.imbalanceHandling).description}
                  <br />
                  <strong>Best for:</strong> {getImbalanceHandlingInfo(config.imbalanceHandling).bestFor}
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="class-weight-strategy">Class Weighting Strategy</Label>
                <Select
                  value={config.classWeightStrategy}
                  onValueChange={(value: any) => updateConfig({ classWeightStrategy: value })}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select class weighting" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="balanced">Balanced</SelectItem>
                    <SelectItem value="custom">Custom</SelectItem>
                    <SelectItem value="focal_loss">Focal Loss</SelectItem>
                    <SelectItem value="none">None</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {config.classWeightStrategy === 'custom' && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4 border rounded-lg">
                  <div className="space-y-2">
                    <Label htmlFor="positive-weight">Positive Class Weight ({positiveClass})</Label>
                    <div className="space-y-2">
                      <Slider
                        value={[config.positiveClassWeight]}
                        onValueChange={([value]) => updateConfig({ positiveClassWeight: value })}
                        max={10}
                        min={0.1}
                        step={0.1}
                      />
                      <div className="text-sm text-muted-foreground text-center">
                        {config.positiveClassWeight.toFixed(1)}
                      </div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="negative-weight">Negative Class Weight ({negativeClass})</Label>
                    <div className="space-y-2">
                      <Slider
                        value={[config.negativeClassWeight]}
                        onValueChange={([value]) => updateConfig({ negativeClassWeight: value })}
                        max={10}
                        min={0.1}
                        step={0.1}
                      />
                      <div className="text-sm text-muted-foreground text-center">
                        {config.negativeClassWeight.toFixed(1)}
                      </div>
                    </div>
                  </div>
                </div>
              )}

              <div className="flex items-center space-x-2">
                <Switch
                  id="cost-sensitive"
                  checked={config.costSensitive}
                  onCheckedChange={(checked) => updateConfig({ costSensitive: checked })}
                />
                <Label htmlFor="cost-sensitive">Enable cost-sensitive learning</Label>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Optimization Configuration */}
        <TabsContent value="optimization" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Zap className="w-5 h-5" />
                Performance Optimization
              </CardTitle>
              <CardDescription>
                Hardware and performance optimization settings
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Switch
                    id="use-unsloth"
                    checked={config.useUnsloth}
                    onCheckedChange={(checked) => updateConfig({ useUnsloth: checked })}
                  />
                  <Label htmlFor="use-unsloth">Use Unsloth optimization</Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Switch
                    id="fp16"
                    checked={config.fp16}
                    onCheckedChange={(checked) => updateConfig({ fp16: checked })}
                  />
                  <Label htmlFor="fp16">Enable FP16 training</Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Switch
                    id="gradient-checkpointing"
                    checked={config.gradientCheckpointing}
                    onCheckedChange={(checked) => updateConfig({ gradientCheckpointing: checked })}
                  />
                  <Label htmlFor="gradient-checkpointing">Enable gradient checkpointing</Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Switch
                    id="early-stopping"
                    checked={config.enableEarlyStopping}
                    onCheckedChange={(checked) => updateConfig({ enableEarlyStopping: checked })}
                  />
                  <Label htmlFor="early-stopping">Enable early stopping</Label>
                </div>
              </div>

              {config.enableEarlyStopping && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4 border rounded-lg">
                  <div className="space-y-2">
                    <Label htmlFor="patience">Patience</Label>
                    <div className="space-y-2">
                      <Slider
                        value={[config.patience]}
                        onValueChange={([value]) => updateConfig({ patience: value })}
                        max={10}
                        min={1}
                        step={1}
                      />
                      <div className="text-sm text-muted-foreground text-center">
                        {config.patience} epochs
                      </div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="min-delta">Min Delta</Label>
                    <div className="space-y-2">
                      <Slider
                        value={[config.minDelta * 1000]}
                        onValueChange={([value]) => updateConfig({ minDelta: value / 1000 })}
                        max={10}
                        min={0.1}
                        step={0.1}
                      />
                      <div className="text-sm text-muted-foreground text-center">
                        {config.minDelta.toFixed(3)}
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {isAdvancedMode && (
                <div className="space-y-4 p-4 border rounded-lg">
                  <h4 className="font-medium">Advanced Parameters</h4>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="warmup-steps">Warmup Steps</Label>
                      <Input
                        id="warmup-steps"
                        type="number"
                        value={config.warmupSteps}
                        onChange={(e) => updateConfig({ warmupSteps: parseInt(e.target.value) || 0 })}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="weight-decay">Weight Decay</Label>
                      <Input
                        id="weight-decay"
                        type="number"
                        step="0.001"
                        value={config.weightDecay}
                        onChange={(e) => updateConfig({ weightDecay: parseFloat(e.target.value) || 0 })}
                      />
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Save Button */}
        <div className="flex justify-end">
          <Button onClick={handleSave} className="flex items-center gap-2">
            <CheckCircle2 className="w-4 h-4" />
            Save Configuration
          </Button>
        </div>
      </Tabs>
    </div>
  );
};
