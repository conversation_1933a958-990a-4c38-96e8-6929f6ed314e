"""
Application configuration module.

This module centralizes configuration settings for the application,
including paths, environment variables, and other settings.
It uses Pydantic for configuration validation and provides
secure defaults for sensitive settings.
"""
import os
import logging
import secrets
from pathlib import Path
from typing import List, Dict, Any, Optional

from pydantic import BaseModel, Field, field_validator, SecretStr
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# --- Environment ---
# Debug mode (affects cookie security, error details, etc.)
DEBUG = os.getenv("DEBUG", "True").lower() == "true"

# --- Core Paths ---
# Use relative paths suitable for the application context
UPLOAD_DIR = Path("./temp_uploads")
HF_MODELS_DIR = Path("./saved_hf_models")
MODEL_ARTIFACTS_DIR = Path("./model_artifacts")

# Ensure directories exist
UPLOAD_DIR.mkdir(parents=True, exist_ok=True)
HF_MODELS_DIR.mkdir(parents=True, exist_ok=True)
MODEL_ARTIFACTS_DIR.mkdir(parents=True, exist_ok=True)

# --- CORS Settings ---
# When using credentials, CORS_ORIGINS must be specific URLs, not wildcards
CORS_ORIGINS = [
    "http://localhost:5174",  # Frontend dev server
    "http://localhost:4174",  # Frontend preview server
    "http://127.0.0.1:5174",
    "http://127.0.0.1:4174",
    # Add production URLs as needed
]
CORS_ALLOW_CREDENTIALS = True  # Required for cookies
CORS_ALLOW_METHODS = ["GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"]
CORS_ALLOW_HEADERS = ["Content-Type", "Authorization", "X-Requested-With"]

# --- HF Training Defaults ---
DEFAULT_VALIDATION_SPLIT = 0.15
DEFAULT_HF_THRESHOLD = 0.5
HF_RULE_COLUMNS = ['Label', 'Keywords', 'Confidence Threshold']
DEFAULT_HF_BASE_MODEL = "bert-base-uncased"
DEFAULT_HF_EPOCHS = 3

# --- Hierarchy Definition ---
# Default hierarchy levels - can be overridden by user configurations
DEFAULT_HIERARCHY_LEVELS = ["Level_1", "Level_2", "Level_3", "Level_4"]
# Default confidence threshold for all hierarchy levels
DEFAULT_HIERARCHY_CONFIDENCE_THRESHOLD = 0.5
MAX_HIERARCHY_LEVELS = 10

# Modern hierarchy configuration
DEFAULT_HIERARCHY_CONFIDENCE_THRESHOLDS = {
    "Level_1": 0.5,
    "Level_2": 0.5,
    "Level_3": 0.5,
    "Level_4": 0.5
}

# --- API Defaults ---
DEFAULT_OLLAMA_ENDPOINT = os.getenv("OLLAMA_ENDPOINT", "http://localhost:11435")
DEFAULT_GROQ_ENDPOINT = os.getenv("GROQ_ENDPOINT", "https://api.groq.com/openai/v1")
DEFAULT_OPENAI_ENDPOINT = os.getenv("OPENAI_ENDPOINT", "https://api.openai.com/v1")
DEFAULT_GEMINI_ENDPOINT = os.getenv("GEMINI_ENDPOINT", "https://generativelanguage.googleapis.com")
DEFAULT_OPENROUTER_ENDPOINT = os.getenv("OPENROUTER_ENDPOINT", "https://openrouter.ai/api/v1")

# --- Model Defaults ---
DEFAULT_GROQ_MODEL = "llama3-70b-8192"
DEFAULT_OLLAMA_MODEL = "llama3:latest"
DEFAULT_OPENAI_MODEL = "gpt-3.5-turbo"
DEFAULT_GEMINI_MODEL = "gemini-pro"
DEFAULT_OPENROUTER_MODEL = "openai/gpt-3.5-turbo"

# --- Classification Defaults ---
DEFAULT_LLM_TEMPERATURE = 0.1

# --- UI Defaults ---
DEFAULT_LLM_SAMPLE_SIZE = 200
MIN_LLM_SAMPLE_SIZE = 50
MAX_LLM_SAMPLE_SIZE = 1000

# --- Provider List ---
SUPPORTED_PROVIDERS = ["Groq", "Ollama", "OpenAI", "Gemini", "Openrouter"]

# --- Database Configuration ---
# Default to SQLite, but when using PostgreSQL, use port 5433 to avoid conflicts
DATABASE_URL = os.getenv("DATABASE_URL", "sqlite:///../classyweb.db") # Adjusted relative path
DATABASE_ECHO = os.getenv("DATABASE_ECHO", "False").lower() == "true"

# --- Authentication Configuration ---
# Generate a secure random key if not provided in environment
# This is more secure than a hardcoded default
if not os.getenv("JWT_SECRET_KEY"):
    logger.warning("JWT_SECRET_KEY not found in environment. Generating a random key for this session.")
    logger.warning("For production, set a permanent JWT_SECRET_KEY in your environment variables.")
    JWT_SECRET_KEY = secrets.token_hex(32)  # 256-bit random key
else:
    JWT_SECRET_KEY = os.getenv("JWT_SECRET_KEY")

# JWT configuration
JWT_ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = int(os.getenv("ACCESS_TOKEN_EXPIRE_MINUTES", "240"))  # 4 hours
REFRESH_TOKEN_EXPIRE_DAYS = int(os.getenv("REFRESH_TOKEN_EXPIRE_DAYS", "7"))  # 7 days
JWT_TOKEN_LEEWAY_SECONDS = int(os.getenv("JWT_TOKEN_LEEWAY_SECONDS", "30"))  # Leeway for clock skew

# --- Redis Configuration for Token Blacklisting ---
REDIS_URL = os.getenv("REDIS_URL", "redis://localhost:6380/0")
REDIS_TOKEN_BLACKLIST_PREFIX = "blacklist:"

# Google OAuth settings
GOOGLE_CLIENT_ID = os.getenv("GOOGLE_CLIENT_ID", "")
GOOGLE_CLIENT_SECRET = os.getenv("GOOGLE_CLIENT_SECRET", "")
GOOGLE_REDIRECT_URI = os.getenv("GOOGLE_REDIRECT_URI", "http://localhost:5174/auth/google/callback")

# Email verification/reset tokens
VERIFICATION_TOKEN_EXPIRE_HOURS = 48
RESET_TOKEN_EXPIRE_HOURS = 1

# Email settings
EMAIL_ENABLED = os.getenv("EMAIL_ENABLED", "False").lower() == "true"
EMAIL_SENDER = os.getenv("EMAIL_SENDER", "<EMAIL>")
SMTP_SERVER = os.getenv("SMTP_SERVER", "smtp.gmail.com")
SMTP_PORT = int(os.getenv("SMTP_PORT", "587"))
SMTP_USERNAME = os.getenv("SMTP_USERNAME", "")
SMTP_PASSWORD = os.getenv("SMTP_PASSWORD", "")
SMTP_TLS = os.getenv("SMTP_TLS", "True").lower() == "true"

# --- Batch Processing Settings ---
LLM_BATCH_SIZE = 10
LLM_MAX_CONCURRENCY = 2
HF_BATCH_SIZE = 100

# --- Retry Settings ---
MAX_RETRY_ATTEMPTS = 3
RETRY_DELAY_SECONDS = 2.0
RETRY_BACKOFF_FACTOR = 2.0

# --- Cache Settings ---
CACHE_TTL_SECONDS = 300  # 5 minutes

# --- Log Configuration ---
logger.info(f"Temporary upload directory: {UPLOAD_DIR.resolve()}")
logger.info(f"Saved HF models directory: {HF_MODELS_DIR.resolve()}")
logger.info(f"Model artifacts directory: {MODEL_ARTIFACTS_DIR.resolve()}")
logger.info(f"Database URL: {DATABASE_URL}") # Log DB URL
logger.info(f"Database Echo: {DATABASE_ECHO}")

# --- Config Object ---
class Config:
    """Configuration class that provides access to all config variables."""

    def __init__(self):
        # Copy all module-level variables to this object
        for key, value in globals().items():
            # Only copy uppercase variables and logger
            if key.isupper() or key == 'logger':
                setattr(self, key, value)

# Create an instance of the Config class to be imported by other modules
config = Config()

# Log final config values (optional, for debugging)
# logger.info(f"Final config loaded: {vars(config)}")
