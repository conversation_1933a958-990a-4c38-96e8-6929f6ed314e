/**
 * EnhancedWorkflowSelector.tsx
 * 
 * Enhanced workflow selector component for Phase 4 implementation
 * Uses dynamic workflow routing and provides smart recommendations
 */

import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import { 
  workflowRouter, 
  WORKFLOW_ROUTES, 
  ClassificationType, 
  UserExperienceLevel 
} from '@/services/workflowRouter';
import { workflowProgressManager } from '@/services/workflowProgressManager';
import { WorkflowResumeDialog } from './WorkflowResumeDialog';
import { 
  Brain, 
  GraduationCap, 
  Settings, 
  Clock, 
  CheckCircle2, 
  ArrowRight,
  Lightbulb,
  Play,
  RotateCcw
} from 'lucide-react';

interface EnhancedWorkflowSelectorProps {
  onWorkflowSelect?: (type: ClassificationType, level: UserExperienceLevel) => void;
  showResumeOption?: boolean;
  className?: string;
}

export const EnhancedWorkflowSelector: React.FC<EnhancedWorkflowSelectorProps> = ({
  onWorkflowSelect,
  showResumeOption = true,
  className = ""
}) => {
  const navigate = useNavigate();
  const [selectedType, setSelectedType] = useState<ClassificationType | null>(null);
  const [selectedLevel, setSelectedLevel] = useState<UserExperienceLevel | null>(null);
  const [showRecommendations, setShowRecommendations] = useState(true);
  const [resumableWorkflows, setResumableWorkflows] = useState(0);

  useEffect(() => {
    if (showResumeOption) {
      const workflows = workflowProgressManager.getResumableWorkflows();
      setResumableWorkflows(workflows.filter(w => w.canResume).length);
    }
  }, [showResumeOption]);

  const classificationTypes: Array<{
    type: ClassificationType;
    title: string;
    description: string;
    icon: React.ComponentType<any>;
    complexity: 'Simple' | 'Moderate' | 'Complex';
    useCase: string;
  }> = [
    {
      type: 'binary',
      title: 'Binary Classification',
      description: 'Classify into two categories (Yes/No, Positive/Negative)',
      icon: Brain,
      complexity: 'Simple',
      useCase: 'Sentiment analysis, spam detection'
    },
    {
      type: 'multiclass',
      title: 'Multi-Class Classification',
      description: 'Classify into multiple mutually exclusive categories',
      icon: Brain,
      complexity: 'Moderate',
      useCase: 'Topic classification, image recognition'
    },
    {
      type: 'multilabel',
      title: 'Multi-Label Classification',
      description: 'Assign multiple labels to each instance',
      icon: Brain,
      complexity: 'Complex',
      useCase: 'Tag assignment, medical diagnosis'
    },
    {
      type: 'hierarchical',
      title: 'Hierarchical Classification',
      description: 'Classify into hierarchically structured categories',
      icon: Brain,
      complexity: 'Complex',
      useCase: 'Product categorization, document classification'
    },
    {
      type: 'flat',
      title: 'Flat Classification',
      description: 'General-purpose classification with smart detection',
      icon: Brain,
      complexity: 'Simple',
      useCase: 'Auto-detect best approach for your data'
    }
  ];

  const experienceLevels: Array<{
    level: UserExperienceLevel;
    title: string;
    description: string;
    icon: React.ComponentType<any>;
    features: string[];
  }> = [
    {
      level: 'beginner',
      title: 'Beginner (Guided)',
      description: 'Step-by-step guidance with smart recommendations',
      icon: GraduationCap,
      features: ['Smart detection', 'Auto-configuration', 'Real-time guidance', 'Best practices']
    },
    {
      level: 'expert',
      title: 'Expert (Advanced)',
      description: 'Full control with advanced features and customization',
      icon: Settings,
      features: ['Custom architectures', 'Hyperparameter tuning', 'Advanced metrics', 'Model comparison']
    }
  ];

  const getRecommendation = () => {
    // Simple recommendation logic - in a real app this would be more sophisticated
    const recommendation = workflowRouter.getRecommendedWorkflow('beginner', 'simple', 'quick');
    return {
      type: recommendation.classificationType,
      level: recommendation.experienceLevel,
      reason: 'Based on typical first-time user patterns'
    };
  };

  const handleWorkflowStart = () => {
    if (!selectedType || !selectedLevel) return;

    const success = workflowRouter.navigateToWorkflow(
      navigate,
      selectedType,
      selectedLevel,
      {
        step: 1,
        config: {},
        resumeData: {}
      }
    );

    if (success) {
      onWorkflowSelect?.(selectedType, selectedLevel);
    }
  };

  const getComplexityColor = (complexity: string) => {
    switch (complexity) {
      case 'Simple':
        return 'text-green-600 bg-green-50 border-green-200';
      case 'Moderate':
        return 'text-blue-600 bg-blue-50 border-blue-200';
      case 'Complex':
        return 'text-orange-600 bg-orange-50 border-orange-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const recommendation = getRecommendation();

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Resume Workflows */}
      {showResumeOption && resumableWorkflows > 0 && (
        <Alert className="border-blue-200 bg-blue-50">
          <Play className="w-4 h-4" />
          <AlertDescription className="flex items-center justify-between">
            <span>
              You have {resumableWorkflows} incomplete workflow{resumableWorkflows !== 1 ? 's' : ''}. 
              Resume where you left off or start fresh.
            </span>
            <WorkflowResumeDialog
              trigger={
                <Button size="sm" className="ml-4">
                  <RotateCcw className="w-4 h-4 mr-1" />
                  Resume
                </Button>
              }
            />
          </AlertDescription>
        </Alert>
      )}

      {/* Smart Recommendation */}
      {showRecommendations && (
        <Card className="border-yellow-200 bg-yellow-50">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center gap-2">
              <Lightbulb className="w-5 h-5 text-yellow-600" />
              Smart Recommendation
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm mb-2">
                  We recommend starting with <strong>{recommendation.type} classification</strong> using the{' '}
                  <strong>{recommendation.level} workflow</strong>.
                </p>
                <p className="text-xs text-muted-foreground">{recommendation.reason}</p>
              </div>
              <Button
                size="sm"
                onClick={() => {
                  setSelectedType(recommendation.type);
                  setSelectedLevel(recommendation.level);
                }}
              >
                Use Recommendation
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Classification Type Selection */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">1. Choose Classification Type</h3>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {classificationTypes.map((type) => (
            <Card
              key={type.type}
              className={`cursor-pointer transition-colors hover:bg-muted/50 ${
                selectedType === type.type ? 'ring-2 ring-blue-500 bg-blue-50' : ''
              }`}
              onClick={() => setSelectedType(type.type)}
            >
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <type.icon className="w-6 h-6 text-blue-600" />
                  <Badge className={getComplexityColor(type.complexity)}>
                    {type.complexity}
                  </Badge>
                </div>
                <CardTitle className="text-base">{type.title}</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground mb-3">
                  {type.description}
                </p>
                <div className="text-xs text-muted-foreground">
                  <strong>Use case:</strong> {type.useCase}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Experience Level Selection */}
      {selectedType && (
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">2. Choose Experience Level</h3>
          <div className="grid gap-4 md:grid-cols-2">
            {experienceLevels.map((level) => {
              const config = WORKFLOW_ROUTES[`${level.level}-${selectedType}`];
              return (
                <Card
                  key={level.level}
                  className={`cursor-pointer transition-colors hover:bg-muted/50 ${
                    selectedLevel === level.level ? 'ring-2 ring-blue-500 bg-blue-50' : ''
                  }`}
                  onClick={() => setSelectedLevel(level.level)}
                >
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <level.icon className="w-6 h-6 text-blue-600" />
                      {config && (
                        <div className="flex items-center gap-1 text-xs text-muted-foreground">
                          <Clock className="w-3 h-3" />
                          {config.estimatedTime}
                        </div>
                      )}
                    </div>
                    <CardTitle className="text-base">{level.title}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm text-muted-foreground mb-3">
                      {level.description}
                    </p>
                    <div className="space-y-2">
                      <p className="text-xs font-medium">Features:</p>
                      <div className="flex flex-wrap gap-1">
                        {level.features.map((feature, index) => (
                          <Badge key={index} variant="outline" className="text-xs">
                            {feature}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </div>
      )}

      {/* Start Workflow Button */}
      {selectedType && selectedLevel && (
        <div className="flex justify-center pt-6">
          <Button
            size="lg"
            onClick={handleWorkflowStart}
            className="px-8"
          >
            <Play className="w-5 h-5 mr-2" />
            Start {selectedType} Classification ({selectedLevel})
            <ArrowRight className="w-5 h-5 ml-2" />
          </Button>
        </div>
      )}

      {/* Progress Indicator */}
      {(selectedType || selectedLevel) && (
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>Setup Progress</span>
            <span>{selectedType && selectedLevel ? '100%' : selectedType ? '50%' : '0%'}</span>
          </div>
          <Progress 
            value={selectedType && selectedLevel ? 100 : selectedType ? 50 : 0} 
            className="h-2" 
          />
        </div>
      )}
    </div>
  );
};
