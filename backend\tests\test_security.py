"""
Security tests for ClassyWeb ML Platform.

Tests the security enhancements implemented during refactoring.
"""
import pytest
import tempfile
import os
from fastapi.testclient import TestClient
from unittest.mock import patch, MagicMock

from app.main import app
from app.security_middleware import SecurityMiddleware, sanitize_input, validate_json_input
from app.error_handlers import ValidationException, AuthenticationException

client = TestClient(app)

class TestSecurityMiddleware:
    """Test security middleware functionality."""
    
    def test_rate_limiting(self):
        """Test rate limiting functionality."""
        # This would require a more sophisticated test setup
        # For now, test the basic structure
        middleware = SecurityMiddleware(app, rate_limit_requests=5, rate_limit_window=60)
        assert middleware.rate_limit_requests == 5
        assert middleware.rate_limit_window == 60
    
    def test_input_sanitization(self):
        """Test input sanitization."""
        # Test XSS prevention
        malicious_input = "<script>alert('xss')</script>"
        sanitized = sanitize_input(malicious_input)
        assert "<script>" not in sanitized
        assert "&lt;script&gt;" in sanitized
        
        # Test SQL injection prevention
        sql_injection = "'; DROP TABLE users; --"
        sanitized = sanitize_input(sql_injection)
        assert sanitized == "'; DROP TABLE users; --"  # Should be escaped
    
    def test_json_validation(self):
        """Test JSON input validation."""
        # Test deep nesting
        deep_json = {"level1": {"level2": {"level3": {"level4": {"level5": "value"}}}}}
        
        # Should not raise exception for reasonable depth
        validate_json_input(deep_json, max_depth=10)
        
        # Should raise exception for excessive depth
        with pytest.raises(Exception):
            validate_json_input(deep_json, max_depth=3)
        
        # Test too many keys
        large_json = {f"key_{i}": f"value_{i}" for i in range(1500)}
        with pytest.raises(Exception):
            validate_json_input(large_json, max_keys=1000)


class TestFileUploadSecurity:
    """Test file upload security measures."""
    
    def test_file_type_validation(self):
        """Test file type validation."""
        # Test allowed file types
        with tempfile.NamedTemporaryFile(suffix='.csv', delete=False) as tmp:
            tmp.write(b"test,data\n1,2\n")
            tmp.flush()
            
            with open(tmp.name, 'rb') as f:
                response = client.post(
                    "/files/upload",
                    files={"file": ("test.csv", f, "text/csv")}
                )
                # Should succeed (assuming authentication is mocked)
                assert response.status_code in [200, 401]  # 401 if not authenticated
        
        os.unlink(tmp.name)
    
    def test_file_size_validation(self):
        """Test file size validation."""
        # Create a large file (simulated)
        large_content = b"x" * (101 * 1024 * 1024)  # 101MB
        
        with tempfile.NamedTemporaryFile(suffix='.csv', delete=False) as tmp:
            tmp.write(large_content[:1000])  # Write small amount for test
            tmp.flush()
            
            # Mock the file size check
            with patch('app.api.files.MAX_FILE_SIZE', 1000):
                with open(tmp.name, 'rb') as f:
                    response = client.post(
                        "/files/upload",
                        files={"file": ("large.csv", f, "text/csv")}
                    )
                    # Should fail due to size limit
                    assert response.status_code in [400, 401]
        
        os.unlink(tmp.name)
    
    def test_malicious_filename(self):
        """Test protection against malicious filenames."""
        malicious_filenames = [
            "../../../etc/passwd",
            "..\\..\\windows\\system32\\config\\sam",
            "test<script>alert('xss')</script>.csv",
            "test|rm -rf /.csv"
        ]
        
        for filename in malicious_filenames:
            with tempfile.NamedTemporaryFile(suffix='.csv', delete=False) as tmp:
                tmp.write(b"test,data\n1,2\n")
                tmp.flush()
                
                with open(tmp.name, 'rb') as f:
                    response = client.post(
                        "/files/upload",
                        files={"file": (filename, f, "text/csv")}
                    )
                    # Should either reject or sanitize the filename
                    assert response.status_code in [400, 401]
            
            os.unlink(tmp.name)


class TestAuthenticationSecurity:
    """Test authentication security measures."""
    
    def test_invalid_token(self):
        """Test handling of invalid tokens."""
        response = client.get(
            "/files/",
            headers={"Authorization": "Bearer invalid_token"}
        )
        assert response.status_code == 401
    
    def test_missing_token(self):
        """Test handling of missing authentication."""
        response = client.get("/files/")
        # Should either allow (if endpoint is public) or reject
        assert response.status_code in [200, 401]
    
    def test_expired_token(self):
        """Test handling of expired tokens."""
        # This would require generating an expired token
        # For now, test the structure
        expired_token = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**********************************************************.invalid"
        
        response = client.get(
            "/files/",
            headers={"Authorization": f"Bearer {expired_token}"}
        )
        assert response.status_code == 401


class TestErrorHandling:
    """Test error handling security."""
    
    def test_error_information_disclosure(self):
        """Test that errors don't disclose sensitive information."""
        # Test database error handling
        response = client.get("/files/nonexistent-id")
        assert response.status_code == 404
        
        # Check that response doesn't contain sensitive info
        response_text = response.text.lower()
        assert "database" not in response_text
        assert "sql" not in response_text
        assert "traceback" not in response_text
    
    def test_validation_error_handling(self):
        """Test validation error responses."""
        # Send invalid JSON
        response = client.post(
            "/files/upload",
            json={"invalid": "data"},
            headers={"Content-Type": "application/json"}
        )
        
        # Should return proper validation error
        assert response.status_code in [400, 422]


class TestAPISecurityHeaders:
    """Test security headers in API responses."""
    
    def test_security_headers_present(self):
        """Test that security headers are present in responses."""
        response = client.get("/")
        
        # Check for important security headers
        expected_headers = [
            "x-content-type-options",
            "x-frame-options",
            "x-xss-protection",
            "strict-transport-security"
        ]
        
        for header in expected_headers:
            assert header in response.headers, f"Missing security header: {header}"
    
    def test_cors_configuration(self):
        """Test CORS configuration."""
        response = client.options("/")
        
        # Should have proper CORS headers
        assert "access-control-allow-origin" in response.headers


class TestInputValidation:
    """Test input validation across endpoints."""
    
    def test_sql_injection_prevention(self):
        """Test SQL injection prevention."""
        sql_payloads = [
            "'; DROP TABLE users; --",
            "1' OR '1'='1",
            "admin'/*",
            "1; DELETE FROM files; --"
        ]
        
        for payload in sql_payloads:
            # Test in query parameters
            response = client.get(f"/files/?search={payload}")
            # Should not cause server error
            assert response.status_code != 500
    
    def test_xss_prevention(self):
        """Test XSS prevention."""
        xss_payloads = [
            "<script>alert('xss')</script>",
            "javascript:alert('xss')",
            "<img src=x onerror=alert('xss')>",
            "eval('alert(1)')"
        ]
        
        for payload in xss_payloads:
            # Test in various endpoints
            response = client.get(f"/files/?name={payload}")
            # Should not reflect the payload unsanitized
            assert "<script>" not in response.text
            assert "javascript:" not in response.text


# Fixtures for testing
@pytest.fixture
def mock_authenticated_user():
    """Mock an authenticated user for testing."""
    with patch('app.auth.get_current_user') as mock:
        mock.return_value = MagicMock(id=1, email="<EMAIL>")
        yield mock


@pytest.fixture
def sample_csv_file():
    """Create a sample CSV file for testing."""
    with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as tmp:
        tmp.write("text,label\n")
        tmp.write("Hello world,positive\n")
        tmp.write("Bad day,negative\n")
        tmp.flush()
        yield tmp.name
    
    os.unlink(tmp.name)


# Integration tests
class TestSecurityIntegration:
    """Integration tests for security features."""
    
    def test_complete_file_upload_security(self, sample_csv_file, mock_authenticated_user):
        """Test complete file upload security flow."""
        with open(sample_csv_file, 'rb') as f:
            response = client.post(
                "/files/upload",
                files={"file": ("test.csv", f, "text/csv")}
            )
            
            # Should succeed with proper authentication
            assert response.status_code in [200, 201]
    
    def test_rate_limiting_integration(self):
        """Test rate limiting in practice."""
        # Make multiple requests quickly
        responses = []
        for i in range(10):
            response = client.get("/")
            responses.append(response.status_code)
        
        # Should not all be rate limited (depends on configuration)
        assert not all(status == 429 for status in responses)
