"""Restore HFModel HFRule tables add indexes timezone

Revision ID: 0795255204dc
Revises: 003
Create Date: 2025-04-20 23:36:37.295622

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '0795255204dc'
down_revision = '003'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Check if indexes and columns exist before creating/modifying them
    from sqlalchemy.engine.reflection import Inspector
    from sqlalchemy import create_engine
    from app import config

    engine = create_engine(config.DATABASE_URL)
    inspector = Inspector.from_engine(engine)

    # Check if tables exist
    tables = inspector.get_table_names()

    # Create indexes only if they don't exist
    if 'files' in tables:
        indexes = [idx['name'] for idx in inspector.get_indexes('files')]
        if 'ix_files_user_id' not in indexes:
            op.create_index(op.f('ix_files_user_id'), 'files', ['user_id'], unique=False)

    # Add columns to hf_models if the table exists
    if 'hf_models' in tables:
        columns = [col['name'] for col in inspector.get_columns('hf_models')]
        if 'model_data' not in columns:
            op.add_column('hf_models', sa.Column('model_data', sa.Text(), nullable=True))
        if 'tokenizer_data' not in columns:
            op.add_column('hf_models', sa.Column('tokenizer_data', sa.Text(), nullable=True))
        if 'label_map_data' not in columns:
            op.add_column('hf_models', sa.Column('label_map_data', sa.JSON(), nullable=True))
        if 'user_id' not in columns:
            op.add_column('hf_models', sa.Column('user_id', sa.Integer(), nullable=True))

        # Create index and foreign key if they don't exist
        indexes = [idx['name'] for idx in inspector.get_indexes('hf_models')]
        if 'ix_hf_models_user_id' not in indexes:
            op.create_index(op.f('ix_hf_models_user_id'), 'hf_models', ['user_id'], unique=False)

        # Create foreign key - this is harder to check, so we'll try/except
        try:
            op.create_foreign_key(None, 'hf_models', 'users', ['user_id'], ['id'])
        except Exception as e:
            print(f"Note: Foreign key creation skipped: {e}")

        # Drop model_path if it exists
        if 'model_path' in columns:
            op.drop_column('hf_models', 'model_path')

    # Create other indexes if their tables exist
    if 'hf_rules' in tables:
        indexes = [idx['name'] for idx in inspector.get_indexes('hf_rules')]
        if 'ix_hf_rules_model_id' not in indexes:
            op.create_index(op.f('ix_hf_rules_model_id'), 'hf_rules', ['model_id'], unique=False)

    if 'hierarchy_configs' in tables:
        indexes = [idx['name'] for idx in inspector.get_indexes('hierarchy_configs')]
        if 'ix_hierarchy_configs_is_default' not in indexes:
            op.create_index(op.f('ix_hierarchy_configs_is_default'), 'hierarchy_configs', ['is_default'], unique=False)
        if 'ix_hierarchy_configs_user_id' not in indexes:
            op.create_index(op.f('ix_hierarchy_configs_user_id'), 'hierarchy_configs', ['user_id'], unique=False)

    if 'tasks' in tables:
        indexes = [idx['name'] for idx in inspector.get_indexes('tasks')]
        if 'ix_tasks_status' not in indexes:
            op.create_index(op.f('ix_tasks_status'), 'tasks', ['status'], unique=False)
        if 'ix_tasks_user_id' not in indexes:
            op.create_index(op.f('ix_tasks_user_id'), 'tasks', ['user_id'], unique=False)

    # Manually added timezone changes
    with op.batch_alter_table('files', schema=None) as batch_op:
        batch_op.alter_column('created_at',
               existing_type=sa.DATETIME(),
               type_=sa.DateTime(timezone=True),
               existing_nullable=True)
        batch_op.alter_column('updated_at',
               existing_type=sa.DATETIME(),
               type_=sa.DateTime(timezone=True),
               existing_nullable=True)

    with op.batch_alter_table('hf_models', schema=None) as batch_op:
        batch_op.alter_column('created_at',
               existing_type=sa.DATETIME(),
               type_=sa.DateTime(timezone=True),
               existing_nullable=True)
        batch_op.alter_column('updated_at',
               existing_type=sa.DATETIME(),
               type_=sa.DateTime(timezone=True),
               existing_nullable=True)

    with op.batch_alter_table('hf_rules', schema=None) as batch_op:
        batch_op.alter_column('created_at',
               existing_type=sa.DATETIME(),
               type_=sa.DateTime(timezone=True),
               existing_nullable=True)
        batch_op.alter_column('updated_at',
               existing_type=sa.DATETIME(),
               type_=sa.DateTime(timezone=True),
               existing_nullable=True)

    with op.batch_alter_table('hierarchy_configs', schema=None) as batch_op:
        batch_op.alter_column('created_at',
               existing_type=sa.DATETIME(),
               type_=sa.DateTime(timezone=True),
               existing_nullable=True)
        batch_op.alter_column('updated_at',
               existing_type=sa.DATETIME(),
               type_=sa.DateTime(timezone=True),
               existing_nullable=True)

    with op.batch_alter_table('tasks', schema=None) as batch_op:
        batch_op.alter_column('created_at',
               existing_type=sa.DATETIME(),
               type_=sa.DateTime(timezone=True),
               existing_nullable=True)
        batch_op.alter_column('updated_at',
               existing_type=sa.DATETIME(),
               type_=sa.DateTime(timezone=True),
               existing_nullable=True)
        batch_op.alter_column('completed_at',
               existing_type=sa.DATETIME(),
               type_=sa.DateTime(timezone=True),
               existing_nullable=True)

    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_tasks_user_id'), table_name='tasks')
    op.drop_index(op.f('ix_tasks_status'), table_name='tasks')
    op.drop_index(op.f('ix_hierarchy_configs_user_id'), table_name='hierarchy_configs')
    op.drop_index(op.f('ix_hierarchy_configs_is_default'), table_name='hierarchy_configs')
    op.drop_index(op.f('ix_hf_rules_model_id'), table_name='hf_rules')
    op.add_column('hf_models', sa.Column('model_path', sa.VARCHAR(length=255), nullable=True))
    op.drop_constraint(None, 'hf_models', type_='foreignkey')
    op.drop_index(op.f('ix_hf_models_user_id'), table_name='hf_models')
    op.drop_column('hf_models', 'user_id')
    op.drop_column('hf_models', 'label_map_data')
    op.drop_column('hf_models', 'tokenizer_data')
    op.drop_column('hf_models', 'model_data')
    op.drop_index(op.f('ix_files_user_id'), table_name='files')

    # Manually added timezone changes (downgrade)
    with op.batch_alter_table('tasks', schema=None) as batch_op:
        batch_op.alter_column('completed_at',
               existing_type=sa.DateTime(timezone=True),
               type_=sa.DATETIME(),
               existing_nullable=True)
        batch_op.alter_column('updated_at',
               existing_type=sa.DateTime(timezone=True),
               type_=sa.DATETIME(),
               existing_nullable=True)
        batch_op.alter_column('created_at',
               existing_type=sa.DateTime(timezone=True),
               type_=sa.DATETIME(),
               existing_nullable=True)

    with op.batch_alter_table('hierarchy_configs', schema=None) as batch_op:
        batch_op.alter_column('updated_at',
               existing_type=sa.DateTime(timezone=True),
               type_=sa.DATETIME(),
               existing_nullable=True)
        batch_op.alter_column('created_at',
               existing_type=sa.DateTime(timezone=True),
               type_=sa.DATETIME(),
               existing_nullable=True)

    with op.batch_alter_table('hf_rules', schema=None) as batch_op:
        batch_op.alter_column('updated_at',
               existing_type=sa.DateTime(timezone=True),
               type_=sa.DATETIME(),
               existing_nullable=True)
        batch_op.alter_column('created_at',
               existing_type=sa.DateTime(timezone=True),
               type_=sa.DATETIME(),
               existing_nullable=True)

    with op.batch_alter_table('hf_models', schema=None) as batch_op:
        batch_op.alter_column('updated_at',
               existing_type=sa.DateTime(timezone=True),
               type_=sa.DATETIME(),
               existing_nullable=True)
        batch_op.alter_column('created_at',
               existing_type=sa.DateTime(timezone=True),
               type_=sa.DATETIME(),
               existing_nullable=True)

    with op.batch_alter_table('files', schema=None) as batch_op:
        batch_op.alter_column('updated_at',
               existing_type=sa.DateTime(timezone=True),
               type_=sa.DATETIME(),
               existing_nullable=True)
        batch_op.alter_column('created_at',
               existing_type=sa.DateTime(timezone=True),
               type_=sa.DATETIME(),
               existing_nullable=True)

    # ### end Alembic commands ###
