#!/usr/bin/env python3
"""
Database migration script to add missing model_name column to model_performance table.
"""

import sqlite3
import os
import sys
from pathlib import Path

# Add the backend directory to the Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

def migrate_model_performance_table():
    """Add model_name column to model_performance table if it doesn't exist."""
    
    # Database path
    db_path = backend_dir / "classyweb.db"
    
    if not db_path.exists():
        print(f"Database not found at {db_path}")
        return False
    
    try:
        # Connect to the database
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()
        
        # Check if model_name column exists
        cursor.execute("PRAGMA table_info(model_performance)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'model_name' not in columns:
            print("Adding model_name column to model_performance table...")
            
            # Add the missing column
            cursor.execute("""
                ALTER TABLE model_performance 
                ADD COLUMN model_name VARCHAR(100) DEFAULT 'Unknown Model'
            """)
            
            # Update existing records to have a default model name
            cursor.execute("""
                UPDATE model_performance 
                SET model_name = 'Legacy Model - ' || substr(model_id, 1, 8)
                WHERE model_name = 'Unknown Model' OR model_name IS NULL
            """)
            
            conn.commit()
            print("✅ Successfully added model_name column and updated existing records")
        else:
            print("✅ model_name column already exists")
        
        # Verify the migration
        cursor.execute("SELECT COUNT(*) FROM model_performance")
        count = cursor.fetchone()[0]
        print(f"📊 model_performance table has {count} records")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Migration failed: {e}")
        if 'conn' in locals():
            conn.rollback()
            conn.close()
        return False

if __name__ == "__main__":
    print("🔄 Starting model_performance table migration...")
    success = migrate_model_performance_table()
    
    if success:
        print("✅ Migration completed successfully!")
        sys.exit(0)
    else:
        print("❌ Migration failed!")
        sys.exit(1)
