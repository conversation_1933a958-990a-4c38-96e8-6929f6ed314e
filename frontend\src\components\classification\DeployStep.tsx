/**
 * DeployStep.tsx
 * 
 * Final deployment step for hierarchical classification workflow
 * Provides comprehensive deployment options including local export, API generation,
 * batch processing, and integration code examples
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Switch } from "@/components/ui/switch";
import { Separator } from "@/components/ui/separator";
import {
  Download,
  Cloud,
  Server,
  Code,
  Database,
  Shield,
  Monitor,
  Zap,
  Copy,
  CheckCircle2,
  AlertCircle,
  ExternalLink,
  Package,
  Rocket,
  Settings,
  Key,
  Globe,
  Smartphone,
  HardDrive,
  FileText,
  Play,
  Pause,
  RotateCcw,
  ArrowLeft
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { deployModel, exportModel, getIntegrationCode } from "@/services/deploymentApi";

interface DeployStepProps {
  // Model information
  modelId?: string;
  modelName?: string;
  modelPath?: string;

  // Classification type
  classificationType?: 'hierarchical' | 'multi-label' | 'binary' | 'multi-class' | 'flat';

  // Training results
  trainingMetrics?: {
    accuracy?: number;
    f1_score?: number;
    f1_macro?: number;
    f1_micro?: number;
    precision?: number;
    recall?: number;
    hamming_loss?: number;
    jaccard_score?: number;
    subset_accuracy?: number;
    training_time?: number;
    model_size_mb?: number;
  };

  // Hierarchy configuration (for hierarchical)
  hierarchyLevels?: Array<{
    name: string;
    column: string;
    order: number;
  }>;

  // Multi-label configuration
  labels?: string[];
  labelThresholds?: Record<string, number>;
  labelCorrelations?: Record<string, string[]>;

  // User license information
  userLicense?: {
    type: 'personal' | 'professional' | 'enterprise';
    features: string[];
    limits: {
      max_deployments?: number;
      max_api_calls_per_month?: number;
      cloud_deployment?: boolean;
      enterprise_features?: boolean;
    };
  };

  // Callbacks
  onComplete?: (deploymentInfo: any) => void;
  onBack?: () => void;
}

interface DeploymentOption {
  id: string;
  title: string;
  description: string;
  icon: React.ComponentType<any>;
  available: boolean;
  requiresLicense?: 'professional' | 'enterprise';
  estimatedTime?: string;
}

export const DeployStep: React.FC<DeployStepProps> = ({
  modelId,
  modelName = "Classification Model",
  modelPath,
  classificationType = 'hierarchical',
  trainingMetrics = {},
  hierarchyLevels = [],
  labels = [],
  labelThresholds = {},
  labelCorrelations = {},
  userLicense = { type: 'personal', features: [], limits: {} },
  onComplete,
  onBack
}) => {
  const { toast } = useToast();
  
  // State management
  const [selectedDeployment, setSelectedDeployment] = useState<string>('local');
  const [isDeploying, setIsDeploying] = useState(false);
  const [deploymentProgress, setDeploymentProgress] = useState(0);
  const [deploymentStatus, setDeploymentStatus] = useState<'idle' | 'preparing' | 'deploying' | 'complete' | 'error'>('idle');
  const [deploymentResult, setDeploymentResult] = useState<any>(null);
  const [copiedCode, setCopiedCode] = useState<string>('');
  
  // Configuration state
  const [apiConfig, setApiConfig] = useState({
    name: modelName.replace(/\s+/g, '-').toLowerCase(),
    description: `${classificationType.charAt(0).toUpperCase() + classificationType.slice(1)} classification API for ${modelName}`,
    version: '1.0.0',
    enableAuth: true,
    enableRateLimit: true,
    maxRequestsPerMinute: 100
  });
  
  const [exportConfig, setExportConfig] = useState({
    format: 'pytorch',
    includeTokenizer: true,
    includeMetadata: true,
    compression: 'zip'
  });

  // Define deployment options based on user license
  const deploymentOptions: DeploymentOption[] = [
    {
      id: 'local',
      title: 'Local Model Export',
      description: 'Download trained model files for offline use',
      icon: Download,
      available: true,
      estimatedTime: '2-5 minutes'
    },
    {
      id: 'api',
      title: 'API Endpoint',
      description: 'Generate REST API for real-time inference',
      icon: Server,
      available: userLicense.limits.cloud_deployment !== false,
      requiresLicense: 'professional',
      estimatedTime: '5-10 minutes'
    },
    {
      id: 'batch',
      title: 'Batch Processing',
      description: 'Setup for large-scale batch classification',
      icon: Database,
      available: true,
      estimatedTime: '3-7 minutes'
    },
    {
      id: 'cloud',
      title: 'Cloud Deployment',
      description: 'Deploy to AWS/GCP/Azure with auto-scaling',
      icon: Cloud,
      available: userLicense.limits.cloud_deployment === true,
      requiresLicense: 'enterprise',
      estimatedTime: '10-15 minutes'
    },
    {
      id: 'edge',
      title: 'Edge Deployment',
      description: 'Optimize for mobile and edge devices',
      icon: Smartphone,
      available: userLicense.limits.enterprise_features === true,
      requiresLicense: 'enterprise',
      estimatedTime: '8-12 minutes'
    }
  ];

  // Check if deployment option is available
  const isOptionAvailable = (option: DeploymentOption): boolean => {
    if (!option.available) return false;
    
    if (option.requiresLicense === 'professional' && userLicense.type === 'personal') {
      return false;
    }
    
    if (option.requiresLicense === 'enterprise' && 
        !['enterprise'].includes(userLicense.type)) {
      return false;
    }
    
    return true;
  };

  // Handle deployment initiation
  const handleDeploy = async () => {
    if (!modelId) {
      toast({
        title: "No model available",
        description: "Please complete training first",
        variant: "destructive"
      });
      return;
    }

    setIsDeploying(true);
    setDeploymentStatus('preparing');
    setDeploymentProgress(0);

    try {
      // Prepare deployment configuration
      let deploymentConfig: Record<string, any> = {};

      if (selectedDeployment === 'api') {
        deploymentConfig = {
          ...apiConfig,
          classification_type: classificationType,
          ...(classificationType === 'hierarchical' && { hierarchy_levels: hierarchyLevels }),
          ...(classificationType === 'multi-label' && {
            labels: labels,
            label_thresholds: labelThresholds,
            label_correlations: labelCorrelations
          }),
          ...(classificationType === 'multi-class' && {
            classes: labels,
            strategy: trainingMetrics.strategy || 'softmax',
            confidence_threshold: 0.5,
            return_probabilities: true,
            max_predictions: 1
          })
        };
      } else if (selectedDeployment === 'local') {
        deploymentConfig = {
          ...exportConfig,
          classification_type: classificationType,
          ...(classificationType === 'hierarchical' && { hierarchy_levels: hierarchyLevels }),
          ...(classificationType === 'multi-label' && {
            labels: labels,
            label_thresholds: labelThresholds,
            label_correlations: labelCorrelations
          }),
          ...(classificationType === 'multi-class' && {
            classes: labels,
            strategy: trainingMetrics.strategy || 'softmax',
            include_strategy_metadata: true,
            export_format: exportConfig.format || 'pytorch'
          })
        };
      } else if (selectedDeployment === 'batch') {
        deploymentConfig = {
          classification_type: classificationType,
          batch_size: 32,
          parallel_workers: 4,
          ...(classificationType === 'multi-class' && {
            classes: labels,
            strategy: trainingMetrics.strategy || 'softmax',
            confidence_threshold: 0.5,
            output_format: 'csv'
          })
        };
      }

      // Simulate deployment progress
      const progressSteps = [
        { step: 'Validating model...', progress: 20 },
        { step: 'Preparing deployment package...', progress: 40 },
        { step: 'Configuring deployment environment...', progress: 60 },
        { step: 'Deploying model...', progress: 80 },
        { step: 'Finalizing deployment...', progress: 100 }
      ];

      for (const { step, progress } of progressSteps) {
        setDeploymentProgress(progress);
        await new Promise(resolve => setTimeout(resolve, 800));
      }

      // Call the deployment API
      const result = await deployModel({
        model_id: modelId,
        deployment_type: selectedDeployment,
        config: deploymentConfig,
        name: apiConfig.name,
        description: apiConfig.description
      });

      setDeploymentStatus('complete');
      setDeploymentResult(result);

      toast({
        title: "Deployment successful",
        description: `Your model has been deployed successfully using ${selectedDeployment} method`
      });

    } catch (error: any) {
      setDeploymentStatus('error');
      toast({
        title: "Deployment failed",
        description: error.message || "Failed to deploy model",
        variant: "destructive"
      });
    } finally {
      setIsDeploying(false);
    }
  };

  // Handle code copying
  const handleCopyCode = (code: string, type: string) => {
    navigator.clipboard.writeText(code);
    setCopiedCode(type);
    setTimeout(() => setCopiedCode(''), 2000);
    
    toast({
      title: "Code copied",
      description: `${type} code copied to clipboard`
    });
  };

  // Generate integration code examples
  const generatePythonCode = () => {
    const classificationTypeTitle = classificationType.charAt(0).toUpperCase() + classificationType.slice(1);

    if (classificationType === 'multi-label') {
      return `import requests
import json

# ClassyWeb Multi-Label Classification API
API_ENDPOINT = "${deploymentResult?.endpoint || 'https://api.classyweb.com/models/your-model-id'}"
API_KEY = "your-api-key-here"

def classify_text(text, threshold_override=None):
    """Classify text using your trained multi-label model."""
    headers = {
        "Authorization": f"Bearer {API_KEY}",
        "Content-Type": "application/json"
    }

    payload = {
        "text": text,
        "return_probabilities": True,
        "return_all_labels": True
    }

    if threshold_override:
        payload["threshold_override"] = threshold_override

    response = requests.post(f"{API_ENDPOINT}/classify",
                           headers=headers,
                           json=payload)

    if response.status_code == 200:
        return response.json()
    else:
        raise Exception(f"Classification failed: {response.text}")

# Example usage
result = classify_text("Your text to classify here")
print(f"Predicted labels: {result['predicted_labels']}")
print(f"Label probabilities: {result['label_probabilities']}")
print(f"Active labels: {[label for label, prob in result['label_probabilities'].items() if prob > 0.5]}")`;
    } else {
      return `import requests
import json

# ClassyWeb ${classificationTypeTitle} Classification API
API_ENDPOINT = "${deploymentResult?.endpoint || 'https://api.classyweb.com/models/your-model-id'}"
API_KEY = "your-api-key-here"

def classify_text(text${classificationType === 'hierarchical' ? ', hierarchy_level=None' : ''}):
    """Classify text using your trained ${classificationType} model."""
    headers = {
        "Authorization": f"Bearer {API_KEY}",
        "Content-Type": "application/json"
    }

    payload = {
        "text": text,
        ${classificationType === 'hierarchical' ? '"hierarchy_level": hierarchy_level,' : ''}
        "return_probabilities": True
    }

    response = requests.post(f"{API_ENDPOINT}/classify",
                           headers=headers,
                           json=payload)

    if response.status_code == 200:
        return response.json()
    else:
        raise Exception(f"Classification failed: {response.text}")

# Example usage
result = classify_text("Your text to classify here")
print(f"Classification: {result['prediction']}")
print(f"Confidence: {result['confidence']:.2f}")
${classificationType === 'hierarchical' ? 'print(f"Hierarchy path: {\' > \'.join(result[\'hierarchy_path\'])}")' : ''}`;
    }
  };

  const generateCurlCode = () => {
    const classificationTypeTitle = classificationType.charAt(0).toUpperCase() + classificationType.slice(1);

    if (classificationType === 'multi-label') {
      return `# ClassyWeb Multi-Label Classification API
curl -X POST "${deploymentResult?.endpoint || 'https://api.classyweb.com/models/your-model-id'}/classify" \\
  -H "Authorization: Bearer your-api-key-here" \\
  -H "Content-Type: application/json" \\
  -d '{
    "text": "Your text to classify here",
    "return_probabilities": true,
    "return_all_labels": true
  }'`;
    } else {
      return `# ClassyWeb ${classificationTypeTitle} Classification API
curl -X POST "${deploymentResult?.endpoint || 'https://api.classyweb.com/models/your-model-id'}/classify" \\
  -H "Authorization: Bearer your-api-key-here" \\
  -H "Content-Type: application/json" \\
  -d '{
    "text": "Your text to classify here",
    ${classificationType === 'hierarchical' ? '"hierarchy_level": null,' : ''}
    "return_probabilities": true
  }'`;
    }
  };

  const generateJavaScriptCode = () => {
    const classificationTypeTitle = classificationType.charAt(0).toUpperCase() + classificationType.slice(1);

    if (classificationType === 'multi-label') {
      return `// ClassyWeb Multi-Label Classification API
const API_ENDPOINT = "${deploymentResult?.endpoint || 'https://api.classyweb.com/models/your-model-id'}";
const API_KEY = "your-api-key-here";

async function classifyText(text, thresholdOverride = null) {
  const payload = {
    text: text,
    return_probabilities: true,
    return_all_labels: true
  };

  if (thresholdOverride) {
    payload.threshold_override = thresholdOverride;
  }

  const response = await fetch(\`\${API_ENDPOINT}/classify\`, {
    method: 'POST',
    headers: {
      'Authorization': \`Bearer \${API_KEY}\`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(payload)
  });

  if (!response.ok) {
    throw new Error(\`Classification failed: \${response.statusText}\`);
  }

  return await response.json();
}

// Example usage
classifyText("Your text to classify here")
  .then(result => {
    console.log('Predicted labels:', result.predicted_labels);
    console.log('Label probabilities:', result.label_probabilities);
    console.log('Active labels:', Object.entries(result.label_probabilities)
      .filter(([label, prob]) => prob > 0.5)
      .map(([label, prob]) => label));
  })
  .catch(error => console.error('Error:', error));`;
    } else {
      return `// ClassyWeb ${classificationTypeTitle} Classification API
const API_ENDPOINT = "${deploymentResult?.endpoint || 'https://api.classyweb.com/models/your-model-id'}";
const API_KEY = "your-api-key-here";

async function classifyText(text${classificationType === 'hierarchical' ? ', hierarchyLevel = null' : ''}) {
  const response = await fetch(\`\${API_ENDPOINT}/classify\`, {
    method: 'POST',
    headers: {
      'Authorization': \`Bearer \${API_KEY}\`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      text: text,
      ${classificationType === 'hierarchical' ? 'hierarchy_level: hierarchyLevel,' : ''}
      return_probabilities: true
    })
  });

  if (!response.ok) {
    throw new Error(\`Classification failed: \${response.statusText}\`);
  }

  return await response.json();
}

// Example usage
classifyText("Your text to classify here")
  .then(result => {
    console.log('Classification:', result.prediction);
    console.log('Confidence:', result.confidence);
    ${classificationType === 'hierarchical' ? "console.log('Hierarchy path:', result.hierarchy_path.join(' > '));" : ''}
  })
  .catch(error => console.error('Error:', error));`;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center space-y-2">
        <div className="flex items-center justify-center gap-2">
          <Rocket className="w-6 h-6 text-primary" />
          <h2 className="text-2xl font-bold">Deploy Your Model</h2>
        </div>
        <p className="text-muted-foreground">
          Choose how you want to deploy your trained hierarchical classification model
        </p>
      </div>

      {/* Model Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Package className="w-5 h-5" />
            Model Summary
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-primary">
                {(trainingMetrics.accuracy * 100)?.toFixed(1) || 'N/A'}%
              </div>
              <div className="text-sm text-muted-foreground">Accuracy</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-primary">
                {hierarchyLevels.length || 0}
              </div>
              <div className="text-sm text-muted-foreground">Hierarchy Levels</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-primary">
                {trainingMetrics.model_size_mb?.toFixed(1) || 'N/A'}MB
              </div>
              <div className="text-sm text-muted-foreground">Model Size</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-primary">
                {Math.round(trainingMetrics.training_time / 60) || 'N/A'}min
              </div>
              <div className="text-sm text-muted-foreground">Training Time</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Deployment Options */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="w-5 h-5" />
            Deployment Options
          </CardTitle>
          <CardDescription>
            Select your preferred deployment method based on your use case and license
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {deploymentOptions.map((option) => {
              const Icon = option.icon;
              const available = isOptionAvailable(option);

              return (
                <Card
                  key={option.id}
                  className={`cursor-pointer transition-all ${
                    selectedDeployment === option.id
                      ? 'ring-2 ring-primary bg-primary/5'
                      : available
                      ? 'hover:bg-muted/50'
                      : 'opacity-50 cursor-not-allowed'
                  }`}
                  onClick={() => available && setSelectedDeployment(option.id)}
                >
                  <CardContent className="p-4">
                    <div className="flex items-start gap-3">
                      <div className={`p-2 rounded-lg ${
                        selectedDeployment === option.id
                          ? 'bg-primary text-white'
                          : 'bg-muted'
                      }`}>
                        <Icon className="w-4 h-4" />
                      </div>
                      <div className="flex-1 space-y-1">
                        <div className="flex items-center gap-2">
                          <h4 className="font-medium">{option.title}</h4>
                          {!available && option.requiresLicense && (
                            <Badge variant="outline" className="text-xs">
                              {option.requiresLicense}
                            </Badge>
                          )}
                        </div>
                        <p className="text-sm text-muted-foreground">
                          {option.description}
                        </p>
                        {option.estimatedTime && (
                          <p className="text-xs text-muted-foreground">
                            Est. time: {option.estimatedTime}
                          </p>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>

          {/* License upgrade notice */}
          {deploymentOptions.some(opt => !isOptionAvailable(opt)) && (
            <Alert className="mt-4">
              <Shield className="w-4 h-4" />
              <AlertDescription>
                Some deployment options require a Professional or Enterprise license.
                <Button variant="link" className="p-0 h-auto ml-1">
                  Upgrade your license
                </Button>
                to unlock all features.
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* Configuration Panel */}
      {selectedDeployment && (
        <Card>
          <CardHeader>
            <CardTitle>Deployment Configuration</CardTitle>
            <CardDescription>
              Configure your {deploymentOptions.find(opt => opt.id === selectedDeployment)?.title} deployment
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="basic" className="w-full">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="basic">Basic</TabsTrigger>
                <TabsTrigger value="advanced">Advanced</TabsTrigger>
                <TabsTrigger value="security">Security</TabsTrigger>
              </TabsList>

              <TabsContent value="basic" className="space-y-4">
                {selectedDeployment === 'api' && (
                  <>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="api-name">API Name</Label>
                        <Input
                          id="api-name"
                          value={apiConfig.name}
                          onChange={(e) => setApiConfig(prev => ({ ...prev, name: e.target.value }))}
                          placeholder="my-classification-api"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="api-version">Version</Label>
                        <Input
                          id="api-version"
                          value={apiConfig.version}
                          onChange={(e) => setApiConfig(prev => ({ ...prev, version: e.target.value }))}
                          placeholder="1.0.0"
                        />
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="api-description">Description</Label>
                      <Textarea
                        id="api-description"
                        value={apiConfig.description}
                        onChange={(e) => setApiConfig(prev => ({ ...prev, description: e.target.value }))}
                        placeholder="Describe your API..."
                        rows={3}
                      />
                    </div>
                  </>
                )}

                {selectedDeployment === 'local' && (
                  <>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="export-format">Export Format</Label>
                        <Select
                          value={exportConfig.format}
                          onValueChange={(value) => setExportConfig(prev => ({ ...prev, format: value }))}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="pytorch">PyTorch (.pt)</SelectItem>
                            <SelectItem value="onnx">ONNX (.onnx)</SelectItem>
                            <SelectItem value="huggingface">HuggingFace</SelectItem>
                            <SelectItem value="tensorflow">TensorFlow</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="compression">Compression</Label>
                        <Select
                          value={exportConfig.compression}
                          onValueChange={(value) => setExportConfig(prev => ({ ...prev, compression: value }))}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="zip">ZIP Archive</SelectItem>
                            <SelectItem value="tar">TAR Archive</SelectItem>
                            <SelectItem value="none">No Compression</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                    <div className="space-y-3">
                      <div className="flex items-center space-x-2">
                        <Switch
                          id="include-tokenizer"
                          checked={exportConfig.includeTokenizer}
                          onCheckedChange={(checked) => setExportConfig(prev => ({ ...prev, includeTokenizer: checked }))}
                        />
                        <Label htmlFor="include-tokenizer">Include tokenizer</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Switch
                          id="include-metadata"
                          checked={exportConfig.includeMetadata}
                          onCheckedChange={(checked) => setExportConfig(prev => ({ ...prev, includeMetadata: checked }))}
                        />
                        <Label htmlFor="include-metadata">Include training metadata</Label>
                      </div>
                    </div>
                  </>
                )}
              </TabsContent>

              <TabsContent value="advanced" className="space-y-4">
                {selectedDeployment === 'api' && (
                  <>
                    <div className="space-y-3">
                      <div className="flex items-center space-x-2">
                        <Switch
                          id="enable-auth"
                          checked={apiConfig.enableAuth}
                          onCheckedChange={(checked) => setApiConfig(prev => ({ ...prev, enableAuth: checked }))}
                        />
                        <Label htmlFor="enable-auth">Enable API authentication</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Switch
                          id="enable-rate-limit"
                          checked={apiConfig.enableRateLimit}
                          onCheckedChange={(checked) => setApiConfig(prev => ({ ...prev, enableRateLimit: checked }))}
                        />
                        <Label htmlFor="enable-rate-limit">Enable rate limiting</Label>
                      </div>
                    </div>
                    {apiConfig.enableRateLimit && (
                      <div className="space-y-2">
                        <Label htmlFor="rate-limit">Requests per minute</Label>
                        <Input
                          id="rate-limit"
                          type="number"
                          value={apiConfig.maxRequestsPerMinute}
                          onChange={(e) => setApiConfig(prev => ({ ...prev, maxRequestsPerMinute: parseInt(e.target.value) }))}
                          min="1"
                          max="10000"
                        />
                      </div>
                    )}

                    {/* Multi-class specific API configuration */}
                    {classificationType === 'multi-class' && (
                      <div className="space-y-4 border-t pt-4">
                        <h4 className="font-medium">Multi-Class Configuration</h4>
                        <div className="grid grid-cols-2 gap-4">
                          <div className="space-y-2">
                            <Label htmlFor="confidence-threshold">Confidence Threshold</Label>
                            <Input
                              id="confidence-threshold"
                              type="number"
                              step="0.01"
                              min="0"
                              max="1"
                              value={0.5}
                              onChange={(e) => {
                                // Update confidence threshold in deployment config
                              }}
                            />
                            <p className="text-xs text-muted-foreground">
                              Minimum confidence for predictions (0.0 - 1.0)
                            </p>
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="max-predictions">Max Predictions</Label>
                            <Select defaultValue="1">
                              <SelectTrigger>
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="1">Top 1 prediction</SelectItem>
                                <SelectItem value="3">Top 3 predictions</SelectItem>
                                <SelectItem value="5">Top 5 predictions</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                        </div>
                        <div className="space-y-3">
                          <div className="flex items-center space-x-2">
                            <Switch
                              id="return-probabilities"
                              defaultChecked={true}
                            />
                            <Label htmlFor="return-probabilities">Return class probabilities</Label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Switch
                              id="include-strategy-info"
                              defaultChecked={false}
                            />
                            <Label htmlFor="include-strategy-info">Include strategy metadata in response</Label>
                          </div>
                        </div>
                      </div>
                    )}
                  </>
                )}

                {/* Multi-class specific local export configuration */}
                {selectedDeployment === 'local' && classificationType === 'multi-class' && (
                  <div className="space-y-4 border-t pt-4">
                    <h4 className="font-medium">Multi-Class Export Configuration</h4>
                    <div className="space-y-3">
                      <div className="flex items-center space-x-2">
                        <Switch
                          id="include-strategy-metadata"
                          defaultChecked={true}
                        />
                        <Label htmlFor="include-strategy-metadata">Include strategy metadata</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Switch
                          id="include-class-mapping"
                          defaultChecked={true}
                        />
                        <Label htmlFor="include-class-mapping">Include class name mapping</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Switch
                          id="include-training-config"
                          defaultChecked={false}
                        />
                        <Label htmlFor="include-training-config">Include training configuration</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Switch
                          id="optimize-for-inference"
                          defaultChecked={true}
                        />
                        <Label htmlFor="optimize-for-inference">Optimize for inference speed</Label>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="export-documentation">Documentation Level</Label>
                      <Select defaultValue="standard">
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="minimal">Minimal (README only)</SelectItem>
                          <SelectItem value="standard">Standard (README + API docs)</SelectItem>
                          <SelectItem value="comprehensive">Comprehensive (Full documentation)</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                )}

                {/* Batch processing configuration for multi-class */}
                {selectedDeployment === 'batch' && classificationType === 'multi-class' && (
                  <div className="space-y-4 border-t pt-4">
                    <h4 className="font-medium">Batch Processing Configuration</h4>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="batch-size">Batch Size</Label>
                        <Input
                          id="batch-size"
                          type="number"
                          defaultValue={32}
                          min="1"
                          max="512"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="parallel-workers">Parallel Workers</Label>
                        <Input
                          id="parallel-workers"
                          type="number"
                          defaultValue={4}
                          min="1"
                          max="16"
                        />
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="output-format">Output Format</Label>
                      <Select defaultValue="csv">
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="csv">CSV</SelectItem>
                          <SelectItem value="json">JSON</SelectItem>
                          <SelectItem value="parquet">Parquet</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                )}
              </TabsContent>

              <TabsContent value="security" className="space-y-4">
                <Alert>
                  <Shield className="w-4 h-4" />
                  <AlertDescription>
                    Your model will be deployed with enterprise-grade security including encryption at rest,
                    secure API endpoints, and audit logging.
                  </AlertDescription>
                </Alert>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      )}

      {/* Deployment Progress */}
      {isDeploying && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Monitor className="w-5 h-5" />
              Deployment Progress
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <Progress value={deploymentProgress} className="w-full" />
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-primary rounded-full animate-pulse" />
                <span className="text-sm">
                  {deploymentStatus === 'preparing' && 'Preparing deployment...'}
                  {deploymentStatus === 'deploying' && 'Deploying model...'}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Deployment Results */}
      {deploymentStatus === 'complete' && deploymentResult && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CheckCircle2 className="w-5 h-5 text-green-600" />
              Deployment Complete
            </CardTitle>
            <CardDescription>
              Your model has been successfully deployed and is ready to use
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>Deployment ID</Label>
                  <div className="flex items-center gap-2">
                    <code className="bg-muted px-2 py-1 rounded text-sm">
                      {deploymentResult.deploymentId}
                    </code>
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => handleCopyCode(deploymentResult.deploymentId, 'Deployment ID')}
                    >
                      <Copy className="w-3 h-3" />
                    </Button>
                  </div>
                </div>
                <div className="space-y-2">
                  <Label>Status</Label>
                  <Badge variant="default" className="bg-green-100 text-green-800">
                    {deploymentResult.status}
                  </Badge>
                </div>
              </div>

              {deploymentResult.endpoint && (
                <div className="space-y-2">
                  <Label>API Endpoint</Label>
                  <div className="flex items-center gap-2">
                    <code className="bg-muted px-2 py-1 rounded text-sm flex-1">
                      {deploymentResult.endpoint}
                    </code>
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => handleCopyCode(deploymentResult.endpoint, 'API Endpoint')}
                    >
                      <Copy className="w-3 h-3" />
                    </Button>
                    <Button size="sm" variant="ghost">
                      <ExternalLink className="w-3 h-3" />
                    </Button>
                  </div>
                </div>
              )}

              {deploymentResult.downloadUrl && (
                <div className="space-y-2">
                  <Label>Download Model</Label>
                  <Button
                    onClick={() => window.open(deploymentResult.downloadUrl, '_blank')}
                    className="w-full"
                  >
                    <Download className="w-4 h-4 mr-2" />
                    Download Model Package
                  </Button>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Integration Code Examples */}
      {deploymentStatus === 'complete' && deploymentResult?.endpoint && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Code className="w-5 h-5" />
              Integration Examples
            </CardTitle>
            <CardDescription>
              Copy and paste these code examples to integrate with your application
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="python" className="w-full">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="python">Python</TabsTrigger>
                <TabsTrigger value="javascript">JavaScript</TabsTrigger>
                <TabsTrigger value="curl">cURL</TabsTrigger>
              </TabsList>

              <TabsContent value="python" className="space-y-4">
                <div className="relative">
                  <pre className="bg-muted p-4 rounded-lg text-sm overflow-x-auto">
                    <code>{generatePythonCode()}</code>
                  </pre>
                  <Button
                    size="sm"
                    variant="ghost"
                    className="absolute top-2 right-2"
                    onClick={() => handleCopyCode(generatePythonCode(), 'Python')}
                  >
                    {copiedCode === 'Python' ? (
                      <CheckCircle2 className="w-3 h-3 text-green-600" />
                    ) : (
                      <Copy className="w-3 h-3" />
                    )}
                  </Button>
                </div>
              </TabsContent>

              <TabsContent value="javascript" className="space-y-4">
                <div className="relative">
                  <pre className="bg-muted p-4 rounded-lg text-sm overflow-x-auto">
                    <code>{generateJavaScriptCode()}</code>
                  </pre>
                  <Button
                    size="sm"
                    variant="ghost"
                    className="absolute top-2 right-2"
                    onClick={() => handleCopyCode(generateJavaScriptCode(), 'JavaScript')}
                  >
                    {copiedCode === 'JavaScript' ? (
                      <CheckCircle2 className="w-3 h-3 text-green-600" />
                    ) : (
                      <Copy className="w-3 h-3" />
                    )}
                  </Button>
                </div>
              </TabsContent>

              <TabsContent value="curl" className="space-y-4">
                <div className="relative">
                  <pre className="bg-muted p-4 rounded-lg text-sm overflow-x-auto">
                    <code>{generateCurlCode()}</code>
                  </pre>
                  <Button
                    size="sm"
                    variant="ghost"
                    className="absolute top-2 right-2"
                    onClick={() => handleCopyCode(generateCurlCode(), 'cURL')}
                  >
                    {copiedCode === 'cURL' ? (
                      <CheckCircle2 className="w-3 h-3 text-green-600" />
                    ) : (
                      <Copy className="w-3 h-3" />
                    )}
                  </Button>
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      )}

      {/* Action Buttons */}
      <div className="flex items-center justify-between">
        <Button variant="outline" onClick={onBack} disabled={isDeploying}>
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to Results
        </Button>

        <div className="flex items-center gap-3">
          {deploymentStatus === 'complete' ? (
            <Button onClick={() => onComplete?.(deploymentResult)}>
              <CheckCircle2 className="w-4 h-4 mr-2" />
              Complete Workflow
            </Button>
          ) : (
            <Button
              onClick={handleDeploy}
              disabled={!selectedDeployment || isDeploying || !isOptionAvailable(deploymentOptions.find(opt => opt.id === selectedDeployment)!)}
            >
              {isDeploying ? (
                <>
                  <div className="w-4 h-4 mr-2 animate-spin rounded-full border-2 border-current border-t-transparent" />
                  Deploying...
                </>
              ) : (
                <>
                  <Rocket className="w-4 h-4 mr-2" />
                  Deploy Model
                </>
              )}
            </Button>
          )}
        </div>
      </div>
    </div>
  );
};
