#!/usr/bin/env python3
"""
Test runner for the beginner workflow.

This script sets up the test environment and runs the comprehensive
beginner workflow test.
"""

import os
import sys
import subprocess
import tempfile
from pathlib import Path

# Add the backend directory to Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

def setup_test_environment():
    """Set up the test environment."""
    print("Setting up test environment...")
    
    # Set environment variables for testing
    os.environ.setdefault("TESTING", "true")
    os.environ.setdefault("DATABASE_URL", "sqlite:///./test.db")
    os.environ.setdefault("JWT_SECRET_KEY", "test-secret-key-for-testing-only")
    os.environ.setdefault("REDIS_URL", "redis://localhost:6379/1")  # Use test DB
    
    # Create test directories
    test_upload_dir = backend_dir / "test_uploads"
    test_upload_dir.mkdir(exist_ok=True)
    os.environ.setdefault("UPLOAD_DIR", str(test_upload_dir))
    
    print("Test environment configured.")

def run_tests():
    """Run the beginner workflow tests."""
    print("Running beginner workflow tests...")
    
    try:
        # Run the specific test file
        result = subprocess.run([
            sys.executable, "-m", "pytest", 
            "tests/test_beginner_workflow.py",
            "-v",
            "--tb=short",
            "--color=yes"
        ], cwd=backend_dir, capture_output=True, text=True)
        
        print("STDOUT:")
        print(result.stdout)
        
        if result.stderr:
            print("STDERR:")
            print(result.stderr)
        
        if result.returncode == 0:
            print("\n✅ All tests passed!")
        else:
            print(f"\n❌ Tests failed with return code {result.returncode}")
            
        return result.returncode
        
    except FileNotFoundError:
        print("❌ pytest not found. Please install pytest:")
        print("pip install pytest")
        return 1
    except Exception as e:
        print(f"❌ Error running tests: {e}")
        return 1

def cleanup():
    """Clean up test artifacts."""
    print("Cleaning up test artifacts...")
    
    # Remove test database
    test_db = backend_dir / "test.db"
    if test_db.exists():
        test_db.unlink()
    
    # Remove test uploads directory
    test_upload_dir = backend_dir / "test_uploads"
    if test_upload_dir.exists():
        import shutil
        shutil.rmtree(test_upload_dir)
    
    print("Cleanup completed.")

def main():
    """Main function."""
    print("🚀 Starting Beginner Workflow Test Suite")
    print("=" * 50)
    
    try:
        setup_test_environment()
        return_code = run_tests()
        
        if return_code == 0:
            print("\n🎉 Beginner workflow test completed successfully!")
        else:
            print("\n💥 Beginner workflow test failed!")
            
        return return_code
        
    except KeyboardInterrupt:
        print("\n⏹️  Test interrupted by user")
        return 1
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        return 1
    finally:
        cleanup()

if __name__ == "__main__":
    sys.exit(main())
