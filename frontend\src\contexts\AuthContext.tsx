// frontend/src/contexts/AuthContext.tsx
import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { useAuthStore } from '../store/authStore';
import { getCurrentUser } from '../services/authApi';

interface AuthContextType {
  isAuthenticated: boolean;
  isLoading: boolean;
  checkAuth: () => Promise<boolean>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  // Get all needed state and actions from the store at once
  const { token, refreshToken, tokenExpiry, isAuthenticated, isLoading, setUser, setLoading, logout, refreshAccessToken } = useAuthStore();
  const [isInitialized, setIsInitialized] = useState(false);

  // Function to check if token is expired or about to expire (within 5 minutes)
  const isTokenExpired = (): boolean => {
    if (!tokenExpiry) return false;
    // Check if token expires in less than 5 minutes
    return Date.now() > tokenExpiry - 5 * 60 * 1000;
  };

  // Function to check authentication status
  const checkAuth = async (): Promise<boolean> => {
    if (!token) {
      console.log('No token available, authentication check failed');
      return false;
    }

    console.log('Checking authentication status...');
    console.log('Token expiry:', tokenExpiry ? new Date(tokenExpiry).toISOString() : 'none');
    console.log('Current time:', new Date().toISOString());

    // If token is expired or about to expire, try to refresh it
    if (refreshToken && isTokenExpired()) {
      console.log('Token is expired or about to expire, attempting refresh...');
      const refreshed = await refreshAccessToken();
      if (!refreshed) {
        console.error('Token refresh failed');
        return false;
      }
    }

    try {
      setLoading(true);
      // Always fetch user data to ensure it's up to date
      console.log('Fetching current user data...');
      const user = await getCurrentUser();
      console.log('User data received:', user);
      setUser(user);
      setLoading(false);
      return true;
    } catch (error: any) {
      console.error('Authentication check failed:', error);
      console.error('Error details:', error.response?.data || error.message);

      // Don't automatically logout on network errors
      if (error.response?.status === 401) {
        console.log('Unauthorized error, logging out');
        logout();
      } else {
        console.log('Error occurred but not logging out automatically');
      }

      setLoading(false);
      return false;
    }
  };

  // Check authentication on mount and set up token refresh
  useEffect(() => {
    const initAuth = async () => {
      try {
        console.log('AuthContext - Initializing auth...');
        if (token) {
          console.log('Token found, initializing auth...', token.substring(0, 10) + '...');
          console.log('Auth state on init:', {
            isAuthenticated,
            hasRefreshToken: !!refreshToken,
            hasUser: !!useAuthStore.getState().user,
            tokenExpiry: tokenExpiry ? new Date(tokenExpiry).toISOString() : 'none'
          });

          // Check if token is expired or about to expire
          if (refreshToken && isTokenExpired()) {
            console.log('Token expired, refreshing...');
            try {
              const refreshed = await refreshAccessToken();
              if (!refreshed) {
                console.error('Token refresh failed during initialization');
                // Don't logout here, just continue and try to use the existing token
              }
            } catch (refreshError) {
              console.error('Error during token refresh:', refreshError);
              // Don't logout here either, try to continue
            }
          }

          // Always fetch user data on initialization
          setLoading(true);
          try {
            console.log('Fetching user data during initialization...');
            const user = await getCurrentUser();
            console.log('User data fetched successfully:', user);
            setUser(user);
          } catch (userError: any) {
            console.error('Error fetching user data:', userError);
            console.error('Error details:', userError.response?.data || userError.message);

            // Only logout on 401 errors, not on network errors
            if (userError.response?.status === 401) {
              console.log('Unauthorized error during initialization, logging out');
              logout();
            } else {
              console.log('Non-auth error during initialization, not logging out');
            }
          } finally {
            setLoading(false);
          }
        } else {
          console.log('No token found during initialization');
        }
      } catch (error) {
        console.error('Error during authentication initialization:', error);
        // Don't automatically logout on general errors
      } finally {
        setIsInitialized(true);
      }
    };

    initAuth();

    // Set up interval to check token expiration
    const tokenCheckInterval = setInterval(() => {
      if (token && refreshToken && isTokenExpired()) {
        console.log('Token expired during interval check, refreshing...');
        refreshAccessToken().catch(error => {
          console.error('Token refresh failed during interval check:', error);
          // Only logout on auth errors
          if (error.response?.status === 401) {
            logout();
          }
        });
      }
    }, 60000); // Check every minute

    return () => clearInterval(tokenCheckInterval);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Memoize the context value to prevent unnecessary re-renders
  // Moved before the conditional return
  const contextValue = React.useMemo(() => ({
    isAuthenticated,
    isLoading,
    checkAuth,
  }), [isAuthenticated, isLoading]);

  // If not initialized yet, show nothing
  if (!isInitialized) {
    return null;
  }

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
