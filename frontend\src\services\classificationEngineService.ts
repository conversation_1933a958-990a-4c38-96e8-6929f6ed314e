/**
 * Classification Engine Service for ClassyWeb ML Platform Phase 3
 * 
 * This service provides API v2 integration for dynamic engine selection,
 * training configuration, and real-time monitoring capabilities.
 */

import apiClient from './apiClient';

// Types and Interfaces
export interface ClassificationEngine {
  id: string;
  name: string;
  type: ClassificationType;
  description: string;
  capabilities: EngineCapability[];
  supported_methods: TrainingMethod[];
  performance_metrics?: PerformanceMetrics;
}

export interface EngineCapability {
  name: string;
  description: string;
  enabled: boolean;
}

export interface PerformanceMetrics {
  accuracy?: number;
  precision?: number;
  recall?: number;
  f1_score?: number;
  training_time?: number;
  inference_speed?: number;
}

export enum ClassificationType {
  BINARY = 'binary',
  MULTI_CLASS = 'multi-class',
  MULTI_LABEL = 'multi-label',
  HIERARCHICAL = 'hierarchical',
  FLAT = 'flat'
}

export enum TrainingMethod {
  LLM = 'llm',
  CUSTOM = 'custom',
  ENSEMBLE = 'ensemble'
}

export interface TrainingConfigV2 {
  file_id: string;
  classification_type: ClassificationType;
  training_method: TrainingMethod;
  text_column: string;
  label_columns: string[];
  hierarchy_levels?: string[];
  custom_prompt?: string;
  llm_provider?: string;
  llm_model?: string;
  training_params?: TrainingParameters;
  advanced_config?: AdvancedTrainingConfig;
}

export interface TrainingParameters {
  num_epochs?: number;
  batch_size?: number;
  learning_rate?: number;
  validation_split?: number;
  early_stopping?: boolean;
  patience?: number;
  use_unsloth?: boolean;
  max_length?: number;
  warmup_steps?: number;
  weight_decay?: number;
}

export interface AdvancedTrainingConfig {
  threshold_optimization?: boolean;
  class_balancing?: boolean;
  feature_selection?: boolean;
  hyperparameter_tuning?: boolean;
  cross_validation?: boolean;
  ensemble_methods?: string[];
  optimization_strategy?: string;
}

export interface TrainingResponse {
  session_id: string;
  task_id: string;
  status: string;
  message: string;
  estimated_duration?: number;
  websocket_url?: string;
}

export interface InferenceRequest {
  texts: string[];
  model_id?: string;
  session_id?: string;
  confidence_threshold?: number;
  return_probabilities?: boolean;
  batch_size?: number;
}

export interface InferenceResponse {
  predictions: any[];
  confidence_scores?: number[][];
  probabilities?: number[][];
  processing_time: number;
  model_info: {
    id: string;
    type: string;
    version: string;
  };
}

export interface EngineRecommendation {
  engine_id: string;
  confidence: number;
  reasoning: string;
  estimated_performance: PerformanceMetrics;
  recommended_config: Partial<TrainingConfigV2>;
}

/**
 * Get available classification engines for a specific type
 */
export const getEnginesForType = async (classificationType: ClassificationType): Promise<ClassificationEngine[]> => {
  try {
    const response = await apiClient.get(`/api/v2/classification/engines`, {
      params: { type: classificationType }
    });
    return response.data.engines;
  } catch (error) {
    console.error('Failed to fetch engines for type:', classificationType, error);
    throw new Error(`Failed to fetch engines for ${classificationType}: ${error}`);
  }
};

/**
 * Get a specific classification engine by ID
 */
export const getEngineById = async (engineId: string): Promise<ClassificationEngine> => {
  try {
    const response = await apiClient.get(`/api/v2/classification/engines/${engineId}`);
    return response.data;
  } catch (error) {
    console.error('Failed to fetch engine:', engineId, error);
    throw new Error(`Failed to fetch engine ${engineId}: ${error}`);
  }
};

/**
 * Get engine recommendations based on data characteristics
 */
export const getEngineRecommendations = async (
  fileId: string,
  classificationType: ClassificationType,
  dataCharacteristics?: any
): Promise<EngineRecommendation[]> => {
  try {
    const response = await apiClient.post('/api/v2/classification/engines/recommend', {
      file_id: fileId,
      classification_type: classificationType,
      data_characteristics: dataCharacteristics
    });
    return response.data.recommendations;
  } catch (error) {
    console.error('Failed to get engine recommendations:', error);
    throw new Error(`Failed to get engine recommendations: ${error}`);
  }
};

/**
 * Start training with API v2
 */
export const startTrainingV2 = async (config: TrainingConfigV2): Promise<TrainingResponse> => {
  try {
    const response = await apiClient.post('/api/v2/classification/train', config);
    return response.data;
  } catch (error) {
    console.error('Failed to start training:', error);
    throw new Error(`Failed to start training: ${error}`);
  }
};

/**
 * Get training status and progress
 */
export const getTrainingStatus = async (sessionId: string): Promise<any> => {
  try {
    const response = await apiClient.get(`/api/v2/classification/training/${sessionId}/status`);
    return response.data;
  } catch (error) {
    console.error('Failed to get training status:', sessionId, error);
    throw new Error(`Failed to get training status: ${error}`);
  }
};

/**
 * Stop training session
 */
export const stopTraining = async (sessionId: string): Promise<void> => {
  try {
    await apiClient.post(`/api/v2/classification/training/${sessionId}/stop`);
  } catch (error) {
    console.error('Failed to stop training:', sessionId, error);
    throw new Error(`Failed to stop training: ${error}`);
  }
};

/**
 * Perform inference using trained model
 */
export const performInference = async (request: InferenceRequest): Promise<InferenceResponse> => {
  try {
    const response = await apiClient.post('/api/v2/classification/inference', request);
    return response.data;
  } catch (error) {
    console.error('Failed to perform inference:', error);
    throw new Error(`Failed to perform inference: ${error}`);
  }
};

/**
 * Get available models for inference
 */
export const getAvailableModels = async (classificationType?: ClassificationType): Promise<any[]> => {
  try {
    const response = await apiClient.get('/api/v2/classification/models', {
      params: classificationType ? { type: classificationType } : {}
    });
    return response.data.models;
  } catch (error) {
    console.error('Failed to fetch available models:', error);
    throw new Error(`Failed to fetch available models: ${error}`);
  }
};

/**
 * Get model performance metrics
 */
export const getModelMetrics = async (modelId: string): Promise<PerformanceMetrics> => {
  try {
    const response = await apiClient.get(`/api/v2/classification/models/${modelId}/metrics`);
    return response.data;
  } catch (error) {
    console.error('Failed to get model metrics:', modelId, error);
    throw new Error(`Failed to get model metrics: ${error}`);
  }
};

/**
 * Export model for deployment
 */
export const exportModel = async (modelId: string, format: string = 'onnx'): Promise<Blob> => {
  try {
    const response = await apiClient.get(`/api/v2/classification/models/${modelId}/export`, {
      params: { format },
      responseType: 'blob'
    });
    return response.data;
  } catch (error) {
    console.error('Failed to export model:', modelId, error);
    throw new Error(`Failed to export model: ${error}`);
  }
};

/**
 * Delete a trained model
 */
export const deleteModel = async (modelId: string): Promise<void> => {
  try {
    await apiClient.delete(`/api/v2/classification/models/${modelId}`);
  } catch (error) {
    console.error('Failed to delete model:', modelId, error);
    throw new Error(`Failed to delete model: ${error}`);
  }
};

/**
 * Get training history for a session
 */
export const getTrainingHistory = async (sessionId: string): Promise<any> => {
  try {
    const response = await apiClient.get(`/api/v2/classification/training/${sessionId}/history`);
    return response.data;
  } catch (error) {
    console.error('Failed to get training history:', sessionId, error);
    throw new Error(`Failed to get training history: ${error}`);
  }
};

/**
 * Get system performance metrics during training
 */
export const getSystemMetrics = async (sessionId: string): Promise<any> => {
  try {
    const response = await apiClient.get(`/api/v2/classification/training/${sessionId}/system-metrics`);
    return response.data;
  } catch (error) {
    console.error('Failed to get system metrics:', sessionId, error);
    throw new Error(`Failed to get system metrics: ${error}`);
  }
};

/**
 * Validate training configuration before starting
 */
export const validateTrainingConfig = async (config: TrainingConfigV2): Promise<any> => {
  try {
    const response = await apiClient.post('/api/v2/classification/validate-config', config);
    return response.data;
  } catch (error) {
    console.error('Failed to validate training config:', error);
    throw new Error(`Failed to validate training config: ${error}`);
  }
};

/**
 * Get optimal hyperparameters for a configuration
 */
export const getOptimalHyperparameters = async (
  fileId: string,
  classificationType: ClassificationType,
  trainingMethod: TrainingMethod
): Promise<TrainingParameters> => {
  try {
    const response = await apiClient.post('/api/v2/classification/optimize-hyperparameters', {
      file_id: fileId,
      classification_type: classificationType,
      training_method: trainingMethod
    });
    return response.data.optimal_params;
  } catch (error) {
    console.error('Failed to get optimal hyperparameters:', error);
    throw new Error(`Failed to get optimal hyperparameters: ${error}`);
  }
};

/**
 * Compare multiple models
 */
export const compareModels = async (modelIds: string[]): Promise<any> => {
  try {
    const response = await apiClient.post('/api/v2/classification/models/compare', {
      model_ids: modelIds
    });
    return response.data;
  } catch (error) {
    console.error('Failed to compare models:', error);
    throw new Error(`Failed to compare models: ${error}`);
  }
};

/**
 * Get WebSocket URL for real-time monitoring
 */
export const getWebSocketUrl = (sessionId: string): string => {
  const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
  const host = window.location.host;
  const token = localStorage.getItem('auth_token') || '';
  return `${protocol}//${host}/ws/training/${sessionId}?token=${encodeURIComponent(token)}`;
};

export default {
  getEnginesForType,
  getEngineById,
  getEngineRecommendations,
  startTrainingV2,
  getTrainingStatus,
  stopTraining,
  performInference,
  getAvailableModels,
  deleteModel,
  getModelMetrics,
  exportModel,
  getTrainingHistory,
  getSystemMetrics,
  validateTrainingConfig,
  getOptimalHyperparameters,
  compareModels,
  getWebSocketUrl
};
