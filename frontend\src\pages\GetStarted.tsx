import { Navigation } from "@/components/Navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { UnifiedFileUploadZone } from "@/components/UnifiedFileUploadZone";
import { unifiedDataManager } from "@/services/unifiedDataManager";
import {
  GraduationCap,
  Settings,
  ArrowRight,
  Users,
  Zap,
  Brain,
  Target,
  Code,
  Lightbulb,
  Upload,
  Database
} from "lucide-react";

const GetStarted = () => {
  return (
    <div className="min-h-screen bg-background">
      <Navigation />
      
      <main className="pt-20">
        {/* Header */}
        <section className="py-16 bg-card border-b-2 border-border">
          <div className="max-w-4xl mx-auto px-6 text-center">
            <Badge variant="outline" className="mb-4">
              <Lightbulb className="w-4 h-4 mr-2" />
              Get Started
            </Badge>
            <h1 className="text-4xl md:text-5xl font-bold mb-6 text-foreground">
              Choose Your
              <span className="text-ml-primary"> Learning Path</span>
            </h1>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              Select the workflow that best matches your experience level and project requirements.
            </p>
          </div>
        </section>

        <div className="max-w-6xl mx-auto px-6 py-16">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Beginner Path */}
            <Card className="relative overflow-hidden border-2 border-ml-secondary/20 bg-gradient-to-br from-ml-secondary/5 to-transparent hover:shadow-glow transition-all duration-300">
              <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-ml-secondary/20 to-transparent rounded-full -mr-16 -mt-16" />
              
              <CardHeader className="relative z-10">
                <div className="flex items-center gap-3 mb-4">
                  <div className="w-16 h-16 rounded-full bg-ml-secondary/10 flex items-center justify-center">
                    <GraduationCap className="w-8 h-8 text-ml-secondary" />
                  </div>
                  <div>
                    <CardTitle className="text-2xl">Beginner Journey</CardTitle>
                    <CardDescription>Perfect for ML newcomers</CardDescription>
                  </div>
                </div>
                
                <div className="space-y-4">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 rounded-lg bg-ml-secondary/10 flex items-center justify-center">
                      <Users className="w-4 h-4 text-ml-secondary" />
                    </div>
                    <span className="text-sm">Guided step-by-step workflow</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 rounded-lg bg-ml-secondary/10 flex items-center justify-center">
                      <Brain className="w-4 h-4 text-ml-secondary" />
                    </div>
                    <span className="text-sm">AI-powered recommendations</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 rounded-lg bg-ml-secondary/10 flex items-center justify-center">
                      <Zap className="w-4 h-4 text-ml-secondary" />
                    </div>
                    <span className="text-sm">One-click training with defaults</span>
                  </div>
                </div>
              </CardHeader>

              <CardContent className="relative z-10">
                <div className="space-y-4">
                  <div className="p-4 bg-muted/50 rounded-lg">
                    <h4 className="font-semibold mb-2">What you'll learn:</h4>
                    <ul className="text-sm text-muted-foreground space-y-1">
                      <li>• How to prepare your data</li>
                      <li>• Understanding classification types</li>
                      <li>• Model evaluation basics</li>
                      <li>• Interpreting results</li>
                    </ul>
                  </div>
                  
                  <Button className="w-full bg-ml-secondary hover:bg-ml-secondary-dark" size="lg" asChild>
                    <a href="/beginner">
                      Start Beginner Journey
                      <ArrowRight className="w-4 h-4 ml-2" />
                    </a>
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Expert Path */}
            <Card className="relative overflow-hidden border-2 border-ml-primary/20 bg-gradient-to-br from-ml-primary/5 to-transparent hover:shadow-glow transition-all duration-300">
              <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-ml-primary/20 to-transparent rounded-full -mr-16 -mt-16" />
              
              <CardHeader className="relative z-10">
                <div className="flex items-center gap-3 mb-4">
                  <div className="w-16 h-16 rounded-full bg-ml-primary/10 flex items-center justify-center">
                    <Settings className="w-8 h-8 text-ml-primary" />
                  </div>
                  <div>
                    <CardTitle className="text-2xl">Expert Journey</CardTitle>
                    <CardDescription>Advanced control and customization</CardDescription>
                  </div>
                </div>
                
                <div className="space-y-4">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 rounded-lg bg-ml-primary/10 flex items-center justify-center">
                      <Code className="w-4 h-4 text-ml-primary" />
                    </div>
                    <span className="text-sm">Full programmatic control</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 rounded-lg bg-ml-primary/10 flex items-center justify-center">
                      <Target className="w-4 h-4 text-ml-primary" />
                    </div>
                    <span className="text-sm">Advanced hyperparameter tuning</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 rounded-lg bg-ml-primary/10 flex items-center justify-center">
                      <Settings className="w-4 h-4 text-ml-primary" />
                    </div>
                    <span className="text-sm">Custom model architectures</span>
                  </div>
                </div>
              </CardHeader>

              <CardContent className="relative z-10">
                <div className="space-y-4">
                  <div className="p-4 bg-muted/50 rounded-lg">
                    <h4 className="font-semibold mb-2">Advanced features:</h4>
                    <ul className="text-sm text-muted-foreground space-y-1">
                      <li>• Custom preprocessing pipelines</li>
                      <li>• Model ensemble techniques</li>
                      <li>• Advanced evaluation metrics</li>
                      <li>• Production deployment</li>
                    </ul>
                  </div>
                  
                  <Button className="w-full bg-ml-primary hover:bg-ml-primary-dark" size="lg" asChild>
                    <a href="/expert">
                      Start Expert Journey
                      <ArrowRight className="w-4 h-4 ml-2" />
                    </a>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Additional Resources */}
          <div className="mt-16 text-center">
            <h2 className="text-2xl font-bold mb-8">Need Help Deciding?</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <Card className="hover:shadow-card transition-all duration-300">
                <CardHeader>
                  <CardTitle className="text-lg">Documentation</CardTitle>
                  <CardDescription>Comprehensive guides and tutorials</CardDescription>
                </CardHeader>
                <CardContent>
                  <Button variant="outline" className="w-full">
                    View Docs
                  </Button>
                </CardContent>
              </Card>
              
              <Card className="hover:shadow-card transition-all duration-300">
                <CardHeader>
                  <CardTitle className="text-lg">Examples</CardTitle>
                  <CardDescription>Sample projects and use cases</CardDescription>
                </CardHeader>
                <CardContent>
                  <Button variant="outline" className="w-full">
                    Browse Examples
                  </Button>
                </CardContent>
              </Card>
              
              <Card className="hover:shadow-card transition-all duration-300">
                <CardHeader>
                  <CardTitle className="text-lg">Community</CardTitle>
                  <CardDescription>Get help from other users</CardDescription>
                </CardHeader>
                <CardContent>
                  <Button variant="outline" className="w-full">
                    Join Community
                  </Button>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Quick Start Section */}
          <div className="mt-16 pt-16 border-t border-border">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold mb-4">Quick Start</h2>
              <p className="text-muted-foreground max-w-2xl mx-auto">
                Already have data? Upload it now and get smart recommendations for the best workflow.
              </p>
            </div>

            <div className="max-w-4xl mx-auto">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Upload className="w-5 h-5" />
                    Smart File Upload
                  </CardTitle>
                  <CardDescription>
                    Upload your data and we'll automatically detect the best classification approach
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <UnifiedFileUploadZone
                    onFileSelected={(fileId, fileInfo, purposes) => {
                      // Redirect to appropriate workflow based on suggestions
                      const suggestions = unifiedDataManager.suggestDataPurposes(fileInfo);

                      if (suggestions.canUseForTraining) {
                        // Redirect to beginner workflow
                        window.location.href = '/workflow/beginner';
                      } else {
                        // Redirect to classification workflow
                        window.location.href = '/classification/flat';
                      }
                    }}
                    requiredPurposes={['analysis']}
                    allowMultiplePurposes={true}
                    showFileReuse={true}
                    title="Upload Your Data"
                    description="We'll analyze your data and recommend the perfect workflow"
                    showContinueButton={false}
                  />
                </CardContent>
              </Card>

              {/* File Stats */}
              {unifiedDataManager.getUsageStats().totalFiles > 0 && (
                <div className="mt-6 text-center">
                  <Card className="inline-block">
                    <CardContent className="p-4">
                      <div className="flex items-center gap-4">
                        <div className="flex items-center gap-2">
                          <Database className="w-4 h-4 text-muted-foreground" />
                          <span className="text-sm">
                            {unifiedDataManager.getUsageStats().totalFiles} files uploaded
                          </span>
                        </div>
                        <Button variant="outline" size="sm" asChild>
                          <a href="/dashboard">
                            View Dashboard
                            <ArrowRight className="w-3 h-3 ml-1" />
                          </a>
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              )}
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default GetStarted;
