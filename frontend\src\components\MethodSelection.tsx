import { useState } from "react";
import { <PERSON>, Card<PERSON>ontent, CardDescription, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";

import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import {
  Brain,
  Zap,
  CheckCircle2,
  Clock,
  Settings,
  Cpu,
  DollarSign,
  Target,
  Cloud
} from "lucide-react";

export type TrainingMethod = 'custom' | 'llm';

export interface MethodConfig {
  method: TrainingMethod;
  customConfig?: {
    algorithm: string;
    hyperparameterTuning: boolean;
    crossValidation: boolean;
    ensembleMethods: boolean;
    maxTrainingTime: number;
  };
  llmConfig?: {
    model: string;
    temperature: number;
    maxTokens: number;
    promptTemplate: string;
    fewShotExamples: number;
  };
}

interface MethodSelectionProps {
  onMethodSelect: (config: MethodConfig) => void;
  selectedMethod?: TrainingMethod;
  classificationType: string;
  datasetSize: number;
  estimatedComplexity: 'low' | 'medium' | 'high';
}

export const MethodSelection = ({ 
  onMethodSelect, 
  selectedMethod, 
  classificationType,
  datasetSize,
  estimatedComplexity
}: MethodSelectionProps) => {
  const [activeMethod, setActiveMethod] = useState<TrainingMethod | null>(selectedMethod || null);
  const [customConfig, setCustomConfig] = useState({
    algorithm: 'auto',
    hyperparameterTuning: true,
    crossValidation: true,
    ensembleMethods: false,
    maxTrainingTime: 30
  });
  const [llmConfig, setLlmConfig] = useState({
    model: 'gpt-4-turbo',
    temperature: 0.1,
    maxTokens: 1000,
    promptTemplate: 'default',
    fewShotExamples: 5
  });

  const getRecommendation = () => {
    if (datasetSize < 1000) return 'llm';
    if (estimatedComplexity === 'high' && datasetSize > 10000) return 'custom';
    if (classificationType === 'hierarchical' || classificationType === 'multilabel') return 'custom';
    return 'custom'; // Default recommendation
  };

  const recommended = getRecommendation();

  const handleMethodSelect = (method: TrainingMethod) => {
    setActiveMethod(method);
    const config: MethodConfig = {
      method,
      ...(method === 'custom' ? { customConfig } : { llmConfig })
    };
    onMethodSelect(config);
  };

  const getEstimatedTime = (method: TrainingMethod) => {
    if (method === 'llm') return '2-8 minutes';
    
    const baseTime = Math.max(5, Math.floor(datasetSize / 1000) * 2);
    const complexityMultiplier = estimatedComplexity === 'high' ? 2 : estimatedComplexity === 'medium' ? 1.5 : 1;
    const estimatedMinutes = Math.floor(baseTime * complexityMultiplier);
    
    return `${estimatedMinutes}-${estimatedMinutes + 10} minutes`;
  };

  const getEstimatedAccuracy = (method: TrainingMethod) => {
    if (method === 'llm') {
      return datasetSize < 1000 ? '75-85%' : '70-80%';
    }
    
    const baseAccuracy = estimatedComplexity === 'low' ? 85 : estimatedComplexity === 'medium' ? 80 : 75;
    const dataBonus = datasetSize > 10000 ? 5 : datasetSize > 5000 ? 3 : 0;
    const finalAccuracy = baseAccuracy + dataBonus;
    
    return `${finalAccuracy}-${finalAccuracy + 8}%`;
  };

  return (
    <div className="space-y-6">
      {/* Recommendation Banner */}
      <Card className="border-2 border-ml-success/20 bg-ml-success/5">
        <CardContent className="p-4">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 rounded-lg bg-ml-success/10 flex items-center justify-center">
              <Target className="w-4 h-4 text-ml-success" />
            </div>
            <div>
              <h4 className="font-semibold text-ml-success">AI Recommendation</h4>
              <p className="text-sm text-muted-foreground">
                Based on your data ({datasetSize.toLocaleString()} samples, {estimatedComplexity} complexity), 
                we recommend <strong>{recommended === 'custom' ? 'Custom Training' : 'LLM Inference'}</strong>
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Method Selection Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Custom Training */}
        <Card 
          className={`cursor-pointer transition-all ${
            activeMethod === 'custom' ? 'border-2 border-primary bg-primary/5' : 'hover:border-primary/20'
          }`}
          onClick={() => handleMethodSelect('custom')}
        >
          <CardContent className="p-6">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="w-12 h-12 rounded-full bg-ml-primary/10 flex items-center justify-center">
                    <Zap className="w-6 h-6 text-ml-primary" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold">Custom Training</h3>
                    <p className="text-sm text-muted-foreground">Train a specialized model</p>
                  </div>
                </div>
                {recommended === 'custom' && (
                  <Badge className="bg-ml-success text-white">Recommended</Badge>
                )}
                {activeMethod === 'custom' && (
                  <CheckCircle2 className="w-5 h-5 text-primary" />
                )}
              </div>

              <div className="grid grid-cols-2 gap-4 text-sm">
                <div className="flex items-center gap-2">
                  <Clock className="w-4 h-4 text-ml-warning" />
                  <span>{getEstimatedTime('custom')}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Target className="w-4 h-4 text-ml-success" />
                  <span>{getEstimatedAccuracy('custom')}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Cpu className="w-4 h-4 text-ml-primary" />
                  <span>High compute</span>
                </div>
                <div className="flex items-center gap-2">
                  <DollarSign className="w-4 h-4 text-ml-secondary" />
                  <span>Low cost</span>
                </div>
              </div>

              <div className="space-y-2 text-sm">
                <div className="flex items-center gap-2">
                  <CheckCircle2 className="w-3 h-3 text-ml-success" />
                  <span>Full control over architecture</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle2 className="w-3 h-3 text-ml-success" />
                  <span>Optimized for your data</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle2 className="w-3 h-3 text-ml-success" />
                  <span>Hyperparameter tuning</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle2 className="w-3 h-3 text-ml-success" />
                  <span>Fast inference after training</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* LLM Inference */}
        <Card 
          className={`cursor-pointer transition-all ${
            activeMethod === 'llm' ? 'border-2 border-primary bg-primary/5' : 'hover:border-primary/20'
          }`}
          onClick={() => handleMethodSelect('llm')}
        >
          <CardContent className="p-6">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="w-12 h-12 rounded-full bg-ml-secondary/10 flex items-center justify-center">
                    <Brain className="w-6 h-6 text-ml-secondary" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold">LLM Inference</h3>
                    <p className="text-sm text-muted-foreground">Use pre-trained models</p>
                  </div>
                </div>
                {recommended === 'llm' && (
                  <Badge className="bg-ml-success text-white">Recommended</Badge>
                )}
                {activeMethod === 'llm' && (
                  <CheckCircle2 className="w-5 h-5 text-primary" />
                )}
              </div>

              <div className="grid grid-cols-2 gap-4 text-sm">
                <div className="flex items-center gap-2">
                  <Clock className="w-4 h-4 text-ml-success" />
                  <span>{getEstimatedTime('llm')}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Target className="w-4 h-4 text-ml-warning" />
                  <span>{getEstimatedAccuracy('llm')}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Cloud className="w-4 h-4 text-ml-primary" />
                  <span>Cloud-based</span>
                </div>
                <div className="flex items-center gap-2">
                  <DollarSign className="w-4 h-4 text-ml-warning" />
                  <span>Per-use cost</span>
                </div>
              </div>

              <div className="space-y-2 text-sm">
                <div className="flex items-center gap-2">
                  <CheckCircle2 className="w-3 h-3 text-ml-success" />
                  <span>No training required</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle2 className="w-3 h-3 text-ml-success" />
                  <span>Immediate results</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle2 className="w-3 h-3 text-ml-success" />
                  <span>Excellent for text data</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle2 className="w-3 h-3 text-ml-success" />
                  <span>Built-in reasoning</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Configuration Panel */}
      {activeMethod && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="w-5 h-5" />
              {activeMethod === 'custom' ? 'Training Configuration' : 'LLM Configuration'}
            </CardTitle>
            <CardDescription>
              {activeMethod === 'custom' 
                ? 'Configure your custom training parameters'
                : 'Configure your LLM inference settings'
              }
            </CardDescription>
          </CardHeader>
          <CardContent>
            {activeMethod === 'custom' ? (
              <Tabs defaultValue="algorithm" className="space-y-4">
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger value="algorithm">Algorithm</TabsTrigger>
                  <TabsTrigger value="optimization">Optimization</TabsTrigger>
                  <TabsTrigger value="resources">Resources</TabsTrigger>
                </TabsList>

                <TabsContent value="algorithm" className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="algorithm">Algorithm Selection</Label>
                      <select 
                        id="algorithm" 
                        className="w-full mt-1 px-3 py-2 border rounded-md bg-background"
                        value={customConfig.algorithm}
                        onChange={(e) => setCustomConfig({...customConfig, algorithm: e.target.value})}
                      >
                        <option value="auto">Auto-Select Best</option>
                        <option value="random_forest">Random Forest</option>
                        <option value="gradient_boosting">Gradient Boosting</option>
                        <option value="svm">Support Vector Machine</option>
                        <option value="neural_network">Neural Network</option>
                        <option value="logistic_regression">Logistic Regression</option>
                      </select>
                    </div>
                    <div className="space-y-3">
                      <div className="flex items-center space-x-2">
                        <input 
                          type="checkbox" 
                          id="ensemble" 
                          className="w-4 h-4"
                          checked={customConfig.ensembleMethods}
                          onChange={(e) => setCustomConfig({...customConfig, ensembleMethods: e.target.checked})}
                        />
                        <Label htmlFor="ensemble">Enable ensemble methods</Label>
                      </div>
                    </div>
                  </div>
                </TabsContent>

                <TabsContent value="optimization" className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-3">
                      <div className="flex items-center space-x-2">
                        <input 
                          type="checkbox" 
                          id="hyperparameter" 
                          className="w-4 h-4"
                          checked={customConfig.hyperparameterTuning}
                          onChange={(e) => setCustomConfig({...customConfig, hyperparameterTuning: e.target.checked})}
                        />
                        <Label htmlFor="hyperparameter">Hyperparameter tuning</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <input 
                          type="checkbox" 
                          id="cross-validation" 
                          className="w-4 h-4"
                          checked={customConfig.crossValidation}
                          onChange={(e) => setCustomConfig({...customConfig, crossValidation: e.target.checked})}
                        />
                        <Label htmlFor="cross-validation">Cross-validation</Label>
                      </div>
                    </div>
                  </div>
                </TabsContent>

                <TabsContent value="resources" className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="max-time">Max Training Time (minutes)</Label>
                      <Input
                        id="max-time"
                        type="number"
                        min="5"
                        max="120"
                        value={customConfig.maxTrainingTime}
                        onChange={(e) => setCustomConfig({...customConfig, maxTrainingTime: parseInt(e.target.value)})}
                        className="mt-1"
                      />
                    </div>
                  </div>
                </TabsContent>
              </Tabs>
            ) : (
              <Tabs defaultValue="model" className="space-y-4">
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger value="model">Model</TabsTrigger>
                  <TabsTrigger value="prompt">Prompt</TabsTrigger>
                  <TabsTrigger value="examples">Examples</TabsTrigger>
                </TabsList>

                <TabsContent value="model" className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="llm-model">Model Selection</Label>
                      <select 
                        id="llm-model" 
                        className="w-full mt-1 px-3 py-2 border rounded-md bg-background"
                        value={llmConfig.model}
                        onChange={(e) => setLlmConfig({...llmConfig, model: e.target.value})}
                      >
                        <option value="gpt-4-turbo">GPT-4 Turbo</option>
                        <option value="gpt-3.5-turbo">GPT-3.5 Turbo</option>
                        <option value="claude-3-sonnet">Claude 3 Sonnet</option>
                        <option value="claude-3-haiku">Claude 3 Haiku</option>
                      </select>
                    </div>
                    <div>
                      <Label htmlFor="temperature">Temperature</Label>
                      <Input
                        id="temperature"
                        type="number"
                        min="0"
                        max="1"
                        step="0.1"
                        value={llmConfig.temperature}
                        onChange={(e) => setLlmConfig({...llmConfig, temperature: parseFloat(e.target.value)})}
                        className="mt-1"
                      />
                    </div>
                  </div>
                </TabsContent>

                <TabsContent value="prompt" className="space-y-4">
                  <div>
                    <Label htmlFor="prompt-template">Prompt Template</Label>
                    <select 
                      id="prompt-template" 
                      className="w-full mt-1 px-3 py-2 border rounded-md bg-background"
                      value={llmConfig.promptTemplate}
                      onChange={(e) => setLlmConfig({...llmConfig, promptTemplate: e.target.value})}
                    >
                      <option value="default">Default Template</option>
                      <option value="detailed">Detailed Analysis</option>
                      <option value="concise">Concise Classification</option>
                      <option value="reasoning">Step-by-step Reasoning</option>
                    </select>
                  </div>
                </TabsContent>

                <TabsContent value="examples" className="space-y-4">
                  <div>
                    <Label htmlFor="few-shot">Few-shot Examples</Label>
                    <Input
                      id="few-shot"
                      type="number"
                      min="0"
                      max="10"
                      value={llmConfig.fewShotExamples}
                      onChange={(e) => setLlmConfig({...llmConfig, fewShotExamples: parseInt(e.target.value)})}
                      className="mt-1"
                    />
                  </div>
                </TabsContent>
              </Tabs>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
};
