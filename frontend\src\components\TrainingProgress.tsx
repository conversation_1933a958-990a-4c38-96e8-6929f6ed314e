import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import {
  Zap,
  CheckCircle2,
  Pause,
  Play,
  Square,
  Activity,
  Cpu,
  MemoryStick,
  HardDrive,
  TrendingUp,
  TrendingDown,
  BarChart3
} from "lucide-react";
import { TrainingMethod, MethodConfig } from "./MethodSelection";

export interface TrainingMetrics {
  epoch?: number;
  totalEpochs?: number;
  loss?: number;
  accuracy?: number;
  valLoss?: number;
  valAccuracy?: number;
  learningRate?: number;
  processingSpeed?: number;
  estimatedTimeRemaining?: number;
}

export interface SystemMetrics {
  cpu: number;
  memory: number;
  gpu?: number;
  disk: number;
}

interface TrainingProgressProps {
  method: TrainingMethod;
  config: MethodConfig;
  isTraining: boolean;
  progress: number;
  metrics?: TrainingMetrics;
  systemMetrics?: SystemMetrics;
  onStart: () => void;
  onPause: () => void;
  onStop: () => void;
  onComplete: () => void;
  classificationType: string;
}

export const TrainingProgress = ({
  method,

  isTraining,
  progress,
  metrics = {},
  systemMetrics = { cpu: 45, memory: 62, gpu: 78, disk: 23 },
  onStart,
  onPause,
  onStop,
  onComplete,
  classificationType
}: TrainingProgressProps) => {
  const [isPaused, setIsPaused] = useState(false);
  const [logs, setLogs] = useState<string[]>([]);

  useEffect(() => {
    if (isTraining && progress === 100) {
      onComplete();
    }
  }, [isTraining, progress, onComplete]);

  useEffect(() => {
    if (isTraining && !isPaused) {
      const interval = setInterval(() => {
        const timestamp = new Date().toLocaleTimeString();
        if (method === 'custom') {
          const logMessages = [
            `[${timestamp}] Training epoch ${metrics.epoch || 1}/${metrics.totalEpochs || 10}`,
            `[${timestamp}] Loss: ${metrics.loss?.toFixed(4) || '0.0000'}, Accuracy: ${metrics.accuracy?.toFixed(2) || '0.00'}%`,
            `[${timestamp}] Validation accuracy: ${metrics.valAccuracy?.toFixed(2) || '0.00'}%`,
            `[${timestamp}] Learning rate: ${metrics.learningRate?.toExponential(2) || '1.00e-3'}`,
            `[${timestamp}] Processing ${metrics.processingSpeed || 1250} samples/sec`
          ];
          setLogs(prev => [...prev.slice(-10), logMessages[Math.floor(Math.random() * logMessages.length)]]);
        } else {
          const llmMessages = [
            `[${timestamp}] Processing batch ${Math.floor(progress / 10) + 1}/10`,
            `[${timestamp}] Analyzing text features and patterns`,
            `[${timestamp}] Generating ${classificationType} predictions`,
            `[${timestamp}] Confidence scoring and validation`,
            `[${timestamp}] Optimizing prompt effectiveness`
          ];
          setLogs(prev => [...prev.slice(-10), llmMessages[Math.floor(Math.random() * llmMessages.length)]]);
        }
      }, 2000);

      return () => clearInterval(interval);
    }
  }, [isTraining, isPaused, method, metrics, progress, classificationType]);

  const handlePause = () => {
    setIsPaused(!isPaused);
    onPause();
  };

  const getStatusColor = () => {
    if (progress === 100) return 'text-ml-success';
    if (isPaused) return 'text-ml-warning';
    if (isTraining) return 'text-ml-primary';
    return 'text-muted-foreground';
  };

  const getStatusText = () => {
    if (progress === 100) return 'Complete';
    if (isPaused) return 'Paused';
    if (isTraining) return method === 'custom' ? 'Training' : 'Processing';
    return 'Ready';
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 rounded-lg bg-ml-primary/10 flex items-center justify-center">
                <Zap className="w-5 h-5 text-ml-primary" />
              </div>
              <div>
                <CardTitle>
                  {method === 'custom' ? 'Model Training' : 'LLM Processing'}
                </CardTitle>
                <CardDescription>
                  {method === 'custom' 
                    ? `Training ${classificationType} classification model`
                    : `Processing ${classificationType} classification with LLM`
                  }
                </CardDescription>
              </div>
            </div>
            <Badge className={getStatusColor()}>
              {getStatusText()}
            </Badge>
          </div>
        </CardHeader>
      </Card>

      {/* Progress Overview */}
      <Card>
        <CardContent className="p-6">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="font-medium">Overall Progress</span>
              <span className="text-sm text-muted-foreground">{progress}%</span>
            </div>
            <Progress value={progress} className="h-3" />
            
            {method === 'custom' && metrics.estimatedTimeRemaining && (
              <div className="flex items-center justify-between text-sm">
                <span className="text-muted-foreground">Estimated time remaining:</span>
                <span className="font-medium">{Math.floor(metrics.estimatedTimeRemaining / 60)}m {metrics.estimatedTimeRemaining % 60}s</span>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Training Metrics */}
      {method === 'custom' && isTraining && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2 mb-2">
                <Activity className="w-4 h-4 text-ml-primary" />
                <span className="text-sm font-medium">Current Epoch</span>
              </div>
              <div className="text-2xl font-bold">
                {metrics.epoch || 1}/{metrics.totalEpochs || 10}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2 mb-2">
                <TrendingDown className="w-4 h-4 text-ml-error" />
                <span className="text-sm font-medium">Training Loss</span>
              </div>
              <div className="text-2xl font-bold text-ml-error">
                {metrics.loss?.toFixed(4) || '0.0000'}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2 mb-2">
                <TrendingUp className="w-4 h-4 text-ml-success" />
                <span className="text-sm font-medium">Accuracy</span>
              </div>
              <div className="text-2xl font-bold text-ml-success">
                {metrics.accuracy?.toFixed(1) || '0.0'}%
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2 mb-2">
                <BarChart3 className="w-4 h-4 text-ml-secondary" />
                <span className="text-sm font-medium">Val Accuracy</span>
              </div>
              <div className="text-2xl font-bold text-ml-secondary">
                {metrics.valAccuracy?.toFixed(1) || '0.0'}%
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* System Metrics */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">System Resources</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <Cpu className="w-4 h-4 text-ml-primary" />
                <span className="text-sm">CPU</span>
              </div>
              <div className="space-y-1">
                <div className="flex justify-between text-sm">
                  <span>{systemMetrics.cpu}%</span>
                </div>
                <Progress value={systemMetrics.cpu} className="h-2" />
              </div>
            </div>

            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <MemoryStick className="w-4 h-4 text-ml-secondary" />
                <span className="text-sm">Memory</span>
              </div>
              <div className="space-y-1">
                <div className="flex justify-between text-sm">
                  <span>{systemMetrics.memory}%</span>
                </div>
                <Progress value={systemMetrics.memory} className="h-2" />
              </div>
            </div>

            {systemMetrics.gpu && (
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Zap className="w-4 h-4 text-ml-accent" />
                  <span className="text-sm">GPU</span>
                </div>
                <div className="space-y-1">
                  <div className="flex justify-between text-sm">
                    <span>{systemMetrics.gpu}%</span>
                  </div>
                  <Progress value={systemMetrics.gpu} className="h-2" />
                </div>
              </div>
            )}

            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <HardDrive className="w-4 h-4 text-ml-warning" />
                <span className="text-sm">Disk</span>
              </div>
              <div className="space-y-1">
                <div className="flex justify-between text-sm">
                  <span>{systemMetrics.disk}%</span>
                </div>
                <Progress value={systemMetrics.disk} className="h-2" />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Controls */}
      <Card>
        <CardContent className="p-6">
          <div className="flex gap-3">
            {!isTraining && progress === 0 && (
              <Button onClick={onStart} className="flex-1">
                <Play className="w-4 h-4 mr-2" />
                Start {method === 'custom' ? 'Training' : 'Processing'}
              </Button>
            )}
            
            {isTraining && (
              <>
                <Button variant="outline" onClick={handlePause}>
                  {isPaused ? <Play className="w-4 h-4 mr-2" /> : <Pause className="w-4 h-4 mr-2" />}
                  {isPaused ? 'Resume' : 'Pause'}
                </Button>
                <Button variant="destructive" onClick={onStop}>
                  <Square className="w-4 h-4 mr-2" />
                  Stop
                </Button>
              </>
            )}

            {progress === 100 && (
              <Button onClick={onComplete} className="flex-1">
                <CheckCircle2 className="w-4 h-4 mr-2" />
                View Results
              </Button>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Training Logs */}
      {isTraining && logs.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Training Logs</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="bg-black text-green-400 p-4 rounded-lg font-mono text-sm max-h-48 overflow-y-auto">
              {logs.map((log, index) => (
                <div key={index} className="mb-1">
                  {log}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
