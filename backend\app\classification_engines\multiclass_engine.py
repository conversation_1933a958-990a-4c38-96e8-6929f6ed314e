"""Multi-class Classification Engine for ClassyWeb ML Platform.

This module implements multi-class classification (multiple mutually exclusive classes)
with support for both custom model training and LLM inference approaches.
"""

import logging
import time
import pandas as pd
import numpy as np
from typing import List, Dict, Any, Optional, Tuple
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, classification_report
import torch
from transformers import AutoTokenizer, AutoModelForSequenceClassification

from .base_engine import (
    BaseClassificationEngine, 
    ClassificationType, 
    TrainingMethod,
    ClassificationResult,
    TrainingConfig,
    TrainingResult
)

logger = logging.getLogger(__name__)


class MultiClassEngine(BaseClassificationEngine):
    """Multi-class classification engine for multiple mutually exclusive classes."""
    
    def __init__(self, classification_type: ClassificationType, **kwargs):
        """Initialize multi-class classification engine."""
        super().__init__(classification_type)
        self.strategy = 'softmax'  # or 'one_vs_rest'
        self.num_classes = 0
        self.class_names = []
        
    @property
    def supported_training_methods(self) -> List[TrainingMethod]:
        """Return supported training methods."""
        return [TrainingMethod.CUSTOM, TrainingMethod.LLM]
    
    @property
    def default_metrics(self) -> List[str]:
        """Return default metrics for multi-class classification."""
        return ['accuracy', 'macro_f1', 'weighted_f1', 'per_class_f1']
    
    def validate_config(self, config: TrainingConfig) -> Tuple[bool, List[str]]:
        """Validate configuration for multi-class classification."""
        errors = []
        
        # Check classification type
        if config.classification_type != ClassificationType.MULTICLASS:
            errors.append(f"Expected multiclass classification, got {config.classification_type}")
        
        # Check text columns
        if not config.text_columns:
            errors.append("At least one text column must be specified")
        
        # Check label columns
        if len(config.label_columns) != 1:
            errors.append("Multi-class classification requires exactly one label column")
        
        # Check training method specific requirements
        if config.training_method == TrainingMethod.LLM:
            if not config.llm_provider:
                errors.append("LLM provider must be specified for LLM training method")
            if not config.prompt_template:
                errors.append("Prompt template must be specified for LLM training method")
        
        return len(errors) == 0, errors
    
    def get_recommended_config(
        self,
        data: pd.DataFrame,
        training_method: TrainingMethod
    ) -> TrainingConfig:
        """Get recommended configuration for multi-class classification."""
        config = super().get_recommended_config(data, training_method)
        
        # Detect text and label columns
        text_cols = [col for col in data.columns if data[col].dtype == 'object' and 'text' in col.lower()]
        if not text_cols:
            text_cols = [col for col in data.columns if data[col].dtype == 'object'][:1]
        
        label_cols = [col for col in data.columns if 'label' in col.lower() or 'class' in col.lower()]
        if not label_cols:
            # Find columns with more than 2 unique values
            for col in data.columns:
                if col not in text_cols and data[col].nunique() > 2:
                    label_cols = [col]
                    break
        
        config.text_columns = text_cols
        config.label_columns = label_cols[:1]  # Only one label column for multi-class
        
        # Analyze classes
        if label_cols:
            label_col = label_cols[0]
            class_counts = data[label_col].value_counts()
            self.num_classes = len(class_counts)
            self.class_names = class_counts.index.tolist()
            
            # Adjust for class imbalance
            imbalance_ratio = class_counts.max() / class_counts.min()
            if imbalance_ratio > 3:
                # Use class weights for imbalanced data
                total = len(data)
                config.class_weights = {}
                for class_name in self.class_names:
                    config.class_weights[str(class_name)] = total / (self.num_classes * class_counts[class_name])
        
        # Set multi-class specific defaults
        config.strategy = 'softmax'
        
        if training_method == TrainingMethod.LLM:
            config.prompt_template = self._get_default_prompt_template()
            config.temperature = 0.1
            config.max_tokens = 20
        
        return config
    
    def _get_default_prompt_template(self) -> str:
        """Get default prompt template for multi-class classification."""
        if self.class_names:
            classes_str = ", ".join(self.class_names)
            return f"""Classify the following text into one of these categories: {classes_str}

Text: {{text}}

Classification (respond with only the category name):"""
        else:
            return """Classify the following text into the most appropriate category.

Text: {text}

Classification:"""
    
    async def train_custom_model(
        self,
        data: pd.DataFrame,
        config: TrainingConfig,
        progress_callback: Optional[callable] = None
    ) -> TrainingResult:
        """Train a custom multi-class classification model."""
        start_time = time.time()
        
        try:
            # Validate configuration
            is_valid, errors = self.validate_config(config)
            if not is_valid:
                return TrainingResult(
                    model_id="",
                    training_time=0,
                    final_metrics={},
                    training_history=[],
                    error=f"Configuration validation failed: {'; '.join(errors)}"
                )
            
            if progress_callback:
                progress_callback({"stage": "preparation", "progress": 0.1})
            
            # Prepare data
            text_col = config.text_columns[0]
            label_col = config.label_columns[0]
            
            texts = data[text_col].astype(str).tolist()
            labels = data[label_col].tolist()
            
            # Get unique labels and create mapping
            unique_labels = sorted(list(set(labels)))
            self.num_classes = len(unique_labels)
            self.class_names = unique_labels
            
            if self.num_classes < 3:
                return TrainingResult(
                    model_id="",
                    training_time=0,
                    final_metrics={},
                    training_history=[],
                    error=f"Multi-class classification requires at least 3 classes, found {self.num_classes}"
                )
            
            # Map labels to integers
            label_map = {label: idx for idx, label in enumerate(unique_labels)}
            numeric_labels = [label_map[label] for label in labels]
            
            if progress_callback:
                progress_callback({"stage": "model_setup", "progress": 0.2})
            
            # Initialize tokenizer and model
            tokenizer = AutoTokenizer.from_pretrained(config.base_model)
            model = AutoModelForSequenceClassification.from_pretrained(
                config.base_model,
                num_labels=self.num_classes
            )
            
            # Tokenize data
            encodings = tokenizer(
                texts,
                truncation=True,
                padding=True,
                max_length=config.max_length,
                return_tensors="pt"
            )
            
            if progress_callback:
                progress_callback({"stage": "training", "progress": 0.3})
            
            # Create dataset
            class MultiClassDataset(torch.utils.data.Dataset):
                def __init__(self, encodings, labels):
                    self.encodings = encodings
                    self.labels = labels
                
                def __getitem__(self, idx):
                    item = {key: torch.tensor(val[idx]) for key, val in self.encodings.items()}
                    item['labels'] = torch.tensor(self.labels[idx], dtype=torch.long)
                    return item
                
                def __len__(self):
                    return len(self.labels)
            
            # Split data
            from sklearn.model_selection import train_test_split
            train_texts, val_texts, train_labels, val_labels = train_test_split(
                texts, numeric_labels, test_size=config.validation_split, random_state=42, stratify=numeric_labels
            )
            
            train_encodings = tokenizer(train_texts, truncation=True, padding=True, max_length=config.max_length, return_tensors="pt")
            val_encodings = tokenizer(val_texts, truncation=True, padding=True, max_length=config.max_length, return_tensors="pt")
            
            train_dataset = MultiClassDataset(train_encodings, train_labels)
            val_dataset = MultiClassDataset(val_encodings, val_labels)
            
            # Training setup
            from transformers import Trainer, TrainingArguments
            
            training_args = TrainingArguments(
                output_dir='./results',
                num_train_epochs=config.num_epochs,
                per_device_train_batch_size=config.batch_size,
                per_device_eval_batch_size=config.batch_size,
                warmup_steps=config.warmup_steps,
                weight_decay=config.weight_decay,
                logging_dir='./logs',
                evaluation_strategy=config.evaluation_strategy,
                save_strategy=config.save_strategy,
                fp16=config.fp16,
                gradient_accumulation_steps=config.gradient_accumulation_steps,
                gradient_checkpointing=config.gradient_checkpointing,
                learning_rate=config.learning_rate,
            )
            
            def compute_metrics(eval_pred):
                predictions, labels = eval_pred
                predictions = np.argmax(predictions, axis=1)
                return {
                    'accuracy': accuracy_score(labels, predictions),
                    'macro_f1': f1_score(labels, predictions, average='macro'),
                    'weighted_f1': f1_score(labels, predictions, average='weighted'),
                    'macro_precision': precision_score(labels, predictions, average='macro'),
                    'macro_recall': recall_score(labels, predictions, average='macro')
                }
            
            trainer = Trainer(
                model=model,
                args=training_args,
                train_dataset=train_dataset,
                eval_dataset=val_dataset,
                compute_metrics=compute_metrics,
            )
            
            # Train model
            trainer.train()
            
            if progress_callback:
                progress_callback({"stage": "evaluation", "progress": 0.8})
            
            # Evaluate model
            eval_results = trainer.evaluate()
            
            # Calculate additional metrics
            val_predictions = trainer.predict(val_dataset)
            val_preds = np.argmax(val_predictions.predictions, axis=1)
            
            # Per-class metrics
            class_report = classification_report(val_labels, val_preds, target_names=self.class_names, output_dict=True)
            
            final_metrics = {
                'accuracy': accuracy_score(val_labels, val_preds),
                'macro_f1': f1_score(val_labels, val_preds, average='macro'),
                'weighted_f1': f1_score(val_labels, val_preds, average='weighted'),
                'macro_precision': precision_score(val_labels, val_preds, average='macro'),
                'macro_recall': recall_score(val_labels, val_preds, average='macro'),
                'per_class_metrics': class_report
            }
            
            # Save model artifacts
            model_id = f"multiclass_model_{int(time.time())}"
            model_path = f"./model_artifacts/{model_id}"
            
            trainer.save_model(model_path)
            tokenizer.save_pretrained(model_path)
            
            # Store model info
            self.model = model
            self.tokenizer = tokenizer
            self.label_map = {v: k for k, v in label_map.items()}
            self.is_trained = True
            
            training_time = time.time() - start_time
            
            if progress_callback:
                progress_callback({"stage": "complete", "progress": 1.0})
            
            return TrainingResult(
                model_id=model_id,
                training_time=training_time,
                final_metrics=final_metrics,
                training_history=[],  # TODO: Extract from trainer logs
                model_path=model_path,
                tokenizer_path=model_path,
                config_path=model_path
            )
            
        except Exception as e:
            logger.error(f"Training failed: {e}")
            return TrainingResult(
                model_id="",
                training_time=time.time() - start_time,
                final_metrics={},
                training_history=[],
                error=str(e)
            )

    async def llm_inference(
        self,
        texts: List[str],
        config: TrainingConfig,
        progress_callback: Optional[callable] = None
    ) -> List[ClassificationResult]:
        """Perform multi-class classification using LLM inference."""
        results = []

        try:
            # Use sequential thinking LLM classifier for enhanced results
            from .sequential_llm_classifier import SequentialLLMClassifier

            if progress_callback:
                progress_callback({"stage": "llm_setup", "progress": 0.1})

            # Initialize sequential classifier
            sequential_classifier = SequentialLLMClassifier()

            # Use sequential thinking approach for enhanced classification
            results = await sequential_classifier.classify_with_sequential_thinking(
                texts=texts,
                classification_type="multi-class",
                llm_provider=config.llm_provider,
                llm_model=config.llm_model,
                llm_endpoint=getattr(config, 'llm_endpoint', ''),
                api_key=getattr(config, 'api_key', None),
                custom_prompt=config.prompt_template,
                temperature=config.temperature,
                max_tokens=config.max_tokens,
                progress_callback=progress_callback
            )

            return results

        except Exception as e:
            logger.error(f"LLM inference failed: {e}")
            # Return error results for all texts
            return [
                ClassificationResult(
                    text=text,
                    predictions=["error"],
                    confidence=0.0,
                    processing_time=0.0,
                    method_used="llm_inference",
                    reasoning=f"Error: {str(e)}",
                    metadata={"error": str(e)}
                )
                for text in texts
            ]

    async def predict(
        self,
        texts: List[str],
        model_id: Optional[str] = None
    ) -> List[ClassificationResult]:
        """Make predictions using a trained multi-class classification model."""
        if not self.is_trained or self.model is None or self.tokenizer is None:
            raise ValueError("Model not trained or loaded. Train a model first or load an existing one.")

        results = []

        try:
            # Tokenize texts
            encodings = self.tokenizer(
                texts,
                truncation=True,
                padding=True,
                max_length=512,
                return_tensors="pt"
            )

            # Make predictions
            with torch.no_grad():
                outputs = self.model(**encodings)
                predictions = torch.nn.functional.softmax(outputs.logits, dim=-1)

            # Process results
            for i, text in enumerate(texts):
                start_time = time.time()

                probs = predictions[i].numpy()
                predicted_class = int(np.argmax(probs))
                confidence = float(probs[predicted_class])

                # Map back to original labels
                prediction_label = self.label_map.get(predicted_class, f"class_{predicted_class}")

                # Create probability dictionary
                prob_dict = {}
                for class_idx, prob in enumerate(probs):
                    label = self.label_map.get(class_idx, f"class_{class_idx}")
                    prob_dict[label] = float(prob)

                processing_time = time.time() - start_time

                results.append(ClassificationResult(
                    text=text,
                    predictions=[prediction_label],
                    confidence=confidence,
                    probabilities=prob_dict,
                    processing_time=processing_time,
                    method_used="custom_model",
                    reasoning=f"Predicted {prediction_label} with {confidence:.3f} confidence",
                    metadata={"model_id": model_id, "num_classes": self.num_classes}
                ))

            return results

        except Exception as e:
            logger.error(f"Prediction failed: {e}")
            return [
                ClassificationResult(
                    text=text,
                    predictions=["error"],
                    confidence=0.0,
                    processing_time=0.0,
                    method_used="custom_model",
                    reasoning=f"Prediction error: {str(e)}",
                    metadata={"error": str(e)}
                )
                for text in texts
            ]
