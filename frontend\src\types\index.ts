export interface FileInfo {
    file_id: string;
    filename: string;
    columns: string[];
    num_rows: number;
    row_count?: number; // Alternative property name for compatibility
    preview: Record<string, any>[]; // Array of objects for preview rows
  }

  export interface LLMProviderConfig {
    provider: string;
    endpoint: string;
    model_name: string;
    api_key?: string; // Optional but not null
  }

  export interface TaskStatus {
    task_id: string;
    status: 'PENDING' | 'RUNNING' | 'SUCCESS' | 'FAILED' | 'processing' | 'completed' | 'failed'; // Extended status values
    message?: string | null;
    result_data_url?: string | null; // Renamed from result_url
    progress?: number; // Progress percentage (0-100)
    stage?: string; // Current stage of the task (e.g., 'Loading data', 'Training')
    result?: any; // Result data from completed tasks
    error?: string | null; // Error message for failed tasks
  }

  // --- LLM Config API Types ---

  export interface ProviderListResponse {
    providers: string[];
    default_endpoints: Record<string, string>;
    default_models: Record<string, string>;
    api_keys: Record<string, string>;
  }

  export interface FetchModelsRequest {
    provider: string;
    endpoint: string;
    api_key?: string | null;
  }

  export interface ModelListResponse {
    models: string[];
  }

  // --- Hierarchy Editor Types ---

  // Dynamic hierarchy row interface
  export interface HierarchyRow {
    // Using optional id for potential grid library integration
    id?: number | string;
    // Dynamic properties for hierarchy levels
    [key: string]: string | number | undefined;
    // Keywords is always required
    Keywords: string; // Comma-separated string
  }

  // --- Flat Label Editor Types ---

  // Flat label row interface for multi-label classification
  export interface FlatLabelRow {
    id?: number | string;
    label: string; // The label name
    keywords: string; // Comma-separated keywords/examples
    description?: string; // Optional description for the label
  }

  // Hierarchy configuration types
  export interface HierarchyConfig {
    id: number;
    name: string;
    hierarchy_levels: string[];
    is_default: boolean;
    user_id?: number | null;
    description?: string | null;
    domain?: string | null;
    validation_rules?: Record<string, any> | null;
    ui_config?: Record<string, any> | null;
    confidence_thresholds?: Record<string, number> | null;
    extra_metadata?: Record<string, any> | null;
    created_at: string;
    updated_at: string;
  }

  export interface HierarchyConfigListResponse {
    configs: HierarchyConfig[];
  }

  export interface HierarchyConfigCreate {
    name: string;
    hierarchy_levels: string[];
    is_default?: boolean;
    description?: string;
    domain?: string;
    validation_rules?: Record<string, any>;
    ui_config?: Record<string, any>;
    confidence_thresholds?: Record<string, number>;
    extra_metadata?: Record<string, any>;
  }

  export interface HierarchyConfigUpdate {
    name?: string;
    hierarchy_levels?: string[];
    is_default?: boolean;
    description?: string;
    domain?: string;
    validation_rules?: Record<string, any>;
    ui_config?: Record<string, any>;
    confidence_thresholds?: Record<string, number>;
    extra_metadata?: Record<string, any>;
  }

  // --- Hierarchy Suggestion API Types ---

  export interface HierarchySuggestRequest {
    sample_texts: string[];
    llm_config: LLMProviderConfig; // Reuse existing config type
  }

  // Represents the nested structure returned by the backend suggestion
  // This is intentionally kept generic (Dict/Any) as the exact structure
  // might vary slightly, and we primarily use it for flattening.
  export type NestedHierarchySuggestion = Record<string, any>;

  export interface HierarchySuggestResponse {
    suggestion?: NestedHierarchySuggestion | null;
    error?: string | null;
  }

  // --- LLM Classification API Types ---

  export interface ClassifyLLMRequest {
    file_id: string;
    original_filename: string;
    text_columns: string[];
    hierarchy: NestedHierarchySuggestion; // The nested hierarchy structure
    llm_config: LLMProviderConfig;
    hierarchy_config_id?: number | null; // Optional hierarchy configuration ID
  }

  // --- Classification Results Type ---
  // Represents a single row in the results data (flexible columns)
  export type ClassificationResultRow = Record<string, any>;

  // --- File Management Types ---
  export interface FileUpdateRequest {
    filename: string;
  }

  // --- HF Model Management Types ---

  // Represents the detailed info for a single saved HF model from the DB
  export interface SavedHFModelInfo {
    id: number;
    name: string;
    base_model: string;
    num_epochs: number | null;
    created_at: string; // ISO date string
  }

  // Updated response type for listing saved models from the DB
  export interface SavedHFModelListResponse {
    models: SavedHFModelInfo[];
  }

  // --- HF Training Types ---

  export interface HFTrainingRequest {
    training_file_id: string;
    original_training_filename: string;
    text_columns: string[];
    // Mapping of hierarchy levels (e.g., 'L1') to column names. Value is an array of strings.
    hierarchy_columns: Record<string, string[]>;
    base_model?: string; // Optional, backend has default
    num_epochs?: number; // Optional, backend has default
    validation_split?: number; // Optional, backend has default (0.15)
    new_model_name: string;
    hierarchy_config_id?: number | null; // Optional, ID of the hierarchy configuration to use
  }

  // --- HF Rules Types ---

  export interface HFRule {
    // Match backend model (using alias for Confidence Threshold)
    Label: string;
    Keywords: string;
    'Confidence Threshold': number; // Use the actual name expected by the backend/CSV
  }

  export interface HFRulesResponse {
    rules: HFRule[];
  }

  export interface HFRulesUpdateRequest {
    rules: HFRule[];
  }

  // --- HF Classification Types ---

  export interface HFClassificationRequest {
    file_id: string;
    original_filename: string;
    text_columns: string[];
    model_name: string; // Name of the saved HF model to use (from DB)
    hierarchy_config_id?: number | null; // Optional hierarchy config ID
  }

  // --- Generic API Response Types ---
  export interface MessageResponse {
    message: string;
    detail?: string; // Optional detail field for more info/errors
  }

  // Add more types as needed for hierarchy, rules, etc. later
