/**
 * ValidationResultsDisplay.tsx
 * 
 * Component to display hierarchy validation results and violations
 */

import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import * as Collapsible from "@radix-ui/react-collapsible";
import { 
  AlertCircle, 
  AlertTriangle, 
  CheckCircle2, 
  ChevronDown,
  ChevronRight,
  Info,
  Shield
} from "lucide-react";
import { ComprehensiveValidationResult, ValidationResult, ValidationViolation } from "@/services/hierarchyValidationService";

interface ValidationResultsDisplayProps {
  validationResults: ComprehensiveValidationResult | null;
  isLoading?: boolean;
}

export const ValidationResultsDisplay: React.FC<ValidationResultsDisplayProps> = ({
  validationResults,
  isLoading = false
}) => {
  const [expandedRules, setExpandedRules] = React.useState<Set<string>>(new Set());

  const toggleRuleExpansion = (ruleId: string) => {
    const newExpanded = new Set(expandedRules);
    if (newExpanded.has(ruleId)) {
      newExpanded.delete(ruleId);
    } else {
      newExpanded.add(ruleId);
    }
    setExpandedRules(newExpanded);
  };

  const getRuleDisplayName = (ruleId: string): string => {
    const ruleNames: Record<string, string> = {
      'parent_child_consistency': 'Parent-Child Consistency',
      'circular_dependencies': 'Circular Dependencies',
      'orphaned_nodes': 'Orphaned Nodes',
      'level_completeness': 'Level Completeness',
      'duplicate_paths': 'Duplicate Paths'
    };
    return ruleNames[ruleId] || ruleId;
  };

  const getViolationIcon = (severity: 'error' | 'warning') => {
    return severity === 'error' ? 
      <AlertCircle className="w-4 h-4 text-red-500" /> : 
      <AlertTriangle className="w-4 h-4 text-yellow-500" />;
  };

  const renderViolation = (violation: ValidationViolation, index: number) => (
    <div key={index} className="flex items-start gap-2 p-2 bg-muted/30 rounded text-sm">
      {getViolationIcon(violation.severity)}
      <div className="flex-1">
        <p className="font-medium">{violation.type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}</p>
        <p className="text-muted-foreground">{violation.message}</p>
        {violation.rowIndex !== undefined && (
          <p className="text-xs text-muted-foreground mt-1">Row: {violation.rowIndex + 1}</p>
        )}
      </div>
    </div>
  );

  const renderRuleResult = (ruleId: string, result: ValidationResult) => {
    const isExpanded = expandedRules.has(ruleId);
    const hasIssues = result.violations.length > 0 || result.warnings.length > 0;
    const totalIssues = result.violations.length + result.warnings.length;

    return (
      <Card key={ruleId} className="mb-3">
        <Collapsible.Root open={isExpanded} onOpenChange={() => toggleRuleExpansion(ruleId)}>
          <Collapsible.Trigger
            className="w-full"
          >
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  {isExpanded ? <ChevronDown className="w-4 h-4" /> : <ChevronRight className="w-4 h-4" />}
                  <CardTitle className="text-sm font-medium">
                    {getRuleDisplayName(ruleId)}
                  </CardTitle>
                </div>
                <div className="flex items-center gap-2">
                  {result.valid ? (
                    <Badge variant="secondary" className="text-green-600">
                      <CheckCircle2 className="w-3 h-3 mr-1" />
                      Valid
                    </Badge>
                  ) : (
                    <Badge variant="destructive">
                      <AlertCircle className="w-3 h-3 mr-1" />
                      {totalIssues} Issue{totalIssues !== 1 ? 's' : ''}
                    </Badge>
                  )}
                </div>
              </div>
            </CardHeader>
          </Collapsible.Trigger>

          {hasIssues && (
            <Collapsible.Content>
              <CardContent className="pt-0">
                <div className="space-y-3">
                  {result.violations.length > 0 && (
                    <div>
                      <h4 className="text-sm font-medium text-red-600 mb-2 flex items-center gap-1">
                        <AlertCircle className="w-4 h-4" />
                        Violations ({result.violations.length})
                      </h4>
                      <div className="space-y-2">
                        {result.violations.map((violation, index) => renderViolation(violation, index))}
                      </div>
                    </div>
                  )}
                  
                  {result.warnings.length > 0 && (
                    <div>
                      <h4 className="text-sm font-medium text-yellow-600 mb-2 flex items-center gap-1">
                        <AlertTriangle className="w-4 h-4" />
                        Warnings ({result.warnings.length})
                      </h4>
                      <div className="space-y-2">
                        {result.warnings.map((warning, index) => renderViolation(warning, index))}
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Collapsible.Content>
          )}
        </Collapsible.Root>
      </Card>
    );
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="w-5 h-5" />
            Validation Results
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            <span className="ml-2 text-muted-foreground">Validating hierarchy...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!validationResults) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="w-5 h-5" />
            Validation Results
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Alert>
            <Info className="h-4 w-4" />
            <AlertDescription>
              No validation results available. Upload data and configure hierarchy levels to see validation results.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Shield className="w-5 h-5" />
          Validation Results
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Overall Status */}
        <Alert variant={validationResults.overall_valid ? "default" : "destructive"}>
          {validationResults.overall_valid ? (
            <CheckCircle2 className="h-4 w-4" />
          ) : (
            <AlertCircle className="h-4 w-4" />
          )}
          <AlertDescription>
            {validationResults.overall_valid ? (
              <span className="text-green-600 font-medium">
                ✓ All validation rules passed successfully
              </span>
            ) : (
              <div>
                <span className="font-medium">Validation Issues Found:</span>
                <div className="mt-1 text-sm">
                  {validationResults.critical_violations > 0 && (
                    <div className="text-red-600">
                      • {validationResults.critical_violations} critical violation{validationResults.critical_violations !== 1 ? 's' : ''}
                    </div>
                  )}
                  {validationResults.warnings > 0 && (
                    <div className="text-yellow-600">
                      • {validationResults.warnings} warning{validationResults.warnings !== 1 ? 's' : ''}
                    </div>
                  )}
                </div>
              </div>
            )}
          </AlertDescription>
        </Alert>

        {/* Summary Stats */}
        <div className="grid grid-cols-3 gap-4 text-center">
          <div className="p-3 bg-muted/30 rounded">
            <div className="text-2xl font-bold text-green-600">{Object.keys(validationResults.rule_results).length}</div>
            <div className="text-sm text-muted-foreground">Rules Checked</div>
          </div>
          <div className="p-3 bg-muted/30 rounded">
            <div className="text-2xl font-bold text-red-600">{validationResults.critical_violations}</div>
            <div className="text-sm text-muted-foreground">Critical Issues</div>
          </div>
          <div className="p-3 bg-muted/30 rounded">
            <div className="text-2xl font-bold text-yellow-600">{validationResults.warnings}</div>
            <div className="text-sm text-muted-foreground">Warnings</div>
          </div>
        </div>

        {/* Rule Results */}
        <div>
          <h3 className="text-sm font-medium mb-3">Rule Details</h3>
          <ScrollArea className="h-96">
            <div className="space-y-2">
              {Object.entries(validationResults.rule_results).map(([ruleId, result]) => 
                renderRuleResult(ruleId, result)
              )}
            </div>
          </ScrollArea>
        </div>
      </CardContent>
    </Card>
  );
};
