"""
Training Models for Multi-Class Classification

Enhanced models to support both single and multiple label formats
"""

from typing import List, Optional, Union, Dict, Any
from pydantic import BaseModel, Field, validator
import logging

logger = logging.getLogger(__name__)

class MultiClassTrainingRequest(BaseModel):
    """Enhanced training request supporting multiple label formats"""
    
    file_id: str = Field(..., description="ID of the uploaded training file")
    text_column: str = Field(..., description="Name of the text column")
    classification_type: str = Field(default="multi-class", description="Type of classification")
    model_type: str = Field(default="custom", description="Type of model to train")
    
    # Label configuration - supports both formats
    label_columns: List[str] = Field(..., description="Label column(s) - single column for traditional format, multiple for binary format")
    label_format: Optional[str] = Field(None, description="'single' or 'multiple' - auto-detected if not specified")
    
    # Training parameters
    model_name: str = Field(..., description="Name for the trained model")
    training_params: Dict[str, Any] = Field(default_factory=dict, description="Training configuration parameters")
    
    # Dual data support
    dual_data_setup: Optional[bool] = Field(default=False, description="Whether using separate training and classification files")
    training_file_id: Optional[str] = Field(None, description="ID of the training file (for dual data setup)")
    classification_file_id: Optional[str] = Field(None, description="ID of the classification file (for dual data setup)")
    
    # Additional metadata
    custom_prompt: Optional[str] = Field(None, description="Custom prompt for LLM-based training")
    llm_provider: Optional[str] = Field(None, description="LLM provider (openai, anthropic, etc.)")
    llm_model: Optional[str] = Field(None, description="Specific LLM model to use")
    
    @validator('label_format', always=True)
    def detect_label_format(cls, v, values):
        """Auto-detect label format if not specified"""
        if v is None:
            label_columns = values.get('label_columns', [])
            # If multiple columns, assume binary format
            v = 'multiple' if len(label_columns) > 1 else 'single'
            logger.info(f"Auto-detected label format: {v} (based on {len(label_columns)} columns)")
        return v
    
    @validator('label_columns')
    def validate_label_columns(cls, v, values):
        """Validate label columns based on format"""
        if not v:
            raise ValueError("At least one label column is required")
        
        label_format = values.get('label_format')
        if label_format == 'single' and len(v) != 1:
            raise ValueError("Single format requires exactly one label column")
        elif label_format == 'multiple' and len(v) < 2:
            raise ValueError("Multiple format requires at least 2 binary columns")
        
        return v
    
    @validator('training_params')
    def validate_training_params(cls, v):
        """Validate and set default training parameters"""
        defaults = {
            'max_epochs': 3,
            'batch_size': 16,
            'learning_rate': 2e-5,
            'validation_split': 0.2,
            'strategy': 'softmax'
        }
        
        # Merge with defaults
        for key, default_value in defaults.items():
            if key not in v:
                v[key] = default_value
        
        return v

class MultiClassInferenceRequest(BaseModel):
    """Enhanced inference request supporting multiple label formats"""
    
    model_id: str = Field(..., description="ID of the trained model")
    file_id: str = Field(..., description="ID of the file to classify")
    text_column: str = Field(..., description="Name of the text column")
    classification_type: str = Field(default="multi-class", description="Type of classification")
    
    # Inference configuration
    config: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Inference configuration")
    
    @validator('config')
    def validate_config(cls, v):
        """Validate and set default inference configuration"""
        defaults = {
            'confidence_threshold': 0.5,
            'return_probabilities': True,
            'max_predictions': 1
        }
        
        # Merge with defaults
        for key, default_value in defaults.items():
            if key not in v:
                v[key] = default_value
        
        return v

class TrainingConfig(BaseModel):
    """Enhanced training configuration with label format support"""
    
    text_columns: List[str] = Field(..., description="Text column names")
    label_columns: List[str] = Field(..., description="Label column names (normalized to single column)")
    classification_type: str = Field(..., description="Classification type")
    training_method: str = Field(..., description="Training method")
    
    # Class information
    classes: List[str] = Field(..., description="List of class names")
    num_classes: Optional[int] = Field(None, description="Number of classes")
    
    # Label format metadata
    original_label_format: str = Field(..., description="Original label format (single/multiple)")
    original_label_columns: List[str] = Field(..., description="Original label columns before normalization")
    
    # Training parameters
    max_epochs: int = Field(default=3, description="Maximum training epochs")
    batch_size: int = Field(default=16, description="Training batch size")
    learning_rate: float = Field(default=2e-5, description="Learning rate")
    validation_split: float = Field(default=0.2, description="Validation split ratio")
    strategy: str = Field(default='softmax', description="Multi-class strategy")
    
    # Advanced parameters
    use_unsloth: bool = Field(default=False, description="Use Unsloth optimization")
    fp16: bool = Field(default=True, description="Use 16-bit precision")
    gradient_checkpointing: bool = Field(default=True, description="Use gradient checkpointing")
    warmup_steps: int = Field(default=100, description="Warmup steps")
    weight_decay: float = Field(default=0.01, description="Weight decay")
    
    # Class balancing
    class_weight_strategy: str = Field(default='balanced', description="Class weight strategy")
    custom_class_weights: Optional[Dict[str, float]] = Field(None, description="Custom class weights")
    
    @validator('num_classes', always=True)
    def set_num_classes(cls, v, values):
        """Set number of classes from classes list"""
        classes = values.get('classes', [])
        return len(classes) if classes else v

class TrainingResult(BaseModel):
    """Training result with label format information"""
    
    task_id: str = Field(..., description="Training task ID")
    model_id: Optional[str] = Field(None, description="Trained model ID")
    status: str = Field(..., description="Training status")
    
    # Label format information
    label_format_info: Dict[str, Any] = Field(..., description="Label format metadata")
    
    # Training metrics
    metrics: Optional[Dict[str, Any]] = Field(None, description="Training metrics")
    
    # Model information
    model_info: Optional[Dict[str, Any]] = Field(None, description="Model metadata")

class ValidationResult(BaseModel):
    """Data validation result"""
    
    valid: bool = Field(..., description="Whether data is valid")
    issues: List[str] = Field(default_factory=list, description="Validation issues")
    warnings: List[str] = Field(default_factory=list, description="Validation warnings")
    stats: Dict[str, Any] = Field(default_factory=dict, description="Data statistics")
    
    # Format-specific information
    detected_format: str = Field(..., description="Detected label format")
    recommended_format: Optional[str] = Field(None, description="Recommended format for optimal performance")

class LabelFormatInfo(BaseModel):
    """Label format metadata"""
    
    original_format: str = Field(..., description="Original label format")
    original_columns: List[str] = Field(..., description="Original label columns")
    normalized_column: str = Field(..., description="Normalized label column for training")
    class_names: List[str] = Field(..., description="Class names")
    validation_stats: Dict[str, Any] = Field(..., description="Validation statistics")
    
    # Conversion information
    conversion_applied: bool = Field(default=False, description="Whether format conversion was applied")
    rows_filtered: int = Field(default=0, description="Number of rows filtered during conversion")

# Request/Response models for API endpoints
class UniversalTrainingRequest(BaseModel):
    """Universal training request supporting all classification types"""
    
    file_id: str
    text_column: str
    label_columns: List[str]
    classification_type: str
    model_type: str = "custom"
    model_name: str
    
    # Optional label format specification
    label_format: Optional[str] = None
    
    # Training parameters
    training_params: Dict[str, Any] = Field(default_factory=dict)
    
    # Dual data support
    dual_data_setup: Optional[bool] = False
    training_file_id: Optional[str] = None
    classification_file_id: Optional[str] = None
    
    # LLM parameters
    custom_prompt: Optional[str] = None
    llm_provider: Optional[str] = None
    llm_model: Optional[str] = None

class UniversalInferenceRequest(BaseModel):
    """Universal inference request supporting all classification types"""
    
    model_id: str
    file_id: str
    text_column: str
    classification_type: str
    
    # Inference configuration
    config: Optional[Dict[str, Any]] = Field(default_factory=dict)

class TrainingResponse(BaseModel):
    """Training response with enhanced information"""
    
    task_id: str
    message: str
    label_format: Optional[Dict[str, Any]] = None
    validation_result: Optional[ValidationResult] = None

class InferenceResponse(BaseModel):
    """Inference response with format-aware results"""
    
    task_id: Optional[str] = None
    predictions: Optional[List[Dict[str, Any]]] = None
    summary: Optional[Dict[str, Any]] = None
    label_format: Optional[Dict[str, Any]] = None
