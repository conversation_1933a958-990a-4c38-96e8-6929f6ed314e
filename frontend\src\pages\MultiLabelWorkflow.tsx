/**
 * MultiLabelWorkflow.tsx
 *
 * Multi-label classification workflow page - streamlined unified implementation
 */

import { MultiLabelWorkflow as MultiLabelWorkflowComponent } from "@/components/classification/MultiLabelWorkflow";

const MultiLabelWorkflow = () => {
  return (
    <MultiLabelWorkflowComponent
      onComplete={(results) => {
        console.log('Multi-label classification workflow completed:', results);
        // Handle completion - could navigate to results page or show success message
      }}
    />
  );
};

export default MultiLabelWorkflow;
