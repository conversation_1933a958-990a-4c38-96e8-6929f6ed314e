"""Tenant models for ClassyWeb Universal Platform."""

from sqlalchemy import Column, Integer, String, Text, Boolean, DateTime, JSON, ForeignKey, Float
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from datetime import datetime, timezone

from ..database import Base


class Tenant(Base):
    """Tenant model for multi-tenancy support."""
    
    __tablename__ = "tenants"
    
    id = Column(String(36), primary_key=True, index=True)  # UUID
    name = Column(String(255), nullable=False, index=True)
    tier = Column(String(50), nullable=False, index=True)  # free, professional, enterprise, custom
    
    # Tenant settings and configuration
    settings = Column(JSON, default=dict)
    
    # Status and lifecycle
    is_active = Column(Boolean, default=True, index=True)
    admin_user_id = Column(Integer, ForeignKey("users.id"), nullable=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), default=func.now(), onupdate=func.now())
    
    # Billing and subscription
    subscription_start = Column(DateTime(timezone=True))
    subscription_end = Column(DateTime(timezone=True))
    billing_email = Column(String(255))
    
    # Relationships
    users = relationship("TenantUser", back_populates="tenant")
    resources = relationship("TenantResource", back_populates="tenant")
    audit_logs = relationship("AuditLog", back_populates="tenant")
    
    def __repr__(self):
        return f"<Tenant(id='{self.id}', name='{self.name}', tier='{self.tier}')>"
    
    def to_dict(self):
        """Convert tenant to dictionary."""
        return {
            "id": self.id,
            "name": self.name,
            "tier": self.tier,
            "settings": self.settings,
            "is_active": self.is_active,
            "admin_user_id": self.admin_user_id,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "subscription_start": self.subscription_start.isoformat() if self.subscription_start else None,
            "subscription_end": self.subscription_end.isoformat() if self.subscription_end else None,
            "billing_email": self.billing_email
        }


class TenantUser(Base):
    """Association between tenants and users."""
    
    __tablename__ = "tenant_users"
    
    id = Column(Integer, primary_key=True, index=True)
    tenant_id = Column(String(36), ForeignKey("tenants.id"), nullable=False, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    role = Column(String(50), nullable=False, default="member")  # admin, member, viewer
    
    # Status
    is_active = Column(Boolean, default=True, index=True)
    
    # Timestamps
    joined_at = Column(DateTime(timezone=True), default=func.now(), nullable=False)
    last_active_at = Column(DateTime(timezone=True))
    
    # Relationships
    tenant = relationship("Tenant", back_populates="users")
    user = relationship("User")  # Assuming User model exists
    
    def __repr__(self):
        return f"<TenantUser(tenant_id='{self.tenant_id}', user_id={self.user_id}, role='{self.role}')>"


class TenantResource(Base):
    """Resource quotas and usage for tenants."""
    
    __tablename__ = "tenant_resources"
    
    id = Column(Integer, primary_key=True, index=True)
    tenant_id = Column(String(36), ForeignKey("tenants.id"), nullable=False, index=True)
    resource_type = Column(String(50), nullable=False, index=True)  # storage, compute, api_calls, etc.
    
    # Quota limits
    limit_amount = Column(Integer, nullable=False)
    used_amount = Column(Integer, default=0, nullable=False)
    unit = Column(String(20), default="count")  # count, MB, GB, calls/month, etc.
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), default=func.now(), onupdate=func.now())
    
    # Relationships
    tenant = relationship("Tenant", back_populates="resources")
    
    def __repr__(self):
        return f"<TenantResource(tenant_id='{self.tenant_id}', type='{self.resource_type}', used={self.used_amount}/{self.limit_amount})>"
    
    @property
    def usage_percentage(self) -> float:
        """Calculate usage percentage."""
        if self.limit_amount == 0:
            return 0.0
        return (self.used_amount / self.limit_amount) * 100.0
    
    @property
    def is_exceeded(self) -> bool:
        """Check if quota is exceeded."""
        return self.used_amount > self.limit_amount
    
    def to_dict(self):
        """Convert resource to dictionary."""
        return {
            "id": self.id,
            "tenant_id": self.tenant_id,
            "resource_type": self.resource_type,
            "limit_amount": self.limit_amount,
            "used_amount": self.used_amount,
            "unit": self.unit,
            "usage_percentage": self.usage_percentage,
            "is_exceeded": self.is_exceeded,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }


class AuditLog(Base):
    """Audit log for tenant activities."""
    
    __tablename__ = "audit_logs"
    
    id = Column(Integer, primary_key=True, index=True)
    tenant_id = Column(String(36), ForeignKey("tenants.id"), nullable=False, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=True, index=True)
    
    # Action details
    action = Column(String(100), nullable=False, index=True)
    resource_type = Column(String(50), index=True)
    resource_id = Column(String(255), index=True)
    
    # Request details
    ip_address = Column(String(45))  # IPv6 compatible
    user_agent = Column(Text)
    request_id = Column(String(100), index=True)
    
    # Result details
    status = Column(String(20), nullable=False, index=True)  # success, failure, warning
    details = Column(JSON)
    error_message = Column(Text)
    
    # Timestamps
    timestamp = Column(DateTime(timezone=True), default=func.now(), nullable=False, index=True)
    
    # Relationships
    tenant = relationship("Tenant", back_populates="audit_logs")
    user = relationship("User")
    
    def __repr__(self):
        return f"<AuditLog(tenant_id='{self.tenant_id}', action='{self.action}', status='{self.status}')>"
    
    def to_dict(self):
        """Convert audit log to dictionary."""
        return {
            "id": self.id,
            "tenant_id": self.tenant_id,
            "user_id": self.user_id,
            "action": self.action,
            "resource_type": self.resource_type,
            "resource_id": self.resource_id,
            "ip_address": self.ip_address,
            "user_agent": self.user_agent,
            "request_id": self.request_id,
            "status": self.status,
            "details": self.details,
            "error_message": self.error_message,
            "timestamp": self.timestamp.isoformat() if self.timestamp else None
        }
