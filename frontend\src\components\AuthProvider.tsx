import { useEffect, ReactNode, useRef } from "react";
import { useAuthStore } from "@/store/authStore";
import { getCurrentUser } from "@/services/authApi";

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider = ({ children }: AuthProviderProps) => {
  const { token, user, isAuthenticated, setUser, setLoading, refreshAccessToken, logout } = useAuthStore();
  const initializingRef = useRef(false);

  useEffect(() => {
    const initializeAuth = async () => {
      // Prevent multiple simultaneous initialization attempts
      if (initializingRef.current) {
        console.log('Auth initialization already in progress, skipping...');
        return;
      }

      initializingRef.current = true;
      setLoading(true);

      try {
        // If we have a token, try to get current user
        if (token) {
          // Skip if we already have valid user data and are authenticated
          if (user && isAuthenticated) {
            console.log('AuthProvider: Already authenticated with user data, skipping initialization');
            return;
          }

          console.log('AuthProvider: Initializing with existing token');
          try {
            const userData = await getCurrentUser();
            setUser(userData);
            console.log('AuthProvider: User data fetched successfully');
          } catch (error: any) {
            console.log('AuthProvider: Token validation failed, attempting refresh...');

            // Only attempt refresh on 401 errors, not on network errors
            if (error.response?.status === 401) {
              try {
                const refreshSuccess = await refreshAccessToken();

                if (refreshSuccess) {
                  // Try to get user again with new token
                  try {
                    const user = await getCurrentUser();
                    setUser(user);
                    console.log('AuthProvider: User data fetched after refresh');
                  } catch (userError) {
                    console.error('AuthProvider: Failed to get user after refresh:', userError);
                    logout();
                  }
                } else {
                  console.log('AuthProvider: Refresh failed, logging out');
                  logout();
                }
              } catch (refreshError) {
                console.error('AuthProvider: Refresh error:', refreshError);
                logout();
              }
            } else {
              console.error('AuthProvider: Network or other error, not attempting refresh:', error);
              // Don't logout on network errors, just log the error
            }
          }
        } else {
          console.log('AuthProvider: No token found, skipping initialization');
        }
      } catch (error) {
        console.error('AuthProvider: Initialization error:', error);
        // Only logout on auth-related errors, not network errors
        if (error instanceof Error && error.message.includes('401')) {
          logout();
        }
      } finally {
        setLoading(false);
        initializingRef.current = false;
      }
    };

    // Add a small delay to prevent rapid-fire requests on app startup
    const timeoutId = setTimeout(() => {
      initializeAuth();
    }, 100);

    return () => {
      clearTimeout(timeoutId);
      initializingRef.current = false;
    };
  }, [token]); // Only depend on token changes, not the store functions

  return <>{children}</>;
};
