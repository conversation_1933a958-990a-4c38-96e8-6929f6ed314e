"""
Comprehensive end-to-end test for the beginner workflow.

This test validates the complete beginner workflow from file upload
through analysis, recommendations, and training/inference.
"""

import pytest
import tempfile
import pandas as pd
from pathlib import Path
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from app.main import app
from app.database import get_db, create_user, get_user_by_email
from app.auth import create_access_token
from app.models.auth import UserCreate


@pytest.fixture
def client():
    """Create test client."""
    return TestClient(app)


@pytest.fixture
def test_user(client):
    """Create a test user and return authentication token."""
    # Create test user
    user_data = UserCreate(
        email="<EMAIL>",
        password="testpassword123",
        username="testuser"
    )
    
    # Register user
    response = client.post("/auth/register", json=user_data.model_dump())
    if response.status_code != 201:
        print(f"Registration failed: {response.status_code} - {response.text}")
        # Try to continue with login if user already exists
        pass
    
    # Login to get token
    login_response = client.post("/auth/login", json={
        "email": "<EMAIL>",
        "password": "testpassword123"
    })

    if login_response.status_code != 200:
        print(f"Login failed: {login_response.status_code} - {login_response.text}")
        # Return a dummy token for testing
        return {"Authorization": "Bearer dummy-token-for-testing"}
    
    token = login_response.json()["access_token"]
    return {"Authorization": f"Bearer {token}"}


@pytest.fixture
def sample_csv_file():
    """Create a sample CSV file for testing."""
    # Create sample data for multi-class classification
    data = {
        'text': [
            'I love this product, it works great!',
            'This is terrible, worst purchase ever.',
            'Average quality, nothing special.',
            'Excellent service and fast delivery!',
            'Poor quality, broke after one day.',
            'Good value for money.',
            'Outstanding performance!',
            'Not worth the price.',
            'Decent product overall.',
            'Amazing quality and design!'
        ],
        'sentiment': [
            'positive', 'negative', 'neutral', 'positive', 'negative',
            'positive', 'positive', 'negative', 'neutral', 'positive'
        ],
        'category': [
            'electronics', 'electronics', 'clothing', 'service', 'electronics',
            'clothing', 'electronics', 'clothing', 'service', 'electronics'
        ]
    }
    
    df = pd.DataFrame(data)
    
    # Create temporary file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
        df.to_csv(f.name, index=False)
        return f.name


class TestBeginnerWorkflow:
    """Test class for the complete beginner workflow."""
    
    def test_complete_workflow_llm_classification(self, client, test_user, sample_csv_file):
        """Test complete workflow with LLM classification."""
        
        # Step 1: Upload file
        with open(sample_csv_file, 'rb') as f:
            upload_response = client.post(
                "/files/upload",
                files={"file": ("test_data.csv", f, "text/csv")},
                headers=test_user
            )
        
        assert upload_response.status_code == 200
        file_data = upload_response.json()
        file_id = file_data["file_id"]
        
        # Verify file upload
        assert "file_id" in file_data
        assert file_data["filename"] == "test_data.csv"
        assert file_data["num_rows"] == 10
        assert len(file_data["columns"]) == 3
        
        # Step 2: Analyze file structure
        analysis_response = client.post(
            f"/files/{file_id}/analyze",
            json={},
            headers=test_user
        )
        
        assert analysis_response.status_code == 200
        analysis_data = analysis_response.json()
        
        # Verify analysis results
        assert "detected_structure" in analysis_data
        assert "confidence" in analysis_data
        assert "suggestions" in analysis_data
        assert "preview" in analysis_data
        assert "column_analysis" in analysis_data
        
        # Step 3: Get workflow recommendations
        recommendations_response = client.post(
            "/analysis/workflow-recommendations",
            json={"analysis": analysis_data},
            headers=test_user
        )
        
        assert recommendations_response.status_code == 200
        recommendations = recommendations_response.json()
        
        # Verify recommendations
        assert "recommended_workflow" in recommendations
        assert "reasoning" in recommendations
        assert "configuration_hints" in recommendations
        
        # Step 4: Start LLM classification
        llm_config = {
            "file_id": file_id,
            "original_filename": "test_data.csv",
            "text_columns": ["text"],
            "hierarchy": {
                "classification_type": "multi-class",
                "labels": ["positive", "negative", "neutral"]
            },
            "llm_config": {
                "provider": "OpenAI",
                "endpoint": "",
                "model_name": "gpt-3.5-turbo",
                "api_key": None
            },
            "hierarchy_config_id": None
        }
        
        classification_response = client.post(
            "/api/v2/classification/llm/classify-file",
            json=llm_config,
            headers=test_user
        )
        
        # Note: This might fail if no API keys are configured, which is expected in test environment
        # We'll check for either success or a specific error about missing API keys
        if classification_response.status_code == 200:
            task_data = classification_response.json()
            assert "task_id" in task_data
            
            # Step 5: Check task status
            task_id = task_data["task_id"]
            status_response = client.get(
                f"/tasks/{task_id}",
                headers=test_user
            )
            
            assert status_response.status_code == 200
            status_data = status_response.json()
            assert "task_id" in status_data
            assert "status" in status_data
        else:
            # Check if it's an expected error (missing API keys, etc.)
            assert classification_response.status_code in [400, 500]
            error_detail = classification_response.json().get("detail", "")
            # These are expected errors in test environment
            expected_errors = ["api key", "provider", "configuration", "model"]
            assert any(err in error_detail.lower() for err in expected_errors)
    
    def test_complete_workflow_custom_training(self, client, test_user, sample_csv_file):
        """Test complete workflow with custom model training."""
        
        # Step 1: Upload file
        with open(sample_csv_file, 'rb') as f:
            upload_response = client.post(
                "/files/upload",
                files={"file": ("test_data.csv", f, "text/csv")},
                headers=test_user
            )
        
        assert upload_response.status_code == 200
        file_data = upload_response.json()
        file_id = file_data["file_id"]
        
        # Step 2: Analyze file structure
        analysis_response = client.post(
            f"/files/{file_id}/analyze",
            json={},
            headers=test_user
        )
        
        assert analysis_response.status_code == 200
        analysis_data = analysis_response.json()
        
        # Step 3: Start custom training
        training_config = {
            "file_id": file_id,
            "classification_type": "multi-class",
            "model_type": "custom",
            "text_column": "text",
            "label_columns": ["sentiment"],
            "training_params": {
                "epochs": 1,  # Small number for testing
                "batch_size": 2,
                "learning_rate": 0.001
            }
        }
        
        training_response = client.post(
            "/api/v2/classification/universal/train",
            json=training_config,
            headers=test_user
        )
        
        assert training_response.status_code == 200
        training_data = training_response.json()
        
        # Verify training response
        assert "task_id" in training_data
        assert "status" in training_data
        assert "config_id" in training_data
        assert "session_id" in training_data
        
        # Step 4: Check training status
        task_id = training_data["task_id"]
        status_response = client.get(
            f"/tasks/{task_id}",
            headers=test_user
        )
        
        assert status_response.status_code == 200
        status_data = status_response.json()
        assert "task_id" in status_data
        assert "status" in status_data
    
    def test_error_handling(self, client, test_user):
        """Test error handling in the workflow."""
        
        # Test with non-existent file
        analysis_response = client.post(
            "/files/nonexistent/analyze",
            json={},
            headers=test_user
        )
        
        assert analysis_response.status_code == 404
        
        # Test workflow recommendations with invalid data
        invalid_recommendations_response = client.post(
            "/analysis/workflow-recommendations",
            json={"analysis": {}},
            headers=test_user
        )
        
        # Should handle gracefully (might return default recommendations or error)
        assert invalid_recommendations_response.status_code in [200, 400, 500]
    
    def test_authentication_required(self, client, sample_csv_file):
        """Test that authentication is required for workflow endpoints."""

        # Try to upload without authentication
        with open(sample_csv_file, 'rb') as f:
            upload_response = client.post(
                "/files/upload",
                files={"file": ("test_data.csv", f, "text/csv")}
            )

        # Should work with optional auth (returns 200 but might have limited functionality)
        assert upload_response.status_code in [200, 401]

        # Try to start training without authentication
        training_response = client.post(
            "/api/v2/classification/universal/train",
            json={
                "file_id": "test",
                "classification_type": "binary",
                "model_type": "custom",
                "text_column": "text",
                "label_columns": ["label"]
            }
        )

        assert training_response.status_code == 401

    def test_basic_functionality(self, client):
        """Test basic API functionality without complex workflows."""
        # Test health check or basic endpoint
        response = client.get("/")
        # Should return some response (200, 404, etc. - just not crash)
        assert response.status_code in [200, 404, 405]


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
