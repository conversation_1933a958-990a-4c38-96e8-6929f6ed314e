/**
 * HierarchicalMetricsDisplay.tsx
 * 
 * Component for displaying hierarchical classification metrics
 */

import React, { useMemo } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { 
  <PERSON><PERSON><PERSON>, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell
} from 'recharts';
import { 
  TreePine, 
  Target, 
  AlertTriangle, 
  CheckCircle2,
  TrendingUp,
  TrendingDown,
  Minus
} from "lucide-react";

interface HierarchicalMetrics {
  // Overall metrics
  hierarchical_precision: number;
  hierarchical_recall: number;
  hierarchical_f1: number;
  path_accuracy: number;
  partial_path_accuracy: number;
  tree_induced_error: number;
  
  // Level-wise metrics
  level_wise_accuracy: Record<string, number>;
  level_wise_precision: Record<string, number>;
  level_wise_recall: Record<string, number>;
  level_wise_f1: Record<string, number>;
  
  // Constraint metrics
  constraint_violations: number;
  constraint_violation_rate: number;
  constraint_violations_by_type: Record<string, number>;
  
  // Additional metrics
  depth_accuracy: Record<string, number>;
  ancestor_accuracy: number;
  leaf_accuracy: number;
  
  // Performance by hierarchy level
  performance_by_level: Array<{
    level: string;
    accuracy: number;
    precision: number;
    recall: number;
    f1: number;
    support: number;
  }>;
  
  // Confusion data
  confusion_by_level: Record<string, any>;
}

interface HierarchicalMetricsDisplayProps {
  metrics: HierarchicalMetrics;
  hierarchyLevels: string[];
  className?: string;
}

export const HierarchicalMetricsDisplay: React.FC<HierarchicalMetricsDisplayProps> = ({
  metrics,
  hierarchyLevels,
  className
}) => {
  const formatMetric = (value: number): string => {
    return (value * 100).toFixed(1) + '%';
  };

  const getMetricColor = (value: number): string => {
    if (value >= 0.8) return 'text-green-600';
    if (value >= 0.6) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getMetricIcon = (value: number) => {
    if (value >= 0.8) return <CheckCircle2 className="w-4 h-4 text-green-600" />;
    if (value >= 0.6) return <Minus className="w-4 h-4 text-yellow-600" />;
    return <AlertTriangle className="w-4 h-4 text-red-600" />;
  };

  // Prepare chart data
  const levelPerformanceData = useMemo(() => {
    return metrics.performance_by_level.map(level => ({
      level: level.level,
      accuracy: level.accuracy * 100,
      precision: level.precision * 100,
      recall: level.recall * 100,
      f1: level.f1 * 100,
      support: level.support
    }));
  }, [metrics.performance_by_level]);

  const constraintViolationData = useMemo(() => {
    return Object.entries(metrics.constraint_violations_by_type).map(([type, count]) => ({
      type: type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
      count,
      percentage: (count / metrics.constraint_violations) * 100
    }));
  }, [metrics.constraint_violations_by_type, metrics.constraint_violations]);

  const depthAccuracyData = useMemo(() => {
    return Object.entries(metrics.depth_accuracy).map(([depth, accuracy]) => ({
      depth: `Depth ${depth}`,
      accuracy: accuracy * 100
    }));
  }, [metrics.depth_accuracy]);

  const COLORS = ['#3b82f6', '#ef4444', '#f59e0b', '#10b981', '#8b5cf6', '#f97316'];

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Overall Metrics */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TreePine className="w-5 h-5" />
            Hierarchical Classification Metrics
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
            <div className="text-center space-y-2">
              <div className="flex items-center justify-center gap-1">
                {getMetricIcon(metrics.hierarchical_f1)}
                <span className="text-sm font-medium">H-F1 Score</span>
              </div>
              <div className={`text-2xl font-bold ${getMetricColor(metrics.hierarchical_f1)}`}>
                {formatMetric(metrics.hierarchical_f1)}
              </div>
              <Progress value={metrics.hierarchical_f1 * 100} className="h-2" />
            </div>

            <div className="text-center space-y-2">
              <div className="flex items-center justify-center gap-1">
                {getMetricIcon(metrics.hierarchical_precision)}
                <span className="text-sm font-medium">H-Precision</span>
              </div>
              <div className={`text-2xl font-bold ${getMetricColor(metrics.hierarchical_precision)}`}>
                {formatMetric(metrics.hierarchical_precision)}
              </div>
              <Progress value={metrics.hierarchical_precision * 100} className="h-2" />
            </div>

            <div className="text-center space-y-2">
              <div className="flex items-center justify-center gap-1">
                {getMetricIcon(metrics.hierarchical_recall)}
                <span className="text-sm font-medium">H-Recall</span>
              </div>
              <div className={`text-2xl font-bold ${getMetricColor(metrics.hierarchical_recall)}`}>
                {formatMetric(metrics.hierarchical_recall)}
              </div>
              <Progress value={metrics.hierarchical_recall * 100} className="h-2" />
            </div>

            <div className="text-center space-y-2">
              <div className="flex items-center justify-center gap-1">
                {getMetricIcon(metrics.path_accuracy)}
                <span className="text-sm font-medium">Path Accuracy</span>
              </div>
              <div className={`text-2xl font-bold ${getMetricColor(metrics.path_accuracy)}`}>
                {formatMetric(metrics.path_accuracy)}
              </div>
              <Progress value={metrics.path_accuracy * 100} className="h-2" />
            </div>

            <div className="text-center space-y-2">
              <div className="flex items-center justify-center gap-1">
                {getMetricIcon(metrics.ancestor_accuracy)}
                <span className="text-sm font-medium">Ancestor Acc.</span>
              </div>
              <div className={`text-2xl font-bold ${getMetricColor(metrics.ancestor_accuracy)}`}>
                {formatMetric(metrics.ancestor_accuracy)}
              </div>
              <Progress value={metrics.ancestor_accuracy * 100} className="h-2" />
            </div>

            <div className="text-center space-y-2">
              <div className="flex items-center justify-center gap-1">
                <AlertTriangle className="w-4 h-4 text-orange-600" />
                <span className="text-sm font-medium">Tree Error</span>
              </div>
              <div className="text-2xl font-bold text-orange-600">
                {formatMetric(metrics.tree_induced_error)}
              </div>
              <Progress value={metrics.tree_induced_error * 100} className="h-2" />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Detailed Metrics Tabs */}
      <Tabs defaultValue="levels" className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="levels">Level Performance</TabsTrigger>
          <TabsTrigger value="constraints">Constraints</TabsTrigger>
          <TabsTrigger value="depth">Depth Analysis</TabsTrigger>
          <TabsTrigger value="comparison">Comparison</TabsTrigger>
        </TabsList>

        {/* Level Performance */}
        <TabsContent value="levels" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Performance by Hierarchy Level</CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={levelPerformanceData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="level" />
                  <YAxis domain={[0, 100]} />
                  <Tooltip formatter={(value) => `${value.toFixed(1)}%`} />
                  <Bar dataKey="accuracy" fill="#3b82f6" name="Accuracy" />
                  <Bar dataKey="precision" fill="#10b981" name="Precision" />
                  <Bar dataKey="recall" fill="#f59e0b" name="Recall" />
                  <Bar dataKey="f1" fill="#8b5cf6" name="F1 Score" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          {/* Level-wise detailed metrics */}
          <div className="grid gap-4">
            {metrics.performance_by_level.map((level, index) => (
              <Card key={level.level}>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between mb-3">
                    <h4 className="font-semibold">{level.level}</h4>
                    <Badge variant="outline">{level.support} samples</Badge>
                  </div>
                  <div className="grid grid-cols-4 gap-4">
                    <div className="text-center">
                      <div className="text-sm text-muted-foreground">Accuracy</div>
                      <div className={`font-bold ${getMetricColor(level.accuracy)}`}>
                        {formatMetric(level.accuracy)}
                      </div>
                    </div>
                    <div className="text-center">
                      <div className="text-sm text-muted-foreground">Precision</div>
                      <div className={`font-bold ${getMetricColor(level.precision)}`}>
                        {formatMetric(level.precision)}
                      </div>
                    </div>
                    <div className="text-center">
                      <div className="text-sm text-muted-foreground">Recall</div>
                      <div className={`font-bold ${getMetricColor(level.recall)}`}>
                        {formatMetric(level.recall)}
                      </div>
                    </div>
                    <div className="text-center">
                      <div className="text-sm text-muted-foreground">F1 Score</div>
                      <div className={`font-bold ${getMetricColor(level.f1)}`}>
                        {formatMetric(level.f1)}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        {/* Constraint Violations */}
        <TabsContent value="constraints" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>Constraint Violation Summary</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="text-center space-y-2">
                  <div className="text-3xl font-bold text-red-600">
                    {metrics.constraint_violations}
                  </div>
                  <div className="text-sm text-muted-foreground">Total Violations</div>
                  <div className="text-lg font-semibold text-red-600">
                    {formatMetric(metrics.constraint_violation_rate)} violation rate
                  </div>
                </div>

                <div className="space-y-2">
                  {constraintViolationData.map((item, index) => (
                    <div key={item.type} className="flex items-center justify-between">
                      <span className="text-sm">{item.type}</span>
                      <div className="flex items-center gap-2">
                        <span className="text-sm font-medium">{item.count}</span>
                        <Badge variant="destructive" className="text-xs">
                          {item.percentage.toFixed(1)}%
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Violation Distribution</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={250}>
                  <PieChart>
                    <Pie
                      data={constraintViolationData}
                      cx="50%"
                      cy="50%"
                      outerRadius={80}
                      dataKey="count"
                      label={({ type, percentage }) => `${type}: ${percentage.toFixed(1)}%`}
                    >
                      {constraintViolationData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Depth Analysis */}
        <TabsContent value="depth" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Accuracy by Hierarchy Depth</CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={depthAccuracyData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="depth" />
                  <YAxis domain={[0, 100]} />
                  <Tooltip formatter={(value) => `${value.toFixed(1)}%`} />
                  <Line 
                    type="monotone" 
                    dataKey="accuracy" 
                    stroke="#3b82f6" 
                    strokeWidth={3}
                    dot={{ fill: '#3b82f6', strokeWidth: 2, r: 6 }}
                  />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {formatMetric(metrics.partial_path_accuracy)}
                </div>
                <div className="text-sm text-muted-foreground">Partial Path Accuracy</div>
                <p className="text-xs text-muted-foreground mt-1">
                  Accuracy when allowing partial correct paths
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-green-600">
                  {formatMetric(metrics.leaf_accuracy)}
                </div>
                <div className="text-sm text-muted-foreground">Leaf Accuracy</div>
                <p className="text-xs text-muted-foreground mt-1">
                  Accuracy at the most specific level
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-purple-600">
                  {formatMetric(metrics.ancestor_accuracy)}
                </div>
                <div className="text-sm text-muted-foreground">Ancestor Accuracy</div>
                <p className="text-xs text-muted-foreground mt-1">
                  Accuracy of parent-level predictions
                </p>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Comparison */}
        <TabsContent value="comparison" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Metric Comparison</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {[
                  { name: 'Hierarchical F1', value: metrics.hierarchical_f1, benchmark: 0.75 },
                  { name: 'Path Accuracy', value: metrics.path_accuracy, benchmark: 0.70 },
                  { name: 'Ancestor Accuracy', value: metrics.ancestor_accuracy, benchmark: 0.80 },
                  { name: 'Constraint Compliance', value: 1 - metrics.constraint_violation_rate, benchmark: 0.95 }
                ].map((metric, index) => (
                  <div key={metric.name} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="font-medium">{metric.name}</span>
                      <div className="flex items-center gap-2">
                        <span className={`font-bold ${getMetricColor(metric.value)}`}>
                          {formatMetric(metric.value)}
                        </span>
                        {metric.value >= metric.benchmark ? (
                          <TrendingUp className="w-4 h-4 text-green-600" />
                        ) : (
                          <TrendingDown className="w-4 h-4 text-red-600" />
                        )}
                      </div>
                    </div>
                    <div className="relative">
                      <Progress value={metric.value * 100} className="h-3" />
                      <div 
                        className="absolute top-0 h-3 w-0.5 bg-gray-400"
                        style={{ left: `${metric.benchmark * 100}%` }}
                      />
                    </div>
                    <div className="flex justify-between text-xs text-muted-foreground">
                      <span>0%</span>
                      <span>Benchmark: {formatMetric(metric.benchmark)}</span>
                      <span>100%</span>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};
