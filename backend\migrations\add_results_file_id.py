"""
Migration script to add results_file_id column to training_sessions table.
This supports dual data workflow where classification results are stored separately.
"""

import sqlite3
import logging
from pathlib import Path

logger = logging.getLogger(__name__)

def migrate_database(db_path: str):
    """Add results_file_id column to training_sessions table."""
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check if column already exists
        cursor.execute("PRAGMA table_info(training_sessions)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'results_file_id' not in columns:
            logger.info("Adding results_file_id column to training_sessions table")
            cursor.execute("""
                ALTER TABLE training_sessions 
                ADD COLUMN results_file_id TEXT REFERENCES files(id)
            """)
            conn.commit()
            logger.info("Successfully added results_file_id column")
        else:
            logger.info("results_file_id column already exists")
        
        conn.close()
        
    except Exception as e:
        logger.error(f"Migration failed: {e}")
        raise

if __name__ == "__main__":
    # Default database path
    db_path = Path(__file__).parent.parent / "classyweb.db"
    migrate_database(str(db_path))
    print("Migration completed successfully")
