"""Example Simple Classifier Plugin for ClassyWeb Universal Platform.

This is a demonstration plugin that shows how to implement a custom classification engine
using the ClassyWeb plugin architecture.
"""

import logging
from typing import Dict, List, Any, Optional
import asyncio
import random

from ..base_plugin import ClassificationPlugin, PluginMetadata, PluginCapabilities, PluginType

logger = logging.getLogger(__name__)


class SimpleClassifierPlugin(ClassificationPlugin):
    """A simple example classification plugin using keyword-based classification."""
    
    def __init__(self, config: Dict[str, Any] = None):
        super().__init__(config)
        self.keyword_rules = config.get('keyword_rules', {}) if config else {}
        self.default_labels = config.get('default_labels', ['general']) if config else ['general']
        
    def get_metadata(self) -> PluginMetadata:
        """Return plugin metadata."""
        return PluginMetadata(
            name="simple_classifier",
            version="1.0.0",
            description="Simple keyword-based classification engine for demonstration",
            author="ClassyWeb Team",
            plugin_type=PluginType.CLASSIFICATION_ENGINE,
            capabilities=["keyword_matching", "rule_based", "fast_classification"],
            dependencies=[],
            supported_formats=["text"],
            enterprise_only=False,
            license="MIT",
            documentation_url="https://docs.classyweb.com/plugins/simple-classifier",
            support_url="https://support.classyweb.com"
        )
    
    def get_capabilities(self) -> PluginCapabilities:
        """Return plugin capabilities."""
        return PluginCapabilities(
            supports_hierarchical=True,
            supports_flat=True,
            supports_multi_modal=False,
            supports_streaming=True,
            supports_batch=True,
            max_batch_size=10000,
            estimated_speed_ms=50,  # Very fast
            memory_requirements_mb=64,
            gpu_required=False
        )
    
    async def initialize(self) -> bool:
        """Initialize the plugin."""
        try:
            logger.info("Initializing Simple Classifier Plugin...")
            
            # Set up default keyword rules if none provided
            if not self.keyword_rules:
                self.keyword_rules = {
                    'technology': ['software', 'computer', 'tech', 'digital', 'code', 'programming'],
                    'business': ['company', 'market', 'sales', 'revenue', 'customer', 'business'],
                    'science': ['research', 'study', 'experiment', 'analysis', 'data', 'scientific'],
                    'health': ['medical', 'health', 'patient', 'treatment', 'diagnosis', 'healthcare'],
                    'education': ['school', 'student', 'learning', 'education', 'academic', 'university']
                }
            
            # Simulate initialization time
            await asyncio.sleep(0.1)
            
            self.is_initialized = True
            logger.info("Simple Classifier Plugin initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error initializing Simple Classifier Plugin: {str(e)}")
            return False
    
    async def cleanup(self) -> bool:
        """Cleanup plugin resources."""
        try:
            logger.info("Cleaning up Simple Classifier Plugin...")
            self.is_initialized = False
            return True
            
        except Exception as e:
            logger.error(f"Error cleaning up Simple Classifier Plugin: {str(e)}")
            return False
    
    async def classify(
        self,
        texts: List[str],
        hierarchy_config: Optional[Dict[str, Any]] = None,
        confidence_threshold: float = 0.5
    ) -> List[Dict[str, Any]]:
        """Classify texts using keyword matching."""
        try:
            if not self.is_initialized:
                raise RuntimeError("Plugin not initialized")
            
            results = []
            
            for text in texts:
                text_lower = text.lower()
                matched_labels = []
                label_scores = {}
                
                # Check each keyword rule
                for label, keywords in self.keyword_rules.items():
                    score = 0.0
                    matches = 0
                    
                    for keyword in keywords:
                        if keyword.lower() in text_lower:
                            matches += 1
                            # Simple scoring based on keyword frequency
                            score += text_lower.count(keyword.lower()) * 0.1
                    
                    # Normalize score based on text length and keyword matches
                    if matches > 0:
                        normalized_score = min(score / len(text.split()) * 10, 1.0)
                        if normalized_score >= confidence_threshold:
                            matched_labels.append(label)
                            label_scores[label] = normalized_score
                
                # If no labels matched, use default
                if not matched_labels:
                    matched_labels = self.default_labels
                    label_scores = {label: 0.3 for label in self.default_labels}
                
                # Calculate overall confidence
                overall_confidence = max(label_scores.values()) if label_scores else 0.3
                
                results.append({
                    'text': text,
                    'labels': matched_labels,
                    'confidence': overall_confidence,
                    'label_scores': label_scores,
                    'processing_details': {
                        'plugin_name': 'simple_classifier',
                        'method': 'keyword_matching',
                        'keywords_matched': sum(
                            1 for label in matched_labels 
                            for keyword in self.keyword_rules.get(label, [])
                            if keyword.lower() in text.lower()
                        )
                    }
                })
            
            return results
            
        except Exception as e:
            logger.error(f"Error classifying texts with Simple Classifier Plugin: {str(e)}")
            raise
    
    async def train(
        self,
        texts: List[str],
        labels: List[List[str]],
        hierarchy_config: Optional[Dict[str, Any]] = None,
        training_params: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Train the classification model (update keyword rules)."""
        try:
            if not self.is_initialized:
                raise RuntimeError("Plugin not initialized")
            
            logger.info(f"Training Simple Classifier with {len(texts)} examples...")
            
            # Extract keywords from training data
            new_keyword_rules = {}
            
            for text, text_labels in zip(texts, labels):
                words = text.lower().split()
                
                for label in text_labels:
                    if label not in new_keyword_rules:
                        new_keyword_rules[label] = set()
                    
                    # Add significant words (length > 3) as potential keywords
                    for word in words:
                        if len(word) > 3 and word.isalpha():
                            new_keyword_rules[label].add(word)
            
            # Convert sets to lists and limit to top keywords per label
            for label, keywords in new_keyword_rules.items():
                # Keep top 10 most common keywords per label
                keyword_list = list(keywords)[:10]
                new_keyword_rules[label] = keyword_list
            
            # Update keyword rules
            self.keyword_rules.update(new_keyword_rules)
            
            # Simulate training time
            await asyncio.sleep(0.5)
            
            training_result = {
                'status': 'completed',
                'labels_trained': list(new_keyword_rules.keys()),
                'total_keywords': sum(len(keywords) for keywords in new_keyword_rules.values()),
                'training_examples': len(texts),
                'training_time_ms': 500,
                'model_version': '1.0.0'
            }
            
            logger.info("Simple Classifier training completed successfully")
            return training_result
            
        except Exception as e:
            logger.error(f"Error training Simple Classifier Plugin: {str(e)}")
            raise
    
    def validate_config(self, config: Dict[str, Any]) -> bool:
        """Validate plugin configuration."""
        try:
            # Check if keyword_rules is a dictionary
            if 'keyword_rules' in config:
                if not isinstance(config['keyword_rules'], dict):
                    return False
                
                # Check if all values are lists of strings
                for label, keywords in config['keyword_rules'].items():
                    if not isinstance(keywords, list):
                        return False
                    if not all(isinstance(keyword, str) for keyword in keywords):
                        return False
            
            # Check if default_labels is a list of strings
            if 'default_labels' in config:
                if not isinstance(config['default_labels'], list):
                    return False
                if not all(isinstance(label, str) for label in config['default_labels']):
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error validating Simple Classifier config: {str(e)}")
            return False
    
    def get_health_status(self) -> Dict[str, Any]:
        """Return plugin health status."""
        base_status = super().get_health_status()
        
        # Add plugin-specific health information
        base_status.update({
            "keyword_rules_count": len(self.keyword_rules),
            "total_keywords": sum(len(keywords) for keywords in self.keyword_rules.values()),
            "labels_supported": list(self.keyword_rules.keys()),
            "memory_usage_mb": 1,  # Very lightweight
            "last_classification_time": None  # TODO: Track last usage
        })
        
        return base_status


# Example configuration for the Simple Classifier Plugin
EXAMPLE_CONFIG = {
    "keyword_rules": {
        "technology": ["software", "computer", "tech", "digital", "AI", "machine learning"],
        "business": ["company", "market", "sales", "revenue", "customer", "profit"],
        "science": ["research", "study", "experiment", "analysis", "hypothesis", "data"],
        "health": ["medical", "health", "patient", "treatment", "diagnosis", "medicine"],
        "education": ["school", "student", "learning", "education", "academic", "university"]
    },
    "default_labels": ["general"],
    "confidence_boost": 0.1  # Boost confidence for keyword matches
}
