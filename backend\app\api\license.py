"""License Management API for ClassyWeb ML Platform.

This module provides license validation, feature checking, and usage tracking functionality.
"""

import logging
import traceback
from typing import Dict, Any, Optional, List
from datetime import datetime, timezone
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from pydantic import BaseModel, Field

from ..database import get_db, User
from ..auth import get_current_user
from ..license_manager import license_manager, LicenseTypeEnum

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/license", tags=["license"])


# --- Request/Response Models ---

class FeatureValidationRequest(BaseModel):
    feature: str = Field(..., description="Feature to validate")
    operation: Optional[str] = Field(None, description="Specific operation to validate")


class UsageUpdateRequest(BaseModel):
    operation: str = Field(..., description="Operation type")
    count: int = Field(default=1, description="Usage count to add")


class DeploymentValidationRequest(BaseModel):
    deployment_type: str = Field(..., description="Type of deployment to validate")


class LicenseResponse(BaseModel):
    id: str
    user_id: int
    license_type: str
    license_key: str
    status: str
    max_models: Optional[int]
    max_monthly_inferences: Optional[int]
    feature_flags: Dict[str, Any]
    price_paid_cents: Optional[int]
    billing_cycle: str
    created_at: str
    expires_at: Optional[str]
    total_models_trained: int
    current_month_inferences: int


class LicenseFeaturesResponse(BaseModel):
    model_export: bool
    api_deployment: bool
    batch_processing: bool
    cloud_deployment: bool
    edge_deployment: bool
    enterprise_features: bool
    max_models: int
    max_monthly_inferences: int
    priority_support: bool
    custom_branding: bool
    advanced_analytics: bool


class UsageStatsResponse(BaseModel):
    models_trained: int
    monthly_inferences: int
    deployments_active: int
    api_calls_today: int
    storage_used_mb: int


class ValidationResult(BaseModel):
    valid: bool
    feature_available: bool
    usage_allowed: bool
    error_message: Optional[str] = None
    upgrade_required: Optional[bool] = None
    limits_exceeded: Optional[List[str]] = None


class LicenseTier(BaseModel):
    name: str
    price_monthly: int
    price_yearly: int
    features: LicenseFeaturesResponse
    description: str
    popular: Optional[bool] = False


class LicenseTiersResponse(BaseModel):
    tiers: List[LicenseTier]


class DeploymentLimitsResponse(BaseModel):
    max_deployments: int
    current_deployments: int
    available_types: List[str]
    restrictions: Dict[str, Any]


# --- Helper Functions ---

def get_license_features(license_type: str) -> LicenseFeaturesResponse:
    """Get features available for a license type."""
    feature_matrix = {
        'personal': {
            'model_export': True,
            'api_deployment': False,
            'batch_processing': True,
            'cloud_deployment': False,
            'edge_deployment': False,
            'enterprise_features': False,
            'max_models': 3,
            'max_monthly_inferences': 1000,
            'priority_support': False,
            'custom_branding': False,
            'advanced_analytics': False
        },
        'professional': {
            'model_export': True,
            'api_deployment': True,
            'batch_processing': True,
            'cloud_deployment': False,
            'edge_deployment': False,
            'enterprise_features': False,
            'max_models': 25,
            'max_monthly_inferences': 100000,
            'priority_support': True,
            'custom_branding': False,
            'advanced_analytics': True
        },
        'enterprise': {
            'model_export': True,
            'api_deployment': True,
            'batch_processing': True,
            'cloud_deployment': True,
            'edge_deployment': True,
            'enterprise_features': True,
            'max_models': -1,  # Unlimited
            'max_monthly_inferences': -1,  # Unlimited
            'priority_support': True,
            'custom_branding': True,
            'advanced_analytics': True
        }
    }
    
    features = feature_matrix.get(license_type, feature_matrix['personal'])
    return LicenseFeaturesResponse(**features)


# --- API Endpoints ---

@router.get("/current", response_model=LicenseResponse)
async def get_current_license(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get current user's license information."""
    try:
        license_info = license_manager.get_user_license(current_user.id)
        
        if not license_info:
            # Create a default personal license
            license_info = license_manager.create_license(
                user_id=current_user.id,
                license_type=LicenseTypeEnum.PERSONAL
            )
        
        return LicenseResponse(
            id=str(license_info.id),
            user_id=license_info.user_id,
            license_type=license_info.license_type.value,
            license_key=license_info.license_key,
            status=license_info.status.value,
            max_models=license_info.max_models,
            max_monthly_inferences=license_info.max_monthly_inferences,
            feature_flags=license_info.feature_flags or {},
            price_paid_cents=license_info.price_paid_cents,
            billing_cycle=license_info.billing_cycle,
            created_at=license_info.created_at.isoformat(),
            expires_at=license_info.expires_at.isoformat() if license_info.expires_at else None,
            total_models_trained=license_info.total_models_trained,
            current_month_inferences=license_info.current_month_inferences
        )
        
    except Exception as e:
        logger.error(f"Failed to get license: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve license information"
        )


@router.get("/features", response_model=LicenseFeaturesResponse)
async def get_license_features(
    current_user: User = Depends(get_current_user)
):
    """Get license features for current user."""
    try:
        license_info = license_manager.get_user_license(current_user.id)
        license_type = license_info.license_type.value if license_info else 'personal'
        
        return get_license_features(license_type)
        
    except Exception as e:
        logger.error(f"Failed to get license features: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve license features"
        )


@router.get("/usage", response_model=UsageStatsResponse)
async def get_usage_stats(
    current_user: User = Depends(get_current_user)
):
    """Get usage statistics for current user."""
    try:
        license_info = license_manager.get_user_license(current_user.id)
        
        if not license_info:
            return UsageStatsResponse(
                models_trained=0,
                monthly_inferences=0,
                deployments_active=0,
                api_calls_today=0,
                storage_used_mb=0
            )
        
        return UsageStatsResponse(
            models_trained=license_info.total_models_trained,
            monthly_inferences=license_info.current_month_inferences,
            deployments_active=0,  # TODO: Implement deployment tracking
            api_calls_today=0,     # TODO: Implement API call tracking
            storage_used_mb=0      # TODO: Implement storage tracking
        )
        
    except Exception as e:
        logger.error(f"Failed to get usage stats: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve usage statistics"
        )


@router.post("/validate", response_model=ValidationResult)
async def validate_feature(
    request: FeatureValidationRequest,
    current_user: User = Depends(get_current_user)
):
    """Validate if a specific feature is available for the user."""
    try:
        license_info = license_manager.get_user_license(current_user.id)
        license_type = license_info.license_type.value if license_info else 'personal'
        
        features = get_license_features(license_type)
        feature_available = getattr(features, request.feature, False)
        
        if not feature_available:
            return ValidationResult(
                valid=False,
                feature_available=False,
                usage_allowed=False,
                error_message=f"Feature '{request.feature}' not available with {license_type} license",
                upgrade_required=True
            )
        
        # Check usage limits if applicable
        usage_check = license_manager.check_usage_limits(current_user.id, request.operation or request.feature)
        
        return ValidationResult(
            valid=usage_check['allowed'],
            feature_available=True,
            usage_allowed=usage_check['allowed'],
            error_message=usage_check.get('error'),
            limits_exceeded=usage_check.get('limits_exceeded', [])
        )
        
    except Exception as e:
        logger.error(f"Failed to validate feature: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to validate feature"
        )


@router.post("/usage/update")
async def update_usage(
    request: UsageUpdateRequest,
    current_user: User = Depends(get_current_user)
):
    """Update usage for a specific operation."""
    try:
        success = license_manager.update_usage(current_user.id, request.operation, request.count)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Failed to update usage"
            )
        
        return {"message": "Usage updated successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to update usage: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update usage"
        )


@router.get("/tiers", response_model=LicenseTiersResponse)
async def get_license_tiers():
    """Get available license tiers and pricing."""
    try:
        tiers = [
            LicenseTier(
                name="Personal",
                price_monthly=0,
                price_yearly=0,
                features=get_license_features('personal'),
                description="Perfect for individual users and small projects"
            ),
            LicenseTier(
                name="Professional",
                price_monthly=29,
                price_yearly=290,
                features=get_license_features('professional'),
                description="Ideal for professionals and growing teams",
                popular=True
            ),
            LicenseTier(
                name="Enterprise",
                price_monthly=99,
                price_yearly=990,
                features=get_license_features('enterprise'),
                description="Full-featured solution for large organizations"
            )
        ]
        
        return LicenseTiersResponse(tiers=tiers)
        
    except Exception as e:
        logger.error(f"Failed to get license tiers: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve license tiers"
        )


@router.post("/validate-deployment", response_model=ValidationResult)
async def validate_deployment(
    request: DeploymentValidationRequest,
    current_user: User = Depends(get_current_user)
):
    """Check if user can perform a deployment operation."""
    try:
        license_info = license_manager.get_user_license(current_user.id)
        license_type = license_info.license_type.value if license_info else 'personal'

        # Define deployment permissions by license type
        deployment_permissions = {
            'personal': ['local', 'batch'],
            'professional': ['local', 'batch', 'api'],
            'enterprise': ['local', 'batch', 'api', 'cloud', 'edge']
        }

        allowed_types = deployment_permissions.get(license_type, ['local'])
        deployment_allowed = request.deployment_type in allowed_types

        if not deployment_allowed:
            required_license = 'professional' if request.deployment_type == 'api' else 'enterprise'
            return ValidationResult(
                valid=False,
                feature_available=False,
                usage_allowed=False,
                error_message=f"Deployment type '{request.deployment_type}' requires {required_license} license",
                upgrade_required=True
            )

        # Check deployment limits
        usage_check = license_manager.check_usage_limits(current_user.id, 'deployment')

        return ValidationResult(
            valid=usage_check['allowed'],
            feature_available=True,
            usage_allowed=usage_check['allowed'],
            error_message=usage_check.get('error'),
            limits_exceeded=usage_check.get('limits_exceeded', [])
        )

    except Exception as e:
        logger.error(f"Failed to validate deployment: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to validate deployment"
        )


@router.get("/deployment-limits", response_model=DeploymentLimitsResponse)
async def get_deployment_limits(
    current_user: User = Depends(get_current_user)
):
    """Get deployment limits for current license."""
    try:
        license_info = license_manager.get_user_license(current_user.id)
        license_type = license_info.license_type.value if license_info else 'personal'

        # Define limits by license type
        limits_config = {
            'personal': {
                'max_deployments': 1,
                'available_types': ['local', 'batch'],
                'restrictions': {
                    'api_rate_limit': 100,
                    'concurrent_requests': 1,
                    'storage_limit_mb': 100
                }
            },
            'professional': {
                'max_deployments': 10,
                'available_types': ['local', 'batch', 'api'],
                'restrictions': {
                    'api_rate_limit': 10000,
                    'concurrent_requests': 10,
                    'storage_limit_mb': 1000
                }
            },
            'enterprise': {
                'max_deployments': -1,  # Unlimited
                'available_types': ['local', 'batch', 'api', 'cloud', 'edge'],
                'restrictions': {
                    'api_rate_limit': -1,  # Unlimited
                    'concurrent_requests': -1,  # Unlimited
                    'storage_limit_mb': -1  # Unlimited
                }
            }
        }

        config = limits_config.get(license_type, limits_config['personal'])

        return DeploymentLimitsResponse(
            max_deployments=config['max_deployments'],
            current_deployments=0,  # TODO: Implement deployment counting
            available_types=config['available_types'],
            restrictions=config['restrictions']
        )

    except Exception as e:
        logger.error(f"Failed to get deployment limits: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve deployment limits"
        )
