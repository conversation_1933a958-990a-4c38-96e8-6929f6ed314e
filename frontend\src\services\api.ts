// frontend/src/services/api.ts
// This file is kept for backward compatibility
// It re-exports all API functions from the modularized services

// Re-export everything from the new modular API services
export * from './dataApi';
export * from './llmApi';
export * from './taskApi';
export * from './authApi';
export * from './hierarchyApi';
export * from './nonHierarchicalApi';
export * from './universalApi';

// Export the API client for direct use if needed
export { default } from './apiClient';
