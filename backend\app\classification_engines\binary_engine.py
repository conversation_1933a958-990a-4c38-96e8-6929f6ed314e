"""Binary Classification Engine for ClassyWeb ML Platform.

This module implements binary classification (two-class problems) with support for
both custom model training and LLM inference approaches.
"""

import logging
import time
import pandas as pd
import numpy as np
from typing import List, Dict, Any, Optional, Tuple
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, roc_auc_score
import torch
from transformers import AutoTokenizer, AutoModelForSequenceClassification

from .base_engine import (
    BaseClassificationEngine, 
    ClassificationType, 
    TrainingMethod,
    ClassificationResult,
    TrainingConfig,
    TrainingResult
)

logger = logging.getLogger(__name__)


class BinaryClassificationEngine(BaseClassificationEngine):
    """Binary classification engine for two-class problems."""
    
    def __init__(self, classification_type: ClassificationType, **kwargs):
        """Initialize binary classification engine."""
        super().__init__(classification_type)
        self.threshold = 0.5
        self.positive_class = None
        self.negative_class = None
        
    @property
    def supported_training_methods(self) -> List[TrainingMethod]:
        """Return supported training methods."""
        return [TrainingMethod.CUSTOM, TrainingMethod.LLM]
    
    @property
    def default_metrics(self) -> List[str]:
        """Return default metrics for binary classification."""
        return ['accuracy', 'precision', 'recall', 'f1', 'auc_roc']
    
    def validate_config(self, config: TrainingConfig) -> Tuple[bool, List[str]]:
        """Validate configuration for binary classification."""
        errors = []
        
        # Check classification type
        if config.classification_type != ClassificationType.BINARY:
            errors.append(f"Expected binary classification, got {config.classification_type}")
        
        # Check text columns
        if not config.text_columns:
            errors.append("At least one text column must be specified")
        
        # Check label columns
        if len(config.label_columns) != 1:
            errors.append("Binary classification requires exactly one label column")
        
        # Check training method specific requirements
        if config.training_method == TrainingMethod.LLM:
            if not config.llm_provider:
                errors.append("LLM provider must be specified for LLM training method")
            if not config.prompt_template:
                errors.append("Prompt template must be specified for LLM training method")
        
        # Check threshold
        if not (0.0 < config.threshold < 1.0):
            errors.append("Threshold must be between 0 and 1")
        
        return len(errors) == 0, errors
    
    def get_recommended_config(
        self,
        data: pd.DataFrame,
        training_method: TrainingMethod
    ) -> TrainingConfig:
        """Get recommended configuration for binary classification."""
        config = super().get_recommended_config(data, training_method)
        
        # Detect text and label columns
        text_cols = [col for col in data.columns if data[col].dtype == 'object' and 'text' in col.lower()]
        if not text_cols:
            text_cols = [col for col in data.columns if data[col].dtype == 'object'][:1]
        
        label_cols = [col for col in data.columns if 'label' in col.lower() or 'class' in col.lower()]
        if not label_cols:
            # Find columns with exactly 2 unique values
            for col in data.columns:
                if col not in text_cols and data[col].nunique() == 2:
                    label_cols = [col]
                    break
        
        config.text_columns = text_cols
        config.label_columns = label_cols[:1]  # Only one label column for binary
        
        # Analyze class balance
        if label_cols:
            label_col = label_cols[0]
            class_counts = data[label_col].value_counts()
            if len(class_counts) == 2:
                classes = class_counts.index.tolist()
                self.positive_class = classes[0]
                self.negative_class = classes[1]
                
                # Adjust for class imbalance
                imbalance_ratio = class_counts.max() / class_counts.min()
                if imbalance_ratio > 3:
                    # Use class weights for imbalanced data
                    total = len(data)
                    config.class_weights = {
                        str(classes[0]): total / (2 * class_counts[classes[0]]),
                        str(classes[1]): total / (2 * class_counts[classes[1]])
                    }
        
        # Set binary-specific defaults
        config.threshold = 0.5
        
        if training_method == TrainingMethod.LLM:
            config.prompt_template = self._get_default_prompt_template()
            config.temperature = 0.1
            config.max_tokens = 10
        
        return config
    
    def _get_default_prompt_template(self) -> str:
        """Get default prompt template for binary classification."""
        return """Classify the following text as either positive or negative.

Text: {text}

Classification (respond with only 'positive' or 'negative'):"""
    
    async def train_custom_model(
        self,
        data: pd.DataFrame,
        config: TrainingConfig,
        progress_callback: Optional[callable] = None
    ) -> TrainingResult:
        """Train a custom binary classification model."""
        start_time = time.time()
        
        try:
            # Validate configuration
            is_valid, errors = self.validate_config(config)
            if not is_valid:
                return TrainingResult(
                    model_id="",
                    training_time=0,
                    final_metrics={},
                    training_history=[],
                    error=f"Configuration validation failed: {'; '.join(errors)}"
                )
            
            if progress_callback:
                progress_callback({"stage": "preparation", "progress": 0.1})
            
            # Prepare data
            text_col = config.text_columns[0]
            label_col = config.label_columns[0]
            
            texts = data[text_col].astype(str).tolist()
            labels = data[label_col].tolist()
            
            # Convert labels to binary (0, 1)
            unique_labels = list(set(labels))
            if len(unique_labels) != 2:
                return TrainingResult(
                    model_id="",
                    training_time=0,
                    final_metrics={},
                    training_history=[],
                    error=f"Expected 2 unique labels, found {len(unique_labels)}"
                )
            
            # Map labels to 0, 1
            label_map = {unique_labels[0]: 0, unique_labels[1]: 1}
            binary_labels = [label_map[label] for label in labels]
            
            if progress_callback:
                progress_callback({"stage": "model_setup", "progress": 0.2})
            
            # Initialize tokenizer and model
            tokenizer = AutoTokenizer.from_pretrained(config.base_model)
            model = AutoModelForSequenceClassification.from_pretrained(
                config.base_model,
                num_labels=2
            )
            
            # Tokenize data
            encodings = tokenizer(
                texts,
                truncation=True,
                padding=True,
                max_length=config.max_length,
                return_tensors="pt"
            )
            
            if progress_callback:
                progress_callback({"stage": "training", "progress": 0.3})
            
            # Create dataset
            class BinaryDataset(torch.utils.data.Dataset):
                def __init__(self, encodings, labels):
                    self.encodings = encodings
                    self.labels = labels
                
                def __getitem__(self, idx):
                    item = {key: torch.tensor(val[idx]) for key, val in self.encodings.items()}
                    item['labels'] = torch.tensor(self.labels[idx], dtype=torch.long)
                    return item
                
                def __len__(self):
                    return len(self.labels)
            
            # Split data
            from sklearn.model_selection import train_test_split
            train_texts, val_texts, train_labels, val_labels = train_test_split(
                texts, binary_labels, test_size=config.validation_split, random_state=42
            )
            
            train_encodings = tokenizer(train_texts, truncation=True, padding=True, max_length=config.max_length, return_tensors="pt")
            val_encodings = tokenizer(val_texts, truncation=True, padding=True, max_length=config.max_length, return_tensors="pt")
            
            train_dataset = BinaryDataset(train_encodings, train_labels)
            val_dataset = BinaryDataset(val_encodings, val_labels)
            
            # Training setup
            from transformers import Trainer, TrainingArguments
            
            training_args = TrainingArguments(
                output_dir='./results',
                num_train_epochs=config.num_epochs,
                per_device_train_batch_size=config.batch_size,
                per_device_eval_batch_size=config.batch_size,
                warmup_steps=config.warmup_steps,
                weight_decay=config.weight_decay,
                logging_dir='./logs',
                evaluation_strategy=config.evaluation_strategy,
                save_strategy=config.save_strategy,
                fp16=config.fp16,
                gradient_accumulation_steps=config.gradient_accumulation_steps,
                gradient_checkpointing=config.gradient_checkpointing,
                learning_rate=config.learning_rate,
            )
            
            def compute_metrics(eval_pred):
                predictions, labels = eval_pred
                predictions = np.argmax(predictions, axis=1)
                return {
                    'accuracy': accuracy_score(labels, predictions),
                    'f1': f1_score(labels, predictions),
                    'precision': precision_score(labels, predictions),
                    'recall': recall_score(labels, predictions)
                }
            
            trainer = Trainer(
                model=model,
                args=training_args,
                train_dataset=train_dataset,
                eval_dataset=val_dataset,
                compute_metrics=compute_metrics,
            )
            
            # Train model
            trainer.train()
            
            if progress_callback:
                progress_callback({"stage": "evaluation", "progress": 0.8})
            
            # Evaluate model
            eval_results = trainer.evaluate()
            
            # Calculate additional metrics
            val_predictions = trainer.predict(val_dataset)
            val_preds = np.argmax(val_predictions.predictions, axis=1)
            val_probs = torch.softmax(torch.tensor(val_predictions.predictions), dim=1)[:, 1].numpy()
            
            final_metrics = {
                'accuracy': accuracy_score(val_labels, val_preds),
                'precision': precision_score(val_labels, val_preds),
                'recall': recall_score(val_labels, val_preds),
                'f1': f1_score(val_labels, val_preds),
                'auc_roc': roc_auc_score(val_labels, val_probs)
            }
            
            # Save model artifacts
            model_id = f"binary_model_{int(time.time())}"
            model_path = f"./model_artifacts/{model_id}"
            
            trainer.save_model(model_path)
            tokenizer.save_pretrained(model_path)
            
            # Store model info
            self.model = model
            self.tokenizer = tokenizer
            self.label_map = {v: k for k, v in label_map.items()}
            self.is_trained = True
            
            training_time = time.time() - start_time
            
            if progress_callback:
                progress_callback({"stage": "complete", "progress": 1.0})
            
            return TrainingResult(
                model_id=model_id,
                training_time=training_time,
                final_metrics=final_metrics,
                training_history=[],  # TODO: Extract from trainer logs
                model_path=model_path,
                tokenizer_path=model_path,
                config_path=model_path
            )
            
        except Exception as e:
            logger.error(f"Training failed: {e}")
            return TrainingResult(
                model_id="",
                training_time=time.time() - start_time,
                final_metrics={},
                training_history=[],
                error=str(e)
            )

    async def llm_inference(
        self,
        texts: List[str],
        config: TrainingConfig,
        progress_callback: Optional[callable] = None
    ) -> List[ClassificationResult]:
        """Perform binary classification using LLM inference."""
        results = []

        try:
            # Use sequential thinking LLM classifier for enhanced results
            from .sequential_llm_classifier import SequentialLLMClassifier

            if progress_callback:
                progress_callback({"stage": "llm_setup", "progress": 0.1})

            # Initialize sequential classifier
            sequential_classifier = SequentialLLMClassifier()

            # Use sequential thinking approach for enhanced classification
            results = await sequential_classifier.classify_with_sequential_thinking(
                texts=texts,
                classification_type="binary",
                llm_provider=config.llm_provider,
                llm_model=config.llm_model,
                llm_endpoint=getattr(config, 'llm_endpoint', ''),
                api_key=getattr(config, 'api_key', None),
                custom_prompt=config.prompt_template,
                temperature=config.temperature,
                max_tokens=config.max_tokens,
                progress_callback=progress_callback
            )

            if progress_callback:
                progress_callback({"stage": "complete", "progress": 1.0})

            return results

        except Exception as e:
            logger.error(f"LLM inference failed: {e}")
            # Return error results for all texts
            return [
                ClassificationResult(
                    text=text,
                    predictions=["error"],
                    confidence=0.0,
                    processing_time=0.0,
                    method_used="llm_inference",
                    reasoning=f"Error: {str(e)}",
                    metadata={"error": str(e)}
                )
                for text in texts
            ]

    async def predict(
        self,
        texts: List[str],
        model_id: Optional[str] = None
    ) -> List[ClassificationResult]:
        """Make predictions using a trained binary classification model."""
        if not self.is_trained or self.model is None or self.tokenizer is None:
            raise ValueError("Model not trained or loaded. Train a model first or load an existing one.")

        results = []

        try:
            # Tokenize texts
            encodings = self.tokenizer(
                texts,
                truncation=True,
                padding=True,
                max_length=512,
                return_tensors="pt"
            )

            # Make predictions
            with torch.no_grad():
                outputs = self.model(**encodings)
                predictions = torch.nn.functional.softmax(outputs.logits, dim=-1)

            # Process results
            for i, text in enumerate(texts):
                start_time = time.time()

                probs = predictions[i].numpy()
                predicted_class = int(np.argmax(probs))
                confidence = float(probs[predicted_class])

                # Map back to original labels
                prediction_label = self.label_map.get(predicted_class, f"class_{predicted_class}")

                # Create probability dictionary
                prob_dict = {}
                for class_idx, prob in enumerate(probs):
                    label = self.label_map.get(class_idx, f"class_{class_idx}")
                    prob_dict[label] = float(prob)

                processing_time = time.time() - start_time

                results.append(ClassificationResult(
                    text=text,
                    predictions=[prediction_label],
                    confidence=confidence,
                    probabilities=prob_dict,
                    processing_time=processing_time,
                    method_used="custom_model",
                    reasoning=f"Predicted {prediction_label} with {confidence:.3f} confidence",
                    metadata={"model_id": model_id, "threshold": self.threshold}
                ))

            return results

        except Exception as e:
            logger.error(f"Prediction failed: {e}")
            return [
                ClassificationResult(
                    text=text,
                    predictions=["error"],
                    confidence=0.0,
                    processing_time=0.0,
                    method_used="custom_model",
                    reasoning=f"Prediction error: {str(e)}",
                    metadata={"error": str(e)}
                )
                for text in texts
            ]
