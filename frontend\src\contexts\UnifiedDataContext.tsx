/**
 * UnifiedDataContext.tsx
 * 
 * Global state management for unified data files accessible across all components
 */

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { 
  unifiedDataManager, 
  UnifiedDataUpload, 
  DataPurpose,
  DataPurposeSuggestion 
} from '@/services/unifiedDataManager';
import { UploadedFile } from '@/services/fileUploadApi';

interface UnifiedDataContextType {
  // File management
  files: UnifiedDataUpload[];
  selectedFileId: string | null;
  selectedFile: UnifiedDataUpload | null;
  
  // Actions
  uploadFile: (file: File, purposes: DataPurpose[]) => Promise<string>;
  selectFile: (fileId: string) => void;
  removeFile: (fileId: string) => boolean;
  updateFilePurposes: (fileId: string, purposes: DataPurpose[]) => boolean;
  clearAllFiles: () => void;
  
  // Data analysis
  getFileSuggestions: (fileId: string) => DataPurposeSuggestion | null;
  getFileForPurpose: (fileId: string, purpose: DataPurpose) => UploadedFile | null;
  
  // Statistics
  getUsageStats: () => any;
  
  // Loading states
  isLoading: boolean;
  error: string | null;
  
  // Refresh data
  refreshFiles: () => void;
}

const UnifiedDataContext = createContext<UnifiedDataContextType | undefined>(undefined);

interface UnifiedDataProviderProps {
  children: ReactNode;
}

export const UnifiedDataProvider: React.FC<UnifiedDataProviderProps> = ({ children }) => {
  const [files, setFiles] = useState<UnifiedDataUpload[]>([]);
  const [selectedFileId, setSelectedFileId] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Load files on mount
  useEffect(() => {
    refreshFiles();
  }, []);

  const refreshFiles = async () => {
    try {
      setIsLoading(true);
      const result = await unifiedDataManager.getValidatedFiles();
      setFiles(result.files);
      setError(null);

      // Log cleanup if any files were removed
      if (result.cleanedCount > 0) {
        console.log(`Cleaned up ${result.cleanedCount} stale files from cache`);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load files');
      setFiles([]);
    } finally {
      setIsLoading(false);
    }
  };

  const uploadFile = async (file: File, purposes: DataPurpose[]): Promise<string> => {
    setIsLoading(true);
    setError(null);
    
    try {
      const fileId = await unifiedDataManager.uploadFile(file, purposes);
      refreshFiles();
      return fileId;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to upload file';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const selectFile = (fileId: string) => {
    setSelectedFileId(fileId);
  };

  const removeFile = (fileId: string): boolean => {
    try {
      const success = unifiedDataManager.removeFile(fileId);
      if (success) {
        refreshFiles();
        if (selectedFileId === fileId) {
          setSelectedFileId(null);
        }
      }
      return success;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to remove file');
      return false;
    }
  };

  const updateFilePurposes = (fileId: string, purposes: DataPurpose[]): boolean => {
    try {
      const success = unifiedDataManager.updatePurposes(fileId, purposes);
      if (success) {
        refreshFiles();
      }
      return success;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update file purposes');
      return false;
    }
  };

  const clearAllFiles = () => {
    try {
      unifiedDataManager.clearAll();
      refreshFiles();
      setSelectedFileId(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to clear files');
    }
  };

  const getFileSuggestions = (fileId: string): DataPurposeSuggestion | null => {
    try {
      const fileData = unifiedDataManager.getDataForPurpose(fileId, 'analysis');
      if (fileData) {
        return unifiedDataManager.suggestDataPurposes(fileData);
      }
      return null;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to get file suggestions');
      return null;
    }
  };

  const getFileForPurpose = (fileId: string, purpose: DataPurpose): UploadedFile | null => {
    try {
      return unifiedDataManager.getDataForPurpose(fileId, purpose);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to get file data');
      return null;
    }
  };

  const getUsageStats = () => {
    try {
      return unifiedDataManager.getUsageStats();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to get usage stats');
      return {
        totalFiles: 0,
        totalSize: 0,
        purposeBreakdown: { analysis: 0, training: 0, classification: 0 }
      };
    }
  };

  // Computed values
  const selectedFile = selectedFileId 
    ? files.find(f => f.fileInfo.file_id === selectedFileId) || null
    : null;

  const contextValue: UnifiedDataContextType = {
    // File management
    files,
    selectedFileId,
    selectedFile,
    
    // Actions
    uploadFile,
    selectFile,
    removeFile,
    updateFilePurposes,
    clearAllFiles,
    
    // Data analysis
    getFileSuggestions,
    getFileForPurpose,
    
    // Statistics
    getUsageStats,
    
    // Loading states
    isLoading,
    error,
    
    // Refresh data
    refreshFiles
  };

  return (
    <UnifiedDataContext.Provider value={contextValue}>
      {children}
    </UnifiedDataContext.Provider>
  );
};

// Custom hook to use the unified data context
export const useUnifiedData = (): UnifiedDataContextType => {
  const context = useContext(UnifiedDataContext);
  if (context === undefined) {
    throw new Error('useUnifiedData must be used within a UnifiedDataProvider');
  }
  return context;
};

// HOC for components that need unified data
export const withUnifiedData = <P extends object>(
  Component: React.ComponentType<P>
): React.ComponentType<P> => {
  return (props: P) => (
    <UnifiedDataProvider>
      <Component {...props} />
    </UnifiedDataProvider>
  );
};

// Utility hooks for common operations
export const useFileUpload = () => {
  const { uploadFile, isLoading, error } = useUnifiedData();
  return { uploadFile, isLoading, error };
};

export const useFileSelection = () => {
  const { files, selectedFileId, selectedFile, selectFile } = useUnifiedData();
  return { files, selectedFileId, selectedFile, selectFile };
};

export const useFileManagement = () => {
  const { 
    files, 
    removeFile, 
    updateFilePurposes, 
    clearAllFiles, 
    refreshFiles,
    getUsageStats 
  } = useUnifiedData();
  
  return { 
    files, 
    removeFile, 
    updateFilePurposes, 
    clearAllFiles, 
    refreshFiles,
    getUsageStats 
  };
};

export const useDataAnalysis = () => {
  const { getFileSuggestions, getFileForPurpose } = useUnifiedData();
  return { getFileSuggestions, getFileForPurpose };
};
