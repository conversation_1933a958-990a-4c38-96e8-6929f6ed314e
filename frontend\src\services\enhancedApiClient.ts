/**
 * Enhanced API Client for ClassyWeb ML Platform Phase 3
 * 
 * This extends the base API client with v2 endpoints, WebSocket support,
 * and advanced error handling for real-time training monitoring.
 */

import apiClient, { API_BASE_URL, setAuthHeader } from './apiClient';
import { AxiosResponse, AxiosRequestConfig } from 'axios';

// Enhanced API Client Class
export class EnhancedApiClient {
  private baseClient = apiClient;
  private wsConnections: Map<string, WebSocket> = new Map();
  private wsEventHandlers: Map<string, Set<(event: MessageEvent) => void>> = new Map();

  /**
   * Make a standard HTTP request
   */
  async request<T = any>(config: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.baseClient.request(config);
  }

  /**
   * GET request
   */
  async get<T = any>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.baseClient.get(url, config);
  }

  /**
   * POST request
   */
  async post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.baseClient.post(url, data, config);
  }

  /**
   * PUT request
   */
  async put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.baseClient.put(url, data, config);
  }

  /**
   * PATCH request
   */
  async patch<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.baseClient.patch(url, data, config);
  }

  /**
   * DELETE request
   */
  async delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.baseClient.delete(url, config);
  }

  /**
   * Upload file with progress tracking
   */
  async uploadFile<T = any>(
    url: string,
    file: File,
    onProgress?: (progress: number) => void,
    additionalData?: Record<string, any>
  ): Promise<AxiosResponse<T>> {
    const formData = new FormData();
    formData.append('file', file);

    // Add additional data if provided
    if (additionalData) {
      Object.entries(additionalData).forEach(([key, value]) => {
        formData.append(key, typeof value === 'string' ? value : JSON.stringify(value));
      });
    }

    return this.baseClient.post(url, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress: (progressEvent) => {
        if (onProgress && progressEvent.total) {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          onProgress(progress);
        }
      },
    });
  }

  /**
   * Download file as blob
   */
  async downloadFile(url: string, filename?: string): Promise<Blob> {
    const response = await this.baseClient.get(url, {
      responseType: 'blob',
    });

    // If filename is provided, trigger download
    if (filename) {
      const blob = response.data;
      const downloadUrl = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(downloadUrl);
    }

    return response.data;
  }

  /**
   * Create WebSocket connection with authentication
   */
  createWebSocket(
    endpoint: string,
    onMessage?: (event: MessageEvent) => void,
    onOpen?: (event: Event) => void,
    onClose?: (event: CloseEvent) => void,
    onError?: (event: Event) => void
  ): WebSocket {
    const token = this.getAuthToken();
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const host = window.location.host;
    const wsUrl = `${protocol}//${host}${endpoint}${token ? `?token=${encodeURIComponent(token)}` : ''}`;

    const ws = new WebSocket(wsUrl);

    // Set up event handlers
    if (onOpen) ws.onopen = onOpen;
    if (onClose) ws.onclose = onClose;
    if (onError) ws.onerror = onError;
    if (onMessage) ws.onmessage = onMessage;

    // Store connection
    this.wsConnections.set(endpoint, ws);

    return ws;
  }

  /**
   * Get WebSocket connection
   */
  getWebSocket(endpoint: string): WebSocket | undefined {
    return this.wsConnections.get(endpoint);
  }

  /**
   * Close WebSocket connection
   */
  closeWebSocket(endpoint: string): void {
    const ws = this.wsConnections.get(endpoint);
    if (ws) {
      ws.close();
      this.wsConnections.delete(endpoint);
      this.wsEventHandlers.delete(endpoint);
    }
  }

  /**
   * Close all WebSocket connections
   */
  closeAllWebSockets(): void {
    this.wsConnections.forEach((ws, endpoint) => {
      ws.close();
    });
    this.wsConnections.clear();
    this.wsEventHandlers.clear();
  }

  /**
   * Add WebSocket event handler
   */
  addWebSocketHandler(endpoint: string, handler: (event: MessageEvent) => void): void {
    if (!this.wsEventHandlers.has(endpoint)) {
      this.wsEventHandlers.set(endpoint, new Set());
    }
    this.wsEventHandlers.get(endpoint)!.add(handler);

    // If WebSocket exists, add handler
    const ws = this.wsConnections.get(endpoint);
    if (ws) {
      const existingHandler = ws.onmessage;
      ws.onmessage = (event) => {
        // Call existing handler first
        if (existingHandler) existingHandler.call(ws, event);
        // Call new handler
        handler(event);
      };
    }
  }

  /**
   * Remove WebSocket event handler
   */
  removeWebSocketHandler(endpoint: string, handler: (event: MessageEvent) => void): void {
    const handlers = this.wsEventHandlers.get(endpoint);
    if (handlers) {
      handlers.delete(handler);
      if (handlers.size === 0) {
        this.wsEventHandlers.delete(endpoint);
      }
    }
  }

  /**
   * Send WebSocket message
   */
  sendWebSocketMessage(endpoint: string, message: any): boolean {
    const ws = this.wsConnections.get(endpoint);
    if (ws && ws.readyState === WebSocket.OPEN) {
      ws.send(JSON.stringify(message));
      return true;
    }
    return false;
  }

  /**
   * Get authentication token
   */
  private getAuthToken(): string | null {
    try {
      const authStorage = localStorage.getItem('auth-storage');
      if (authStorage) {
        const { state } = JSON.parse(authStorage);
        return state?.token || null;
      }
    } catch (error) {
      console.error('Failed to get auth token:', error);
    }
    return null;
  }

  /**
   * Batch requests with concurrency control
   */
  async batchRequests<T>(
    requests: (() => Promise<T>)[],
    concurrency: number = 5
  ): Promise<T[]> {
    const results: T[] = [];
    const executing: Promise<void>[] = [];

    for (const request of requests) {
      const promise = request().then((result) => {
        results.push(result);
      });

      executing.push(promise);

      if (executing.length >= concurrency) {
        await Promise.race(executing);
        executing.splice(executing.findIndex(p => p === promise), 1);
      }
    }

    await Promise.all(executing);
    return results;
  }

  /**
   * Retry request with exponential backoff
   */
  async retryRequest<T>(
    requestFn: () => Promise<T>,
    maxRetries: number = 3,
    baseDelay: number = 1000
  ): Promise<T> {
    let lastError: Error;

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        return await requestFn();
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));
        
        if (attempt === maxRetries) {
          throw lastError;
        }

        // Exponential backoff with jitter
        const delay = baseDelay * Math.pow(2, attempt) + Math.random() * 1000;
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    throw lastError!;
  }

  /**
   * Health check
   */
  async healthCheck(): Promise<boolean> {
    try {
      await this.get('/health');
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * Get API version info
   */
  async getApiVersion(): Promise<any> {
    const response = await this.get('/version');
    return response.data;
  }

  /**
   * Set authentication header
   */
  setAuthHeader(token: string | null): void {
    setAuthHeader(token);
  }
}

// Create singleton instance
export const enhancedApiClient = new EnhancedApiClient();

// Export commonly used methods
export const {
  get,
  post,
  put,
  patch,
  delete: del,
  uploadFile,
  downloadFile,
  createWebSocket,
  closeWebSocket,
  sendWebSocketMessage,
  batchRequests,
  retryRequest,
  healthCheck,
  getApiVersion
} = enhancedApiClient;

export default enhancedApiClient;
