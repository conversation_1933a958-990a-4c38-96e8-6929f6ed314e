#!/usr/bin/env python3
"""
Test script for dynamic hierarchy configuration system.
Tests the enhanced hierarchical classification with user-defined hierarchy levels.
"""

import requests
import json
import pandas as pd
import tempfile
import os
from pathlib import Path

def create_test_data():
    """Create test data with various hierarchy structures."""
    
    # Test data with custom hierarchy levels
    test_data = [
        {"Description": "Customer complaint about billing", "Department": "Finance", "Team": "Billing", "Priority": "High"},
        {"Description": "Software bug in login system", "Department": "Engineering", "Team": "Backend", "Priority": "Critical"},
        {"Description": "New feature request for mobile app", "Department": "Product", "Team": "Mobile", "Priority": "Medium"},
        {"Description": "HR policy question", "Department": "Human Resources", "Team": "Policy", "Priority": "Low"},
        {"Description": "Database performance issue", "Department": "Engineering", "Team": "Infrastructure", "Priority": "High"},
        {"Description": "Marketing campaign analysis", "Department": "Marketing", "Team": "Analytics", "Priority": "Medium"},
        {"Description": "Legal compliance review", "Department": "Legal", "Team": "Compliance", "Priority": "High"},
        {"Description": "Customer success story", "Department": "Marketing", "Team": "Content", "Priority": "Low"},
        {"Description": "Security vulnerability report", "Department": "Engineering", "Team": "Security", "Priority": "Critical"},
        {"Description": "Budget planning meeting", "Department": "Finance", "Team": "Planning", "Priority": "Medium"}
    ]
    
    df = pd.DataFrame(test_data)
    
    # Save to temporary file
    temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False)
    df.to_csv(temp_file.name, index=False)
    temp_file.close()
    
    return temp_file.name, df

def test_dynamic_hierarchy_training():
    """Test dynamic hierarchy configuration with custom levels."""
    
    print("🧪 Testing Dynamic Hierarchy Configuration System")
    print("=" * 60)
    
    # Configuration
    base_url = "http://localhost:8001"
    headers = {
        "Authorization": "Bearer test-token",  # Replace with actual token
        "Content-Type": "application/json"
    }
    
    try:
        # 1. Create test data
        print("📊 Creating test data with custom hierarchy levels...")
        test_file_path, test_df = create_test_data()
        print(f"✅ Created test data: {len(test_df)} rows")
        print(f"📋 Columns: {list(test_df.columns)}")
        print(f"🏗️ Sample hierarchy: {test_df.iloc[0][['Department', 'Team', 'Priority']].to_dict()}")
        
        # 2. Upload test file
        print("\n📤 Uploading test file...")
        with open(test_file_path, 'rb') as f:
            upload_response = requests.post(
                f"{base_url}/api/v2/files/upload",
                files={"file": f},
                headers={"Authorization": headers["Authorization"]}
            )
        
        if upload_response.status_code != 200:
            print(f"❌ File upload failed: {upload_response.status_code}")
            print(upload_response.text)
            return
        
        file_id = upload_response.json()["id"]
        print(f"✅ File uploaded successfully: {file_id}")
        
        # 3. Test dynamic hierarchy training with custom levels
        print("\n🚀 Testing dynamic hierarchy training...")
        
        # Define custom hierarchy levels that match our data structure
        custom_hierarchy_levels = ["Department", "Team", "Priority"]
        
        training_request = {
            "file_id": file_id,
            "classification_type": "hierarchical",
            "model_type": "custom",  # Test custom training
            "text_column": "Description",
            "label_columns": custom_hierarchy_levels,  # This should match hierarchy_levels
            "hierarchy_levels": custom_hierarchy_levels,  # Custom user-defined levels
            "training_params": {
                "max_epochs": 2,  # Quick test
                "batch_size": 8,
                "learning_rate": 2e-5,
                "use_unsloth": False  # Use standard transformers for testing
            }
        }
        
        print(f"📋 Training request configuration:")
        print(f"   - Text column: {training_request['text_column']}")
        print(f"   - Hierarchy levels: {training_request['hierarchy_levels']}")
        print(f"   - Label columns: {training_request['label_columns']}")
        
        training_response = requests.post(
            f"{base_url}/api/v2/classification/universal/train",
            json=training_request,
            headers=headers
        )
        
        print(f"\n📊 Training response status: {training_response.status_code}")
        
        if training_response.status_code == 200:
            result = training_response.json()
            task_id = result.get("task_id")
            
            print(f"✅ Dynamic hierarchy training started successfully!")
            print(f"🆔 Task ID: {task_id}")
            print(f"📈 The system should now:")
            print(f"   - Intelligently map hierarchy levels to data columns")
            print(f"   - Extract hierarchical labels: Department → Team → Priority")
            print(f"   - Train a model that understands the custom hierarchy structure")
            print(f"   - Handle dynamic prompt generation for the custom levels")
            
        else:
            print(f"❌ Training failed: {training_response.status_code}")
            print(training_response.text)
            
            # Try to extract useful error information
            try:
                error_data = training_response.json()
                if "detail" in error_data:
                    print(f"🔍 Error details: {error_data['detail']}")
            except:
                pass
        
        # 4. Test LLM inference with dynamic hierarchy
        print("\n🤖 Testing LLM inference with dynamic hierarchy...")
        
        llm_request = {
            "file_id": file_id,
            "classification_type": "hierarchical",
            "model_type": "llm",
            "text_column": "Description",
            "label_columns": custom_hierarchy_levels,
            "hierarchy_levels": custom_hierarchy_levels,
            "llm_provider": "openai",
            "llm_model": "gpt-3.5-turbo"
        }
        
        llm_response = requests.post(
            f"{base_url}/api/v2/classification/universal/train",
            json=llm_request,
            headers=headers
        )
        
        print(f"📊 LLM inference response status: {llm_response.status_code}")
        
        if llm_response.status_code == 200:
            llm_result = llm_response.json()
            llm_task_id = llm_result.get("task_id")
            print(f"✅ Dynamic hierarchy LLM inference started!")
            print(f"🆔 LLM Task ID: {llm_task_id}")
        else:
            print(f"❌ LLM inference failed: {llm_response.status_code}")
            print(llm_response.text)
        
    except Exception as e:
        print(f"❌ Test failed with exception: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Cleanup
        try:
            if 'test_file_path' in locals():
                os.unlink(test_file_path)
                print(f"\n🧹 Cleaned up test file: {test_file_path}")
        except:
            pass
    
    print("\n" + "=" * 60)
    print("🏁 Dynamic Hierarchy Test Complete")

if __name__ == "__main__":
    test_dynamic_hierarchy_training()
