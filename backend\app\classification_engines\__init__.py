"""Classification Engines Package for ClassyWeb ML Platform.

This package contains the enhanced classification engine architecture supporting
five distinct classification paradigms with dual training approaches.

Classification Types:
- Binary: Two-class problems (spam/not spam, positive/negative)
- Multi-class: Multiple mutually exclusive classes (sentiment analysis)
- Multi-label: Multiple non-exclusive labels (document tagging)
- Hierarchical: Tree-structured class relationships (product categories)
- Flat: Standard single-level classification without hierarchy

Training Approaches:
- Custom Models: HuggingFace transformers with Unsloth acceleration
- LLM Inference: Large language model based classification
"""

from .base_engine import BaseClassificationEngine, ClassificationResult, TrainingConfig, ClassificationType, TrainingMethod
from .engine_factory import (
    ClassificationEngineFactory,
    get_engine_for_type,
    get_all_engine_info,
    validate_classification_type
)
from .binary_engine import BinaryClassificationEngine
from .multiclass_engine import MultiClassEngine
from .multilabel_engine import MultiLabelEngine, EnhancedMultiLabelEngine
from .hierarchical_engine import HierarchicalEngine, EnhancedHierarchicalEngine
from .flat_engine import FlatClassificationEngine

__all__ = [
    "BaseClassificationEngine",
    "ClassificationResult",
    "TrainingConfig",
    "ClassificationType",
    "TrainingMethod",
    "ClassificationEngineFactory",
    "get_engine_for_type",
    "get_all_engine_info",
    "validate_classification_type",
    "BinaryClassificationEngine",
    "MultiClassEngine",
    "MultiLabelEngine",
    "EnhancedMultiLabelEngine",
    "HierarchicalEngine",
    "EnhancedHierarchicalEngine",
    "FlatClassificationEngine"
]

# Version info
__version__ = "2.0.0"
__author__ = "ClassyWeb Team"
