/**
 * Error Handling and Recovery Service for ClassyWeb ML Platform Phase 3
 * 
 * This service provides comprehensive error handling, recovery mechanisms,
 * and resilience patterns for WebSocket connections and API v2 calls.
 */

import { enhancedApiClient } from './enhancedApiClient';

// Error Types and Interfaces
export enum ErrorType {
  NETWORK_ERROR = 'network_error',
  API_ERROR = 'api_error',
  WEBSOCKET_ERROR = 'websocket_error',
  VALIDATION_ERROR = 'validation_error',
  AUTHENTICATION_ERROR = 'authentication_error',
  TIMEOUT_ERROR = 'timeout_error',
  RATE_LIMIT_ERROR = 'rate_limit_error',
  SERVER_ERROR = 'server_error',
  CLIENT_ERROR = 'client_error',
  UNKNOWN_ERROR = 'unknown_error'
}

export enum ErrorSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

export interface ErrorInfo {
  id: string;
  type: ErrorType;
  severity: ErrorSeverity;
  message: string;
  details?: any;
  timestamp: Date;
  context?: ErrorContext;
  stackTrace?: string;
  userAgent?: string;
  url?: string;
  retryable: boolean;
  retryCount: number;
  maxRetries: number;
}

export interface ErrorContext {
  userId?: string;
  sessionId?: string;
  workflowId?: string;
  stepId?: string;
  fileId?: string;
  apiEndpoint?: string;
  requestData?: any;
  userAction?: string;
}

export interface RecoveryStrategy {
  type: RecoveryType;
  description: string;
  execute: (error: ErrorInfo) => Promise<boolean>;
  canRecover: (error: ErrorInfo) => boolean;
  priority: number;
}

export enum RecoveryType {
  RETRY = 'retry',
  FALLBACK = 'fallback',
  REFRESH_TOKEN = 'refresh_token',
  RECONNECT = 'reconnect',
  CACHE_FALLBACK = 'cache_fallback',
  USER_INTERVENTION = 'user_intervention',
  GRACEFUL_DEGRADATION = 'graceful_degradation'
}

export interface ErrorHandlerOptions {
  enableLogging: boolean;
  enableRetry: boolean;
  enableRecovery: boolean;
  enableNotifications: boolean;
  maxRetries: number;
  retryDelay: number;
  timeoutMs: number;
}

/**
 * Error Handling and Recovery Service
 */
export class ErrorHandlingService {
  private errors: Map<string, ErrorInfo> = new Map();
  private recoveryStrategies: RecoveryStrategy[] = [];
  private errorListeners: Set<(error: ErrorInfo) => void> = new Set();
  private options: ErrorHandlerOptions;

  constructor(options: Partial<ErrorHandlerOptions> = {}) {
    this.options = {
      enableLogging: true,
      enableRetry: true,
      enableRecovery: true,
      enableNotifications: true,
      maxRetries: 3,
      retryDelay: 1000,
      timeoutMs: 30000,
      ...options
    };

    this.initializeRecoveryStrategies();
    this.setupGlobalErrorHandlers();
  }

  /**
   * Initialize recovery strategies
   */
  private initializeRecoveryStrategies(): void {
    // Retry strategy
    this.recoveryStrategies.push({
      type: RecoveryType.RETRY,
      description: 'Retry the failed operation with exponential backoff',
      priority: 1,
      canRecover: (error) => error.retryable && error.retryCount < error.maxRetries,
      execute: async (error) => {
        const delay = this.options.retryDelay * Math.pow(2, error.retryCount);
        await this.delay(delay);
        error.retryCount++;
        return true;
      }
    });

    // Token refresh strategy
    this.recoveryStrategies.push({
      type: RecoveryType.REFRESH_TOKEN,
      description: 'Refresh authentication token',
      priority: 2,
      canRecover: (error) => error.type === ErrorType.AUTHENTICATION_ERROR,
      execute: async (error) => {
        try {
          // Token refresh is handled by the API client interceptor
          return true;
        } catch (refreshError) {
          console.error('Token refresh failed:', refreshError);
          return false;
        }
      }
    });

    // WebSocket reconnection strategy
    this.recoveryStrategies.push({
      type: RecoveryType.RECONNECT,
      description: 'Reconnect WebSocket connection',
      priority: 3,
      canRecover: (error) => error.type === ErrorType.WEBSOCKET_ERROR,
      execute: async (error) => {
        try {
          if (error.context?.sessionId) {
            const endpoint = `/ws/training/${error.context.sessionId}`;
            enhancedApiClient.closeWebSocket(endpoint);
            await this.delay(1000);
            enhancedApiClient.createWebSocket(endpoint);
            return true;
          }
          return false;
        } catch (reconnectError) {
          console.error('WebSocket reconnection failed:', reconnectError);
          return false;
        }
      }
    });

    // Fallback strategy
    this.recoveryStrategies.push({
      type: RecoveryType.FALLBACK,
      description: 'Use fallback endpoint or method',
      priority: 4,
      canRecover: (error) => error.type === ErrorType.API_ERROR || error.type === ErrorType.SERVER_ERROR,
      execute: async (error) => {
        // Implement fallback logic based on the specific API endpoint
        return this.executeFallbackStrategy(error);
      }
    });

    // Graceful degradation strategy
    this.recoveryStrategies.push({
      type: RecoveryType.GRACEFUL_DEGRADATION,
      description: 'Degrade functionality gracefully',
      priority: 5,
      canRecover: () => true, // Always available as last resort
      execute: async (error) => {
        // Implement graceful degradation
        this.notifyGracefulDegradation(error);
        return true;
      }
    });
  }

  /**
   * Setup global error handlers
   */
  private setupGlobalErrorHandlers(): void {
    // Handle unhandled promise rejections
    window.addEventListener('unhandledrejection', (event) => {
      const error = this.createErrorInfo({
        type: ErrorType.UNKNOWN_ERROR,
        severity: ErrorSeverity.HIGH,
        message: 'Unhandled promise rejection',
        details: event.reason,
        retryable: false
      });
      this.handleError(error);
    });

    // Handle global errors
    window.addEventListener('error', (event) => {
      const error = this.createErrorInfo({
        type: ErrorType.CLIENT_ERROR,
        severity: ErrorSeverity.MEDIUM,
        message: event.message,
        details: {
          filename: event.filename,
          lineno: event.lineno,
          colno: event.colno
        },
        stackTrace: event.error?.stack,
        retryable: false
      });
      this.handleError(error);
    });
  }

  /**
   * Create error information object
   */
  createErrorInfo(params: {
    type: ErrorType;
    severity: ErrorSeverity;
    message: string;
    details?: any;
    context?: ErrorContext;
    stackTrace?: string;
    retryable?: boolean;
    maxRetries?: number;
  }): ErrorInfo {
    return {
      id: `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type: params.type,
      severity: params.severity,
      message: params.message,
      details: params.details,
      timestamp: new Date(),
      context: params.context,
      stackTrace: params.stackTrace,
      userAgent: navigator.userAgent,
      url: window.location.href,
      retryable: params.retryable ?? true,
      retryCount: 0,
      maxRetries: params.maxRetries ?? this.options.maxRetries
    };
  }

  /**
   * Handle error with recovery attempts
   */
  async handleError(error: ErrorInfo): Promise<boolean> {
    // Store error
    this.errors.set(error.id, error);

    // Log error
    if (this.options.enableLogging) {
      this.logError(error);
    }

    // Notify listeners
    this.notifyErrorListeners(error);

    // Attempt recovery if enabled
    if (this.options.enableRecovery) {
      return await this.attemptRecovery(error);
    }

    return false;
  }

  /**
   * Attempt error recovery
   */
  private async attemptRecovery(error: ErrorInfo): Promise<boolean> {
    // Sort strategies by priority
    const applicableStrategies = this.recoveryStrategies
      .filter(strategy => strategy.canRecover(error))
      .sort((a, b) => a.priority - b.priority);

    for (const strategy of applicableStrategies) {
      try {
        console.log(`Attempting recovery with strategy: ${strategy.type}`);
        const success = await strategy.execute(error);
        
        if (success) {
          console.log(`Recovery successful with strategy: ${strategy.type}`);
          error.details = { ...error.details, recoveredWith: strategy.type };
          return true;
        }
      } catch (recoveryError) {
        console.error(`Recovery strategy ${strategy.type} failed:`, recoveryError);
      }
    }

    console.error('All recovery strategies failed for error:', error.id);
    return false;
  }

  /**
   * Execute fallback strategy
   */
  private async executeFallbackStrategy(error: ErrorInfo): Promise<boolean> {
    if (!error.context?.apiEndpoint) return false;

    const endpoint = error.context.apiEndpoint;
    
    // Define fallback mappings
    const fallbackMappings: Record<string, string> = {
      '/api/v2/classification/train': '/api/classification/train',
      '/api/v2/classification/inference': '/api/classification/inference',
      '/api/v2/classification/engines': '/api/classification/engines'
    };

    const fallbackEndpoint = fallbackMappings[endpoint];
    if (!fallbackEndpoint) return false;

    try {
      // Attempt fallback request
      console.log(`Using fallback endpoint: ${fallbackEndpoint}`);
      // The actual fallback request would be handled by the calling code
      return true;
    } catch (fallbackError) {
      console.error('Fallback strategy failed:', fallbackError);
      return false;
    }
  }

  /**
   * Notify graceful degradation
   */
  private notifyGracefulDegradation(error: ErrorInfo): void {
    // Implement graceful degradation notifications
    console.warn('Graceful degradation activated for error:', error.id);
    
    // Could show user notification about reduced functionality
    if (this.options.enableNotifications) {
      // Show toast or notification to user
      this.showUserNotification({
        type: 'warning',
        message: 'Some features may be temporarily unavailable',
        details: 'We\'re working to restore full functionality'
      });
    }
  }

  /**
   * Log error
   */
  private logError(error: ErrorInfo): void {
    const logLevel = this.getLogLevel(error.severity);
    const logMessage = `[${error.type}] ${error.message}`;
    
    console[logLevel](logMessage, {
      id: error.id,
      severity: error.severity,
      details: error.details,
      context: error.context,
      timestamp: error.timestamp,
      stackTrace: error.stackTrace
    });

    // Send to external logging service if configured
    this.sendToExternalLogger(error);
  }

  /**
   * Get log level based on severity
   */
  private getLogLevel(severity: ErrorSeverity): 'log' | 'warn' | 'error' {
    switch (severity) {
      case ErrorSeverity.LOW:
        return 'log';
      case ErrorSeverity.MEDIUM:
        return 'warn';
      case ErrorSeverity.HIGH:
      case ErrorSeverity.CRITICAL:
        return 'error';
      default:
        return 'log';
    }
  }

  /**
   * Send error to external logging service
   */
  private sendToExternalLogger(error: ErrorInfo): void {
    // Implement external logging (e.g., Sentry, LogRocket, etc.)
    // This would be configured based on the environment
    if (process.env.NODE_ENV === 'production') {
      // Send to production logging service
    }
  }

  /**
   * Show user notification
   */
  private showUserNotification(notification: {
    type: 'info' | 'warning' | 'error';
    message: string;
    details?: string;
  }): void {
    // This would integrate with the app's notification system
    console.log('User notification:', notification);
  }

  /**
   * Notify error listeners
   */
  private notifyErrorListeners(error: ErrorInfo): void {
    this.errorListeners.forEach(listener => {
      try {
        listener(error);
      } catch (listenerError) {
        console.error('Error listener failed:', listenerError);
      }
    });
  }

  /**
   * Add error listener
   */
  addErrorListener(listener: (error: ErrorInfo) => void): void {
    this.errorListeners.add(listener);
  }

  /**
   * Remove error listener
   */
  removeErrorListener(listener: (error: ErrorInfo) => void): void {
    this.errorListeners.delete(listener);
  }

  /**
   * Get error by ID
   */
  getError(errorId: string): ErrorInfo | undefined {
    return this.errors.get(errorId);
  }

  /**
   * Get all errors
   */
  getAllErrors(): ErrorInfo[] {
    return Array.from(this.errors.values());
  }

  /**
   * Clear errors
   */
  clearErrors(): void {
    this.errors.clear();
  }

  /**
   * Utility: Delay function
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Handle API errors specifically
   */
  handleApiError(error: any, context?: ErrorContext): ErrorInfo {
    let errorType = ErrorType.API_ERROR;
    let severity = ErrorSeverity.MEDIUM;
    let retryable = true;

    // Determine error type based on status code
    if (error.response?.status) {
      const status = error.response.status;
      if (status === 401) {
        errorType = ErrorType.AUTHENTICATION_ERROR;
        severity = ErrorSeverity.HIGH;
      } else if (status === 429) {
        errorType = ErrorType.RATE_LIMIT_ERROR;
        severity = ErrorSeverity.MEDIUM;
      } else if (status >= 500) {
        errorType = ErrorType.SERVER_ERROR;
        severity = ErrorSeverity.HIGH;
      } else if (status >= 400) {
        errorType = ErrorType.CLIENT_ERROR;
        severity = ErrorSeverity.MEDIUM;
        retryable = false;
      }
    } else if (error.code === 'ECONNABORTED') {
      errorType = ErrorType.TIMEOUT_ERROR;
      severity = ErrorSeverity.MEDIUM;
    } else if (!navigator.onLine) {
      errorType = ErrorType.NETWORK_ERROR;
      severity = ErrorSeverity.HIGH;
    }

    const errorInfo = this.createErrorInfo({
      type: errorType,
      severity,
      message: error.message || 'API request failed',
      details: {
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        config: error.config
      },
      context,
      retryable
    });

    this.handleError(errorInfo);
    return errorInfo;
  }

  /**
   * Handle WebSocket errors specifically
   */
  handleWebSocketError(error: any, context?: ErrorContext): ErrorInfo {
    const errorInfo = this.createErrorInfo({
      type: ErrorType.WEBSOCKET_ERROR,
      severity: ErrorSeverity.MEDIUM,
      message: 'WebSocket connection error',
      details: error,
      context,
      retryable: true
    });

    this.handleError(errorInfo);
    return errorInfo;
  }
}

// Export singleton instance
export const errorHandlingService = new ErrorHandlingService();

export default errorHandlingService;
