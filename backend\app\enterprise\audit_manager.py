"""Audit Manager for Enterprise ClassyWeb.

Provides comprehensive audit trails, compliance monitoring, and governance features
for enterprise deployments.
"""

import logging
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
from enum import Enum
from dataclasses import dataclass, asdict
from sqlalchemy.orm import Session

logger = logging.getLogger(__name__)


class AuditEventType(Enum):
    """Types of audit events."""
    USER_LOGIN = "user_login"
    USER_LOGOUT = "user_logout"
    USER_CREATED = "user_created"
    USER_UPDATED = "user_updated"
    USER_DELETED = "user_deleted"
    
    MODEL_TRAINED = "model_trained"
    MODEL_DELETED = "model_deleted"
    MODEL_ACCESSED = "model_accessed"
    
    DATA_UPLOADED = "data_uploaded"
    DATA_PROCESSED = "data_processed"
    DATA_EXPORTED = "data_exported"
    DATA_DELETED = "data_deleted"
    
    CLASSIFICATION_RUN = "classification_run"
    WORKFLOW_EXECUTED = "workflow_executed"
    
    ADMIN_ACTION = "admin_action"
    SECURITY_EVENT = "security_event"
    COMPLIANCE_CHECK = "compliance_check"
    
    SYSTEM_ERROR = "system_error"
    SYSTEM_WARNING = "system_warning"


class AuditSeverity(Enum):
    """Severity levels for audit events."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class AuditEvent:
    """Represents a single audit event."""
    event_type: AuditEventType
    severity: AuditSeverity
    user_id: Optional[int]
    tenant_id: Optional[str]
    resource_id: Optional[str]
    resource_type: Optional[str]
    action: str
    details: Dict[str, Any]
    ip_address: Optional[str]
    user_agent: Optional[str]
    timestamp: datetime
    session_id: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert audit event to dictionary."""
        data = asdict(self)
        data['event_type'] = self.event_type.value
        data['severity'] = self.severity.value
        data['timestamp'] = self.timestamp.isoformat()
        return data


class AuditManager:
    """Manages audit trails and compliance monitoring for enterprise features."""
    
    def __init__(self):
        """Initialize the audit manager."""
        self.events_buffer: List[AuditEvent] = []
        self.buffer_size = 1000
        self.auto_flush_interval = 300  # 5 minutes
        self.last_flush = datetime.utcnow()
        
        logger.info("Audit Manager initialized")
    
    def log_event(
        self,
        event_type: AuditEventType,
        action: str,
        severity: AuditSeverity = AuditSeverity.LOW,
        user_id: Optional[int] = None,
        tenant_id: Optional[str] = None,
        resource_id: Optional[str] = None,
        resource_type: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        session_id: Optional[str] = None
    ) -> None:
        """Log an audit event."""
        try:
            event = AuditEvent(
                event_type=event_type,
                severity=severity,
                user_id=user_id,
                tenant_id=tenant_id,
                resource_id=resource_id,
                resource_type=resource_type,
                action=action,
                details=details or {},
                ip_address=ip_address,
                user_agent=user_agent,
                timestamp=datetime.utcnow(),
                session_id=session_id
            )
            
            self.events_buffer.append(event)
            
            # Auto-flush if buffer is full or time interval exceeded
            if (len(self.events_buffer) >= self.buffer_size or 
                (datetime.utcnow() - self.last_flush).seconds >= self.auto_flush_interval):
                self.flush_events()
                
        except Exception as e:
            logger.error(f"Failed to log audit event: {e}", exc_info=True)
    
    def flush_events(self) -> None:
        """Flush buffered events to storage."""
        if not self.events_buffer:
            return
            
        try:
            # In a real implementation, this would write to a database or external audit system
            # For now, we'll log to file
            self._write_events_to_log()
            
            logger.info(f"Flushed {len(self.events_buffer)} audit events")
            self.events_buffer.clear()
            self.last_flush = datetime.utcnow()
            
        except Exception as e:
            logger.error(f"Failed to flush audit events: {e}", exc_info=True)
    
    def _write_events_to_log(self) -> None:
        """Write events to audit log file."""
        try:
            import os
            log_dir = "logs"
            os.makedirs(log_dir, exist_ok=True)
            
            audit_log_path = os.path.join(log_dir, "audit.log")
            
            with open(audit_log_path, "a", encoding="utf-8") as f:
                for event in self.events_buffer:
                    f.write(json.dumps(event.to_dict()) + "\n")
                    
        except Exception as e:
            logger.error(f"Failed to write audit events to log: {e}", exc_info=True)
    
    def get_events(
        self,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None,
        event_types: Optional[List[AuditEventType]] = None,
        user_id: Optional[int] = None,
        tenant_id: Optional[str] = None,
        severity: Optional[AuditSeverity] = None,
        limit: int = 100
    ) -> List[AuditEvent]:
        """Retrieve audit events based on filters."""
        # In a real implementation, this would query a database
        # For now, return buffered events that match filters
        filtered_events = []
        
        for event in self.events_buffer:
            if start_time and event.timestamp < start_time:
                continue
            if end_time and event.timestamp > end_time:
                continue
            if event_types and event.event_type not in event_types:
                continue
            if user_id and event.user_id != user_id:
                continue
            if tenant_id and event.tenant_id != tenant_id:
                continue
            if severity and event.severity != severity:
                continue
                
            filtered_events.append(event)
            
            if len(filtered_events) >= limit:
                break
        
        return filtered_events
    
    def generate_compliance_report(
        self,
        tenant_id: Optional[str] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> Dict[str, Any]:
        """Generate a compliance report."""
        if not start_date:
            start_date = datetime.utcnow() - timedelta(days=30)
        if not end_date:
            end_date = datetime.utcnow()
        
        events = self.get_events(
            start_time=start_date,
            end_time=end_date,
            tenant_id=tenant_id,
            limit=10000
        )
        
        # Analyze events for compliance metrics
        report = {
            "report_period": {
                "start": start_date.isoformat(),
                "end": end_date.isoformat()
            },
            "tenant_id": tenant_id,
            "total_events": len(events),
            "event_breakdown": {},
            "security_events": 0,
            "data_access_events": 0,
            "user_activity": {},
            "compliance_score": 0.0,
            "recommendations": []
        }
        
        # Count events by type
        for event in events:
            event_type = event.event_type.value
            report["event_breakdown"][event_type] = report["event_breakdown"].get(event_type, 0) + 1
            
            if event.event_type == AuditEventType.SECURITY_EVENT:
                report["security_events"] += 1
            
            if event.event_type in [AuditEventType.DATA_UPLOADED, AuditEventType.DATA_PROCESSED, 
                                   AuditEventType.DATA_EXPORTED, AuditEventType.DATA_DELETED]:
                report["data_access_events"] += 1
            
            if event.user_id:
                user_key = str(event.user_id)
                if user_key not in report["user_activity"]:
                    report["user_activity"][user_key] = 0
                report["user_activity"][user_key] += 1
        
        # Calculate compliance score (simplified)
        report["compliance_score"] = min(100.0, max(0.0, 100.0 - (report["security_events"] * 5)))
        
        # Generate recommendations
        if report["security_events"] > 10:
            report["recommendations"].append("High number of security events detected. Review security policies.")
        
        if report["data_access_events"] == 0:
            report["recommendations"].append("No data access events recorded. Verify audit logging is working.")
        
        return report
    
    def cleanup_old_events(self, days_to_keep: int = 90) -> int:
        """Clean up old audit events."""
        cutoff_date = datetime.utcnow() - timedelta(days=days_to_keep)
        
        # In a real implementation, this would delete from database
        # For now, just remove from buffer
        original_count = len(self.events_buffer)
        self.events_buffer = [event for event in self.events_buffer if event.timestamp > cutoff_date]
        removed_count = original_count - len(self.events_buffer)
        
        logger.info(f"Cleaned up {removed_count} old audit events")
        return removed_count


# Global audit manager instance
audit_manager = AuditManager()
