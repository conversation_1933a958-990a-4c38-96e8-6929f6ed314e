# Multi-Class Classification Technical Implementation

This document provides technical details for developers implementing or extending the multi-class classification functionality in ClassyWeb.

## Architecture Overview

### Component Structure

```
MultiClassWorkflow
├── DataUpload & Validation
├── Configuration & Strategy Selection
├── Training Pipeline
│   ├── MultiClassTrainingConfig
│   ├── TrainingProgressMonitor
│   └── ModelManager
├── Classification Pipeline
│   ├── BatchClassification
│   └── RealTimeClassification
├── Results Analysis
│   ├── MultiClassResults
│   ├── ConfusionMatrix
│   └── PerformanceMetrics
└── Deployment
    ├── LocalExport
    ├── APIDeployment
    └── CloudDeployment
```

### Key Components

#### 1. MultiClassWorkflow Component

**Location**: `frontend/src/components/classification/MultiClassWorkflow.tsx`

**Responsibilities**:
- Orchestrates the entire multi-class workflow
- Manages state transitions between steps
- Handles error recovery and progress monitoring
- Integrates with backend APIs

**Key Features**:
- Dual data upload support (training + classification)
- Strategy recommendation engine
- Real-time progress monitoring
- Comprehensive error handling

#### 2. MultiClassTrainingConfig Component

**Location**: `frontend/src/components/classification/MultiClassTrainingConfig.tsx`

**Responsibilities**:
- Provides training configuration interface
- Validates training parameters
- Offers preset configurations
- Handles strategy-specific settings

**Key Features**:
- Intelligent parameter validation
- Preset configuration templates
- Strategy-aware recommendations
- Real-time configuration preview

#### 3. MultiClassResults Component

**Location**: `frontend/src/components/classification/MultiClassResults.tsx`

**Responsibilities**:
- Displays comprehensive results analysis
- Provides interactive visualizations
- Handles results export
- Supports strategy comparison

**Key Features**:
- Multiple visualization types (bar, radar, pie charts)
- Interactive confusion matrix
- Per-class performance metrics
- Export in multiple formats

## Backend Implementation

### API Endpoints

#### Training Endpoint

```python
@router.post("/train/multi-class")
async def train_multiclass_model(
    request: MultiClassTrainingRequest,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Train a multi-class classification model"""
    
    # Validate request
    validate_training_request(request)
    
    # Create training task
    task_id = create_training_task(request, current_user.id)
    
    # Start background training
    background_tasks.add_task(
        execute_multiclass_training,
        task_id,
        request,
        current_user.id
    )
    
    return {"task_id": task_id, "status": "started"}
```

#### Classification Endpoint

```python
@router.post("/classify/multi-class")
async def classify_multiclass(
    request: MultiClassInferenceRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Perform multi-class classification"""
    
    # Load model
    model = load_multiclass_model(request.model_id)
    
    # Load and preprocess data
    data = load_classification_data(request.file_id)
    processed_data = preprocess_multiclass_data(data, request.text_column)
    
    # Perform classification
    predictions = model.predict(processed_data)
    
    # Format results
    results = format_multiclass_results(predictions, request.strategy)
    
    return {"predictions": results, "summary": generate_summary(results)}
```

### Training Pipeline

#### Strategy Implementation

```python
class MultiClassStrategy:
    """Base class for multi-class strategies"""
    
    def __init__(self, num_classes: int, class_names: List[str]):
        self.num_classes = num_classes
        self.class_names = class_names
    
    def prepare_model(self, base_model):
        """Prepare model for specific strategy"""
        raise NotImplementedError
    
    def compute_loss(self, logits, labels):
        """Compute strategy-specific loss"""
        raise NotImplementedError
    
    def predict(self, logits):
        """Generate predictions from logits"""
        raise NotImplementedError

class SoftmaxStrategy(MultiClassStrategy):
    """Standard softmax strategy for multi-class classification"""
    
    def prepare_model(self, base_model):
        return nn.Sequential(
            base_model,
            nn.Linear(base_model.config.hidden_size, self.num_classes)
        )
    
    def compute_loss(self, logits, labels):
        return F.cross_entropy(logits, labels)
    
    def predict(self, logits):
        probabilities = F.softmax(logits, dim=-1)
        predictions = torch.argmax(probabilities, dim=-1)
        return predictions, probabilities

class OneVsRestStrategy(MultiClassStrategy):
    """One-vs-Rest strategy for imbalanced multi-class classification"""
    
    def prepare_model(self, base_model):
        return nn.Sequential(
            base_model,
            nn.Linear(base_model.config.hidden_size, self.num_classes),
            nn.Sigmoid()  # Independent binary classifiers
        )
    
    def compute_loss(self, logits, labels):
        # Convert to one-hot encoding
        one_hot = F.one_hot(labels, num_classes=self.num_classes).float()
        return F.binary_cross_entropy(logits, one_hot)
    
    def predict(self, logits):
        # Take class with highest probability
        predictions = torch.argmax(logits, dim=-1)
        return predictions, logits
```

#### Training Loop

```python
def train_multiclass_model(
    model,
    train_dataloader,
    val_dataloader,
    strategy: MultiClassStrategy,
    config: TrainingConfig,
    progress_callback=None
):
    """Main training loop for multi-class models"""
    
    optimizer = AdamW(model.parameters(), lr=config.learning_rate)
    scheduler = get_linear_schedule_with_warmup(
        optimizer,
        num_warmup_steps=config.warmup_steps,
        num_training_steps=len(train_dataloader) * config.num_epochs
    )
    
    best_val_accuracy = 0
    patience_counter = 0
    
    for epoch in range(config.num_epochs):
        # Training phase
        model.train()
        total_loss = 0
        correct_predictions = 0
        total_predictions = 0
        
        for batch_idx, batch in enumerate(train_dataloader):
            optimizer.zero_grad()
            
            # Forward pass
            outputs = model(
                input_ids=batch['input_ids'],
                attention_mask=batch['attention_mask']
            )
            
            # Compute loss
            loss = strategy.compute_loss(outputs.logits, batch['labels'])
            
            # Backward pass
            loss.backward()
            optimizer.step()
            scheduler.step()
            
            # Track metrics
            total_loss += loss.item()
            predictions, _ = strategy.predict(outputs.logits)
            correct_predictions += (predictions == batch['labels']).sum().item()
            total_predictions += batch['labels'].size(0)
            
            # Progress callback
            if progress_callback and batch_idx % 10 == 0:
                progress = {
                    'epoch': epoch + 1,
                    'batch': batch_idx,
                    'loss': loss.item(),
                    'accuracy': correct_predictions / total_predictions
                }
                progress_callback(progress)
        
        # Validation phase
        val_accuracy = evaluate_model(model, val_dataloader, strategy)
        
        # Early stopping
        if val_accuracy > best_val_accuracy:
            best_val_accuracy = val_accuracy
            patience_counter = 0
            save_checkpoint(model, f"best_model_epoch_{epoch}.pt")
        else:
            patience_counter += 1
            if patience_counter >= config.patience:
                print(f"Early stopping at epoch {epoch}")
                break
    
    return model, best_val_accuracy
```

### Data Processing

#### Text Preprocessing

```python
class MultiClassDataProcessor:
    """Data processor for multi-class classification"""
    
    def __init__(self, tokenizer, max_length=512):
        self.tokenizer = tokenizer
        self.max_length = max_length
    
    def process_dataset(self, texts: List[str], labels: List[str]):
        """Process texts and labels for training"""
        
        # Create label mapping
        unique_labels = sorted(list(set(labels)))
        label_to_id = {label: idx for idx, label in enumerate(unique_labels)}
        id_to_label = {idx: label for label, idx in label_to_id.items()}
        
        # Tokenize texts
        encodings = self.tokenizer(
            texts,
            truncation=True,
            padding=True,
            max_length=self.max_length,
            return_tensors='pt'
        )
        
        # Convert labels to IDs
        label_ids = [label_to_id[label] for label in labels]
        
        return {
            'input_ids': encodings['input_ids'],
            'attention_mask': encodings['attention_mask'],
            'labels': torch.tensor(label_ids),
            'label_mapping': {
                'label_to_id': label_to_id,
                'id_to_label': id_to_label
            }
        }
    
    def process_for_inference(self, texts: List[str]):
        """Process texts for inference"""
        
        encodings = self.tokenizer(
            texts,
            truncation=True,
            padding=True,
            max_length=self.max_length,
            return_tensors='pt'
        )
        
        return {
            'input_ids': encodings['input_ids'],
            'attention_mask': encodings['attention_mask']
        }
```

#### Data Validation

```python
def validate_multiclass_data(data: pd.DataFrame, text_column: str, label_column: str):
    """Validate multi-class training data"""
    
    errors = []
    warnings = []
    
    # Check required columns
    if text_column not in data.columns:
        errors.append(f"Text column '{text_column}' not found")
    
    if label_column not in data.columns:
        errors.append(f"Label column '{label_column}' not found")
    
    if errors:
        return {"valid": False, "errors": errors, "warnings": warnings}
    
    # Check data quality
    texts = data[text_column].dropna()
    labels = data[label_column].dropna()
    
    # Minimum samples check
    if len(texts) < 10:
        errors.append("Minimum 10 samples required")
    
    # Class distribution check
    unique_labels = labels.unique()
    if len(unique_labels) < 3:
        errors.append("Minimum 3 classes required for multi-class classification")
    
    # Class balance check
    label_counts = labels.value_counts()
    min_count = label_counts.min()
    max_count = label_counts.max()
    imbalance_ratio = max_count / min_count if min_count > 0 else float('inf')
    
    if imbalance_ratio > 10:
        warnings.append(f"High class imbalance detected (ratio: {imbalance_ratio:.1f}:1)")
    
    # Minimum samples per class
    if min_count < 5:
        warnings.append(f"Some classes have very few samples (minimum: {min_count})")
    
    # Text length check
    text_lengths = texts.str.len()
    avg_length = text_lengths.mean()
    
    if avg_length < 10:
        warnings.append("Average text length is very short")
    elif avg_length > 1000:
        warnings.append("Average text length is very long, consider truncation")
    
    return {
        "valid": len(errors) == 0,
        "errors": errors,
        "warnings": warnings,
        "stats": {
            "num_samples": len(texts),
            "num_classes": len(unique_labels),
            "class_distribution": label_counts.to_dict(),
            "imbalance_ratio": imbalance_ratio,
            "avg_text_length": avg_length
        }
    }
```

## Frontend Implementation

### State Management

```typescript
// Multi-class workflow state
interface MultiClassWorkflowState {
  // Data state
  uploadedData: FileInfo | null;
  dualData: DualDataState | null;
  detectedClasses: string[];
  
  // Configuration state
  selectedTextColumn: string;
  selectedLabelColumn: string;
  selectedStrategy: ClassificationStrategy;
  trainingConfig: MultiClassTrainingConfig;
  
  // Training state
  isTraining: boolean;
  trainingProgress: number;
  trainingTaskId: string | null;
  trainingResults: MultiClassTrainingResults | null;
  trainingStatus: DetailedTrainingStatus;
  
  // Classification state
  isClassifying: boolean;
  classificationProgress: number;
  classificationResults: any;
  classificationStatus: DetailedClassificationStatus;
  
  // Error handling
  error: string | null;
  errors: string[];
  errorHistory: ErrorHistoryItem[];
  retryCount: number;
  isRecovering: boolean;
  
  // UI state
  currentStep: number;
  workflowType: 'training' | 'model_classification';
  userJourney: 'beginner' | 'expert';
}
```

### Error Handling System

```typescript
// Centralized error handling
class ErrorHandler {
  private errorHistory: ErrorHistoryItem[] = [];
  private retryCount = 0;
  private maxRetries = 3;
  
  handleError(error: any, step: string, context?: any): void {
    const errorMessage = this.extractErrorMessage(error);
    
    // Log to history
    this.errorHistory.push({
      timestamp: new Date(),
      step,
      error: errorMessage,
      context
    });
    
    // Determine if recoverable
    if (this.isRecoverableError(error) && this.retryCount < this.maxRetries) {
      this.attemptRecovery(step, context);
    } else {
      this.showErrorToUser(errorMessage, step);
    }
  }
  
  private isRecoverableError(error: any): boolean {
    const message = error?.message?.toLowerCase() || '';
    
    return message.includes('network') ||
           message.includes('timeout') ||
           message.includes('rate limit') ||
           message.includes('server error');
  }
  
  private async attemptRecovery(step: string, context?: any): Promise<void> {
    this.retryCount++;
    
    // Wait with exponential backoff
    const delay = Math.pow(2, this.retryCount) * 1000;
    await new Promise(resolve => setTimeout(resolve, delay));
    
    // Retry the operation
    switch (step) {
      case 'training':
        await this.retryTraining(context);
        break;
      case 'classification':
        await this.retryClassification(context);
        break;
      default:
        throw new Error(`Unknown step for recovery: ${step}`);
    }
  }
  
  clearErrors(): void {
    this.errorHistory = [];
    this.retryCount = 0;
  }
}
```

### Progress Monitoring

```typescript
// Real-time progress monitoring
class ProgressMonitor {
  private intervals: Map<string, NodeJS.Timeout> = new Map();
  
  startMonitoring(
    taskId: string,
    onProgress: (progress: ProgressUpdate) => void,
    onComplete: (result: any) => void,
    onError: (error: any) => void
  ): void {
    const interval = setInterval(async () => {
      try {
        const status = await getUniversalTaskStatus(taskId);
        
        if (status.status === 'SUCCESS') {
          this.stopMonitoring(taskId);
          onComplete(status.result);
        } else if (status.status === 'FAILURE') {
          this.stopMonitoring(taskId);
          onError(new Error(status.message));
        } else {
          onProgress({
            progress: status.progress || 0,
            currentEpoch: status.current_epoch,
            totalEpochs: status.total_epochs,
            currentLoss: status.current_loss,
            currentAccuracy: status.current_accuracy,
            estimatedTimeRemaining: status.estimated_time_remaining
          });
        }
      } catch (error) {
        console.error('Error monitoring progress:', error);
      }
    }, 2000);
    
    this.intervals.set(taskId, interval);
  }
  
  stopMonitoring(taskId: string): void {
    const interval = this.intervals.get(taskId);
    if (interval) {
      clearInterval(interval);
      this.intervals.delete(taskId);
    }
  }
  
  stopAllMonitoring(): void {
    this.intervals.forEach(interval => clearInterval(interval));
    this.intervals.clear();
  }
}
```

## Testing Strategy

### Unit Tests

```typescript
// Component testing
describe('MultiClassWorkflow', () => {
  test('should handle data upload correctly', async () => {
    const mockFile = new File(['test content'], 'test.csv', { type: 'text/csv' });
    const { getByTestId } = render(<MultiClassWorkflow />);
    
    const uploadZone = getByTestId('file-upload-zone');
    fireEvent.drop(uploadZone, { dataTransfer: { files: [mockFile] } });
    
    await waitFor(() => {
      expect(getByTestId('upload-success')).toBeInTheDocument();
    });
  });
  
  test('should validate training configuration', () => {
    const config = {
      modelName: '',
      numEpochs: 0,
      batchSize: 0,
      learningRate: 0
    };
    
    const errors = validateTrainingConfig(config);
    expect(errors).toContain('Model name is required');
    expect(errors).toContain('Number of epochs must be between 1 and 20');
  });
});
```

### Integration Tests

```python
# API testing
def test_multiclass_training_pipeline():
    """Test complete multi-class training pipeline"""
    
    # Upload test data
    test_data = create_test_multiclass_data()
    file_response = client.post("/files/upload", files={"file": test_data})
    file_id = file_response.json()["file_id"]
    
    # Start training
    training_request = {
        "file_id": file_id,
        "text_column": "text",
        "label_column": "category",
        "classification_type": "multi-class",
        "config": {
            "model_name": "distilbert-base-uncased",
            "strategy": "softmax",
            "num_epochs": 1,  # Quick test
            "batch_size": 8
        }
    }
    
    training_response = client.post("/train/multi-class", json=training_request)
    assert training_response.status_code == 200
    
    task_id = training_response.json()["task_id"]
    
    # Wait for completion
    while True:
        status_response = client.get(f"/tasks/{task_id}/status")
        status = status_response.json()
        
        if status["status"] == "SUCCESS":
            break
        elif status["status"] == "FAILURE":
            pytest.fail(f"Training failed: {status['message']}")
        
        time.sleep(1)
    
    # Verify results
    model_id = status["result"]["model_id"]
    assert model_id is not None
    assert status["result"]["metrics"]["accuracy"] > 0
```

## Performance Optimization

### Model Optimization

```python
# Model quantization for deployment
def optimize_model_for_deployment(model_path: str, optimization_level: str = "standard"):
    """Optimize trained model for deployment"""
    
    model = torch.load(model_path)
    
    if optimization_level == "aggressive":
        # Dynamic quantization
        quantized_model = torch.quantization.quantize_dynamic(
            model, {torch.nn.Linear}, dtype=torch.qint8
        )
        return quantized_model
    
    elif optimization_level == "standard":
        # Convert to TorchScript
        model.eval()
        example_input = torch.randint(0, 1000, (1, 512))
        traced_model = torch.jit.trace(model, example_input)
        return traced_model
    
    return model
```

### Caching Strategy

```python
# Result caching for repeated classifications
class ClassificationCache:
    def __init__(self, max_size: int = 10000):
        self.cache = {}
        self.max_size = max_size
        self.access_times = {}
    
    def get(self, text_hash: str):
        if text_hash in self.cache:
            self.access_times[text_hash] = time.time()
            return self.cache[text_hash]
        return None
    
    def set(self, text_hash: str, result: dict):
        if len(self.cache) >= self.max_size:
            # Remove least recently used
            oldest_key = min(self.access_times.keys(), 
                           key=lambda k: self.access_times[k])
            del self.cache[oldest_key]
            del self.access_times[oldest_key]
        
        self.cache[text_hash] = result
        self.access_times[text_hash] = time.time()
```

## Deployment Considerations

### Scalability

- **Horizontal Scaling**: Deploy multiple model instances
- **Load Balancing**: Distribute requests across instances
- **Caching**: Implement Redis for result caching
- **Async Processing**: Use Celery for background tasks

### Monitoring

- **Performance Metrics**: Track latency, throughput, accuracy
- **Error Tracking**: Monitor and alert on errors
- **Resource Usage**: Monitor CPU, memory, GPU usage
- **Model Drift**: Track prediction distribution changes

### Security

- **API Authentication**: Secure API endpoints
- **Data Privacy**: Encrypt sensitive data
- **Model Protection**: Secure model artifacts
- **Audit Logging**: Log all operations

## API Reference Summary

### Training Endpoints

```
POST /api/train/multi-class
POST /api/tasks/{task_id}/status
GET  /api/models/{model_id}
```

### Classification Endpoints

```
POST /api/classify/multi-class
POST /api/classify/batch
GET  /api/classify/{task_id}/results
```

### Deployment Endpoints

```
POST /api/deploy
GET  /api/deploy/{deployment_id}/status
POST /api/export
```

### Model Management

```
GET    /api/models
POST   /api/models/{model_id}/versions
DELETE /api/models/{model_id}
```

---

For more technical details, see the [API Documentation](../api/multiclass-api.md) and [Deployment Guide](../deployment/multiclass-deployment.md).
