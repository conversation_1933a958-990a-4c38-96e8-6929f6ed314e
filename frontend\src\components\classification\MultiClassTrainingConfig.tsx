/**
 * MultiClassTrainingConfig.tsx
 *
 * Advanced training configuration component for multi-class classification.
 * Features strategy-specific parameters, class balance optimization, and comprehensive training settings.
 */

import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Slider } from "@/components/ui/slider";
import { Switch } from "@/components/ui/switch";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { useToast } from "@/hooks/use-toast";
import {
  Settings,
  Target,
  Network,
  TrendingUp,
  Zap,
  Brain,
  BarChart3,
  Info,
  AlertCircle,
  CheckCircle2,
  Sliders,
  Activity,
  Grid3X3,
  Award
} from "lucide-react";

export interface MultiClassTrainingConfig {
  // Base training parameters
  modelName: string;
  baseModel: string;
  maxLength: number;
  
  // Training parameters
  numEpochs: number;
  batchSize: number;
  learningRate: number;
  validationSplit: number;
  
  // Advanced parameters
  warmupSteps: number;
  weightDecay: number;
  gradientAccumulationSteps: number;
  
  // Hardware optimization
  useUnsloth: boolean;
  fp16: boolean;
  gradientCheckpointing: boolean;
  
  // Early stopping
  enableEarlyStopping: boolean;
  patience: number;
  minDelta: number;
  
  // Multi-class specific parameters
  strategy: 'softmax' | 'ovr' | 'ovo' | 'auto';
  classWeightStrategy: 'balanced' | 'custom' | 'none';
  multiClassLoss: 'categorical_crossentropy' | 'sparse_categorical_crossentropy' | 'focal_loss';
  
  // Strategy-specific parameters
  ovrConfig: { estimator: string; n_jobs: number };
  ovoConfig: { estimator: string; n_jobs: number };
  softmaxConfig: { temperature: number; label_smoothing: number };
  
  // Advanced multi-class features
  classBalanceHandling: 'none' | 'oversample' | 'undersample' | 'smote';
  customClassWeights: Record<string, number>;
  confidenceThreshold: number;
}

interface MultiClassTrainingConfigProps {
  onConfigChange: (config: MultiClassTrainingConfig) => void;
  onSave: (config: MultiClassTrainingConfig) => void;
  initialConfig?: Partial<MultiClassTrainingConfig>;
  userJourney: 'beginner' | 'expert';
  detectedClasses: string[];
  classDistribution?: Record<string, number>;
  estimatedTrainingTime?: string;
}

const DEFAULT_CONFIG: MultiClassTrainingConfig = {
  // Base parameters
  modelName: "multi-class-classifier",
  baseModel: "distilbert-base-uncased",
  maxLength: 512,
  
  // Training parameters
  numEpochs: 3,
  batchSize: 16,
  learningRate: 2e-5,
  validationSplit: 0.2,
  
  // Advanced parameters
  warmupSteps: 500,
  weightDecay: 0.01,
  gradientAccumulationSteps: 1,
  
  // Hardware optimization
  useUnsloth: true,
  fp16: true,
  gradientCheckpointing: true,
  
  // Early stopping
  enableEarlyStopping: true,
  patience: 2,
  minDelta: 0.001,
  
  // Multi-class specific
  strategy: 'softmax',
  classWeightStrategy: 'balanced',
  multiClassLoss: 'categorical_crossentropy',
  
  // Strategy-specific parameters
  ovrConfig: { estimator: 'logistic_regression', n_jobs: -1 },
  ovoConfig: { estimator: 'svm', n_jobs: -1 },
  softmaxConfig: { temperature: 1.0, label_smoothing: 0.0 },
  
  // Advanced features
  classBalanceHandling: 'none',
  customClassWeights: {},
  confidenceThreshold: 0.5
};

const TRANSFORMER_MODELS = [
  { value: "distilbert-base-uncased", label: "DistilBERT Base", description: "Fast and efficient" },
  { value: "bert-base-uncased", label: "BERT Base", description: "Balanced performance" },
  { value: "roberta-base", label: "RoBERTa Base", description: "High accuracy" },
  { value: "albert-base-v2", label: "ALBERT Base", description: "Memory efficient" },
  { value: "electra-base-discriminator", label: "ELECTRA Base", description: "Fast training" }
];

export const MultiClassTrainingConfig: React.FC<MultiClassTrainingConfigProps> = ({
  onConfigChange,
  onSave,
  initialConfig = {},
  userJourney,
  detectedClasses,
  classDistribution = {},
  estimatedTrainingTime
}) => {
  const { toast } = useToast();
  const [config, setConfig] = useState<MultiClassTrainingConfig>({
    ...DEFAULT_CONFIG,
    ...initialConfig
  });
  const [activeTab, setActiveTab] = useState('basic');
  const [isValidating, setIsValidating] = useState(false);
  const [validationErrors, setValidationErrors] = useState<string[]>([]);
  const [showAdvanced, setShowAdvanced] = useState(userJourney === 'expert');
  const [presetConfigs, setPresetConfigs] = useState<Record<string, Partial<MultiClassTrainingConfig>>>({});

  // Enhanced class analysis
  const classAnalysis = React.useMemo(() => {
    if (Object.keys(classDistribution).length === 0) {
      return {
        imbalanceRatio: 1,
        totalSamples: 0,
        minSamples: 0,
        maxSamples: 0,
        isBalanced: true,
        needsWeighting: false
      };
    }

    const counts = Object.values(classDistribution);
    const totalSamples = counts.reduce((sum, count) => sum + count, 0);
    const maxCount = Math.max(...counts);
    const minCount = Math.min(...counts);
    const imbalanceRatio = maxCount / minCount;

    return {
      imbalanceRatio,
      totalSamples,
      minSamples: minCount,
      maxSamples: maxCount,
      isBalanced: imbalanceRatio <= 2,
      needsWeighting: imbalanceRatio > 3
    };
  }, [classDistribution]);

  // Enhanced strategy recommendation with reasoning
  const strategyRecommendation = React.useMemo(() => {
    const numClasses = detectedClasses.length;
    const { totalSamples, imbalanceRatio, isBalanced } = classAnalysis;

    let strategy: 'softmax' | 'ovr' | 'ovo' | 'auto';
    let reasoning: string;
    let confidence: number;

    if (numClasses <= 3 && isBalanced) {
      strategy = 'softmax';
      reasoning = 'Few classes with balanced distribution - softmax is optimal';
      confidence = 0.9;
    } else if (imbalanceRatio > 5) {
      strategy = 'ovr';
      reasoning = 'High class imbalance detected - One-vs-Rest handles imbalanced data well';
      confidence = 0.8;
    } else if (numClasses > 10 && totalSamples < 1000) {
      strategy = 'ovo';
      reasoning = 'Many classes with limited data - One-vs-One reduces complexity';
      confidence = 0.7;
    } else if (numClasses > 20) {
      strategy = 'ovr';
      reasoning = 'Large number of classes - One-vs-Rest is more efficient';
      confidence = 0.8;
    } else {
      strategy = 'softmax';
      reasoning = 'Standard multi-class scenario - softmax is recommended';
      confidence = 0.7;
    }

    return { strategy, reasoning, confidence };
  }, [detectedClasses.length, classAnalysis]);

  // Initialize preset configurations
  useEffect(() => {
    const presets = {
      'fast': {
        numEpochs: 2,
        batchSize: 32,
        learningRate: 5e-5,
        useUnsloth: true,
        fp16: true,
        gradientCheckpointing: false,
        enableEarlyStopping: false
      },
      'balanced': {
        numEpochs: 3,
        batchSize: 16,
        learningRate: 2e-5,
        useUnsloth: true,
        fp16: true,
        gradientCheckpointing: true,
        enableEarlyStopping: true,
        patience: 2
      },
      'high-quality': {
        numEpochs: 5,
        batchSize: 8,
        learningRate: 1e-5,
        useUnsloth: false,
        fp16: false,
        gradientCheckpointing: true,
        enableEarlyStopping: true,
        patience: 3,
        warmupSteps: 1000
      }
    };
    setPresetConfigs(presets);
  }, []);

  // Enhanced validation
  const validateConfig = (configToValidate: MultiClassTrainingConfig): string[] => {
    const errors: string[] = [];

    // Basic validation
    if (!configToValidate.modelName.trim()) {
      errors.push('Model name is required');
    }

    if (configToValidate.numEpochs < 1 || configToValidate.numEpochs > 20) {
      errors.push('Number of epochs must be between 1 and 20');
    }

    if (configToValidate.batchSize < 1 || configToValidate.batchSize > 128) {
      errors.push('Batch size must be between 1 and 128');
    }

    if (configToValidate.learningRate < 1e-6 || configToValidate.learningRate > 1e-2) {
      errors.push('Learning rate must be between 1e-6 and 1e-2');
    }

    if (configToValidate.validationSplit < 0.1 || configToValidate.validationSplit > 0.5) {
      errors.push('Validation split must be between 0.1 and 0.5');
    }

    // Multi-class specific validation
    if (detectedClasses.length < 3) {
      errors.push('Multi-class classification requires at least 3 classes');
    }

    if (configToValidate.strategy === 'ovo' && detectedClasses.length > 50) {
      errors.push('One-vs-One strategy is not recommended for more than 50 classes');
    }

    // Class weight validation
    if (configToValidate.classWeightStrategy === 'custom') {
      const customWeights = configToValidate.customClassWeights;
      const missingWeights = detectedClasses.filter(cls => !(cls in customWeights));
      if (missingWeights.length > 0) {
        errors.push(`Missing custom weights for classes: ${missingWeights.join(', ')}`);
      }
    }

    return errors;
  };

  useEffect(() => {
    const errors = validateConfig(config);
    setValidationErrors(errors);
    onConfigChange(config);
  }, [config, onConfigChange, detectedClasses]);

  const updateConfig = (updates: Partial<MultiClassTrainingConfig>) => {
    setConfig(prev => ({ ...prev, ...updates }));
  };

  // Apply preset configuration
  const applyPreset = (presetName: string) => {
    const preset = presetConfigs[presetName];
    if (preset) {
      updateConfig(preset);
      toast({
        title: "Preset applied",
        description: `Applied ${presetName} configuration preset`
      });
    }
  };

  // Apply strategy recommendation
  const applyRecommendedStrategy = () => {
    if (strategyRecommendation?.strategy) {
      updateConfig({ strategy: strategyRecommendation.strategy });
      toast({
        title: "Strategy applied",
        description: strategyRecommendation.reasoning || "Strategy recommendation applied"
      });
    } else {
      toast({
        title: "No recommendation available",
        description: "Unable to apply strategy recommendation",
        variant: "destructive"
      });
    }
  };

  // Auto-configure class weights based on distribution
  const autoConfigureClassWeights = () => {
    if (classAnalysis.needsWeighting) {
      const weights: Record<string, number> = {};
      const { totalSamples } = classAnalysis;

      detectedClasses.forEach(className => {
        const classCount = classDistribution[className] || 1;
        weights[className] = totalSamples / (detectedClasses.length * classCount);
      });

      updateConfig({
        classWeightStrategy: 'custom',
        customClassWeights: weights
      });

      toast({
        title: "Class weights configured",
        description: "Automatically balanced class weights based on distribution"
      });
    }
  };

  const handleSave = () => {
    const errors = validateConfig(config);
    if (errors.length === 0) {
      onSave(config);
      toast({
        title: "Configuration saved",
        description: "Multi-class training configuration has been saved successfully"
      });
    } else {
      toast({
        title: "Validation failed",
        description: "Please fix the configuration errors before saving",
        variant: "destructive"
      });
    }
  };

  const getStrategyDescription = (strategy: string) => {
    switch (strategy) {
      case 'softmax':
        return "Direct multi-class classification with softmax activation. Best for balanced datasets.";
      case 'ovr':
        return "One-vs-Rest: Train binary classifiers for each class. Good for imbalanced data.";
      case 'ovo':
        return "One-vs-One: Train classifiers for each pair of classes. Effective for small datasets.";
      case 'auto':
        return "Automatically select the best strategy based on your data characteristics.";
      default:
        return "";
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold flex items-center gap-2">
            <Settings className="w-5 h-5" />
            Multi-Class Training Configuration
          </h3>
          <p className="text-sm text-muted-foreground">
            Configure advanced training parameters for multi-class classification
          </p>
        </div>
        {estimatedTrainingTime && (
          <Badge variant="outline" className="flex items-center gap-1">
            <Activity className="w-3 h-3" />
            Est. {estimatedTrainingTime}
          </Badge>
        )}
      </div>

      {/* Enhanced Class Analysis */}
      {detectedClasses.length > 0 && (
        <div className="space-y-4">
          <Alert>
            <Grid3X3 className="h-4 w-4" />
            <AlertDescription>
              <div className="space-y-3">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <strong>Dataset Analysis:</strong>
                    <div className="text-sm mt-1 space-y-1">
                      <div>{detectedClasses.length} classes detected</div>
                      <div>{classAnalysis.totalSamples.toLocaleString()} total samples</div>
                      <div>Range: {classAnalysis.minSamples} - {classAnalysis.maxSamples} samples per class</div>
                    </div>
                  </div>
                  <div>
                    <strong>Balance Status:</strong>
                    <div className="text-sm mt-1 space-y-1">
                      <div className="flex items-center gap-2">
                        {classAnalysis.isBalanced ? (
                          <Badge variant="default">Balanced</Badge>
                        ) : (
                          <Badge variant="destructive">
                            Imbalanced ({classAnalysis.imbalanceRatio.toFixed(1)}:1)
                          </Badge>
                        )}
                      </div>
                      {classAnalysis.needsWeighting && (
                        <Button
                          variant="link"
                          size="sm"
                          className="p-0 h-auto"
                          onClick={autoConfigureClassWeights}
                        >
                          Auto-balance weights
                        </Button>
                      )}
                    </div>
                  </div>
                </div>

                <div className="border-t pt-3">
                  <div className="flex items-center justify-between">
                    <div>
                      <strong>Recommended Strategy:</strong> {strategyRecommendation?.strategy?.toUpperCase() || 'N/A'}
                      <div className="text-xs text-muted-foreground mt-1">
                        {strategyRecommendation?.reasoning || 'No recommendation available'}
                        {strategyRecommendation?.confidence && ` (Confidence: ${(strategyRecommendation.confidence * 100).toFixed(0)}%)`}
                      </div>
                    </div>
                    {config.strategy !== strategyRecommendation?.strategy && strategyRecommendation?.strategy && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={applyRecommendedStrategy}
                      >
                        Apply Recommendation
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            </AlertDescription>
          </Alert>

          {/* Preset Configurations */}
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">Quick Configuration Presets</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-3 gap-3">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => applyPreset('fast')}
                  className="flex flex-col h-auto p-3"
                >
                  <Zap className="w-4 h-4 mb-1" />
                  <span className="text-xs font-medium">Fast Training</span>
                  <span className="text-xs text-muted-foreground">2 epochs, optimized</span>
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => applyPreset('balanced')}
                  className="flex flex-col h-auto p-3"
                >
                  <Target className="w-4 h-4 mb-1" />
                  <span className="text-xs font-medium">Balanced</span>
                  <span className="text-xs text-muted-foreground">3 epochs, recommended</span>
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => applyPreset('high-quality')}
                  className="flex flex-col h-auto p-3"
                >
                  <Award className="w-4 h-4 mb-1" />
                  <span className="text-xs font-medium">High Quality</span>
                  <span className="text-xs text-muted-foreground">5 epochs, thorough</span>
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Validation Errors */}
      {validationErrors.length > 0 && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            <ul className="list-disc list-inside space-y-1">
              {validationErrors.map((error, index) => (
                <li key={index}>{error}</li>
              ))}
            </ul>
          </AlertDescription>
        </Alert>
      )}

      {/* Configuration Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="basic">Basic</TabsTrigger>
          <TabsTrigger value="strategy">Strategy</TabsTrigger>
          <TabsTrigger value="advanced">Advanced</TabsTrigger>
          <TabsTrigger value="optimization">Optimization</TabsTrigger>
        </TabsList>

        {/* Basic Configuration */}
        <TabsContent value="basic" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Brain className="w-4 h-4" />
                Model Configuration
              </CardTitle>
              <CardDescription>
                Basic model and training parameters
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="modelName">Model Name</Label>
                  <Input
                    id="modelName"
                    value={config.modelName}
                    onChange={(e) => updateConfig({ modelName: e.target.value })}
                    placeholder="Enter model name"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="baseModel">Base Model</Label>
                  <Select
                    value={config.baseModel}
                    onValueChange={(value) => updateConfig({ baseModel: value })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {TRANSFORMER_MODELS.map((model) => (
                        <SelectItem key={model.value} value={model.value}>
                          <div className="flex flex-col">
                            <span>{model.label}</span>
                            <span className="text-xs text-muted-foreground">{model.description}</span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="grid grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="numEpochs">Epochs</Label>
                  <Input
                    id="numEpochs"
                    type="number"
                    min="1"
                    max="100"
                    value={config.numEpochs}
                    onChange={(e) => updateConfig({ numEpochs: parseInt(e.target.value) || 1 })}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="batchSize">Batch Size</Label>
                  <Input
                    id="batchSize"
                    type="number"
                    min="1"
                    max="128"
                    value={config.batchSize}
                    onChange={(e) => updateConfig({ batchSize: parseInt(e.target.value) || 1 })}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="learningRate">Learning Rate</Label>
                  <Input
                    id="learningRate"
                    type="number"
                    step="0.00001"
                    min="0.00001"
                    max="1"
                    value={config.learningRate}
                    onChange={(e) => updateConfig({ learningRate: parseFloat(e.target.value) || 0.00001 })}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label>Validation Split: {(config.validationSplit * 100).toFixed(0)}%</Label>
                <Slider
                  value={[config.validationSplit]}
                  onValueChange={([value]) => updateConfig({ validationSplit: value })}
                  min={0.1}
                  max={0.4}
                  step={0.05}
                  className="w-full"
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Strategy Configuration */}
        <TabsContent value="strategy" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Grid3X3 className="w-4 h-4" />
                Classification Strategy
              </CardTitle>
              <CardDescription>
                Choose the multi-class classification approach
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-4">
                {(['softmax', 'ovr', 'ovo', 'auto'] as const).map((strategy) => (
                  <Card
                    key={strategy}
                    className={`cursor-pointer transition-all ${
                      config.strategy === strategy
                        ? 'ring-2 ring-primary bg-primary/5'
                        : 'hover:bg-muted/50'
                    }`}
                    onClick={() => updateConfig({ strategy })}
                  >
                    <CardContent className="p-4">
                      <div className="flex items-start gap-3">
                        <div className={`p-2 rounded-lg ${
                          config.strategy === strategy
                            ? 'bg-primary text-white'
                            : 'bg-muted'
                        }`}>
                          <Target className="w-4 h-4" />
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center gap-2">
                            <h4 className="font-medium">{strategy.toUpperCase()}</h4>
                            {strategy === strategyRecommendation?.strategy && (
                              <Badge variant="secondary">Recommended</Badge>
                            )}
                          </div>
                          <p className="text-sm text-muted-foreground mt-1">
                            {getStrategyDescription(strategy)}
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>

              {/* Strategy-specific configuration */}
              {config.strategy === 'softmax' && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm">Softmax Configuration</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label>Temperature: {config.softmaxConfig.temperature}</Label>
                        <Slider
                          value={[config.softmaxConfig.temperature]}
                          onValueChange={([value]) => updateConfig({
                            softmaxConfig: { ...config.softmaxConfig, temperature: value }
                          })}
                          min={0.1}
                          max={2.0}
                          step={0.1}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label>Label Smoothing: {config.softmaxConfig.label_smoothing}</Label>
                        <Slider
                          value={[config.softmaxConfig.label_smoothing]}
                          onValueChange={([value]) => updateConfig({
                            softmaxConfig: { ...config.softmaxConfig, label_smoothing: value }
                          })}
                          min={0.0}
                          max={0.3}
                          step={0.01}
                        />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Advanced Configuration */}
        <TabsContent value="advanced" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Sliders className="w-4 h-4" />
                Advanced Parameters
              </CardTitle>
              <CardDescription>
                Fine-tune training behavior and class handling
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="classWeightStrategy">Class Weight Strategy</Label>
                  <Select
                    value={config.classWeightStrategy}
                    onValueChange={(value: any) => updateConfig({ classWeightStrategy: value })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="balanced">Balanced (Auto-weight)</SelectItem>
                      <SelectItem value="custom">Custom Weights</SelectItem>
                      <SelectItem value="none">No Weighting</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="multiClassLoss">Loss Function</Label>
                  <Select
                    value={config.multiClassLoss}
                    onValueChange={(value: any) => updateConfig({ multiClassLoss: value })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="categorical_crossentropy">Categorical Cross-entropy</SelectItem>
                      <SelectItem value="sparse_categorical_crossentropy">Sparse Categorical Cross-entropy</SelectItem>
                      <SelectItem value="focal_loss">Focal Loss (for imbalanced data)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="classBalanceHandling">Class Balance Handling</Label>
                <Select
                  value={config.classBalanceHandling}
                  onValueChange={(value: any) => updateConfig({ classBalanceHandling: value })}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="none">None</SelectItem>
                    <SelectItem value="oversample">Oversample Minority Classes</SelectItem>
                    <SelectItem value="undersample">Undersample Majority Classes</SelectItem>
                    <SelectItem value="smote">SMOTE (Synthetic Oversampling)</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label>Confidence Threshold: {config.confidenceThreshold}</Label>
                <Slider
                  value={[config.confidenceThreshold]}
                  onValueChange={([value]) => updateConfig({ confidenceThreshold: value })}
                  min={0.1}
                  max={0.9}
                  step={0.05}
                  className="w-full"
                />
              </div>

              {/* Early Stopping */}
              <Separator />
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label htmlFor="enableEarlyStopping">Enable Early Stopping</Label>
                  <Switch
                    id="enableEarlyStopping"
                    checked={config.enableEarlyStopping}
                    onCheckedChange={(checked) => updateConfig({ enableEarlyStopping: checked })}
                  />
                </div>

                {config.enableEarlyStopping && (
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="patience">Patience (epochs)</Label>
                      <Input
                        id="patience"
                        type="number"
                        min="1"
                        max="20"
                        value={config.patience}
                        onChange={(e) => updateConfig({ patience: parseInt(e.target.value) || 1 })}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="minDelta">Min Delta</Label>
                      <Input
                        id="minDelta"
                        type="number"
                        step="0.0001"
                        min="0.0001"
                        max="0.1"
                        value={config.minDelta}
                        onChange={(e) => updateConfig({ minDelta: parseFloat(e.target.value) || 0.001 })}
                      />
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Optimization Configuration */}
        <TabsContent value="optimization" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Zap className="w-4 h-4" />
                Hardware Optimization
              </CardTitle>
              <CardDescription>
                GPU acceleration and memory optimization settings
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="useUnsloth">Enable Unsloth (GPU Acceleration)</Label>
                    <p className="text-xs text-muted-foreground">2x faster training with NVIDIA GPUs</p>
                  </div>
                  <Switch
                    id="useUnsloth"
                    checked={config.useUnsloth}
                    onCheckedChange={(checked) => updateConfig({ useUnsloth: checked })}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="fp16">Mixed Precision (FP16)</Label>
                    <p className="text-xs text-muted-foreground">Reduce memory usage and increase speed</p>
                  </div>
                  <Switch
                    id="fp16"
                    checked={config.fp16}
                    onCheckedChange={(checked) => updateConfig({ fp16: checked })}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="gradientCheckpointing">Gradient Checkpointing</Label>
                    <p className="text-xs text-muted-foreground">Trade compute for memory</p>
                  </div>
                  <Switch
                    id="gradientCheckpointing"
                    checked={config.gradientCheckpointing}
                    onCheckedChange={(checked) => updateConfig({ gradientCheckpointing: checked })}
                  />
                </div>
              </div>

              <Separator />

              <div className="grid grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="warmupSteps">Warmup Steps</Label>
                  <Input
                    id="warmupSteps"
                    type="number"
                    min="0"
                    max="2000"
                    value={config.warmupSteps}
                    onChange={(e) => updateConfig({ warmupSteps: parseInt(e.target.value) || 0 })}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="weightDecay">Weight Decay</Label>
                  <Input
                    id="weightDecay"
                    type="number"
                    step="0.001"
                    min="0"
                    max="1"
                    value={config.weightDecay}
                    onChange={(e) => updateConfig({ weightDecay: parseFloat(e.target.value) || 0 })}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="gradientAccumulationSteps">Gradient Accumulation</Label>
                  <Input
                    id="gradientAccumulationSteps"
                    type="number"
                    min="1"
                    max="16"
                    value={config.gradientAccumulationSteps}
                    onChange={(e) => updateConfig({ gradientAccumulationSteps: parseInt(e.target.value) || 1 })}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Save Button */}
      <div className="flex justify-end">
        <Button onClick={handleSave} className="flex items-center gap-2">
          <CheckCircle2 className="w-4 h-4" />
          Save Configuration
        </Button>
      </div>
    </div>
  );
};
