/**
 * HierarchyLevelSelector.tsx
 * 
 * Component for configuring hierarchy levels from data columns
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  Plus, 
  Minus, 
  TreePine, 
  ArrowDown, 
  ArrowUp, 
  AlertCircle,
  CheckCircle2,
  Info
} from "lucide-react";
import { toast } from "@/hooks/use-toast";

interface HierarchyLevel {
  id: string;
  name: string;
  column: string;
  displayName: string;
  description?: string;
  order: number;
}

interface HierarchyLevelSelectorProps {
  availableColumns: string[];
  columnInfo: Record<string, any>;
  onLevelsChange: (levels: HierarchyLevel[]) => void;
  initialLevels?: HierarchyLevel[];
  maxLevels?: number;
  minLevels?: number;
  trainingData?: any[]; // Add training data for column analysis
  onColumnInfoUpdate?: (columnInfo: Record<string, any>) => void; // Callback to update column info
}

export const HierarchyLevelSelector: React.FC<HierarchyLevelSelectorProps> = ({
  availableColumns,
  columnInfo,
  onLevelsChange,
  initialLevels = [],
  maxLevels = 10,
  minLevels = 2,
  trainingData = [],
  onColumnInfoUpdate
}) => {
  const [levels, setLevels] = useState<HierarchyLevel[]>(initialLevels);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Update levels when initialLevels changes
  useEffect(() => {
    if (initialLevels.length > 0) {
      // Use provided initial levels (e.g., from hierarchy detection)
      console.log('HierarchyLevelSelector: Using provided initial levels:', initialLevels);
      setLevels(initialLevels);
      onLevelsChange(initialLevels);
    } else if (availableColumns.length > 0) {
      // Auto-detect potential hierarchy columns only if no initial levels provided
      const hierarchyColumns = availableColumns.filter(col =>
        col.toLowerCase().includes('level') ||
        col.toLowerCase().includes('category') ||
        col.toLowerCase().includes('class') ||
        col.toLowerCase().includes('type') ||
        col.toLowerCase().includes('group')
      ).slice(0, Math.min(4, maxLevels));

      if (hierarchyColumns.length >= minLevels) {
        const autoLevels = hierarchyColumns.map((col, index) => ({
          id: `level_${index}`,
          name: `Level ${index + 1}`,
          column: col,
          displayName: col.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
          description: `Hierarchy level ${index + 1} - ${index === 0 ? 'Most general' : index === hierarchyColumns.length - 1 ? 'Most specific' : 'Intermediate level'}`,
          order: index
        }));
        console.log('HierarchyLevelSelector: Auto-detected levels:', autoLevels);
        setLevels(autoLevels);
        onLevelsChange(autoLevels);
      }
    }
  }, [availableColumns, initialLevels, maxLevels, minLevels, onLevelsChange]);

  // Separate effect to handle initialLevels changes after component mount
  useEffect(() => {
    if (initialLevels.length > 0 && JSON.stringify(levels) !== JSON.stringify(initialLevels)) {
      console.log('HierarchyLevelSelector: Updating levels due to initialLevels change');
      setLevels(initialLevels);
    }
  }, [initialLevels]);

  const validateLevels = (currentLevels: HierarchyLevel[]): Record<string, string> => {
    const newErrors: Record<string, string> = {};

    if (currentLevels.length < minLevels) {
      newErrors.general = `At least ${minLevels} hierarchy levels are required`;
    }

    if (currentLevels.length > maxLevels) {
      newErrors.general = `Maximum ${maxLevels} hierarchy levels allowed`;
    }

    // Check for duplicate columns
    const usedColumns = new Set<string>();
    currentLevels.forEach((level, index) => {
      if (!level.column) {
        newErrors[`level_${index}_column`] = 'Column selection is required';
      } else if (usedColumns.has(level.column)) {
        newErrors[`level_${index}_column`] = 'Column already used in another level';
      } else {
        usedColumns.add(level.column);
      }

      if (!level.name.trim()) {
        newErrors[`level_${index}_name`] = 'Level name is required';
      }

      if (!level.displayName.trim()) {
        newErrors[`level_${index}_display`] = 'Display name is required';
      }
    });

    return newErrors;
  };

  const updateLevels = (newLevels: HierarchyLevel[]) => {
    const sortedLevels = newLevels.sort((a, b) => a.order - b.order);
    setLevels(sortedLevels);
    
    const validationErrors = validateLevels(sortedLevels);
    setErrors(validationErrors);
    
    if (Object.keys(validationErrors).length === 0) {
      onLevelsChange(sortedLevels);
    }
  };

  const addLevel = () => {
    if (levels.length >= maxLevels) {
      toast({
        title: "Maximum levels reached",
        description: `You can only have up to ${maxLevels} hierarchy levels`,
        variant: "destructive"
      });
      return;
    }

    const newLevel: HierarchyLevel = {
      id: `level_${Date.now()}`,
      name: `Level ${levels.length + 1}`,
      column: '',
      displayName: `Level ${levels.length + 1}`,
      description: '',
      order: levels.length
    };

    updateLevels([...levels, newLevel]);
  };

  const removeLevel = (levelId: string) => {
    if (levels.length <= minLevels) {
      toast({
        title: "Minimum levels required",
        description: `You must have at least ${minLevels} hierarchy levels`,
        variant: "destructive"
      });
      return;
    }

    const newLevels = levels.filter(level => level.id !== levelId)
      .map((level, index) => ({ ...level, order: index }));
    updateLevels(newLevels);
  };

  // Function to analyze column data from training data
  const analyzeColumnData = (columnName: string) => {
    if (!trainingData || trainingData.length === 0) {
      return {
        unique_values: 0,
        total_values: 0,
        sample_values: []
      };
    }

    // Check if column exists in the data
    const firstRow = trainingData[0];
    if (!firstRow || !(columnName in firstRow)) {
      return {
        unique_values: 0,
        total_values: 0,
        sample_values: []
      };
    }

    const values = trainingData
      .map(row => row[columnName])
      .filter(val => val !== null && val !== undefined && val !== '');

    const uniqueValues = [...new Set(values)];
    const sampleValues = uniqueValues.slice(0, 5); // Get first 5 unique values as samples

    return {
      unique_values: uniqueValues.length,
      total_values: values.length,
      sample_values: sampleValues
    };
  };

  // Function to format column name for display
  const formatColumnName = (columnName: string): string => {
    return columnName
      .replace(/_/g, ' ')
      .replace(/\b\w/g, l => l.toUpperCase())
      .trim();
  };

  const updateLevel = (levelId: string, updates: Partial<HierarchyLevel>) => {
    let finalUpdates = { ...updates };

    // If column is being updated, also update name and displayName automatically
    if (updates.column) {
      const formattedName = formatColumnName(updates.column);
      finalUpdates = {
        ...finalUpdates,
        name: formattedName,
        displayName: formattedName
      };

      // Analyze the column data and update columnInfo
      if (trainingData && trainingData.length > 0) {
        const columnAnalysis = analyzeColumnData(updates.column);
        const updatedColumnInfo = {
          ...columnInfo,
          [updates.column]: {
            name: updates.column,
            type: 'string',
            ...columnAnalysis
          }
        };

        // Call the callback to update column info in parent component
        if (onColumnInfoUpdate) {
          onColumnInfoUpdate(updatedColumnInfo);
        }
      }
    }

    const newLevels = levels.map(level =>
      level.id === levelId ? { ...level, ...finalUpdates } : level
    );
    updateLevels(newLevels);
  };

  const moveLevel = (levelId: string, direction: 'up' | 'down') => {
    const currentIndex = levels.findIndex(level => level.id === levelId);
    if (currentIndex === -1) return;

    const newIndex = direction === 'up' ? currentIndex - 1 : currentIndex + 1;
    if (newIndex < 0 || newIndex >= levels.length) return;

    const newLevels = [...levels];
    [newLevels[currentIndex], newLevels[newIndex]] = [newLevels[newIndex], newLevels[currentIndex]];
    
    // Update order
    newLevels.forEach((level, index) => {
      level.order = index;
    });

    updateLevels(newLevels);
  };

  const getAvailableColumns = (currentLevelId: string) => {
    const usedColumns = levels
      .filter(level => level.id !== currentLevelId)
      .map(level => level.column);
    return availableColumns.filter(col => !usedColumns.includes(col));
  };

  const getColumnRecommendation = (column: string) => {
    const info = columnInfo[column];
    if (!info) return null;

    const uniqueValues = info.unique_values || 0;
    const totalValues = info.total_values || 1;
    const uniqueRatio = uniqueValues / totalValues;

    if (uniqueRatio < 0.1) return 'high-level';
    if (uniqueRatio < 0.3) return 'mid-level';
    if (uniqueRatio < 0.7) return 'low-level';
    return 'leaf-level';
  };

  const isValid = Object.keys(errors).length === 0 && levels.length >= minLevels;

  // Show loading/error state if no columns available
  if (!availableColumns || availableColumns.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TreePine className="w-5 h-5" />
            Hierarchy Level Configuration
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              No columns available for hierarchy configuration. Please ensure your data has been uploaded correctly and try refreshing the page.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <TreePine className="w-5 h-5" />
          Hierarchy Level Configuration
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* General Error */}
        {errors.general && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{errors.general}</AlertDescription>
          </Alert>
        )}

        {/* Info Alert */}
        <Alert>
          <Info className="h-4 w-4" />
          <AlertDescription>
            Configure your hierarchy levels from most general (top) to most specific (bottom). 
            Each level should represent a different granularity of classification.
          </AlertDescription>
        </Alert>

        {/* Levels List */}
        <div className="space-y-4">
          {levels.map((level, index) => (
            <Card key={level.id} className="p-4">
              <div className="space-y-4">
                {/* Level Header */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Badge variant="outline">
                      Level {index + 1}
                    </Badge>
                    <span className="text-sm text-muted-foreground">
                      {index === 0 ? 'Most General' : 
                       index === levels.length - 1 ? 'Most Specific' : 
                       'Intermediate'}
                    </span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => moveLevel(level.id, 'up')}
                      disabled={index === 0}
                    >
                      <ArrowUp className="w-4 h-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => moveLevel(level.id, 'down')}
                      disabled={index === levels.length - 1}
                    >
                      <ArrowDown className="w-4 h-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeLevel(level.id)}
                      disabled={levels.length <= minLevels}
                    >
                      <Minus className="w-4 h-4" />
                    </Button>
                  </div>
                </div>

                {/* Level Configuration */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor={`level-name-${level.id}`}>Level Name</Label>
                    <Input
                      id={`level-name-${level.id}`}
                      value={level.name}
                      onChange={(e) => updateLevel(level.id, { name: e.target.value })}
                      placeholder="e.g., Category, Subcategory"
                    />
                    {errors[`level_${index}_name`] && (
                      <p className="text-sm text-destructive">{errors[`level_${index}_name`]}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor={`level-column-${level.id}`}>Data Column</Label>
                    <Select
                      value={level.column}
                      onValueChange={(value) => updateLevel(level.id, { column: value })}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select column" />
                      </SelectTrigger>
                      <SelectContent>
                        {getAvailableColumns(level.id).map(column => {
                          const recommendation = getColumnRecommendation(column);
                          return (
                            <SelectItem key={column} value={column}>
                              <div className="flex items-center justify-between w-full">
                                <span>{column}</span>
                                {recommendation && (
                                  <Badge variant="secondary" className="ml-2 text-xs">
                                    {recommendation}
                                  </Badge>
                                )}
                              </div>
                            </SelectItem>
                          );
                        })}
                      </SelectContent>
                    </Select>
                    {errors[`level_${index}_column`] && (
                      <p className="text-sm text-destructive">{errors[`level_${index}_column`]}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor={`level-display-${level.id}`}>Display Name</Label>
                    <Input
                      id={`level-display-${level.id}`}
                      value={level.displayName}
                      onChange={(e) => updateLevel(level.id, { displayName: e.target.value })}
                      placeholder="Display name for UI"
                    />
                    {errors[`level_${index}_display`] && (
                      <p className="text-sm text-destructive">{errors[`level_${index}_display`]}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor={`level-description-${level.id}`}>Description (Optional)</Label>
                    <Input
                      id={`level-description-${level.id}`}
                      value={level.description || ''}
                      onChange={(e) => updateLevel(level.id, { description: e.target.value })}
                      placeholder="Brief description of this level"
                    />
                  </div>
                </div>

                {/* Column Info */}
                {level.column && columnInfo[level.column] && (
                  <div className="text-sm text-muted-foreground bg-muted/30 p-3 rounded">
                    <strong>{level.column}:</strong> {columnInfo[level.column].unique_values} unique values 
                    out of {columnInfo[level.column].total_values} total
                    {columnInfo[level.column].sample_values && (
                      <div className="mt-1">
                        <strong>Sample values:</strong> {columnInfo[level.column].sample_values.slice(0, 3).join(', ')}
                        {columnInfo[level.column].sample_values.length > 3 && '...'}
                      </div>
                    )}
                  </div>
                )}
              </div>
            </Card>
          ))}
        </div>

        {/* Add Level Button */}
        <Button
          variant="outline"
          onClick={addLevel}
          disabled={levels.length >= maxLevels}
          className="w-full"
        >
          <Plus className="w-4 h-4 mr-2" />
          Add Hierarchy Level
        </Button>

        {/* Validation Status */}
        <div className="flex items-center gap-2 p-3 rounded bg-muted/30">
          {isValid ? (
            <>
              <CheckCircle2 className="w-4 h-4 text-green-600" />
              <span className="text-sm text-green-600">Hierarchy configuration is valid</span>
            </>
          ) : (
            <>
              <AlertCircle className="w-4 h-4 text-orange-600" />
              <span className="text-sm text-orange-600">
                Please fix the errors above to continue
              </span>
            </>
          )}
        </div>
      </CardContent>
    </Card>
  );
};
