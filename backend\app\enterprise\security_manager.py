"""Enterprise Security Manager for ClassyWeb Universal Platform."""

import logging
import hashlib
import secrets
import base64
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timezone, timedelta
from enum import Enum
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import jwt

from ..database import SessionLocal
from ..models.tenant import AuditLog
from ..config import config

logger = logging.getLogger(__name__)


class SecurityLevel(Enum):
    """Security levels for different operations."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class EnterpriseSecurityManager:
    """Manages enterprise-grade security features."""
    
    def __init__(self):
        self.encryption_keys: Dict[str, bytes] = {}
        self.rate_limiters: Dict[str, Dict[str, Any]] = {}
        self.security_policies: Dict[str, Dict[str, Any]] = {}
        
        # Initialize default security policies
        self._initialize_default_policies()
        
        logger.info("Enterprise Security Manager initialized")
    
    def _initialize_default_policies(self):
        """Initialize default security policies."""
        self.security_policies = {
            "password_policy": {
                "min_length": 12,
                "require_uppercase": True,
                "require_lowercase": True,
                "require_numbers": True,
                "require_special_chars": True,
                "max_age_days": 90,
                "history_count": 5
            },
            "session_policy": {
                "max_session_duration_hours": 8,
                "idle_timeout_minutes": 30,
                "concurrent_sessions_limit": 3,
                "require_mfa": False
            },
            "api_policy": {
                "rate_limit_per_minute": 100,
                "rate_limit_per_hour": 1000,
                "require_api_key": True,
                "log_all_requests": True
            },
            "data_policy": {
                "encrypt_at_rest": True,
                "encrypt_in_transit": True,
                "data_retention_days": 365,
                "require_data_classification": True
            }
        }
    
    async def encrypt_sensitive_data(
        self,
        data: str,
        tenant_id: str,
        data_classification: str = "confidential"
    ) -> str:
        """Encrypt sensitive data with tenant-specific keys."""
        try:
            # Get or create tenant encryption key
            encryption_key = await self._get_tenant_encryption_key(tenant_id)
            
            # Create Fernet cipher
            cipher = Fernet(encryption_key)
            
            # Encrypt data
            encrypted_data = cipher.encrypt(data.encode('utf-8'))
            
            # Encode as base64 for storage
            encoded_data = base64.b64encode(encrypted_data).decode('utf-8')
            
            # Log encryption event
            await self._log_security_event(
                tenant_id=tenant_id,
                action="data_encryption",
                details={
                    "data_classification": data_classification,
                    "data_size": len(data),
                    "encryption_algorithm": "Fernet"
                }
            )
            
            return encoded_data
            
        except Exception as e:
            logger.error(f"Error encrypting data for tenant {tenant_id}: {str(e)}")
            raise
    
    async def decrypt_sensitive_data(
        self,
        encrypted_data: str,
        tenant_id: str
    ) -> str:
        """Decrypt sensitive data with tenant-specific keys."""
        try:
            # Get tenant encryption key
            encryption_key = await self._get_tenant_encryption_key(tenant_id)
            
            # Create Fernet cipher
            cipher = Fernet(encryption_key)
            
            # Decode from base64
            encrypted_bytes = base64.b64decode(encrypted_data.encode('utf-8'))
            
            # Decrypt data
            decrypted_data = cipher.decrypt(encrypted_bytes)
            
            # Log decryption event
            await self._log_security_event(
                tenant_id=tenant_id,
                action="data_decryption",
                details={
                    "data_size": len(decrypted_data)
                }
            )
            
            return decrypted_data.decode('utf-8')
            
        except Exception as e:
            logger.error(f"Error decrypting data for tenant {tenant_id}: {str(e)}")
            raise
    
    async def validate_api_access(
        self,
        tenant_id: str,
        user_id: int,
        endpoint: str,
        ip_address: str,
        user_agent: str
    ) -> Tuple[bool, Optional[str]]:
        """Validate API access with enterprise security checks."""
        try:
            # Check rate limiting
            if not await self._check_rate_limit(tenant_id, user_id, ip_address):
                return False, "Rate limit exceeded"
            
            # Check IP whitelist (if configured)
            if not await self._check_ip_whitelist(tenant_id, ip_address):
                return False, "IP address not whitelisted"
            
            # Check endpoint permissions
            if not await self._check_endpoint_permissions(tenant_id, user_id, endpoint):
                return False, "Insufficient permissions for endpoint"
            
            # Check security policies
            if not await self._check_security_policies(tenant_id, user_id):
                return False, "Security policy violation"
            
            # Log successful access
            await self._log_security_event(
                tenant_id=tenant_id,
                user_id=user_id,
                action="api_access",
                details={
                    "endpoint": endpoint,
                    "ip_address": ip_address,
                    "user_agent": user_agent
                },
                status="success"
            )
            
            return True, None
            
        except Exception as e:
            logger.error(f"Error validating API access: {str(e)}")
            return False, "Internal security error"
    
    async def generate_api_key(
        self,
        tenant_id: str,
        user_id: int,
        permissions: List[str],
        expires_in_days: int = 365
    ) -> Dict[str, Any]:
        """Generate a new API key for a tenant user."""
        try:
            # Generate secure API key
            api_key = secrets.token_urlsafe(32)
            
            # Create key metadata
            key_metadata = {
                "tenant_id": tenant_id,
                "user_id": user_id,
                "permissions": permissions,
                "created_at": datetime.now(timezone.utc),
                "expires_at": datetime.now(timezone.utc) + timedelta(days=expires_in_days),
                "is_active": True
            }
            
            # Store API key (hashed)
            key_hash = hashlib.sha256(api_key.encode()).hexdigest()
            await self._store_api_key(key_hash, key_metadata)
            
            # Log API key generation
            await self._log_security_event(
                tenant_id=tenant_id,
                user_id=user_id,
                action="api_key_generated",
                details={
                    "permissions": permissions,
                    "expires_in_days": expires_in_days
                }
            )
            
            return {
                "api_key": api_key,
                "key_id": key_hash[:16],  # First 16 chars for identification
                "permissions": permissions,
                "expires_at": key_metadata["expires_at"].isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error generating API key: {str(e)}")
            raise
    
    async def validate_api_key(self, api_key: str) -> Optional[Dict[str, Any]]:
        """Validate an API key and return metadata."""
        try:
            key_hash = hashlib.sha256(api_key.encode()).hexdigest()
            key_metadata = await self._get_api_key_metadata(key_hash)
            
            if not key_metadata:
                return None
            
            # Check if key is active
            if not key_metadata.get("is_active", False):
                return None
            
            # Check expiration
            expires_at = key_metadata.get("expires_at")
            if expires_at and datetime.now(timezone.utc) > expires_at:
                return None
            
            return key_metadata
            
        except Exception as e:
            logger.error(f"Error validating API key: {str(e)}")
            return None
    
    async def audit_user_action(
        self,
        tenant_id: str,
        user_id: int,
        action: str,
        resource_type: str,
        resource_id: str,
        details: Dict[str, Any],
        ip_address: str = None,
        user_agent: str = None,
        request_id: str = None
    ) -> bool:
        """Audit a user action for compliance."""
        try:
            with SessionLocal() as db:
                audit_log = AuditLog(
                    tenant_id=tenant_id,
                    user_id=user_id,
                    action=action,
                    resource_type=resource_type,
                    resource_id=resource_id,
                    ip_address=ip_address,
                    user_agent=user_agent,
                    request_id=request_id,
                    status="success",
                    details=details,
                    timestamp=datetime.now(timezone.utc)
                )
                
                db.add(audit_log)
                db.commit()
                
                logger.info(f"Audited action {action} for user {user_id} in tenant {tenant_id}")
                return True
                
        except Exception as e:
            logger.error(f"Error auditing user action: {str(e)}")
            return False
    
    async def get_security_report(
        self,
        tenant_id: str,
        start_date: datetime,
        end_date: datetime
    ) -> Dict[str, Any]:
        """Generate security report for a tenant."""
        try:
            with SessionLocal() as db:
                # Get audit logs for the period
                audit_logs = db.query(AuditLog).filter(
                    AuditLog.tenant_id == tenant_id,
                    AuditLog.timestamp >= start_date,
                    AuditLog.timestamp <= end_date
                ).all()
                
                # Analyze security events
                total_events = len(audit_logs)
                failed_events = sum(1 for log in audit_logs if log.status == "failure")
                unique_users = len(set(log.user_id for log in audit_logs if log.user_id))
                unique_ips = len(set(log.ip_address for log in audit_logs if log.ip_address))
                
                # Action breakdown
                action_counts = {}
                for log in audit_logs:
                    action_counts[log.action] = action_counts.get(log.action, 0) + 1
                
                # Risk assessment
                risk_score = self._calculate_risk_score(audit_logs)
                
                return {
                    "tenant_id": tenant_id,
                    "report_period": {
                        "start_date": start_date.isoformat(),
                        "end_date": end_date.isoformat()
                    },
                    "summary": {
                        "total_events": total_events,
                        "failed_events": failed_events,
                        "success_rate": ((total_events - failed_events) / total_events * 100) if total_events > 0 else 100,
                        "unique_users": unique_users,
                        "unique_ips": unique_ips
                    },
                    "action_breakdown": action_counts,
                    "risk_assessment": {
                        "risk_score": risk_score,
                        "risk_level": self._get_risk_level(risk_score),
                        "recommendations": self._get_security_recommendations(risk_score, audit_logs)
                    },
                    "generated_at": datetime.now(timezone.utc).isoformat()
                }
                
        except Exception as e:
            logger.error(f"Error generating security report: {str(e)}")
            return {}
    
    async def _get_tenant_encryption_key(self, tenant_id: str) -> bytes:
        """Get or create encryption key for a tenant."""
        try:
            if tenant_id in self.encryption_keys:
                return self.encryption_keys[tenant_id]
            
            # Generate new key for tenant
            password = f"{config.JWT_SECRET_KEY}_{tenant_id}".encode()
            salt = tenant_id.encode()
            
            kdf = PBKDF2HMAC(
                algorithm=hashes.SHA256(),
                length=32,
                salt=salt,
                iterations=100000,
            )
            
            key = base64.urlsafe_b64encode(kdf.derive(password))
            self.encryption_keys[tenant_id] = key
            
            return key
            
        except Exception as e:
            logger.error(f"Error getting encryption key for tenant {tenant_id}: {str(e)}")
            raise
    
    async def _check_rate_limit(self, tenant_id: str, user_id: int, ip_address: str) -> bool:
        """Check rate limiting for API access."""
        try:
            current_time = datetime.now(timezone.utc)
            
            # Create rate limiter key
            limiter_key = f"{tenant_id}_{user_id}_{ip_address}"
            
            if limiter_key not in self.rate_limiters:
                self.rate_limiters[limiter_key] = {
                    "requests": [],
                    "last_reset": current_time
                }
            
            limiter = self.rate_limiters[limiter_key]
            
            # Clean old requests (older than 1 hour)
            cutoff_time = current_time - timedelta(hours=1)
            limiter["requests"] = [
                req_time for req_time in limiter["requests"]
                if req_time > cutoff_time
            ]
            
            # Check limits
            api_policy = self.security_policies.get("api_policy", {})
            hourly_limit = api_policy.get("rate_limit_per_hour", 1000)
            
            if len(limiter["requests"]) >= hourly_limit:
                logger.warning(f"Rate limit exceeded for {limiter_key}")
                return False
            
            # Add current request
            limiter["requests"].append(current_time)
            
            return True
            
        except Exception as e:
            logger.error(f"Error checking rate limit: {str(e)}")
            return True  # Allow on error to avoid blocking legitimate requests
    
    async def _check_ip_whitelist(self, tenant_id: str, ip_address: str) -> bool:
        """Check if IP address is whitelisted for tenant."""
        try:
            # TODO: Implement IP whitelist checking
            # For now, allow all IPs
            return True
            
        except Exception as e:
            logger.error(f"Error checking IP whitelist: {str(e)}")
            return True
    
    async def _check_endpoint_permissions(self, tenant_id: str, user_id: int, endpoint: str) -> bool:
        """Check if user has permission to access endpoint."""
        try:
            # TODO: Implement role-based access control
            # For now, allow all endpoints
            return True
            
        except Exception as e:
            logger.error(f"Error checking endpoint permissions: {str(e)}")
            return True
    
    async def _check_security_policies(self, tenant_id: str, user_id: int) -> bool:
        """Check if user complies with security policies."""
        try:
            # TODO: Implement security policy checking
            # This would check things like:
            # - Password age
            # - MFA requirements
            # - Session limits
            # - Account status
            return True
            
        except Exception as e:
            logger.error(f"Error checking security policies: {str(e)}")
            return True
    
    async def _log_security_event(
        self,
        tenant_id: str,
        action: str,
        details: Dict[str, Any],
        user_id: Optional[int] = None,
        status: str = "success"
    ):
        """Log a security event."""
        try:
            with SessionLocal() as db:
                audit_log = AuditLog(
                    tenant_id=tenant_id,
                    user_id=user_id,
                    action=action,
                    resource_type="security",
                    status=status,
                    details=details,
                    timestamp=datetime.now(timezone.utc)
                )
                
                db.add(audit_log)
                db.commit()
                
        except Exception as e:
            logger.error(f"Error logging security event: {str(e)}")
    
    async def _store_api_key(self, key_hash: str, metadata: Dict[str, Any]):
        """Store API key metadata."""
        try:
            # TODO: Implement API key storage in database
            # For now, store in memory (not persistent)
            pass
            
        except Exception as e:
            logger.error(f"Error storing API key: {str(e)}")
    
    async def _get_api_key_metadata(self, key_hash: str) -> Optional[Dict[str, Any]]:
        """Get API key metadata."""
        try:
            # TODO: Implement API key retrieval from database
            # For now, return None
            return None
            
        except Exception as e:
            logger.error(f"Error getting API key metadata: {str(e)}")
            return None
    
    def _calculate_risk_score(self, audit_logs: List[AuditLog]) -> float:
        """Calculate risk score based on audit logs."""
        try:
            if not audit_logs:
                return 0.0
            
            risk_score = 0.0
            total_events = len(audit_logs)
            
            # Failed events increase risk
            failed_events = sum(1 for log in audit_logs if log.status == "failure")
            risk_score += (failed_events / total_events) * 30
            
            # Multiple IPs from same user increase risk
            user_ips = {}
            for log in audit_logs:
                if log.user_id and log.ip_address:
                    if log.user_id not in user_ips:
                        user_ips[log.user_id] = set()
                    user_ips[log.user_id].add(log.ip_address)
            
            multi_ip_users = sum(1 for ips in user_ips.values() if len(ips) > 3)
            if user_ips:
                risk_score += (multi_ip_users / len(user_ips)) * 20
            
            # High-risk actions
            high_risk_actions = ["data_export", "model_download", "admin_action"]
            high_risk_count = sum(1 for log in audit_logs if log.action in high_risk_actions)
            risk_score += (high_risk_count / total_events) * 25
            
            # Off-hours activity
            off_hours_count = sum(
                1 for log in audit_logs
                if log.timestamp.hour < 6 or log.timestamp.hour > 22
            )
            risk_score += (off_hours_count / total_events) * 15
            
            return min(risk_score, 100.0)  # Cap at 100
            
        except Exception as e:
            logger.error(f"Error calculating risk score: {str(e)}")
            return 0.0
    
    def _get_risk_level(self, risk_score: float) -> str:
        """Get risk level based on score."""
        if risk_score < 20:
            return "low"
        elif risk_score < 50:
            return "medium"
        elif risk_score < 80:
            return "high"
        else:
            return "critical"
    
    def _get_security_recommendations(
        self,
        risk_score: float,
        audit_logs: List[AuditLog]
    ) -> List[str]:
        """Get security recommendations based on risk assessment."""
        recommendations = []
        
        if risk_score > 50:
            recommendations.append("Enable multi-factor authentication for all users")
            recommendations.append("Review and update IP whitelist settings")
        
        if risk_score > 30:
            recommendations.append("Implement stricter rate limiting")
            recommendations.append("Enable real-time security monitoring")
        
        # Check for specific patterns
        failed_logins = sum(1 for log in audit_logs if log.action == "login" and log.status == "failure")
        if failed_logins > 10:
            recommendations.append("Investigate potential brute force attacks")
        
        return recommendations


# Global enterprise security manager instance
enterprise_security_manager = EnterpriseSecurityManager()
