"""Plugin API endpoints for ClassyWeb Universal Platform."""

import logging
from typing import Dict, List, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Body, Query, UploadFile, File
from sqlalchemy.orm import Session
from pydantic import BaseModel

from ..database import get_db
from ..auth import get_current_user
from ..models.auth import User
from ..plugins.plugin_manager import plugin_manager
from ..plugins.base_plugin import PluginType
from ..universal_platform import universal_platform, ClassificationRequest

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/plugins", tags=["plugins"])


# --- Request/Response Models ---

class PluginInstallRequest(BaseModel):
    plugin_name: str
    config: Optional[Dict[str, Any]] = None
    auto_initialize: bool = True


class ClassificationRequest(BaseModel):
    texts: List[str]
    hierarchy_config: Optional[Dict[str, Any]] = None
    classification_type: str = "auto"  # auto, hierarchical, flat
    preferred_engine: Optional[str] = None
    confidence_threshold: float = 0.5
    use_ensemble: bool = False
    max_processing_time_ms: int = 30000


class PluginRecommendationRequest(BaseModel):
    task_type: str  # hierarchical, flat, multi_modal
    data_characteristics: Dict[str, Any]


# --- Plugin Management Endpoints ---

@router.get("/available")
async def get_available_plugins(
    plugin_type: Optional[str] = Query(None),
    enterprise_only: bool = Query(False),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get list of available plugins."""
    try:
        # Convert plugin type string to enum
        plugin_type_enum = None
        if plugin_type:
            try:
                plugin_type_enum = PluginType(plugin_type)
            except ValueError:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Invalid plugin type: {plugin_type}"
                )
        
        # Get available plugins
        plugins = await plugin_manager.get_available_plugins(
            plugin_type=plugin_type_enum,
            enterprise_only=enterprise_only
        )
        
        return {
            "plugins": plugins,
            "total_count": len(plugins),
            "filtered_by": {
                "plugin_type": plugin_type,
                "enterprise_only": enterprise_only
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting available plugins: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get available plugins"
        )


@router.post("/install")
async def install_plugin(
    request: PluginInstallRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Install a plugin from the marketplace."""
    try:
        # TODO: Add permission checks
        
        # Install plugin
        success = await plugin_manager.install_plugin_from_marketplace(
            plugin_id=request.plugin_name,
            marketplace_url="",  # TODO: Add marketplace URL
            config=request.config
        )
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Failed to install plugin: {request.plugin_name}"
            )
        
        return {
            "message": f"Plugin '{request.plugin_name}' installed successfully",
            "plugin_name": request.plugin_name
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error installing plugin: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to install plugin"
        )


@router.delete("/uninstall/{plugin_name}")
async def uninstall_plugin(
    plugin_name: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Uninstall a plugin."""
    try:
        # TODO: Add permission checks
        
        success = await plugin_manager.uninstall_plugin(plugin_name)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Failed to uninstall plugin: {plugin_name}"
            )
        
        return {
            "message": f"Plugin '{plugin_name}' uninstalled successfully"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error uninstalling plugin: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to uninstall plugin"
        )


@router.post("/upload")
async def upload_plugin_package(
    file: UploadFile = File(...),
    config: Optional[str] = Body(None),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Upload and install a plugin package."""
    try:
        # Security validations
        if not file.filename:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Filename is required"
            )

        # Validate filename for path traversal
        if '..' in file.filename or '/' in file.filename or '\\' in file.filename:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid filename"
            )

        # Validate file type
        if not file.filename.lower().endswith('.zip'):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Plugin package must be a ZIP file"
            )

        # Check file size (max 50MB for plugins)
        MAX_PLUGIN_SIZE = 50 * 1024 * 1024  # 50MB
        content = await file.read()
        if len(content) > MAX_PLUGIN_SIZE:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Plugin package too large. Maximum size: {MAX_PLUGIN_SIZE // (1024*1024)}MB"
            )
        
        # Save uploaded file temporarily
        import tempfile
        import os
        from pathlib import Path
        
        with tempfile.NamedTemporaryFile(delete=False, suffix='.zip') as temp_file:
            content = await file.read()
            temp_file.write(content)
            temp_file_path = Path(temp_file.name)
        
        try:
            # Parse config if provided
            plugin_config = None
            if config:
                import json
                plugin_config = json.loads(config)
            
            # Install plugin package
            success = await plugin_manager.install_plugin_package(
                package_path=temp_file_path,
                target_directory=None  # Use default
            )
            
            if not success:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Failed to install plugin package"
                )
            
            return {
                "message": f"Plugin package '{file.filename}' installed successfully",
                "filename": file.filename
            }
            
        finally:
            # Clean up temporary file
            os.unlink(temp_file_path)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error uploading plugin package: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to upload plugin package"
        )


# --- Universal Classification Endpoints ---

@router.get("/engines")
async def get_available_engines(
    classification_type: Optional[str] = Query(None),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get available classification engines."""
    try:
        engines = await universal_platform.get_available_engines(
            user_id=current_user.id,
            classification_type=classification_type
        )
        
        return {
            "engines": engines,
            "total_count": len(engines)
        }
        
    except Exception as e:
        logger.error(f"Error getting available engines: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get available engines"
        )


@router.post("/classify")
async def universal_classify(
    request: ClassificationRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Perform classification using the universal platform."""
    try:
        # Create platform request
        platform_request = ClassificationRequest(
            texts=request.texts,
            user_id=current_user.id,
            hierarchy_config=request.hierarchy_config,
            classification_type=request.classification_type,
            preferred_engine=request.preferred_engine,
            confidence_threshold=request.confidence_threshold,
            use_ensemble=request.use_ensemble,
            max_processing_time_ms=request.max_processing_time_ms
        )
        
        # Execute classification
        response = await universal_platform.classify(platform_request)
        
        return {
            "request_id": response.request_id,
            "results": response.results,
            "engine_used": response.engine_used,
            "processing_time_ms": response.processing_time_ms,
            "confidence_scores": response.confidence_scores,
            "recommendations": response.recommendations,
            "metadata": response.metadata
        }
        
    except Exception as e:
        logger.error(f"Error performing universal classification: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to perform classification"
        )


@router.post("/recommendations")
async def get_plugin_recommendations(
    request: PluginRecommendationRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get plugin recommendations based on task and data characteristics."""
    try:
        recommendations = await plugin_manager.get_plugin_recommendations(
            task_type=request.task_type,
            data_characteristics=request.data_characteristics
        )
        
        return {
            "recommendations": recommendations,
            "total_count": len(recommendations),
            "task_type": request.task_type
        }
        
    except Exception as e:
        logger.error(f"Error getting plugin recommendations: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get plugin recommendations"
        )
