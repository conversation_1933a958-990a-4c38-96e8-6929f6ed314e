import { <PERSON>, CardContent, CardD<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import {
  BarChart3,
  Download,
  Share,
  CheckCircle2,
  AlertTriangle,
  Info,
  FileText,
  Code,
  Cloud
} from "lucide-react";
import { TrainingMethod } from "./MethodSelection";

export interface ClassificationResults {
  accuracy: number;
  precision: number;
  recall: number;
  f1Score: number;
  auc?: number;
  confusionMatrix?: number[][];
  classNames?: string[];
  featureImportance?: { name: string; importance: number }[];
  modelSize?: string;
  inferenceSpeed?: string;
  trainingTime?: string;
  crossValidationScore?: number;
  classificationReport?: {
    [className: string]: {
      precision: number;
      recall: number;
      f1Score: number;
      support: number;
    };
  };
}

interface ResultsDisplayProps {
  method: TrainingMethod;
  classificationType: string;
  results: ClassificationResults;
  onExport: (format: string) => void;
  onDeploy: () => void;
  onShare: () => void;
}

export const ResultsDisplay = ({
  method,
  classificationType,
  results,
  onExport,
  onDeploy,
  onShare
}: ResultsDisplayProps) => {
  const getPerformanceLevel = (score: number) => {
    if (score >= 0.9) return { level: 'Excellent', color: 'text-ml-success', bg: 'bg-ml-success/10' };
    if (score >= 0.8) return { level: 'Good', color: 'text-ml-primary', bg: 'bg-ml-primary/10' };
    if (score >= 0.7) return { level: 'Fair', color: 'text-ml-warning', bg: 'bg-ml-warning/10' };
    return { level: 'Poor', color: 'text-ml-error', bg: 'bg-ml-error/10' };
  };

  const overallPerformance = getPerformanceLevel(results.accuracy / 100);

  const renderConfusionMatrix = () => {
    if (!results.confusionMatrix || !results.classNames) return null;

    return (
      <div className="space-y-4">
        <h4 className="font-semibold">Confusion Matrix</h4>
        <div className="overflow-x-auto">
          <table className="w-full border-collapse">
            <thead>
              <tr>
                <th className="p-2 text-left">Actual \ Predicted</th>
                {results.classNames.map((name, i) => (
                  <th key={i} className="p-2 text-center text-sm">{name}</th>
                ))}
              </tr>
            </thead>
            <tbody>
              {results.confusionMatrix.map((row, i) => (
                <tr key={i}>
                  <td className="p-2 font-medium text-sm">{results.classNames![i]}</td>
                  {row.map((value, j) => (
                    <td key={j} className={`p-2 text-center text-sm ${
                      i === j ? 'bg-ml-success/20 font-bold' : 'bg-ml-error/10'
                    }`}>
                      {value.toLocaleString()}
                    </td>
                  ))}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    );
  };

  const renderClassificationReport = () => {
    if (!results.classificationReport) return null;

    return (
      <div className="space-y-4">
        <h4 className="font-semibold">Per-Class Performance</h4>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b">
                <th className="text-left p-2">Class</th>
                <th className="text-center p-2">Precision</th>
                <th className="text-center p-2">Recall</th>
                <th className="text-center p-2">F1-Score</th>
                <th className="text-center p-2">Support</th>
              </tr>
            </thead>
            <tbody>
              {Object.entries(results.classificationReport).map(([className, metrics]) => (
                <tr key={className} className="border-b">
                  <td className="p-2 font-medium">{className}</td>
                  <td className="p-2 text-center">{(metrics.precision * 100).toFixed(1)}%</td>
                  <td className="p-2 text-center">{(metrics.recall * 100).toFixed(1)}%</td>
                  <td className="p-2 text-center">{(metrics.f1Score * 100).toFixed(1)}%</td>
                  <td className="p-2 text-center">{metrics.support.toLocaleString()}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    );
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 rounded-lg bg-ml-success/10 flex items-center justify-center">
                <BarChart3 className="w-5 h-5 text-ml-success" />
              </div>
              <div>
                <CardTitle>Model Results</CardTitle>
                <CardDescription>
                  Performance metrics for your {classificationType} classification model
                </CardDescription>
              </div>
            </div>
            <Badge className={`${overallPerformance.bg} ${overallPerformance.color}`}>
              {overallPerformance.level} Performance
            </Badge>
          </div>
        </CardHeader>
      </Card>

      {/* Key Metrics */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-ml-success">{results.accuracy.toFixed(1)}%</div>
            <div className="text-sm text-muted-foreground">Accuracy</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-ml-primary">{results.precision.toFixed(1)}%</div>
            <div className="text-sm text-muted-foreground">Precision</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-ml-secondary">{results.recall.toFixed(1)}%</div>
            <div className="text-sm text-muted-foreground">Recall</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-ml-accent">{results.f1Score.toFixed(1)}%</div>
            <div className="text-sm text-muted-foreground">F1-Score</div>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Results */}
      <Card>
        <CardContent className="p-6">
          <Tabs defaultValue="overview" className="space-y-6">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="matrix">Confusion Matrix</TabsTrigger>
              <TabsTrigger value="features">Features</TabsTrigger>
              <TabsTrigger value="details">Details</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <h4 className="font-semibold">Performance Summary</h4>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-sm">Overall Accuracy:</span>
                      <span className="font-medium">{results.accuracy.toFixed(2)}%</span>
                    </div>
                    {results.auc && (
                      <div className="flex justify-between">
                        <span className="text-sm">AUC Score:</span>
                        <span className="font-medium">{results.auc.toFixed(3)}</span>
                      </div>
                    )}
                    {results.crossValidationScore && (
                      <div className="flex justify-between">
                        <span className="text-sm">Cross-Validation:</span>
                        <span className="font-medium">{results.crossValidationScore.toFixed(2)}%</span>
                      </div>
                    )}
                  </div>
                </div>

                <div className="space-y-4">
                  <h4 className="font-semibold">Model Information</h4>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-sm">Method:</span>
                      <span className="font-medium">{method === 'custom' ? 'Custom Training' : 'LLM Inference'}</span>
                    </div>
                    {results.modelSize && (
                      <div className="flex justify-between">
                        <span className="text-sm">Model Size:</span>
                        <span className="font-medium">{results.modelSize}</span>
                      </div>
                    )}
                    {results.inferenceSpeed && (
                      <div className="flex justify-between">
                        <span className="text-sm">Inference Speed:</span>
                        <span className="font-medium">{results.inferenceSpeed}</span>
                      </div>
                    )}
                    {results.trainingTime && (
                      <div className="flex justify-between">
                        <span className="text-sm">Training Time:</span>
                        <span className="font-medium">{results.trainingTime}</span>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Performance Insights */}
              <div className="space-y-4">
                <h4 className="font-semibold">Performance Insights</h4>
                <div className="space-y-3">
                  {results.accuracy >= 90 && (
                    <div className="flex items-center gap-2 p-3 bg-ml-success/10 rounded-lg">
                      <CheckCircle2 className="w-4 h-4 text-ml-success" />
                      <span className="text-sm">Excellent accuracy - model is ready for production</span>
                    </div>
                  )}
                  {results.precision < 80 && (
                    <div className="flex items-center gap-2 p-3 bg-ml-warning/10 rounded-lg">
                      <AlertTriangle className="w-4 h-4 text-ml-warning" />
                      <span className="text-sm">Low precision - consider adjusting classification threshold</span>
                    </div>
                  )}
                  {results.recall < 80 && (
                    <div className="flex items-center gap-2 p-3 bg-ml-warning/10 rounded-lg">
                      <AlertTriangle className="w-4 h-4 text-ml-warning" />
                      <span className="text-sm">Low recall - model may be missing positive cases</span>
                    </div>
                  )}
                  <div className="flex items-center gap-2 p-3 bg-ml-primary/10 rounded-lg">
                    <Info className="w-4 h-4 text-ml-primary" />
                    <span className="text-sm">F1-Score balances precision and recall: {results.f1Score.toFixed(1)}%</span>
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="matrix" className="space-y-6">
              {renderConfusionMatrix()}
              {renderClassificationReport()}
            </TabsContent>

            <TabsContent value="features" className="space-y-6">
              {results.featureImportance && (
                <div className="space-y-4">
                  <h4 className="font-semibold">Feature Importance</h4>
                  <div className="space-y-3">
                    {results.featureImportance.slice(0, 10).map((feature, index) => (
                      <div key={index} className="flex items-center gap-3">
                        <div className="w-32 text-sm truncate">{feature.name}</div>
                        <div className="flex-1 bg-muted rounded-full h-2">
                          <div 
                            className="bg-ml-primary h-2 rounded-full" 
                            style={{ width: `${feature.importance * 100}%` }}
                          ></div>
                        </div>
                        <div className="w-12 text-sm text-right">{(feature.importance * 100).toFixed(0)}%</div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </TabsContent>

            <TabsContent value="details" className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <h4 className="font-semibold">Technical Details</h4>
                  <div className="p-4 bg-muted/50 rounded-lg text-sm space-y-2">
                    <div>Classification Type: {classificationType}</div>
                    <div>Training Method: {method === 'custom' ? 'Custom ML Model' : 'Large Language Model'}</div>
                    {method === 'custom' && (
                      <>
                        <div>Algorithm: Random Forest</div>
                        <div>Hyperparameters: Optimized</div>
                        <div>Cross-Validation: 5-fold</div>
                      </>
                    )}
                    {method === 'llm' && (
                      <>
                        <div>Model: GPT-4 Turbo</div>
                        <div>Temperature: 0.1</div>
                        <div>Few-shot Examples: 5</div>
                      </>
                    )}
                  </div>
                </div>

                <div className="space-y-4">
                  <h4 className="font-semibold">Export Options</h4>
                  <div className="space-y-2">
                    <Button variant="outline" size="sm" className="w-full justify-start" onClick={() => onExport('json')}>
                      <FileText className="w-4 h-4 mr-2" />
                      Export Results (JSON)
                    </Button>
                    <Button variant="outline" size="sm" className="w-full justify-start" onClick={() => onExport('csv')}>
                      <FileText className="w-4 h-4 mr-2" />
                      Export Predictions (CSV)
                    </Button>
                    {method === 'custom' && (
                      <Button variant="outline" size="sm" className="w-full justify-start" onClick={() => onExport('model')}>
                        <Code className="w-4 h-4 mr-2" />
                        Export Model (.pkl)
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Action Buttons */}
      <div className="flex gap-3">
        <Button onClick={onDeploy} className="flex-1">
          <Cloud className="w-4 h-4 mr-2" />
          Deploy Model
        </Button>
        <Button variant="outline" onClick={onShare}>
          <Share className="w-4 h-4 mr-2" />
          Share Results
        </Button>
        <Button variant="outline" onClick={() => onExport('report')}>
          <Download className="w-4 h-4 mr-2" />
          Download Report
        </Button>
      </div>
    </div>
  );
};
