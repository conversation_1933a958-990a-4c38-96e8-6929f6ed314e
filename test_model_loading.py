#!/usr/bin/env python3
"""
Test script to verify the model loading fixes.
"""

import sys
import os
import logging
import asyncio

# Add the backend directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_model_loading():
    """Test the model loading with hierarchy level names."""
    logger.info("Testing model loading with hierarchy level names...")
    
    try:
        from app.classification_engines.hierarchical_engine import HierarchicalEngine
        from app.classification_engines.base_engine import ClassificationType
        
        # Create engine
        engine = HierarchicalEngine(ClassificationType.HIERARCHICAL)
        
        # Test the model loading with the actual session ID
        model_id = "fac0665b-2fa2-4735-ae32-649343be3ba3"
        
        # Test the _find_model_directory method
        model_dir = engine._find_model_directory(model_id)
        logger.info(f"Found model directory: {model_dir}")
        
        if model_dir and os.path.exists(model_dir):
            # Test loading the model
            logger.info("Testing model loading...")
            
            # Simulate the model loading process
            try:
                from transformers import AutoTokenizer, AutoModelForSequenceClassification
                import torch
                import pickle
                import json
                
                # Load tokenizer and model
                tokenizer = AutoTokenizer.from_pretrained(model_dir)
                model = AutoModelForSequenceClassification.from_pretrained(model_dir)
                
                # Set model to evaluation mode
                model.eval()
                
                # Load label encoder if available
                label_encoder_path = os.path.join(model_dir, "label_encoder.pkl")
                if os.path.exists(label_encoder_path):
                    with open(label_encoder_path, "rb") as f:
                        label_encoder = pickle.load(f)
                    engine.label_encoder = label_encoder
                    logger.info("Loaded label encoder from disk")
                
                # Load metadata if available
                metadata_path = os.path.join(model_dir, "metadata.json")
                if os.path.exists(metadata_path):
                    with open(metadata_path, "r") as f:
                        metadata = json.load(f)
                    logger.info(f"Loaded model metadata: {metadata.get('model_type', 'unknown')}")
                    
                    # Test the hierarchy restoration logic
                    hierarchy_levels = metadata.get('hierarchy_levels', 0)
                    if hierarchy_levels > 0:
                        # First try to get level names from metadata
                        level_names = metadata.get('hierarchy_level_names')
                        logger.info(f"Level names from metadata: {level_names}")
                        
                        # If not in metadata, try to get from training results
                        if not level_names:
                            try:
                                # Search for training results file that contains this model
                                import glob
                                results_pattern = os.path.join("backend/model_artifacts", "training_results_*.json")
                                for results_file in glob.glob(results_pattern):
                                    try:
                                        with open(results_file, 'r') as f:
                                            results_data = json.load(f)
                                        
                                        # Check if this results file is for our model
                                        if (results_data.get('model_id') == metadata.get('model_id') or 
                                            results_data.get('session_id') == model_id):
                                            logger.info(f"Found matching training results file: {results_file}")
                                            logger.info(f"Results data keys: {list(results_data.keys())}")
                                            
                                            # Look for hierarchy levels in results
                                            if 'hierarchy_levels' in results_data:
                                                level_names = results_data['hierarchy_levels']
                                                logger.info(f"Found hierarchy level names in training results: {level_names}")
                                                break
                                    except Exception as e:
                                        logger.warning(f"Error reading training results file {results_file}: {e}")
                                        continue
                            except Exception as e:
                                logger.warning(f"Error searching for training results: {e}")
                        
                        # Set level names
                        if level_names and isinstance(level_names, list):
                            engine.level_names = level_names
                            engine.max_depth = len(level_names)
                            logger.info(f"✅ Restored hierarchy structure: {len(level_names)} levels - {engine.level_names}")
                        else:
                            # Fallback to default level names
                            engine.level_names = [f"Level_{i+1}" for i in range(hierarchy_levels)]
                            engine.max_depth = hierarchy_levels
                            logger.info(f"⚠️ Using default hierarchy structure: {hierarchy_levels} levels - {engine.level_names}")
                    else:
                        logger.warning("❌ No hierarchy levels found in metadata")
                
                # Cache the loaded model
                engine.trained_model = model
                engine.trained_tokenizer = tokenizer
                engine.model_id = model_id
                
                logger.info("✅ Model loaded successfully")
                
                # Test a simple prediction
                test_texts = ["KCB Chess Club won first place at the tournament"]
                logger.info(f"Testing prediction with: {test_texts[0]}")
                
                predictions = await engine._predict_with_trained_model(test_texts)
                logger.info(f"Prediction result: {predictions}")
                
                if predictions and len(predictions) > 0:
                    pred = predictions[0]
                    if 'hierarchy_path' in pred and pred['hierarchy_path']:
                        logger.info(f"✅ Prediction successful: {pred['hierarchy_path']}")
                        logger.info(f"✅ Confidence: {pred['confidence']}")
                        return True
                    else:
                        logger.error(f"❌ Prediction returned empty hierarchy path: {pred}")
                        return False
                else:
                    logger.error("❌ No predictions returned")
                    return False
                
            except Exception as e:
                logger.error(f"❌ Model loading failed: {e}")
                import traceback
                traceback.print_exc()
                return False
        else:
            logger.error(f"❌ Model directory not found or doesn't exist: {model_dir}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Run the test."""
    logger.info("Starting model loading test...")
    
    success = await test_model_loading()
    
    if success:
        logger.info("🎉 Model loading test passed!")
        return True
    else:
        logger.error("❌ Model loading test failed!")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
