// frontend/src/services/universalApi.ts
// Universal API service for training and classification

import apiClient from './apiClient';

export interface UniversalTrainingRequest {
  file_id: string;
  classification_type: string; // 'binary', 'multi-class', 'multi-label', 'hierarchical'
  model_type: string; // 'llm', 'custom', 'ensemble'
  text_column: string;
  label_columns: string[];
  hierarchy_levels?: string[];
  custom_prompt?: string;
  llm_provider?: string;
  llm_model?: string;
  training_params?: Record<string, any>;

  // Model naming
  model_name?: string;

  // Dual data setup fields
  dual_data_setup?: boolean;
  training_file_id?: string;
  classification_file_id?: string;
}

export interface UniversalTrainingResponse {
  task_id: string;
  status: string;
  message: string;
  config_id: string;
  session_id: string;
}

export interface UniversalClassificationRequest {
  texts: string[];
  config: {
    classification_type: string;
    model_type: string;
    text_column: string;
    label_columns: string[];
    hierarchy_levels?: string[];
    custom_prompt?: string;
    llm_provider?: string;
    llm_model?: string;
  };
}

export interface UniversalClassificationResponse {
  results: Array<{
    text: string;
    predictions: Array<{
      label: string;
      confidence: number;
    }>;
  }>;
  total_processed: number;
  config: Record<string, any>;
}

export interface UniversalInferenceRequest {
  file_id: string;
  model_id: string;
  text_column: string;
  classification_type?: string;
  config?: Record<string, any>;
}

export interface UniversalInferenceResponse {
  task_id: string;
  status: string;
  message: string;
  results: Array<{
    text: string;
    predictions: Array<{
      label: string;
      confidence: number;
    }>;
    hierarchy_path: string[];
    confidence: number;
  }>;
  summary: {
    total_predictions: number;
    model_id: string;
    classification_type: string;
  };
}

export interface TrainingSessionStatus {
  id: string;
  config_id: string;
  session_name?: string;
  classification_type: string;
  training_method: string;
  status: string;
  progress_percentage: number;
  current_stage?: string;
  model_id?: string;
  final_metrics?: Record<string, any>;
  error_message?: string;
  created_at: string;
  started_at?: string;
  completed_at?: string;
}

/**
 * Start universal training for any classification type
 */
export const startUniversalTraining = async (
  request: UniversalTrainingRequest
): Promise<UniversalTrainingResponse> => {
  try {
    const response = await apiClient.post('/api/v2/classification/universal/train', request);
    return response.data;
  } catch (error: any) {
    console.error('Error starting universal training:', error);
    throw new Error(error.response?.data?.detail || 'Failed to start training');
  }
};

/**
 * Perform universal classification
 */
export const performUniversalClassification = async (
  request: UniversalClassificationRequest
): Promise<UniversalClassificationResponse> => {
  try {
    const response = await apiClient.post('/api/v2/classification/universal/classify', request);
    return response.data;
  } catch (error: any) {
    console.error('Error performing universal classification:', error);
    throw new Error(error.response?.data?.detail || 'Failed to perform classification');
  }
};

/**
 * Get training session status
 */
export const getTrainingSessionStatus = async (
  sessionId: string
): Promise<TrainingSessionStatus> => {
  try {
    const response = await apiClient.get(`/api/v2/classification/sessions/${sessionId}`);
    return response.data;
  } catch (error: any) {
    console.error('Error getting training session status:', error);
    throw new Error(error.response?.data?.detail || 'Failed to get training status');
  }
};

/**
 * Start LLM classification on uploaded file
 */
export const startLLMClassificationOnFile = async (
  fileId: string,
  config: {
    classification_type: string;
    text_column: string;
    label_columns: string[];
    hierarchy_levels?: string[];
    custom_prompt?: string;
    llm_provider?: string;
    llm_model?: string;
    original_filename?: string;
  }
): Promise<{ task_id: string; status: string; message: string }> => {
  try {
    // Validate required parameters
    if (!fileId) {
      throw new Error('File ID is required');
    }
    if (!config.text_column) {
      throw new Error('Text column is required');
    }
    if (!config.label_columns || config.label_columns.length === 0) {
      throw new Error('At least one label column is required');
    }
    // Build hierarchy structure based on classification type
    let hierarchy: any = {};

    if (config.classification_type === 'hierarchical' && config.hierarchy_levels) {
      // For hierarchical classification, create nested structure
      hierarchy = {
        themes: [
          {
            name: "General",
            categories: config.label_columns.map(label => ({
              name: label,
              segments: [
                {
                  name: "Default",
                  subsegments: [
                    {
                      name: "Default",
                      keywords: []
                    }
                  ]
                }
              ]
            }))
          }
        ]
      };
    } else {
      // For flat classification (binary, multi-class, multi-label)
      hierarchy = {
        classification_type: config.classification_type,
        labels: config.label_columns
      };
    }

    // Build LLM config in the expected format
    const llmConfig = {
      provider: config.llm_provider || 'OpenAI',
      endpoint: '', // Will be set by backend based on provider
      model_name: config.llm_model || 'gpt-3.5-turbo',
      api_key: null // Backend will use environment variables
    };

    // Build the request in the format expected by ClassifyLLMRequest
    const payload = {
      file_id: fileId,
      original_filename: config.original_filename || 'uploaded_file.csv',
      text_columns: [config.text_column], // Convert single column to array
      hierarchy: hierarchy,
      llm_config: llmConfig,
      hierarchy_config_id: null
    };

    console.log('Sending LLM classification request:', JSON.stringify(payload, null, 2));

    const response = await apiClient.post('/api/v2/classification/llm/classify-file', payload);
    return response.data;
  } catch (error: any) {
    console.error('Error starting LLM classification on file:', error);

    // Handle specific error cases
    if (error.message && !error.response) {
      // This is a validation error we threw
      throw error;
    } else if (error.response?.status === 404) {
      throw new Error('File not found. Please upload the file again.');
    } else if (error.response?.status === 401) {
      throw new Error('Authentication required. Please log in again.');
    } else if (error.response?.status === 429) {
      throw new Error('Too many requests. Please wait a moment and try again.');
    } else if (error.response?.status >= 500) {
      throw new Error('Server error. Please try again later.');
    }

    throw new Error(error.response?.data?.detail || 'Failed to start LLM classification');
  }
};

/**
 * Start custom model training on uploaded file
 */
export const startCustomTrainingOnFile = async (
  fileId: string,
  config: {
    classification_type: string;
    text_column: string;
    label_columns: string[];
    hierarchy_levels?: string[];
    training_params?: Record<string, any>;
  }
): Promise<UniversalTrainingResponse> => {
  try {
    const request: UniversalTrainingRequest = {
      file_id: fileId,
      classification_type: config.classification_type,
      model_type: 'custom',
      text_column: config.text_column,
      label_columns: config.label_columns,
      hierarchy_levels: config.hierarchy_levels,
      training_params: config.training_params
    };
    
    return await startUniversalTraining(request);
  } catch (error: any) {
    console.error('Error starting custom training on file:', error);
    throw new Error(error.response?.data?.detail || 'Failed to start custom training');
  }
};

/**
 * Get universal task status (for both training and classification tasks)
 */
export const getUniversalTaskStatus = async (
  taskId: string
): Promise<{
  task_id: string;
  status: string;
  progress?: number;
  result?: any;
  error?: string;
  message?: string;
  result_data_url?: string;
}> => {
  try {
    // Use the existing task status endpoint
    const response = await apiClient.get(`/tasks/${taskId}`);
    return {
      task_id: response.data.task_id,
      status: response.data.status,
      progress: response.data.progress,
      result: response.data.result,
      error: response.data.error,
      message: response.data.message,
      result_data_url: response.data.result_data_url
    };
  } catch (error: any) {
    console.error('Error getting task status:', error);
    throw new Error(error.response?.data?.detail || 'Failed to get task status');
  }
};

/**
 * Start universal inference using a trained model
 */
export const startUniversalInference = async (
  request: UniversalInferenceRequest
): Promise<UniversalInferenceResponse> => {
  try {
    const response = await apiClient.post('/api/v2/classification/universal/inference', request);
    return response.data;
  } catch (error: any) {
    console.error('Error starting universal inference:', error);
    throw new Error(error.response?.data?.detail || 'Failed to start inference');
  }
};

/**
 * Cancel a running task
 */
export const cancelTask = async (taskId: string): Promise<{ message: string }> => {
  try {
    const response = await apiClient.post(`/api/v2/classification/tasks/${taskId}/cancel`);
    return response.data;
  } catch (error: any) {
    console.error('Error canceling task:', error);
    throw new Error(error.response?.data?.detail || 'Failed to cancel task');
  }
};
