"""Compliance Manager for Enterprise ClassyWeb.

Provides compliance monitoring, policy enforcement, and regulatory compliance
features for enterprise deployments.
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Set
from enum import Enum
from dataclasses import dataclass, asdict

logger = logging.getLogger(__name__)


class ComplianceFramework(Enum):
    """Supported compliance frameworks."""
    GDPR = "gdpr"
    HIPAA = "hipaa"
    SOX = "sox"
    PCI_DSS = "pci_dss"
    ISO_27001 = "iso_27001"
    CCPA = "ccpa"
    SOC2 = "soc2"


class ComplianceStatus(Enum):
    """Compliance status levels."""
    COMPLIANT = "compliant"
    NON_COMPLIANT = "non_compliant"
    PARTIALLY_COMPLIANT = "partially_compliant"
    UNDER_REVIEW = "under_review"
    NOT_APPLICABLE = "not_applicable"


@dataclass
class ComplianceRule:
    """Represents a compliance rule."""
    rule_id: str
    framework: ComplianceFramework
    title: str
    description: str
    severity: str
    automated_check: bool
    check_function: Optional[str] = None
    remediation_steps: Optional[List[str]] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert rule to dictionary."""
        data = asdict(self)
        data['framework'] = self.framework.value
        return data


@dataclass
class ComplianceCheck:
    """Represents a compliance check result."""
    check_id: str
    rule_id: str
    status: ComplianceStatus
    timestamp: datetime
    details: Dict[str, Any]
    remediation_required: bool
    tenant_id: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert check to dictionary."""
        data = asdict(self)
        data['status'] = self.status.value
        data['timestamp'] = self.timestamp.isoformat()
        return data


class ComplianceManager:
    """Manages compliance monitoring and policy enforcement."""
    
    def __init__(self):
        """Initialize the compliance manager."""
        self.rules: Dict[str, ComplianceRule] = {}
        self.checks: List[ComplianceCheck] = []
        self.enabled_frameworks: Set[ComplianceFramework] = set()
        
        # Initialize default rules
        self._initialize_default_rules()
        
        logger.info("Compliance Manager initialized")
    
    def _initialize_default_rules(self) -> None:
        """Initialize default compliance rules."""
        default_rules = [
            ComplianceRule(
                rule_id="gdpr_data_retention",
                framework=ComplianceFramework.GDPR,
                title="Data Retention Policy",
                description="Personal data must not be kept longer than necessary",
                severity="high",
                automated_check=True,
                check_function="check_data_retention",
                remediation_steps=[
                    "Review data retention policies",
                    "Delete unnecessary personal data",
                    "Implement automated data cleanup"
                ]
            ),
            ComplianceRule(
                rule_id="gdpr_consent_tracking",
                framework=ComplianceFramework.GDPR,
                title="Consent Tracking",
                description="User consent must be tracked and verifiable",
                severity="critical",
                automated_check=True,
                check_function="check_consent_tracking"
            ),
            ComplianceRule(
                rule_id="hipaa_data_encryption",
                framework=ComplianceFramework.HIPAA,
                title="Data Encryption",
                description="PHI must be encrypted at rest and in transit",
                severity="critical",
                automated_check=True,
                check_function="check_data_encryption"
            ),
            ComplianceRule(
                rule_id="sox_audit_trail",
                framework=ComplianceFramework.SOX,
                title="Audit Trail Integrity",
                description="Audit trails must be complete and tamper-proof",
                severity="high",
                automated_check=True,
                check_function="check_audit_trail_integrity"
            ),
            ComplianceRule(
                rule_id="iso27001_access_control",
                framework=ComplianceFramework.ISO_27001,
                title="Access Control",
                description="Access to systems must be controlled and monitored",
                severity="high",
                automated_check=True,
                check_function="check_access_control"
            )
        ]
        
        for rule in default_rules:
            self.rules[rule.rule_id] = rule
    
    def enable_framework(self, framework: ComplianceFramework) -> None:
        """Enable a compliance framework."""
        self.enabled_frameworks.add(framework)
        logger.info(f"Enabled compliance framework: {framework.value}")
    
    def disable_framework(self, framework: ComplianceFramework) -> None:
        """Disable a compliance framework."""
        self.enabled_frameworks.discard(framework)
        logger.info(f"Disabled compliance framework: {framework.value}")
    
    def add_custom_rule(self, rule: ComplianceRule) -> None:
        """Add a custom compliance rule."""
        self.rules[rule.rule_id] = rule
        logger.info(f"Added custom compliance rule: {rule.rule_id}")
    
    def run_compliance_check(
        self,
        rule_id: Optional[str] = None,
        framework: Optional[ComplianceFramework] = None,
        tenant_id: Optional[str] = None
    ) -> List[ComplianceCheck]:
        """Run compliance checks."""
        checks_to_run = []
        
        if rule_id:
            if rule_id in self.rules:
                checks_to_run = [self.rules[rule_id]]
        elif framework:
            checks_to_run = [rule for rule in self.rules.values() if rule.framework == framework]
        else:
            # Run all checks for enabled frameworks
            checks_to_run = [
                rule for rule in self.rules.values() 
                if rule.framework in self.enabled_frameworks
            ]
        
        results = []
        for rule in checks_to_run:
            try:
                result = self._execute_compliance_check(rule, tenant_id)
                results.append(result)
                self.checks.append(result)
            except Exception as e:
                logger.error(f"Failed to execute compliance check {rule.rule_id}: {e}")
                # Create a failed check result
                failed_check = ComplianceCheck(
                    check_id=f"{rule.rule_id}_{datetime.utcnow().timestamp()}",
                    rule_id=rule.rule_id,
                    status=ComplianceStatus.NON_COMPLIANT,
                    timestamp=datetime.utcnow(),
                    details={"error": str(e)},
                    remediation_required=True,
                    tenant_id=tenant_id
                )
                results.append(failed_check)
                self.checks.append(failed_check)
        
        return results
    
    def _execute_compliance_check(self, rule: ComplianceRule, tenant_id: Optional[str]) -> ComplianceCheck:
        """Execute a single compliance check."""
        check_id = f"{rule.rule_id}_{datetime.utcnow().timestamp()}"
        
        if not rule.automated_check or not rule.check_function:
            # Manual check - mark as under review
            return ComplianceCheck(
                check_id=check_id,
                rule_id=rule.rule_id,
                status=ComplianceStatus.UNDER_REVIEW,
                timestamp=datetime.utcnow(),
                details={"message": "Manual review required"},
                remediation_required=False,
                tenant_id=tenant_id
            )
        
        # Execute automated check
        check_result = self._run_automated_check(rule.check_function, tenant_id)
        
        return ComplianceCheck(
            check_id=check_id,
            rule_id=rule.rule_id,
            status=check_result["status"],
            timestamp=datetime.utcnow(),
            details=check_result["details"],
            remediation_required=check_result.get("remediation_required", False),
            tenant_id=tenant_id
        )
    
    def _run_automated_check(self, check_function: str, tenant_id: Optional[str]) -> Dict[str, Any]:
        """Run an automated compliance check function."""
        # This is a simplified implementation
        # In a real system, these would be actual check functions
        
        check_functions = {
            "check_data_retention": self._check_data_retention,
            "check_consent_tracking": self._check_consent_tracking,
            "check_data_encryption": self._check_data_encryption,
            "check_audit_trail_integrity": self._check_audit_trail_integrity,
            "check_access_control": self._check_access_control
        }
        
        if check_function in check_functions:
            return check_functions[check_function](tenant_id)
        else:
            return {
                "status": ComplianceStatus.NOT_APPLICABLE,
                "details": {"message": f"Check function {check_function} not implemented"},
                "remediation_required": False
            }
    
    def _check_data_retention(self, tenant_id: Optional[str]) -> Dict[str, Any]:
        """Check data retention compliance."""
        # Simplified check - in reality would check actual data retention
        return {
            "status": ComplianceStatus.COMPLIANT,
            "details": {"message": "Data retention policies are in place"},
            "remediation_required": False
        }
    
    def _check_consent_tracking(self, tenant_id: Optional[str]) -> Dict[str, Any]:
        """Check consent tracking compliance."""
        return {
            "status": ComplianceStatus.PARTIALLY_COMPLIANT,
            "details": {"message": "Consent tracking implemented but needs improvement"},
            "remediation_required": True
        }
    
    def _check_data_encryption(self, tenant_id: Optional[str]) -> Dict[str, Any]:
        """Check data encryption compliance."""
        return {
            "status": ComplianceStatus.COMPLIANT,
            "details": {"message": "Data encryption is properly configured"},
            "remediation_required": False
        }
    
    def _check_audit_trail_integrity(self, tenant_id: Optional[str]) -> Dict[str, Any]:
        """Check audit trail integrity."""
        return {
            "status": ComplianceStatus.COMPLIANT,
            "details": {"message": "Audit trails are complete and tamper-proof"},
            "remediation_required": False
        }
    
    def _check_access_control(self, tenant_id: Optional[str]) -> Dict[str, Any]:
        """Check access control compliance."""
        return {
            "status": ComplianceStatus.COMPLIANT,
            "details": {"message": "Access controls are properly implemented"},
            "remediation_required": False
        }
    
    def get_compliance_status(
        self,
        framework: Optional[ComplianceFramework] = None,
        tenant_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """Get overall compliance status."""
        relevant_checks = self.checks
        
        if framework:
            relevant_rules = [rule_id for rule_id, rule in self.rules.items() if rule.framework == framework]
            relevant_checks = [check for check in self.checks if check.rule_id in relevant_rules]
        
        if tenant_id:
            relevant_checks = [check for check in relevant_checks if check.tenant_id == tenant_id]
        
        if not relevant_checks:
            return {
                "overall_status": ComplianceStatus.NOT_APPLICABLE.value,
                "total_checks": 0,
                "compliant": 0,
                "non_compliant": 0,
                "partially_compliant": 0,
                "under_review": 0,
                "compliance_percentage": 0.0
            }
        
        status_counts = {}
        for status in ComplianceStatus:
            status_counts[status.value] = sum(1 for check in relevant_checks if check.status == status)
        
        total_checks = len(relevant_checks)
        compliant_checks = status_counts.get(ComplianceStatus.COMPLIANT.value, 0)
        compliance_percentage = (compliant_checks / total_checks) * 100 if total_checks > 0 else 0
        
        # Determine overall status
        if status_counts.get(ComplianceStatus.NON_COMPLIANT.value, 0) > 0:
            overall_status = ComplianceStatus.NON_COMPLIANT.value
        elif status_counts.get(ComplianceStatus.PARTIALLY_COMPLIANT.value, 0) > 0:
            overall_status = ComplianceStatus.PARTIALLY_COMPLIANT.value
        elif status_counts.get(ComplianceStatus.UNDER_REVIEW.value, 0) > 0:
            overall_status = ComplianceStatus.UNDER_REVIEW.value
        else:
            overall_status = ComplianceStatus.COMPLIANT.value
        
        return {
            "overall_status": overall_status,
            "total_checks": total_checks,
            "compliant": status_counts.get(ComplianceStatus.COMPLIANT.value, 0),
            "non_compliant": status_counts.get(ComplianceStatus.NON_COMPLIANT.value, 0),
            "partially_compliant": status_counts.get(ComplianceStatus.PARTIALLY_COMPLIANT.value, 0),
            "under_review": status_counts.get(ComplianceStatus.UNDER_REVIEW.value, 0),
            "compliance_percentage": compliance_percentage
        }
    
    def generate_compliance_report(
        self,
        framework: Optional[ComplianceFramework] = None,
        tenant_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """Generate a comprehensive compliance report."""
        status = self.get_compliance_status(framework, tenant_id)
        
        # Get recent checks
        recent_checks = sorted(
            [check for check in self.checks if 
             (not framework or self.rules[check.rule_id].framework == framework) and
             (not tenant_id or check.tenant_id == tenant_id)],
            key=lambda x: x.timestamp,
            reverse=True
        )[:50]  # Last 50 checks
        
        # Get remediation items
        remediation_items = [
            {
                "rule_id": check.rule_id,
                "rule_title": self.rules[check.rule_id].title,
                "status": check.status.value,
                "details": check.details,
                "remediation_steps": self.rules[check.rule_id].remediation_steps or []
            }
            for check in recent_checks
            if check.remediation_required
        ]
        
        return {
            "report_timestamp": datetime.utcnow().isoformat(),
            "framework": framework.value if framework else "all",
            "tenant_id": tenant_id,
            "compliance_status": status,
            "recent_checks": [check.to_dict() for check in recent_checks],
            "remediation_items": remediation_items,
            "enabled_frameworks": [fw.value for fw in self.enabled_frameworks]
        }


# Global compliance manager instance
compliance_manager = ComplianceManager()
