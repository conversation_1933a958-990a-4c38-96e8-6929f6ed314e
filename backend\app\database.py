"""
Database models and connection utilities for the ClassyWeb ML Platform.

This module provides:
1. SQLAlchemy ORM models for all database tables
2. Database connection and session management
3. CRUD operations for all models
4. Helper functions for common database operations

All database operations use parameterized queries to prevent SQL injection.
"""
import logging
import time
import os
import uuid
from datetime import datetime, timezone
from typing import Dict, Any, Optional, List, Tuple
from functools import wraps
from pathlib import Path
from enum import Enum

from sqlalchemy import create_engine, Column, Integer, String, DateTime, Text, ForeignKey, Boolean, JSON, Float, Enum as SQLEnum, Index
from sqlalchemy.orm import declarative_base
from sqlalchemy.orm import sessionmaker, relationship, Session
from sqlalchemy.pool import QueuePool
from sqlalchemy.sql import text
from sqlalchemy.exc import SQLAlchemyError

from . import config # Use relative import for config within the backend package
from .cache import cache_result, invalidate_model_cache # Import from app directory
from .cache_service import get_hierarchy_cache, get_query_cache

# Set up logging
logger = logging.getLogger(__name__)

# Helper functions
def get_utc_now():
    """Return current UTC time with timezone information."""
    return datetime.now(timezone.utc)


def generate_uuid():
    """Generate a UUID string."""
    return str(uuid.uuid4())


def validate_input_data(data: Dict[str, Any]) -> Dict[str, Any]:
    """Validate and sanitize input data for database operations."""
    if not isinstance(data, dict):
        raise ValueError("Input data must be a dictionary")

    # Remove None values and empty strings
    cleaned_data = {}
    for key, value in data.items():
        if value is not None and value != "":
            # Sanitize string values
            if isinstance(value, str):
                # Remove potentially dangerous characters
                value = value.strip()
                if len(value) > 10000:  # Prevent extremely long strings
                    raise ValueError(f"Value for {key} is too long")
            cleaned_data[key] = value

    return cleaned_data


def secure_db_operation(func):
    """Decorator to add security checks to database operations."""
    @wraps(func)
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except SQLAlchemyError as e:
            logger.error(f"Database operation failed: {e}", exc_info=True)
            raise ValueError("Database operation failed")
        except Exception as e:
            logger.error(f"Unexpected error in database operation: {e}", exc_info=True)
            raise
    return wrapper


# Enums for the classification system
class ClassificationTypeEnum(str, Enum):
    """Classification type enumeration."""
    BINARY = "binary"
    MULTICLASS = "multiclass"
    MULTILABEL = "multilabel"
    HIERARCHICAL = "hierarchical"
    FLAT = "flat"


class TrainingMethodEnum(str, Enum):
    """Training method enumeration."""
    CUSTOM = "custom"
    LLM = "llm"


class TrainingStatusEnum(str, Enum):
    """Training status enumeration."""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"


class LicenseTypeEnum(str, Enum):
    """License type enumeration."""
    PERSONAL = "personal"
    PROFESSIONAL = "professional"
    ENTERPRISE = "enterprise"


class LicenseStatusEnum(str, Enum):
    """License status enumeration."""
    ACTIVE = "active"
    SUSPENDED = "suspended"
    EXPIRED = "expired"


class ModelStatusEnum(str, Enum):
    """Model status enumeration."""
    ACTIVE = "active"
    INACTIVE = "inactive"

# Create SQLAlchemy engine with connection pooling and security settings
engine = create_engine(
    config.DATABASE_URL,
    echo=config.DATABASE_ECHO,
    # SQLite-specific settings
    connect_args={"check_same_thread": False} if config.DATABASE_URL.startswith("sqlite") else {},
    # Connection pooling for better performance and security
    poolclass=QueuePool,
    pool_size=10,  # Default number of connections to keep open
    max_overflow=20,  # Allow up to 20 connections to be opened above pool_size
    pool_timeout=30,  # Seconds to wait before giving up on getting a connection
    pool_recycle=1800,  # Recycle connections after 30 minutes to avoid stale connections
    # Security settings
    pool_pre_ping=True,  # Verify connections before using them (prevents stale connections)
    isolation_level="SERIALIZABLE" if not config.DATABASE_URL.startswith("sqlite") else None  # Highest isolation level for non-SQLite databases
)

# Create session factory
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Create base class for models
Base = declarative_base()

# Define models
class User(Base):
    """User model for authentication."""
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), unique=True, index=True, nullable=True)  # Optional for OAuth users
    email = Column(String(100), unique=True, index=True)
    hashed_password = Column(String(100), nullable=True)  # Nullable for OAuth-only users

    # OAuth related fields
    oauth_provider = Column(String(20), nullable=True)  # 'google', 'github', etc.
    oauth_user_id = Column(String(100), nullable=True, index=True)  # ID from the provider

    # User profile
    first_name = Column(String(50), nullable=True)
    last_name = Column(String(50), nullable=True)
    profile_picture = Column(String(255), nullable=True)  # URL to profile picture
    bio = Column(Text, nullable=True)  # User's bio or about me text
    job_title = Column(String(100), nullable=True)  # User's job title
    company = Column(String(100), nullable=True)  # User's company or organization
    website = Column(String(255), nullable=True)  # User's website URL
    location = Column(String(100), nullable=True)  # User's location
    theme_preference = Column(String(20), nullable=True, default="light")  # UI theme preference

    # Account status
    is_active = Column(Boolean, default=True)
    is_verified = Column(Boolean, default=False)  # Email verification status
    verification_token = Column(String(100), nullable=True)  # For email verification
    verification_token_expires = Column(DateTime, nullable=True)

    # Password reset
    reset_token = Column(String(100), nullable=True)
    reset_token_expires = Column(DateTime, nullable=True)

    # Timestamps
    created_at = Column(DateTime, default=get_utc_now)
    updated_at = Column(DateTime, default=get_utc_now, onupdate=get_utc_now)
    last_login = Column(DateTime, nullable=True)

    # Relationships
    files = relationship("File", back_populates="user", cascade="all, delete-orphan")
    tasks = relationship("Task", back_populates="user", cascade="all, delete-orphan")
    hierarchy_configs = relationship("HierarchyConfig", back_populates="user", cascade="all, delete-orphan")

    # Performance indexes
    __table_args__ = (
        Index('idx_user_email_active', 'email', 'is_active'),
        Index('idx_user_oauth', 'oauth_provider', 'oauth_user_id'),
        Index('idx_user_created_at', 'created_at'),
        Index('idx_user_last_login', 'last_login'),
    )


class File(Base):
    """File model for uploaded files."""
    __tablename__ = "files"

    id = Column(String(36), primary_key=True, index=True)  # UUID as string
    filename = Column(String(255))
    file_path = Column(String(255))
    file_size = Column(Integer)
    num_rows = Column(Integer)
    columns = Column(JSON)  # Store column names as JSON array
    user_id = Column(Integer, ForeignKey("users.id"), nullable=True, index=True)
    created_at = Column(DateTime(timezone=True), default=get_utc_now)
    updated_at = Column(DateTime(timezone=True), default=get_utc_now, onupdate=get_utc_now)

    # Relationships
    user = relationship("User", back_populates="files")
    tasks = relationship("Task", back_populates="input_file")

    # Performance indexes
    __table_args__ = (
        Index('idx_file_user_created', 'user_id', 'created_at'),
        Index('idx_file_size', 'file_size'),
        Index('idx_file_filename', 'filename'),
    )


class Task(Base):
    """Task model for background tasks."""
    __tablename__ = "tasks"

    id = Column(String(36), primary_key=True, index=True)  # UUID as string
    task_type = Column(String(50))  # e.g., "llm_classification", "hf_training", "hf_classification"
    status = Column(String(20), index=True)  # "PENDING", "RUNNING", "SUCCESS", "FAILED"
    message = Column(Text, nullable=True)
    input_file_id = Column(String(36), ForeignKey("files.id"))
    result_file_path = Column(String(255), nullable=True)
    config = Column(JSON, nullable=True)  # Store task configuration as JSON
    user_id = Column(Integer, ForeignKey("users.id"), nullable=True, index=True)
    created_at = Column(DateTime(timezone=True), default=get_utc_now)
    updated_at = Column(DateTime(timezone=True), default=get_utc_now, onupdate=get_utc_now)
    completed_at = Column(DateTime(timezone=True), nullable=True)

    # Relationships
    input_file = relationship("File", back_populates="tasks")
    user = relationship("User", back_populates="tasks")





class HierarchyConfig(Base):
    """Model for storing custom hierarchy configurations."""
    __tablename__ = "hierarchy_configs"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100))  # Name of this configuration
    is_default = Column(Boolean, default=False, index=True)  # Whether this is the default configuration
    hierarchy_levels = Column(JSON)  # Store hierarchy levels as JSON array
    user_id = Column(Integer, ForeignKey("users.id"), nullable=True, index=True)  # Optional user association

    # Enhanced fields for dynamic hierarchy support
    description = Column(String(500), nullable=True)  # Optional description
    domain = Column(String(100), nullable=True)  # Domain/industry this hierarchy is for (e.g., "E-commerce", "Legal", "Medical")
    validation_rules = Column(JSON, nullable=True)  # Custom validation rules per level
    ui_config = Column(JSON, nullable=True)  # UI configuration (display names, colors, etc.)
    confidence_thresholds = Column(JSON, nullable=True)  # Confidence thresholds per level
    extra_metadata = Column(JSON, nullable=True)  # Additional metadata for extensibility

    created_at = Column(DateTime(timezone=True), default=get_utc_now)
    updated_at = Column(DateTime(timezone=True), default=get_utc_now, onupdate=get_utc_now)

    # Relationships
    user = relationship("User", back_populates="hierarchy_configs")





class ClassificationConfig(Base):
    """Configuration for classification tasks."""
    __tablename__ = "classification_configs"

    id = Column(String(36), primary_key=True, index=True, default=generate_uuid)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)

    # Basic configuration
    name = Column(String(100), nullable=False)
    description = Column(Text, nullable=True)
    classification_type = Column(SQLEnum(ClassificationTypeEnum), nullable=False, index=True)
    training_method = Column(SQLEnum(TrainingMethodEnum), nullable=False)

    # Configuration data (JSON for flexibility)
    config_data = Column(JSON, nullable=False)

    # Dataset information
    dataset_info = Column(JSON, nullable=True)
    label_mapping = Column(JSON, nullable=True)

    # Detection metadata
    detection_confidence = Column(Float, nullable=True)
    detection_reasoning = Column(Text, nullable=True)
    alternative_types = Column(JSON, nullable=True)

    # Timestamps
    created_at = Column(DateTime(timezone=True), default=get_utc_now, index=True)
    updated_at = Column(DateTime(timezone=True), default=get_utc_now, onupdate=get_utc_now)

    # Relationships
    training_sessions = relationship("TrainingSession", back_populates="config")
    performance_records = relationship("ModelPerformance", back_populates="config")


class TrainingSession(Base):
    """Training session tracking."""
    __tablename__ = "training_sessions"

    id = Column(String(36), primary_key=True, index=True, default=generate_uuid)
    config_id = Column(String(36), ForeignKey("classification_configs.id"), nullable=False, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)

    # Session metadata
    session_name = Column(String(255), nullable=True)

    # Training configuration
    classification_type = Column(SQLEnum(ClassificationTypeEnum), nullable=False, index=True)
    training_method = Column(SQLEnum(TrainingMethodEnum), nullable=False)
    training_config = Column(JSON, nullable=False)

    # Status tracking
    status = Column(SQLEnum(TrainingStatusEnum), default=TrainingStatusEnum.PENDING, index=True)
    progress_percentage = Column(Float, default=0.0)
    current_stage = Column(String(50), nullable=True)
    progress_data = Column(JSON, nullable=True)
    current_epoch = Column(Integer, nullable=True)
    total_epochs = Column(Integer, nullable=True)

    # System monitoring
    gpu_utilization = Column(JSON, nullable=True)
    memory_usage = Column(JSON, nullable=True)
    system_metrics = Column(JSON, nullable=True)

    # Results
    model_id = Column(String(36), nullable=True)
    final_metrics = Column(JSON, nullable=True)
    training_history = Column(JSON, nullable=True)
    error_message = Column(Text, nullable=True)

    # File paths
    model_path = Column(String(255), nullable=True)
    tokenizer_path = Column(String(255), nullable=True)
    config_path = Column(String(255), nullable=True)
    results_file_id = Column(String(36), ForeignKey("files.id"), nullable=True)  # For dual data classification results

    # Timestamps
    created_at = Column(DateTime(timezone=True), default=get_utc_now, index=True)
    started_at = Column(DateTime(timezone=True), nullable=True)
    completed_at = Column(DateTime(timezone=True), nullable=True)
    updated_at = Column(DateTime(timezone=True), default=get_utc_now, onupdate=get_utc_now)

    # Relationships
    config = relationship("ClassificationConfig", back_populates="training_sessions")


class ModelPerformance(Base):
    """Model performance tracking."""
    __tablename__ = "model_performance"

    id = Column(String(36), primary_key=True, index=True, default=generate_uuid)
    config_id = Column(String(36), ForeignKey("classification_configs.id"), nullable=False, index=True)
    model_id = Column(String(36), nullable=False, index=True)

    # Model information
    model_name = Column(String(100), nullable=False)
    classification_type = Column(SQLEnum(ClassificationTypeEnum), nullable=False, index=True)
    training_method = Column(SQLEnum(TrainingMethodEnum), nullable=False)

    # Core metrics (JSON for flexibility)
    metrics = Column(JSON, nullable=False)

    # Type-specific metrics
    binary_metrics = Column(JSON, nullable=True)
    multiclass_metrics = Column(JSON, nullable=True)
    multilabel_metrics = Column(JSON, nullable=True)
    hierarchical_metrics = Column(JSON, nullable=True)

    # Validation data
    validation_data = Column(JSON, nullable=True)
    confusion_matrix = Column(JSON, nullable=True)
    feature_importance = Column(JSON, nullable=True)

    # Benchmarking
    benchmark_dataset = Column(String(100), nullable=True)
    benchmark_score = Column(Float, nullable=True)
    comparison_models = Column(JSON, nullable=True)

    # Timestamps
    created_at = Column(DateTime(timezone=True), default=get_utc_now, index=True)
    updated_at = Column(DateTime(timezone=True), default=get_utc_now, onupdate=get_utc_now)

    # Relationships
    config = relationship("ClassificationConfig", back_populates="performance_records")


class License(Base):
    """License management."""
    __tablename__ = "licenses"

    id = Column(String(36), primary_key=True, index=True, default=generate_uuid)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)

    # License details
    license_type = Column(SQLEnum(LicenseTypeEnum), nullable=False, index=True)
    license_key = Column(String(255), unique=True, nullable=False)

    # Hardware binding
    hardware_fingerprint = Column(String(255), nullable=True)
    activation_count = Column(Integer, default=0)
    max_activations = Column(Integer, default=1)

    # Validity
    expires_at = Column(DateTime(timezone=True), nullable=True)
    status = Column(SQLEnum(LicenseStatusEnum), default=LicenseStatusEnum.ACTIVE, index=True)

    # Features
    feature_flags = Column(JSON, nullable=True)
    usage_limits = Column(JSON, nullable=True)

    # Timestamps
    created_at = Column(DateTime(timezone=True), default=get_utc_now, index=True)
    updated_at = Column(DateTime(timezone=True), default=get_utc_now, onupdate=get_utc_now)
    last_validated = Column(DateTime(timezone=True), nullable=True)


class MarketplaceModel(Base):
    """Models available in the marketplace."""
    __tablename__ = "marketplace_models"

    id = Column(String(36), primary_key=True, index=True, default=generate_uuid)

    # Model details
    name = Column(String(100), nullable=False, index=True)
    description = Column(Text, nullable=True)
    version = Column(String(20), default="1.0.0")

    # Classification info
    classification_type = Column(SQLEnum(ClassificationTypeEnum), nullable=False, index=True)
    supported_languages = Column(JSON, nullable=True)
    domain = Column(String(50), nullable=True, index=True)

    # Pricing
    price_cents = Column(Integer, nullable=False, default=0)
    currency = Column(String(3), default="USD")

    # Performance metrics
    accuracy = Column(Float, nullable=True)
    f1_score = Column(Float, nullable=True)
    benchmark_metrics = Column(JSON, nullable=True)

    # Usage statistics
    download_count = Column(Integer, default=0)
    rating = Column(Float, default=0.0)
    rating_count = Column(Integer, default=0)

    # File storage
    model_file_path = Column(String(255), nullable=True)
    model_size_mb = Column(Float, nullable=True)
    encryption_key = Column(String(255), nullable=True)

    # Publisher info
    created_by = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    publisher_name = Column(String(100), nullable=True)
    publisher_verified = Column(Boolean, default=False)

    # Status
    status = Column(SQLEnum(ModelStatusEnum), default=ModelStatusEnum.ACTIVE, index=True)
    featured = Column(Boolean, default=False)

    # Metadata
    tags = Column(JSON, nullable=True)
    requirements = Column(JSON, nullable=True)

    # Timestamps
    created_at = Column(DateTime(timezone=True), default=get_utc_now, index=True)
    updated_at = Column(DateTime(timezone=True), default=get_utc_now, onupdate=get_utc_now)

    # Relationships
    purchases = relationship("ModelPurchase", back_populates="model")


class ModelPurchase(Base):
    """Model purchase tracking."""
    __tablename__ = "model_purchases"

    id = Column(String(36), primary_key=True, index=True, default=generate_uuid)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    model_id = Column(String(36), ForeignKey("marketplace_models.id"), nullable=False, index=True)

    # Purchase details
    purchase_price_cents = Column(Integer, nullable=False)
    currency = Column(String(3), default="USD")
    license_type = Column(String(20), default="personal")

    # Download management
    download_key = Column(String(255), unique=True, nullable=True)
    download_count = Column(Integer, default=0)
    max_downloads = Column(Integer, default=5)

    # Timestamps
    purchased_at = Column(DateTime(timezone=True), default=get_utc_now, index=True)
    expires_at = Column(DateTime(timezone=True), nullable=True)
    last_downloaded = Column(DateTime(timezone=True), nullable=True)

    # Relationships
    model = relationship("MarketplaceModel", back_populates="purchases")

# Database utility functions
def create_task(db: Session, task_data: Dict[str, Any]) -> Task:
    """Create a new task in the database."""
    db_task = Task(**task_data)
    db.add(db_task)
    db.commit()
    db.refresh(db_task)
    return db_task


def update_task(db: Session, task_id: str, update_data: Dict[str, Any]) -> Optional[Task]:
    """Update an existing task in the database."""
    db_task = db.query(Task).filter(Task.id == task_id).first()
    if not db_task:
        return None

    for key, value in update_data.items():
        setattr(db_task, key, value)

    if update_data.get("status") == "SUCCESS" or update_data.get("status") == "FAILED":
        db_task.completed_at = get_utc_now()

    db.commit()
    db.refresh(db_task)
    return db_task


def get_task(db: Session, task_id: str) -> Optional[Task]:
    """Get a task by ID."""
    return db.query(Task).filter(Task.id == task_id).first()


def get_tasks(db: Session, user_id: Optional[int] = None, skip: int = 0, limit: int = 100) -> List[Task]:
    """Get a list of tasks with pagination, optionally filtered by user ID."""
    query = db.query(Task).order_by(Task.created_at.desc())
    if user_id is not None:
        query = query.filter(Task.user_id == user_id)
    return query.offset(skip).limit(limit).all()


def delete_task(db: Session, task_id: str) -> bool:
    """Delete a task from the database and its result file if it exists.

    Args:
        db: Database session
        task_id: ID of the task to delete

    Returns:
        True if the task was deleted successfully, False otherwise.
    """
    from pathlib import Path

    # Get the task record using a parameterized query for security
    db_task = db.query(Task).filter(Task.id == task_id).first()
    if not db_task:
        logger.warning(f"Attempted to delete non-existent task with ID: {task_id}")
        return False

    try:
        # Get the result file path before deleting the record
        result_file_path = db_task.result_file_path

        # Delete the task record from the database
        db.delete(db_task)
        db.commit()
        logger.info(f"Deleted task record with ID: {task_id}, Type: {db_task.task_type}")

        # Delete the result file from the filesystem if it exists
        if result_file_path:
            file_path = Path(result_file_path)
            if file_path.exists():
                try:
                    file_path.unlink()  # More secure than os.remove
                    logger.info(f"Deleted result file from filesystem: {result_file_path}")
                except Exception as e:
                    logger.error(f"Error deleting result file from filesystem: {e}", exc_info=True)
                    # Continue even if file deletion fails

        return True
    except Exception as e:
        db.rollback()
        logger.error(f"Error deleting task ID {task_id}: {e}", exc_info=True)
        return False


def create_file(db: Session, file_data: Dict[str, Any]) -> File:
    """Create a new file record in the database."""
    db_file = File(**file_data)
    db.add(db_file)
    db.commit()
    db.refresh(db_file)
    return db_file


def get_file(db: Session, file_id: str) -> Optional[File]:
    """Get a file by ID."""
    return db.query(File).filter(File.id == file_id).first()


def get_files(db: Session, skip: int = 0, limit: int = 100, user_id: Optional[int] = None) -> List[File]:
    """Get a list of files with pagination."""
    query = db.query(File).order_by(File.created_at.desc())
    if user_id is not None:
        query = query.filter(File.user_id == user_id)
    return query.offset(skip).limit(limit).all()


def update_file(db: Session, file_id: str, update_data: Dict[str, Any]) -> Optional[File]:
    """Update an existing file in the database."""
    db_file = db.query(File).filter(File.id == file_id).first()
    if not db_file:
        return None

    for key, value in update_data.items():
        setattr(db_file, key, value)

    db.commit()
    db.refresh(db_file)
    return db_file


def delete_file(db: Session, file_id: str) -> bool:
    """Delete a file from the database and filesystem.

    Args:
        db: Database session
        file_id: ID of the file to delete

    Returns:
        True if the file was deleted successfully, False otherwise.
    """
    from pathlib import Path

    # Get the file record using a parameterized query for security
    db_file = db.query(File).filter(File.id == file_id).first()
    if not db_file:
        logger.warning(f"Attempted to delete non-existent file with ID: {file_id}")
        return False

    try:
        # Get the file path before deleting the record
        file_path_str = db_file.file_path

        # Delete the file record from the database
        db.delete(db_file)
        db.commit()
        logger.info(f"Deleted file record with ID: {file_id}, Filename: {db_file.filename}")

        # Delete the file from the filesystem if it exists
        if file_path_str:
            file_path = Path(file_path_str)
            if file_path.exists():
                try:
                    file_path.unlink()  # More secure than os.remove
                    logger.info(f"Deleted file from filesystem: {file_path_str}")
                except Exception as e:
                    logger.error(f"Error deleting file from filesystem: {e}", exc_info=True)
                    # Continue even if file deletion fails

        return True
    except Exception as e:
        db.rollback()
        logger.error(f"Error deleting file ID {file_id}: {e}", exc_info=True)
        return False


# User management functions
def get_user_by_email(db: Session, email: str) -> Optional[User]:
    """Get a user by email."""
    return db.query(User).filter(User.email == email).first()


def get_user_by_username(db: Session, username: str) -> Optional[User]:
    """Get a user by username."""
    return db.query(User).filter(User.username == username).first()


def get_user_by_oauth(db: Session, provider: str, provider_user_id: str) -> Optional[User]:
    """Get a user by OAuth provider and provider user ID."""
    return db.query(User).filter(
        User.oauth_provider == provider,
        User.oauth_user_id == provider_user_id
    ).first()


def get_user_by_verification_token(db: Session, token: str) -> Optional[User]:
    """Get a user by verification token."""
    return db.query(User).filter(
        User.verification_token == token,
        User.verification_token_expires > get_utc_now()
    ).first()


def get_user_by_reset_token(db: Session, token: str) -> Optional[User]:
    """Get a user by password reset token."""
    return db.query(User).filter(
        User.reset_token == token,
        User.reset_token_expires > get_utc_now()
    ).first()


def create_user(db: Session, user_data: Dict[str, Any]) -> User:
    """Create a new user in the database."""
    db_user = User(**user_data)
    db.add(db_user)
    db.commit()
    db.refresh(db_user)
    return db_user


def update_user(db: Session, user_id: int, update_data: Dict[str, Any]) -> Optional[User]:
    """Update an existing user in the database."""
    db_user = db.query(User).filter(User.id == user_id).first()
    if not db_user:
        return None

    for key, value in update_data.items():
        setattr(db_user, key, value)

    db.commit()
    db.refresh(db_user)
    return db_user


# Hierarchy configuration functions
def get_hierarchy_config(db: Session, config_id: int) -> Optional[HierarchyConfig]:
    """Get a hierarchy configuration by ID with caching."""
    # Try cache first
    hierarchy_cache = get_hierarchy_cache()
    cached_config = hierarchy_cache.get_cached_hierarchy_config(config_id)

    if cached_config:
        # Convert cached dict back to model instance
        config = HierarchyConfig(**cached_config)
        return config

    # Query database
    config = db.query(HierarchyConfig).filter(HierarchyConfig.id == config_id).first()

    # Cache the result
    if config:
        config_dict = {
            'id': config.id,
            'name': config.name,
            'is_default': config.is_default,
            'hierarchy_levels': config.hierarchy_levels,
            'user_id': config.user_id,
            'description': config.description,
            'domain': config.domain,
            'validation_rules': config.validation_rules,
            'ui_config': config.ui_config,
            'confidence_thresholds': config.confidence_thresholds,
            'extra_metadata': config.extra_metadata,
            'created_at': config.created_at,
            'updated_at': config.updated_at
        }
        hierarchy_cache.cache_hierarchy_config(config_id, config_dict)

    return config


def get_default_hierarchy_config(db: Session) -> Optional[HierarchyConfig]:
    """Get the default hierarchy configuration."""
    return db.query(HierarchyConfig).filter(HierarchyConfig.is_default == True).first()


def get_user_hierarchy_configs(db: Session, user_id: int) -> List[HierarchyConfig]:
    """Get all hierarchy configurations for a specific user."""
    return db.query(HierarchyConfig).filter(HierarchyConfig.user_id == user_id).all()


def create_hierarchy_config(db: Session, config_data: Dict[str, Any]) -> HierarchyConfig:
    """Create a new hierarchy configuration in the database."""
    # Set default values for new fields if not provided
    if 'validation_rules' not in config_data:
        levels = config_data.get('hierarchy_levels', [])
        config_data['validation_rules'] = {
            level: {
                'required': True,
                'max_length': 100,
                'pattern': r'^[a-zA-Z0-9\s\-_\.]+$',
                'min_length': 1
            } for level in levels
        }

    if 'ui_config' not in config_data:
        levels = config_data.get('hierarchy_levels', [])
        config_data['ui_config'] = {
            'display_names': {level: level.replace('_', ' ').title() for level in levels},
            'column_widths': {level: 150 for level in levels},
            'input_types': {level: 'text' for level in levels},
            'placeholders': {level: f"Enter {level.replace('_', ' ').lower()}" for level in levels}
        }

    if 'confidence_thresholds' not in config_data:
        levels = config_data.get('hierarchy_levels', [])
        config_data['confidence_thresholds'] = {level: 0.5 for level in levels}

    db_config = HierarchyConfig(**config_data)
    db.add(db_config)
    db.commit()
    db.refresh(db_config)
    return db_config


def update_hierarchy_config(db: Session, config_id: int, update_data: Dict[str, Any]) -> Optional[HierarchyConfig]:
    """Update an existing hierarchy configuration in the database."""
    db_config = db.query(HierarchyConfig).filter(HierarchyConfig.id == config_id).first()
    if not db_config:
        return None

    for key, value in update_data.items():
        setattr(db_config, key, value)

    db.commit()
    db.refresh(db_config)

    # Invalidate cache
    hierarchy_cache = get_hierarchy_cache()
    hierarchy_cache.invalidate_hierarchy_cache(config_id)

    return db_config


def delete_hierarchy_config(db: Session, config_id: int) -> bool:
    """Delete a hierarchy configuration from the database."""
    db_config = db.query(HierarchyConfig).filter(HierarchyConfig.id == config_id).first()
    if not db_config:
        return False

    try:
        db.delete(db_config)
        db.commit()

        # Invalidate cache
        hierarchy_cache = get_hierarchy_cache()
        hierarchy_cache.invalidate_hierarchy_cache(config_id)

        return True
    except Exception as e:
        db.rollback()
        logger.error(f"Error deleting hierarchy config ID {config_id}: {e}", exc_info=True)
        return False





# Database session dependency for FastAPI
def get_db():
    """Dependency to get a database session for FastAPI endpoints."""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


# Database initialization
def init_db():
    """Initialize the database connection and create tables if they don't exist."""
    try:
        Base.metadata.create_all(bind=engine)
        logger.info("Database tables created successfully")
    except Exception as e:
        logger.error(f"Error creating database tables: {e}", exc_info=True)
        raise
