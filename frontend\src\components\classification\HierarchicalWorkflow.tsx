/**
 * HierarchicalWorkflow.tsx
 *
 * Complete hierarchical classification workflow component
 */

import React, { useState, useEffect, useCallback, useRef } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  TreePine,
  Upload,
  Settings,
  Brain,
  Zap,
  BarChart3,
  Download,
  ArrowLeft,
  ArrowRight,
  CheckCircle2,
  AlertCircle,
  FileText,
  Target,
  Shield,
  FileSpreadsheet,
  Activity,
  ChevronLeft,
  Home,
  Database,
  Info,
  Loader2,
  Rocket
} from "lucide-react";
import { toast } from "@/hooks/use-toast";

// Import our new components
import { HierarchyLevelSelector } from "./HierarchyLevelSelector";
import { HierarchyTreeBuilder } from "./HierarchyTreeBuilder";
import { HierarchyConstraintEditor } from "./HierarchyConstraintEditor";
import { ValidationResultsDisplay } from "./ValidationResultsDisplay";
import { HierarchicalMetricsDisplay } from "./HierarchicalMetricsDisplay";
import { HierarchyVisualization } from "./HierarchyVisualization";
import { ConstraintViolationReporter } from "./ConstraintViolationReporter";
import { TrainingProgressMonitor } from "./TrainingProgressMonitor";
import { HierarchicalModelClassification } from "./HierarchicalModelClassification";
import { HierarchyValidationService, ComprehensiveValidationResult } from '@/services/hierarchyValidationService';
import type { ClassificationConfig } from "./HierarchicalModelClassification";
import { ColumnSelector } from "@/components/ColumnSelector";
import { ResultsDataTable } from "@/components/ResultsDataTable";
import { LLMConfigurationPanel } from "@/components/LLMConfigurationPanel";
import { UnifiedFileUploadZone } from "@/components/UnifiedFileUploadZone";
import { HierarchicalTrainingConfig, HierarchicalTrainingConfig as TrainingConfigType } from "./HierarchicalTrainingConfig";
import { HierarchicalModelManager, HierarchicalModel } from "./HierarchicalModelManager";
import { DeployStep } from "./DeployStep";

// Import services
import {
  startUniversalTraining,
  startUniversalInference,
  getUniversalTaskStatus,
  UniversalTrainingRequest,
  UniversalInferenceRequest
} from "@/services/universalApi";
import {
  startEnhancedHierarchicalTraining,
  EnhancedTrainingRequest
} from "@/services/hierarchicalModelApi";
import { getResultData } from "@/services/taskApi";
import { ClassificationResultRow } from "@/types";
import { unifiedDataManager, DataPurpose } from "@/services/unifiedDataManager";
import { downloadResultsCSV, downloadResultsExcel, downloadHierarchicalResults } from "@/services/exportApi";
import { HierarchyDetectionService, DetectedHierarchyLevel } from "@/services/hierarchyDetectionService";
import { getUserLicense, getLicenseFeatures } from "@/services/licenseApi";

interface HierarchicalWorkflowProps {
  initialData?: any;
  onComplete: (results: any) => void;
}

interface DualDataUpload {
  trainingData: {
    fileId: string;
    fileInfo: any;
    purpose: string;
  };
  classificationData: {
    fileId: string;
    fileInfo: any;
    purpose: string;
  };
  dualUpload: boolean;
}

export const HierarchicalWorkflow: React.FC<HierarchicalWorkflowProps> = ({
  initialData,
  onComplete
}) => {
  // Navigation hooks
  const navigate = useNavigate();
  const location = useLocation();

  // Check if we came from expert workflow
  const fromExpertWorkflow = location.state?.fromExpertWorkflow ||
                            sessionStorage.getItem('expertWorkflowDualData');

  // State management
  const [currentStep, setCurrentStep] = useState(1);
  const [isTraining, setIsTraining] = useState(false);
  const [trainingProgress, setTrainingProgress] = useState(0);
  const [errors, setErrors] = useState<string[]>([]);

  // Data state
  const [uploadedData, setUploadedData] = useState<any>(initialData);
  const [availableColumns, setAvailableColumns] = useState<string[]>([]);
  const [columnInfo, setColumnInfo] = useState<Record<string, any>>({});

  // Hierarchy configuration state
  const [hierarchyLevels, setHierarchyLevels] = useState<any[]>([]);
  const [hierarchyTree, setHierarchyTree] = useState<any[]>([]);
  const [constraints, setConstraints] = useState<any[]>([]);
  const [validationRules, setValidationRules] = useState<any[]>([]);
  const [detectedConstraints, setDetectedConstraints] = useState<Record<string, string[]>>({});
  const [constraintInfo, setConstraintInfo] = useState<any[]>([]);
  const [validationResults, setValidationResults] = useState<ComprehensiveValidationResult | null>(null);
  const [isValidating, setIsValidating] = useState(false);

  // Ref to prevent infinite validation loops
  const validationTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Column selection state
  const [selectedTextColumns, setSelectedTextColumns] = useState<string[]>([]);
  const [selectedLabelColumns, setSelectedLabelColumns] = useState<string[]>([]);

  // Training configuration state
  const [trainingMethod, setTrainingMethod] = useState<'llm' | 'custom' | 'model_classification'>('custom');
  const [llmProvider, setLlmProvider] = useState<string>('openai');

  // Enhanced training configuration state
  const [trainingConfig, setTrainingConfig] = useState<TrainingConfigType | null>(null);
  const [showTrainingConfig, setShowTrainingConfig] = useState(false);

  // Workflow state management
  const [workflowType, setWorkflowType] = useState<'training' | 'inference' | 'model_classification'>('training');

  // Centralized step management
  const getWorkflowSteps = () => {
    const baseSteps = [
      { id: 1, title: "Data Upload", icon: Upload, key: "upload" },
      { id: 2, title: "Hierarchy Setup", icon: TreePine, key: "hierarchy" },
      { id: 3, title: "Configuration", icon: Settings, key: "config" },
      { id: 4, title: "Method Selection", icon: Brain, key: "method" },
    ];

    switch (workflowType) {
      case 'inference':
        return [
          ...baseSteps,
          { id: 5, title: "Classification", icon: Zap, key: "classification" },
          { id: 6, title: "Results", icon: BarChart3, key: "results" },
          { id: 7, title: "Deploy", icon: Rocket, key: "deploy" }
        ];
      case 'training':
        return [
          ...baseSteps,
          { id: 5, title: "Training", icon: Zap, key: "training" },
          { id: 6, title: "Classification", icon: Target, key: "classification" },
          { id: 7, title: "Results", icon: BarChart3, key: "results" },
          { id: 8, title: "Deploy", icon: Rocket, key: "deploy" }
        ];
      case 'model_classification':
        return [
          ...baseSteps,
          { id: 5, title: "Results", icon: BarChart3, key: "results" },
          { id: 6, title: "Deploy", icon: Rocket, key: "deploy" }
        ];
      default:
        return baseSteps;
    }
  };

  const isCurrentStep = (stepKey: string) => {
    const steps = getWorkflowSteps();
    const step = steps.find(s => s.key === stepKey);
    return step ? currentStep === step.id : false;
  };

  const goToStep = (stepKey: string) => {
    const steps = getWorkflowSteps();
    const step = steps.find(s => s.key === stepKey);
    if (step) {
      setCurrentStep(step.id);
    }
  };
  const [showModelManager, setShowModelManager] = useState(false);
  const [selectedModel, setSelectedModel] = useState<HierarchicalModel | null>(null);
  const [userJourney, setUserJourney] = useState<'beginner' | 'expert'>('beginner');
  const [llmModel, setLlmModel] = useState<string>('gpt-3.5-turbo');
  const [customPrompt, setCustomPrompt] = useState<string>('');

  // Results state
  const [trainingTaskId, setTrainingTaskId] = useState<string>('');
  const [trainingResults, setTrainingResults] = useState<any>(null);
  const [metrics, setMetrics] = useState<any>(null);
  const [violations, setViolations] = useState<any[]>([]);
  const [resultsData, setResultsData] = useState<ClassificationResultRow[]>([]);

  // Classification state
  const [isClassifying, setIsClassifying] = useState(false);
  const [classificationProgress, setClassificationProgress] = useState(0);

  // Dual data and hierarchy detection state
  const [dualData, setDualData] = useState<DualDataUpload | null>(null);
  const [detectedHierarchy, setDetectedHierarchy] = useState<DetectedHierarchyLevel[]>([]);
  const [hierarchyDetectionResult, setHierarchyDetectionResult] = useState<any>(null);
  const [isDetectingHierarchy, setIsDetectingHierarchy] = useState(false);

  // Deployment state
  const [userLicense, setUserLicense] = useState<{
    type: 'personal' | 'professional' | 'enterprise';
    features: string[];
    limits: {
      max_deployments?: number;
      max_api_calls_per_month?: number;
      cloud_deployment?: boolean;
      enterprise_features?: boolean;
    };
  }>({
    type: 'personal',
    features: [],
    limits: {}
  });

  // Dynamic steps based on training method
  const getSteps = () => {
    return getWorkflowSteps().map(step => ({
      ...step,
      status: currentStep === step.id ? "current" : currentStep > step.id ? "complete" : "pending"
    }));
  };

  const steps = getSteps();

  // Initialize data from expert workflow or start fresh
  useEffect(() => {
    // Check for dual data from expert workflow
    const checkForDualData = () => {
      // Check session storage for dual data
      const storedDualData = sessionStorage.getItem('expertWorkflowDualData');
      if (storedDualData) {
        try {
          const parsedDualData = JSON.parse(storedDualData);
          setDualData(parsedDualData);
          console.log('Dual data detected from expert workflow:', parsedDualData);

          // Use training data for hierarchy detection
          if (parsedDualData.trainingData?.fileInfo) {
            setUploadedData(parsedDualData.trainingData.fileInfo);

            // Set up columns from training data
            const trainingFileInfo = parsedDualData.trainingData.fileInfo;
            console.log('Setting up columns from training data:', trainingFileInfo);
            if (trainingFileInfo.columns) {
              console.log('Training data columns:', trainingFileInfo.columns);
              setAvailableColumns(trainingFileInfo.columns);

              // Analyze column data from training data preview
              const trainingDataPreview = trainingFileInfo.preview || [];
              const columnInfo = analyzeColumnData(trainingDataPreview, trainingFileInfo.columns);
              setColumnInfo(columnInfo);
            }

            // Automatically detect hierarchy from training data
            detectHierarchyFromTrainingData(parsedDualData.trainingData.fileInfo);

            // Skip to step 2 since we have data and will auto-detect hierarchy
            setCurrentStep(2);
            return;
          }
        } catch (error) {
          console.error('Error parsing dual data:', error);
        }
      }
    };

    checkForDualData();

    // Only run fallback initialization if no dual data was found
    if (!sessionStorage.getItem('expertWorkflowDualData') && initialData) {
      setUploadedData(initialData);

      if (initialData.analysis?.column_analysis) {
        // Use detailed column analysis if available
        const columnAnalysis = initialData.analysis.column_analysis;
        setAvailableColumns(Object.keys(columnAnalysis));
        setColumnInfo(columnAnalysis);

        // If we have initial data, skip to step 2
        setCurrentStep(2);
      } else if (Array.isArray(initialData.columns)) {
        // If columns is an array of strings, convert to column info format
        setAvailableColumns(initialData.columns);
        // Analyze column data from initial data
        const initialDataPreview = initialData.data || [];
        const columnInfo = analyzeColumnData(initialDataPreview, initialData.columns);
        setColumnInfo(columnInfo);

        // If we have initial data, skip to step 2
        setCurrentStep(2);
      } else if (initialData.columns && typeof initialData.columns === 'object') {
        // If columns is already an object with detailed info
        setAvailableColumns(Object.keys(initialData.columns));
        setColumnInfo(initialData.columns);

        // If we have initial data, skip to step 2
        setCurrentStep(2);
      } else {
        setAvailableColumns([]);
        setColumnInfo({});
      }
    } else if (!sessionStorage.getItem('expertWorkflowDualData')) {
      // Only try unified data manager if no dual data
      // Try to get data from unified data manager
      const allFiles = unifiedDataManager.getAllFiles();
      if (allFiles.length > 0) {
        const latestFile = allFiles[0]; // Files are sorted by lastUsed descending
        const fileData = latestFile.fileInfo;
        setUploadedData(fileData);

        // Handle different column data formats for unified data manager
        if (Array.isArray(fileData.columns)) {
          setAvailableColumns(fileData.columns);
          // Analyze column data from file data
          const fileDataPreview = fileData.data || [];
          const columnInfo = analyzeColumnData(fileDataPreview, fileData.columns);
          setColumnInfo(columnInfo);
        } else if (fileData.columns && typeof fileData.columns === 'object') {
          setAvailableColumns(Object.keys(fileData.columns));
          setColumnInfo(fileData.columns);
        }

        // If we found data from unified data manager, skip to step 2
        setCurrentStep(2);
      } else {
        // No data available, start at step 1 for upload
        setCurrentStep(1);
      }
    }
  }, [initialData]);

  // Function to detect hierarchy from training data
  const detectHierarchyFromTrainingData = async (trainingFileInfo: any) => {
    setIsDetectingHierarchy(true);

    try {
      console.log('Detecting hierarchy from training data:', trainingFileInfo);

      const detectionResult = await HierarchyDetectionService.detectHierarchyFromTrainingData(
        trainingFileInfo,
        trainingFileInfo.preview
      );

      console.log('Hierarchy detection result:', detectionResult);

      setHierarchyDetectionResult(detectionResult);
      setDetectedHierarchy(detectionResult.levels);

      // Convert detected levels to the format expected by the workflow
      const hierarchyLevels = detectionResult.levels.map((level, index) => ({
        id: level.id,
        name: level.name,
        column: level.column,
        displayName: level.displayName,
        description: level.description,
        order: level.order
      }));

      console.log('Setting hierarchy levels from detection:', hierarchyLevels);
      setHierarchyLevels(hierarchyLevels);

      // Show success message
      toast({
        title: "Hierarchy Detected",
        description: `Automatically detected ${detectionResult.levels.length} hierarchy levels from training data with ${Math.round(detectionResult.confidence * 100)}% confidence.`
      });

    } catch (error) {
      console.error('Error detecting hierarchy:', error);
      toast({
        title: "Hierarchy Detection Failed",
        description: "Could not automatically detect hierarchy. Please configure manually.",
        variant: "destructive"
      });
    } finally {
      setIsDetectingHierarchy(false);
    }
  };

  // Memoized callbacks to prevent infinite loops
  const handleLevelsChange = useCallback((levels: any[]) => {
    console.log('HierarchicalWorkflow: Hierarchy levels changed:', levels);
    setHierarchyLevels(levels);
  }, []);

  // Handle column info updates from HierarchyLevelSelector
  const handleColumnInfoUpdate = useCallback((updatedColumnInfo: Record<string, any>) => {
    console.log('Column info updated:', updatedColumnInfo);
    setColumnInfo(updatedColumnInfo);
  }, []);

  // Helper function to analyze column data from training data
  const analyzeColumnData = useCallback((data: any[], columns: string[]) => {
    if (!data || data.length === 0) {
      return columns.reduce((acc: Record<string, any>, col: string) => {
        acc[col] = {
          name: col,
          type: 'string',
          unique_values: 0,
          total_values: 0,
          sample_values: []
        };
        return acc;
      }, {});
    }

    return columns.reduce((acc: Record<string, any>, col: string) => {
      const values = data
        .map(row => row[col])
        .filter(val => val !== null && val !== undefined && val !== '');

      const uniqueValues = [...new Set(values)];
      const sampleValues = uniqueValues.slice(0, 5);

      acc[col] = {
        name: col,
        type: 'string',
        unique_values: uniqueValues.length,
        total_values: values.length,
        sample_values: sampleValues
      };
      return acc;
    }, {});
  }, []);

  const handleConstraintsChange = useCallback((
    detectedConstraintsData: Record<string, string[]>,
    enhancedConstraintInfo?: any[]
  ) => {
    console.log('HierarchicalWorkflow: Constraints changed:', {
      detectedConstraintsData,
      enhancedConstraintInfo,
      constraintCount: Object.keys(detectedConstraintsData).length
    });

    // Store the detected constraints for the constraint editor
    setDetectedConstraints(detectedConstraintsData);

    // Store enhanced constraint information if available
    if (enhancedConstraintInfo) {
      setConstraintInfo(enhancedConstraintInfo);
    }

    // Convert detected constraints to constraint rules format with proper level detection
    const rules = Object.entries(detectedConstraintsData).map(([parent, children], index) => {
      let parentLevel = 0;
      let childLevel = 1;

      // Use enhanced constraint info if available for accurate level mapping
      if (enhancedConstraintInfo) {
        const constraintDetail = enhancedConstraintInfo.find(info => info.parentValue === parent);
        if (constraintDetail) {
          parentLevel = constraintDetail.parentLevel;
          childLevel = constraintDetail.childLevel;
        }
      } else {
        // Fallback: Check training data to determine which level the parent belongs to
        const trainingData = dualData?.trainingData?.fileInfo?.preview || uploadedData?.data || [];
        if (trainingData.length > 0 && hierarchyLevels.length > 0) {
          for (let i = 0; i < hierarchyLevels.length; i++) {
            const levelColumn = hierarchyLevels[i].column;
            const levelValues = trainingData.map(row => row[levelColumn]).filter(Boolean);
            if (levelValues.includes(parent)) {
              parentLevel = i;
              childLevel = Math.min(i + 1, hierarchyLevels.length - 1);
              break;
            }
          }
        }
      }

      return {
        id: `detected_${parent}_${index}`,
        parentLevel,
        parentValue: parent,
        childLevel,
        allowedChildren: children,
        isStrict: false,
        description: `Auto-detected: ${parent} (${hierarchyLevels[parentLevel]?.name || `Level ${parentLevel + 1}`} → ${hierarchyLevels[childLevel]?.name || `Level ${childLevel + 1}`})`
      };
    });

    // Store the generated rules for potential auto-application
    // The constraint editor will handle whether to apply them based on auto-detect setting

    // Note: Removed runValidation() call here to prevent infinite loop
    // Validation will be triggered by the useEffect that watches for constraint changes
  }, [hierarchyLevels, dualData, uploadedData]);

  const runValidation = useCallback(() => {
    // Clear any existing timeout
    if (validationTimeoutRef.current) {
      clearTimeout(validationTimeoutRef.current);
    }

    // Debounce validation to prevent infinite loops
    validationTimeoutRef.current = setTimeout(async () => {
      if (!uploadedData?.data || hierarchyLevels.length === 0 || validationRules.length === 0) {
        setValidationResults(null);
        return;
      }

      setIsValidating(true);
      try {
        // Use training data if available, otherwise use uploaded data
        const dataToValidate = dualData?.trainingData?.fileInfo?.preview || uploadedData.data;

        const results = HierarchyValidationService.validateHierarchyData(
          dataToValidate,
          hierarchyLevels,
          validationRules
        );

        setValidationResults(results);
      } catch (error) {
        console.error('Validation error:', error);
        toast({
          title: "Validation Error",
          description: "Failed to validate hierarchy data",
          variant: "destructive"
        });
      } finally {
        setIsValidating(false);
      }
    }, 300); // 300ms debounce
  }, [uploadedData, hierarchyLevels, validationRules, dualData]);

  // Run validation when relevant data changes (debounced)
  useEffect(() => {
    // Only run validation if we have the minimum required data
    if (uploadedData?.data && hierarchyLevels.length > 0 && validationRules.length > 0) {
      runValidation();
    }
  }, [uploadedData?.data, hierarchyLevels.length, validationRules.length, detectedConstraints]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (validationTimeoutRef.current) {
        clearTimeout(validationTimeoutRef.current);
      }
    };
  }, []);

  // Fetch user license information
  useEffect(() => {
    const fetchUserLicense = async () => {
      try {
        const [licenseInfo, features] = await Promise.all([
          getUserLicense(),
          getLicenseFeatures()
        ]);

        setUserLicense({
          type: licenseInfo.license_type as 'personal' | 'professional' | 'enterprise',
          features: Object.keys(features).filter(key => features[key as keyof typeof features] === true),
          limits: {
            max_deployments: features.max_models,
            max_api_calls_per_month: features.max_monthly_inferences,
            cloud_deployment: features.cloud_deployment,
            enterprise_features: features.enterprise_features
          }
        });
      } catch (error) {
        console.error('Failed to fetch user license:', error);
        // Fallback to personal license
        setUserLicense({
          type: 'personal',
          features: ['model_export'],
          limits: {
            max_deployments: 1,
            max_api_calls_per_month: 1000,
            cloud_deployment: false,
            enterprise_features: false
          }
        });
      }
    };

    fetchUserLicense();
  }, []);





  const validateStep = (step: number): boolean => {
    const newErrors: string[] = [];

    switch (step) {
      case 1:
        if (!uploadedData) {
          newErrors.push('No data uploaded');
        }
        break;
      case 2:
        console.log('Validating step 2, hierarchyLevels:', hierarchyLevels);
        if (hierarchyLevels.length < 2) {
          newErrors.push('At least 2 hierarchy levels are required');
        }
        break;
      case 3:
        if (selectedTextColumns.length === 0) {
          newErrors.push('At least one text column must be selected');
        }
        if (selectedLabelColumns.length === 0) {
          newErrors.push('At least one label column must be selected');
        }
        break;
      case 4:
        if (trainingMethod === 'llm' && !llmProvider) {
          newErrors.push('LLM provider must be selected');
        }
        if (trainingMethod === 'model_classification') {
          // For model classification, validation is handled within the HierarchicalModelClassification component
          // No additional validation needed here
        }
        break;
    }

    setErrors(newErrors);
    return newErrors.length === 0;
  };

  const handleNext = () => {
    if (validateStep(currentStep)) {
      if (currentStep < steps.length) {
        setCurrentStep(currentStep + 1);
      }
    } else {
      toast({
        title: "Validation Error",
        description: "Please fix the errors before proceeding",
        variant: "destructive"
      });
    }
  };

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  // Separate handlers for LLM inference and custom training
  const handleStartInference = async () => {
    // Check if we have dual data setup
    if (dualData) {
      // Dual data inference - use classification data for inference
      if (!dualData.classificationData?.fileInfo || selectedTextColumns.length === 0 || selectedLabelColumns.length === 0) {
        toast({
          title: "Configuration Error",
          description: "Please complete the configuration before starting inference with dual data",
          variant: "destructive"
        });
        return;
      }
    } else {
      // Single file inference
      if (!uploadedData || selectedTextColumns.length === 0 || selectedLabelColumns.length === 0) {
        toast({
          title: "Configuration Error",
          description: "Please complete the configuration before starting inference",
          variant: "destructive"
        });
        return;
      }
    }

    setIsTraining(true); // Reuse training state for inference UI
    setTrainingProgress(0);

    try {
      // For LLM inference, use classification data file (or main file if no dual data)
      const inferenceFileId = dualData ? dualData.classificationData.fileInfo.file_id : uploadedData.file_id;

      const inferenceRequest: UniversalTrainingRequest = {
        file_id: inferenceFileId,
        classification_type: 'hierarchical',
        model_type: 'llm',
        text_column: selectedTextColumns[0],
        label_columns: selectedLabelColumns,
        hierarchy_levels: hierarchyLevels.map(level => level.column),
        llm_provider: llmProvider,
        llm_model: llmModel,
        custom_prompt: customPrompt || undefined,
        training_params: {
          constraints: constraints,
          validation_rules: validationRules
        },
        // Add dual data information if available
        ...(dualData && {
          dual_data_setup: true,
          classification_file_id: dualData.classificationData.fileInfo.file_id,
          training_file_id: dualData.trainingData.fileInfo.file_id
        })
      };

      const response = await startUniversalTraining(inferenceRequest);
      setTrainingTaskId(response.task_id);

      // Start monitoring inference progress
      monitorInferenceProgress(response.task_id);

      toast({
        title: "Inference started",
        description: "Your data is being classified using LLM. This may take a few minutes."
      });
    } catch (error: any) {
      console.error('Inference error:', error);
      toast({
        title: "Inference failed",
        description: error.message || 'Failed to start inference',
        variant: "destructive"
      });
      setIsTraining(false);
    }
  };

  const handleStartTraining = async () => {
    // Check if we have dual data setup
    if (dualData) {
      // Dual data training - use training data for training
      if (!dualData.trainingData?.fileInfo || selectedTextColumns.length === 0 || selectedLabelColumns.length === 0) {
        toast({
          title: "Configuration Error",
          description: "Please complete the configuration before starting training with dual data",
          variant: "destructive"
        });
        return;
      }
    } else {
      // Single file training
      if (!uploadedData || selectedTextColumns.length === 0 || selectedLabelColumns.length === 0) {
        toast({
          title: "Configuration Error",
          description: "Please complete the configuration before starting training",
          variant: "destructive"
        });
        return;
      }
    }

    setIsTraining(true);
    setTrainingProgress(0);

    try {
      // For custom training, use training data file (or main file if no dual data)
      const trainingFileId = dualData ? dualData.trainingData.fileInfo.file_id : uploadedData.file_id;

      // Generate a default model name if not provided
      const defaultModelName = `h${Math.floor(Math.random() * 1000)}`;

      const trainingRequest: UniversalTrainingRequest = {
        file_id: trainingFileId,
        classification_type: 'hierarchical',
        model_type: 'custom',
        text_column: selectedTextColumns[0],
        label_columns: selectedLabelColumns,
        hierarchy_levels: hierarchyLevels.map(level => level.column),
        model_name: trainingConfig?.modelName || defaultModelName,
        training_params: {
          max_epochs: 3,
          batch_size: 16,
          learning_rate: 2e-5,
          constraints: constraints,
          validation_rules: validationRules
        },
        // Add dual data information if available
        ...(dualData && {
          dual_data_setup: true,
          classification_file_id: dualData.classificationData.fileInfo.file_id,
          training_file_id: dualData.trainingData.fileInfo.file_id
        })
      };

      const response = await startUniversalTraining(trainingRequest);
      setTrainingTaskId(response.task_id);

      // Start monitoring training progress
      monitorTrainingProgress(response.task_id);

      toast({
        title: "Training started",
        description: "Your hierarchical model is being trained. This may take several minutes."
      });
    } catch (error: any) {
      console.error('Training error:', error);
      toast({
        title: "Training failed",
        description: error.message || 'Failed to start training',
        variant: "destructive"
      });
      setIsTraining(false);
    }
  };

  // Enhanced training with configuration
  const handleEnhancedTraining = async () => {
    if (!trainingConfig) {
      toast({
        title: "Configuration Required",
        description: "Please configure your training parameters first.",
        variant: "destructive"
      });
      return;
    }

    if (!uploadedData?.file_id) {
      toast({
        title: "No data uploaded",
        description: "Please upload your data first.",
        variant: "destructive"
      });
      return;
    }

    setIsTraining(true);
    setTrainingProgress(0);

    try {
      const trainingFileId = dualData ? dualData.trainingData.fileInfo.file_id : uploadedData.file_id;

      const enhancedRequest: EnhancedTrainingRequest = {
        model_name: trainingConfig.modelName,
        description: `Hierarchical model trained on ${new Date().toLocaleDateString()}`,
        file_id: trainingFileId,
        classification_type: 'hierarchical',
        model_type: 'custom',
        text_column: selectedTextColumns[0],
        label_columns: selectedLabelColumns,
        hierarchy_levels: hierarchyLevels.map(level => level.column),
        training_params: {
          max_epochs: trainingConfig.numEpochs,
          batch_size: trainingConfig.batchSize,
          learning_rate: trainingConfig.learningRate,
          validation_split: trainingConfig.validationSplit,
          base_model: trainingConfig.baseModel,
          max_length: trainingConfig.maxLength,
          warmup_steps: trainingConfig.warmupSteps,
          weight_decay: trainingConfig.weightDecay,
          gradient_accumulation_steps: trainingConfig.gradientAccumulationSteps,
          use_unsloth: trainingConfig.useUnsloth,
          fp16: trainingConfig.fp16,
          gradient_checkpointing: trainingConfig.gradientCheckpointing,
          enable_early_stopping: trainingConfig.enableEarlyStopping,
          patience: trainingConfig.patience,
          min_delta: trainingConfig.minDelta,
          hierarchy_weights: trainingConfig.hierarchyWeights,
          constraint_enforcement: trainingConfig.constraintEnforcement,
          level_wise_training: trainingConfig.levelWiseTraining,
          constraints: constraints,
          validation_rules: validationRules
        },
        ...(dualData && {
          dual_data_setup: true,
          classification_file_id: dualData.classificationData.fileInfo.file_id,
          training_file_id: dualData.trainingData.fileInfo.file_id
        })
      };

      const response = await startEnhancedHierarchicalTraining(enhancedRequest);
      setTrainingTaskId(response.task_id);

      monitorTrainingProgress(response.task_id);

      toast({
        title: "Enhanced Training Started",
        description: `Training model "${trainingConfig.modelName}" with custom configuration.`
      });
    } catch (error: any) {
      console.error('Enhanced training error:', error);
      toast({
        title: "Training failed",
        description: error.message || 'Failed to start enhanced training',
        variant: "destructive"
      });
      setIsTraining(false);
    }
  };

  // Model management handlers
  const handleTrainingConfigChange = (config: TrainingConfigType) => {
    setTrainingConfig(config);
  };

  const handleTrainingConfigSave = (config: TrainingConfigType) => {
    setTrainingConfig(config);
    toast({
      title: "Configuration Saved",
      description: "Training configuration has been saved successfully."
    });
  };

  const handleModelSelect = (model: HierarchicalModel) => {
    setSelectedModel(model);
  };

  const handleModelUse = (modelId: string) => {
    // Logic to use the selected model for inference
    toast({
      title: "Model Selected",
      description: "Model is ready for classification tasks."
    });
  };

  const handleModelDelete = (modelId: string) => {
    // Logic to delete the model
    toast({
      title: "Model Deleted",
      description: "Model has been removed successfully."
    });
  };

  const monitorInferenceProgress = async (taskId: string) => {
    const checkProgress = async () => {
      try {
        const status = await getUniversalTaskStatus(taskId);
        console.log('Inference status update:', status); // Debug log

        if (status.progress !== undefined) {
          setTrainingProgress(status.progress);
        }

        if (status.status === 'SUCCESS' || status.status === 'completed') {
          setIsTraining(false);
          setTrainingResults(status.result);
          setMetrics(status.result?.metrics);
          setViolations(status.result?.constraint_violations || []);

          // Load results data for the table
          try {
            const data = await getResultData(taskId);
            // Ensure data is an array
            if (Array.isArray(data)) {
              setResultsData(data);
            } else if (data && typeof data === 'object' && data.results && Array.isArray(data.results)) {
              // Handle case where data is wrapped in an object with results property
              setResultsData(data.results);
            } else {
              console.warn('Results data is not in expected array format:', data);
              setResultsData([]);
            }
          } catch (error) {
            console.error('Error loading results data:', error);
            setResultsData([]);
          }

          // For LLM inference, move to results step
          goToStep('results');

          toast({
            title: "Inference completed",
            description: "Your data has been classified successfully using LLM!"
          });
        } else if (status.status === 'FAILED' || status.status === 'failed') {
          setIsTraining(false);
          toast({
            title: "Inference failed",
            description: status.error || 'Inference failed with unknown error',
            variant: "destructive"
          });
        } else {
          // Continue monitoring
          setTimeout(checkProgress, 2000);
        }
      } catch (error) {
        console.error('Error checking inference status:', error);
        setTimeout(checkProgress, 5000); // Retry after longer delay
      }
    };

    checkProgress();
  };

  const monitorTrainingProgress = async (taskId: string) => {
    const checkProgress = async () => {
      try {
        const status = await getUniversalTaskStatus(taskId);
        console.log('Training status update:', status); // Debug log

        if (status.progress !== undefined) {
          setTrainingProgress(status.progress);
        }

        if (status.status === 'SUCCESS' || status.status === 'completed') {
          setIsTraining(false);
          setTrainingResults(status.result);
          setMetrics(status.result?.metrics);
          setViolations(status.result?.constraint_violations || []);

          // Check if this is a dual data setup
          if (dualData) {
            // For dual data setup, classification should be done automatically
            // Try to load the classification results and move to results step
            try {
              const data = await getResultData(taskId);
              if (Array.isArray(data) && data.length > 0) {
                setResultsData(data);
                goToStep('results'); // Move directly to results
                toast({
                  title: "Training and Classification completed",
                  description: "Your model has been trained and applied to the classification data successfully!"
                });
              } else {
                // No results yet, move to model classification step
                goToStep('classification');
                toast({
                  title: "Training completed",
                  description: "Your hierarchical model has been trained successfully! Proceed to model classification."
                });
              }
            } catch (error) {
              console.error('Error loading classification results:', error);
              // Fall back to model classification step
              goToStep('classification');
              toast({
                title: "Training completed",
                description: "Your hierarchical model has been trained successfully! Proceed to model classification."
              });
            }
          } else {
            // For single data setup, move to model classification step
            goToStep('classification');
            toast({
              title: "Training completed",
              description: "Your hierarchical model has been trained successfully! Proceed to model classification."
            });
          }
        } else if (status.status === 'FAILED' || status.status === 'failed') {
          setIsTraining(false);
          toast({
            title: "Training failed",
            description: status.error || 'Training failed with unknown error',
            variant: "destructive"
          });
        } else {
          // Continue monitoring
          setTimeout(checkProgress, 2000);
        }
      } catch (error) {
        console.error('Error checking training status:', error);
        setTimeout(checkProgress, 5000); // Retry after longer delay
      }
    };

    checkProgress();
  };

  const handleStartModelClassification = async (config: any) => {
    if (!config.selectedModelId) {
      toast({
        title: "Model Not Selected",
        description: "Please select a model for classification",
        variant: "destructive"
      });
      return;
    }

    setIsTraining(true);
    setTrainingProgress(0);
    setWorkflowType('model_classification');

    try {
      // Determine the file ID based on data source selection
      let classificationFileId;
      if (config.useTrainingData) {
        classificationFileId = dualData ? dualData.trainingData.fileInfo.file_id : uploadedData.file_id;
      } else {
        classificationFileId = config.classificationFileId || (dualData ? dualData.classificationData.fileInfo.file_id : uploadedData.file_id);
      }

      // Create inference request using the selected model
      const inferenceRequest = {
        model_id: config.selectedModelId,
        file_id: classificationFileId,
        text_column: config.textColumn,
        classification_type: 'hierarchical',
        batch_size: config.batchSize || 32,
        confidence_threshold: config.confidenceThreshold || 0.5,
        include_confidence_scores: config.includeConfidenceScores || true,
        include_probabilities: config.includeProbabilities || false
      };

      const results = await startUniversalInference(inferenceRequest);

      // Handle the results properly using the same logic as handleClassifyData
      handleClassificationComplete(results);
      goToStep('results'); // Move to results step
      setIsTraining(false);

      toast({
        title: "Classification completed",
        description: `Successfully classified ${results.summary?.total_predictions || 'all'} texts using the selected model`
      });
    } catch (error: any) {
      console.error('Model classification error:', error);
      toast({
        title: "Classification failed",
        description: error.message || 'Failed to classify data with selected model',
        variant: "destructive"
      });
      setIsTraining(false);
    }
  };

  const handleStartClassification = async (config: ClassificationConfig) => {
    if (!config.selectedModelId) {
      toast({
        title: "Model Not Selected",
        description: "Please select a model for classification",
        variant: "destructive"
      });
      return;
    }

    setIsClassifying(true);
    setClassificationProgress(0);

    try {
      toast({
        title: "Classification started",
        description: "Starting to classify your data with the selected model..."
      });

      // Determine the file ID based on data source selection
      let classificationFileId;
      if (config.useTrainingData) {
        classificationFileId = dualData ? dualData.trainingData.fileInfo.file_id : uploadedData.file_id;
      } else {
        classificationFileId = config.classificationFileId || (dualData ? dualData.classificationData.fileInfo.file_id : uploadedData.file_id);
      }

      // Create inference request using the selected model
      const inferenceRequest = {
        model_id: config.selectedModelId,
        file_id: classificationFileId,
        text_column: config.textColumn,
        classification_type: 'hierarchical',
        config: {
          batch_size: config.batchSize || 32,
          confidence_threshold: config.confidenceThreshold || 0.5,
          include_confidence_scores: config.includeConfidenceScores || true,
          include_probabilities: config.includeProbabilities || false,
          output_format: config.outputFormat || 'csv'
        }
      };

      console.log('Starting classification with request:', inferenceRequest);

      const results = await startUniversalInference(inferenceRequest);

      console.log('Classification results received:', results);

      // Handle the results properly
      handleClassificationComplete(results);
      setIsClassifying(false);
      setClassificationProgress(100);

      toast({
        title: "Classification completed",
        description: `Successfully classified ${results.summary?.total_predictions || 'all'} texts using the selected model`
      });

    } catch (error: any) {
      console.error('Classification error:', error);
      setIsClassifying(false);
      setClassificationProgress(0);
      toast({
        title: "Classification failed",
        description: error.message || 'Failed to classify data',
        variant: "destructive"
      });
    }
  };

  const handleClassificationComplete = (results: any) => {
    console.log('Processing classification results:', results);
    console.log('Results type:', typeof results);
    console.log('Results keys:', results ? Object.keys(results) : 'null');

    // Handle the API response format from universal inference
    if (results && typeof results === 'object') {
      if (results.results && Array.isArray(results.results)) {
        // Standard API response format: { results: [...], summary: {...} }
        setResultsData(results.results);
        console.log('Set results data from results.results:', results.results.length, 'items');
        console.log('Sample result item:', results.results[0]);
      } else if (Array.isArray(results)) {
        // Direct array format
        setResultsData(results);
        console.log('Set results data from direct array:', results.length, 'items');
        console.log('Sample result item:', results[0]);
      } else if (results.predictions && Array.isArray(results.predictions)) {
        // Alternative format with predictions array
        setResultsData(results.predictions);
        console.log('Set results data from predictions:', results.predictions.length, 'items');
        console.log('Sample result item:', results.predictions[0]);
      } else {
        console.warn('Classification results not in expected format:', results);
        console.warn('Available keys:', Object.keys(results));
        setResultsData([]);
      }
    } else {
      console.warn('Classification results not in expected object format:', results);
      setResultsData([]);
    }

    goToStep('results'); // Move to results step
    toast({
      title: "Classification completed",
      description: "Your data has been classified successfully!"
    });
  };

  const handleExportResults = async (format: 'csv' | 'excel' | 'json') => {
    if (!resultsData || resultsData.length === 0) {
      toast({
        title: "No results to export",
        description: "Please complete classification first",
        variant: "destructive"
      });
      return;
    }

    try {
      if (format === 'json') {
        // Export results data directly as JSON
        const exportData = {
          results: resultsData,
          metadata: {
            total_results: resultsData.length,
            export_timestamp: new Date().toISOString(),
            classification_type: 'hierarchical',
            filename: uploadedData?.filename || dualData?.classificationData?.fileInfo?.filename || 'unknown'
          }
        };

        const jsonString = JSON.stringify(exportData, null, 2);
        const blob = new Blob([jsonString], { type: 'application/json' });
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `hierarchical_results_${Date.now()}.json`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);

        toast({
          title: "Export successful",
          description: "Results exported as JSON file"
        });
      } else if (format === 'csv') {
        // Convert results data to CSV
        const csvContent = convertToCSV(resultsData);
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `hierarchical_results_${Date.now()}.csv`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);

        toast({
          title: "Export successful",
          description: "Results exported as CSV file"
        });
      } else if (format === 'excel') {
        // For Excel, we'll use CSV format for now (can be enhanced later with a proper Excel library)
        const csvContent = convertToCSV(resultsData);
        const blob = new Blob([csvContent], { type: 'application/vnd.ms-excel;charset=utf-8;' });
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `hierarchical_results_${Date.now()}.xlsx`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);

        toast({
          title: "Export successful",
          description: "Results exported as Excel file"
        });
      }
    } catch (error: any) {
      console.error('Export error:', error);
      toast({
        title: "Export failed",
        description: error.message || `Failed to export results as ${format}`,
        variant: "destructive"
      });
    }
  };

  // Helper function to convert results data to CSV
  const convertToCSV = (data: any[]): string => {
    if (!data || data.length === 0) return '';

    // Get all unique column names
    const allColumns = new Set<string>();
    data.forEach(row => {
      Object.keys(row).forEach(key => allColumns.add(key));
    });

    const columns = Array.from(allColumns);

    // Create CSV header
    const header = columns.map(col => `"${col}"`).join(',');

    // Create CSV rows
    const rows = data.map(row => {
      return columns.map(col => {
        const value = row[col];
        if (value === null || value === undefined) {
          return '""';
        }
        if (typeof value === 'object') {
          return `"${JSON.stringify(value).replace(/"/g, '""')}"`;
        }
        return `"${String(value).replace(/"/g, '""')}"`;
      }).join(',');
    });

    return [header, ...rows].join('\n');
  };

  const handleComplete = () => {
    onComplete({
      type: 'hierarchical',
      taskId: trainingTaskId,
      results: trainingResults,
      metrics: metrics,
      violations: violations
    });
  };

  // Handle navigation back to expert workflow
  const handleBackToExpert = () => {
    // Clear any stored dual data
    sessionStorage.removeItem('expertWorkflowDualData');

    // Navigate back to expert workflow
    navigate('/expert', {
      state: {
        returnedFromHierarchical: true,
        hierarchicalData: {
          currentStep,
          hierarchyLevels,
          trainingResults
        }
      }
    });
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Navigation Bar */}
      {fromExpertWorkflow && (
        <div className="border-b border-border bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
          <div className="container mx-auto px-4 py-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleBackToExpert}
                  className="flex items-center gap-2 text-muted-foreground hover:text-foreground"
                >
                  <ChevronLeft className="w-4 h-4" />
                  Back to Expert Workflow
                </Button>
                <div className="h-4 w-px bg-border" />
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <Home className="w-4 h-4" />
                  <span>Expert Workflow</span>
                  <ChevronLeft className="w-3 h-3 rotate-180" />
                  <span className="text-foreground font-medium">Hierarchical Classification</span>
                </div>
              </div>
              <Badge variant="outline" className="text-xs">
                Step {currentStep} of {steps.length}
              </Badge>
            </div>
          </div>
        </div>
      )}

      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center gap-3 mb-4">
            <div className="w-12 h-12 rounded-lg bg-ml-primary/10 flex items-center justify-center">
              <TreePine className="w-6 h-6 text-ml-primary" />
            </div>
            <div>
              <h1 className="text-3xl font-bold">Hierarchical Classification</h1>
              <p className="text-muted-foreground">Multi-level tree-based classification</p>
            </div>
            <Badge className="bg-ml-primary/10 text-ml-primary ml-auto">
              Advanced
            </Badge>
          </div>

          {/* Progress */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Progress</span>
              <span className="text-sm text-muted-foreground">Step {currentStep} of {steps.length}</span>
            </div>
            <Progress value={(currentStep / steps.length) * 100} className="h-2" />
          </div>

          {/* Errors */}
          {errors.length > 0 && (
            <Alert variant="destructive" className="mt-4">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                <ul className="list-disc list-inside">
                  {errors.map((error, index) => (
                    <li key={index}>{error}</li>
                  ))}
                </ul>
              </AlertDescription>
            </Alert>
          )}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Sidebar - Steps */}
          <div className="lg:col-span-1">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Workflow Steps</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {steps.map((step) => {
                    const Icon = step.icon;
                    const isActive = currentStep === step.id;
                    const isComplete = step.status === 'complete';

                    return (
                      <div
                        key={step.id}
                        className={`flex items-center gap-3 p-3 rounded-lg transition-colors cursor-pointer ${
                          isActive ? 'bg-primary/10 border border-primary/20' :
                          isComplete ? 'bg-green-50 border border-green-200' : 'bg-muted/30'
                        }`}
                        onClick={() => {
                          if (isComplete || step.id <= currentStep) {
                            setCurrentStep(step.id);
                          }
                        }}
                      >
                        <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                          isActive ? 'bg-primary text-white' :
                          isComplete ? 'bg-green-600 text-white' : 'bg-muted'
                        }`}>
                          {isComplete ? (
                            <CheckCircle2 className="w-4 h-4" />
                          ) : (
                            <Icon className="w-4 h-4" />
                          )}
                        </div>
                        <div>
                          <div className={`font-medium text-sm ${
                            isActive ? 'text-primary' :
                            isComplete ? 'text-green-700' : 'text-muted-foreground'
                          }`}>
                            {step.title}
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Main Content */}
          <div className="lg:col-span-3">
            {/* Step 1: Data Upload */}
            {currentStep === 1 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Upload className="w-5 h-5" />
                    {dualData
                      ? 'Dual Data Setup Complete'
                      : uploadedData
                        ? 'Data Upload Complete'
                        : 'Upload Classification Data'
                    }
                  </CardTitle>
                  <CardDescription>
                    {dualData
                      ? 'Training and classification data have been loaded from the Expert Workflow. The training data will be used to configure the hierarchy and train the model.'
                      : uploadedData
                        ? 'Your data has been successfully uploaded and is ready for hierarchical classification.'
                        : 'Upload a CSV or Excel file containing the text data you want to classify hierarchically.'
                    }
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {dualData ? (
                    // Show dual data information
                    <div className="space-y-4">
                      <Alert>
                        <CheckCircle2 className="h-4 w-4" />
                        <AlertDescription>
                          <strong>Dual Data Setup Detected</strong>
                          <div className="mt-2 space-y-1">
                            <div><strong>Training Data:</strong> {dualData.trainingData.fileInfo.filename || 'Training file'} ({dualData.trainingData.fileInfo.num_rows || 0} rows)</div>
                            <div><strong>Classification Data:</strong> {dualData.classificationData.fileInfo.filename || 'Classification file'} ({dualData.classificationData.fileInfo.num_rows || 0} rows)</div>
                          </div>
                        </AlertDescription>
                      </Alert>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <Card>
                          <CardHeader className="pb-3">
                            <CardTitle className="text-lg">Training Data</CardTitle>
                            <CardDescription>Used for hierarchy detection and model training</CardDescription>
                          </CardHeader>
                          <CardContent>
                            <div className="space-y-2">
                              <div className="flex justify-between">
                                <span className="text-sm text-muted-foreground">Rows:</span>
                                <span className="font-medium">{dualData.trainingData.fileInfo.num_rows || 0}</span>
                              </div>
                              <div className="flex justify-between">
                                <span className="text-sm text-muted-foreground">Columns:</span>
                                <span className="font-medium">{dualData.trainingData.fileInfo.columns?.length || 0}</span>
                              </div>
                              {dualData.trainingData.fileInfo.columns && (
                                <div className="mt-3">
                                  <div className="text-sm text-muted-foreground mb-2">Available Columns:</div>
                                  <div className="flex flex-wrap gap-1">
                                    {dualData.trainingData.fileInfo.columns.slice(0, 6).map((col: string) => (
                                      <Badge key={col} variant="secondary" className="text-xs">{col}</Badge>
                                    ))}
                                    {dualData.trainingData.fileInfo.columns.length > 6 && (
                                      <Badge variant="outline" className="text-xs">+{dualData.trainingData.fileInfo.columns.length - 6} more</Badge>
                                    )}
                                  </div>
                                </div>
                              )}
                            </div>
                          </CardContent>
                        </Card>

                        <Card>
                          <CardHeader className="pb-3">
                            <CardTitle className="text-lg">Classification Data</CardTitle>
                            <CardDescription>Data to be classified using the trained model</CardDescription>
                          </CardHeader>
                          <CardContent>
                            <div className="space-y-2">
                              <div className="flex justify-between">
                                <span className="text-sm text-muted-foreground">Rows:</span>
                                <span className="font-medium">{dualData.classificationData.fileInfo.num_rows || 0}</span>
                              </div>
                              <div className="flex justify-between">
                                <span className="text-sm text-muted-foreground">Columns:</span>
                                <span className="font-medium">{dualData.classificationData.fileInfo.columns?.length || 0}</span>
                              </div>
                              {dualData.classificationData.fileInfo.columns && (
                                <div className="mt-3">
                                  <div className="text-sm text-muted-foreground mb-2">Available Columns:</div>
                                  <div className="flex flex-wrap gap-1">
                                    {dualData.classificationData.fileInfo.columns.slice(0, 6).map((col: string) => (
                                      <Badge key={col} variant="outline" className="text-xs">{col}</Badge>
                                    ))}
                                    {dualData.classificationData.fileInfo.columns.length > 6 && (
                                      <Badge variant="outline" className="text-xs">+{dualData.classificationData.fileInfo.columns.length - 6} more</Badge>
                                    )}
                                  </div>
                                </div>
                              )}
                            </div>
                          </CardContent>
                        </Card>
                      </div>
                    </div>
                  ) : uploadedData ? (
                    <div className="space-y-4">
                      <Alert>
                        <CheckCircle2 className="h-4 w-4" />
                        <AlertDescription>
                          Data has been successfully uploaded and analyzed.
                        </AlertDescription>
                      </Alert>

                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                        <div className="text-center p-3 bg-muted/30 rounded">
                          <div className="text-2xl font-bold">{uploadedData.row_count || uploadedData.num_rows || 0}</div>
                          <div className="text-sm text-muted-foreground">Rows</div>
                        </div>
                        <div className="text-center p-3 bg-muted/30 rounded">
                          <div className="text-2xl font-bold">{availableColumns.length}</div>
                          <div className="text-sm text-muted-foreground">Columns</div>
                        </div>
                        <div className="text-center p-3 bg-muted/30 rounded">
                          <div className="text-2xl font-bold">{uploadedData.file_size || 'N/A'}</div>
                          <div className="text-sm text-muted-foreground">File Size</div>
                        </div>
                        <div className="text-center p-3 bg-muted/30 rounded">
                          <div className="text-2xl font-bold">{uploadedData.file_type || uploadedData.filename?.split('.').pop()?.toUpperCase() || 'CSV'}</div>
                          <div className="text-sm text-muted-foreground">Format</div>
                        </div>
                      </div>

                      {/* Show column preview */}
                      {availableColumns.length > 0 && (
                        <div className="space-y-2">
                          <h4 className="font-semibold">Available Columns:</h4>
                          <div className="flex flex-wrap gap-2">
                            {availableColumns.map((column) => (
                              <Badge key={column} variant="outline">
                                {column}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  ) : (
                    <div className="space-y-4">
                      <Alert>
                        <Upload className="h-4 w-4" />
                        <AlertDescription>
                          Upload your data file to begin hierarchical classification. Supported formats: CSV, Excel (.xlsx, .xls)
                        </AlertDescription>
                      </Alert>

                      <UnifiedFileUploadZone
                        onFileSelected={(fileId, fileInfo, purposes) => {
                          // Only allow file upload if no dual data is present
                          if (dualData) {
                            toast({
                              title: "Upload Not Allowed",
                              description: "Data has already been loaded from the Expert Workflow. Please use the provided data.",
                              variant: "destructive"
                            });
                            return;
                          }

                          console.log('File selected:', fileInfo);
                          setUploadedData(fileInfo);

                          // Set available columns
                          if (fileInfo.columns) {
                            setAvailableColumns(fileInfo.columns);

                            // Analyze column data from file preview
                            const fileDataPreview = fileInfo.preview || [];
                            const columnInfo = analyzeColumnData(fileDataPreview, fileInfo.columns);
                            setColumnInfo(columnInfo);
                          }

                          toast({
                            title: "File uploaded successfully",
                            description: `${fileInfo.filename} has been uploaded with ${fileInfo.num_rows} rows and ${fileInfo.columns?.length} columns.`
                          });
                        }}
                        acceptedTypes={['.csv', '.xlsx', '.xls']}
                        maxSize={50 * 1024 * 1024} // 50MB
                        title="Upload Classification Data"
                        description="Upload a CSV or Excel file containing the text data you want to classify hierarchically."
                        requiredPurposes={['classification']}
                        showFileReuse={true}
                        showContinueButton={false}
                      />
                    </div>
                  )}
                </CardContent>
              </Card>
            )}

            {/* Step 2: Hierarchy Setup */}
            {currentStep === 2 && (
              <div className="space-y-6">
                {/* Hierarchy Level Configuration */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <TreePine className="w-5 h-5 text-primary" />
                      Hierarchy Level Configuration
                    </CardTitle>
                    <CardDescription>
                      {hierarchyLevels.length > 0 && hierarchyDetectionResult
                        ? `Automatically detected ${hierarchyLevels.length} hierarchy levels from your training data (${Math.round(hierarchyDetectionResult.confidence * 100)}% confidence). Review and modify as needed.`
                        : hierarchyLevels.length > 0
                        ? "Review the hierarchy levels below. You can modify names, reorder levels, or add/remove levels as needed."
                        : "Configure your hierarchy levels by selecting columns and defining the structure."
                      }
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    {/* Show detection status and warnings/recommendations if available */}
                    {hierarchyDetectionResult && (
                      <div className="mb-6 space-y-3">
                        {/* Detection Status */}
                        {isDetectingHierarchy ? (
                          <div className="flex items-center gap-2 p-3 bg-blue-50 rounded-lg border border-blue-200">
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                            <span className="text-sm">Analyzing training data for hierarchy patterns...</span>
                          </div>
                        ) : (
                          <div className="flex items-center gap-2 p-3 bg-green-50 rounded-lg border border-green-200">
                            <CheckCircle2 className="w-4 h-4 text-green-600" />
                            <span className="text-sm font-medium text-green-800">
                              Successfully detected {hierarchyDetectionResult.levels.length} hierarchy levels from training data
                            </span>
                          </div>
                        )}

                        {/* Recommendations */}
                        {hierarchyDetectionResult.recommendations.length > 0 && (
                          <Alert>
                            <Info className="h-4 w-4" />
                            <AlertDescription>
                              <strong>Recommendations:</strong>
                              <ul className="list-disc list-inside mt-1">
                                {hierarchyDetectionResult.recommendations.map((rec: string, index: number) => (
                                  <li key={index} className="text-sm">{rec}</li>
                                ))}
                              </ul>
                            </AlertDescription>
                          </Alert>
                        )}

                        {/* Warnings */}
                        {hierarchyDetectionResult.warnings.length > 0 && (
                          <Alert variant="destructive">
                            <AlertCircle className="h-4 w-4" />
                            <AlertDescription>
                              <strong>Warnings:</strong>
                              <ul className="list-disc list-inside mt-1">
                                {hierarchyDetectionResult.warnings.map((warning: string, index: number) => (
                                  <li key={index} className="text-sm">{warning}</li>
                                ))}
                              </ul>
                            </AlertDescription>
                          </Alert>
                        )}
                      </div>
                    )}
                    <HierarchyLevelSelector
                      key={`hierarchy-selector-${hierarchyLevels.length}-${hierarchyLevels.map(l => l.id).join('-')}`}
                      availableColumns={availableColumns}
                      columnInfo={columnInfo}
                      onLevelsChange={handleLevelsChange}
                      initialLevels={hierarchyLevels}
                      trainingData={dualData?.trainingData?.fileInfo?.preview || uploadedData?.data || []}
                      onColumnInfoUpdate={handleColumnInfoUpdate}
                    />
                  </CardContent>
                </Card>

                {/* Debug info for tree builder */}
                {process.env.NODE_ENV === 'development' && (
                  <div className="p-2 bg-yellow-100 text-xs border border-yellow-300 rounded">
                    <strong>Tree Builder Debug:</strong><br/>
                    <strong>Hierarchy levels count:</strong> {hierarchyLevels.length}<br/>
                    <strong>Uploaded data exists:</strong> {uploadedData?.data ? 'Yes' : 'No'}<br/>
                    <strong>Uploaded data length:</strong> {uploadedData?.data?.length || 0}<br/>
                    <strong>Dual data exists:</strong> {dualData?.trainingData?.fileInfo?.preview ? 'Yes' : 'No'}<br/>
                    <strong>Dual data length:</strong> {dualData?.trainingData?.fileInfo?.preview?.length || 0}<br/>
                    <strong>Detected constraints count:</strong> {Object.keys(detectedConstraints).length}<br/>
                    <strong>Hierarchy levels:</strong> {JSON.stringify(hierarchyLevels.map(l => ({ name: l.name, column: l.column, order: l.order })))}<br/>
                  </div>
                )}

                {(() => {
                  // Use training data if available (dual data setup), otherwise use uploaded data
                  const dataToUse = dualData?.trainingData?.fileInfo?.preview || uploadedData?.data;
                  const hasData = dataToUse && dataToUse.length > 0;

                  return hierarchyLevels.length > 0 && hasData && (
                    <HierarchyTreeBuilder
                      data={dataToUse}
                      hierarchyLevels={hierarchyLevels}
                      onTreeChange={setHierarchyTree}
                      onConstraintsChange={handleConstraintsChange}
                    />
                  );
                })()}

                {/* Hierarchy Constraints Editor - appears after hierarchy levels are configured */}
                {(() => {
                  // Use training data if available (dual data setup), otherwise use uploaded data
                  const dataToUse = dualData?.trainingData?.fileInfo?.preview || uploadedData?.data;
                  const hasData = dataToUse && dataToUse.length > 0;

                  return hierarchyLevels.length > 0 && hasData && (
                    <HierarchyConstraintEditor
                      hierarchyLevels={hierarchyLevels}
                      detectedConstraints={detectedConstraints}
                      constraintInfo={constraintInfo}
                      onConstraintsChange={setConstraints}
                      onValidationRulesChange={setValidationRules}
                      initialConstraints={constraints}
                      initialValidationRules={validationRules}
                    />
                  );
                })()}

                {/* Validation Results Display - show validation results in Step 2 */}
                {(() => {
                  // Use training data if available (dual data setup), otherwise use uploaded data
                  const dataToUse = dualData?.trainingData?.fileInfo?.preview || uploadedData?.data;
                  const hasData = dataToUse && dataToUse.length > 0;

                  return hierarchyLevels.length > 0 && hasData && (
                    <ValidationResultsDisplay
                      validationResults={validationResults}
                      isLoading={isValidating}
                    />
                  );
                })()}
              </div>
            )}





            {/* Step 3: Configuration */}
            {currentStep === 3 && (
              <div className="space-y-6">
                {/* Debug info */}
                {process.env.NODE_ENV === 'development' && (
                  <div className="p-2 bg-gray-100 text-xs">
                    <strong>Debug:</strong> Available columns: {availableColumns.join(', ')}<br/>
                    <strong>Column info keys:</strong> {Object.keys(columnInfo).join(', ')}<br/>
                    <strong>Column info sample:</strong> {JSON.stringify(Object.values(columnInfo)[0], null, 2)}<br/>
                    <strong>Detected constraints:</strong> {JSON.stringify(detectedConstraints, null, 2)}<br/>
                    <strong>Constraint info:</strong> {JSON.stringify(constraintInfo, null, 2)}
                  </div>
                )}

                <ColumnSelector
                  columns={columnInfo}
                  selectedTextColumns={selectedTextColumns}
                  selectedLabelColumns={selectedLabelColumns}
                  onTextColumnsChange={setSelectedTextColumns}
                  onLabelColumnsChange={setSelectedLabelColumns}
                />
              </div>
            )}

            {/* Step 4: Method Selection */}
            {currentStep === 4 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Brain className="w-5 h-5" />
                    Method Selection
                  </CardTitle>
                  <CardDescription>
                    Choose how you want to perform hierarchical classification
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Tabs value={trainingMethod} onValueChange={(value) => {
                    setTrainingMethod(value as 'llm' | 'custom' | 'model_classification');
                    if (value === 'llm') {
                      setWorkflowType('inference');
                    } else if (value === 'custom') {
                      setWorkflowType('training');
                    } else if (value === 'model_classification') {
                      setWorkflowType('model_classification');
                    }
                  }}>
                    <TabsList className="grid w-full grid-cols-3">
                      <TabsTrigger value="custom">Custom Training</TabsTrigger>
                      <TabsTrigger value="model_classification">Model Classification</TabsTrigger>
                      <TabsTrigger value="llm">LLM Classification</TabsTrigger>
                    </TabsList>

                    <TabsContent value="custom" className="space-y-4">
                      <Alert>
                        <Target className="h-4 w-4" />
                        <AlertDescription>
                          Train a custom hierarchical classification model using your data.
                          This provides the best accuracy for your specific use case.
                        </AlertDescription>
                      </Alert>

                      {/* Model Management Section */}
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <h4 className="text-lg font-semibold">Model Options</h4>
                          <div className="flex gap-2">
                            <Button
                              variant="outline"
                              onClick={() => setShowModelManager(!showModelManager)}
                            >
                              <Brain className="w-4 h-4 mr-2" />
                              {showModelManager ? 'Hide' : 'Show'} Existing Models
                            </Button>
                            <Button
                              variant="outline"
                              onClick={() => setShowTrainingConfig(!showTrainingConfig)}
                            >
                              <Settings className="w-4 h-4 mr-2" />
                              {showTrainingConfig ? 'Hide' : 'Show'} Training Config
                            </Button>
                          </div>
                        </div>

                        {/* Model Manager */}
                        {showModelManager && (
                          <HierarchicalModelManager
                            onModelSelect={handleModelSelect}
                            onModelUse={handleModelUse}
                            onModelDelete={handleModelDelete}
                            selectedModelId={selectedModel?.id}
                            showActions={true}
                          />
                        )}

                        {/* Training Configuration */}
                        {showTrainingConfig && (
                          <HierarchicalTrainingConfig
                            onConfigChange={handleTrainingConfigChange}
                            onSave={handleTrainingConfigSave}
                            userJourney={userJourney}
                            hierarchyLevels={hierarchyLevels.length}
                          />
                        )}
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div className="p-4 border rounded">
                          <h4 className="font-semibold mb-2">Model Architecture</h4>
                          <p className="text-sm text-muted-foreground">
                            {trainingConfig?.baseModel || 'Hierarchical BERT-based'} classifier with constraint enforcement
                          </p>
                        </div>
                        <div className="p-4 border rounded">
                          <h4 className="font-semibold mb-2">Training Time</h4>
                          <p className="text-sm text-muted-foreground">
                            {trainingConfig ?
                              `Estimated ${Math.round(trainingConfig.numEpochs * 10)} minutes` :
                              'Estimated 10-30 minutes depending on data size'
                            }
                          </p>
                        </div>
                        <div className="p-4 border rounded">
                          <h4 className="font-semibold mb-2">Accuracy</h4>
                          <p className="text-sm text-muted-foreground">
                            Typically 85-95% hierarchical F1 score
                          </p>
                        </div>
                      </div>
                    </TabsContent>

                    <TabsContent value="model_classification" className="space-y-4">
                      <Alert>
                        <Target className="h-4 w-4" />
                        <AlertDescription>
                          Use a pre-trained hierarchical model to classify your data.
                          Select your model, configure settings, and run classification directly from this step.
                          You'll be taken to the results step once classification is complete.
                        </AlertDescription>
                      </Alert>

                      <HierarchicalModelClassification
                        trainingResults={trainingResults}
                        uploadedData={uploadedData}
                        dualData={dualData}
                        selectedTextColumns={selectedTextColumns}
                        selectedLabelColumns={selectedLabelColumns}
                        hierarchyLevels={hierarchyLevels.map(level => level.column)}
                        onClassificationStart={handleStartClassification}
                        onClassificationComplete={handleClassificationComplete}
                        isClassifying={isClassifying}
                        classificationProgress={classificationProgress}
                      />
                    </TabsContent>

                    <TabsContent value="llm" className="space-y-4">
                      <Alert>
                        <Zap className="h-4 w-4" />
                        <AlertDescription>
                          Use a large language model for hierarchical classification.
                          Faster setup but may require prompt engineering.
                        </AlertDescription>
                      </Alert>

                      <LLMConfigurationPanel
                        initialConfig={{
                          provider: llmProvider,
                          model: llmModel,
                          endpoint: '',
                          customPrompt: customPrompt,
                          temperature: 0.1,
                          maxTokens: 100
                        }}
                        onConfigurationChange={(config) => {
                          // Update state when configuration changes
                          setLlmProvider(config.provider);
                          setLlmModel(config.model);
                          setCustomPrompt(config.customPrompt || '');
                        }}
                        onApply={(config) => {
                          setLlmProvider(config.provider);
                          setLlmModel(config.model);
                          setCustomPrompt(config.customPrompt || '');
                        }}
                        onCancel={() => {}}
                        classificationType="hierarchical"
                      />
                    </TabsContent>
                  </Tabs>
                </CardContent>
              </Card>
            )}

            {/* Step 5: Training (Custom), Classification (LLM/Model) */}
            {currentStep === 5 && (
              <div className="space-y-6">
                {/* Training Step for Custom Training */}
                {workflowType === 'training' && (
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Zap className="w-5 h-5" />
                        {isTraining ? 'Training in Progress' : 'Start Training'}
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      {!isTraining && !trainingResults && (
                        <div className="space-y-4">
                          <Alert>
                            <Shield className="h-4 w-4" />
                            <AlertDescription>
                              Ready to start hierarchical classification training with {hierarchyLevels.length} levels and {constraints.length} constraint rules.
                            </AlertDescription>
                          </Alert>

                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div className="p-4 border rounded">
                              <h4 className="font-semibold mb-2">Configuration Summary</h4>
                              <ul className="text-sm space-y-1">
                                <li>• Method: Custom Training</li>
                                <li>• Text Columns: {selectedTextColumns.join(', ') || 'None selected'}</li>
                                <li>• Hierarchy Levels: {hierarchyLevels.length}</li>
                                <li>• Constraints: {constraints.length} rules</li>
                                <li>• Validation Rules: {validationRules.filter(r => r.enabled).length} enabled</li>
                              </ul>
                            </div>
                            <div className="p-4 border rounded">
                              <h4 className="font-semibold mb-2">Expected Results</h4>
                              <ul className="text-sm space-y-1">
                                <li>• Hierarchical metrics (H-F1, path accuracy)</li>
                                <li>• Level-wise performance analysis</li>
                                <li>• Constraint violation detection</li>
                                <li>• Interactive tree visualization</li>
                                <li>• Exportable results and model</li>
                              </ul>
                            </div>
                          </div>

                          <div className="space-y-2">
                            <Button
                              onClick={trainingConfig ? handleEnhancedTraining : handleStartTraining}
                              className="w-full"
                              size="lg"
                              disabled={isTraining}
                            >
                              {isTraining ? (
                                <>
                                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                                  Training... {Math.round(trainingProgress || 0)}%
                                </>
                              ) : (
                                <>
                                  <Zap className="w-4 h-4 mr-2" />
                                  {trainingConfig ? `Train "${trainingConfig.modelName}"` : 'Start Hierarchical Training'}
                                </>
                              )}
                            </Button>

                            {!trainingConfig && (
                              <p className="text-sm text-muted-foreground text-center">
                                Configure training parameters above for enhanced options
                              </p>
                          )}
                        </div>
                      </div>
                    )}

                    {isTraining && (
                      <Card>
                        <CardHeader>
                          <CardTitle className="flex items-center gap-2">
                            <Zap className="w-5 h-5" />
                            Training Progress
                          </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                          <div className="space-y-2">
                            <div className="flex justify-between text-sm">
                              <span>Overall Progress</span>
                              <span>{trainingProgress.toFixed(1)}%</span>
                            </div>
                            <Progress value={trainingProgress} className="h-2" />
                          </div>

                          <div className="text-sm text-muted-foreground">
                            Real-time training metrics and progress
                          </div>

                          <div className="flex justify-between items-center">
                            <Badge variant="outline" className="flex items-center gap-1">
                              <Activity className="w-3 h-3" />
                              Training in Progress
                            </Badge>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => {
                                setIsTraining(false);
                                toast({
                                  title: "Training cancelled",
                                  description: "Training has been stopped"
                                });
                              }}
                            >
                              Cancel Training
                            </Button>
                          </div>
                        </CardContent>
                      </Card>
                    )}

                    {trainingResults && (
                      <Alert>
                        <CheckCircle2 className="h-4 w-4" />
                        <AlertDescription>
                          Training completed successfully! View results in the next step.
                        </AlertDescription>
                      </Alert>
                    )}
                  </CardContent>
                </Card>
                )}

                {/* Classification Step for LLM Inference Only */}
                {workflowType === 'inference' && (
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Zap className="w-5 h-5" />
                        LLM Classification
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <Alert>
                          <Zap className="h-4 w-4" />
                          <AlertDescription>
                            Ready to start hierarchical classification inference with {hierarchyLevels.length} levels using {llmProvider} {llmModel}.
                          </AlertDescription>
                        </Alert>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div className="p-4 border rounded">
                            <h4 className="font-semibold mb-2">Configuration Summary</h4>
                            <ul className="text-sm space-y-1">
                              <li>• Method: LLM Classification</li>
                              <li>• Text Columns: {selectedTextColumns.join(', ') || 'None selected'}</li>
                              <li>• Hierarchy Levels: {hierarchyLevels.length}</li>
                              <li>• LLM Provider: {llmProvider}</li>
                              <li>• LLM Model: {llmModel}</li>
                            </ul>
                          </div>
                          <div className="p-4 border rounded">
                            <h4 className="font-semibold mb-2">Expected Results</h4>
                            <ul className="text-sm space-y-1">
                              <li>• Hierarchical classification results</li>
                              <li>• Confidence scores per level</li>
                              <li>• Path-based predictions</li>
                              <li>• Interactive visualization</li>
                              <li>• Exportable results</li>
                            </ul>
                          </div>
                        </div>

                        <Button
                          onClick={handleStartInference}
                          className="w-full"
                          size="lg"
                          disabled={isTraining}
                        >
                          {isTraining ? (
                            <>
                              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                              Processing... {Math.round(trainingProgress || 0)}%
                            </>
                          ) : (
                            <>
                              <Zap className="w-4 h-4 mr-2" />
                              Start Hierarchical Inference
                            </>
                          )}
                        </Button>

                        {/* Progress Display during Inference */}
                        {isTraining && (
                          <Card className="mt-4">
                            <CardContent className="pt-6">
                              <div className="space-y-4">
                                <div className="flex items-center gap-3">
                                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                                  <div>
                                    <h4 className="font-semibold">Processing Classification</h4>
                                    <p className="text-sm text-muted-foreground">
                                      Analyzing your data with {llmProvider} {llmModel}...
                                    </p>
                                  </div>
                                </div>

                                <div className="space-y-2">
                                  <div className="flex justify-between text-sm">
                                    <span>Progress</span>
                                    <span>{Math.round(trainingProgress || 0)}%</span>
                                  </div>
                                  <Progress value={trainingProgress || 0} className="w-full" />
                                </div>

                                <div className="grid grid-cols-2 gap-4 text-sm">
                                  <div>
                                    <span className="text-muted-foreground">Data Rows:</span>
                                    <span className="ml-2 font-medium">
                                      {dualData ? dualData.classificationData?.fileInfo?.num_rows : uploadedData?.num_rows}
                                    </span>
                                  </div>
                                  <div>
                                    <span className="text-muted-foreground">Hierarchy Levels:</span>
                                    <span className="ml-2 font-medium">{hierarchyLevels.length}</span>
                                  </div>
                                </div>

                                <Alert>
                                  <Info className="h-4 w-4" />
                                  <AlertDescription>
                                    This process may take a few minutes depending on your data size and model complexity.
                                  </AlertDescription>
                                </Alert>
                              </div>
                            </CardContent>
                          </Card>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                )}
              </div>
            )}

            {/* Step 6: Classification (Custom Training Path Only) */}
            {currentStep === 6 && workflowType === 'training' && (
              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Target className="w-5 h-5" />
                      Model Classification
                    </CardTitle>
                    <CardDescription>
                      Use your trained model to classify data
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    {trainingResults && (
                      <Alert className="mb-4">
                        <CheckCircle2 className="h-4 w-4" />
                        <AlertDescription>
                          <strong>Training completed successfully!</strong>
                          <br />
                          Model ID: {trainingResults.model_id}
                          <br />
                          Training Method: {trainingResults.training_method}
                          {trainingResults.metrics && (
                            <>
                              <br />
                              Training Loss: {trainingResults.metrics.training_loss?.toFixed(4)}
                            </>
                          )}
                        </AlertDescription>
                      </Alert>
                    )}

                    <HierarchicalModelClassification
                      trainingResults={trainingResults}
                      uploadedData={uploadedData}
                      dualData={dualData}
                      selectedTextColumns={selectedTextColumns}
                      selectedLabelColumns={selectedLabelColumns}
                      hierarchyLevels={hierarchyLevels.map(level => level.column)}
                      onClassificationStart={handleStartClassification}
                      onClassificationComplete={handleClassificationComplete}
                      isClassifying={isClassifying}
                      classificationProgress={classificationProgress}
                    />
                  </CardContent>
                </Card>
              </div>
            )}

            {/* Results Step */}
            {isCurrentStep('results') && (
              <div className="space-y-6">
                {/* Results Header */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <BarChart3 className="w-5 h-5" />
                      Hierarchical Classification Results
                    </CardTitle>
                    <CardDescription>
                      View your classification results, metrics, and download the processed data
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="flex flex-wrap gap-3">
                      <Button
                        onClick={() => handleExportResults('csv')}
                        variant="outline"
                        className="flex items-center gap-2"
                      >
                        <FileSpreadsheet className="w-4 h-4" />
                        Export CSV
                      </Button>
                      <Button
                        onClick={() => handleExportResults('excel')}
                        variant="outline"
                        className="flex items-center gap-2"
                      >
                        <FileSpreadsheet className="w-4 h-4" />
                        Export Excel
                      </Button>
                      <Button
                        onClick={() => handleExportResults('json')}
                        variant="outline"
                        className="flex items-center gap-2"
                      >
                        <Download className="w-4 h-4" />
                        Export JSON
                      </Button>
                    </div>
                  </CardContent>
                </Card>

                {/* Metrics Display */}
                {metrics && (
                  <HierarchicalMetricsDisplay
                    metrics={metrics}
                    hierarchyLevels={hierarchyLevels.map(level => level.name)}
                  />
                )}

                {/* Hierarchy Visualization */}
                {trainingResults && (
                  <HierarchyVisualization
                    results={trainingResults.hierarchy_results || []}
                    hierarchyLevels={hierarchyLevels.map(level => level.name)}
                    onNodeSelect={(node) => {
                      console.log('Selected node:', node);
                    }}
                  />
                )}

                {/* Results Data Table */}
                {resultsData && resultsData.length > 0 && (
                  <ResultsDataTable
                    data={resultsData}
                    taskId={trainingTaskId || 'model-classification'}
                    filename={uploadedData?.filename || dualData?.classificationData?.fileInfo?.filename}
                    onExportCSV={() => handleExportResults('csv')}
                    onExportExcel={() => handleExportResults('excel')}
                  />
                )}

                {/* Show message if no results */}
                {(!resultsData || resultsData.length === 0) && (
                  <Card>
                    <CardContent className="text-center py-8">
                      <p className="text-muted-foreground">No classification results available yet.</p>
                    </CardContent>
                  </Card>
                )}

                {/* Navigation to Deploy Step */}
                {resultsData && resultsData.length > 0 && (
                  <div className="flex justify-between">
                    <Button
                      variant="outline"
                      onClick={() => setCurrentStep(currentStep - 1)}
                    >
                      Back
                    </Button>
                    <Button
                      onClick={() => goToStep('deploy')}
                      className="bg-ml-success hover:bg-ml-success-dark"
                    >
                      Continue to Deploy
                    </Button>
                  </div>
                )}

                {violations && violations.length > 0 && (
                  <ConstraintViolationReporter
                    violations={violations}
                    summary={{
                      total_violations: violations.length,
                      violations_by_type: violations.reduce((acc, v) => {
                        acc[v.type] = (acc[v.type] || 0) + 1;
                        return acc;
                      }, {} as Record<string, number>),
                      violations_by_level: violations.reduce((acc, v) => {
                        acc[v.level] = (acc[v.level] || 0) + 1;
                        return acc;
                      }, {} as Record<string, number>),
                      violations_by_severity: violations.reduce((acc, v) => {
                        acc[v.severity] = (acc[v.severity] || 0) + 1;
                        return acc;
                      }, {} as Record<string, number>),
                      violation_rate: violations.length / (uploadedData?.row_count || 1),
                      most_common_violations: []
                    }}
                    hierarchyLevels={hierarchyLevels.map(level => level.name)}
                    onViolationSelect={(violation) => {
                      console.log('Selected violation:', violation);
                    }}
                    onExportReport={() => {
                      toast({
                        title: "Export started",
                        description: "Violation report is being generated"
                      });
                    }}
                    onAutoFix={(violations) => {
                      toast({
                        title: "Auto-fix applied",
                        description: `Fixed ${violations.length} violations`
                      });
                    }}
                  />
                )}
              </div>
            )}

            {/* Deploy Step */}
            {isCurrentStep('deploy') && (
              <DeployStep
                modelId={trainingTaskId}
                modelName={`Hierarchical Model ${trainingTaskId ? trainingTaskId.slice(-8) : ''}`}
                trainingMetrics={{
                  accuracy: metrics?.accuracy || 0,
                  f1_score: metrics?.f1_score || 0,
                  precision: metrics?.precision || 0,
                  recall: metrics?.recall || 0,
                  training_time: metrics?.training_time || 0,
                  model_size_mb: metrics?.model_size_mb || 0
                }}
                hierarchyLevels={hierarchyLevels.map(level => ({
                  name: level.name,
                  column: level.column,
                  order: level.order
                }))}
                userLicense={userLicense}
                onComplete={(deploymentInfo) => {
                  console.log('Deployment completed:', deploymentInfo);
                  handleComplete();
                }}
                onBack={() => goToStep('results')}
              />
            )}

            {/* Navigation */}
            <div className="flex justify-between mt-8">
              <Button
                variant="outline"
                onClick={handlePrevious}
                disabled={currentStep === 1}
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Previous
              </Button>

              {currentStep < steps.length ? (
                <Button
                  onClick={handleNext}
                  disabled={isTraining}
                >
                  Next Step
                  <ArrowRight className="w-4 h-4 ml-2" />
                </Button>
              ) : (
                <Button
                  onClick={handleComplete}
                  disabled={!trainingResults}
                >
                  Complete Workflow
                  <CheckCircle2 className="w-4 h-4 ml-2" />
                </Button>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
