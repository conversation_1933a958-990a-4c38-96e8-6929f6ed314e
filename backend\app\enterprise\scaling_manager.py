"""Horizontal Scaling Manager for ClassyWeb Universal Platform."""

import logging
import asyncio
import json
from typing import Dict, List, Any, Optional
from datetime import datetime, timezone
from dataclasses import dataclass
from enum import Enum
import psutil
import aioredis

logger = logging.getLogger(__name__)


class ScalingMetric(Enum):
    """Metrics used for scaling decisions."""
    CPU_USAGE = "cpu_usage"
    MEMORY_USAGE = "memory_usage"
    QUEUE_LENGTH = "queue_length"
    RESPONSE_TIME = "response_time"
    ERROR_RATE = "error_rate"
    ACTIVE_CONNECTIONS = "active_connections"


class ScalingAction(Enum):
    """Types of scaling actions."""
    SCALE_UP = "scale_up"
    SCALE_DOWN = "scale_down"
    MAINTAIN = "maintain"


@dataclass
class ScalingRule:
    """Rule for automatic scaling decisions."""
    metric: ScalingMetric
    threshold_up: float
    threshold_down: float
    min_instances: int
    max_instances: int
    cooldown_seconds: int
    enabled: bool = True


@dataclass
class WorkerNode:
    """Represents a worker node in the cluster."""
    id: str
    host: str
    port: int
    status: str  # active, inactive, draining
    cpu_usage: float
    memory_usage: float
    active_tasks: int
    last_heartbeat: datetime
    capabilities: List[str]


class HorizontalScalingManager:
    """Manages horizontal scaling of ClassyWeb services."""
    
    def __init__(self, redis_url: str = "redis://localhost:6379"):
        self.redis_url = redis_url
        self.redis_client = None
        self.worker_nodes: Dict[str, WorkerNode] = {}
        self.scaling_rules: Dict[str, ScalingRule] = {}
        self.task_queue = asyncio.Queue()
        self.is_monitoring = False
        
        logger.info("Horizontal Scaling Manager initialized")
    
    async def initialize(self):
        """Initialize the scaling manager."""
        try:
            # Initialize default scaling rules
            await self._initialize_default_scaling_rules()

            # Connect to Redis for coordination
            self.redis_client = await aioredis.from_url(self.redis_url)

            # Start monitoring
            await self._start_monitoring()

            # Register current node
            await self._register_current_node()

            logger.info("Horizontal Scaling Manager ready")
            
        except Exception as e:
            logger.error(f"Error initializing scaling manager: {str(e)}")
            raise
    
    async def scale_service(
        self,
        service_name: str,
        target_instances: int,
        scaling_reason: str
    ) -> bool:
        """Scale a service to target number of instances."""
        try:
            logger.info(f"Scaling {service_name} to {target_instances} instances. Reason: {scaling_reason}")
            
            current_instances = await self._get_current_instances(service_name)
            
            if target_instances > current_instances:
                # Scale up
                for i in range(target_instances - current_instances):
                    success = await self._start_new_instance(service_name)
                    if not success:
                        logger.error(f"Failed to start new instance for {service_name}")
                        return False
            
            elif target_instances < current_instances:
                # Scale down
                for i in range(current_instances - target_instances):
                    success = await self._stop_instance(service_name)
                    if not success:
                        logger.error(f"Failed to stop instance for {service_name}")
                        return False
            
            # Log scaling action
            await self._log_scaling_action(service_name, current_instances, target_instances, scaling_reason)
            
            return True
            
        except Exception as e:
            logger.error(f"Error scaling service {service_name}: {str(e)}")
            return False
    
    async def distribute_task(
        self,
        task_data: Dict[str, Any],
        required_capabilities: List[str] = None
    ) -> Optional[str]:
        """Distribute a task to an available worker node."""
        try:
            # Find suitable worker nodes
            suitable_nodes = []
            for node in self.worker_nodes.values():
                if node.status != "active":
                    continue
                
                # Check capabilities
                if required_capabilities:
                    if not all(cap in node.capabilities for cap in required_capabilities):
                        continue
                
                # Check load
                if node.cpu_usage < 80 and node.memory_usage < 80:
                    suitable_nodes.append(node)
            
            if not suitable_nodes:
                logger.warning("No suitable worker nodes available")
                return None
            
            # Select node with lowest load
            selected_node = min(suitable_nodes, key=lambda n: n.cpu_usage + n.memory_usage)
            
            # Send task to selected node
            task_id = await self._send_task_to_node(selected_node, task_data)
            
            logger.info(f"Task distributed to node {selected_node.id}")
            return task_id
            
        except Exception as e:
            logger.error(f"Error distributing task: {str(e)}")
            return None
    
    async def get_cluster_status(self) -> Dict[str, Any]:
        """Get current cluster status and metrics."""
        try:
            active_nodes = [node for node in self.worker_nodes.values() if node.status == "active"]
            
            # Calculate cluster metrics
            total_cpu = sum(node.cpu_usage for node in active_nodes) / len(active_nodes) if active_nodes else 0
            total_memory = sum(node.memory_usage for node in active_nodes) / len(active_nodes) if active_nodes else 0
            total_tasks = sum(node.active_tasks for node in active_nodes)
            
            # Get queue status
            queue_length = await self._get_queue_length()
            
            return {
                "cluster_id": "classyweb_cluster",
                "total_nodes": len(self.worker_nodes),
                "active_nodes": len(active_nodes),
                "avg_cpu_usage": total_cpu,
                "avg_memory_usage": total_memory,
                "total_active_tasks": total_tasks,
                "queue_length": queue_length,
                "nodes": [
                    {
                        "id": node.id,
                        "host": node.host,
                        "port": node.port,
                        "status": node.status,
                        "cpu_usage": node.cpu_usage,
                        "memory_usage": node.memory_usage,
                        "active_tasks": node.active_tasks,
                        "last_heartbeat": node.last_heartbeat.isoformat()
                    }
                    for node in self.worker_nodes.values()
                ],
                "scaling_rules": {
                    rule_name: {
                        "metric": rule.metric.value,
                        "threshold_up": rule.threshold_up,
                        "threshold_down": rule.threshold_down,
                        "min_instances": rule.min_instances,
                        "max_instances": rule.max_instances,
                        "enabled": rule.enabled
                    }
                    for rule_name, rule in self.scaling_rules.items()
                },
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error getting cluster status: {str(e)}")
            return {}
    
    async def add_scaling_rule(
        self,
        rule_name: str,
        metric: ScalingMetric,
        threshold_up: float,
        threshold_down: float,
        min_instances: int,
        max_instances: int,
        cooldown_seconds: int = 300
    ) -> bool:
        """Add a new scaling rule."""
        try:
            rule = ScalingRule(
                metric=metric,
                threshold_up=threshold_up,
                threshold_down=threshold_down,
                min_instances=min_instances,
                max_instances=max_instances,
                cooldown_seconds=cooldown_seconds
            )
            
            self.scaling_rules[rule_name] = rule
            
            # Save to Redis for persistence
            if self.redis_client:
                await self.redis_client.set(
                    f"scaling_rule:{rule_name}",
                    json.dumps({
                        "metric": metric.value,
                        "threshold_up": threshold_up,
                        "threshold_down": threshold_down,
                        "min_instances": min_instances,
                        "max_instances": max_instances,
                        "cooldown_seconds": cooldown_seconds,
                        "enabled": True
                    })
                )
            
            logger.info(f"Added scaling rule: {rule_name}")
            return True
            
        except Exception as e:
            logger.error(f"Error adding scaling rule: {str(e)}")
            return False
    
    async def _initialize_default_scaling_rules(self):
        """Initialize default scaling rules."""
        default_rules = [
            ("cpu_scaling", ScalingMetric.CPU_USAGE, 70.0, 30.0, 1, 10, 300),
            ("memory_scaling", ScalingMetric.MEMORY_USAGE, 80.0, 40.0, 1, 10, 300),
            ("queue_scaling", ScalingMetric.QUEUE_LENGTH, 50.0, 10.0, 1, 20, 180),
            ("response_time_scaling", ScalingMetric.RESPONSE_TIME, 2000.0, 500.0, 1, 15, 240)
        ]
        
        for rule_name, metric, threshold_up, threshold_down, min_inst, max_inst, cooldown in default_rules:
            await self.add_scaling_rule(
                rule_name, metric, threshold_up, threshold_down, min_inst, max_inst, cooldown
            )
    
    async def _start_monitoring(self):
        """Start background monitoring for scaling decisions."""
        if self.is_monitoring:
            return
        
        self.is_monitoring = True
        logger.info("Starting scaling monitoring...")
        
        async def monitor_and_scale():
            while self.is_monitoring:
                try:
                    # Update node metrics
                    await self._update_node_metrics()
                    
                    # Check scaling rules
                    await self._check_scaling_rules()
                    
                    # Clean up inactive nodes
                    await self._cleanup_inactive_nodes()
                    
                    # Sleep for monitoring interval
                    await asyncio.sleep(30)  # Check every 30 seconds
                    
                except Exception as e:
                    logger.error(f"Error in scaling monitoring: {str(e)}")
                    await asyncio.sleep(60)  # Longer sleep on error
        
        # Start monitoring task
        asyncio.create_task(monitor_and_scale())
    
    async def _update_node_metrics(self):
        """Update metrics for all worker nodes."""
        try:
            current_time = datetime.now(timezone.utc)
            
            for node_id, node in self.worker_nodes.items():
                # Check if node is still responsive
                time_since_heartbeat = (current_time - node.last_heartbeat).total_seconds()
                
                if time_since_heartbeat > 120:  # 2 minutes timeout
                    node.status = "inactive"
                    logger.warning(f"Node {node_id} marked as inactive (no heartbeat)")
                    continue
                
                # Update local metrics if this is the current node
                if node.host == "localhost":  # Current node
                    node.cpu_usage = psutil.cpu_percent(interval=1)
                    node.memory_usage = psutil.virtual_memory().percent
                    node.last_heartbeat = current_time
            
        except Exception as e:
            logger.error(f"Error updating node metrics: {str(e)}")
    
    async def _check_scaling_rules(self):
        """Check scaling rules and trigger scaling actions."""
        try:
            for rule_name, rule in self.scaling_rules.items():
                if not rule.enabled:
                    continue
                
                # Get current metric value
                metric_value = await self._get_metric_value(rule.metric)
                
                # Determine scaling action
                action = ScalingAction.MAINTAIN
                
                if metric_value > rule.threshold_up:
                    action = ScalingAction.SCALE_UP
                elif metric_value < rule.threshold_down:
                    action = ScalingAction.SCALE_DOWN
                
                # Execute scaling action if needed
                if action != ScalingAction.MAINTAIN:
                    await self._execute_scaling_action(rule_name, action, rule, metric_value)
            
        except Exception as e:
            logger.error(f"Error checking scaling rules: {str(e)}")
    
    async def _get_metric_value(self, metric: ScalingMetric) -> float:
        """Get current value for a scaling metric."""
        try:
            active_nodes = [node for node in self.worker_nodes.values() if node.status == "active"]
            
            if metric == ScalingMetric.CPU_USAGE:
                return sum(node.cpu_usage for node in active_nodes) / len(active_nodes) if active_nodes else 0
            
            elif metric == ScalingMetric.MEMORY_USAGE:
                return sum(node.memory_usage for node in active_nodes) / len(active_nodes) if active_nodes else 0
            
            elif metric == ScalingMetric.QUEUE_LENGTH:
                return await self._get_queue_length()
            
            elif metric == ScalingMetric.RESPONSE_TIME:
                # TODO: Implement response time tracking
                return 1000.0  # Placeholder
            
            elif metric == ScalingMetric.ERROR_RATE:
                # TODO: Implement error rate tracking
                return 5.0  # Placeholder
            
            elif metric == ScalingMetric.ACTIVE_CONNECTIONS:
                return sum(node.active_tasks for node in active_nodes)
            
            return 0.0
            
        except Exception as e:
            logger.error(f"Error getting metric value for {metric}: {str(e)}")
            return 0.0
    
    async def _execute_scaling_action(
        self,
        rule_name: str,
        action: ScalingAction,
        rule: ScalingRule,
        metric_value: float
    ):
        """Execute a scaling action."""
        try:
            current_instances = len([node for node in self.worker_nodes.values() if node.status == "active"])
            
            if action == ScalingAction.SCALE_UP:
                if current_instances < rule.max_instances:
                    target_instances = min(current_instances + 1, rule.max_instances)
                    await self.scale_service("classyweb_worker", target_instances, f"Rule {rule_name}: {rule.metric.value} = {metric_value}")
            
            elif action == ScalingAction.SCALE_DOWN:
                if current_instances > rule.min_instances:
                    target_instances = max(current_instances - 1, rule.min_instances)
                    await self.scale_service("classyweb_worker", target_instances, f"Rule {rule_name}: {rule.metric.value} = {metric_value}")
            
        except Exception as e:
            logger.error(f"Error executing scaling action: {str(e)}")
    
    async def _register_current_node(self):
        """Register the current node in the cluster."""
        try:
            import socket
            import uuid
            
            node_id = str(uuid.uuid4())
            hostname = socket.gethostname()
            
            node = WorkerNode(
                id=node_id,
                host=hostname,
                port=8000,  # Default port
                status="active",
                cpu_usage=psutil.cpu_percent(),
                memory_usage=psutil.virtual_memory().percent,
                active_tasks=0,
                last_heartbeat=datetime.now(timezone.utc),
                capabilities=["classification", "training", "analysis"]
            )
            
            self.worker_nodes[node_id] = node
            
            # Register in Redis
            if self.redis_client:
                await self.redis_client.set(
                    f"node:{node_id}",
                    json.dumps({
                        "host": node.host,
                        "port": node.port,
                        "status": node.status,
                        "capabilities": node.capabilities,
                        "registered_at": node.last_heartbeat.isoformat()
                    }),
                    ex=300  # Expire in 5 minutes
                )
            
            logger.info(f"Registered current node: {node_id}")
            
        except Exception as e:
            logger.error(f"Error registering current node: {str(e)}")
    
    async def _get_current_instances(self, service_name: str) -> int:
        """Get current number of instances for a service."""
        try:
            active_nodes = [node for node in self.worker_nodes.values() if node.status == "active"]
            return len(active_nodes)
            
        except Exception as e:
            logger.error(f"Error getting current instances: {str(e)}")
            return 1
    
    async def _start_new_instance(self, service_name: str) -> bool:
        """Start a new service instance."""
        try:
            # TODO: Implement actual instance creation
            # This would typically involve:
            # 1. Creating new container/process
            # 2. Registering with load balancer
            # 3. Health checking
            
            logger.info(f"Starting new instance for {service_name}")
            
            # Simulate new instance
            import uuid
            new_node_id = str(uuid.uuid4())
            new_node = WorkerNode(
                id=new_node_id,
                host=f"worker-{new_node_id[:8]}",
                port=8000,
                status="active",
                cpu_usage=10.0,
                memory_usage=20.0,
                active_tasks=0,
                last_heartbeat=datetime.now(timezone.utc),
                capabilities=["classification", "training"]
            )
            
            self.worker_nodes[new_node_id] = new_node
            
            return True
            
        except Exception as e:
            logger.error(f"Error starting new instance: {str(e)}")
            return False
    
    async def _stop_instance(self, service_name: str) -> bool:
        """Stop a service instance."""
        try:
            # Find instance to stop (prefer least loaded)
            active_nodes = [node for node in self.worker_nodes.values() if node.status == "active"]
            
            if not active_nodes:
                return False
            
            # Select node with lowest load
            node_to_stop = min(active_nodes, key=lambda n: n.active_tasks)
            
            # Gracefully drain the node
            node_to_stop.status = "draining"
            
            # TODO: Wait for tasks to complete, then stop
            # For now, just mark as inactive
            await asyncio.sleep(5)  # Simulate drain time
            node_to_stop.status = "inactive"
            
            logger.info(f"Stopped instance: {node_to_stop.id}")
            return True
            
        except Exception as e:
            logger.error(f"Error stopping instance: {str(e)}")
            return False
    
    async def _send_task_to_node(self, node: WorkerNode, task_data: Dict[str, Any]) -> str:
        """Send a task to a specific worker node."""
        try:
            # TODO: Implement actual task distribution
            # This would involve sending HTTP request or message queue
            
            task_id = f"task_{int(datetime.now().timestamp() * 1000)}"
            node.active_tasks += 1
            
            logger.info(f"Sent task {task_id} to node {node.id}")
            return task_id
            
        except Exception as e:
            logger.error(f"Error sending task to node: {str(e)}")
            raise
    
    async def _get_queue_length(self) -> int:
        """Get current task queue length."""
        try:
            if self.redis_client:
                queue_length = await self.redis_client.llen("task_queue")
                return queue_length
            return self.task_queue.qsize()
            
        except Exception as e:
            logger.error(f"Error getting queue length: {str(e)}")
            return 0
    
    async def _cleanup_inactive_nodes(self):
        """Remove inactive nodes from the cluster."""
        try:
            current_time = datetime.now(timezone.utc)
            inactive_nodes = []
            
            for node_id, node in self.worker_nodes.items():
                time_since_heartbeat = (current_time - node.last_heartbeat).total_seconds()
                
                # Remove nodes that haven't sent heartbeat in 10 minutes
                if time_since_heartbeat > 600:
                    inactive_nodes.append(node_id)
            
            for node_id in inactive_nodes:
                del self.worker_nodes[node_id]
                logger.info(f"Removed inactive node: {node_id}")
                
                # Remove from Redis
                if self.redis_client:
                    await self.redis_client.delete(f"node:{node_id}")
            
        except Exception as e:
            logger.error(f"Error cleaning up inactive nodes: {str(e)}")
    
    async def _log_scaling_action(
        self,
        service_name: str,
        from_instances: int,
        to_instances: int,
        reason: str
    ):
        """Log scaling action for audit purposes."""
        try:
            scaling_log = {
                "service_name": service_name,
                "from_instances": from_instances,
                "to_instances": to_instances,
                "reason": reason,
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
            
            # Store in Redis
            if self.redis_client:
                await self.redis_client.lpush("scaling_logs", json.dumps(scaling_log))
                await self.redis_client.ltrim("scaling_logs", 0, 999)  # Keep last 1000 logs
            
            logger.info(f"Logged scaling action: {service_name} {from_instances} -> {to_instances}")
            
        except Exception as e:
            logger.error(f"Error logging scaling action: {str(e)}")


# Global horizontal scaling manager instance
horizontal_scaling_manager = HorizontalScalingManager()
