/**
 * EnhancedModelComparisonDashboard.tsx
 * 
 * Comprehensive model comparison dashboard with metrics visualization and performance analysis.
 * Supports multiple models, detailed metrics, and interactive comparisons.
 */

import React, { useState, useMemo } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  BarChart3, 
  TrendingUp, 
  Target, 
  Clock, 
  Zap, 
  Award,
  GitCompare,
  Download,
  Eye,
  Star,
  CheckCircle2
} from "lucide-react";
import { 
  <PERSON><PERSON><PERSON>, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  RadarChart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  Radar,
  LineChart,
  Line
} from 'recharts';

interface ModelMetrics {
  accuracy: number;
  precision: number;
  recall: number;
  f1_score: number;
  confusion_matrix?: number[][];
  class_report?: any;
  roc_auc?: number;
  pr_auc?: number;
}

interface ModelInfo {
  id: string;
  name: string;
  type: 'llm' | 'custom' | 'ensemble';
  metrics: ModelMetrics;
  trainingTime: number;
  modelSize: number;
  status: 'training' | 'completed' | 'failed';
  createdAt?: string;
  hyperparameters?: Record<string, any>;
}

interface EnhancedModelComparisonDashboardProps {
  models: ModelInfo[];
  selectedMetric?: string;
  onModelSelect?: (modelId: string) => void;
  onModelDownload?: (modelId: string) => void;
  onModelDeploy?: (modelId: string) => void;
  showAdvancedMetrics?: boolean;
}

const METRIC_COLORS = {
  accuracy: '#3b82f6',
  precision: '#22c55e',
  recall: '#f59e0b',
  f1_score: '#8b5cf6',
  roc_auc: '#ef4444',
  pr_auc: '#06b6d4'
};

export const EnhancedModelComparisonDashboard: React.FC<EnhancedModelComparisonDashboardProps> = ({
  models,
  selectedMetric = 'accuracy',
  onModelSelect,
  onModelDownload,
  onModelDeploy,
  showAdvancedMetrics = true
}) => {
  const [selectedModels, setSelectedModels] = useState<string[]>(models.map(m => m.id));
  const [comparisonView, setComparisonView] = useState<'table' | 'chart' | 'radar'>('table');

  // Filter completed models
  const completedModels = useMemo(() => 
    models.filter(model => model.status === 'completed'),
    [models]
  );

  // Prepare data for charts
  const chartData = useMemo(() => {
    return completedModels
      .filter(model => selectedModels.includes(model.id))
      .map(model => ({
        name: model.name,
        accuracy: model.metrics.accuracy * 100,
        precision: model.metrics.precision * 100,
        recall: model.metrics.recall * 100,
        f1_score: model.metrics.f1_score * 100,
        roc_auc: (model.metrics.roc_auc || 0) * 100,
        pr_auc: (model.metrics.pr_auc || 0) * 100,
        trainingTime: model.trainingTime / 60, // Convert to minutes
        modelSize: model.modelSize / (1024 * 1024) // Convert to MB
      }));
  }, [completedModels, selectedModels]);

  // Find best model for each metric
  const bestModels = useMemo(() => {
    const metrics = ['accuracy', 'precision', 'recall', 'f1_score', 'roc_auc', 'pr_auc'];
    const best: Record<string, ModelInfo> = {};

    metrics.forEach(metric => {
      const bestModel = completedModels.reduce((prev, current) => {
        const prevValue = (prev.metrics as any)[metric] || 0;
        const currentValue = (current.metrics as any)[metric] || 0;
        return currentValue > prevValue ? current : prev;
      });
      if (bestModel) {
        best[metric] = bestModel;
      }
    });

    return best;
  }, [completedModels]);

  const getModelTypeIcon = (type: string) => {
    switch (type) {
      case 'llm': return <Zap className="w-4 h-4" />;
      case 'custom': return <Target className="w-4 h-4" />;
      case 'ensemble': return <GitCompare className="w-4 h-4" />;
      default: return <BarChart3 className="w-4 h-4" />;
    }
  };

  const getModelTypeColor = (type: string) => {
    switch (type) {
      case 'llm': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'custom': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'ensemble': return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  const formatMetric = (value: number, isPercentage = true) => {
    return isPercentage ? `${(value * 100).toFixed(1)}%` : value.toFixed(3);
  };

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return minutes > 0 ? `${minutes}m ${remainingSeconds}s` : `${remainingSeconds}s`;
  };

  const formatSize = (bytes: number) => {
    const mb = bytes / (1024 * 1024);
    return mb > 1024 ? `${(mb / 1024).toFixed(1)} GB` : `${mb.toFixed(1)} MB`;
  };

  if (completedModels.length === 0) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <BarChart3 className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">No Models to Compare</h3>
          <p className="text-muted-foreground">
            Train some models to see performance comparisons here.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Award className="w-5 h-5 text-yellow-600" />
              <div>
                <div className="text-sm text-muted-foreground">Best Accuracy</div>
                <div className="font-bold">
                  {bestModels.accuracy ? formatMetric(bestModels.accuracy.metrics.accuracy) : 'N/A'}
                </div>
                <div className="text-xs text-muted-foreground">
                  {bestModels.accuracy?.name}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Target className="w-5 h-5 text-green-600" />
              <div>
                <div className="text-sm text-muted-foreground">Best F1 Score</div>
                <div className="font-bold">
                  {bestModels.f1_score ? formatMetric(bestModels.f1_score.metrics.f1_score) : 'N/A'}
                </div>
                <div className="text-xs text-muted-foreground">
                  {bestModels.f1_score?.name}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Clock className="w-5 h-5 text-blue-600" />
              <div>
                <div className="text-sm text-muted-foreground">Fastest Training</div>
                <div className="font-bold">
                  {Math.min(...completedModels.map(m => m.trainingTime)) > 0 
                    ? formatTime(Math.min(...completedModels.map(m => m.trainingTime)))
                    : 'N/A'
                  }
                </div>
                <div className="text-xs text-muted-foreground">
                  {completedModels.reduce((prev, current) => 
                    current.trainingTime < prev.trainingTime ? current : prev
                  )?.name}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <TrendingUp className="w-5 h-5 text-purple-600" />
              <div>
                <div className="text-sm text-muted-foreground">Total Models</div>
                <div className="font-bold">{completedModels.length}</div>
                <div className="text-xs text-muted-foreground">
                  {models.filter(m => m.status === 'training').length} training
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Model Comparison */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <GitCompare className="w-5 h-5" />
            Model Comparison
          </CardTitle>
          <CardDescription>
            Compare performance metrics across different models
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs value={comparisonView} onValueChange={(value) => setComparisonView(value as any)}>
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="table">Table View</TabsTrigger>
              <TabsTrigger value="chart">Bar Chart</TabsTrigger>
              <TabsTrigger value="radar">Radar Chart</TabsTrigger>
            </TabsList>

            <TabsContent value="table" className="space-y-4">
              <div className="border rounded-lg overflow-hidden">
                <table className="w-full text-sm">
                  <thead className="bg-muted">
                    <tr>
                      <th className="text-left p-3">Model</th>
                      <th className="text-left p-3">Type</th>
                      <th className="text-left p-3">Accuracy</th>
                      <th className="text-left p-3">Precision</th>
                      <th className="text-left p-3">Recall</th>
                      <th className="text-left p-3">F1 Score</th>
                      <th className="text-left p-3">Training Time</th>
                      <th className="text-left p-3">Size</th>
                      <th className="text-left p-3">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {completedModels.map((model) => (
                      <tr key={model.id} className="border-t hover:bg-muted/50">
                        <td className="p-3">
                          <div className="flex items-center gap-2">
                            <div className="font-medium">{model.name}</div>
                            {bestModels.accuracy?.id === model.id && (
                              <Star className="w-4 h-4 text-yellow-500" />
                            )}
                          </div>
                        </td>
                        <td className="p-3">
                          <Badge className={getModelTypeColor(model.type)}>
                            {getModelTypeIcon(model.type)}
                            <span className="ml-1 capitalize">{model.type}</span>
                          </Badge>
                        </td>
                        <td className="p-3 font-medium">
                          {formatMetric(model.metrics.accuracy)}
                        </td>
                        <td className="p-3 font-medium">
                          {formatMetric(model.metrics.precision)}
                        </td>
                        <td className="p-3 font-medium">
                          {formatMetric(model.metrics.recall)}
                        </td>
                        <td className="p-3 font-medium">
                          {formatMetric(model.metrics.f1_score)}
                        </td>
                        <td className="p-3">{formatTime(model.trainingTime)}</td>
                        <td className="p-3">{formatSize(model.modelSize)}</td>
                        <td className="p-3">
                          <div className="flex gap-1">
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => onModelSelect?.(model.id)}
                            >
                              <Eye className="w-3 h-3" />
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => onModelDownload?.(model.id)}
                            >
                              <Download className="w-3 h-3" />
                            </Button>
                            <Button
                              size="sm"
                              onClick={() => onModelDeploy?.(model.id)}
                            >
                              <Zap className="w-3 h-3" />
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </TabsContent>

            <TabsContent value="chart" className="space-y-4">
              <div className="space-y-4">
                <div className="flex flex-wrap gap-2">
                  {['accuracy', 'precision', 'recall', 'f1_score'].map((metric) => (
                    <Button
                      key={metric}
                      variant={selectedMetric === metric ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => {/* setSelectedMetric(metric) */}}
                    >
                      {metric.replace('_', ' ').toUpperCase()}
                    </Button>
                  ))}
                </div>
                
                <ResponsiveContainer width="100%" height={400}>
                  <BarChart data={chartData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <Tooltip />
                    <Bar 
                      dataKey={selectedMetric} 
                      fill={METRIC_COLORS[selectedMetric as keyof typeof METRIC_COLORS]} 
                    />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </TabsContent>

            <TabsContent value="radar" className="space-y-4">
              <ResponsiveContainer width="100%" height={400}>
                <RadarChart data={chartData}>
                  <PolarGrid />
                  <PolarAngleAxis dataKey="name" />
                  <PolarRadiusAxis angle={90} domain={[0, 100]} />
                  <Radar
                    name="Accuracy"
                    dataKey="accuracy"
                    stroke="#3b82f6"
                    fill="#3b82f6"
                    fillOpacity={0.1}
                  />
                  <Radar
                    name="Precision"
                    dataKey="precision"
                    stroke="#22c55e"
                    fill="#22c55e"
                    fillOpacity={0.1}
                  />
                  <Radar
                    name="Recall"
                    dataKey="recall"
                    stroke="#f59e0b"
                    fill="#f59e0b"
                    fillOpacity={0.1}
                  />
                  <Radar
                    name="F1 Score"
                    dataKey="f1_score"
                    stroke="#8b5cf6"
                    fill="#8b5cf6"
                    fillOpacity={0.1}
                  />
                  <Tooltip />
                </RadarChart>
              </ResponsiveContainer>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Advanced Metrics */}
      {showAdvancedMetrics && (
        <Card>
          <CardHeader>
            <CardTitle>Advanced Metrics</CardTitle>
            <CardDescription>
              Detailed performance analysis and model characteristics
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Training Time vs Accuracy */}
              <div>
                <h4 className="font-medium mb-3">Training Time vs Accuracy</h4>
                <ResponsiveContainer width="100%" height={250}>
                  <LineChart data={chartData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="trainingTime" />
                    <YAxis />
                    <Tooltip />
                    <Line 
                      type="monotone" 
                      dataKey="accuracy" 
                      stroke="#3b82f6" 
                      strokeWidth={2}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </div>

              {/* Model Size vs Performance */}
              <div>
                <h4 className="font-medium mb-3">Model Size vs F1 Score</h4>
                <ResponsiveContainer width="100%" height={250}>
                  <LineChart data={chartData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="modelSize" />
                    <YAxis />
                    <Tooltip />
                    <Line 
                      type="monotone" 
                      dataKey="f1_score" 
                      stroke="#8b5cf6" 
                      strokeWidth={2}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default EnhancedModelComparisonDashboard;
