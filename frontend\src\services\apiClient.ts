// frontend/src/services/apiClient.ts
import axios from 'axios';

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8001';

const apiClient = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  withCredentials: true, // Important: This allows cookies to be sent with requests
});

// Get token from localStorage if available
const token = localStorage.getItem('auth-storage')
  ? JSON.parse(localStorage.getItem('auth-storage') || '{}').state?.token
  : null;

// Set auth token if available
if (token) {
  console.log('Setting initial Authorization header from localStorage');
  apiClient.defaults.headers.common['Authorization'] = `Bearer ${token}`;
} else {
  console.log('No token found in localStorage');
}

// Add request interceptor for auth token
apiClient.interceptors.request.use(
  (config) => {
    // Get the latest token from localStorage
    const authStorage = localStorage.getItem('auth-storage');
    if (authStorage) {
      const token = JSON.parse(authStorage).state?.token;
      if (token) {
        console.log(`Setting Authorization header for request to ${config.url}`);
        config.headers['Authorization'] = `Bearer ${token}`;
      } else {
        console.log(`No token found for request to ${config.url}`);
      }
    } else {
      console.log(`No auth storage found for request to ${config.url}`);
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Create a flag to prevent multiple refresh attempts at once
let isRefreshing = false;
let refreshPromise: Promise<any> | null = null;

// Store pending requests that should be retried after token refresh
let pendingRequests: Array<{
  resolve: (value: any) => void;
  reject: (reason: any) => void;
  config: any;
}> = [];

// Function to get auth store without creating circular dependencies
const getAuthStore = () => {
  // This is a workaround to avoid circular dependencies
  // We need to dynamically import the store when needed
  return window.localStorage.getItem('auth-storage')
    ? JSON.parse(window.localStorage.getItem('auth-storage') || '{}')
    : { state: { token: null, refreshToken: null, tokenExpiry: null } };
};

// Function to refresh token
const refreshAuthToken = async () => {
  try {
    console.log('Attempting to refresh token using HttpOnly cookie');

    // Call refresh endpoint (no need to send refresh token in body, it's in the cookie)
    const response = await axios.post(`${API_BASE_URL}/auth/refresh`, {}, {
      withCredentials: true, // Ensure cookies are sent
      timeout: 10000, // 10 second timeout
    });

    // Get current state
    const { state } = getAuthStore();

    // Update token in localStorage
    const newState = {
      ...state,
      token: response.data.access_token,
      // Still store refresh token in localStorage for backward compatibility
      // but prefer using the HttpOnly cookie in actual requests
      refreshToken: response.data.refresh_token || state.refreshToken,
      tokenExpiry: response.data.expires_in ? Date.now() + response.data.expires_in * 1000 : null
    };

    localStorage.setItem('auth-storage', JSON.stringify({ state: newState }));

    // Update axios headers
    apiClient.defaults.headers.common['Authorization'] = `Bearer ${response.data.access_token}`;
    console.log('Token refreshed successfully using HttpOnly cookie');

    return response.data.access_token;
  } catch (error: any) {
    console.error('Token refresh failed:', error);

    // Handle specific error cases
    if (error.response?.status === 429) {
      console.warn('Rate limited during token refresh, will retry later');
      throw new Error('Rate limited - please try again later');
    } else if (error.response?.status === 401) {
      console.warn('Refresh token invalid or expired');
      // Clear auth storage on refresh failure
      localStorage.removeItem('auth-storage');
      throw new Error('Session expired - please log in again');
    } else if (error.code === 'ECONNABORTED') {
      console.warn('Token refresh timed out');
      throw new Error('Request timed out - please try again');
    }

    // Clear auth storage on other failures
    localStorage.removeItem('auth-storage');
    throw error;
  }
};

// Add response interceptor for error handling
apiClient.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;

    // Handle 401 Unauthorized errors
    // Skip token refresh for auth endpoints that should be handled by AuthProvider
    const isAuthEndpoint = originalRequest.url && (
      originalRequest.url.includes('/auth/login') ||
      originalRequest.url.includes('/auth/register') ||
      originalRequest.url.includes('/auth/token') ||
      originalRequest.url.includes('/auth/me') ||
      originalRequest.url.includes('/auth/refresh')
    );

    if (error.response && error.response.status === 401 && !originalRequest._retry && !isAuthEndpoint) {
      // We'll try to refresh the token using the HttpOnly cookie
      // No need to check if we have a refresh token in localStorage
      try {
        originalRequest._retry = true;

        // If already refreshing, wait for that to complete
        if (isRefreshing) {
          try {
            // Wait for the existing refresh to complete
            const newToken = await refreshPromise;
            // Update the request with the new token
            originalRequest.headers['Authorization'] = `Bearer ${newToken}`;
            // Retry the request
            return apiClient(originalRequest);
          } catch (refreshError) {
            // If refresh failed, redirect to login
            localStorage.removeItem('auth-storage');
            if (window.location.pathname !== '/login') {
              window.location.href = '/login';
            }
            return Promise.reject(refreshError);
          }
        }

        // Start refreshing
        isRefreshing = true;
        refreshPromise = refreshAuthToken()
          .then(token => {
            // Process pending requests
            pendingRequests.forEach(request => {
              request.config.headers['Authorization'] = `Bearer ${token}`;
              request.resolve(apiClient(request.config));
            });
            pendingRequests = [];
            return token;
          })
          .catch(err => {
            // Reject all pending requests
            pendingRequests.forEach(request => {
              request.reject(err);
            });
            pendingRequests = [];
            throw err;
          })
          .finally(() => {
            isRefreshing = false;
            refreshPromise = null;
          });

        try {
          // Wait for the refresh to complete
          const newToken = await refreshPromise;
          // Update the request with the new token
          originalRequest.headers['Authorization'] = `Bearer ${newToken}`;
          // Retry the request
          return await apiClient(originalRequest);
        } catch (refreshError) {
          // If refresh failed, redirect to login
          localStorage.removeItem('auth-storage');
          if (window.location.pathname !== '/login') {
            window.location.href = '/login';
          }
          return Promise.reject(refreshError);
        }
      } catch (refreshError) {
        // If refresh failed, redirect to login
        console.error('Token refresh failed:', refreshError);
        localStorage.removeItem('auth-storage');
        if (window.location.pathname !== '/login') {
          window.location.href = '/login';
        }
        return Promise.reject(refreshError);
      }
    }

    // For auth endpoints, provide more specific error handling
    if (originalRequest.url && (
      originalRequest.url.includes('/auth/login') ||
      originalRequest.url.includes('/auth/register') ||
      originalRequest.url.includes('/auth/token')
    )) {
      console.error('Auth API call error:', error.response?.data || error.message);
      // For auth endpoints, preserve the original error structure which includes 'detail' field
      return Promise.reject(error.response?.data || { detail: error.message });
    } else {
      console.error('API call error:', error.response?.data || error.message);
      // Transform error into a standard format
      return Promise.reject(error.response?.data || { message: error.message });
    }
  }
);

// Function to manually set auth header
export const setAuthHeader = (token: string | null) => {
  if (token) {
    console.log('Manually setting Authorization header');
    apiClient.defaults.headers.common['Authorization'] = `Bearer ${token}`;
  } else {
    console.log('Removing Authorization header');
    delete apiClient.defaults.headers.common['Authorization'];
  }
};

export { API_BASE_URL };
export default apiClient;
