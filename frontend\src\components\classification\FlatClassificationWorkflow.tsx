/**
 * FlatClassificationWorkflow.tsx
 * 
 * Complete flat classification workflow component connecting to backend flat engine.
 * Implements all 7 steps from data upload to deployment with production-ready features.
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  LayoutGrid, 
  Upload, 
  Settings, 
  Brain, 
  Zap, 
  BarChart3, 
  Download,
  CheckCircle2,
  ArrowRight,
  ArrowLeft,
  Play,
  Pause,
  AlertCircle,
  TrendingUp,
  Target,
  Clock
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";

// Import services
import { uploadFile, UploadedFile } from "@/services/fileUploadApi";
import { 
  startUniversalTraining, 
  startUniversalInference,
  getUniversalTaskStatus,
  UniversalTrainingRequest,
  UniversalInferenceRequest 
} from "@/services/universalApi";

// Import other classification components
import { TrainingProgressMonitor } from "./TrainingProgressMonitor";
import { EnhancedModelComparisonDashboard } from "./EnhancedModelComparisonDashboard";
import { ClassBalanceAnalyzer } from "./ClassBalanceAnalyzer";
import { UnifiedFileUploadZone } from "../UnifiedFileUploadZone";
import { FlatTrainingConfig, FlatTrainingConfig as FlatTrainingConfigType } from "./FlatTrainingConfig";
import { FlatModelManager, FlatModel } from "./FlatModelManager";
import { FlatResults } from "./FlatResults";
import { DeployStep } from "./DeployStep";

// Import unified data management
import { unifiedDataManager, DualDataUpload } from "@/services/unifiedDataManager";

interface FlatClassificationWorkflowProps {
  initialData?: UploadedFile;
  onComplete?: (results: any) => void;
}

interface TrainingResults {
  task_id: string;
  model_id: string;
  metrics: {
    accuracy: number;
    precision: number;
    recall: number;
    f1_score: number;
    confusion_matrix: number[][];
    class_report: any;
  };
  training_time: number;
  model_size: number;
}

interface ClassificationResults {
  predictions: Array<{
    text: string;
    predicted_class: string;
    confidence: number;
    probabilities: Record<string, number>;
  }>;
  summary: {
    total_predictions: number;
    avg_confidence: number;
    class_distribution: Record<string, number>;
  };
}

export const FlatClassificationWorkflow: React.FC<FlatClassificationWorkflowProps> = ({
  initialData,
  onComplete
}) => {
  const { toast } = useToast();
  
  // Step management
  const [currentStep, setCurrentStep] = useState(1);
  const [completedSteps, setCompletedSteps] = useState<number[]>([]);
  
  // Data management
  const [uploadedData, setUploadedData] = useState<UploadedFile | null>(initialData || null);
  const [dualData, setDualData] = useState<DualDataUpload | null>(null);
  const [categories, setCategories] = useState<string[]>([]);
  const [selectedTextColumn, setSelectedTextColumn] = useState<string>('');
  const [selectedLabelColumn, setSelectedLabelColumn] = useState<string>('');

  // Configuration
  const [trainingMethod, setTrainingMethod] = useState<'llm' | 'custom'>('llm');
  const [llmProvider, setLlmProvider] = useState<string>('openai');
  const [llmModel, setLlmModel] = useState<string>('gpt-3.5-turbo');
  const [customPrompt, setCustomPrompt] = useState<string>('');
  const [trainingConfig, setTrainingConfig] = useState<FlatTrainingConfigType | null>(null);

  // Model management
  const [selectedModel, setSelectedModel] = useState<FlatModel | null>(null);
  const [showModelManager, setShowModelManager] = useState(false);

  // Training state
  const [isTraining, setIsTraining] = useState(false);
  const [trainingProgress, setTrainingProgress] = useState(0);
  const [trainingResults, setTrainingResults] = useState<TrainingResults | null>(null);
  const [trainingTaskId, setTrainingTaskId] = useState<string>('');

  // Classification state
  const [classificationData, setClassificationData] = useState<UploadedFile | null>(null);
  const [isClassifying, setIsClassifying] = useState(false);
  const [classificationProgress, setClassificationProgress] = useState(0);
  const [classificationResults, setClassificationResults] = useState<ClassificationResults | null>(null);

  // Error handling
  const [error, setError] = useState<string | null>(null);

  const steps = [
    { id: 1, title: "Data Upload", icon: Upload, description: "Upload your dataset" },
    { id: 2, title: "Category Setup", icon: LayoutGrid, description: "Configure categories" },
    { id: 3, title: "Configuration", icon: Settings, description: "Set training parameters" },
    { id: 4, title: "Method Selection", icon: Brain, description: "Choose training approach" },
    { id: 5, title: "Training/Inference", icon: Zap, description: "Train model or run inference" },
    { id: 6, title: "Results", icon: BarChart3, description: "View results and metrics" },
    { id: 7, title: "Deploy", icon: Download, description: "Export and deploy model" }
  ];

  // Initialize categories from uploaded data
  useEffect(() => {
    if (uploadedData && selectedLabelColumn) {
      const uniqueCategories = [...new Set(
        uploadedData.preview?.map(row => row[selectedLabelColumn]).filter(Boolean)
      )];
      setCategories(uniqueCategories);
    }
  }, [uploadedData, selectedLabelColumn]);

  const handleStepComplete = (stepId: number) => {
    if (!completedSteps.includes(stepId)) {
      setCompletedSteps([...completedSteps, stepId]);
    }
  };

  const handleNext = () => {
    if (currentStep < steps.length) {
      handleStepComplete(currentStep);
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleDataUpload = async (file: File) => {
    try {
      const uploadedFile = await uploadFile(file);
      setUploadedData(uploadedFile);
      
      // Auto-select first text column
      if (uploadedFile.columns) {
        const textColumn = uploadedFile.columns.find(col => 
          col.toLowerCase().includes('text') || 
          col.toLowerCase().includes('content') ||
          col.toLowerCase().includes('message')
        ) || uploadedFile.columns[0];
        setSelectedTextColumn(textColumn);
        
        // Auto-select label column
        const labelColumn = uploadedFile.columns.find(col => 
          col.toLowerCase().includes('label') || 
          col.toLowerCase().includes('category') ||
          col.toLowerCase().includes('class')
        );
        if (labelColumn) {
          setSelectedLabelColumn(labelColumn);
        }
      }
      
      toast({
        title: "Data uploaded successfully",
        description: `${uploadedFile.filename} with ${uploadedFile.num_rows} rows`
      });
    } catch (error) {
      setError(`Upload failed: ${error}`);
      toast({
        title: "Upload failed",
        description: "Please try again with a valid CSV file",
        variant: "destructive"
      });
    }
  };

  // Dual data upload handler
  const handleDualDataUpload = (dualDataUpload: DualDataUpload) => {
    setDualData(dualDataUpload);
    setUploadedData(dualDataUpload.trainingData.fileInfo);

    // Auto-select columns from training data
    const trainingColumns = dualDataUpload.trainingData.fileInfo.columns || [];
    const textColumn = trainingColumns.find(col =>
      col.toLowerCase().includes('text') ||
      col.toLowerCase().includes('content') ||
      col.toLowerCase().includes('message')
    ) || trainingColumns[0];
    setSelectedTextColumn(textColumn);

    const labelColumn = trainingColumns.find(col =>
      col.toLowerCase().includes('label') ||
      col.toLowerCase().includes('category') ||
      col.toLowerCase().includes('class')
    );
    if (labelColumn) {
      setSelectedLabelColumn(labelColumn);
    }

    toast({
      title: "Dual data uploaded",
      description: `Training: ${dualDataUpload.trainingData.fileInfo.filename}, Classification: ${dualDataUpload.classificationData.fileInfo.filename}`
    });
  };

  // Training configuration handler
  const handleTrainingConfigSave = (config: FlatTrainingConfigType) => {
    setTrainingConfig(config);
    toast({
      title: "Configuration saved",
      description: "Flat classification training configuration has been saved"
    });
  };

  // Model management handlers
  const handleModelSelect = (model: FlatModel) => {
    setSelectedModel(model);
  };

  const handleModelUse = (modelId: string) => {
    setShowModelManager(false);
    toast({
      title: "Model selected",
      description: "Using existing model for classification"
    });
  };

  const handleModelDelete = (modelId: string) => {
    // Model deletion handled in FlatModelManager
  };

  const startTraining = async () => {
    if (!uploadedData || !selectedTextColumn || !selectedLabelColumn) {
      toast({
        title: "Missing configuration",
        description: "Please ensure data is uploaded and columns are selected",
        variant: "destructive"
      });
      return;
    }

    setIsTraining(true);
    setError(null);

    try {
      const trainingRequest: UniversalTrainingRequest = {
        file_id: uploadedData.file_id,
        classification_type: 'flat',
        model_type: trainingMethod,
        text_column: selectedTextColumn,
        label_columns: [selectedLabelColumn],
        llm_provider: trainingMethod === 'llm' ? llmProvider : undefined,
        llm_model: trainingMethod === 'llm' ? llmModel : undefined,
        custom_prompt: customPrompt || undefined,
        training_params: {
          // Basic parameters
          max_epochs: trainingConfig?.numEpochs || 3,
          batch_size: trainingConfig?.batchSize || 16,
          learning_rate: trainingConfig?.learningRate || 2e-5,

          // Enhanced flat parameters
          ...(trainingConfig && {
            model_name: trainingConfig.modelName,
            base_model: trainingConfig.baseModel,
            max_length: trainingConfig.maxLength,
            validation_split: trainingConfig.validationSplit,
            warmup_steps: trainingConfig.warmupSteps,
            weight_decay: trainingConfig.weightDecay,
            gradient_accumulation_steps: trainingConfig.gradientAccumulationSteps,
            use_unsloth: trainingConfig.useUnsloth,
            fp16: trainingConfig.fp16,
            gradient_checkpointing: trainingConfig.gradientCheckpointing,
            enable_early_stopping: trainingConfig.enableEarlyStopping,
            patience: trainingConfig.patience,
            min_delta: trainingConfig.minDelta,

            // Flat specific
            flat_config: {
              scalability_mode: trainingConfig.scalabilityMode,
              memory_optimization: trainingConfig.memoryOptimization,
              batch_processing: trainingConfig.batchProcessing,
              chunk_size: trainingConfig.chunkSize,
              parallel_processing: trainingConfig.parallelProcessing,
              disk_caching: trainingConfig.diskCaching,
              model_compression: trainingConfig.modelCompression,
              quantization: trainingConfig.quantization,
              pruning: trainingConfig.pruning,
              max_workers: trainingConfig.maxWorkers,
              cache_size: trainingConfig.cacheSize,
              streaming_threshold: trainingConfig.streamingThreshold
            }
          })
        },
        // Add dual data information if available
        ...(dualData && {
          dual_data_setup: true,
          classification_file_id: dualData.classificationData.fileInfo.file_id,
          training_file_id: dualData.trainingData.fileInfo.file_id
        })
      };

      const response = await startUniversalTraining(trainingRequest);
      setTrainingTaskId(response.task_id);
      
      // Start monitoring training progress
      monitorTrainingProgress(response.task_id);
      
      toast({
        title: "Training started",
        description: "Your model is being trained. This may take several minutes."
      });
    } catch (error) {
      setError(`Training failed: ${error}`);
      setIsTraining(false);
      toast({
        title: "Training failed",
        description: "Please check your configuration and try again",
        variant: "destructive"
      });
    }
  };

  const monitorTrainingProgress = async (taskId: string) => {
    const checkProgress = async () => {
      try {
        const status = await getUniversalTaskStatus(taskId);
        
        if (status.progress !== undefined) {
          setTrainingProgress(status.progress);
        }
        
        if (status.status === 'completed') {
          setIsTraining(false);
          setTrainingResults(status.result);
          handleStepComplete(5);
          toast({
            title: "Training completed",
            description: "Your model has been trained successfully!"
          });
        } else if (status.status === 'failed') {
          setIsTraining(false);
          setError(status.error || 'Training failed');
          toast({
            title: "Training failed",
            description: status.error || "Unknown error occurred",
            variant: "destructive"
          });
        } else {
          // Continue monitoring
          setTimeout(checkProgress, 2000);
        }
      } catch (error) {
        console.error('Error monitoring training:', error);
        setTimeout(checkProgress, 5000); // Retry after longer delay
      }
    };
    
    checkProgress();
  };

  const startClassification = async () => {
    if (!trainingResults || !classificationData) {
      toast({
        title: "Missing requirements",
        description: "Please ensure model is trained and classification data is uploaded",
        variant: "destructive"
      });
      return;
    }

    setIsClassifying(true);
    setError(null);

    try {
      const inferenceRequest: UniversalInferenceRequest = {
        model_id: trainingResults.model_id,
        file_id: classificationData.file_id,
        text_column: selectedTextColumn,
        classification_type: 'flat'
      };

      const results = await startUniversalInference(inferenceRequest);
      setClassificationResults(results);
      handleStepComplete(6);
      
      toast({
        title: "Classification completed",
        description: `Classified ${results.summary.total_predictions} texts successfully`
      });
    } catch (error) {
      setError(`Classification failed: ${error}`);
      toast({
        title: "Classification failed",
        description: "Please try again",
        variant: "destructive"
      });
    } finally {
      setIsClassifying(false);
    }
  };

  return (
    <div className="max-w-7xl mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center">
            <LayoutGrid className="w-6 h-6 text-primary" />
          </div>
          <div>
            <h1 className="text-3xl font-bold">Flat Classification Workflow</h1>
            <p className="text-muted-foreground">Non-hierarchical multi-class classification</p>
          </div>
        </div>
        <Badge variant="outline" className="px-3 py-1">
          Step {currentStep} of {steps.length}
        </Badge>
      </div>

      {/* Progress Bar */}
      <div className="space-y-2">
        <div className="flex justify-between text-sm text-muted-foreground">
          <span>Progress</span>
          <span>{Math.round((completedSteps.length / steps.length) * 100)}% Complete</span>
        </div>
        <Progress value={(completedSteps.length / steps.length) * 100} className="h-2" />
      </div>

      {/* Error Alert */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Steps Sidebar */}
        <div className="lg:col-span-1">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Workflow Steps</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              {steps.map((step) => {
                const Icon = step.icon;
                const isActive = currentStep === step.id;
                const isComplete = completedSteps.includes(step.id);
                
                return (
                  <div
                    key={step.id}
                    className={`flex items-center gap-3 p-3 rounded-lg transition-colors cursor-pointer ${
                      isActive ? 'bg-primary/10 border border-primary/20' : 
                      isComplete ? 'bg-green-50 dark:bg-green-950' : 'bg-muted/30'
                    }`}
                    onClick={() => setCurrentStep(step.id)}
                  >
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                      isActive ? 'bg-primary text-white' :
                      isComplete ? 'bg-green-500 text-white' : 'bg-muted'
                    }`}>
                      {isComplete ? (
                        <CheckCircle2 className="w-4 h-4" />
                      ) : (
                        <Icon className="w-4 h-4" />
                      )}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className={`font-medium text-sm ${
                        isActive ? 'text-primary' : 
                        isComplete ? 'text-green-600 dark:text-green-400' : 'text-muted-foreground'
                      }`}>
                        {step.title}
                      </div>
                      <div className="text-xs text-muted-foreground truncate">
                        {step.description}
                      </div>
                    </div>
                  </div>
                );
              })}
            </CardContent>
          </Card>
        </div>

        {/* Main Content */}
        <div className="lg:col-span-3">
          {/* Step 1: Data Upload */}
          {currentStep === 1 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Upload className="w-5 h-5" />
                  Data Upload
                </CardTitle>
                <CardDescription>
                  Upload your dataset for flat classification. The file should contain text data and category labels.
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <UnifiedFileUploadZone
                  onFileSelected={(fileId, fileInfo, purposes) => {
                    if (purposes.includes('training') && purposes.includes('classification')) {
                      // Single file for both purposes
                      setUploadedData(fileInfo);
                    } else {
                      // Check if we have dual data setup
                      const dualDataUpload = unifiedDataManager.getDualDataUpload();
                      if (dualDataUpload) {
                        handleDualDataUpload(dualDataUpload);
                      } else {
                        setUploadedData(fileInfo);
                      }
                    }
                  }}
                  requiredPurposes={['training']}
                  suggestedPurposes={['classification']}
                  allowMultiplePurposes={true}
                  showFileReuse={true}
                  title="Upload Flat Classification Data"
                  description="Upload your training data and optionally separate classification data"
                />

                {(uploadedData || dualData) && (
                  <div className="space-y-4">
                    {dualData ? (
                      <div className="space-y-3">
                        <div className="flex items-center justify-between p-4 bg-blue-50 dark:bg-blue-950 rounded-lg">
                          <div className="flex items-center gap-3">
                            <CheckCircle2 className="w-5 h-5 text-blue-500" />
                            <div>
                              <div className="font-medium">Training Data: {dualData.trainingData.fileInfo.filename}</div>
                              <div className="text-sm text-muted-foreground">
                                {dualData.trainingData.fileInfo.num_rows} rows, {dualData.trainingData.fileInfo.columns?.length} columns
                              </div>
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center justify-between p-4 bg-green-50 dark:bg-green-950 rounded-lg">
                          <div className="flex items-center gap-3">
                            <CheckCircle2 className="w-5 h-5 text-green-500" />
                            <div>
                              <div className="font-medium">Classification Data: {dualData.classificationData.fileInfo.filename}</div>
                              <div className="text-sm text-muted-foreground">
                                {dualData.classificationData.fileInfo.num_rows} rows, {dualData.classificationData.fileInfo.columns?.length} columns
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    ) : (
                      <div className="flex items-center justify-between p-4 bg-green-50 dark:bg-green-950 rounded-lg">
                        <div className="flex items-center gap-3">
                          <CheckCircle2 className="w-5 h-5 text-green-500" />
                          <div>
                            <div className="font-medium">{uploadedData?.filename}</div>
                            <div className="text-sm text-muted-foreground">
                              {uploadedData?.num_rows} rows, {uploadedData?.columns?.length} columns
                            </div>
                          </div>
                        </div>
                        <Button variant="outline" size="sm" onClick={() => setUploadedData(null)}>
                          Remove
                        </Button>
                      </div>
                    )}

                    {(uploadedData?.preview || dualData?.trainingData.fileInfo.preview) && (
                      <div className="border rounded-lg overflow-hidden">
                        <div className="bg-muted p-3 border-b">
                          <h4 className="font-medium">Training Data Preview</h4>
                        </div>
                        <div className="overflow-x-auto">
                          <table className="w-full text-sm">
                            <thead className="bg-muted/50">
                              <tr>
                                {(uploadedData?.columns || dualData?.trainingData.fileInfo.columns)?.map((col) => (
                                  <th key={col} className="text-left p-2 font-medium">
                                    {col}
                                  </th>
                                ))}
                              </tr>
                            </thead>
                            <tbody>
                              {(uploadedData?.preview || dualData?.trainingData.fileInfo.preview)?.slice(0, 5).map((row, idx) => (
                                <tr key={idx} className="border-t">
                                  {(uploadedData?.columns || dualData?.trainingData.fileInfo.columns)?.map((col) => (
                                    <td key={col} className="p-2 max-w-xs truncate">
                                      {row[col]}
                                    </td>
                                  ))}
                                </tr>
                              ))}
                            </tbody>
                          </table>
                        </div>
                      </div>
                    )}
                  </div>
                )}

                <div className="flex justify-end">
                  <Button
                    onClick={handleNext}
                    disabled={!uploadedData && !dualData}
                    className="flex items-center gap-2"
                  >
                    Next Step
                    <ArrowRight className="w-4 h-4" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Step 2: Category Setup */}
          {currentStep === 2 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <LayoutGrid className="w-5 h-5" />
                  Category Setup
                </CardTitle>
                <CardDescription>
                  Configure the categories for your flat classification model.
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Column Selection */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Text Column</label>
                    <select
                      value={selectedTextColumn}
                      onChange={(e) => setSelectedTextColumn(e.target.value)}
                      className="w-full p-2 border rounded-md"
                    >
                      <option value="">Select text column...</option>
                      {uploadedData?.columns?.map((col) => (
                        <option key={col} value={col}>{col}</option>
                      ))}
                    </select>
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium">Label Column</label>
                    <select
                      value={selectedLabelColumn}
                      onChange={(e) => setSelectedLabelColumn(e.target.value)}
                      className="w-full p-2 border rounded-md"
                    >
                      <option value="">Select label column...</option>
                      {uploadedData?.columns?.map((col) => (
                        <option key={col} value={col}>{col}</option>
                      ))}
                    </select>
                  </div>
                </div>

                {/* Categories Display */}
                {categories.length > 0 && (
                  <div className="space-y-3">
                    <h4 className="font-medium">Detected Categories ({categories.length})</h4>
                    <div className="flex flex-wrap gap-2">
                      {categories.map((category, index) => (
                        <Badge key={index} variant="secondary" className="px-3 py-1">
                          {category}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}

                {/* Class Balance Analysis */}
                {uploadedData && selectedLabelColumn && (
                  <ClassBalanceAnalyzer
                    data={uploadedData}
                    labelColumn={selectedLabelColumn}
                  />
                )}

                <div className="flex justify-between">
                  <Button variant="outline" onClick={handlePrevious}>
                    <ArrowLeft className="w-4 h-4 mr-2" />
                    Previous
                  </Button>
                  <Button
                    onClick={handleNext}
                    disabled={!selectedTextColumn || !selectedLabelColumn || categories.length === 0}
                  >
                    Next Step
                    <ArrowRight className="w-4 h-4 ml-2" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Step 3: Configuration */}
          {currentStep === 3 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Settings className="w-5 h-5" />
                  Configuration
                </CardTitle>
                <CardDescription>
                  Configure training parameters and model settings.
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <Tabs value={trainingMethod} onValueChange={(value) => setTrainingMethod(value as 'llm' | 'custom')}>
                  <TabsList className="grid w-full grid-cols-2">
                    <TabsTrigger value="llm">LLM Inference</TabsTrigger>
                    <TabsTrigger value="custom">Custom Training</TabsTrigger>
                  </TabsList>

                  <TabsContent value="llm" className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <label className="text-sm font-medium">LLM Provider</label>
                        <select
                          value={llmProvider}
                          onChange={(e) => setLlmProvider(e.target.value)}
                          className="w-full p-2 border rounded-md"
                        >
                          <option value="openai">OpenAI</option>
                          <option value="anthropic">Anthropic</option>
                          <option value="groq">Groq</option>
                          <option value="ollama">Ollama (Local)</option>
                        </select>
                      </div>

                      <div className="space-y-2">
                        <label className="text-sm font-medium">Model</label>
                        <select
                          value={llmModel}
                          onChange={(e) => setLlmModel(e.target.value)}
                          className="w-full p-2 border rounded-md"
                        >
                          {llmProvider === 'openai' && (
                            <>
                              <option value="gpt-3.5-turbo">GPT-3.5 Turbo</option>
                              <option value="gpt-4">GPT-4</option>
                              <option value="gpt-4-turbo">GPT-4 Turbo</option>
                            </>
                          )}
                          {llmProvider === 'anthropic' && (
                            <>
                              <option value="claude-3-haiku">Claude 3 Haiku</option>
                              <option value="claude-3-sonnet">Claude 3 Sonnet</option>
                              <option value="claude-3-opus">Claude 3 Opus</option>
                            </>
                          )}
                          {llmProvider === 'groq' && (
                            <>
                              <option value="llama2-70b">Llama 2 70B</option>
                              <option value="mixtral-8x7b">Mixtral 8x7B</option>
                            </>
                          )}
                          {llmProvider === 'ollama' && (
                            <>
                              <option value="llama2">Llama 2</option>
                              <option value="mistral">Mistral</option>
                              <option value="codellama">Code Llama</option>
                            </>
                          )}
                        </select>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <label className="text-sm font-medium">Custom Prompt (Optional)</label>
                      <textarea
                        value={customPrompt}
                        onChange={(e) => setCustomPrompt(e.target.value)}
                        placeholder="Enter custom classification prompt..."
                        className="w-full p-3 border rounded-md h-24 resize-none"
                      />
                      <p className="text-xs text-muted-foreground">
                        Leave empty to use the default prompt optimized for flat classification.
                      </p>
                    </div>
                  </TabsContent>

                  <TabsContent value="custom" className="space-y-4">
                    <Alert>
                      <AlertCircle className="h-4 w-4" />
                      <AlertDescription>
                        Custom training will fine-tune a transformer model on your data with scalability optimizations.
                        This provides better accuracy and handles large datasets efficiently.
                      </AlertDescription>
                    </Alert>

                    {/* Model Manager */}
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <h4 className="font-medium">Existing Models</h4>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setShowModelManager(!showModelManager)}
                        >
                          {showModelManager ? 'Hide' : 'Show'} Models
                        </Button>
                      </div>

                      {showModelManager && (
                        <FlatModelManager
                          onModelSelect={handleModelSelect}
                          onModelUse={handleModelUse}
                          onModelDelete={handleModelDelete}
                          selectedModelId={selectedModel?.id}
                        />
                      )}
                    </div>

                    {/* Training Configuration */}
                    <FlatTrainingConfig
                      onConfigChange={setTrainingConfig}
                      onSave={handleTrainingConfigSave}
                      initialConfig={trainingConfig || undefined}
                      userJourney="expert"
                      datasetSize={uploadedData?.num_rows || dualData?.trainingData.fileInfo.num_rows || 0}
                      estimatedTrainingTime="10-25 minutes"
                    />
                  </TabsContent>
                </Tabs>

                <div className="flex justify-between">
                  <Button variant="outline" onClick={handlePrevious}>
                    <ArrowLeft className="w-4 h-4 mr-2" />
                    Previous
                  </Button>
                  <Button
                    onClick={handleNext}
                    disabled={trainingMethod === 'custom' && !trainingConfig}
                  >
                    Continue to Training
                    <ArrowRight className="w-4 h-4 ml-2" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Step 4: Method Selection */}
          {currentStep === 4 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Brain className="w-5 h-5" />
                  Method Selection
                </CardTitle>
                <CardDescription>
                  Choose your classification approach and review the configuration.
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <Card className={`cursor-pointer transition-all ${trainingMethod === 'llm' ? 'ring-2 ring-primary' : ''}`}>
                    <CardContent className="p-6">
                      <div className="flex items-center gap-3 mb-3">
                        <div className="w-10 h-10 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center">
                          <Zap className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                        </div>
                        <div>
                          <h3 className="font-semibold">LLM Inference</h3>
                          <p className="text-sm text-muted-foreground">Fast, no training required</p>
                        </div>
                      </div>
                      <ul className="text-sm space-y-1 text-muted-foreground">
                        <li>• Immediate results</li>
                        <li>• Works with small datasets</li>
                        <li>• Customizable prompts</li>
                        <li>• Multiple LLM providers</li>
                      </ul>
                    </CardContent>
                  </Card>

                  <Card className={`cursor-pointer transition-all ${trainingMethod === 'custom' ? 'ring-2 ring-primary' : ''}`}>
                    <CardContent className="p-6">
                      <div className="flex items-center gap-3 mb-3">
                        <div className="w-10 h-10 rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center">
                          <Target className="w-5 h-5 text-green-600 dark:text-green-400" />
                        </div>
                        <div>
                          <h3 className="font-semibold">Custom Training</h3>
                          <p className="text-sm text-muted-foreground">High accuracy, domain-specific</p>
                        </div>
                      </div>
                      <ul className="text-sm space-y-1 text-muted-foreground">
                        <li>• Higher accuracy</li>
                        <li>• Domain adaptation</li>
                        <li>• Model ownership</li>
                        <li>• GPU acceleration</li>
                      </ul>
                    </CardContent>
                  </Card>
                </div>

                {/* Configuration Summary */}
                <div className="bg-muted/50 rounded-lg p-4 space-y-3">
                  <h4 className="font-medium">Configuration Summary</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-muted-foreground">Dataset:</span>
                      <span className="ml-2 font-medium">{uploadedData?.filename}</span>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Categories:</span>
                      <span className="ml-2 font-medium">{categories.length}</span>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Text Column:</span>
                      <span className="ml-2 font-medium">{selectedTextColumn}</span>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Label Column:</span>
                      <span className="ml-2 font-medium">{selectedLabelColumn}</span>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Method:</span>
                      <span className="ml-2 font-medium">
                        {trainingMethod === 'llm' ? `${llmProvider} ${llmModel}` : 'Custom Training'}
                      </span>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Rows:</span>
                      <span className="ml-2 font-medium">{uploadedData?.num_rows}</span>
                    </div>
                  </div>
                </div>

                <div className="flex justify-between">
                  <Button variant="outline" onClick={handlePrevious}>
                    <ArrowLeft className="w-4 h-4 mr-2" />
                    Previous
                  </Button>
                  <Button onClick={handleNext}>
                    Start {trainingMethod === 'llm' ? 'Inference' : 'Training'}
                    <ArrowRight className="w-4 h-4 ml-2" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Step 5: Training/Inference */}
          {currentStep === 5 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Zap className="w-5 h-5" />
                  {trainingMethod === 'llm' ? 'LLM Inference' : 'Model Training'}
                </CardTitle>
                <CardDescription>
                  {trainingMethod === 'llm'
                    ? 'Running classification using the selected LLM'
                    : 'Training your custom classification model'
                  }
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {!isTraining && !trainingResults ? (
                  <div className="text-center space-y-4">
                    <div className="w-16 h-16 rounded-full bg-primary/10 flex items-center justify-center mx-auto">
                      <Play className="w-8 h-8 text-primary" />
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold mb-2">
                        Ready to {trainingMethod === 'llm' ? 'Start Inference' : 'Begin Training'}
                      </h3>
                      <p className="text-muted-foreground mb-4">
                        {trainingMethod === 'llm'
                          ? 'This will classify your data using the selected LLM. Results will be available immediately.'
                          : 'This will train a custom model on your data. Training may take several minutes depending on dataset size.'
                        }
                      </p>
                      <Button onClick={startTraining} size="lg" className="px-8">
                        <Play className="w-4 h-4 mr-2" />
                        {trainingMethod === 'llm' ? 'Start Classification' : 'Start Training'}
                      </Button>
                    </div>
                  </div>
                ) : isTraining ? (
                  <div className="space-y-6">
                    <div className="text-center">
                      <div className="w-16 h-16 rounded-full bg-primary/10 flex items-center justify-center mx-auto mb-4">
                        <div className="animate-spin">
                          <Zap className="w-8 h-8 text-primary" />
                        </div>
                      </div>
                      <h3 className="text-lg font-semibold mb-2">
                        {trainingMethod === 'llm' ? 'Processing...' : 'Training in Progress'}
                      </h3>
                      <p className="text-muted-foreground">
                        {trainingMethod === 'llm'
                          ? 'Classifying your data using the LLM...'
                          : 'Your model is being trained. This may take several minutes.'
                        }
                      </p>
                    </div>

                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>Progress</span>
                        <span>{trainingProgress}%</span>
                      </div>
                      <Progress value={trainingProgress} className="h-2" />
                    </div>

                    {trainingTaskId && (
                      <TrainingProgressMonitor
                        taskId={trainingTaskId}
                        onProgress={setTrainingProgress}
                        onComplete={(results) => {
                          setTrainingResults(results);
                          setIsTraining(false);
                        }}
                      />
                    )}
                  </div>
                ) : trainingResults ? (
                  <div className="space-y-6">
                    <div className="text-center">
                      <div className="w-16 h-16 rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center mx-auto mb-4">
                        <CheckCircle2 className="w-8 h-8 text-green-600 dark:text-green-400" />
                      </div>
                      <h3 className="text-lg font-semibold mb-2">
                        {trainingMethod === 'llm' ? 'Classification Complete!' : 'Training Complete!'}
                      </h3>
                      <p className="text-muted-foreground">
                        {trainingMethod === 'llm'
                          ? 'Your data has been classified successfully.'
                          : 'Your model has been trained and is ready for use.'
                        }
                      </p>
                    </div>

                    {/* Training Results */}
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                      <div className="text-center p-4 bg-muted/50 rounded-lg">
                        <div className="text-2xl font-bold text-primary">
                          {(trainingResults.metrics.accuracy * 100).toFixed(1)}%
                        </div>
                        <div className="text-sm text-muted-foreground">Accuracy</div>
                      </div>
                      <div className="text-center p-4 bg-muted/50 rounded-lg">
                        <div className="text-2xl font-bold text-green-600">
                          {(trainingResults.metrics.f1_score * 100).toFixed(1)}%
                        </div>
                        <div className="text-sm text-muted-foreground">F1 Score</div>
                      </div>
                      <div className="text-center p-4 bg-muted/50 rounded-lg">
                        <div className="text-2xl font-bold text-blue-600">
                          {(trainingResults.metrics.precision * 100).toFixed(1)}%
                        </div>
                        <div className="text-sm text-muted-foreground">Precision</div>
                      </div>
                      <div className="text-center p-4 bg-muted/50 rounded-lg">
                        <div className="text-2xl font-bold text-purple-600">
                          {(trainingResults.metrics.recall * 100).toFixed(1)}%
                        </div>
                        <div className="text-sm text-muted-foreground">Recall</div>
                      </div>
                    </div>

                    {/* Training Time and Model Info */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="flex items-center gap-3 p-4 bg-muted/50 rounded-lg">
                        <Clock className="w-5 h-5 text-muted-foreground" />
                        <div>
                          <div className="font-medium">Training Time</div>
                          <div className="text-sm text-muted-foreground">
                            {Math.round(trainingResults.training_time / 60)} minutes
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center gap-3 p-4 bg-muted/50 rounded-lg">
                        <Target className="w-5 h-5 text-muted-foreground" />
                        <div>
                          <div className="font-medium">Model Size</div>
                          <div className="text-sm text-muted-foreground">
                            {(trainingResults.model_size / 1024 / 1024).toFixed(1)} MB
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ) : null}

                <div className="flex justify-between">
                  <Button variant="outline" onClick={handlePrevious} disabled={isTraining}>
                    <ArrowLeft className="w-4 h-4 mr-2" />
                    Previous
                  </Button>
                  <Button
                    onClick={handleNext}
                    disabled={!trainingResults}
                  >
                    View Results
                    <ArrowRight className="w-4 h-4 ml-2" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Step 6: Results */}
          {currentStep === 6 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="w-5 h-5" />
                  Results & Analysis
                </CardTitle>
                <CardDescription>
                  View detailed results and performance metrics for your classification model.
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {trainingResults && (
                  <FlatResults
                    results={{
                      accuracy: trainingResults.metrics.accuracy,
                      f1Score: trainingResults.metrics.f1_score,
                      precision: trainingResults.metrics.precision,
                      recall: trainingResults.metrics.recall,
                      performanceMetrics: {
                        trainingTime: trainingResults.training_time,
                        inferenceTime: 0.05, // Mock value
                        throughput: 20, // Mock value
                        memoryUsage: trainingResults.model_size / 1000, // Convert MB to GB
                        modelSize: trainingResults.model_size,
                        cpuUtilization: 75 // Mock value
                      },
                      scalabilityAnalysis: {
                        maxDatasetSize: trainingConfig?.scalabilityMode === 'large_dataset' ? 1000000 : 50000,
                        recommendedBatchSize: trainingConfig?.batchSize || 16,
                        memoryEfficiency: 0.85, // Mock value
                        scalabilityScore: trainingConfig?.scalabilityMode === 'large_dataset' ? 90 : 70,
                        bottlenecks: trainingConfig?.scalabilityMode === 'standard' && (uploadedData?.num_rows || 0) > 50000
                          ? ['Dataset size exceeds standard mode capacity']
                          : []
                      },
                      datasetComplexity: {
                        totalSamples: uploadedData?.num_rows || dualData?.trainingData.fileInfo.num_rows || 0,
                        avgTextLength: 150, // Mock value
                        vocabularySize: 10000, // Mock value
                        complexityScore: 65 // Mock value
                      },
                      optimizationRecommendations: [
                        {
                          type: 'performance',
                          title: 'Enable GPU Acceleration',
                          description: 'Use Unsloth for 2x faster training with NVIDIA GPUs',
                          impact: 'high',
                          effort: 'low'
                        },
                        {
                          type: 'scalability',
                          title: 'Increase Batch Processing',
                          description: 'Enable batch processing for better throughput on large datasets',
                          impact: 'medium',
                          effort: 'low'
                        }
                      ],
                      samplePredictions: classificationResults?.predictions?.slice(0, 10).map((pred: any) => ({
                        text: pred.text,
                        actualClass: pred.actual_class || 'Unknown',
                        predictedClass: pred.predicted_class,
                        confidence: pred.confidence,
                        processingTime: 0.02, // Mock value
                        correct: pred.actual_class === pred.predicted_class
                      }))
                    }}
                    onExport={(format) => {
                      toast({
                        title: "Export started",
                        description: `Exporting results in ${format} format`
                      });
                    }}
                    onRetrain={() => {
                      setCurrentStep(3);
                      setTrainingResults(null);
                      setClassificationResults(null);
                    }}
                    onOptimize={(recommendation) => {
                      toast({
                        title: "Optimization applied",
                        description: `Applied ${recommendation.title} optimization`
                      });
                    }}
                  />
                )}

                {/* Classification on New Data */}
                <div className="space-y-4">
                  <h4 className="font-medium">Classify New Data</h4>
                  <p className="text-sm text-muted-foreground">
                    Upload new data to classify using your trained model.
                  </p>

                  {!classificationData ? (
                    <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-6 text-center">
                      <Upload className="w-8 h-8 text-muted-foreground mx-auto mb-2" />
                      <p className="text-sm text-muted-foreground mb-3">
                        Upload data to classify (optional)
                      </p>
                      <input
                        type="file"
                        accept=".csv,.xlsx,.xls"
                        onChange={(e) => {
                          const file = e.target.files?.[0];
                          if (file) {
                            uploadFile(file).then(setClassificationData);
                          }
                        }}
                        className="hidden"
                        id="classification-upload"
                      />
                      <label htmlFor="classification-upload">
                        <Button variant="outline" asChild>
                          <span>Choose File</span>
                        </Button>
                      </label>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      <div className="flex items-center justify-between p-3 bg-blue-50 dark:bg-blue-950 rounded-lg">
                        <div className="flex items-center gap-2">
                          <CheckCircle2 className="w-4 h-4 text-blue-500" />
                          <span className="font-medium">{classificationData.filename}</span>
                          <Badge variant="secondary">
                            {classificationData.num_rows} rows
                          </Badge>
                        </div>
                        <Button
                          onClick={startClassification}
                          disabled={isClassifying}
                          size="sm"
                        >
                          {isClassifying ? (
                            <>
                              <div className="animate-spin w-4 h-4 mr-2">
                                <Zap className="w-4 h-4" />
                              </div>
                              Classifying...
                            </>
                          ) : (
                            <>
                              <Play className="w-4 h-4 mr-2" />
                              Classify
                            </>
                          )}
                        </Button>
                      </div>

                      {classificationResults && (
                        <div className="space-y-4">
                          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div className="text-center p-4 bg-muted/50 rounded-lg">
                              <div className="text-2xl font-bold">
                                {classificationResults.summary.total_predictions}
                              </div>
                              <div className="text-sm text-muted-foreground">Total Predictions</div>
                            </div>
                            <div className="text-center p-4 bg-muted/50 rounded-lg">
                              <div className="text-2xl font-bold text-green-600">
                                {(classificationResults.summary.avg_confidence * 100).toFixed(1)}%
                              </div>
                              <div className="text-sm text-muted-foreground">Avg Confidence</div>
                            </div>
                            <div className="text-center p-4 bg-muted/50 rounded-lg">
                              <div className="text-2xl font-bold text-blue-600">
                                {Object.keys(classificationResults.summary.class_distribution).length}
                              </div>
                              <div className="text-sm text-muted-foreground">Classes Found</div>
                            </div>
                          </div>

                          {/* Results Table Preview */}
                          <div className="border rounded-lg overflow-hidden">
                            <div className="bg-muted p-3 border-b">
                              <h5 className="font-medium">Classification Results Preview</h5>
                            </div>
                            <div className="overflow-x-auto">
                              <table className="w-full text-sm">
                                <thead className="bg-muted/50">
                                  <tr>
                                    <th className="text-left p-2">Text</th>
                                    <th className="text-left p-2">Predicted Class</th>
                                    <th className="text-left p-2">Confidence</th>
                                  </tr>
                                </thead>
                                <tbody>
                                  {classificationResults.predictions.slice(0, 5).map((pred, idx) => (
                                    <tr key={idx} className="border-t">
                                      <td className="p-2 max-w-xs truncate">{pred.text}</td>
                                      <td className="p-2">
                                        <Badge variant="secondary">{pred.predicted_class}</Badge>
                                      </td>
                                      <td className="p-2">
                                        <span className={`font-medium ${
                                          pred.confidence > 0.8 ? 'text-green-600' :
                                          pred.confidence > 0.6 ? 'text-yellow-600' : 'text-red-600'
                                        }`}>
                                          {(pred.confidence * 100).toFixed(1)}%
                                        </span>
                                      </td>
                                    </tr>
                                  ))}
                                </tbody>
                              </table>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  )}
                </div>

                <div className="flex justify-between">
                  <Button variant="outline" onClick={handlePrevious}>
                    <ArrowLeft className="w-4 h-4 mr-2" />
                    Previous
                  </Button>
                  <Button onClick={handleNext}>
                    Deploy Model
                    <ArrowRight className="w-4 h-4 ml-2" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Step 7: Deploy */}
          {currentStep === 7 && trainingResults && (
            <DeployStep
              modelId={trainingResults.model_id}
              modelName={trainingConfig?.modelName || `Flat Classification Model ${trainingResults.task_id.slice(-8)}`}
              classificationType="flat"
              trainingMetrics={{
                accuracy: trainingResults.metrics.accuracy,
                f1_score: trainingResults.metrics.f1_score,
                precision: trainingResults.metrics.precision,
                recall: trainingResults.metrics.recall,
                training_time: trainingResults.training_time,
                model_size_mb: trainingResults.model_size
              }}
              labels={categories}
              userLicense={{ type: 'professional', features: [], limits: {} }}
              onComplete={(deploymentInfo) => {
                console.log('Flat classification deployment completed:', deploymentInfo);
                if (onComplete) {
                  onComplete({
                    type: 'flat',
                    trainingResults,
                    classificationResults,
                    deploymentInfo
                  });
                }
              }}
              onBack={() => setCurrentStep(6)}
            />
          )}
        </div>
      </div>
    </div>
  );
};

export default FlatClassificationWorkflow;
