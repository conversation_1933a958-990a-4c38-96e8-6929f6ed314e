"""
Email utilities for the ClassyWeb application.
"""
import logging
import smtplib
from datetime import datetime
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from typing import List, Optional

from .. import config

# Set up logging
logger = logging.getLogger(__name__)

def send_email(
    to_email: str,
    subject: str,
    html_content: str,
    text_content: Optional[str] = None,
    cc: Optional[List[str]] = None,
    bcc: Optional[List[str]] = None
) -> bool:
    """
    Send an email using SMTP.

    Args:
        to_email: Recipient email address
        subject: Email subject
        html_content: HTML content of the email
        text_content: Plain text content of the email (optional)
        cc: List of CC recipients (optional)
        bcc: List of BCC recipients (optional)

    Returns:
        True if the email was sent successfully, False otherwise
    """
    if not config.EMAIL_ENABLED:
        logger.warning("Email sending is disabled. Set EMAIL_ENABLED=True to enable.")
        return False

    if not config.SMTP_USERNAME or not config.SMTP_PASSWORD:
        logger.error("SMTP credentials not configured. Check your environment variables.")
        return False

    try:
        # Create message
        msg = MIMEMultipart('alternative')
        msg['Subject'] = subject
        msg['From'] = config.EMAIL_SENDER
        msg['To'] = to_email

        # Add CC recipients
        if cc:
            msg['Cc'] = ', '.join(cc)

        # Set recipients list for sending
        recipients = [to_email]
        if cc:
            recipients.extend(cc)
        if bcc:
            recipients.extend(bcc)

        # Add text part
        if text_content:
            msg.attach(MIMEText(text_content, 'plain'))

        # Add HTML part
        msg.attach(MIMEText(html_content, 'html'))

        # Connect to SMTP server
        server = smtplib.SMTP(config.SMTP_SERVER, config.SMTP_PORT)
        server.ehlo()

        # Use TLS if configured
        if config.SMTP_TLS:
            server.starttls()
            server.ehlo()

        # Login and send
        server.login(config.SMTP_USERNAME, config.SMTP_PASSWORD)
        server.sendmail(config.EMAIL_SENDER, recipients, msg.as_string())
        server.quit()

        logger.info(f"Email sent successfully to {to_email}")
        return True

    except Exception as e:
        logger.error(f"Failed to send email: {e}", exc_info=True)
        return False

def send_verification_email(to_email: str, verification_token: str) -> bool:
    """
    Send an email verification email.

    Args:
        to_email: Recipient email address
        verification_token: Verification token

    Returns:
        True if the email was sent successfully, False otherwise
    """
    subject = "Verify your ClassyWeb account"
    verification_url = f"http://localhost:5173/verify-email?token={verification_token}"

    html_content = f"""
    <html>
    <head>
        <style>
            body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
            .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
            .header {{ background-color: #4a69bd; color: white; padding: 10px 20px; text-align: center; }}
            .content {{ padding: 20px; background-color: #f9f9f9; }}
            .button {{ display: inline-block; padding: 10px 20px; background-color: #4a69bd; color: white;
                      text-decoration: none; border-radius: 4px; margin: 20px 0; }}
            .footer {{ text-align: center; margin-top: 20px; font-size: 12px; color: #777; }}
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>ClassyWeb</h1>
            </div>
            <div class="content">
                <h2>Verify Your Email Address</h2>
                <p>Thank you for signing up for ClassyWeb. Please verify your email address by clicking the button below:</p>
                <p><a href="{verification_url}" class="button">Verify Email</a></p>
                <p>Or copy and paste this link into your browser:</p>
                <p>{verification_url}</p>
                <p>This link will expire in {config.VERIFICATION_TOKEN_EXPIRE_HOURS} hours.</p>
                <p>If you did not sign up for ClassyWeb, please ignore this email.</p>
            </div>
            <div class="footer">
                <p>&copy; {datetime.now().year} ClassyWeb. All rights reserved.</p>
            </div>
        </div>
    </body>
    </html>
    """

    text_content = f"""
    Verify Your ClassyWeb Account

    Thank you for signing up for ClassyWeb. Please verify your email address by visiting the link below:

    {verification_url}

    This link will expire in {config.VERIFICATION_TOKEN_EXPIRE_HOURS} hours.

    If you did not sign up for ClassyWeb, please ignore this email.
    """

    return send_email(to_email, subject, html_content, text_content)

def send_password_reset_email(to_email: str, reset_token: str) -> bool:
    """
    Send a password reset email.

    Args:
        to_email: Recipient email address
        reset_token: Password reset token

    Returns:
        True if the email was sent successfully, False otherwise
    """
    subject = "Reset your ClassyWeb password"
    reset_url = f"http://localhost:5173/reset-password?token={reset_token}"

    html_content = f"""
    <html>
    <head>
        <style>
            body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
            .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
            .header {{ background-color: #4a69bd; color: white; padding: 10px 20px; text-align: center; }}
            .content {{ padding: 20px; background-color: #f9f9f9; }}
            .button {{ display: inline-block; padding: 10px 20px; background-color: #4a69bd; color: white;
                      text-decoration: none; border-radius: 4px; margin: 20px 0; }}
            .footer {{ text-align: center; margin-top: 20px; font-size: 12px; color: #777; }}
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>ClassyWeb</h1>
            </div>
            <div class="content">
                <h2>Reset Your Password</h2>
                <p>We received a request to reset your password. Click the button below to create a new password:</p>
                <p><a href="{reset_url}" class="button">Reset Password</a></p>
                <p>Or copy and paste this link into your browser:</p>
                <p>{reset_url}</p>
                <p>This link will expire in {config.RESET_TOKEN_EXPIRE_HOURS} hour.</p>
                <p>If you did not request a password reset, please ignore this email.</p>
            </div>
            <div class="footer">
                <p>&copy; {datetime.now().year} ClassyWeb. All rights reserved.</p>
            </div>
        </div>
    </body>
    </html>
    """

    text_content = f"""
    Reset Your ClassyWeb Password

    We received a request to reset your password. Please visit the link below to create a new password:

    {reset_url}

    This link will expire in {config.RESET_TOKEN_EXPIRE_HOURS} hour.

    If you did not request a password reset, please ignore this email.
    """

    return send_email(to_email, subject, html_content, text_content)
