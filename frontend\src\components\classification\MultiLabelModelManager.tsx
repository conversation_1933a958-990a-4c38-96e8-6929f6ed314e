/**
 * MultiLabelModelManager.tsx
 * 
 * Model management system for multi-label classification models.
 * Provides model listing, metadata display, reuse functionality, and performance tracking.
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Separator } from "@/components/ui/separator";
import { useToast } from "@/hooks/use-toast";
import {
  Search,
  Filter,
  MoreVertical,
  Play,
  Download,
  Trash2,
  Eye,
  Copy,
  Star,
  Clock,
  Target,
  BarChart3,
  Tags,
  Network,
  TrendingUp,
  CheckCircle2,
  AlertCircle,
  Info,
  Loader2
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { formatDistanceToNow } from 'date-fns';

export interface MultiLabelModel {
  id: string;
  name: string;
  baseModel: string;
  status: 'training' | 'completed' | 'failed' | 'deployed';
  createdAt: string;
  updatedAt: string;
  
  // Multi-label specific metadata
  labelCount: number;
  labels: string[];
  labelThresholds: Record<string, number>;
  correlationAnalysis: boolean;
  
  // Training configuration
  trainingConfig: {
    numEpochs: number;
    batchSize: number;
    learningRate: number;
    lossFunction: string;
    thresholdOptimization: string;
  };
  
  // Performance metrics
  metrics: {
    hamming_loss?: number;
    jaccard_score?: number;
    f1_macro?: number;
    f1_micro?: number;
    subset_accuracy?: number;
    label_ranking_loss?: number;
  };
  
  // Training metadata
  trainingTime?: number;
  modelSize?: number;
  datasetSize?: number;
  
  // Deployment info
  deploymentInfo?: {
    endpoint?: string;
    status: 'active' | 'inactive';
    lastUsed?: string;
  };
}

interface MultiLabelModelManagerProps {
  onModelSelect: (model: MultiLabelModel) => void;
  onModelUse: (modelId: string) => void;
  onModelDelete: (modelId: string) => void;
  selectedModelId?: string;
  showActions?: boolean;
}

export const MultiLabelModelManager: React.FC<MultiLabelModelManagerProps> = ({
  onModelSelect,
  onModelUse,
  onModelDelete,
  selectedModelId,
  showActions = true
}) => {
  const { toast } = useToast();
  
  // State
  const [models, setModels] = useState<MultiLabelModel[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [sortBy, setSortBy] = useState<string>('created_desc');

  // Load models on component mount
  useEffect(() => {
    loadModels();
  }, []);

  const loadModels = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Mock data for now - in real implementation, this would call an API
      const mockModels: MultiLabelModel[] = [
        {
          id: 'ml-model-1',
          name: 'Document Classifier v1',
          baseModel: 'distilbert-base-uncased',
          status: 'completed',
          createdAt: '2024-01-15T10:30:00Z',
          updatedAt: '2024-01-15T11:45:00Z',
          labelCount: 8,
          labels: ['technology', 'business', 'science', 'health', 'sports', 'entertainment', 'politics', 'education'],
          labelThresholds: {
            'technology': 0.6,
            'business': 0.5,
            'science': 0.7,
            'health': 0.55,
            'sports': 0.45,
            'entertainment': 0.5,
            'politics': 0.65,
            'education': 0.6
          },
          correlationAnalysis: true,
          trainingConfig: {
            numEpochs: 3,
            batchSize: 16,
            learningRate: 2e-5,
            lossFunction: 'binary_crossentropy',
            thresholdOptimization: 'per-label'
          },
          metrics: {
            hamming_loss: 0.12,
            jaccard_score: 0.78,
            f1_macro: 0.83,
            f1_micro: 0.87,
            subset_accuracy: 0.65,
            label_ranking_loss: 0.08
          },
          trainingTime: 1800, // 30 minutes
          modelSize: 256, // MB
          datasetSize: 10000,
          deploymentInfo: {
            endpoint: 'https://api.classyweb.com/models/ml-model-1',
            status: 'active',
            lastUsed: '2024-01-20T14:30:00Z'
          }
        },
        {
          id: 'ml-model-2',
          name: 'Product Tagger v2',
          baseModel: 'roberta-base',
          status: 'training',
          createdAt: '2024-01-20T09:15:00Z',
          updatedAt: '2024-01-20T09:15:00Z',
          labelCount: 12,
          labels: ['electronics', 'clothing', 'books', 'home', 'sports', 'toys', 'automotive', 'beauty', 'health', 'food', 'garden', 'tools'],
          labelThresholds: {},
          correlationAnalysis: true,
          trainingConfig: {
            numEpochs: 5,
            batchSize: 32,
            learningRate: 1e-5,
            lossFunction: 'focal_loss',
            thresholdOptimization: 'adaptive'
          },
          metrics: {},
          trainingTime: undefined,
          modelSize: undefined,
          datasetSize: 25000
        },
        {
          id: 'ml-model-3',
          name: 'Content Moderator',
          baseModel: 'bert-base-uncased',
          status: 'failed',
          createdAt: '2024-01-18T16:20:00Z',
          updatedAt: '2024-01-18T17:30:00Z',
          labelCount: 5,
          labels: ['spam', 'hate_speech', 'violence', 'adult_content', 'misinformation'],
          labelThresholds: {},
          correlationAnalysis: false,
          trainingConfig: {
            numEpochs: 4,
            batchSize: 24,
            learningRate: 3e-5,
            lossFunction: 'asymmetric_loss',
            thresholdOptimization: 'global'
          },
          metrics: {},
          trainingTime: undefined,
          modelSize: undefined,
          datasetSize: 5000
        }
      ];

      setModels(mockModels);
    } catch (err: any) {
      console.error('Failed to load models:', err);
      setError(err.message || 'Failed to load multi-label models');
      toast({
        title: "Error loading models",
        description: err.message || 'Failed to load multi-label models',
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteModel = async (modelId: string) => {
    try {
      // In real implementation, this would call an API
      setModels(prev => prev.filter(model => model.id !== modelId));
      onModelDelete(modelId);
      
      toast({
        title: "Model deleted",
        description: "Multi-label model has been deleted successfully"
      });
    } catch (error: any) {
      toast({
        title: "Delete failed",
        description: error.message || "Failed to delete model",
        variant: "destructive"
      });
    }
  };

  const handleUseModel = (model: MultiLabelModel) => {
    onModelUse(model.id);
    toast({
      title: "Model selected",
      description: `Using model: ${model.name}`
    });
  };

  // Filter and sort models
  const filteredModels = models
    .filter(model => {
      const matchesSearch = model.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           model.labels.some(label => label.toLowerCase().includes(searchTerm.toLowerCase()));
      const matchesStatus = statusFilter === 'all' || model.status === statusFilter;
      return matchesSearch && matchesStatus;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'created_desc':
          return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
        case 'created_asc':
          return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
        case 'name_asc':
          return a.name.localeCompare(b.name);
        case 'name_desc':
          return b.name.localeCompare(a.name);
        case 'performance':
          return (b.metrics.f1_macro || 0) - (a.metrics.f1_macro || 0);
        default:
          return 0;
      }
    });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'training':
        return 'bg-blue-100 text-blue-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      case 'deployed':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle2 className="w-4 h-4" />;
      case 'training':
        return <Loader2 className="w-4 h-4 animate-spin" />;
      case 'failed':
        return <AlertCircle className="w-4 h-4" />;
      case 'deployed':
        return <TrendingUp className="w-4 h-4" />;
      default:
        return <Info className="w-4 h-4" />;
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <Loader2 className="w-6 h-6 animate-spin mr-2" />
          Loading multi-label models...
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="py-8">
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Multi-Label Model Manager</h2>
          <p className="text-muted-foreground">
            Manage and reuse your trained multi-label classification models
          </p>
        </div>
        <Badge variant="secondary" className="flex items-center gap-2">
          <Tags className="w-4 h-4" />
          {models.length} models
        </Badge>
      </div>

      {/* Filters and Search */}
      <Card>
        <CardContent className="py-4">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                <Input
                  placeholder="Search models or labels..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            
            <div className="flex gap-2">
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-32">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="completed">Completed</SelectItem>
                  <SelectItem value="training">Training</SelectItem>
                  <SelectItem value="failed">Failed</SelectItem>
                  <SelectItem value="deployed">Deployed</SelectItem>
                </SelectContent>
              </Select>
              
              <Select value={sortBy} onValueChange={setSortBy}>
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="Sort by" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="created_desc">Newest First</SelectItem>
                  <SelectItem value="created_asc">Oldest First</SelectItem>
                  <SelectItem value="name_asc">Name A-Z</SelectItem>
                  <SelectItem value="name_desc">Name Z-A</SelectItem>
                  <SelectItem value="performance">Best Performance</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Models List */}
      <div className="space-y-4">
        {filteredModels.length === 0 ? (
          <Card>
            <CardContent className="py-8 text-center">
              <Tags className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">No models found</h3>
              <p className="text-muted-foreground">
                {searchTerm || statusFilter !== 'all' 
                  ? 'Try adjusting your search or filters'
                  : 'Train your first multi-label classification model to get started'
                }
              </p>
            </CardContent>
          </Card>
        ) : (
          filteredModels.map((model) => (
            <Card 
              key={model.id} 
              className={`transition-colors ${
                selectedModelId === model.id ? 'ring-2 ring-primary' : ''
              }`}
            >
              <CardContent className="p-6">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <h3 className="text-lg font-semibold">{model.name}</h3>
                      <Badge className={getStatusColor(model.status)}>
                        {getStatusIcon(model.status)}
                        <span className="ml-1 capitalize">{model.status}</span>
                      </Badge>
                      {model.deploymentInfo?.status === 'active' && (
                        <Badge variant="outline" className="text-green-600">
                          <TrendingUp className="w-3 h-3 mr-1" />
                          Deployed
                        </Badge>
                      )}
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
                      <div className="space-y-1">
                        <div className="text-sm text-muted-foreground">Base Model</div>
                        <div className="font-medium">{model.baseModel}</div>
                      </div>
                      
                      <div className="space-y-1">
                        <div className="text-sm text-muted-foreground">Labels</div>
                        <div className="font-medium">{model.labelCount} labels</div>
                      </div>
                      
                      <div className="space-y-1">
                        <div className="text-sm text-muted-foreground">Created</div>
                        <div className="font-medium">
                          {formatDistanceToNow(new Date(model.createdAt), { addSuffix: true })}
                        </div>
                      </div>
                      
                      <div className="space-y-1">
                        <div className="text-sm text-muted-foreground">Dataset Size</div>
                        <div className="font-medium">
                          {model.datasetSize?.toLocaleString() || 'N/A'} samples
                        </div>
                      </div>
                    </div>

                    {/* Performance Metrics */}
                    {model.status === 'completed' && model.metrics && (
                      <div className="grid grid-cols-2 md:grid-cols-5 gap-4 mb-4 p-3 bg-muted/30 rounded-lg">
                        <div className="text-center">
                          <div className="text-sm text-muted-foreground">F1 Macro</div>
                          <div className="font-semibold text-blue-600">
                            {(model.metrics.f1_macro || 0).toFixed(3)}
                          </div>
                        </div>
                        <div className="text-center">
                          <div className="text-sm text-muted-foreground">F1 Micro</div>
                          <div className="font-semibold text-green-600">
                            {(model.metrics.f1_micro || 0).toFixed(3)}
                          </div>
                        </div>
                        <div className="text-center">
                          <div className="text-sm text-muted-foreground">Jaccard</div>
                          <div className="font-semibold text-purple-600">
                            {(model.metrics.jaccard_score || 0).toFixed(3)}
                          </div>
                        </div>
                        <div className="text-center">
                          <div className="text-sm text-muted-foreground">Hamming Loss</div>
                          <div className="font-semibold text-orange-600">
                            {(model.metrics.hamming_loss || 0).toFixed(3)}
                          </div>
                        </div>
                        <div className="text-center">
                          <div className="text-sm text-muted-foreground">Subset Acc.</div>
                          <div className="font-semibold text-teal-600">
                            {(model.metrics.subset_accuracy || 0).toFixed(3)}
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Labels */}
                    <div className="mb-4">
                      <div className="text-sm text-muted-foreground mb-2">Labels:</div>
                      <div className="flex flex-wrap gap-1">
                        {model.labels.slice(0, 8).map(label => (
                          <Badge key={label} variant="outline" className="text-xs">
                            {label}
                          </Badge>
                        ))}
                        {model.labels.length > 8 && (
                          <Badge variant="outline" className="text-xs">
                            +{model.labels.length - 8} more
                          </Badge>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Actions */}
                  {showActions && (
                    <div className="flex items-center gap-2">
                      {model.status === 'completed' && (
                        <Button
                          size="sm"
                          onClick={() => handleUseModel(model)}
                          className="flex items-center gap-2"
                        >
                          <Play className="w-4 h-4" />
                          Use Model
                        </Button>
                      )}
                      
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="outline" size="sm">
                            <MoreVertical className="w-4 h-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => onModelSelect(model)}>
                            <Eye className="w-4 h-4 mr-2" />
                            View Details
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            <Copy className="w-4 h-4 mr-2" />
                            Duplicate
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            <Download className="w-4 h-4 mr-2" />
                            Export
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem 
                            onClick={() => handleDeleteModel(model.id)}
                            className="text-red-600"
                          >
                            <Trash2 className="w-4 h-4 mr-2" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>
    </div>
  );
};
