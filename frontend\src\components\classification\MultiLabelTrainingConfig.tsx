/**
 * MultiLabelTrainingConfig.tsx
 * 
 * Advanced training configuration component for multi-label classification.
 * Includes label correlation analysis, per-label threshold optimization,
 * loss function selection, and multi-label specific parameters.
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Slider } from "@/components/ui/slider";
import { Switch } from "@/components/ui/switch";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Separator } from "@/components/ui/separator";
import { useToast } from "@/hooks/use-toast";
import {
  Settings,
  Target,
  Network,
  TrendingUp,
  Zap,
  Brain,
  BarChart3,
  Info,
  AlertCircle,
  CheckCircle2,
  Sliders,
  Activity
} from "lucide-react";

export interface MultiLabelTrainingConfig {
  // Base training parameters
  modelName: string;
  baseModel: string;
  maxLength: number;
  
  // Training parameters
  numEpochs: number;
  batchSize: number;
  learningRate: number;
  validationSplit: number;
  
  // Advanced parameters
  warmupSteps: number;
  weightDecay: number;
  gradientAccumulationSteps: number;
  
  // Hardware optimization
  useUnsloth: boolean;
  fp16: boolean;
  gradientCheckpointing: boolean;
  
  // Early stopping
  enableEarlyStopping: boolean;
  patience: number;
  minDelta: number;
  
  // Multi-label specific parameters
  labelThresholds: Record<string, number>;
  correlationAnalysis: boolean;
  thresholdOptimization: 'global' | 'per-label' | 'adaptive';
  lossFunction: 'binary_crossentropy' | 'focal_loss' | 'asymmetric_loss';
  classWeights: 'balanced' | 'custom' | 'none';
  
  // Advanced multi-label features
  labelSmoothing: number;
  negativeDownsampling: number;
  hierarchicalConstraints: boolean;
}

interface MultiLabelTrainingConfigProps {
  onConfigChange: (config: MultiLabelTrainingConfig) => void;
  onSave: (config: MultiLabelTrainingConfig) => void;
  initialConfig?: Partial<MultiLabelTrainingConfig>;
  userJourney: 'beginner' | 'expert';
  detectedLabels: string[];
  labelCorrelations?: Record<string, string[]>;
  estimatedTrainingTime?: string;
}

const DEFAULT_CONFIG: MultiLabelTrainingConfig = {
  // Base parameters
  modelName: "multi-label-classifier",
  baseModel: "distilbert-base-uncased",
  maxLength: 512,
  
  // Training parameters
  numEpochs: 3,
  batchSize: 16,
  learningRate: 2e-5,
  validationSplit: 0.2,
  
  // Advanced parameters
  warmupSteps: 500,
  weightDecay: 0.01,
  gradientAccumulationSteps: 1,
  
  // Hardware optimization
  useUnsloth: true,
  fp16: true,
  gradientCheckpointing: true,
  
  // Early stopping
  enableEarlyStopping: true,
  patience: 2,
  minDelta: 0.001,
  
  // Multi-label specific
  labelThresholds: {},
  correlationAnalysis: true,
  thresholdOptimization: 'per-label',
  lossFunction: 'binary_crossentropy',
  classWeights: 'balanced',
  
  // Advanced multi-label features
  labelSmoothing: 0.1,
  negativeDownsampling: 0.1,
  hierarchicalConstraints: false
};

export const MultiLabelTrainingConfig: React.FC<MultiLabelTrainingConfigProps> = ({
  onConfigChange,
  onSave,
  initialConfig,
  userJourney,
  detectedLabels,
  labelCorrelations = {},
  estimatedTrainingTime
}) => {
  const { toast } = useToast();
  const [config, setConfig] = useState<MultiLabelTrainingConfig>({
    ...DEFAULT_CONFIG,
    ...initialConfig,
    labelThresholds: initialConfig?.labelThresholds || 
      detectedLabels.reduce((acc, label) => ({ ...acc, [label]: 0.5 }), {})
  });

  const [activeTab, setActiveTab] = useState('basic');
  const [isAdvancedMode, setIsAdvancedMode] = useState(userJourney === 'expert');

  useEffect(() => {
    onConfigChange(config);
  }, [config, onConfigChange]);

  const updateConfig = (updates: Partial<MultiLabelTrainingConfig>) => {
    setConfig(prev => ({ ...prev, ...updates }));
  };

  const updateLabelThreshold = (label: string, threshold: number) => {
    setConfig(prev => ({
      ...prev,
      labelThresholds: {
        ...prev.labelThresholds,
        [label]: threshold
      }
    }));
  };

  const handleSave = () => {
    // Validate configuration
    const errors: string[] = [];
    
    if (!config.modelName.trim()) {
      errors.push("Model name is required");
    }
    
    if (config.numEpochs < 1 || config.numEpochs > 100) {
      errors.push("Number of epochs must be between 1 and 100");
    }
    
    if (config.batchSize < 1 || config.batchSize > 128) {
      errors.push("Batch size must be between 1 and 128");
    }
    
    if (config.learningRate <= 0 || config.learningRate > 1) {
      errors.push("Learning rate must be between 0 and 1");
    }

    if (errors.length > 0) {
      toast({
        title: "Configuration Error",
        description: errors.join(", "),
        variant: "destructive"
      });
      return;
    }

    onSave(config);
    toast({
      title: "Configuration Saved",
      description: "Multi-label training configuration has been saved successfully"
    });
  };

  const getModelOptions = () => [
    { value: "distilbert-base-uncased", label: "DistilBERT Base (Fast)", description: "Lightweight and fast" },
    { value: "bert-base-uncased", label: "BERT Base", description: "Balanced performance" },
    { value: "roberta-base", label: "RoBERTa Base", description: "Better performance" },
    { value: "albert-base-v2", label: "ALBERT Base", description: "Memory efficient" },
    { value: "electra-base-discriminator", label: "ELECTRA Base", description: "High accuracy" }
  ];

  const getLossFunctionInfo = (lossFunction: string) => {
    const info = {
      'binary_crossentropy': {
        description: "Standard binary cross-entropy for each label",
        bestFor: "Balanced datasets with independent labels"
      },
      'focal_loss': {
        description: "Focuses on hard-to-classify examples",
        bestFor: "Imbalanced datasets with difficult examples"
      },
      'asymmetric_loss': {
        description: "Different penalties for false positives and negatives",
        bestFor: "When false positives and negatives have different costs"
      }
    };
    return info[lossFunction as keyof typeof info] || info['binary_crossentropy'];
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Multi-Label Training Configuration</h2>
          <p className="text-muted-foreground">
            Configure advanced parameters for multi-label classification training
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Label htmlFor="advanced-mode">Advanced Mode</Label>
          <Switch
            id="advanced-mode"
            checked={isAdvancedMode}
            onCheckedChange={setIsAdvancedMode}
          />
        </div>
      </div>

      {/* Configuration Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="basic">Basic</TabsTrigger>
          <TabsTrigger value="thresholds">Thresholds</TabsTrigger>
          <TabsTrigger value="advanced">Advanced</TabsTrigger>
          <TabsTrigger value="optimization">Optimization</TabsTrigger>
        </TabsList>

        {/* Basic Configuration */}
        <TabsContent value="basic" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="w-5 h-5" />
                Basic Configuration
              </CardTitle>
              <CardDescription>
                Essential training parameters for multi-label classification
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="model-name">Model Name</Label>
                  <Input
                    id="model-name"
                    value={config.modelName}
                    onChange={(e) => updateConfig({ modelName: e.target.value })}
                    placeholder="Enter model name"
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="base-model">Base Model</Label>
                  <Select value={config.baseModel} onValueChange={(value) => updateConfig({ baseModel: value })}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select base model" />
                    </SelectTrigger>
                    <SelectContent>
                      {getModelOptions().map(option => (
                        <SelectItem key={option.value} value={option.value}>
                          <div>
                            <div className="font-medium">{option.label}</div>
                            <div className="text-sm text-muted-foreground">{option.description}</div>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="epochs">Epochs</Label>
                  <div className="space-y-2">
                    <Slider
                      value={[config.numEpochs]}
                      onValueChange={([value]) => updateConfig({ numEpochs: value })}
                      max={20}
                      min={1}
                      step={1}
                    />
                    <div className="text-sm text-muted-foreground text-center">
                      {config.numEpochs} epochs
                    </div>
                  </div>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="batch-size">Batch Size</Label>
                  <div className="space-y-2">
                    <Slider
                      value={[config.batchSize]}
                      onValueChange={([value]) => updateConfig({ batchSize: value })}
                      max={64}
                      min={4}
                      step={4}
                    />
                    <div className="text-sm text-muted-foreground text-center">
                      {config.batchSize} samples
                    </div>
                  </div>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="learning-rate">Learning Rate</Label>
                  <div className="space-y-2">
                    <Slider
                      value={[config.learningRate * 100000]}
                      onValueChange={([value]) => updateConfig({ learningRate: value / 100000 })}
                      max={10}
                      min={0.1}
                      step={0.1}
                    />
                    <div className="text-sm text-muted-foreground text-center">
                      {config.learningRate.toExponential(1)}
                    </div>
                  </div>
                </div>
              </div>

              {/* Loss Function Selection */}
              <div className="space-y-2">
                <Label htmlFor="loss-function">Loss Function</Label>
                <Select value={config.lossFunction} onValueChange={(value: any) => updateConfig({ lossFunction: value })}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select loss function" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="binary_crossentropy">Binary Cross-Entropy</SelectItem>
                    <SelectItem value="focal_loss">Focal Loss</SelectItem>
                    <SelectItem value="asymmetric_loss">Asymmetric Loss</SelectItem>
                  </SelectContent>
                </Select>
                <div className="text-sm text-muted-foreground">
                  {getLossFunctionInfo(config.lossFunction).description}
                  <br />
                  <strong>Best for:</strong> {getLossFunctionInfo(config.lossFunction).bestFor}
                </div>
              </div>

              {estimatedTrainingTime && (
                <Alert>
                  <TrendingUp className="h-4 w-4" />
                  <AlertDescription>
                    <strong>Estimated training time:</strong> {estimatedTrainingTime}
                  </AlertDescription>
                </Alert>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Label Thresholds Configuration */}
        <TabsContent value="thresholds" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Target className="w-5 h-5" />
                Label Thresholds
              </CardTitle>
              <CardDescription>
                Configure classification thresholds for each label
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="threshold-optimization">Threshold Optimization Strategy</Label>
                <Select 
                  value={config.thresholdOptimization} 
                  onValueChange={(value: any) => updateConfig({ thresholdOptimization: value })}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select optimization strategy" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="global">Global Threshold</SelectItem>
                    <SelectItem value="per-label">Per-Label Optimization</SelectItem>
                    <SelectItem value="adaptive">Adaptive Thresholds</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Individual Label Thresholds */}
              <div className="space-y-4">
                <h4 className="font-medium">Individual Label Thresholds</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-h-60 overflow-y-auto">
                  {detectedLabels.map(label => (
                    <div key={label} className="space-y-2 p-3 border rounded-lg">
                      <div className="flex items-center justify-between">
                        <Label className="text-sm font-medium">{label}</Label>
                        <Badge variant="outline">
                          {(config.labelThresholds[label] || 0.5).toFixed(2)}
                        </Badge>
                      </div>
                      <Slider
                        value={[config.labelThresholds[label] || 0.5]}
                        onValueChange={([value]) => updateLabelThreshold(label, value)}
                        max={1}
                        min={0}
                        step={0.01}
                      />
                      {labelCorrelations[label] && labelCorrelations[label].length > 0 && (
                        <div className="text-xs text-muted-foreground">
                          Correlated with: {labelCorrelations[label].join(', ')}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="correlation-analysis"
                  checked={config.correlationAnalysis}
                  onCheckedChange={(checked) => updateConfig({ correlationAnalysis: checked })}
                />
                <Label htmlFor="correlation-analysis">Enable correlation analysis</Label>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Advanced Configuration */}
        <TabsContent value="advanced" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Brain className="w-5 h-5" />
                Advanced Parameters
              </CardTitle>
              <CardDescription>
                Fine-tune advanced multi-label training parameters
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {isAdvancedMode ? (
                <>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="label-smoothing">Label Smoothing</Label>
                      <div className="space-y-2">
                        <Slider
                          value={[config.labelSmoothing]}
                          onValueChange={([value]) => updateConfig({ labelSmoothing: value })}
                          max={0.5}
                          min={0}
                          step={0.01}
                        />
                        <div className="text-sm text-muted-foreground text-center">
                          {config.labelSmoothing.toFixed(2)}
                        </div>
                      </div>
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="negative-downsampling">Negative Downsampling</Label>
                      <div className="space-y-2">
                        <Slider
                          value={[config.negativeDownsampling]}
                          onValueChange={([value]) => updateConfig({ negativeDownsampling: value })}
                          max={1}
                          min={0}
                          step={0.01}
                        />
                        <div className="text-sm text-muted-foreground text-center">
                          {config.negativeDownsampling.toFixed(2)}
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <div className="flex items-center space-x-2">
                      <Switch
                        id="hierarchical-constraints"
                        checked={config.hierarchicalConstraints}
                        onCheckedChange={(checked) => updateConfig({ hierarchicalConstraints: checked })}
                      />
                      <Label htmlFor="hierarchical-constraints">Enable hierarchical constraints</Label>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="class-weights">Class Weighting Strategy</Label>
                      <Select 
                        value={config.classWeights} 
                        onValueChange={(value: any) => updateConfig({ classWeights: value })}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select class weighting" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="balanced">Balanced</SelectItem>
                          <SelectItem value="custom">Custom</SelectItem>
                          <SelectItem value="none">None</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </>
              ) : (
                <Alert>
                  <Info className="h-4 w-4" />
                  <AlertDescription>
                    Enable Advanced Mode to access additional configuration options.
                  </AlertDescription>
                </Alert>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Optimization Configuration */}
        <TabsContent value="optimization" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Zap className="w-5 h-5" />
                Performance Optimization
              </CardTitle>
              <CardDescription>
                Hardware and performance optimization settings
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Switch
                    id="use-unsloth"
                    checked={config.useUnsloth}
                    onCheckedChange={(checked) => updateConfig({ useUnsloth: checked })}
                  />
                  <Label htmlFor="use-unsloth">Use Unsloth optimization</Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Switch
                    id="fp16"
                    checked={config.fp16}
                    onCheckedChange={(checked) => updateConfig({ fp16: checked })}
                  />
                  <Label htmlFor="fp16">Enable FP16 training</Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Switch
                    id="gradient-checkpointing"
                    checked={config.gradientCheckpointing}
                    onCheckedChange={(checked) => updateConfig({ gradientCheckpointing: checked })}
                  />
                  <Label htmlFor="gradient-checkpointing">Enable gradient checkpointing</Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Switch
                    id="early-stopping"
                    checked={config.enableEarlyStopping}
                    onCheckedChange={(checked) => updateConfig({ enableEarlyStopping: checked })}
                  />
                  <Label htmlFor="early-stopping">Enable early stopping</Label>
                </div>
              </div>

              {config.enableEarlyStopping && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4 border rounded-lg">
                  <div className="space-y-2">
                    <Label htmlFor="patience">Patience</Label>
                    <div className="space-y-2">
                      <Slider
                        value={[config.patience]}
                        onValueChange={([value]) => updateConfig({ patience: value })}
                        max={10}
                        min={1}
                        step={1}
                      />
                      <div className="text-sm text-muted-foreground text-center">
                        {config.patience} epochs
                      </div>
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="min-delta">Min Delta</Label>
                    <div className="space-y-2">
                      <Slider
                        value={[config.minDelta * 1000]}
                        onValueChange={([value]) => updateConfig({ minDelta: value / 1000 })}
                        max={10}
                        min={0.1}
                        step={0.1}
                      />
                      <div className="text-sm text-muted-foreground text-center">
                        {config.minDelta.toFixed(3)}
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Save Button */}
      <div className="flex justify-end">
        <Button onClick={handleSave} className="flex items-center gap-2">
          <CheckCircle2 className="w-4 h-4" />
          Save Configuration
        </Button>
      </div>
    </div>
  );
};
