"""
Dynamic Hierarchy Manager for ClassyWeb

This module provides a comprehensive system for managing arbitrary hierarchy structures,
replacing the hardcoded Theme->Category->Segment->Subsegment assumptions.
"""

import logging
from typing import Dict, List, Optional, Any, Tuple
from sqlalchemy.orm import Session
from pydantic import BaseModel, Field

from . import config

logger = logging.getLogger(__name__)


class HierarchySchema(BaseModel):
    """Represents a dynamic hierarchy schema configuration."""
    id: Optional[int] = None
    name: str
    levels: List[str] = Field(..., min_length=1, max_length=config.MAX_HIERARCHY_LEVELS)
    depth: int = Field(..., ge=1, le=config.MAX_HIERARCHY_LEVELS)
    constraints: Dict[str, Any] = Field(default_factory=dict)
    validation_rules: Dict[str, Dict[str, Any]] = Field(default_factory=dict)
    ui_config: Dict[str, Any] = Field(default_factory=dict)
    confidence_thresholds: Dict[str, float] = Field(default_factory=dict)
    
    def __init__(self, **data):
        super().__init__(**data)
        if not self.validation_rules:
            self.validation_rules = self._generate_validation_rules()
        if not self.ui_config:
            self.ui_config = self._generate_ui_config()
        if not self.confidence_thresholds:
            self.confidence_thresholds = self._generate_confidence_thresholds()
    
    def _generate_validation_rules(self) -> Dict[str, Dict[str, Any]]:
        """Generate dynamic validation rules for hierarchy levels."""
        return {
            level: {
                'required': True,
                'max_length': 100,
                'pattern': r'^[a-zA-Z0-9\s\-_\.]+$',
                'min_length': 1
            } for level in self.levels
        }
    
    def _generate_ui_config(self) -> Dict[str, Any]:
        """Generate UI configuration for hierarchy levels."""
        return {
            'display_names': {level: level.replace('_', ' ').title() for level in self.levels},
            'column_widths': {level: 150 for level in self.levels},
            'input_types': {level: 'text' for level in self.levels},
            'placeholders': {level: f"Enter {level.replace('_', ' ').lower()}" for level in self.levels}
        }
    
    def _generate_confidence_thresholds(self) -> Dict[str, float]:
        """Generate confidence thresholds for hierarchy levels."""
        return {level: config.DEFAULT_HIERARCHY_CONFIDENCE_THRESHOLD for level in self.levels}


class DynamicHierarchyManager:
    """
    Manages dynamic hierarchy configurations and provides utilities for
    working with arbitrary hierarchy structures.
    """
    
    def __init__(self):
        self.hierarchy_schemas: Dict[str, HierarchySchema] = {}
        self.level_validators: Dict[str, Any] = {}
    
    def create_hierarchy_schema(
        self, 
        user_id: Optional[int], 
        levels: List[str], 
        name: str = "Custom Hierarchy",
        constraints: Optional[Dict] = None
    ) -> str:
        """Create a dynamic hierarchy schema for any domain."""
        if not levels or len(levels) > config.MAX_HIERARCHY_LEVELS:
            raise ValueError(f"Invalid hierarchy levels. Must have 1-{config.MAX_HIERARCHY_LEVELS} levels.")
        
        # Clean and validate level names
        cleaned_levels = [self._clean_level_name(level) for level in levels]
        
        # Generate schema ID
        schema_id = f"user_{user_id}_{hash(tuple(cleaned_levels))}" if user_id else f"default_{hash(tuple(cleaned_levels))}"
        
        # Create schema
        schema = HierarchySchema(
            name=name,
            levels=cleaned_levels,
            depth=len(cleaned_levels),
            constraints=constraints or {}
        )
        
        self.hierarchy_schemas[schema_id] = schema
        logger.info(f"Created hierarchy schema '{name}' with levels: {cleaned_levels}")
        
        return schema_id
    
    def get_hierarchy_schema(self, schema_id: str) -> Optional[HierarchySchema]:
        """Get a hierarchy schema by ID."""
        return self.hierarchy_schemas.get(schema_id)
    
    def get_schema_from_db_config(self, db: Session, config_id: Optional[int] = None) -> HierarchySchema:
        """Get hierarchy schema from database configuration."""
        # Lazy import to avoid circular dependency
        from .database import get_hierarchy_config, get_default_hierarchy_config

        if config_id:
            db_config = get_hierarchy_config(db, config_id)
        else:
            db_config = get_default_hierarchy_config(db)

        if not db_config:
            # Return default schema if no config found
            return HierarchySchema(
                name="Default Hierarchy",
                levels=config.DEFAULT_HIERARCHY_LEVELS,
                depth=len(config.DEFAULT_HIERARCHY_LEVELS)
            )

        return HierarchySchema(
            id=db_config.id,
            name=db_config.name,
            levels=db_config.hierarchy_levels,
            depth=len(db_config.hierarchy_levels)
        )

    def create_schema_from_data_analysis(self, data: Dict[str, Any], suggested_levels: Optional[List[str]] = None) -> HierarchySchema:
        """Create a hierarchy schema by analyzing data structure."""
        if suggested_levels:
            levels = suggested_levels
        else:
            # Auto-detect levels from data structure
            levels = self._detect_levels_from_data(data)

        return HierarchySchema(
            name="Auto-detected Hierarchy",
            levels=levels,
            depth=len(levels)
        )

    def _detect_levels_from_data(self, data: Dict[str, Any]) -> List[str]:
        """Detect hierarchy levels from data structure."""
        # This is a simplified detection - in a real implementation,
        # you might use more sophisticated analysis
        if isinstance(data, dict):
            keys = list(data.keys())
            if 'themes' in keys:
                return config.LEGACY_HIERARCHY_LEVELS
            elif len(keys) > 0:
                # Try to infer from key names
                return [key.replace('_', ' ').title() for key in keys[:4]]

        return config.DEFAULT_HIERARCHY_LEVELS

    def generate_validation_schema(self, levels: List[str]) -> Dict[str, Any]:
        """Generate a JSON schema for validating hierarchy data."""
        properties = {}
        required = []

        for level in levels:
            properties[level] = {
                "type": "string",
                "minLength": 1,
                "maxLength": 100,
                "pattern": "^[a-zA-Z0-9\\s\\-_\\.]+$"
            }
            required.append(level)

        properties["Keywords"] = {
            "type": "string",
            "description": "Comma-separated keywords"
        }

        return {
            "type": "object",
            "properties": properties,
            "required": required,
            "additionalProperties": False
        }
    
    def validate_hierarchy_data(self, data: Dict[str, Any], schema: HierarchySchema) -> Tuple[bool, List[str]]:
        """Validate hierarchy data against schema."""
        errors = []
        
        for level in schema.levels:
            if level not in data:
                errors.append(f"Missing required level: {level}")
                continue
            
            value = data[level]
            rules = schema.validation_rules.get(level, {})
            
            # Check required
            if rules.get('required', False) and not value:
                errors.append(f"{level} is required")
                continue
            
            # Check length constraints
            if value and isinstance(value, str):
                min_len = rules.get('min_length', 0)
                max_len = rules.get('max_length', float('inf'))
                if len(value) < min_len:
                    errors.append(f"{level} must be at least {min_len} characters")
                if len(value) > max_len:
                    errors.append(f"{level} must be at most {max_len} characters")
        
        return len(errors) == 0, errors
    

    
    def generate_hierarchy_prompt_text(self, schema: HierarchySchema) -> str:
        """Generate dynamic prompt text for LLM classification."""
        level_descriptions = []
        for i, level in enumerate(schema.levels):
            display_name = schema.ui_config['display_names'].get(level, level)
            if i == 0:
                level_descriptions.append(f"- {display_name}: The highest level category")
            elif i == len(schema.levels) - 1:
                level_descriptions.append(f"- {display_name}: The most specific subcategory")
            else:
                level_descriptions.append(f"- {display_name}: A subcategory of the previous level")
        
        return f"""
Classification Hierarchy Structure:
{chr(10).join(level_descriptions)}

Please classify the text into the most appropriate path through this hierarchy.
Only use the exact names provided in the structure.
"""
    
    def _clean_level_name(self, level: str) -> str:
        """Clean and standardize level names."""
        # Remove extra whitespace and convert to a standard format
        cleaned = level.strip()
        # Replace spaces with underscores for consistency
        cleaned = cleaned.replace(' ', '_')
        # Remove special characters except underscores and hyphens
        import re
        cleaned = re.sub(r'[^a-zA-Z0-9_\-]', '', cleaned)
        return cleaned if cleaned else f"Level_{len(self.hierarchy_schemas) + 1}"

    def get_hierarchy_levels_for_processing(self, hierarchy_levels: Optional[List[str]] = None) -> List[str]:
        """Get hierarchy levels for processing, with fallback to defaults."""
        if hierarchy_levels:
            return hierarchy_levels
        return config.DEFAULT_HIERARCHY_LEVELS

    def determine_hierarchy_level_from_label(self, label_name: str, hierarchy_levels: Optional[List[str]] = None) -> Optional[str]:
        """Determine which hierarchy level a label belongs to based on its prefix."""
        levels_to_check = self.get_hierarchy_levels_for_processing(hierarchy_levels)

        for level in levels_to_check:
            if label_name.startswith(f"{level}:") or label_name.startswith(f"{level} :"):
                return level

        return None

    def get_confidence_threshold_for_level(self, hierarchy_level: Optional[str]) -> float:
        """Get confidence threshold for a specific hierarchy level."""
        if hierarchy_level:
            # Check if we have legacy thresholds defined
            if hasattr(config, 'DEFAULT_HIERARCHY_CONFIDENCE_THRESHOLDS') and hierarchy_level in config.DEFAULT_HIERARCHY_CONFIDENCE_THRESHOLDS:
                return config.DEFAULT_HIERARCHY_CONFIDENCE_THRESHOLDS[hierarchy_level]

        # Return default threshold
        return config.DEFAULT_HIERARCHY_CONFIDENCE_THRESHOLD


# Global instance
hierarchy_manager = DynamicHierarchyManager()
