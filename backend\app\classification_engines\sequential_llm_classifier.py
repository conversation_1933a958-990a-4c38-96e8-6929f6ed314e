"""
Sequential Thinking LLM Classifier for ClassyWeb ML Platform.

This module implements a two-step LLM classification approach:
1. Analysis Step: LLM analyzes and understands the classification requirements
2. Classification Step: LLM performs the actual classification based on refined understanding
"""

import logging
import time
from typing import List, Dict, Any, Optional
from ..llm_classifier import initialize_llm_client
from ..models.classification import ClassificationResult

logger = logging.getLogger(__name__)


class SequentialLLMClassifier:
    """Enhanced LLM classifier using sequential thinking strategy."""
    
    def __init__(self):
        self.analysis_cache = {}  # Cache analysis results for similar classification tasks
    
    async def classify_with_sequential_thinking(
        self,
        texts: List[str],
        classification_type: str,
        llm_provider: str,
        llm_model: str,
        llm_endpoint: str,
        api_key: Optional[str] = None,
        custom_prompt: Optional[str] = None,
        temperature: float = 0.1,
        max_tokens: int = 100,
        progress_callback: Optional[callable] = None
    ) -> List[ClassificationResult]:
        """
        Perform classification using sequential thinking approach.
        
        Args:
            texts: List of texts to classify
            classification_type: Type of classification (binary, multi-class, etc.)
            llm_provider: LLM provider name
            llm_model: Model name
            llm_endpoint: API endpoint
            api_key: API key for the provider
            custom_prompt: Optional custom prompt template
            temperature: LLM temperature setting
            max_tokens: Maximum tokens for response
            progress_callback: Optional progress callback
            
        Returns:
            List of classification results
        """
        try:
            # Initialize LLM client
            if progress_callback:
                progress_callback({"stage": "llm_setup", "progress": 0.05})
            
            llm_client = initialize_llm_client(
                provider=llm_provider,
                endpoint=llm_endpoint,
                api_key=api_key,
                model_name=llm_model
            )
            
            # Step 1: Analysis Phase - Understand the classification task
            if progress_callback:
                progress_callback({"stage": "analysis", "progress": 0.1})
            
            analysis_result = await self._analyze_classification_requirements(
                texts[:5],  # Use first 5 texts for analysis
                classification_type,
                llm_client,
                custom_prompt
            )
            
            # Step 2: Classification Phase - Perform actual classification
            if progress_callback:
                progress_callback({"stage": "classification", "progress": 0.2})
            
            results = await self._perform_enhanced_classification(
                texts,
                classification_type,
                llm_client,
                analysis_result,
                custom_prompt,
                temperature,
                max_tokens,
                progress_callback
            )
            
            return results
            
        except Exception as e:
            logger.error(f"Sequential LLM classification failed: {str(e)}")
            # Return error results for all texts
            return [
                ClassificationResult(
                    text=text,
                    predictions=["error"],
                    confidence=0.0,
                    processing_time=0.0,
                    method_used="sequential_llm_inference",
                    reasoning=f"Classification failed: {str(e)}",
                    metadata={"error": str(e)}
                )
                for text in texts
            ]
    
    async def _analyze_classification_requirements(
        self,
        sample_texts: List[str],
        classification_type: str,
        llm_client: Any,
        custom_prompt: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        First step: Analyze the classification requirements and understand the task.
        """
        try:
            # Create analysis prompt
            analysis_prompt = self._create_analysis_prompt(
                sample_texts, 
                classification_type, 
                custom_prompt
            )
            
            # Get LLM analysis
            from langchain.schema import HumanMessage
            messages = [HumanMessage(content=analysis_prompt)]
            response = await llm_client.ainvoke(messages)
            
            # Parse analysis response
            analysis_text = response.content if hasattr(response, 'content') else str(response)
            
            return {
                "analysis_text": analysis_text,
                "classification_type": classification_type,
                "sample_texts": sample_texts,
                "refined_understanding": self._extract_key_insights(analysis_text),
                "suggested_categories": self._extract_categories(analysis_text, classification_type)
            }
            
        except Exception as e:
            logger.error(f"Analysis phase failed: {str(e)}")
            return {
                "analysis_text": "Analysis failed",
                "classification_type": classification_type,
                "error": str(e)
            }
    
    async def _perform_enhanced_classification(
        self,
        texts: List[str],
        classification_type: str,
        llm_client: Any,
        analysis_result: Dict[str, Any],
        custom_prompt: Optional[str],
        temperature: float,
        max_tokens: int,
        progress_callback: Optional[callable] = None
    ) -> List[ClassificationResult]:
        """
        Second step: Perform classification using insights from analysis.
        """
        results = []
        total_texts = len(texts)
        
        # Create enhanced prompt based on analysis
        enhanced_prompt = self._create_enhanced_classification_prompt(
            classification_type,
            analysis_result,
            custom_prompt
        )
        
        # Process texts in batches
        batch_size = 10
        for i in range(0, len(texts), batch_size):
            batch_texts = texts[i:i + batch_size]
            
            if progress_callback:
                progress = 0.2 + (i / total_texts) * 0.7
                progress_callback({"stage": "classification", "progress": progress})
            
            # Classify batch
            batch_results = await self._classify_batch(
                batch_texts,
                enhanced_prompt,
                llm_client,
                classification_type,
                analysis_result
            )
            
            results.extend(batch_results)
        
        if progress_callback:
            progress_callback({"stage": "complete", "progress": 1.0})
        
        return results
    
    def _create_analysis_prompt(
        self, 
        sample_texts: List[str], 
        classification_type: str, 
        custom_prompt: Optional[str]
    ) -> str:
        """Create prompt for the analysis phase."""
        
        sample_texts_str = "\n".join([f"{i+1}. {text[:200]}..." for i, text in enumerate(sample_texts)])
        
        base_prompt = f"""You are an expert text classification analyst. Your task is to analyze the following sample texts and understand the classification requirements.

Classification Type: {classification_type}
Sample Texts:
{sample_texts_str}

Please analyze these texts and provide:
1. Key themes and patterns you observe
2. Suggested categories or labels that would be appropriate
3. Important features or keywords that distinguish different classes
4. Any challenges or considerations for this classification task
5. Recommended classification approach

{f"Custom Requirements: {custom_prompt}" if custom_prompt else ""}

Provide a detailed analysis that will help with accurate classification:"""
        
        return base_prompt
    
    def _create_enhanced_classification_prompt(
        self,
        classification_type: str,
        analysis_result: Dict[str, Any],
        custom_prompt: Optional[str]
    ) -> str:
        """Create enhanced prompt for classification based on analysis."""
        
        analysis_insights = analysis_result.get("refined_understanding", "")
        suggested_categories = analysis_result.get("suggested_categories", [])
        
        if custom_prompt:
            base_prompt = custom_prompt
        else:
            # Create type-specific prompt enhanced with analysis insights
            type_prompts = {
                "binary": f"""Based on the analysis, classify the following text as positive or negative.
                
Key insights from analysis: {analysis_insights}

Text: {{text}}

Classification (respond with only 'positive' or 'negative'):""",
                
                "multi-class": f"""Based on the analysis, classify the following text into the most appropriate category.

Key insights from analysis: {analysis_insights}
Suggested categories: {', '.join(suggested_categories) if suggested_categories else 'Will be determined from context'}

Text: {{text}}

Classification (respond with only the category name):""",
                
                "multi-label": f"""Based on the analysis, identify all applicable labels for the following text.

Key insights from analysis: {analysis_insights}
Available labels: {', '.join(suggested_categories) if suggested_categories else 'Will be determined from context'}

Text: {{text}}

Labels (respond with comma-separated list):""",
                
                "hierarchical": f"""Based on the analysis, classify the following text using hierarchical structure.

Key insights from analysis: {analysis_insights}

Text: {{text}}

Classification (respond with hierarchical path):""",
                
                "flat": f"""Based on the analysis, classify the following text into the most appropriate category.

Key insights from analysis: {analysis_insights}

Text: {{text}}

Classification (respond with category name):"""
            }
            
            base_prompt = type_prompts.get(classification_type, type_prompts["multi-class"])
        
        return base_prompt
    
    def _extract_key_insights(self, analysis_text: str) -> str:
        """Extract key insights from analysis response."""
        # Simple extraction - in a real implementation, this could be more sophisticated
        lines = analysis_text.split('\n')
        insights = []
        
        for line in lines:
            if any(keyword in line.lower() for keyword in ['theme', 'pattern', 'feature', 'keyword', 'important']):
                insights.append(line.strip())
        
        return '. '.join(insights[:3])  # Take top 3 insights
    
    def _extract_categories(self, analysis_text: str, classification_type: str) -> List[str]:
        """Extract suggested categories from analysis response."""
        # Simple extraction - in a real implementation, this could use NLP
        categories = []
        lines = analysis_text.split('\n')
        
        for line in lines:
            if 'categor' in line.lower() or 'label' in line.lower():
                # Extract potential categories (this is a simplified approach)
                words = line.split()
                for word in words:
                    if word.isalpha() and len(word) > 3:
                        categories.append(word.lower())
        
        return list(set(categories))[:10]  # Return unique categories, max 10
    
    async def _classify_batch(
        self,
        batch_texts: List[str],
        prompt_template: str,
        llm_client: Any,
        classification_type: str,
        analysis_result: Dict[str, Any]
    ) -> List[ClassificationResult]:
        """Classify a batch of texts using the enhanced prompt."""
        results = []
        
        for text in batch_texts:
            start_time = time.time()
            
            try:
                # Format prompt with current text
                formatted_prompt = prompt_template.replace("{text}", text)
                
                # Get LLM response
                from langchain.schema import HumanMessage
                messages = [HumanMessage(content=formatted_prompt)]
                response = await llm_client.ainvoke(messages)
                
                processing_time = time.time() - start_time
                response_text = response.content if hasattr(response, 'content') else str(response)
                
                # Parse response based on classification type
                predictions, confidence = self._parse_llm_response(
                    response_text, 
                    classification_type,
                    analysis_result
                )
                
                results.append(ClassificationResult(
                    text=text,
                    predictions=predictions,
                    confidence=confidence,
                    probabilities={pred: confidence/len(predictions) for pred in predictions},
                    processing_time=processing_time,
                    method_used="sequential_llm_inference",
                    reasoning=response_text,
                    metadata={
                        "analysis_enhanced": True,
                        "classification_type": classification_type
                    }
                ))
                
            except Exception as e:
                processing_time = time.time() - start_time
                logger.error(f"Failed to classify text: {str(e)}")
                
                results.append(ClassificationResult(
                    text=text,
                    predictions=["error"],
                    confidence=0.0,
                    processing_time=processing_time,
                    method_used="sequential_llm_inference",
                    reasoning=f"Classification failed: {str(e)}",
                    metadata={"error": str(e)}
                ))
        
        return results
    
    def _parse_llm_response(
        self, 
        response: str, 
        classification_type: str,
        analysis_result: Dict[str, Any]
    ) -> tuple[List[str], float]:
        """Parse LLM response based on classification type."""
        response = response.strip().lower()
        
        if classification_type == "binary":
            if "positive" in response:
                return ["positive"], 0.85
            elif "negative" in response:
                return ["negative"], 0.85
            else:
                return ["unknown"], 0.1
        
        elif classification_type == "multi-label":
            # Parse comma-separated labels
            labels = [label.strip() for label in response.split(',') if label.strip()]
            confidence = 0.8 if labels else 0.1
            return labels if labels else ["unknown"], confidence
        
        else:  # multi-class, hierarchical, flat
            # Single category response
            prediction = response.split('\n')[0].strip()
            confidence = 0.8 if prediction and prediction != "unknown" else 0.1
            return [prediction], confidence
