"""Plugin model for ClassyWeb Universal Platform."""

from sqlalchemy import Column, Integer, String, Text, Boolean, DateTime, JSON
from sqlalchemy.sql import func
from datetime import datetime, timezone

from ..database import Base


class Plugin(Base):
    """Plugin model for storing plugin information."""
    
    __tablename__ = "plugins"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(255), unique=True, index=True, nullable=False)
    version = Column(String(50), nullable=False)
    description = Column(Text)
    author = Column(String(255))
    plugin_type = Column(String(50), nullable=False, index=True)
    
    # Plugin capabilities and metadata
    capabilities = Column(JSON, default=list)  # List of capability strings
    dependencies = Column(JSON, default=list)  # List of dependency strings
    supported_formats = Column(JSON, default=list)  # List of supported format strings
    
    # Enterprise and licensing
    enterprise_only = Column(Boolean, default=False, index=True)
    license = Column(String(100), default="MIT")
    
    # Documentation and support
    documentation_url = Column(String(500))
    support_url = Column(String(500))
    
    # Configuration
    config = Column(JSON, default=dict)  # Plugin-specific configuration
    
    # Status and lifecycle
    is_active = Column(Boolean, default=True, index=True)
    is_installed = Column(Boolean, default=False, index=True)
    installation_path = Column(String(500))
    
    # Timestamps
    registered_at = Column(DateTime(timezone=True), default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), default=func.now(), onupdate=func.now())
    last_used_at = Column(DateTime(timezone=True))
    
    # Usage statistics
    usage_count = Column(Integer, default=0)
    success_count = Column(Integer, default=0)
    error_count = Column(Integer, default=0)
    
    def __repr__(self):
        return f"<Plugin(name='{self.name}', version='{self.version}', type='{self.plugin_type}')>"
    
    def to_dict(self):
        """Convert plugin to dictionary."""
        return {
            "id": self.id,
            "name": self.name,
            "version": self.version,
            "description": self.description,
            "author": self.author,
            "plugin_type": self.plugin_type,
            "capabilities": self.capabilities,
            "dependencies": self.dependencies,
            "supported_formats": self.supported_formats,
            "enterprise_only": self.enterprise_only,
            "license": self.license,
            "documentation_url": self.documentation_url,
            "support_url": self.support_url,
            "config": self.config,
            "is_active": self.is_active,
            "is_installed": self.is_installed,
            "installation_path": self.installation_path,
            "registered_at": self.registered_at.isoformat() if self.registered_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "last_used_at": self.last_used_at.isoformat() if self.last_used_at else None,
            "usage_count": self.usage_count,
            "success_count": self.success_count,
            "error_count": self.error_count
        }
    
    def update_usage_stats(self, success: bool = True):
        """Update plugin usage statistics."""
        self.usage_count += 1
        self.last_used_at = datetime.now(timezone.utc)
        
        if success:
            self.success_count += 1
        else:
            self.error_count += 1
    
    @property
    def success_rate(self) -> float:
        """Calculate plugin success rate."""
        if self.usage_count == 0:
            return 0.0
        return self.success_count / self.usage_count
    
    @property
    def is_healthy(self) -> bool:
        """Check if plugin is healthy based on success rate."""
        if self.usage_count < 10:  # Not enough data
            return True
        return self.success_rate >= 0.8  # 80% success rate threshold
