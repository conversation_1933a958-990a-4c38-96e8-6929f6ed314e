"""Smart Classification Type Detection System for ClassyWeb ML Platform.

This module implements intelligent algorithms to automatically detect and recommend
the appropriate classification type based on data structure analysis, label relationships,
and domain patterns.
"""

import logging
import pandas as pd
import numpy as np
from typing import List, Dict, Any, Optional, Tuple, Set
from dataclasses import dataclass
from collections import Counter
import re

from .base_engine import ClassificationType

logger = logging.getLogger(__name__)


@dataclass
class DetectionResult:
    """Result from classification type detection."""
    classification_type: ClassificationType
    confidence: float
    reasoning: str
    alternative_suggestions: List[Tuple[ClassificationType, float]]
    data_characteristics: Dict[str, Any]
    recommendations: Dict[str, Any]


class SmartTypeDetector:
    """Intelligent classification type detection with confidence scoring."""
    
    def __init__(self):
        self.confidence_threshold = 0.8
        self.analysis_methods = [
            self._analyze_label_structure,
            self._analyze_data_distribution,
            self._analyze_label_relationships,
            self._analyze_domain_patterns
        ]
        
    def detect_classification_type(
        self,
        data: pd.DataFrame,
        text_columns: Optional[List[str]] = None,
        label_columns: Optional[List[str]] = None
    ) -> DetectionResult:
        """
        Intelligent classification type detection with confidence scoring.
        
        Args:
            data: Input DataFrame
            text_columns: Optional list of text column names
            label_columns: Optional list of label column names
            
        Returns:
            DetectionResult with type, confidence, and reasoning
        """
        try:
            # Auto-detect columns if not provided
            if text_columns is None:
                text_columns = self._detect_text_columns(data)
            if label_columns is None:
                label_columns = self._detect_label_columns(data, text_columns)
            
            # Run all analysis methods
            analysis_results = []
            for method in self.analysis_methods:
                try:
                    result = method(data, text_columns, label_columns)
                    analysis_results.append(result)
                except Exception as e:
                    logger.warning(f"Analysis method {method.__name__} failed: {e}")
            
            # Ensemble voting with weighted confidence
            final_result = self._ensemble_decision(analysis_results, data, text_columns, label_columns)
            
            return final_result
            
        except Exception as e:
            logger.error(f"Classification type detection failed: {e}")
            return DetectionResult(
                classification_type=ClassificationType.FLAT,
                confidence=0.1,
                reasoning=f"Detection failed: {str(e)}. Defaulting to flat classification.",
                alternative_suggestions=[],
                data_characteristics={},
                recommendations={}
            )
    
    def _detect_text_columns(self, data: pd.DataFrame) -> List[str]:
        """Automatically detect text columns."""
        text_columns = []
        
        for col in data.columns:
            if data[col].dtype == 'object':
                # Check if column contains text-like data
                sample_values = data[col].dropna().head(10).tolist()
                if sample_values:
                    avg_length = np.mean([len(str(val)) for val in sample_values])
                    # Consider columns with average length > 20 as text
                    if avg_length > 20:
                        text_columns.append(col)
                    # Also check for common text column names
                    elif any(keyword in col.lower() for keyword in ['text', 'content', 'description', 'comment', 'message']):
                        text_columns.append(col)
        
        # If no text columns found, take the first object column
        if not text_columns:
            object_cols = [col for col in data.columns if data[col].dtype == 'object']
            if object_cols:
                text_columns = [object_cols[0]]
        
        return text_columns
    
    def _detect_label_columns(self, data: pd.DataFrame, text_columns: List[str]) -> List[str]:
        """Automatically detect label columns."""
        label_columns = []
        
        for col in data.columns:
            if col in text_columns:
                continue
                
            # Check for common label column names
            if any(keyword in col.lower() for keyword in ['label', 'class', 'category', 'target', 'y']):
                label_columns.append(col)
            # Check for binary columns (potential multi-label)
            elif data[col].dtype in ['bool', 'int64'] and set(data[col].dropna().unique()).issubset({0, 1, True, False}):
                label_columns.append(col)
            # Check for categorical columns
            elif data[col].dtype in ['object', 'category'] and data[col].nunique() < len(data) * 0.5:
                label_columns.append(col)
        
        return label_columns
    
    def _analyze_label_structure(
        self,
        data: pd.DataFrame,
        text_columns: List[str],
        label_columns: List[str]
    ) -> Dict[str, Any]:
        """Analyze the structure of labels to determine classification type."""
        if not label_columns:
            return {
                'type': ClassificationType.FLAT,
                'confidence': 0.1,
                'reasoning': 'No label columns detected'
            }
        
        # Check for hierarchical patterns
        hierarchical_score = 0
        hierarchical_indicators = []
        
        # Look for hierarchy naming patterns
        hierarchy_patterns = ['level', 'tier', 'category', 'subcategory', 'theme', 'segment', 'subsegment']
        for pattern in hierarchy_patterns:
            matching_cols = [col for col in label_columns if pattern in col.lower()]
            if len(matching_cols) > 1:
                hierarchical_score += 0.3
                hierarchical_indicators.append(f"Found {len(matching_cols)} columns with '{pattern}' pattern")
        
        # Check for parent-child relationships in data
        if len(label_columns) > 1:
            for i in range(len(label_columns) - 1):
                parent_col = label_columns[i]
                child_col = label_columns[i + 1]
                
                # Check if child values are consistent within parent groups
                grouped = data.groupby(parent_col)[child_col].nunique()
                if grouped.mean() < grouped.max() * 0.8:  # Some structure exists
                    hierarchical_score += 0.2
                    hierarchical_indicators.append(f"Hierarchical relationship detected between {parent_col} and {child_col}")
        
        # Check for multi-label patterns
        multilabel_score = 0
        multilabel_indicators = []
        
        # Look for binary columns (multi-label indicators)
        binary_cols = []
        for col in label_columns:
            unique_vals = set(data[col].dropna().unique())
            if unique_vals.issubset({0, 1, True, False, '0', '1', 'true', 'false'}):
                binary_cols.append(col)
        
        if len(binary_cols) > 1:
            multilabel_score += 0.4
            multilabel_indicators.append(f"Found {len(binary_cols)} binary label columns")
        
        # Check for list-like or comma-separated values
        for col in label_columns:
            if data[col].dtype == 'object':
                sample_vals = data[col].dropna().head(100)
                list_like_count = sum(1 for val in sample_vals if isinstance(val, str) and (',' in val or ';' in val))
                if list_like_count > len(sample_vals) * 0.1:
                    multilabel_score += 0.3
                    multilabel_indicators.append(f"Found comma-separated values in {col}")
        
        # Determine primary type based on scores
        if hierarchical_score > 0.5:
            return {
                'type': ClassificationType.HIERARCHICAL,
                'confidence': min(hierarchical_score, 0.9),
                'reasoning': f"Hierarchical structure detected: {'; '.join(hierarchical_indicators)}"
            }
        elif multilabel_score > 0.4:
            return {
                'type': ClassificationType.MULTILABEL,
                'confidence': min(multilabel_score, 0.9),
                'reasoning': f"Multi-label structure detected: {'; '.join(multilabel_indicators)}"
            }
        elif len(label_columns) == 1:
            # Single label column - could be binary, multiclass, or flat
            unique_count = data[label_columns[0]].nunique()
            if unique_count == 2:
                return {
                    'type': ClassificationType.BINARY,
                    'confidence': 0.8,
                    'reasoning': f"Single label column with 2 unique values detected"
                }
            elif unique_count > 2:
                return {
                    'type': ClassificationType.MULTICLASS,
                    'confidence': 0.7,
                    'reasoning': f"Single label column with {unique_count} unique values detected"
                }
        
        return {
            'type': ClassificationType.FLAT,
            'confidence': 0.5,
            'reasoning': 'Standard flat classification structure detected'
        }
    
    def _analyze_data_distribution(
        self,
        data: pd.DataFrame,
        text_columns: List[str],
        label_columns: List[str]
    ) -> Dict[str, Any]:
        """Analyze data distribution patterns."""
        if not label_columns:
            return {
                'type': ClassificationType.FLAT,
                'confidence': 0.1,
                'reasoning': 'No labels to analyze distribution'
            }
        
        # Analyze label distribution
        distribution_analysis = {}
        
        for col in label_columns:
            value_counts = data[col].value_counts()
            distribution_analysis[col] = {
                'unique_count': len(value_counts),
                'most_common_freq': value_counts.iloc[0] / len(data),
                'entropy': -sum((p := v/len(data)) * np.log2(p) for v in value_counts if v > 0),
                'imbalance_ratio': value_counts.iloc[0] / value_counts.iloc[-1] if len(value_counts) > 1 else 1
            }
        
        # Determine type based on distribution characteristics
        if len(label_columns) == 1:
            col_analysis = distribution_analysis[label_columns[0]]
            unique_count = col_analysis['unique_count']
            
            if unique_count == 2:
                confidence = 0.8 if col_analysis['imbalance_ratio'] < 10 else 0.6
                return {
                    'type': ClassificationType.BINARY,
                    'confidence': confidence,
                    'reasoning': f"Binary distribution with imbalance ratio {col_analysis['imbalance_ratio']:.2f}"
                }
            elif unique_count <= 10:
                return {
                    'type': ClassificationType.MULTICLASS,
                    'confidence': 0.7,
                    'reasoning': f"Multi-class distribution with {unique_count} classes"
                }
            else:
                return {
                    'type': ClassificationType.FLAT,
                    'confidence': 0.6,
                    'reasoning': f"High cardinality ({unique_count} classes) suggests flat classification"
                }
        
        return {
            'type': ClassificationType.FLAT,
            'confidence': 0.4,
            'reasoning': 'Distribution analysis inconclusive'
        }
    
    def _analyze_label_relationships(
        self,
        data: pd.DataFrame,
        text_columns: List[str],
        label_columns: List[str]
    ) -> Dict[str, Any]:
        """Analyze relationships between labels."""
        if len(label_columns) < 2:
            return {
                'type': ClassificationType.FLAT,
                'confidence': 0.3,
                'reasoning': 'Insufficient labels for relationship analysis'
            }
        
        # Calculate correlations between label columns
        correlations = []
        try:
            # Convert to numeric for correlation analysis
            numeric_data = pd.DataFrame()
            for col in label_columns:
                if data[col].dtype in ['object', 'category']:
                    # Label encode categorical variables
                    numeric_data[col] = pd.Categorical(data[col]).codes
                else:
                    numeric_data[col] = data[col]
            
            corr_matrix = numeric_data.corr()
            
            # Extract correlation values (excluding diagonal)
            for i in range(len(label_columns)):
                for j in range(i + 1, len(label_columns)):
                    corr_val = corr_matrix.iloc[i, j]
                    if not np.isnan(corr_val):
                        correlations.append(abs(corr_val))
            
            if correlations:
                avg_correlation = np.mean(correlations)
                max_correlation = max(correlations)
                
                if max_correlation > 0.8:
                    return {
                        'type': ClassificationType.HIERARCHICAL,
                        'confidence': 0.7,
                        'reasoning': f"High correlation ({max_correlation:.2f}) suggests hierarchical structure"
                    }
                elif avg_correlation < 0.3:
                    return {
                        'type': ClassificationType.MULTILABEL,
                        'confidence': 0.6,
                        'reasoning': f"Low correlation ({avg_correlation:.2f}) suggests independent multi-labels"
                    }
        
        except Exception as e:
            logger.warning(f"Correlation analysis failed: {e}")
        
        return {
            'type': ClassificationType.FLAT,
            'confidence': 0.4,
            'reasoning': 'Label relationship analysis inconclusive'
        }
    
    def _analyze_domain_patterns(
        self,
        data: pd.DataFrame,
        text_columns: List[str],
        label_columns: List[str]
    ) -> Dict[str, Any]:
        """Analyze domain-specific patterns in the data."""
        domain_indicators = {
            'sentiment': ['positive', 'negative', 'neutral', 'sentiment'],
            'spam': ['spam', 'ham', 'not spam'],
            'topic': ['topic', 'subject', 'theme'],
            'product': ['product', 'category', 'subcategory', 'brand'],
            'document': ['document', 'type', 'classification']
        }
        
        detected_domains = []
        
        # Check column names for domain indicators
        all_columns = text_columns + label_columns
        for domain, indicators in domain_indicators.items():
            for indicator in indicators:
                if any(indicator in col.lower() for col in all_columns):
                    detected_domains.append(domain)
                    break
        
        # Check label values for domain indicators
        for col in label_columns:
            if data[col].dtype == 'object':
                unique_values = [str(val).lower() for val in data[col].unique()]
                for domain, indicators in domain_indicators.items():
                    if any(indicator in ' '.join(unique_values) for indicator in indicators):
                        if domain not in detected_domains:
                            detected_domains.append(domain)
        
        # Make recommendations based on detected domains
        if 'sentiment' in detected_domains:
            return {
                'type': ClassificationType.MULTICLASS,
                'confidence': 0.8,
                'reasoning': 'Sentiment analysis domain detected - typically multi-class'
            }
        elif 'spam' in detected_domains:
            return {
                'type': ClassificationType.BINARY,
                'confidence': 0.9,
                'reasoning': 'Spam detection domain detected - typically binary'
            }
        elif 'product' in detected_domains:
            return {
                'type': ClassificationType.HIERARCHICAL,
                'confidence': 0.7,
                'reasoning': 'Product categorization domain detected - typically hierarchical'
            }
        
        return {
            'type': ClassificationType.FLAT,
            'confidence': 0.2,
            'reasoning': f'Domain analysis inconclusive. Detected domains: {detected_domains}'
        }

    def _ensemble_decision(
        self,
        analysis_results: List[Dict[str, Any]],
        data: pd.DataFrame,
        text_columns: List[str],
        label_columns: List[str]
    ) -> DetectionResult:
        """Combine results from multiple analysis methods using ensemble voting."""
        if not analysis_results:
            return DetectionResult(
                classification_type=ClassificationType.FLAT,
                confidence=0.1,
                reasoning="No analysis results available",
                alternative_suggestions=[],
                data_characteristics={},
                recommendations={}
            )

        # Collect votes with weights
        type_votes = {}
        total_confidence = 0
        reasoning_parts = []

        # Weight different analysis methods
        method_weights = {
            '_analyze_label_structure': 0.4,
            '_analyze_data_distribution': 0.3,
            '_analyze_label_relationships': 0.2,
            '_analyze_domain_patterns': 0.1
        }

        for i, result in enumerate(analysis_results):
            classification_type = result.get('type', ClassificationType.FLAT)
            confidence = result.get('confidence', 0.0)
            reasoning = result.get('reasoning', '')

            # Get method weight
            method_name = self.analysis_methods[i].__name__ if i < len(self.analysis_methods) else 'unknown'
            weight = method_weights.get(method_name, 0.1)

            # Weighted vote
            weighted_confidence = confidence * weight

            if classification_type not in type_votes:
                type_votes[classification_type] = 0
            type_votes[classification_type] += weighted_confidence

            total_confidence += weighted_confidence
            if reasoning:
                reasoning_parts.append(f"{method_name}: {reasoning}")

        # Find the winning type
        if not type_votes:
            winning_type = ClassificationType.FLAT
            final_confidence = 0.1
        else:
            winning_type = max(type_votes, key=type_votes.get)
            final_confidence = type_votes[winning_type] / max(total_confidence, 0.1)

        # Create alternative suggestions
        alternative_suggestions = []
        sorted_votes = sorted(type_votes.items(), key=lambda x: x[1], reverse=True)
        for classification_type, score in sorted_votes[1:]:  # Skip the winning type
            if score > 0:
                alt_confidence = score / max(total_confidence, 0.1)
                alternative_suggestions.append((classification_type, alt_confidence))

        # Analyze data characteristics
        data_characteristics = self._analyze_data_characteristics(data, text_columns, label_columns)

        # Generate recommendations
        recommendations = self._generate_recommendations(winning_type, data_characteristics, final_confidence)

        # Combine reasoning
        final_reasoning = f"Ensemble decision based on: {'; '.join(reasoning_parts)}"

        return DetectionResult(
            classification_type=winning_type,
            confidence=min(final_confidence, 0.95),  # Cap confidence at 95%
            reasoning=final_reasoning,
            alternative_suggestions=alternative_suggestions[:3],  # Top 3 alternatives
            data_characteristics=data_characteristics,
            recommendations=recommendations
        )

    def _analyze_data_characteristics(
        self,
        data: pd.DataFrame,
        text_columns: List[str],
        label_columns: List[str]
    ) -> Dict[str, Any]:
        """Analyze general characteristics of the dataset."""
        characteristics = {
            'total_samples': len(data),
            'text_columns': len(text_columns),
            'label_columns': len(label_columns),
            'missing_values': data.isnull().sum().sum(),
            'duplicate_rows': data.duplicated().sum()
        }

        # Analyze text columns
        if text_columns:
            text_stats = {}
            for col in text_columns:
                text_data = data[col].dropna().astype(str)
                text_stats[col] = {
                    'avg_length': text_data.str.len().mean(),
                    'max_length': text_data.str.len().max(),
                    'min_length': text_data.str.len().min(),
                    'unique_count': text_data.nunique()
                }
            characteristics['text_statistics'] = text_stats

        # Analyze label columns
        if label_columns:
            label_stats = {}
            for col in label_columns:
                label_data = data[col].dropna()
                label_stats[col] = {
                    'unique_count': label_data.nunique(),
                    'most_common': str(label_data.mode().iloc[0]) if len(label_data.mode()) > 0 else 'N/A',
                    'data_type': str(label_data.dtype)
                }
            characteristics['label_statistics'] = label_stats

        return characteristics

    def _generate_recommendations(
        self,
        classification_type: ClassificationType,
        data_characteristics: Dict[str, Any],
        confidence: float
    ) -> Dict[str, Any]:
        """Generate recommendations based on detected classification type."""
        recommendations = {
            'suggested_approach': 'custom_training',
            'preprocessing_steps': [],
            'model_suggestions': [],
            'evaluation_metrics': []
        }

        total_samples = data_characteristics.get('total_samples', 0)

        # General recommendations based on data size
        if total_samples < 1000:
            recommendations['suggested_approach'] = 'llm_inference'
            recommendations['preprocessing_steps'].append('Consider data augmentation')
        elif total_samples > 10000:
            recommendations['suggested_approach'] = 'custom_training'
            recommendations['preprocessing_steps'].append('Consider using validation split')

        # Type-specific recommendations
        if classification_type == ClassificationType.BINARY:
            recommendations['model_suggestions'] = ['distilbert-base-uncased', 'roberta-base']
            recommendations['evaluation_metrics'] = ['accuracy', 'precision', 'recall', 'f1', 'auc_roc']
            recommendations['preprocessing_steps'].append('Check class balance')

        elif classification_type == ClassificationType.MULTICLASS:
            recommendations['model_suggestions'] = ['bert-base-uncased', 'distilbert-base-uncased']
            recommendations['evaluation_metrics'] = ['accuracy', 'macro_f1', 'weighted_f1']
            recommendations['preprocessing_steps'].append('Analyze class distribution')

        elif classification_type == ClassificationType.MULTILABEL:
            recommendations['model_suggestions'] = ['bert-base-uncased', 'roberta-base']
            recommendations['evaluation_metrics'] = ['hamming_loss', 'subset_accuracy', 'micro_f1', 'macro_f1']
            recommendations['preprocessing_steps'].extend(['Analyze label correlations', 'Optimize thresholds'])

        elif classification_type == ClassificationType.HIERARCHICAL:
            recommendations['model_suggestions'] = ['bert-base-uncased', 'hierarchical-bert']
            recommendations['evaluation_metrics'] = ['hierarchical_f1', 'level_accuracy', 'path_accuracy']
            recommendations['preprocessing_steps'].extend(['Validate hierarchy constraints', 'Check parent-child relationships'])

        elif classification_type == ClassificationType.FLAT:
            recommendations['model_suggestions'] = ['distilbert-base-uncased', 'bert-base-uncased']
            recommendations['evaluation_metrics'] = ['accuracy', 'weighted_f1', 'macro_f1']
            recommendations['preprocessing_steps'].append('Standard text preprocessing')

        # Confidence-based recommendations
        if confidence < 0.6:
            recommendations['preprocessing_steps'].append('Manual review recommended - low detection confidence')
            recommendations['suggested_approach'] = 'llm_inference'  # Safer for uncertain cases

        return recommendations


# Convenience function for easy usage
def detect_classification_type(
    data: pd.DataFrame,
    text_columns: Optional[List[str]] = None,
    label_columns: Optional[List[str]] = None
) -> DetectionResult:
    """
    Convenience function to detect classification type.

    Args:
        data: Input DataFrame
        text_columns: Optional list of text column names
        label_columns: Optional list of label column names

    Returns:
        DetectionResult with classification type and recommendations
    """
    detector = SmartTypeDetector()
    return detector.detect_classification_type(data, text_columns, label_columns)
