"""
Task management and result retrieval API endpoints.
"""
import logging
import pandas as pd
import json
from pathlib import Path
from typing import List, Dict, Any, Optional

from fastapi import APIRouter, Depends, HTTPException, Path as FastApiPath, Query, Response
from fastapi.responses import FileResponse
from sqlalchemy.orm import Session

# Import models and task status
from ..models.common import TaskStatus, TaskStatusEnum
from ..models import User, MessageResponse  # Import models from our models package
# Import utility functions
from ..utils import helpers as utils

# Use relative imports consistently
from ..database import get_db, get_task, get_tasks, delete_task
from ..auth import get_optional_current_user, get_current_active_user # Import the user dependencies

# Configure logging
logger = logging.getLogger(__name__)

# Create routers
tasks_router = APIRouter(prefix="/tasks", tags=["Classification Tasks"])
results_router = APIRouter(prefix="/results", tags=["Classification Tasks"]) # Separate router for results

# --- Task Status Endpoints ---

@tasks_router.get("/{task_id}", response_model=TaskStatus)
async def get_task_status(
    task_id: str = FastApiPath(..., description="ID of the task to check"),
    db: Session = Depends(get_db)
):
    """
    Retrieves the status of a background classification task.
    """
    logger.debug(f"Request received for status of task ID: {task_id}")

    # Get task from database
    db_task = get_task(db, task_id)

    # If no task found, check if this is a training session ID
    if not db_task:
        from ..database import TrainingSession, TrainingStatusEnum as TSEnum
        training_session = db.query(TrainingSession).filter(
            TrainingSession.id == task_id
        ).first()

        if training_session:
            # Create a virtual task response from training session
            logger.info(f"Found training session {task_id}, creating virtual task response")

            # Map training session status to task status
            if training_session.status == TSEnum.COMPLETED:
                task_status = TaskStatusEnum.SUCCESS
                message = f"Training completed: {training_session.current_stage or 'ready_for_inference'}"
            elif training_session.status == TSEnum.FAILED:
                task_status = TaskStatusEnum.FAILED
                message = training_session.error_message or "Training failed"
            elif training_session.status == TSEnum.RUNNING:
                task_status = TaskStatusEnum.RUNNING
                message = f"Training in progress: {training_session.current_stage or 'processing'}"
            else:
                task_status = TaskStatusEnum.PENDING
                message = "Training pending"

            # Determine result data URL
            result_data_url = None
            if training_session.status == TSEnum.COMPLETED:
                result_data_url = f"{results_router.prefix}/{task_id}/data"

            return TaskStatus(
                task_id=task_id,
                status=task_status,
                message=message,
                result_data_url=result_data_url,
                progress=training_session.progress_percentage,
                error=training_session.error_message if training_session.status == TSEnum.FAILED else None
            )

        logger.warning(f"Task ID not found: {task_id}")
        raise HTTPException(status_code=404, detail="Task not found")

    logger.debug(f"Found task {task_id} in database")
    result_data_url = None
    progress = None

    # Check if this is a training task and get progress from training session
    if db_task.task_type and "training" in db_task.task_type:
        from ..database import TrainingSession, TrainingStatusEnum as TSEnum
        training_session = db.query(TrainingSession).filter(
            TrainingSession.id == task_id
        ).first()

        if training_session:
            progress = training_session.progress_percentage

            # Sync task status with training session status
            if training_session.status == TSEnum.COMPLETED:
                # Update task to SUCCESS if training session is completed
                if db_task.status != TaskStatusEnum.SUCCESS:
                    db_task.status = TaskStatusEnum.SUCCESS
                    db_task.message = f"Training completed: {training_session.current_stage or 'ready_for_inference'}"
                    db.commit()
                    logger.info(f"Updated task {task_id} status to SUCCESS based on training session completion")
            elif training_session.status == TSEnum.FAILED:
                # Update task to FAILED if training session failed
                if db_task.status != TaskStatusEnum.FAILED:
                    db_task.status = TaskStatusEnum.FAILED
                    db_task.message = training_session.error_message or "Training failed"
                    db.commit()
                    logger.info(f"Updated task {task_id} status to FAILED based on training session failure")
            elif training_session.status == TSEnum.RUNNING:
                # Update task message with training stage if available
                if training_session.current_stage:
                    db_task.message = f"Training in progress: {training_session.current_stage}"

    if db_task.status == TaskStatusEnum.SUCCESS and db_task.result_file_path:
        # Construct the URL for fetching data dynamically using the results_router prefix
        result_data_url = f"{results_router.prefix}/{task_id}/data"

    # Create enhanced task status response
    return TaskStatus(
        task_id=task_id,
        status=db_task.status,
        message=db_task.message,
        result_data_url=result_data_url,
        progress=progress,
        error=db_task.message if db_task.status == TaskStatusEnum.FAILED else None
    )

@tasks_router.get("", response_model=List[TaskStatus])
async def list_tasks(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
    current_user: Optional[User] = Depends(get_optional_current_user)
):
    """
    Retrieves a list of classification tasks with pagination.
    """
    logger.info(f"Request received to list tasks with skip={skip}, limit={limit}")

    # Get tasks from database, filtered by user if authenticated
    user_id = current_user.id if current_user else None
    db_tasks = get_tasks(db, user_id=user_id, skip=skip, limit=limit) # Pass user_id to get_tasks

    # Convert to response model
    tasks = []
    for task in db_tasks:
        result_data_url = None
        if task.status == TaskStatusEnum.SUCCESS and task.result_file_path:
             # Construct the URL for fetching data dynamically using the results_router prefix
            result_data_url = f"{results_router.prefix}/{task.id}/data"

        tasks.append(TaskStatus(
            task_id=task.id,
            status=task.status,
            message=task.message,
            result_data_url=result_data_url
        ))

    return tasks

# --- Result Retrieval Endpoints ---

@results_router.get("/{task_id}/data", response_model=List[Dict[str, Any]])
async def get_result_data(
    task_id: str = FastApiPath(..., description="ID of the task whose results to fetch"),
    db: Session = Depends(get_db) # Add database dependency
):
    """
    Retrieves the classification results as JSON data for a completed task.
    Fetches result path from the database.
    """
    logger.info(f"Request received to fetch result data for task ID: {task_id}")

    # First check if this is a training session
    from ..database import TrainingSession
    training_session = db.query(TrainingSession).filter(TrainingSession.id == task_id).first()

    if training_session:
        # Handle training session results
        logger.info(f"Found training session for task ID: {task_id}")

        # Check for hierarchical results file
        import json
        from pathlib import Path

        results_file = Path("results") / f"hierarchical_results_{task_id}.json"
        if results_file.exists():
            try:
                with open(results_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    # Return raw_results if available (includes all LLM columns), otherwise fall back to processed results
                    if "raw_results" in data and data["raw_results"]:
                        return data["raw_results"]
                    else:
                        return data.get("results", [])
            except Exception as e:
                logger.error(f"Error reading hierarchical results file: {e}")

        # If no results file, return empty list
        return []

    # Fetch task details from the database
    db_task = get_task(db, task_id)
    if not db_task:
        logger.warning(f"Data request failed: Task ID not found in database: {task_id}")
        raise HTTPException(status_code=404, detail="Task not found")

    if db_task.status != TaskStatusEnum.SUCCESS:
        logger.warning(f"Data request failed: Task {task_id} status is {db_task.status}")
        raise HTTPException(status_code=409, detail=f"Task is not yet completed successfully (Status: {db_task.status})")

    result_path_str = db_task.result_file_path # Get path from DB record
    if not result_path_str:
        logger.error(f"Data request failed: Result path missing in database for completed task {task_id}")
        raise HTTPException(status_code=500, detail="Result file path not found for completed task.")

    result_path = Path(result_path_str)
    if not result_path.exists():
        logger.error(f"Data request failed: Result file not found at path: {result_path}")
        raise HTTPException(status_code=500, detail="Result file not found on server.")

    try:
        # Check if it's a JSON results file (for training results)
        if result_path.suffix.lower() == '.json':
            with open(result_path, 'r') as f:
                results_data = json.load(f)
            logger.info(f"Successfully read JSON results for task {task_id}")

            # For hierarchical results, extract the results array from the JSON structure
            if isinstance(results_data, dict) and 'results' in results_data:
                logger.info(f"Extracting results array from hierarchical JSON structure")
                return results_data['results']

            # For other JSON results, return as-is
            return results_data

        # Otherwise, read as CSV/Excel file (for classification results)
        # Use the utility function for loading data which handles both CSV/Excel
        df_results = utils.load_data(file_path=result_path, original_filename=result_path.name)
        if df_results is None:
             raise ValueError(f"Failed to load result data from file: {result_path}")

        # Convert DataFrame to list of dictionaries (JSON serializable)
        # Handle potential NaN/NA values which are not valid JSON -> convert to None
        results_json = df_results.replace({pd.NA: None, float('nan'): None}).to_dict('records')
        logger.info(f"Successfully read and converted results for task {task_id} to JSON.")
        return results_json
    except Exception as e:
        logger.error(f"Failed to read or convert result file {result_path} for task {task_id}: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Failed to process result file.")

@results_router.get("/{task_id}/excel")
async def get_result_excel(
    task_id: str = FastApiPath(..., description="ID of the task whose results to download as Excel/CSV"),
    db: Session = Depends(get_db) # Add database dependency
):
    """
    Retrieves the classification results as an Excel or CSV file for a completed task.
    Fetches result path from the database.
    Will try Excel first, but fall back to CSV if Excel generation fails.
    """
    logger.info(f"Request received to fetch Excel/CSV results for task ID: {task_id}")

    # Fetch task details from the database
    db_task = get_task(db, task_id)
    if not db_task:
        logger.warning(f"Excel/CSV request failed: Task ID not found in database: {task_id}")
        raise HTTPException(status_code=404, detail="Task not found")

    if db_task.status != TaskStatusEnum.SUCCESS:
        logger.warning(f"Excel/CSV request failed: Task {task_id} status is {db_task.status}")
        raise HTTPException(status_code=409, detail=f"Task is not yet completed successfully (Status: {db_task.status})")

    result_path_str = db_task.result_file_path # Get path from DB record
    if not result_path_str:
        logger.error(f"Excel/CSV request failed: Result path missing in database for completed task {task_id}")
        raise HTTPException(status_code=500, detail="Result file path not found for completed task.")

    result_path = Path(result_path_str)
    if not result_path.exists():
        logger.error(f"Excel/CSV request failed: Result file not found at path: {result_path}")
        raise HTTPException(status_code=500, detail="Result file not found on server.")

    # Read the file into a DataFrame
    try:
        # Use the utility function for loading data which handles both CSV/Excel
        df_results = utils.load_data(file_path=result_path, original_filename=result_path.name)
        if df_results is None:
             raise ValueError(f"Failed to load result data from file: {result_path}")

        # Generate a file in memory (Excel or CSV) using the utility function
        file_bytes, format_extension = utils.df_to_excel_bytes(df_results)
        if not file_bytes:
            logger.error(f"Failed to convert results to downloadable format for task {task_id}")
            raise HTTPException(status_code=500, detail="Failed to generate downloadable file.")

        # Set appropriate filename and media type based on the generated format
        if format_extension == 'csv':
            download_filename = f"classification_results_{task_id}.csv"
            media_type = 'text/csv'
            logger.info(f"Serving CSV file for task {task_id}")
        else: # xlsx
            download_filename = f"classification_results_{task_id}.xlsx"
            media_type = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            logger.info(f"Serving Excel file for task {task_id}")

        # Return the file as a response with proper headers
        return Response(
            content=file_bytes,
            media_type=media_type,
            headers={
                'Content-Disposition': f'attachment; filename="{download_filename}"'
            }
        )
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Error generating downloadable file for task {task_id}: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Failed to generate downloadable file.")


@results_router.get("/{task_id}/download", response_class=FileResponse)
async def download_results(
    task_id: str = FastApiPath(..., description="ID of the task whose results to download"),
    db: Session = Depends(get_db) # Add database dependency
):
    """
    Downloads the result file of a completed classification task.
    Fetches result path from the database.
    """
    logger.info(f"Request received to download results for task ID: {task_id}")

    # Fetch task details from the database
    db_task = get_task(db, task_id)
    if not db_task:
        logger.warning(f"Download request failed: Task ID not found in database: {task_id}")
        raise HTTPException(status_code=404, detail="Task not found")

    if db_task.status != TaskStatusEnum.SUCCESS:
        logger.warning(f"Download request failed: Task {task_id} status is {db_task.status}")
        raise HTTPException(status_code=409, detail=f"Task is not yet completed successfully (Status: {db_task.status})")

    result_path_str = db_task.result_file_path # Get path from DB record
    if not result_path_str:
        logger.error(f"Download request failed: Result path missing in database for completed task {task_id}")
        raise HTTPException(status_code=500, detail="Result file path not found for completed task.")

    result_path = Path(result_path_str)
    if not result_path.exists():
        logger.error(f"Download request failed: Result file not found at path: {result_path}")
        raise HTTPException(status_code=500, detail="Result file not found on server.")

    # Determine filename and media type based on file extension
    file_extension = result_path.suffix.lower()
    if file_extension == '.xlsx':
        media_type = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        download_filename = f"{task_id}_classification_results.xlsx"
    elif file_extension == '.csv':
        media_type = 'text/csv'
        download_filename = f"{task_id}_classification_results.csv"
    else:
        # Default to octet-stream if extension is unknown or not Excel/CSV
        media_type = 'application/octet-stream'
        download_filename = f"{task_id}_classification_results{file_extension}"

    logger.info(f"Streaming result file {result_path} for task {task_id} as '{download_filename}'")
    return FileResponse(
        path=result_path,
        filename=download_filename,
        media_type=media_type
    )


@results_router.get("/{task_id}/hierarchical")
async def get_hierarchical_results(
    task_id: str = FastApiPath(..., description="ID of the hierarchical classification task"),
    format: str = Query("json", description="Export format: json, csv, excel"),
    include_tree: bool = Query(True, description="Include hierarchy tree structure"),
    include_violations: bool = Query(True, description="Include constraint violations"),
    db: Session = Depends(get_db)
):
    """
    Get hierarchical classification results with enhanced formatting.
    Includes hierarchy tree structure, level-wise metrics, and constraint violations.
    """
    logger.info(f"Request received for hierarchical results: task {task_id}, format {format}")

    # Fetch task details from the database
    db_task = get_task(db, task_id)
    if not db_task:
        logger.warning(f"Hierarchical results request failed: Task ID not found: {task_id}")
        raise HTTPException(status_code=404, detail="Task not found")

    if db_task.status != TaskStatusEnum.SUCCESS:
        logger.warning(f"Hierarchical results request failed: Task {task_id} status is {db_task.status}")
        raise HTTPException(status_code=409, detail=f"Task is not yet completed (Status: {db_task.status})")

    # Check if this is a hierarchical classification task (Task model doesn't store classification_type)
    # Determine using the associated TrainingSession (task_id == session_id) or fall back to task.config
    from ..database import TrainingSession, ClassificationTypeEnum as CTypeEnum
    training_session = db.query(TrainingSession).filter(TrainingSession.id == task_id).first()

    is_hierarchical = False
    if training_session and getattr(training_session, 'classification_type', None) is not None:
        try:
            is_hierarchical = training_session.classification_type == CTypeEnum.HIERARCHICAL
        except Exception:
            # In case classification_type is a raw string
            is_hierarchical = str(training_session.classification_type).lower() == 'hierarchical'
    else:
        cfg = db_task.config or {}
        ct = cfg.get('classification_type')
        # Some configs store a hierarchical_config block; treat that as hierarchical
        if not ct and cfg.get('hierarchical_config') is not None:
            ct = 'hierarchical'
        is_hierarchical = (str(ct).lower() == 'hierarchical')

    if not is_hierarchical:
        raise HTTPException(status_code=400, detail="This endpoint is only for hierarchical classification tasks")

    result_path_str = db_task.result_file_path
    if not result_path_str:
        logger.error(f"Hierarchical results request failed: Result path missing for task {task_id}")
        raise HTTPException(status_code=500, detail="Result file path not found")

    result_path = Path(result_path_str)
    if not result_path.exists():
        logger.error(f"Hierarchical results request failed: Result file not found: {result_path}")
        raise HTTPException(status_code=500, detail="Result file not found")

    try:
        # Load the results - handle JSON files directly for hierarchical results
        if result_path.suffix.lower() == '.json':
            import json
            with open(result_path, 'r', encoding='utf-8') as f:
                json_data = json.load(f)

            # Handle hierarchical results JSON structure
            if isinstance(json_data, dict) and 'results' in json_data:
                # This is already a hierarchical results structure
                hierarchical_data = json_data
                hierarchical_data["task_id"] = task_id
                df_results = pd.DataFrame(json_data.get('results', []))
            else:
                # Convert to expected format
                if isinstance(json_data, list):
                    df_results = pd.DataFrame(json_data)
                else:
                    df_results = pd.DataFrame([json_data])

                hierarchical_data = {
                    "task_id": task_id,
                    "classification_type": "hierarchical",
                    "results": df_results.to_dict('records'),
                    "summary": {
                        "total_predictions": len(df_results),
                        "hierarchy_levels": [],
                        "overall_accuracy": 0.0,
                    }
                }
        else:
            # Load CSV/Excel files using the helper
            df_results = utils.load_data(file_path=result_path, original_filename=result_path.name)
            if df_results is None:
                raise ValueError(f"Failed to load result data from file: {result_path}")

            # Prepare hierarchical-specific data
            hierarchical_data = {
                "task_id": task_id,
                "classification_type": "hierarchical",
                "results": df_results.to_dict('records'),
                "summary": {
                    "total_predictions": len(df_results),
                    "hierarchy_levels": [],  # Would be populated from task metadata
                    "overall_accuracy": 0.0,  # Would be calculated from results
                }
            }

        # Add hierarchy tree structure if requested
        if include_tree:
            # Extract hierarchy levels from the data
            hierarchy_levels = hierarchical_data.get('hierarchy_levels', [])
            if not hierarchy_levels and df_results is not None and not df_results.empty:
                # Try to extract from results metadata
                first_result = df_results.iloc[0] if len(df_results) > 0 else {}
                if 'metadata' in first_result and isinstance(first_result['metadata'], dict):
                    hierarchy_levels = first_result['metadata'].get('hierarchy_levels', [])

            # Build hierarchy tree from results
            paths = []
            if df_results is not None and not df_results.empty:
                for _, row in df_results.iterrows():
                    if 'hierarchy_path' in row and row['hierarchy_path']:
                        paths.append(row['hierarchy_path'])
                    elif 'predictions' in row and isinstance(row['predictions'], list):
                        paths.append(" > ".join(row['predictions']))

            hierarchical_data["hierarchy_tree"] = {
                "levels": hierarchy_levels,
                "paths": list(set(paths)),  # Unique paths
                "statistics": {
                    "total_paths": len(set(paths)),
                    "path_frequencies": {path: paths.count(path) for path in set(paths)}
                }
            }

        # Add constraint violations if requested
        if include_violations:
            violations = []
            if df_results is not None and not df_results.empty:
                # Check for common violations
                for idx, row in df_results.iterrows():
                    if 'predictions' in row and isinstance(row['predictions'], list):
                        predictions = row['predictions']
                        # Check for incomplete paths (None/null values)
                        if None in predictions or '' in predictions:
                            violations.append({
                                "row_index": idx,
                                "violation_type": "incomplete_path",
                                "description": "Missing values in hierarchy path",
                                "predictions": predictions
                            })

            hierarchical_data["constraint_violations"] = {
                "total_violations": len(violations),
                "violations_by_type": {"incomplete_path": len(violations)},
                "violation_details": violations
            }

        if format == "json":
            return hierarchical_data
        elif format == "csv":
            # Convert to CSV with hierarchy-specific columns
            csv_data = df_results.to_csv(index=False).encode('utf-8')
            return Response(
                content=csv_data,
                media_type='text/csv',
                headers={
                    'Content-Disposition': f'attachment; filename="hierarchical_results_{task_id}.csv"'
                }
            )
        elif format == "excel":
            # Generate Excel with multiple sheets for hierarchical data
            file_bytes, format_extension = utils.df_to_excel_bytes(df_results)
            if not file_bytes:
                raise HTTPException(status_code=500, detail="Failed to generate Excel file")

            return Response(
                content=file_bytes,
                media_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                headers={
                    'Content-Disposition': f'attachment; filename="hierarchical_results_{task_id}.xlsx"'
                }
            )
        else:
            raise HTTPException(status_code=400, detail="Unsupported format. Use: json, csv, or excel")

    except Exception as e:
        logger.error(f"Error generating hierarchical results for task {task_id}: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Failed to generate hierarchical results: {str(e)}")


@tasks_router.delete("/{task_id}", response_model=MessageResponse)
async def delete_task_endpoint(
    task_id: str = FastApiPath(..., description="ID of the task to delete"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Deletes a task and its result file from the database and filesystem.
    """
    logger.info(f"Request received to delete task ID: {task_id}")

    # Get task from database
    db_task = get_task(db, task_id)
    if not db_task:
        logger.warning(f"Task ID not found: {task_id}")
        raise HTTPException(status_code=404, detail="Task not found")

    # Check if the user has permission to delete this task
    if db_task.user_id and db_task.user_id != current_user.id:
        logger.warning(f"User {current_user.id} attempted to delete task {task_id} owned by user {db_task.user_id}")
        raise HTTPException(status_code=403, detail="You don't have permission to delete this task")

    # Store task info for the response message
    task_type = db_task.task_type

    # Delete the task
    success = delete_task(db, task_id)
    if not success:
        logger.error(f"Failed to delete task {task_id}")
        raise HTTPException(status_code=500, detail="Failed to delete task")

    logger.info(f"Successfully deleted task {task_id}")
    return MessageResponse(message=f"Task '{task_type}' deleted successfully")
