// frontend/src/store/store.ts
import { create } from 'zustand';
// Import necessary types
import {
  FileInfo, LLMProviderConfig, HierarchyRow, NestedHierarchySuggestion, TaskStatus, ClassificationResultRow,
  // HF Types
  HFRule,
  // Hierarchy Configuration Types
  HierarchyConfig,
  // Add SavedHFModelInfo type
  SavedHFModelInfo
} from '../types';

// Enum for Task Types (optional but good practice)
export enum TaskType {
  LLM_CLASSIFICATION = 'LLM_CLASSIFICATION',
  HF_CLASSIFICATION = 'HF_CLASSIFICATION',
  HF_TRAINING = 'HF_TRAINING',
}

interface AppState {
  // App Workflow
  activeWorkflow: 'LLM' | 'HF' | 'NonHierarchical' | 'Compare' | 'Universal';
  setActiveWorkflow: (workflow: 'LLM' | 'HF' | 'NonHierarchical' | 'Compare' | 'Universal') => void;

  // Data & Files
  predictionFileInfo: FileInfo | null;
  setPredictionFileInfo: (info: FileInfo | null) => void;
  selectedPredictionColumns: string[];
  setSelectedPredictionColumns: (cols: string[]) => void;

  // Training Data File (for HF)
  trainingFileInfo: FileInfo | null;
  setTrainingFileInfo: (info: FileInfo | null) => void;
  // Columns selected from training data for text and hierarchy levels
  selectedTrainingTextColumns: string[];
  setSelectedTrainingTextColumns: (cols: string[]) => void;
  selectedTrainingHierarchyColumns: Record<string, string[]>; // e.g., { L1: ['ColA'], L2: ['ColB', 'ColC'], L3: [] }
  setSelectedTrainingHierarchyColumns: (cols: Record<string, string[]>) => void;

  // LLM Config
  llmConfig: LLMProviderConfig | null;
  setLLMConfig: (config: LLMProviderConfig | null) => void;

  // Hierarchy Configuration State
  hierarchyConfigs: HierarchyConfig[];
  setHierarchyConfigs: (configs: HierarchyConfig[]) => void;
  activeHierarchyConfig: HierarchyConfig | null;
  setActiveHierarchyConfig: (config: HierarchyConfig | null) => void;

  // Hierarchy Editor State (Used by both LLM and potentially HF rules)
  hierarchyRows: HierarchyRow[]; // Array of rows for the editor
  setHierarchyRows: (rows: HierarchyRow[]) => void;
  pendingSuggestion: NestedHierarchySuggestion | null; // Holds AI suggestion before applying
  setPendingSuggestion: (suggestion: NestedHierarchySuggestion | null) => void;
  hierarchyIsValid: boolean; // Flag if current rows form a valid structure
  setHierarchyIsValid: (isValid: boolean) => void;

  // --- HF Specific State ---
  savedHFModels: SavedHFModelInfo[]; // Store full model info objects
  setSavedHFModels: (models: SavedHFModelInfo[]) => void;
  selectedHFModel: SavedHFModelInfo | null; // Store the selected model object (or just ID)
  setSelectedHFModel: (model: SavedHFModelInfo | null) => void;
  hfModelRules: HFRule[]; // Rules for the selected HF model
  setHFModelRules: (rules: HFRule[]) => void;

  // --- Generic Task State (Handles LLM/HF Classification & HF Training) ---
  activeTaskId: string | null;
  activeTaskType: TaskType | null; // To know which kind of task is running
  activeTaskStatus: TaskStatus | null;
  setActiveTask: (taskId: string | null, taskType: TaskType | null) => void; // Combined setter
  setActiveTaskStatus: (status: TaskStatus | null) => void;
  // Results are kept separate as they have different structures potentially
  classificationResults: ClassificationResultRow[] | null;
  setClassificationResults: (results: ClassificationResultRow[] | null) => void;
  // Training results might just be a success/fail message in the status

}

// Export the AppState interface
export type { AppState };

export const useAppStore = create<AppState>((set) => ({
  // Initial values
  activeWorkflow: 'LLM', // Default to LLM workflow
  predictionFileInfo: null,
  selectedPredictionColumns: [],
  trainingFileInfo: null,
  selectedTrainingTextColumns: [],
  selectedTrainingHierarchyColumns: {}, // Init as empty object
  llmConfig: null,

  // Hierarchy Configuration State
  hierarchyConfigs: [],
  activeHierarchyConfig: null,

  // Hierarchy Editor State
  hierarchyRows: [],
  pendingSuggestion: null,
  hierarchyIsValid: false,

  // HF State
  savedHFModels: [], // Initialize as empty array
  selectedHFModel: null, // Initialize as null
  hfModelRules: [], // Initialize as empty array

  // Task State
  activeTaskId: null,
  activeTaskType: null,
  activeTaskStatus: null,
  classificationResults: null,

  // Actions/Setters
  setActiveWorkflow: (workflow) => set({
    activeWorkflow: workflow
  }),
  setPredictionFileInfo: (info) => set({
     predictionFileInfo: info,
     selectedPredictionColumns: [] // Reset column selection when file changes
    }),
  setSelectedPredictionColumns: (cols) => set({ selectedPredictionColumns: cols }),
  setTrainingFileInfo: (info) => set({
    trainingFileInfo: info,
    selectedTrainingTextColumns: [], // Reset selections
    selectedTrainingHierarchyColumns: {}
  }),
  setSelectedTrainingTextColumns: (cols) => set({ selectedTrainingTextColumns: cols }),
  setSelectedTrainingHierarchyColumns: (cols) => set({ selectedTrainingHierarchyColumns: cols }),

  setLLMConfig: (config) => set({ llmConfig: config }),

  // Hierarchy Configuration Setters
  setHierarchyConfigs: (configs) => set({ hierarchyConfigs: configs }),
  setActiveHierarchyConfig: (config) => set({ activeHierarchyConfig: config }),

  // Hierarchy Setters
  setHierarchyRows: (rows) => set({ hierarchyRows: rows }),
  setPendingSuggestion: (suggestion) => set({ pendingSuggestion: suggestion }),
  setHierarchyIsValid: (isValid) => set({ hierarchyIsValid: isValid }),

  // HF State Setters
  setSavedHFModels: (models) => set({ savedHFModels: models }),
  setSelectedHFModel: (model) => set({
    selectedHFModel: model,
    hfModelRules: [] // Reset rules when selected model changes
  }),
  setHFModelRules: (rules) => set({ hfModelRules: rules }),

  // Generic Task Setters
  setActiveTask: (taskId, taskType) => set({
      activeTaskId: taskId,
      activeTaskType: taskType,
      activeTaskStatus: null, // Reset status when a new task starts
      classificationResults: null // Clear previous classification results
    }),
  setActiveTaskStatus: (status) => set({ activeTaskStatus: status }),
  setClassificationResults: (results) => set({ classificationResults: results }),

}));
