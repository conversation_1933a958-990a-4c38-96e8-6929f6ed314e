"""
Comprehensive migration script to ensure training_sessions table has all required columns.
This handles any missing columns that might be needed for the TrainingSession model.
"""

import sqlite3
import logging
from pathlib import Path

logger = logging.getLogger(__name__)

def ensure_training_sessions_schema(db_path: str):
    """Ensure training_sessions table has all required columns."""
    
    # Define all expected columns with their types
    expected_columns = {
        'id': 'VARCHAR(36) PRIMARY KEY',
        'config_id': 'VARCHAR(36) NOT NULL',
        'user_id': 'INTEGER NOT NULL',
        'session_name': 'VARCHAR(255)',
        'classification_type': 'VARCHAR(50) NOT NULL',
        'training_method': 'VARCHAR(50) NOT NULL',
        'training_config': 'JSON NOT NULL',
        'status': 'VARCHAR(50) DEFAULT "pending"',
        'progress_percentage': 'REAL DEFAULT 0.0',
        'current_stage': 'VARCHAR(50)',
        'progress_data': 'JSON',
        'current_epoch': 'INTEGER',
        'total_epochs': 'INTEGER',
        'gpu_utilization': 'JSON',
        'memory_usage': 'JSON',
        'system_metrics': 'JSON',
        'model_id': 'VARCHAR(36)',
        'final_metrics': 'JSON',
        'training_history': 'JSON',
        'error_message': 'TEXT',
        'model_path': 'VARCHAR(255)',
        'tokenizer_path': 'VARCHAR(255)',
        'config_path': 'VARCHAR(255)',
        'results_file_id': 'VARCHAR(36)',
        'created_at': 'DATETIME',
        'started_at': 'DATETIME',
        'completed_at': 'DATETIME',
        'updated_at': 'DATETIME'
    }
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Get current table schema
        cursor.execute("PRAGMA table_info(training_sessions)")
        existing_columns = {column[1]: column[2] for column in cursor.fetchall()}
        
        # Find missing columns
        missing_columns = []
        for col_name, col_type in expected_columns.items():
            if col_name not in existing_columns:
                missing_columns.append((col_name, col_type))
        
        # Add missing columns
        if missing_columns:
            logger.info(f"Found {len(missing_columns)} missing columns in training_sessions table")
            
            for col_name, col_type in missing_columns:
                try:
                    # For SQLite, we need to handle the column type properly
                    if col_type.startswith('VARCHAR(36) PRIMARY KEY'):
                        # Skip primary key columns as they can't be added after table creation
                        continue
                    elif 'DEFAULT' in col_type:
                        # Extract the base type and default value
                        parts = col_type.split(' DEFAULT ')
                        base_type = parts[0]
                        default_value = parts[1].strip('"')
                        alter_sql = f"ALTER TABLE training_sessions ADD COLUMN {col_name} {base_type} DEFAULT {default_value}"
                    else:
                        alter_sql = f"ALTER TABLE training_sessions ADD COLUMN {col_name} {col_type}"
                    
                    cursor.execute(alter_sql)
                    logger.info(f"Added column: {col_name} ({col_type})")
                    
                except Exception as col_error:
                    logger.warning(f"Could not add column {col_name}: {col_error}")
            
            conn.commit()
            logger.info("Successfully updated training_sessions table schema")
        else:
            logger.info("training_sessions table schema is up to date")
        
        conn.close()
        
    except Exception as e:
        logger.error(f"Schema migration failed: {e}")
        raise

def verify_schema(db_path: str):
    """Verify that the schema migration was successful."""
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Test that we can select from all expected columns
        cursor.execute("""
            SELECT id, config_id, user_id, session_name, classification_type, 
                   training_method, training_config, status, progress_percentage,
                   current_stage, progress_data, current_epoch, total_epochs,
                   gpu_utilization, memory_usage, system_metrics, model_id,
                   final_metrics, training_history, error_message, model_path,
                   tokenizer_path, config_path, results_file_id, created_at,
                   started_at, completed_at, updated_at
            FROM training_sessions LIMIT 1
        """)
        
        logger.info("Schema verification successful - all columns are accessible")
        conn.close()
        return True
        
    except Exception as e:
        logger.error(f"Schema verification failed: {e}")
        return False

if __name__ == "__main__":
    # Default database path
    db_path = Path(__file__).parent.parent / "classyweb.db"
    
    print("Starting training_sessions schema migration...")
    ensure_training_sessions_schema(str(db_path))
    
    print("Verifying schema...")
    if verify_schema(str(db_path)):
        print("Migration completed successfully!")
    else:
        print("Migration verification failed!")
