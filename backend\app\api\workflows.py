"""Workflow API endpoints for ClassyWeb Universal Platform."""

import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timezone
from fastapi import APIRouter, Depends, HTTPException, status, Body, Query
from sqlalchemy.orm import Session
from pydantic import BaseModel

from ..database import get_db
from ..auth import get_current_user
from ..models.auth import User
from ..workflows.workflow_engine import WorkflowEngine, WorkflowStepConfig, WorkflowStepType
from .plugins import ClassificationRequest, PluginRecommendationRequest

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/workflows", tags=["workflows"])

# Initialize workflow engine
workflow_engine = WorkflowEngine()


# --- Request/Response Models ---

class CreateWorkflowRequest(BaseModel):
    template_id: Optional[str] = None
    workflow_name: Optional[str] = None
    custom_steps: Optional[List[Dict[str, Any]]] = None


class ExecuteWorkflowRequest(BaseModel):
    workflow_id: str
    input_data: Optional[Dict[str, Any]] = None


class WorkflowStepRequest(BaseModel):
    step_type: str
    name: str
    description: str
    required: bool = True
    depends_on: List[str] = []
    parameters: Dict[str, Any] = {}
    timeout_seconds: int = 300
    retry_count: int = 3
    plugin_name: Optional[str] = None


class CreateTemplateRequest(BaseModel):
    name: str
    description: str
    category: str
    steps: List[WorkflowStepRequest]
    is_public: bool = False
    tags: List[str] = []


# --- Workflow Management Endpoints ---

@router.get("/templates")
async def get_workflow_templates(
    category: Optional[str] = Query(None),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get available workflow templates."""
    try:
        # Return only the universal template
        templates = [{
            "id": "universal_classification",
            "name": "Universal Classification Workflow",
            "description": "Simplified universal workflow for all classification types",
            "category": "classification",
            "steps": [
                {"id": "upload", "name": "Upload Data"},
                {"id": "configure", "name": "Configure"},
                {"id": "train", "name": "Train Model"},
                {"id": "classify", "name": "Classify"},
                {"id": "results", "name": "Results"}
            ]
        }]

        return {
            "templates": templates,
            "total_count": len(templates),
            "filtered_by_category": category
        }

    except Exception as e:
        logger.error(f"Error getting workflow templates: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get workflow templates"
        )


@router.post("/create")
async def create_workflow(
    request: CreateWorkflowRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Create a new workflow instance."""
    try:
        # Convert custom steps if provided
        custom_steps = None
        if request.custom_steps:
            custom_steps = []
            for step_data in request.custom_steps:
                try:
                    step_type = WorkflowStepType(step_data["step_type"])
                except ValueError:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail=f"Invalid step type: {step_data['step_type']}"
                    )
                
                step_config = WorkflowStepConfig(
                    step_type=step_type,
                    name=step_data["name"],
                    description=step_data["description"],
                    required=step_data.get("required", True),
                    depends_on=step_data.get("depends_on", []),
                    parameters=step_data.get("parameters", {}),
                    timeout_seconds=step_data.get("timeout_seconds", 300),
                    retry_count=step_data.get("retry_count", 3),
                    plugin_name=step_data.get("plugin_name")
                )
                custom_steps.append(step_config)
        
        # Create workflow
        workflow_id = workflow_engine.create_workflow(
            user_id=current_user.id,
            template_id=request.template_id,
            custom_steps=custom_steps,
            workflow_name=request.workflow_name
        )
        
        return {
            "workflow_id": workflow_id,
            "message": "Workflow created successfully"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating workflow: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create workflow"
        )


@router.post("/execute")
async def execute_workflow(
    request: ExecuteWorkflowRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Execute a workflow."""
    try:
        # TODO: Add permission check to ensure user owns the workflow
        
        result = await workflow_engine.execute_workflow(
            workflow_id=request.workflow_id,
            input_data=request.input_data
        )
        
        return result
        
    except Exception as e:
        logger.error(f"Error executing workflow: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to execute workflow"
        )


@router.get("/{workflow_id}/status")
async def get_workflow_status(
    workflow_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get workflow execution status."""
    try:
        # TODO: Add permission check
        
        status_info = await workflow_engine.get_workflow_status(workflow_id)
        return status_info
        
    except Exception as e:
        logger.error(f"Error getting workflow status: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get workflow status"
        )


@router.post("/templates")
async def create_workflow_template(
    request: CreateTemplateRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Create a new workflow template."""
    try:
        # Convert steps to WorkflowStepConfig objects
        steps = []
        for step_data in request.steps:
            try:
                step_type = WorkflowStepType(step_data.step_type)
            except ValueError:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Invalid step type: {step_data.step_type}"
                )
            
            step_config = WorkflowStepConfig(
                step_type=step_type,
                name=step_data.name,
                description=step_data.description,
                required=step_data.required,
                depends_on=step_data.depends_on,
                parameters=step_data.parameters,
                timeout_seconds=step_data.timeout_seconds,
                retry_count=step_data.retry_count,
                plugin_name=step_data.plugin_name
            )
            steps.append(step_config)
        
        # Create template
        from ..workflows.workflow_engine import WorkflowTemplate
        import uuid
        
        template = WorkflowTemplate(
            id=str(uuid.uuid4()),
            name=request.name,
            description=request.description,
            category=request.category,
            steps=steps,
            created_by=str(current_user.id),
            created_at=datetime.now(timezone.utc),
            is_public=request.is_public,
            tags=request.tags
        )
        
        # Register template
        success = workflow_engine.register_template(template)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Failed to register workflow template"
            )
        
        return {
            "template_id": template.id,
            "message": f"Workflow template '{request.name}' created successfully"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating workflow template: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create workflow template"
        )


# --- Universal Platform Endpoints ---

@router.get("/engines")
async def get_classification_engines(
    classification_type: Optional[str] = Query(None),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get available classification engines."""
    try:
        engines = await universal_platform.get_available_engines(
            user_id=current_user.id,
            classification_type=classification_type
        )
        
        return {
            "engines": engines,
            "total_count": len(engines)
        }
        
    except Exception as e:
        logger.error(f"Error getting classification engines: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get classification engines"
        )


@router.post("/classify")
async def universal_classify(
    request: ClassificationRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Perform classification using the universal platform."""
    try:
        # Create platform request
        from ..universal_platform import ClassificationRequest as PlatformRequest
        
        platform_request = PlatformRequest(
            texts=request.texts,
            user_id=current_user.id,
            hierarchy_config=request.hierarchy_config,
            classification_type=request.classification_type,
            preferred_engine=request.preferred_engine,
            confidence_threshold=request.confidence_threshold,
            use_ensemble=request.use_ensemble,
            max_processing_time_ms=request.max_processing_time_ms
        )
        
        # Execute classification
        response = await universal_platform.classify(platform_request)
        
        return {
            "request_id": response.request_id,
            "results": response.results,
            "engine_used": response.engine_used,
            "processing_time_ms": response.processing_time_ms,
            "confidence_scores": response.confidence_scores,
            "recommendations": response.recommendations,
            "metadata": response.metadata
        }
        
    except Exception as e:
        logger.error(f"Error performing universal classification: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to perform classification"
        )


@router.post("/recommendations")
async def get_plugin_recommendations(
    request: PluginRecommendationRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get plugin recommendations for a specific task."""
    try:
        recommendations = await plugin_manager.get_plugin_recommendations(
            task_type=request.task_type,
            data_characteristics=request.data_characteristics
        )
        
        return {
            "recommendations": recommendations,
            "total_count": len(recommendations),
            "task_type": request.task_type
        }
        
    except Exception as e:
        logger.error(f"Error getting plugin recommendations: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get plugin recommendations"
        )
