/**
 * Enhanced Training Monitor Component for ClassyWeb ML Platform Phase 3
 * 
 * This component provides comprehensive real-time training monitoring
 * with WebSocket integration, metrics visualization, and system monitoring.
 */

import React, { useState, useEffect, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  LineChart, 
  Line, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  AreaChart,
  Area
} from 'recharts';
import { 
  Play, 
  Pause, 
  Square, 
  Activity, 
  Clock, 
  Cpu, 
  HardDrive,
  Zap,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  XCircle
} from 'lucide-react';
import { useTrainingMonitor, TrainingStage, TrainingEventType } from '@/hooks/useTrainingMonitor';
import { stopTraining } from '@/services/classificationEngineService';

interface EnhancedTrainingMonitorProps {
  sessionId: string;
  onComplete?: () => void;
  onError?: (error: Error) => void;
  onStop?: () => void;
  showSystemMetrics?: boolean;
  showAdvancedMetrics?: boolean;
  autoRefresh?: boolean;
}

export const EnhancedTrainingMonitor: React.FC<EnhancedTrainingMonitorProps> = ({
  sessionId,
  onComplete,
  onError,
  onStop,
  showSystemMetrics = true,
  showAdvancedMetrics = true,
  autoRefresh = true
}) => {
  const [isStoppingTraining, setIsStoppingTraining] = useState(false);
  const [selectedTab, setSelectedTab] = useState('progress');

  const {
    isConnected,
    isConnecting,
    error,
    progress,
    metrics,
    systemMetrics,
    events,
    isTraining,
    isCompleted,
    isFailed,
    progressPercentage,
    estimatedTimeRemaining,
    connect,
    disconnect,
    clearEvents
  } = useTrainingMonitor(sessionId, {
    autoConnect: true,
    onError: onError,
    onProgress: (newProgress) => {
      if (newProgress.stage === TrainingStage.COMPLETED && onComplete) {
        onComplete();
      }
    }
  });

  // Format time duration
  const formatDuration = (seconds: number): string => {
    if (!seconds || seconds < 0) return '--:--';
    
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);
    
    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  };

  // Get stage color
  const getStageColor = (stage: TrainingStage): string => {
    switch (stage) {
      case TrainingStage.INITIALIZATION:
      case TrainingStage.DATA_LOADING:
      case TrainingStage.PREPROCESSING:
        return 'bg-blue-500';
      case TrainingStage.TRAINING:
        return 'bg-green-500';
      case TrainingStage.VALIDATION:
      case TrainingStage.EVALUATION:
        return 'bg-yellow-500';
      case TrainingStage.OPTIMIZATION:
        return 'bg-purple-500';
      case TrainingStage.COMPLETED:
        return 'bg-green-600';
      case TrainingStage.FAILED:
        return 'bg-red-500';
      case TrainingStage.STOPPED:
        return 'bg-gray-500';
      default:
        return 'bg-gray-400';
    }
  };

  // Get stage icon
  const getStageIcon = (stage: TrainingStage) => {
    switch (stage) {
      case TrainingStage.TRAINING:
        return <Play className="h-4 w-4" />;
      case TrainingStage.COMPLETED:
        return <CheckCircle className="h-4 w-4" />;
      case TrainingStage.FAILED:
        return <XCircle className="h-4 w-4" />;
      case TrainingStage.STOPPED:
        return <Square className="h-4 w-4" />;
      default:
        return <Activity className="h-4 w-4" />;
    }
  };

  // Handle stop training
  const handleStopTraining = async () => {
    try {
      setIsStoppingTraining(true);
      await stopTraining(sessionId);
      onStop?.();
    } catch (error) {
      console.error('Failed to stop training:', error);
    } finally {
      setIsStoppingTraining(false);
    }
  };

  // Prepare metrics chart data
  const metricsChartData = useMemo(() => {
    if (!events.length) return [];
    
    return events
      .filter(event => event.type === TrainingEventType.METRICS_UPDATE)
      .map((event, index) => ({
        epoch: index + 1,
        loss: event.data.loss,
        accuracy: event.data.accuracy,
        val_loss: event.data.validation_loss,
        val_accuracy: event.data.validation_accuracy,
        learning_rate: event.data.learning_rate
      }))
      .filter(data => data.loss !== undefined);
  }, [events]);

  // Prepare system metrics chart data
  const systemMetricsChartData = useMemo(() => {
    if (!events.length) return [];
    
    return events
      .filter(event => event.type === TrainingEventType.SYSTEM_METRICS)
      .slice(-50) // Keep last 50 data points
      .map((event, index) => ({
        time: index,
        cpu: event.data.cpu_percent,
        memory: event.data.memory_percent,
        gpu: event.data.gpu_utilization || 0
      }));
  }, [events]);

  // Connection status indicator
  const ConnectionStatus = () => (
    <div className="flex items-center gap-2">
      <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-500' : isConnecting ? 'bg-yellow-500' : 'bg-red-500'}`} />
      <span className="text-sm text-gray-600">
        {isConnected ? 'Connected' : isConnecting ? 'Connecting...' : 'Disconnected'}
      </span>
      {!isConnected && !isConnecting && (
        <Button variant="outline" size="sm" onClick={connect}>
          Reconnect
        </Button>
      )}
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <h2 className="text-2xl font-bold">Training Monitor</h2>
          <ConnectionStatus />
        </div>
        <div className="flex items-center gap-2">
          {isTraining && (
            <Button
              variant="destructive"
              size="sm"
              onClick={handleStopTraining}
              disabled={isStoppingTraining}
            >
              <Square className="h-4 w-4 mr-2" />
              {isStoppingTraining ? 'Stopping...' : 'Stop Training'}
            </Button>
          )}
          <Button variant="outline" size="sm" onClick={clearEvents}>
            Clear Events
          </Button>
        </div>
      </div>

      {/* Error Alert */}
      {error && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{error.message}</AlertDescription>
        </Alert>
      )}

      {/* Progress Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            {progress && getStageIcon(progress.stage)}
            Training Progress
            {progress && (
              <Badge className={getStageColor(progress.stage)}>
                {progress.stage.replace('_', ' ').toUpperCase()}
              </Badge>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Overall Progress</span>
              <span>{progressPercentage.toFixed(1)}%</span>
            </div>
            <Progress value={progressPercentage} className="h-2" />
          </div>

          {progress && (
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div className="flex items-center gap-2">
                <Activity className="h-4 w-4 text-blue-500" />
                <div>
                  <div className="font-medium">Epoch</div>
                  <div className="text-gray-600">
                    {progress.current_epoch} / {progress.total_epochs}
                  </div>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <TrendingUp className="h-4 w-4 text-green-500" />
                <div>
                  <div className="font-medium">Step</div>
                  <div className="text-gray-600">
                    {progress.current_step} / {progress.total_steps}
                  </div>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4 text-purple-500" />
                <div>
                  <div className="font-medium">Elapsed</div>
                  <div className="text-gray-600">
                    {formatDuration(progress.elapsed_time)}
                  </div>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <Zap className="h-4 w-4 text-orange-500" />
                <div>
                  <div className="font-medium">ETA</div>
                  <div className="text-gray-600">
                    {estimatedTimeRemaining ? formatDuration(estimatedTimeRemaining) : '--:--'}
                  </div>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Detailed Metrics Tabs */}
      <Tabs value={selectedTab} onValueChange={setSelectedTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="progress">Progress</TabsTrigger>
          <TabsTrigger value="metrics">Metrics</TabsTrigger>
          {showSystemMetrics && <TabsTrigger value="system">System</TabsTrigger>}
          <TabsTrigger value="events">Events</TabsTrigger>
        </TabsList>

        <TabsContent value="progress" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Training Metrics</CardTitle>
            </CardHeader>
            <CardContent>
              {metricsChartData.length > 0 ? (
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart data={metricsChartData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="epoch" />
                    <YAxis />
                    <Tooltip />
                    <Line type="monotone" dataKey="loss" stroke="#ef4444" name="Loss" />
                    <Line type="monotone" dataKey="accuracy" stroke="#22c55e" name="Accuracy" />
                    <Line type="monotone" dataKey="val_loss" stroke="#f97316" name="Val Loss" />
                    <Line type="monotone" dataKey="val_accuracy" stroke="#3b82f6" name="Val Accuracy" />
                  </LineChart>
                </ResponsiveContainer>
              ) : (
                <div className="flex items-center justify-center h-64 text-gray-500">
                  No metrics data available yet
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="metrics" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {metrics && Object.entries(metrics).map(([key, value]) => (
              <Card key={key}>
                <CardContent className="p-4">
                  <div className="text-sm font-medium text-gray-600 capitalize">
                    {key.replace('_', ' ')}
                  </div>
                  <div className="text-2xl font-bold">
                    {typeof value === 'number' ? value.toFixed(4) : value}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        {showSystemMetrics && (
          <TabsContent value="system" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Cpu className="h-5 w-5" />
                  System Performance
                </CardTitle>
              </CardHeader>
              <CardContent>
                {systemMetricsChartData.length > 0 ? (
                  <ResponsiveContainer width="100%" height={300}>
                    <AreaChart data={systemMetricsChartData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="time" />
                      <YAxis domain={[0, 100]} />
                      <Tooltip />
                      <Area type="monotone" dataKey="cpu" stackId="1" stroke="#ef4444" fill="#ef4444" fillOpacity={0.3} name="CPU %" />
                      <Area type="monotone" dataKey="memory" stackId="2" stroke="#3b82f6" fill="#3b82f6" fillOpacity={0.3} name="Memory %" />
                      <Area type="monotone" dataKey="gpu" stackId="3" stroke="#22c55e" fill="#22c55e" fillOpacity={0.3} name="GPU %" />
                    </AreaChart>
                  </ResponsiveContainer>
                ) : (
                  <div className="flex items-center justify-center h-64 text-gray-500">
                    No system metrics available yet
                  </div>
                )}
              </CardContent>
            </Card>

            {systemMetrics && (
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Card>
                  <CardContent className="p-4 flex items-center gap-3">
                    <Cpu className="h-8 w-8 text-red-500" />
                    <div>
                      <div className="text-sm font-medium text-gray-600">CPU Usage</div>
                      <div className="text-2xl font-bold">{systemMetrics.cpu_percent.toFixed(1)}%</div>
                    </div>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-4 flex items-center gap-3">
                    <HardDrive className="h-8 w-8 text-blue-500" />
                    <div>
                      <div className="text-sm font-medium text-gray-600">Memory</div>
                      <div className="text-2xl font-bold">{systemMetrics.memory_percent.toFixed(1)}%</div>
                      <div className="text-xs text-gray-500">{systemMetrics.memory_mb.toFixed(0)} MB</div>
                    </div>
                  </CardContent>
                </Card>
                {systemMetrics.gpu_utilization !== undefined && (
                  <Card>
                    <CardContent className="p-4 flex items-center gap-3">
                      <Zap className="h-8 w-8 text-green-500" />
                      <div>
                        <div className="text-sm font-medium text-gray-600">GPU Usage</div>
                        <div className="text-2xl font-bold">{systemMetrics.gpu_utilization.toFixed(1)}%</div>
                        {systemMetrics.gpu_memory_used && systemMetrics.gpu_memory_total && (
                          <div className="text-xs text-gray-500">
                            {(systemMetrics.gpu_memory_used / 1024).toFixed(1)} / {(systemMetrics.gpu_memory_total / 1024).toFixed(1)} GB
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                )}
              </div>
            )}
          </TabsContent>
        )}

        <TabsContent value="events" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Training Events</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2 max-h-96 overflow-y-auto">
                {events.length > 0 ? (
                  events.slice().reverse().map((event, index) => (
                    <div key={index} className="flex items-start gap-3 p-3 bg-gray-50 rounded-lg">
                      <Badge variant="outline" className="text-xs">
                        {event.type.replace('_', ' ')}
                      </Badge>
                      <div className="flex-1">
                        {event.message && (
                          <div className="text-sm font-medium">{event.message}</div>
                        )}
                        <div className="text-xs text-gray-500">
                          {new Date(event.timestamp).toLocaleTimeString()}
                        </div>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="text-center text-gray-500 py-8">
                    No events recorded yet
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};
