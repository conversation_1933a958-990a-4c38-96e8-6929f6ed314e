import React from "react";
import { <PERSON>, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Eye, Database } from "lucide-react";

interface DataPreviewProps {
  data: Record<string, any>[];
  filename: string;
  totalRows: number;
  className?: string;
}

export const DataPreview = ({ data, filename, totalRows, className }: DataPreviewProps) => {
  if (!data || data.length === 0) {
    return null;
  }

  // Get column names from the first row
  const columns = Object.keys(data[0]);
  const previewRows = data.slice(0, 10); // Show up to 10 rows

  // Helper function to format cell values
  const formatCellValue = (value: any): string => {
    if (value === null || value === undefined) {
      return "";
    }
    if (typeof value === "string" && value.length > 50) {
      return value.substring(0, 50) + "...";
    }
    return String(value);
  };

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 rounded-lg bg-muted flex items-center justify-center">
            <Eye className="w-5 h-5 text-foreground" />
          </div>
          <div className="flex-1">
            <CardTitle className="text-lg">Data Preview</CardTitle>
            <CardDescription>
              First {previewRows.length} rows of {filename}
            </CardDescription>
          </div>
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="flex items-center gap-1">
              <Database className="w-3 h-3" />
              {totalRows.toLocaleString()} rows
            </Badge>
            <Badge variant="outline">
              {columns.length} columns
            </Badge>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                {columns.map((column, index) => (
                  <TableHead key={index} className="font-semibold">
                    {column}
                  </TableHead>
                ))}
              </TableRow>
            </TableHeader>
            <TableBody>
              {previewRows.map((row, rowIndex) => (
                <TableRow key={rowIndex}>
                  {columns.map((column, colIndex) => (
                    <TableCell key={colIndex} className="max-w-xs">
                      <div className="truncate" title={String(row[column] || "")}>
                        {formatCellValue(row[column])}
                      </div>
                    </TableCell>
                  ))}
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
        {totalRows > previewRows.length && (
          <div className="mt-3 text-sm text-muted-foreground text-center">
            Showing {previewRows.length} of {totalRows.toLocaleString()} rows
          </div>
        )}
      </CardContent>
    </Card>
  );
};
