/**
 * Hierarchy Validation Service
 * 
 * Provides real-time validation of hierarchy constraints and rules
 */

export interface ValidationRule {
  id: string;
  name: string;
  enabled: boolean;
  severity: 'error' | 'warning';
  description: string;
}

export interface ValidationViolation {
  type: string;
  message: string;
  severity: 'error' | 'warning';
  rowIndex?: number;
  details?: any;
}

export interface ValidationResult {
  rule_id: string;
  valid: boolean;
  violations: ValidationViolation[];
  warnings: ValidationViolation[];
}

export interface ComprehensiveValidationResult {
  overall_valid: boolean;
  rule_results: Record<string, ValidationResult>;
  total_violations: number;
  critical_violations: number;
  warnings: number;
}

export class HierarchyValidationService {
  
  /**
   * Validate hierarchy data against all enabled rules
   */
  static validateHierarchyData(
    data: Record<string, any>[],
    hierarchyLevels: Array<{ name: string; column: string; order: number }>,
    validationRules: ValidationRule[]
  ): ComprehensiveValidationResult {
    const result: ComprehensiveValidationResult = {
      overall_valid: true,
      rule_results: {},
      total_violations: 0,
      critical_violations: 0,
      warnings: 0
    };

    const enabledRules = validationRules.filter(rule => rule.enabled);
    
    for (const rule of enabledRules) {
      const ruleResult = this.validateSingleRule(rule.id, data, hierarchyLevels);
      result.rule_results[rule.id] = ruleResult;
      
      if (ruleResult.violations.length > 0) {
        result.total_violations += ruleResult.violations.length;
        if (rule.severity === 'error') {
          result.critical_violations += ruleResult.violations.length;
          result.overall_valid = false;
        } else {
          result.warnings += ruleResult.violations.length;
        }
      }
      
      if (ruleResult.warnings.length > 0) {
        result.warnings += ruleResult.warnings.length;
      }
    }

    return result;
  }

  /**
   * Validate a single rule
   */
  private static validateSingleRule(
    ruleId: string,
    data: Record<string, any>[],
    hierarchyLevels: Array<{ name: string; column: string; order: number }>
  ): ValidationResult {
    const result: ValidationResult = {
      rule_id: ruleId,
      valid: true,
      violations: [],
      warnings: []
    };

    switch (ruleId) {
      case 'parent_child_consistency':
        return this.validateParentChildConsistency(data, hierarchyLevels);
      case 'circular_dependencies':
        return this.validateCircularDependencies(data, hierarchyLevels);
      case 'orphaned_nodes':
        return this.validateOrphanedNodes(data, hierarchyLevels);
      case 'level_completeness':
        return this.validateLevelCompleteness(data, hierarchyLevels);
      case 'duplicate_paths':
        return this.validateDuplicatePaths(data, hierarchyLevels);
      default:
        result.warnings.push({
          type: 'unknown_rule',
          message: `Unknown validation rule: ${ruleId}`,
          severity: 'warning'
        });
    }

    result.valid = result.violations.length === 0;
    return result;
  }

  /**
   * Validate parent-child consistency
   */
  private static validateParentChildConsistency(
    data: Record<string, any>[],
    hierarchyLevels: Array<{ name: string; column: string; order: number }>
  ): ValidationResult {
    const result: ValidationResult = {
      rule_id: 'parent_child_consistency',
      valid: true,
      violations: [],
      warnings: []
    };

    const sortedLevels = [...hierarchyLevels].sort((a, b) => a.order - b.order);

    data.forEach((row, rowIndex) => {
      for (let i = 1; i < sortedLevels.length; i++) {
        const parentLevel = sortedLevels[i - 1];
        const childLevel = sortedLevels[i];
        
        const parentValue = row[parentLevel.column];
        const childValue = row[childLevel.column];

        // Check for missing parent when child exists
        if (childValue && !parentValue) {
          result.violations.push({
            type: 'missing_parent',
            message: `Row ${rowIndex + 1}: Child '${childValue}' at level '${childLevel.name}' has no parent at level '${parentLevel.name}'`,
            severity: 'error',
            rowIndex,
            details: {
              childLevel: childLevel.name,
              childValue,
              parentLevel: parentLevel.name
            }
          });
        }
      }
    });

    result.valid = result.violations.length === 0;
    return result;
  }

  /**
   * Validate for circular dependencies
   */
  private static validateCircularDependencies(
    data: Record<string, any>[],
    hierarchyLevels: Array<{ name: string; column: string; order: number }>
  ): ValidationResult {
    const result: ValidationResult = {
      rule_id: 'circular_dependencies',
      valid: true,
      violations: [],
      warnings: []
    };

    // Build adjacency list to detect cycles
    const graph = new Map<string, Set<string>>();
    const sortedLevels = [...hierarchyLevels].sort((a, b) => a.order - b.order);

    // Build graph from data
    data.forEach(row => {
      for (let i = 0; i < sortedLevels.length - 1; i++) {
        const parentLevel = sortedLevels[i];
        const childLevel = sortedLevels[i + 1];
        
        const parentValue = row[parentLevel.column];
        const childValue = row[childLevel.column];

        if (parentValue && childValue) {
          const parentKey = `${parentLevel.name}:${parentValue}`;
          const childKey = `${childLevel.name}:${childValue}`;
          
          if (!graph.has(parentKey)) {
            graph.set(parentKey, new Set());
          }
          graph.get(parentKey)!.add(childKey);
        }
      }
    });

    // Simple cycle detection using DFS
    const visited = new Set<string>();
    const recursionStack = new Set<string>();

    const hasCycle = (node: string): boolean => {
      if (recursionStack.has(node)) {
        return true; // Back edge found, cycle detected
      }
      if (visited.has(node)) {
        return false;
      }

      visited.add(node);
      recursionStack.add(node);

      const neighbors = graph.get(node) || new Set();
      for (const neighbor of neighbors) {
        if (hasCycle(neighbor)) {
          return true;
        }
      }

      recursionStack.delete(node);
      return false;
    };

    // Check for cycles
    for (const node of graph.keys()) {
      if (!visited.has(node) && hasCycle(node)) {
        result.violations.push({
          type: 'circular_dependency',
          message: `Circular dependency detected in hierarchy structure`,
          severity: 'error',
          details: { node }
        });
        break; // One cycle detection is enough for this validation
      }
    }

    result.valid = result.violations.length === 0;
    return result;
  }

  /**
   * Validate for orphaned nodes
   */
  private static validateOrphanedNodes(
    data: Record<string, any>[],
    hierarchyLevels: Array<{ name: string; column: string; order: number }>
  ): ValidationResult {
    const result: ValidationResult = {
      rule_id: 'orphaned_nodes',
      valid: true,
      violations: [],
      warnings: []
    };

    const sortedLevels = [...hierarchyLevels].sort((a, b) => a.order - b.order);

    // Track all values at each level and their relationships
    const levelValues = new Map<string, Set<string>>();
    const parentChildMap = new Map<string, Set<string>>();

    // Build level value sets and parent-child relationships
    data.forEach(row => {
      for (let i = 0; i < sortedLevels.length; i++) {
        const level = sortedLevels[i];
        const value = row[level.column];
        
        if (value) {
          const levelKey = level.name;
          if (!levelValues.has(levelKey)) {
            levelValues.set(levelKey, new Set());
          }
          levelValues.get(levelKey)!.add(value);

          // Track parent-child relationships
          if (i > 0) {
            const parentLevel = sortedLevels[i - 1];
            const parentValue = row[parentLevel.column];
            
            if (parentValue) {
              const parentKey = `${parentLevel.name}:${parentValue}`;
              if (!parentChildMap.has(parentKey)) {
                parentChildMap.set(parentKey, new Set());
              }
              parentChildMap.get(parentKey)!.add(`${level.name}:${value}`);
            } else {
              // Child exists without parent - this is an orphaned node
              result.violations.push({
                type: 'orphaned_node',
                message: `Value '${value}' at level '${level.name}' has no parent relationship`,
                severity: 'warning',
                details: {
                  level: level.name,
                  value,
                  expectedParentLevel: parentLevel.name
                }
              });
            }
          }
        }
      }
    });

    result.valid = result.violations.length === 0;
    return result;
  }

  /**
   * Validate level completeness
   */
  private static validateLevelCompleteness(
    data: Record<string, any>[],
    hierarchyLevels: Array<{ name: string; column: string; order: number }>
  ): ValidationResult {
    const result: ValidationResult = {
      rule_id: 'level_completeness',
      valid: true,
      violations: [],
      warnings: []
    };

    const sortedLevels = [...hierarchyLevels].sort((a, b) => a.order - b.order);

    data.forEach((row, rowIndex) => {
      const missingLevels: string[] = [];
      let hasDeepValue = false;

      // Check from deepest to shallowest to detect incomplete paths
      for (let i = sortedLevels.length - 1; i >= 0; i--) {
        const level = sortedLevels[i];
        const value = row[level.column];

        if (value && String(value).trim()) {
          hasDeepValue = true;
        } else if (hasDeepValue) {
          // Missing intermediate level
          missingLevels.unshift(level.name);
        }
      }

      if (missingLevels.length > 0) {
        result.violations.push({
          type: 'incomplete_levels',
          message: `Row ${rowIndex + 1}: Missing intermediate levels: ${missingLevels.join(', ')}`,
          severity: 'warning',
          rowIndex,
          details: { missingLevels }
        });
      }
    });

    result.valid = result.violations.length === 0;
    return result;
  }

  /**
   * Validate for duplicate paths
   */
  private static validateDuplicatePaths(
    data: Record<string, any>[],
    hierarchyLevels: Array<{ name: string; column: string; order: number }>
  ): ValidationResult {
    const result: ValidationResult = {
      rule_id: 'duplicate_paths',
      valid: true,
      violations: [],
      warnings: []
    };

    const sortedLevels = [...hierarchyLevels].sort((a, b) => a.order - b.order);
    const pathCounts = new Map<string, number[]>();

    data.forEach((row, rowIndex) => {
      const pathValues: string[] = [];
      
      for (const level of sortedLevels) {
        const value = row[level.column];
        pathValues.push(value ? String(value).trim() : '');
      }

      // Create path key from non-empty values
      const pathKey = pathValues.filter(v => v).join('|');
      
      if (pathKey) {
        if (!pathCounts.has(pathKey)) {
          pathCounts.set(pathKey, []);
        }
        pathCounts.get(pathKey)!.push(rowIndex);
      }
    });

    // Find duplicates
    for (const [pathKey, rowIndices] of pathCounts.entries()) {
      if (rowIndices.length > 1) {
        result.violations.push({
          type: 'duplicate_path',
          message: `Duplicate path '${pathKey}' found in rows: ${rowIndices.map(i => i + 1).join(', ')}`,
          severity: 'error',
          details: {
            path: pathKey,
            rowIndices,
            count: rowIndices.length
          }
        });
      }
    }

    result.valid = result.violations.length === 0;
    return result;
  }
}
