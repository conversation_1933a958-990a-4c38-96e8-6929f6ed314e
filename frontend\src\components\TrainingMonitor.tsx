import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { 
  Activity, 
  Play, 
  Pause, 
  Square, 
  TrendingUp,
  TrendingDown,
  Cpu,
  MemoryStick,
  HardDrive,
  Zap,
  Target,
  BarChart3,
  CheckCircle2
} from "lucide-react";

interface TrainingMonitorProps {
  modelName: string;
  onTrainingComplete: (results: any) => void;
}

interface MetricData {
  epoch: number;
  loss: number;
  accuracy: number;
  val_loss: number;
  val_accuracy: number;
  learning_rate: number;
}

export const TrainingMonitor = ({ modelName, onTrainingComplete }: TrainingMonitorProps) => {
  const [isTraining, setIsTraining] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [currentEpoch, setCurrentEpoch] = useState(0);
  const [totalEpochs] = useState(100);
  const [progress, setProgress] = useState(0);
  const [timeElapsed, setTimeElapsed] = useState(0);
  const [estimatedTimeRemaining, setEstimatedTimeRemaining] = useState(0);
  
  const [metrics, setMetrics] = useState<MetricData[]>([]);
  const [currentMetrics, setCurrentMetrics] = useState({
    loss: 0,
    accuracy: 0,
    val_loss: 0,
    val_accuracy: 0,
    learning_rate: 0.001
  });

  const [systemMetrics, setSystemMetrics] = useState({
    cpuUsage: 0,
    memoryUsage: 0,
    gpuUsage: 0,
    diskUsage: 0
  });

  const [trainingLogs, setTrainingLogs] = useState<string[]>([]);

  useEffect(() => {
    if (isTraining && !isPaused) {
      const interval = setInterval(() => {
        simulateTrainingStep();
      }, 1000);
      return () => clearInterval(interval);
    }
  }, [isTraining, isPaused, currentEpoch]);

  const simulateTrainingStep = () => {
    if (currentEpoch >= totalEpochs) {
      setIsTraining(false);
      onTrainingComplete({
        finalAccuracy: currentMetrics.val_accuracy,
        totalEpochs: currentEpoch,
        trainingTime: timeElapsed
      });
      return;
    }

    const newEpoch = currentEpoch + 1;
    setCurrentEpoch(newEpoch);
    setProgress((newEpoch / totalEpochs) * 100);
    setTimeElapsed(prev => prev + 30); // 30 seconds per epoch
    setEstimatedTimeRemaining((totalEpochs - newEpoch) * 30);

    // Simulate improving metrics
    const newMetrics = {
      loss: Math.max(0.1, 2.0 - (newEpoch / totalEpochs) * 1.8 + Math.random() * 0.1),
      accuracy: Math.min(0.95, 0.5 + (newEpoch / totalEpochs) * 0.4 + Math.random() * 0.05),
      val_loss: Math.max(0.15, 2.2 - (newEpoch / totalEpochs) * 1.9 + Math.random() * 0.15),
      val_accuracy: Math.min(0.92, 0.45 + (newEpoch / totalEpochs) * 0.42 + Math.random() * 0.05),
      learning_rate: 0.001 * Math.pow(0.95, newEpoch)
    };

    setCurrentMetrics(newMetrics);
    setMetrics(prev => [...prev, { epoch: newEpoch, ...newMetrics }]);

    // Simulate system metrics
    setSystemMetrics({
      cpuUsage: 60 + Math.random() * 30,
      memoryUsage: 70 + Math.random() * 20,
      gpuUsage: 80 + Math.random() * 15,
      diskUsage: 45 + Math.random() * 10
    });

    // Add training log
    setTrainingLogs(prev => [
      ...prev.slice(-9), // Keep last 9 logs
      `Epoch ${newEpoch}/${totalEpochs} - loss: ${newMetrics.loss.toFixed(4)} - accuracy: ${newMetrics.accuracy.toFixed(4)} - val_loss: ${newMetrics.val_loss.toFixed(4)} - val_accuracy: ${newMetrics.val_accuracy.toFixed(4)}`
    ]);
  };

  const handleStartTraining = () => {
    setIsTraining(true);
    setIsPaused(false);
  };

  const handlePauseTraining = () => {
    setIsPaused(!isPaused);
  };

  const handleStopTraining = () => {
    setIsTraining(false);
    setIsPaused(false);
  };

  const formatTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    return `${hours}h ${minutes}m ${secs}s`;
  };

  const getMetricTrend = (metricName: string) => {
    if (metrics.length < 2) return null;
    const recent = metrics.slice(-5);
    const current = recent[recent.length - 1][metricName as keyof MetricData];
    const previous = recent[recent.length - 2][metricName as keyof MetricData];
    return current > previous ? 'up' : 'down';
  };

  return (
    <div className="space-y-6">
      {/* Training Status */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 rounded-lg bg-ml-primary/10 flex items-center justify-center">
                <Activity className="w-5 h-5 text-ml-primary" />
              </div>
              <div>
                <CardTitle>Training Monitor</CardTitle>
                <CardDescription>
                  Real-time monitoring of {modelName} training progress
                </CardDescription>
              </div>
            </div>
            <div className="flex gap-2">
              {!isTraining ? (
                <Button onClick={handleStartTraining}>
                  <Play className="w-4 h-4 mr-2" />
                  Start Training
                </Button>
              ) : (
                <>
                  <Button variant="outline" onClick={handlePauseTraining}>
                    {isPaused ? <Play className="w-4 h-4" /> : <Pause className="w-4 h-4" />}
                  </Button>
                  <Button variant="outline" onClick={handleStopTraining}>
                    <Square className="w-4 h-4" />
                  </Button>
                </>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {isTraining && (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-ml-success rounded-full animate-pulse"></div>
                  <span className="font-medium">
                    {isPaused ? 'Training paused' : 'Training in progress...'}
                  </span>
                </div>
                <Badge className="bg-ml-primary/10 text-ml-primary">
                  Epoch {currentEpoch}/{totalEpochs}
                </Badge>
              </div>
              
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Progress</span>
                  <span>{Math.round(progress)}%</span>
                </div>
                <Progress value={progress} className="h-2" />
              </div>

              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="p-3 bg-muted/50 rounded-lg text-center">
                  <div className="text-lg font-bold">
                    {formatTime(timeElapsed)}
                  </div>
                  <div className="text-xs text-muted-foreground">Elapsed</div>
                </div>
                <div className="p-3 bg-muted/50 rounded-lg text-center">
                  <div className="text-lg font-bold">
                    {formatTime(estimatedTimeRemaining)}
                  </div>
                  <div className="text-xs text-muted-foreground">Remaining</div>
                </div>
                <div className="p-3 bg-muted/50 rounded-lg text-center">
                  <div className="text-lg font-bold text-ml-success">
                    {(currentMetrics.accuracy * 100).toFixed(1)}%
                  </div>
                  <div className="text-xs text-muted-foreground">Accuracy</div>
                </div>
                <div className="p-3 bg-muted/50 rounded-lg text-center">
                  <div className="text-lg font-bold text-ml-secondary">
                    {(currentMetrics.val_accuracy * 100).toFixed(1)}%
                  </div>
                  <div className="text-xs text-muted-foreground">Val Accuracy</div>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Detailed Metrics */}
      <Tabs defaultValue="metrics" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="metrics">Metrics</TabsTrigger>
          <TabsTrigger value="system">System</TabsTrigger>
          <TabsTrigger value="logs">Logs</TabsTrigger>
          <TabsTrigger value="charts">Charts</TabsTrigger>
        </TabsList>

        <TabsContent value="metrics" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Target className="w-5 h-5" />
                  Training Metrics
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Loss</span>
                    <div className="flex items-center gap-2">
                      <span className="font-mono">{currentMetrics.loss.toFixed(4)}</span>
                      {getMetricTrend('loss') === 'down' ? (
                        <TrendingDown className="w-4 h-4 text-ml-success" />
                      ) : (
                        <TrendingUp className="w-4 h-4 text-ml-error" />
                      )}
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Accuracy</span>
                    <div className="flex items-center gap-2">
                      <span className="font-mono">{(currentMetrics.accuracy * 100).toFixed(2)}%</span>
                      {getMetricTrend('accuracy') === 'up' ? (
                        <TrendingUp className="w-4 h-4 text-ml-success" />
                      ) : (
                        <TrendingDown className="w-4 h-4 text-ml-error" />
                      )}
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Learning Rate</span>
                    <span className="font-mono">{currentMetrics.learning_rate.toExponential(2)}</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CheckCircle2 className="w-5 h-5" />
                  Validation Metrics
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Val Loss</span>
                    <div className="flex items-center gap-2">
                      <span className="font-mono">{currentMetrics.val_loss.toFixed(4)}</span>
                      {getMetricTrend('val_loss') === 'down' ? (
                        <TrendingDown className="w-4 h-4 text-ml-success" />
                      ) : (
                        <TrendingUp className="w-4 h-4 text-ml-error" />
                      )}
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Val Accuracy</span>
                    <div className="flex items-center gap-2">
                      <span className="font-mono">{(currentMetrics.val_accuracy * 100).toFixed(2)}%</span>
                      {getMetricTrend('val_accuracy') === 'up' ? (
                        <TrendingUp className="w-4 h-4 text-ml-success" />
                      ) : (
                        <TrendingDown className="w-4 h-4 text-ml-error" />
                      )}
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Overfitting Risk</span>
                    <Badge variant={Math.abs(currentMetrics.accuracy - currentMetrics.val_accuracy) > 0.1 ? 'destructive' : 'default'}>
                      {Math.abs(currentMetrics.accuracy - currentMetrics.val_accuracy) > 0.1 ? 'High' : 'Low'}
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="system" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Cpu className="w-5 h-5" />
                  CPU Usage
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Usage</span>
                    <span>{systemMetrics.cpuUsage.toFixed(1)}%</span>
                  </div>
                  <Progress value={systemMetrics.cpuUsage} className="h-2" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <MemoryStick className="w-5 h-5" />
                  Memory Usage
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Usage</span>
                    <span>{systemMetrics.memoryUsage.toFixed(1)}%</span>
                  </div>
                  <Progress value={systemMetrics.memoryUsage} className="h-2" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Zap className="w-5 h-5" />
                  GPU Usage
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Usage</span>
                    <span>{systemMetrics.gpuUsage.toFixed(1)}%</span>
                  </div>
                  <Progress value={systemMetrics.gpuUsage} className="h-2" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <HardDrive className="w-5 h-5" />
                  Disk I/O
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Usage</span>
                    <span>{systemMetrics.diskUsage.toFixed(1)}%</span>
                  </div>
                  <Progress value={systemMetrics.diskUsage} className="h-2" />
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="logs" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="w-5 h-5" />
                Training Logs
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="bg-black text-green-400 p-4 rounded-lg font-mono text-sm h-64 overflow-y-auto">
                {trainingLogs.map((log, index) => (
                  <div key={index} className="mb-1">
                    {log}
                  </div>
                ))}
                {trainingLogs.length === 0 && (
                  <div className="text-gray-500">Training logs will appear here...</div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="charts" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Training Charts</CardTitle>
              <CardDescription>
                Visual representation of training progress (charts would be implemented with a charting library)
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-64 bg-muted/30 rounded-lg flex items-center justify-center">
                <div className="text-center">
                  <BarChart3 className="w-12 h-12 mx-auto text-muted-foreground mb-2" />
                  <p className="text-muted-foreground">Training charts will be displayed here</p>
                  <p className="text-sm text-muted-foreground">Integration with Chart.js or similar library needed</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};
