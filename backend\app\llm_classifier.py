# Refactored backend/llm_classifier.py

import logging
import pandas as pd
import requests
import traceback
import os
import sys
import json
from . import config # Use relative import for config in the same directory
from typing import List, Dict, Optional, Any, Tuple
from .hierarchy_manager import hierarchy_manager, HierarchySchema

# LangChain components
from langchain_groq import ChatGroq
from langchain_community.chat_models import ChatOllama
from langchain_openai import ChatOpenAI
from langchain_google_genai import Chat<PERSON><PERSON>gleGenerativeAI

from langchain_core.prompts import PromptTemplate
from langchain.chains import LLMChain
from langchain_core.output_parsers import PydanticOutputParser
from langchain.output_parsers import OutputFixingParser
from pydantic import BaseModel, Field, create_model

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Debug import information
logger.info("--- Debugging Imports in %s ---", __file__)
logger.info("Current Working Directory (CWD): %s", os.getcwd())
logger.info("System Path (sys.path): %s", sys.path)
logger.info("--- End Debugging Imports ---")

# Log successful import of config
logger.info("Successfully imported 'app.config'")

# Note: The LLMClassifier class has been removed as per recommendation #2 in recommendations.md.
# The module-level functions are now exported directly for simpler usage.



# Custom OpenRouter implementation
class ChatOpenRouter(ChatOpenAI):
    def __init__(self,
                 model: str,
                 openrouter_api_key: Optional[str] = None,
                 base_url: str = "https://openrouter.ai/api/v1",
                 **kwargs):
        openrouter_api_key = openrouter_api_key or os.getenv('OPENROUTER_API_KEY')
        super().__init__(base_url=base_url,
                         api_key=openrouter_api_key,
                         model_name=model, **kwargs)


# Logging is already set up at the top of the file

# --- Pydantic Models ---
# Model for AI-generated hierarchy suggestion
class AISuggestionSubSegment(BaseModel):
    name: str = Field(..., description="Name of the sub-segment")
    keywords: List[str] = Field(..., description="List of 3-7 relevant keywords for this sub-segment")

class AISuggestionSegment(BaseModel):
    name: str = Field(..., description="Name of the segment")
    subsegments: List[AISuggestionSubSegment] = Field(..., description="List of subsegments within this segment")

class AISuggestionCategory(BaseModel):
    name: str = Field(..., description="Name of the category")
    segments: List[AISuggestionSegment] = Field(..., description="List of segments within this category")

class AISuggestionTheme(BaseModel):
    name: str = Field(..., description="Name of the theme")
    categories: List[AISuggestionCategory] = Field(..., description="List of categories within this theme")

def create_dynamic_hierarchy_model(level_names: List[str]):
    """Creates a dynamic Pydantic model for AI hierarchy suggestions based on level names."""
    if not level_names:
        # Fallback to default structure
        class AISuggestionHierarchy(BaseModel):
            themes: List[AISuggestionTheme] = Field(..., description="The complete hierarchical structure.")
        return AISuggestionHierarchy

    # Use the first level name as the root field (pluralized)
    root_field_name = level_names[0].lower() + 's'
    root_field_description = f"The complete {level_names[0].lower()} hierarchical structure."

    # Create field definition
    fields = {
        root_field_name: (List[AISuggestionTheme], Field(..., description=root_field_description))
    }

    # Create dynamic model
    DynamicHierarchyModel = create_model(
        "DynamicAISuggestionHierarchy",
        **fields,
        __doc__=f"Dynamically generated model for AI hierarchy suggestions with {level_names[0]} as root level."
    )

    return DynamicHierarchyModel

# Model for single row categorization result
class LLMCategorizationResult(BaseModel):
    """Structure for the LLM to return classification for one text row.

    This model is dynamically modified at runtime to include fields based on the user's hierarchy levels.
    The base model only includes the reasoning field, and other fields are added dynamically.
    """
    reasoning: Optional[str] = Field(None, description="Brief explanation for the categorization.")

# Function to create a dynamic LLMCategorizationResult model with fields based on hierarchy levels or flat labels
def create_dynamic_categorization_model(level_names: List[str], is_flat_classification: bool = False):
    """Creates a dynamic Pydantic model with fields based on the provided level names or flat labels."""
    if not level_names:
        return LLMCategorizationResult

    # Create field definitions
    fields = {
        "reasoning": (Optional[str], Field(None, description="Brief explanation for the categorization."))
    }

    if is_flat_classification:
        # For flat multi-label classification, create a single field for all labels
        fields["labels"] = (Optional[List[str]], Field(None, description="List of assigned labels for multi-label classification."))
    else:
        # Add a field for each hierarchy level with enhanced descriptions
        for i, level_name in enumerate(level_names):
            field_name = level_name.lower().replace(' ', '_').replace('-', '_')  # Convert to snake_case for field names

            # Create more descriptive field descriptions based on hierarchy position
            if i == 0:
                field_description = f"The main {level_name.lower()} category (highest level in hierarchy)."
            elif i == len(level_names) - 1:
                field_description = f"The specific {level_name.lower()} subcategory (most detailed level)."
            else:
                field_description = f"The {level_name.lower()} subcategory (level {i+1} in hierarchy)."

            fields[field_name] = (Optional[str], Field(None, description=field_description))

    # Create a new model class dynamically
    DynamicLLMCategorizationResult = create_model(
        "DynamicLLMCategorizationResult",
        **fields,
        __doc__="Dynamically generated model for LLM categorization results based on custom hierarchy levels or flat labels."
    )

    logging.info(f"Created dynamic categorization model with fields: {list(fields.keys())}, flat_classification: {is_flat_classification}")
    return DynamicLLMCategorizationResult


def detect_classification_type(hierarchy_dict: Dict[str, Any]) -> Tuple[bool, List[str]]:
    """
    Detect if the classification should be hierarchical or flat multi-label.

    Returns:
        Tuple of (is_flat_classification, labels_or_levels)
    """
    if not hierarchy_dict:
        return False, []

    # Check if this is a flat label structure
    # Flat structure indicators:
    # 1. Single level with multiple independent labels
    # 2. Labels list without nested structure
    # 3. Special key indicating flat classification

    if 'labels' in hierarchy_dict and isinstance(hierarchy_dict['labels'], list):
        # Direct labels list - this is flat classification
        return True, hierarchy_dict['labels']

    if 'classification_type' in hierarchy_dict and hierarchy_dict['classification_type'] == 'flat':
        # Explicitly marked as flat classification
        labels = []
        if 'labels' in hierarchy_dict:
            labels = hierarchy_dict['labels']
        elif 'categories' in hierarchy_dict:
            labels = list(hierarchy_dict['categories'].keys()) if isinstance(hierarchy_dict['categories'], dict) else hierarchy_dict['categories']
        return True, labels

    # Check if it's a hierarchical structure with nested levels
    # Look for any nested structure that suggests hierarchy
    nested_keys = []
    for key, value in hierarchy_dict.items():
        if isinstance(value, list) and value and isinstance(value[0], dict):
            # This looks like a hierarchical level with nested objects
            nested_keys.append(key)

    if nested_keys:
        # This is a hierarchical structure - try to infer levels from the structure
        levels = []
        sample_item = None

        # Find the first nested structure to analyze
        for key in nested_keys:
            if hierarchy_dict[key]:
                sample_item = hierarchy_dict[key][0]
                levels.append(key.rstrip('s').title())  # Convert 'themes' -> 'Theme'
                break

        # Recursively find nested levels
        current = sample_item
        while current and isinstance(current, dict):
            found_nested = False
            for sub_key, sub_value in current.items():
                if isinstance(sub_value, list) and sub_value and isinstance(sub_value[0], dict):
                    levels.append(sub_key.rstrip('s').title())
                    current = sub_value[0]
                    found_nested = True
                    break
            if not found_nested:
                break

        return False, levels

    # Check if it's a simple single-level structure that could be treated as flat
    if len(hierarchy_dict) == 1:
        key, value = next(iter(hierarchy_dict.items()))
        if isinstance(value, (list, dict)):
            # Could be flat if it's just a list of labels or simple categories
            if isinstance(value, list):
                return True, value
            elif isinstance(value, dict) and all(not isinstance(v, dict) for v in value.values()):
                # Simple key-value pairs without nesting
                return True, list(value.keys())

    # Default to hierarchical classification for complex structures
    # Extract hierarchy levels from nested structure
    levels = []
    current = hierarchy_dict

    # Try to infer levels from structure depth
    def extract_levels(data, current_levels=[]):
        if isinstance(data, dict):
            for key, value in data.items():
                if isinstance(value, dict) and value:
                    new_levels = current_levels + [key.replace('_', ' ').title()]
                    return extract_levels(value, new_levels)
            return current_levels + list(data.keys())[:1]
        return current_levels

    levels = extract_levels(current)
    if not levels:
        levels = ['Category']  # Fallback

    return False, levels


# --- LLM Client Initialization ---
# Note: Caching is usually handled at the application level (e.g., FastAPI dependency) rather than here.
def initialize_llm_client(provider: str, endpoint: str, api_key: Optional[str], model_name: str):
    """Initializes and returns the LangChain LLM client."""
    logging.info(f"LLM Init: Initializing client for {provider} - Model: {model_name}")
    llm = None # Initialize llm to None
    try:
        if provider == "Groq":
            if not api_key:
                logging.error("LLM Init Error: Groq API key is missing.")
                raise ValueError("Groq API key is missing.")
            llm = ChatGroq(
                temperature=config.DEFAULT_LLM_TEMPERATURE,
                groq_api_key=api_key,
                model_name=model_name,
                request_timeout=60, # Longer timeout for potentially complex tasks
            )
        elif provider == "Ollama":
             # Ollama uses base_url, not api_base in newer langchain
            llm = ChatOllama(
                base_url=endpoint,
                model=model_name,
                temperature=config.DEFAULT_LLM_TEMPERATURE,
                request_timeout=120 # Potentially longer timeout for local models
            )
            # Simple check if Ollama endpoint is reachable (optional)
            try:
                requests.get(endpoint, timeout=5) # Quick check if endpoint is responsive
            except requests.exceptions.ConnectionError:
                 logging.warning(f"LLM Init Warning: Cannot reach Ollama endpoint at {endpoint}. Ensure Ollama is running.")
                 # Proceeding anyway, as Ollama might start later or be accessible by the LangChain lib differently
            except Exception as e_check:
                 logging.warning(f"LLM Init Warning: Error during optional check of Ollama endpoint: {e_check}")

        elif provider == "OpenAI":
            if not api_key:
                logging.error("LLM Init Error: OpenAI API key is missing.")
                raise ValueError("OpenAI API key is missing.")
            llm = ChatOpenAI(
                temperature=config.DEFAULT_LLM_TEMPERATURE,
                api_key=api_key,
                model_name=model_name,
                base_url=endpoint,  # Allow custom base_url
                request_timeout=60
            )

        elif provider == "Gemini":
            if not api_key:
                logging.error("LLM Init Error: Google API key is missing.")
                raise ValueError("Google API key is missing.")
            llm = ChatGoogleGenerativeAI(
                temperature=config.DEFAULT_LLM_TEMPERATURE,
                google_api_key=api_key,
                model=model_name,
                request_timeout=60
            )

        elif provider == "Openrouter":
            if not api_key:
                logging.error("LLM Init Error: Openrouter API key is missing.")
                raise ValueError("Openrouter API key is missing.")
            llm = ChatOpenRouter(
                temperature=config.DEFAULT_LLM_TEMPERATURE,
                openrouter_api_key=api_key,
                model=model_name,
                base_url=endpoint,
                request_timeout=60
            )
        else:
            logging.error(f"LLM Init Error: Unsupported provider '{provider}'")
            raise ValueError(f"Unsupported provider '{provider}'")

        if llm: # Only test if llm was successfully initialized
            # Simple invocation test to confirm basic connectivity and authentication
            logging.info("LLM Init: Testing client connection with a simple request...")
            # Consider making this test optional or configurable
            # llm.invoke("Respond with 'OK'") # Use single quotes for clarity
            logging.info(f"LLM Client ({provider} - {model_name}) Initialized.")
            return llm
        else:
             # This case should ideally be caught by the provider checks, but as a safeguard:
             logging.error(f"LLM Init Error: Client for {provider} could not be initialized.")
             raise RuntimeError(f"Client for {provider} could not be initialized.")

    except Exception as e:
        logging.error(f"LLM Init Error: Failed to initialize {provider} client: {e}")
        logging.error(traceback.format_exc())
        raise e # Re-raise the exception to be handled by the caller

# --- Model Fetching ---
def fetch_available_models(provider: str, endpoint: str, api_key: Optional[str]) -> List[str]:
    """Fetches available model names from the selected provider's endpoint."""
    logging.info(f"Fetching models for {provider} from {endpoint}...")
    headers = {}
    models = []

    try:
        if provider == "Groq":
            if not api_key:
                logging.error("Cannot fetch Groq models: API key missing.")
                return []
            # Groq uses OpenAI compatible endpoint for models
            url = "https://api.groq.com/openai/v1/models"
            headers = {"Authorization": f"Bearer {api_key}"}
            response = requests.get(url, headers=headers, timeout=10)
            response.raise_for_status() # Raise error for bad status codes
            data = response.json()
            models = sorted([model['id'] for model in data.get('data', []) if 'id' in model])

        elif provider == "Ollama":
            # Ollama uses /api/tags endpoint
            url = f"{endpoint.strip('/')}/api/tags"
            response = requests.get(url, timeout=10)
            response.raise_for_status()
            data = response.json()
            # Basic filtering heuristic: exclude models likely intended for embeddings based on name/family
            models = sorted([
                model['name'] for model in data.get('models', [])
                if 'embed' not in model.get('details', {}).get('family', '').lower()
            ])

        elif provider == "OpenAI":
            if not api_key:
                logging.error("Cannot fetch OpenAI models: API key missing.")
                return []
            # OpenAI models endpoint
            url = f"{endpoint.strip('/')}/models"
            headers = {"Authorization": f"Bearer {api_key}"}
            response = requests.get(url, headers=headers, timeout=10)
            response.raise_for_status()
            data = response.json()
            # Filter for chat models only
            models = sorted([model['id'] for model in data.get('data', [])
                           if 'id' in model and ('gpt' in model['id'].lower() or 'turbo' in model['id'].lower())])

        elif provider == "Gemini":
            if not api_key:
                logging.error("Cannot fetch Gemini models: API key missing.")
                return []
            # Gemini doesn't have a models endpoint, return hardcoded list
            models = ["gemini-pro", "gemini-1.5-pro", "gemini-1.5-flash"]

        elif provider == "Openrouter":
            if not api_key:
                logging.error("Cannot fetch Openrouter models: API key missing.")
                return []
            # Openrouter models endpoint
            url = f"{endpoint.strip('/')}/models"
            headers = {"Authorization": f"Bearer {api_key}"}
            response = requests.get(url, headers=headers, timeout=10)
            response.raise_for_status()
            data = response.json()
            # Extract model IDs
            models = sorted([model['id'] for model in data.get('data', []) if 'id' in model])

        if not models:
             logging.warning(f"No models found for {provider} at {endpoint}.")
        else:
             logging.info(f"Found {len(models)} models for {provider}.")

        return models

    except requests.exceptions.ConnectionError:
        logging.error(f"Connection Error: Could not reach endpoint {endpoint}. Is the service running?")
        if provider == "Ollama": logging.info("Ensure Ollama is running locally (`ollama serve`)")
        return []
    except requests.exceptions.Timeout:
        logging.error(f"Timeout: Request to {endpoint} timed out.")
        return []
    except requests.exceptions.HTTPError as e:
        logging.error(f"HTTP Error fetching models: {e.response.status_code} {e.response.reason}")
        try:
            logging.error(f"Response body: {e.response.text}") # Show error details from API
            if e.response.status_code == 401 and provider == "Groq":
                 logging.error("Authentication Error (401): Please check your Groq API Key.")
        except Exception as inner_e:
            # Avoid crashing if response.text is not available or causes another error
            logging.warning(f"Could not display full error response body: {inner_e}")
        return [] # Return empty list on HTTP error
    except Exception as e:
        logging.error(f"An unexpected error occurred fetching models: {e}")
        logging.error(traceback.format_exc())
        return [] # Return empty list on other exceptions


# --- Hierarchy Formatting for Prompt ---
def format_labels_for_flat_prompt(labels: List[str], label_keywords: Dict[str, str] = None) -> str:
    """Formats a list of labels for flat multi-label classification prompts."""
    if not labels:
        return "No labels defined."

    # Format as a numbered list with keywords if available
    formatted_labels = []
    for i, label in enumerate(labels, 1):
        label_text = f"{i}. {label}"

        # Add keywords if available
        if label_keywords and label in label_keywords and label_keywords[label].strip():
            keywords = label_keywords[label].strip()
            label_text += f" (Keywords: {keywords})"

        formatted_labels.append(label_text)

    return "\n".join(formatted_labels)


def format_hierarchy_for_prompt(hierarchy: Dict[str, Any], is_flat_classification: bool = False, labels: List[str] = None) -> str:
    """ Formats the nested hierarchy dictionary or flat labels into a string for LLM prompts."""
    if not hierarchy and not labels:
        return "No hierarchy or labels defined."

    # Handle flat classification
    if is_flat_classification and labels:
        # Extract keywords from hierarchy if available
        label_keywords = {}
        if hierarchy and 'label_keywords' in hierarchy:
            label_keywords = hierarchy['label_keywords']
        return format_labels_for_flat_prompt(labels, label_keywords)

    # Handle flat classification from hierarchy dict
    if 'labels' in hierarchy and isinstance(hierarchy['labels'], list):
        label_keywords = hierarchy.get('label_keywords', {})
        return format_labels_for_flat_prompt(hierarchy['labels'], label_keywords)

    if 'classification_type' in hierarchy and hierarchy['classification_type'] == 'flat':
        flat_labels = []
        label_keywords = {}

        if 'labels' in hierarchy:
            flat_labels = hierarchy['labels']
        elif 'categories' in hierarchy:
            flat_labels = list(hierarchy['categories'].keys()) if isinstance(hierarchy['categories'], dict) else hierarchy['categories']

        if 'label_keywords' in hierarchy:
            label_keywords = hierarchy['label_keywords']

        return format_labels_for_flat_prompt(flat_labels, label_keywords)

    # Continue with hierarchical formatting
    if not hierarchy:
        return "No hierarchy defined."

    # Get hierarchy schema from config if available
    schema = None
    user_level_names = None

    # Check for dynamic hierarchy configuration
    if 'config' in hierarchy and 'hierarchy_levels' in hierarchy['config']:
        user_level_names = hierarchy['config']['hierarchy_levels']
    elif 'levels' in hierarchy and isinstance(hierarchy['levels'], list):
        user_level_names = hierarchy['levels']
    elif 'dynamic_config' in hierarchy and hierarchy.get('levels'):
        user_level_names = hierarchy['levels']

    if user_level_names:
        logging.info(f"format_hierarchy_for_prompt: Using dynamic level names: {user_level_names}")
        schema = HierarchySchema(
            name="Dynamic Hierarchy",
            levels=user_level_names,
            depth=len(user_level_names)
        )

    # Fallback to default schema
    if not schema:
        schema = HierarchySchema(
            name="Default Hierarchy",
            levels=config.DEFAULT_HIERARCHY_LEVELS,
            depth=len(config.DEFAULT_HIERARCHY_LEVELS)
        )

    level_names = schema.levels

    # Fallback path: if no 'themes' key, handle level-mapped structures or engine 'levels' dict
    themes = hierarchy.get('themes', None)
    if not isinstance(themes, list) or len(themes) == 0:
        # Try to build a simple label map from provided structure
        label_map = {}
        try:
            if isinstance(hierarchy.get('levels'), dict):
                # engine.enhanced structure: { index: {name, labels} }
                for _, info in hierarchy['levels'].items():
                    name = str(info.get('name', '')).strip()
                    labels = info.get('labels', []) or []
                    if name:
                        label_map[name] = [str(x) for x in labels]
            else:
                # Direct mapping: { 'Theme': [...], 'Category': [...] }
                for k, v in hierarchy.items():
                    if k in ('config', 'label_keywords', 'classification_type'):
                        continue
                    if isinstance(v, list) and (not v or isinstance(v[0], (str, int))):
                        label_map[k] = [str(x) for x in v]
        except Exception as e:
            logging.warning(f"format_hierarchy_for_prompt: Failed to build label map fallback: {e}")

        if label_map:
            ordered_names = hierarchy.get('config', {}).get('hierarchy_levels', list(label_map.keys()))
            prompt_lines = ["Available Hierarchy Levels:", "---"]
            for name in ordered_names:
                labels = label_map.get(name, [])
                if labels:
                    prompt_lines.append(f"{name}: " + ", ".join(labels))
            prompt_lines.append("---")
            return "\n".join(prompt_lines)


    # Detect structure of the hierarchy dynamically
    hierarchy_structure = {}
    root_level_key = None

    # Find the root level (first level with nested structure)
    for key, value in hierarchy.items():
        if isinstance(value, list) and value and isinstance(value[0], dict):
            root_level_key = key
            hierarchy_structure[key] = value
            break

    if not root_level_key:
        # No nested structure found, return the fallback we built earlier
        return "\n".join(prompt_lines) if 'prompt_lines' in locals() else "No valid hierarchy structure found."

    # Build the prompt dynamically based on the actual hierarchy structure
    prompt_string = "Available Categorization Structure:\n---\n"

    try:
        # Use the configured level names or infer from structure
        configured_levels = hierarchy.get('config', {}).get('hierarchy_levels', level_names)

        def format_hierarchy_level(items, level_index=0, indent=""):
            """Recursively format hierarchy levels with proper indentation."""
            if not items or level_index >= len(configured_levels):
                return ""

            level_name = configured_levels[level_index]
            result = ""

            for item in items:
                if not isinstance(item, dict):
                    continue

                item_name = item.get('name', 'N/A')
                result += f"{indent}{level_name}: {item_name}\n"

                # Look for nested levels
                for key, value in item.items():
                    if key != 'name' and isinstance(value, list) and value:
                        if isinstance(value[0], dict):
                            # This is a nested level
                            result += format_hierarchy_level(value, level_index + 1, indent + "  ")
                        elif key == 'keywords':
                            # This is the keywords list at the leaf level
                            keywords_str = ', '.join(str(k) for k in value if k) if value else 'N/A'
                            result += f"{indent}  Keywords: {keywords_str}\n"

                result += "\n" if level_index == 0 else ""  # Add spacing between top-level items

            return result

        # Start formatting from the root level
        root_items = hierarchy_structure.get(root_level_key, [])
        prompt_string += format_hierarchy_level(root_items)
        prompt_string += "---\n"

    except Exception as e:
        logging.error(f"Error formatting dynamic hierarchy for prompt: {e}")
        return "Error: Could not format hierarchy structure."

    return prompt_string


# --- LLM Hierarchy Suggestion ---
def generate_hierarchy_suggestion(llm_client: Any, sample_texts: List[str], level_names: List[str] = None) -> Optional[Dict[str, Any]]:
    """Uses the LLM to generate a hierarchy suggestion based on sample text."""
    if not llm_client or not sample_texts:
        logging.error("LLM Suggestion: LLM client or sample texts missing.")
        return None

    # Use provided level names or default
    if not level_names:
        level_names = config.DEFAULT_HIERARCHY_LEVELS.copy()

    logging.info(f"LLM Suggestion: Analyzing {len(sample_texts)} samples with levels: {level_names}...")
    try:
        # Create dynamic hierarchy model based on level names
        DynamicHierarchyModel = create_dynamic_hierarchy_model(level_names)

        # Build level structure description for the prompt
        level_structure = " -> ".join(level_names).lower()

        # --- LangChain Prompt for Hierarchy Generation ---
        generation_prompt_template = f"""
        You are an expert data analyst creating structured hierarchies. Analyze the sample text data below to identify hierarchical patterns and create a logical classification structure.

        **Sample Data:**
        ```
        {{sample_data}}
        ```

        **Instructions:**
        1. Identify main high-level categories ({level_names[0].lower()}s).
        2. For each high-level category, identify relevant subcategories.
        3. Continue breaking down into more specific subcategories as appropriate.
        4. Create up to {len(level_names)} levels of hierarchy depth.
        5. For the most specific level, list 3-7 relevant **Keywords** found in or directly inferred from the sample data.
        6. Ensure the hierarchy is logical and covers the data's main topics. Avoid redundancy.
        7. Base the hierarchy *only* on the provided data. Do not invent unrelated topics.
        8. Output the result STRICTLY in the specified JSON format using the structure: {level_structure}.

        {{format_instructions}}
        """

        parser = PydanticOutputParser(pydantic_object=DynamicHierarchyModel)
        prompt = PromptTemplate(
            template=generation_prompt_template,
            input_variables=["sample_data"],
            partial_variables={"format_instructions": parser.get_format_instructions()}
        )
        chain = LLMChain(llm=llm_client, prompt=prompt)
        sample_data_str = "\n".join([f"- {text}" for text in sample_texts])

        # Invoke LLM
        logging.info("LLM Suggestion: Invoking LLM for hierarchy generation...")
        llm_response = chain.invoke({"sample_data": sample_data_str})
        raw_output = llm_response['text']

        # Attempt to parse the output
        logging.info("LLM Suggestion: Parsing response...")
        try:
            parsed_hierarchy = parser.parse(raw_output)
            logging.info("LLM response parsed successfully.")
            return parsed_hierarchy.model_dump() # Return as a standard dictionary
        except Exception as e_parse:
            logging.warning(f"LLM Suggestion: Initial Pydantic parse failed: {e_parse}. Attempting automated fix...")
            try:
                # Use LangChain's fixer which asks the LLM to correct the format
                fixing_parser = OutputFixingParser.from_llm(parser=parser, llm=llm_client)
                parsed_hierarchy = fixing_parser.parse(raw_output)
                logging.info("Successfully parsed hierarchy using OutputFixingParser.")
                return parsed_hierarchy.model_dump() # Return as a standard dictionary
            except Exception as e_fix:
                logging.error(f"LLM Suggestion: OutputFixingParser also failed: {e_fix}")
                logging.error("Raw AI Response that failed parsing:")
                logging.error(raw_output)
                return None # Return None if fixing fails

    except Exception as e:
        logging.error(f"LLM Suggestion: Unexpected error during generation: {e}")
        logging.error(traceback.format_exc())
        return None


# --- LLM Text Classification (Batch Processing) ---
def classify_texts_with_llm(
    df: pd.DataFrame,
    text_columns: List[str],
    hierarchy_dict: Dict[str, Any],
    llm_client: Any,
    batch_size: int = 10, # Number of texts to process in one LLM call
    max_concurrency: int = 5, # Max parallel LLM requests allowed simultaneously
    progress_callback = None # Callback function for progress updates
    ) -> Optional[pd.DataFrame]:
    """
    Classifies text in a DataFrame using the LLM, a defined hierarchy, and batch processing.

    Args:
        df: Input DataFrame.
        text_columns: List of column names containing text to classify.
        hierarchy_dict: Nested dictionary defining the classification structure.
        llm_client: Initialized LangChain LLM client.
        batch_size: How many rows to send to the LLM in each batch request.
        max_concurrency: Maximum number of concurrent requests to the LLM provider.

    Returns:
        A DataFrame with added columns for LLM classification results (Theme, Category, etc.)
        or None if a critical error occurs.
    """

    if df is None or df.empty or not text_columns or not hierarchy_dict or not llm_client:
        logging.error("LLM Classify: Missing inputs (DataFrame, text columns, hierarchy, or LLM client).")
        return None

    # --- Setup LangChain for Categorization ---
    # Detect classification type and extract levels/labels
    is_flat_classification, labels_or_levels = detect_classification_type(hierarchy_dict)

    # Get the level names from the hierarchy configuration (for backward compatibility)
    if 'config' in hierarchy_dict and 'hierarchy_levels' in hierarchy_dict['config']:
        labels_or_levels = hierarchy_dict['config']['hierarchy_levels']
        is_flat_classification = False  # Explicit hierarchy config means hierarchical
        logging.info(f"Using user-defined hierarchy levels: {labels_or_levels}")
    elif not labels_or_levels:
        # Fallback to detect levels from hierarchy structure dynamically
        if hierarchy_dict:
            detected_is_flat, detected_levels = detect_classification_type(hierarchy_dict)
            if not detected_is_flat and detected_levels:
                labels_or_levels = detected_levels
                is_flat_classification = False
        else:
            labels_or_levels = config.DEFAULT_HIERARCHY_LEVELS
            is_flat_classification = False
        logging.info(f"Using detected/default levels: {labels_or_levels}, flat: {is_flat_classification}")

    # Create a dynamic model based on the classification type
    DynamicModel = create_dynamic_categorization_model(labels_or_levels, is_flat_classification)

    # Create a parser for the dynamic model
    categorization_parser = PydanticOutputParser(pydantic_object=DynamicModel)
    # Note: Using OutputFixingParser in batch mode can be slow if many items fail parsing.
    # It's often better to handle parse errors individually after the batch returns.

    # --- Prepare Data and Run ---
    df_to_process = df.copy()

    # Log the received hierarchy dictionary
    logging.info(f"Received hierarchy_dict: {json.dumps(hierarchy_dict, default=str)}")

    hierarchy_str = format_hierarchy_for_prompt(hierarchy_dict, is_flat_classification, labels_or_levels)
    if "Error:" in hierarchy_str or "No hierarchy defined" in hierarchy_str or "No labels defined" in hierarchy_str:
        logging.error(f"LLM Classify: Invalid hierarchy/labels structure provided for prompt: {hierarchy_str}")
        return None

    # Create a prompt template that adapts to the classification type
    if is_flat_classification:
        # Flat multi-label classification prompt
        labels_text = ", ".join(labels_or_levels)
        categorization_prompt_template = f"""
        You are an AI assistant specialized in multi-label text classification. Analyze the text snippet below and assign ALL relevant labels from the provided list. This is multi-label classification, so a text can have multiple labels assigned to it. Only use the label names explicitly defined in the list. If no labels fit well, return an empty list. Provide brief reasoning for your choices.

        **Available Labels:**
        ```
        {{hierarchy_structure}}
        ```

        **Text Snippet to Categorize:**
        ```
        {{text_to_categorize}}
        ```

        {{format_instructions}}
        """
        logging.info(f"Using flat multi-label prompt template with labels: {labels_text}")
    elif len(labels_or_levels) == 1:
        # Special case for single level hierarchies
        single_level_name = labels_or_levels[0]
        categorization_prompt_template = f"""
        You are an AI assistant specialized in precise text categorization. Classify the text snippet below into the most appropriate {single_level_name} within the provided structure. Only use the {single_level_name} names explicitly defined in the structure. If no {single_level_name} fits well, you may return null. Provide brief reasoning.

        **Hierarchy Structure:**
        ```
        {{hierarchy_structure}}
        ```

        **Text Snippet to Categorize:**
        ```
        {{text_to_categorize}}
        ```

        {{format_instructions}}
        """
        logging.info(f"Using single-level prompt template with level name: {single_level_name}")
    else:
        # Multi-level hierarchy
        levels_text = ", ".join(labels_or_levels)
        categorization_prompt_template = f"""
        You are an AI assistant specialized in precise text categorization. Classify the text snippet below into the *single most appropriate* path within the provided structure. Only use the {levels_text} names explicitly defined in the structure. If no path fits well, you may return null for some or all levels. Provide brief reasoning.

        **Hierarchy Structure:**
        ```
        {{hierarchy_structure}}
        ```

        **Text Snippet to Categorize:**
        ```
        {{text_to_categorize}}
        ```

        {{format_instructions}}
        """
        logging.info(f"Using multi-level prompt template with levels: {levels_text}")

    categorization_prompt = PromptTemplate(
        template=categorization_prompt_template,
        input_variables=["hierarchy_structure", "text_to_categorize"],
        partial_variables={"format_instructions": categorization_parser.get_format_instructions()}
    )
    categorization_chain = LLMChain(llm=llm_client, prompt=categorization_prompt)

    # Define mapping from Pydantic fields to DataFrame column names
    output_cols = {
        'reasoning': 'LLM_Reasoning'  # Always include reasoning
    }

    # Create output column mapping based on classification type
    if is_flat_classification:
        # For flat classification, we have a single 'labels' field
        output_cols['labels'] = 'LLM_Labels'
        logging.info(f"Creating flat classification output mapping: {output_cols}")
    else:
        # Create output column mapping for hierarchical classification
        logging.info(f"Creating hierarchical output column mapping with levels: {labels_or_levels}")

        # Map each level to an output column
        for level_name in labels_or_levels:
            # Convert the level name to snake_case for the Pydantic field name
            field_name = level_name.lower().replace(' ', '_')
            # Use the original level name for the output column
            output_cols[field_name] = f'LLM_{level_name}'
            logging.info(f"Mapping field '{field_name}' to output column 'LLM_{level_name}'")

    # Add output columns to the DataFrame if they don't exist, handling potential conflicts
    for df_col in output_cols.values():
        if df_col not in df_to_process.columns:
            df_to_process[df_col] = pd.NA # Use pandas NA for better type handling
        else:
            logging.warning(f"Column '{df_col}' already exists. Output will overwrite it.")

    total_rows = len(df_to_process)
    logging.info(f"LLM Classify: Starting batch categorization for {total_rows} rows (Batch Size: {batch_size}, Concurrency: {max_concurrency})...")
    all_results_parsed = []
    error_count = 0
    processed_rows = 0

    # Prepare list of texts to process by concatenating multiple columns
    texts_to_classify_list = []
    for _, row in df_to_process.iterrows():
        text_parts = []
        for col in text_columns:
            if pd.notna(row[col]):
                text_parts.append(str(row[col]).strip())
        texts_to_classify_list.append(" ".join(text_parts))
    original_indices = df_to_process.index.tolist() # Keep track of original indices

    logging.info(f"LLM Classify: Categorizing in batches...")
    for i in range(0, total_rows, batch_size):
        batch_texts = texts_to_classify_list[i : i + batch_size]
        batch_indices = original_indices[i : i + batch_size]

        if not batch_texts: continue # Skip empty batches

        # Create inputs for the batch call
        batch_inputs = [
            {
                "hierarchy_structure": hierarchy_str,
                "text_to_categorize": text
            }
            for text in batch_texts if text.strip() # Only include non-empty texts
        ]
        # Keep track of indices corresponding to non-empty texts in the batch
        valid_indices_in_batch = [idx for idx, text in zip(batch_indices, batch_texts) if text.strip()]

        if not batch_inputs: # If batch only contained empty texts
             # Add empty results for all original indices in this batch
             all_results_parsed.extend([{'index': idx, **LLMCategorizationResult().model_dump()} for idx in batch_indices])
             processed_rows += len(batch_indices)
             logging.info(f"Processed {processed_rows}/{total_rows} rows (Skipped empty batch)")
             continue

        try:
            # --- Execute Batch Request ---
            batch_responses = categorization_chain.batch(
                batch_inputs,
                config={"max_concurrency": max_concurrency}
            )
            # batch_responses is a list of dictionaries, e.g., [{'text': '...'}, {'text': '...'}]

            # --- Process Batch Responses ---
            if len(batch_responses) != len(batch_inputs):
                 logging.error(f"Batch Error: Mismatch between input ({len(batch_inputs)}) and output ({len(batch_responses)}) count.")
                 # Add error results for this batch
                 all_results_parsed.extend([{'index': idx, **LLMCategorizationResult(reasoning="Error: Batch response mismatch").model_dump()} for idx in valid_indices_in_batch])
                 error_count += len(valid_indices_in_batch)
            else:
                for idx, response in zip(valid_indices_in_batch, batch_responses):
                    raw_output = response.get('text', '')
                    try:
                        parsed_result = categorization_parser.parse(raw_output)
                        all_results_parsed.append({'index': idx, **parsed_result.model_dump()})
                    except Exception as e_parse:
                        logging.warning(f"LLM Parse Error (Index {idx}): {e_parse}. Raw: '{raw_output[:100]}...'")
                        # Attempting to fix might be slow/unreliable in batch, log as error
                        all_results_parsed.append({'index': idx, **LLMCategorizationResult(reasoning=f"Error: Failed to parse LLM output - {e_parse}").model_dump()})
                        error_count += 1

            # Add empty results for any empty texts that were skipped *within* this batch run
            empty_indices_in_batch = [idx for idx, text in zip(batch_indices, batch_texts) if not text.strip()]
            all_results_parsed.extend([{'index': idx, **LLMCategorizationResult().model_dump()} for idx in empty_indices_in_batch])


        except Exception as e_batch:
            logging.error(f"LLM Batch Error (Rows {i+1}-{i+batch_size}): {e_batch}")
            logging.error(traceback.format_exc())
            # Add error results for all valid items in the failed batch
            all_results_parsed.extend([{'index': idx, **LLMCategorizationResult(reasoning=f"Error: Batch processing failed - {e_batch}").model_dump()} for idx in valid_indices_in_batch])
            # Add empty results for skipped empty texts
            empty_indices_in_batch = [idx for idx, text in zip(batch_indices, batch_texts) if not text.strip()]
            all_results_parsed.extend([{'index': idx, **LLMCategorizationResult().model_dump()} for idx in empty_indices_in_batch])
            error_count += len(valid_indices_in_batch)


        # Update progress
        processed_rows += len(batch_indices) # Increment by the original batch size
        logging.info(f"Processed {processed_rows}/{total_rows} rows")

        # Call progress callback if provided
        if progress_callback:
            try:
                progress_callback(processed_rows)
            except Exception as e_callback:
                logging.warning(f"Error in progress callback: {e_callback}")

    logging.info("Assigning results...")

    # --- Consolidate Results ---
    if len(all_results_parsed) == total_rows:
        # Create DataFrame from parsed results, ensuring index matches original
        results_df_final = pd.DataFrame(all_results_parsed).set_index('index')

        # Log the output column mapping and results DataFrame columns
        logging.info(f"Final output_cols mapping: {output_cols}")
        logging.info(f"Results DataFrame columns: {list(results_df_final.columns)}")

        # Assign results back to the original DataFrame columns using the index
        for pydantic_field, df_col in output_cols.items():
            if pydantic_field in results_df_final.columns:
                 # Use .loc for robust index-based assignment
                 logging.info(f"Assigning results from '{pydantic_field}' to output column '{df_col}'")
                 df_to_process.loc[results_df_final.index, df_col] = results_df_final[pydantic_field]
            else:
                 logging.warning(f"LLM Classify: Field '{pydantic_field}' missing in LLM results for column '{df_col}'.")
    else:
        logging.error(f"LLM Classify Error: Number of processed results ({len(all_results_parsed)}) does not match total rows ({total_rows}). Cannot assign results reliably.")
        return None # Return None if result count mismatch

    logging.info(f"LLM Batch Categorization finished! ({error_count} row errors occurred during processing)")
    return df_to_process # Return the processed DataFrame
