# Port Configuration Changes

This document outlines the port changes made to avoid conflicts with other projects running on the default ports.

## Summary of Changes

### Original Ports → New Ports

| Service | Original Port | New Port | Description |
|---------|---------------|----------|-------------|
| Backend Server | 8000 | 8001 | FastAPI backend server |
| Frontend Dev Server | 5173 | 5174 | Vite development server |
| Frontend Preview Server | 4173 | 4174 | Vite preview server |
| PostgreSQL | 5432 | 5433 | PostgreSQL database |
| Redis | 6379 | 6380 | Redis for token blacklisting |
| Ollama | 11434 | 11435 | Ollama LLM service |

## Files Modified

### Backend Configuration
- `backend/server.py` - Updated default port from 8000 to 8001
- `backend/app/config.py` - Updated:
  - CORS_ORIGINS to use new frontend ports (5174, 4174)
  - DEFAULT_OLLAMA_ENDPOINT to use port 11435
  - REDIS_URL to use port 6380
  - GOOGLE_REDIRECT_URI to use port 5174

### Frontend Configuration
- `frontend/vite.config.ts` - Added server and preview port configuration
- `frontend/src/services/apiClient.ts` - Updated API_BASE_URL to use port 8001
- `frontend/src/config.ts` - Updated DEFAULT_OLLAMA_ENDPOINT to use port 11435

### Environment Files
- `.env.example` - Updated all port references
- `backend/.env.example` - Updated all port references

### Documentation
- `README.md` - Updated all port references in examples and instructions

## Setting Up Your Own PostgreSQL Instance

To use your own PostgreSQL instance on port 5433:

1. Install PostgreSQL if not already installed
2. Create a new PostgreSQL instance on port 5433:
   ```bash
   # Create a new data directory
   initdb -D /path/to/your/postgres/data

   # Start PostgreSQL on port 5433
   postgres -D /path/to/your/postgres/data -p 5433
   ```

3. Create the database:
   ```bash
   createdb -p 5433 classyweb
   ```

4. Update your `.env` file:
   ```
   DATABASE_URL=postgresql://username:password@localhost:5433/classyweb
   ```

## Setting Up Redis on Port 6380

To run Redis on port 6380:

1. Start Redis with custom port:
   ```bash
   redis-server --port 6380
   ```

2. Update your `.env` file:
   ```
   REDIS_URL=redis://localhost:6380/0
   ```

## Setting Up Ollama on Port 11435

To run Ollama on port 11435:

1. Set the OLLAMA_HOST environment variable:
   ```bash
   export OLLAMA_HOST=0.0.0.0:11435
   ```

2. Start Ollama:
   ```bash
   ollama serve
   ```

3. Update your `.env` file:
   ```
   OLLAMA_ENDPOINT=http://localhost:11435
   ```

## Running the Application

### Backend
```bash
cd backend
python server.py --port 8001 --reload
```

### Frontend
```bash
cd frontend
npm run dev
# Will now run on http://localhost:5174
```

## Environment Variables

Make sure to update your `.env` file with the new port configurations. You can copy from the updated `.env.example` files.

## Google OAuth Configuration

If you're using Google OAuth, make sure to update your Google Cloud Console OAuth configuration:

1. Go to Google Cloud Console
2. Navigate to APIs & Services > Credentials
3. Edit your OAuth 2.0 Client ID
4. Update the Authorized redirect URIs to include:
   - `http://localhost:5174/auth/google/callback`

## Notes

- All changes maintain backward compatibility through environment variables
- You can still override any port using environment variables
- The changes ensure no conflicts with standard development ports
- All CORS configurations have been updated to match the new frontend ports
