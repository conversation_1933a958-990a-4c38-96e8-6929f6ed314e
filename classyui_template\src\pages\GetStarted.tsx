import { Navigation } from "@/components/Navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { GraduationCap, Brain, ArrowRight, CheckCircle2, <PERSON>rkles } from "lucide-react";

const GetStarted = () => {
  return (
    <div className="min-h-screen bg-background">
      <Navigation />
      
      <main className="pt-20">
        {/* Hero Section */}
        <section className="py-20 bg-gradient-card">
          <div className="max-w-4xl mx-auto px-6 text-center">
            <Badge variant="outline" className="mb-6">
              <Sparkles className="w-4 h-4 mr-2" />
              Get Started
            </Badge>
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              Choose Your 
              <span className="bg-gradient-primary bg-clip-text text-transparent"> ML Journey</span>
            </h1>
            <p className="text-xl text-muted-foreground mb-12 max-w-2xl mx-auto">
              Select the workflow that matches your experience level. 
              You can always switch between modes as you progress.
            </p>
          </div>
        </section>

        {/* Path Selection */}
        <section className="py-16">
          <div className="max-w-6xl mx-auto px-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              
              {/* Beginner Path */}
              <Card className="group hover:shadow-card transition-all duration-300 border-2 hover:border-ml-success/30 relative overflow-hidden">
                <div className="absolute top-0 right-0 w-20 h-20 bg-ml-success/10 rounded-full -mr-10 -mt-10" />
                
                <CardHeader className="pb-6 relative z-10">
                  <div className="flex items-center gap-3 mb-4">
                    <div className="w-12 h-12 rounded-lg bg-ml-success/10 flex items-center justify-center">
                      <GraduationCap className="w-6 h-6 text-ml-success" />
                    </div>
                    <div>
                      <CardTitle className="text-2xl text-ml-success">Beginner Journey</CardTitle>
                      <CardDescription>Guided workflow with smart defaults</CardDescription>
                    </div>
                  </div>
                  
                  <div className="space-y-3 mb-6">
                    <div className="flex items-center gap-2 text-sm">
                      <CheckCircle2 className="w-4 h-4 text-ml-success" />
                      <span>AI-powered data analysis</span>
                    </div>
                    <div className="flex items-center gap-2 text-sm">
                      <CheckCircle2 className="w-4 h-4 text-ml-success" />
                      <span>Automatic classification type detection</span>
                    </div>
                    <div className="flex items-center gap-2 text-sm">
                      <CheckCircle2 className="w-4 h-4 text-ml-success" />
                      <span>Step-by-step guidance</span>
                    </div>
                    <div className="flex items-center gap-2 text-sm">
                      <CheckCircle2 className="w-4 h-4 text-ml-success" />
                      <span>One-click deployment</span>
                    </div>
                  </div>
                </CardHeader>
                
                <CardContent>
                  <div className="space-y-4">
                    <div className="p-4 bg-ml-success/5 rounded-lg border border-ml-success/20">
                      <p className="text-sm text-muted-foreground mb-2">Perfect for:</p>
                      <p className="text-sm font-medium">New to ML, want quick results, prefer guided experiences</p>
                    </div>
                    
                    <Button 
                      className="w-full bg-ml-success hover:bg-ml-success/90 text-white group"
                      size="lg"
                      asChild
                    >
                      <a href="/beginner">
                        Start Guided Journey
                        <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
                      </a>
                    </Button>
                  </div>
                </CardContent>
              </Card>

              {/* Expert Path */}
              <Card className="group hover:shadow-card transition-all duration-300 border-2 hover:border-ml-primary/30 relative overflow-hidden">
                <div className="absolute top-0 right-0 w-20 h-20 bg-ml-primary/10 rounded-full -mr-10 -mt-10" />
                
                <CardHeader className="pb-6 relative z-10">
                  <div className="flex items-center gap-3 mb-4">
                    <div className="w-12 h-12 rounded-lg bg-ml-primary/10 flex items-center justify-center">
                      <Brain className="w-6 h-6 text-ml-primary" />
                    </div>
                    <div>
                      <CardTitle className="text-2xl text-ml-primary">Expert Journey</CardTitle>
                      <CardDescription>Advanced controls and customization</CardDescription>
                    </div>
                  </div>
                  
                  <div className="space-y-3 mb-6">
                    <div className="flex items-center gap-2 text-sm">
                      <CheckCircle2 className="w-4 h-4 text-ml-primary" />
                      <span>Custom model architectures</span>
                    </div>
                    <div className="flex items-center gap-2 text-sm">
                      <CheckCircle2 className="w-4 h-4 text-ml-primary" />
                      <span>Hyperparameter optimization</span>
                    </div>
                    <div className="flex items-center gap-2 text-sm">
                      <CheckCircle2 className="w-4 h-4 text-ml-primary" />
                      <span>Real-time training monitoring</span>
                    </div>
                    <div className="flex items-center gap-2 text-sm">
                      <CheckCircle2 className="w-4 h-4 text-ml-primary" />
                      <span>Production-grade deployment</span>
                    </div>
                  </div>
                </CardHeader>
                
                <CardContent>
                  <div className="space-y-4">
                    <div className="p-4 bg-ml-primary/5 rounded-lg border border-ml-primary/20">
                      <p className="text-sm text-muted-foreground mb-2">Perfect for:</p>
                      <p className="text-sm font-medium">Data scientists, ML engineers, need full control</p>
                    </div>
                    
                    <Button 
                      className="w-full group"
                      size="lg"
                      asChild
                    >
                      <a href="/expert">
                        Access Expert Mode
                        <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
                      </a>
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>

        {/* Quick Assessment CTA */}
        <section className="py-16 bg-muted/30">
          <div className="max-w-3xl mx-auto px-6 text-center">
            <h2 className="text-2xl font-bold mb-4">Not Sure Which Path to Choose?</h2>
            <p className="text-muted-foreground mb-8">
              Take our quick 2-minute assessment to find the perfect workflow for your needs and experience level.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button variant="outline" size="lg">
                Take Quick Assessment
              </Button>
              <Button size="lg" variant="secondary">
                Explore Both Options
              </Button>
            </div>
          </div>
        </section>
      </main>
    </div>
  );
};

export default GetStarted;