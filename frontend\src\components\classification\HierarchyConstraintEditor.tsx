/**
 * HierarchyConstraintEditor.tsx
 * 
 * Component for configuring parent-child relationship constraints
 */

import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Switch } from "@/components/ui/switch";
import { Textarea } from "@/components/ui/textarea";
import { 
  Plus, 
  Minus, 
  Link, 
  AlertCircle,
  CheckCircle2,
  Info,
  Settings,
  Shield
} from "lucide-react";
import { toast } from "@/hooks/use-toast";

interface ConstraintRule {
  id: string;
  parentLevel: number;
  parentValue: string;
  childLevel: number;
  allowedChildren: string[];
  isStrict: boolean;
  description?: string;
}

interface ValidationRule {
  id: string;
  name: string;
  enabled: boolean;
  severity: 'error' | 'warning';
  description: string;
}

interface HierarchyConstraintInfo {
  parentValue: string;
  parentLevel: number;
  childLevel: number;
  allowedChildren: string[];
}

interface HierarchyConstraintEditorProps {
  hierarchyLevels: Array<{ name: string; column: string; order: number }>;
  detectedConstraints: Record<string, string[]>;
  constraintInfo?: HierarchyConstraintInfo[];
  onConstraintsChange: (constraints: ConstraintRule[]) => void;
  onValidationRulesChange: (rules: ValidationRule[]) => void;
  initialConstraints?: ConstraintRule[];
  initialValidationRules?: ValidationRule[];
}

export const HierarchyConstraintEditor: React.FC<HierarchyConstraintEditorProps> = ({
  hierarchyLevels,
  detectedConstraints,
  constraintInfo,
  onConstraintsChange,
  onValidationRulesChange,
  initialConstraints = [],
  initialValidationRules = []
}) => {
  const [constraints, setConstraints] = useState<ConstraintRule[]>(initialConstraints);
  const [validationRules, setValidationRules] = useState<ValidationRule[]>(initialValidationRules);
  const [autoDetectEnabled, setAutoDetectEnabled] = useState(true);

  // Default validation rules
  const defaultValidationRules: ValidationRule[] = [
    {
      id: 'parent_child_consistency',
      name: 'Parent-Child Consistency',
      enabled: true,
      severity: 'error',
      description: 'Ensure all child values have valid parent relationships'
    },
    {
      id: 'level_completeness',
      name: 'Level Completeness',
      enabled: true,
      severity: 'warning',
      description: 'Warn when hierarchy levels are incomplete'
    },
    {
      id: 'circular_dependencies',
      name: 'Circular Dependencies',
      enabled: true,
      severity: 'error',
      description: 'Prevent circular parent-child relationships'
    },
    {
      id: 'orphaned_nodes',
      name: 'Orphaned Nodes',
      enabled: true,
      severity: 'warning',
      description: 'Detect nodes without proper parent relationships'
    },
    {
      id: 'duplicate_paths',
      name: 'Duplicate Paths',
      enabled: true,
      severity: 'error',
      description: 'Prevent duplicate hierarchical paths'
    }
  ];

  useEffect(() => {
    if (validationRules.length === 0) {
      setValidationRules(defaultValidationRules);
      onValidationRulesChange(defaultValidationRules);
    }
  }, [validationRules.length, onValidationRulesChange]);

  useEffect(() => {
    if (autoDetectEnabled && Object.keys(detectedConstraints).length > 0) {
      const autoConstraints = generateConstraintsFromDetected();
      // Only update if constraints actually changed
      if (JSON.stringify(autoConstraints) !== JSON.stringify(constraints)) {
        setConstraints(autoConstraints);
        onConstraintsChange(autoConstraints);
      }
    }
  }, [detectedConstraints, constraintInfo, autoDetectEnabled]);

  const generateConstraintsFromDetected = (): ConstraintRule[] => {
    const rules: ConstraintRule[] = [];

    // Use enhanced constraint info if available for accurate level mapping
    if (constraintInfo && constraintInfo.length > 0) {
      constraintInfo.forEach((info, index) => {
        rules.push({
          id: `auto_${info.parentValue}_${Date.now()}_${index}`,
          parentLevel: info.parentLevel,
          parentValue: info.parentValue,
          childLevel: info.childLevel,
          allowedChildren: info.allowedChildren,
          isStrict: false,
          description: `Auto-detected: ${info.parentValue} (${hierarchyLevels[info.parentLevel]?.name || `Level ${info.parentLevel + 1}`} → ${hierarchyLevels[info.childLevel]?.name || `Level ${info.childLevel + 1}`})`
        });
      });
    } else {
      // Fallback to basic constraint detection
      Object.entries(detectedConstraints).forEach(([parent, children]) => {
        // Use heuristic level detection as fallback
        let parentLevel = 0;
        let childLevel = 1;

        // For multiple levels, try to detect based on constraint depth
        const constraintDepth = Object.keys(detectedConstraints).length;
        if (constraintDepth > 0 && hierarchyLevels.length > 2) {
          const levelIndex = Object.keys(detectedConstraints).indexOf(parent);
          parentLevel = Math.min(levelIndex, hierarchyLevels.length - 2);
          childLevel = parentLevel + 1;
        }

        if (parentLevel < hierarchyLevels.length - 1) {
          rules.push({
            id: `auto_${parent}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            parentLevel,
            parentValue: parent,
            childLevel,
            allowedChildren: children,
            isStrict: false,
            description: `Auto-detected: ${parent} (${hierarchyLevels[parentLevel]?.name || `Level ${parentLevel + 1}`} → ${hierarchyLevels[childLevel]?.name || `Level ${childLevel + 1}`})`
          });
        }
      });
    }

    return rules;
  };

  const addConstraint = () => {
    const newConstraint: ConstraintRule = {
      id: `constraint_${Date.now()}`,
      parentLevel: 0,
      parentValue: '',
      childLevel: 1,
      allowedChildren: [],
      isStrict: false,
      description: ''
    };

    const updatedConstraints = [...constraints, newConstraint];
    setConstraints(updatedConstraints);
    onConstraintsChange(updatedConstraints);
  };

  const removeConstraint = (constraintId: string) => {
    const updatedConstraints = constraints.filter(c => c.id !== constraintId);
    setConstraints(updatedConstraints);
    onConstraintsChange(updatedConstraints);
  };

  const updateConstraint = (constraintId: string, updates: Partial<ConstraintRule>) => {
    const updatedConstraints = constraints.map(constraint =>
      constraint.id === constraintId ? { ...constraint, ...updates } : constraint
    );
    setConstraints(updatedConstraints);
    onConstraintsChange(updatedConstraints);
  };

  const updateValidationRule = (ruleId: string, updates: Partial<ValidationRule>) => {
    const updatedRules = validationRules.map(rule =>
      rule.id === ruleId ? { ...rule, ...updates } : rule
    );
    setValidationRules(updatedRules);
    onValidationRulesChange(updatedRules);
  };

  const addChildToConstraint = (constraintId: string, child: string) => {
    if (!child.trim()) return;

    const constraint = constraints.find(c => c.id === constraintId);
    if (!constraint) return;

    if (constraint.allowedChildren.includes(child)) {
      toast({
        title: "Child already exists",
        description: "This child value is already in the allowed list",
        variant: "destructive"
      });
      return;
    }

    updateConstraint(constraintId, {
      allowedChildren: [...constraint.allowedChildren, child]
    });
  };

  const removeChildFromConstraint = (constraintId: string, child: string) => {
    const constraint = constraints.find(c => c.id === constraintId);
    if (!constraint) return;

    updateConstraint(constraintId, {
      allowedChildren: constraint.allowedChildren.filter(c => c !== child)
    });
  };

  const validateConstraints = (): string[] => {
    const errors: string[] = [];

    constraints.forEach((constraint, index) => {
      if (!constraint.parentValue.trim()) {
        errors.push(`Constraint ${index + 1}: Parent value is required`);
      }

      if (constraint.parentLevel >= constraint.childLevel) {
        errors.push(`Constraint ${index + 1}: Child level must be after parent level`);
      }

      if (constraint.allowedChildren.length === 0) {
        errors.push(`Constraint ${index + 1}: At least one allowed child is required`);
      }
    });

    return errors;
  };

  const errors = validateConstraints();
  const isValid = errors.length === 0;

  return (
    <div className="space-y-6">
      {/* Constraint Rules */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Link className="w-5 h-5" />
            Hierarchy Constraints
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Auto-detect toggle */}
          <div className="flex items-center justify-between p-4 bg-muted/30 rounded">
            <div className="space-y-1">
              <Label htmlFor="auto-detect">Auto-detect Constraints</Label>
              <p className="text-sm text-muted-foreground">
                Automatically generate constraints from your data structure
              </p>
              {Object.keys(detectedConstraints).length > 0 && (
                <p className="text-xs text-green-600">
                  ✓ {Object.keys(detectedConstraints).length} constraint{Object.keys(detectedConstraints).length !== 1 ? 's' : ''} detected from hierarchy
                </p>
              )}
            </div>
            <Switch
              id="auto-detect"
              checked={autoDetectEnabled}
              onCheckedChange={setAutoDetectEnabled}
            />
          </div>

          {/* Info Alert */}
          <Alert>
            <Info className="h-4 w-4" />
            <AlertDescription>
              Constraints define which child values are allowed for each parent value. 
              This helps maintain data consistency and catch classification errors.
            </AlertDescription>
          </Alert>

          {/* Validation Errors */}
          {errors.length > 0 && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                <div className="space-y-1">
                  {errors.map((error, index) => (
                    <div key={index}>{error}</div>
                  ))}
                </div>
              </AlertDescription>
            </Alert>
          )}

          {/* Constraints List */}
          <div className="space-y-4">
            {constraints.map((constraint, index) => (
              <Card key={constraint.id} className="p-4">
                <div className="space-y-4">
                  {/* Constraint Header */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Badge variant="outline">Constraint {index + 1}</Badge>
                      {constraint.id.startsWith('auto_') && (
                        <Badge variant="secondary" className="text-xs">
                          Auto-detected
                        </Badge>
                      )}
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeConstraint(constraint.id)}
                    >
                      <Minus className="w-4 h-4" />
                    </Button>
                  </div>

                  {/* Parent Configuration */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="space-y-2">
                      <Label>Parent Level</Label>
                      <Select
                        value={constraint.parentLevel.toString()}
                        onValueChange={(value) => 
                          updateConstraint(constraint.id, { parentLevel: parseInt(value) })
                        }
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {hierarchyLevels.slice(0, -1).map((level, idx) => (
                            <SelectItem key={idx} value={idx.toString()}>
                              {level.name} (Level {idx + 1})
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label>Parent Value</Label>
                      <Input
                        value={constraint.parentValue}
                        onChange={(e) => 
                          updateConstraint(constraint.id, { parentValue: e.target.value })
                        }
                        placeholder="Enter parent value"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label>Child Level</Label>
                      <Select
                        value={constraint.childLevel.toString()}
                        onValueChange={(value) => 
                          updateConstraint(constraint.id, { childLevel: parseInt(value) })
                        }
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {hierarchyLevels.slice(1).map((level, idx) => (
                            <SelectItem key={idx} value={(idx + 1).toString()}>
                              {level.name} (Level {idx + 2})
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  {/* Allowed Children */}
                  <div className="space-y-2">
                    <Label>Allowed Children</Label>
                    <div className="flex flex-wrap gap-2 mb-2">
                      {constraint.allowedChildren.map(child => (
                        <Badge key={child} variant="secondary" className="flex items-center gap-1">
                          {child}
                          <button
                            onClick={() => removeChildFromConstraint(constraint.id, child)}
                            className="ml-1 hover:text-destructive"
                          >
                            <Minus className="w-3 h-3" />
                          </button>
                        </Badge>
                      ))}
                    </div>
                    <div className="flex gap-2">
                      <Input
                        placeholder="Add allowed child value"
                        onKeyPress={(e) => {
                          if (e.key === 'Enter') {
                            addChildToConstraint(constraint.id, e.currentTarget.value);
                            e.currentTarget.value = '';
                          }
                        }}
                      />
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={(e) => {
                          const input = e.currentTarget.previousElementSibling as HTMLInputElement;
                          addChildToConstraint(constraint.id, input.value);
                          input.value = '';
                        }}
                      >
                        <Plus className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>

                  {/* Constraint Options */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Switch
                        id={`strict-${constraint.id}`}
                        checked={constraint.isStrict}
                        onCheckedChange={(checked) => 
                          updateConstraint(constraint.id, { isStrict: checked })
                        }
                      />
                      <Label htmlFor={`strict-${constraint.id}`}>Strict Enforcement</Label>
                    </div>
                  </div>

                  {/* Description */}
                  <div className="space-y-2">
                    <Label>Description (Optional)</Label>
                    <Textarea
                      value={constraint.description || ''}
                      onChange={(e) => 
                        updateConstraint(constraint.id, { description: e.target.value })
                      }
                      placeholder="Describe this constraint rule"
                      rows={2}
                    />
                  </div>
                </div>
              </Card>
            ))}
          </div>

          {/* Add Constraint Button */}
          <Button variant="outline" onClick={addConstraint} className="w-full">
            <Plus className="w-4 h-4 mr-2" />
            Add Constraint Rule
          </Button>
        </CardContent>
      </Card>

      {/* Validation Rules */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="w-5 h-5" />
            Validation Rules
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <Alert>
            <Settings className="h-4 w-4" />
            <AlertDescription>
              Configure validation rules that will be applied during classification 
              to ensure hierarchy consistency.
            </AlertDescription>
          </Alert>

          {validationRules.map(rule => (
            <div key={rule.id} className="flex items-center justify-between p-3 border rounded">
              <div className="space-y-1">
                <div className="flex items-center gap-2">
                  <span className="font-medium">{rule.name}</span>
                  <Badge variant={rule.severity === 'error' ? 'destructive' : 'secondary'}>
                    {rule.severity}
                  </Badge>
                </div>
                <p className="text-sm text-muted-foreground">{rule.description}</p>
              </div>
              <Switch
                checked={rule.enabled}
                onCheckedChange={(checked) => 
                  updateValidationRule(rule.id, { enabled: checked })
                }
              />
            </div>
          ))}
        </CardContent>
      </Card>

      {/* Status */}
      <div className="flex items-center gap-2 p-3 rounded bg-muted/30">
        {isValid ? (
          <>
            <CheckCircle2 className="w-4 h-4 text-green-600" />
            <span className="text-sm text-green-600">
              Constraint configuration is valid ({constraints.length} rules defined)
            </span>
          </>
        ) : (
          <>
            <AlertCircle className="w-4 h-4 text-orange-600" />
            <span className="text-sm text-orange-600">
              Please fix the errors above to continue
            </span>
          </>
        )}
      </div>
    </div>
  );
};
