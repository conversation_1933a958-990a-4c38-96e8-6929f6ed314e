/**
 * Real-time Progress Bar Component for ClassyWeb ML Platform Phase 3
 * 
 * A lightweight component for displaying real-time training progress
 * with WebSocket integration and basic metrics display.
 */

import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Activity, 
  Clock, 
  Square, 
  AlertTriangle,
  CheckCircle,
  XCircle,
  Loader2
} from 'lucide-react';
import { useTrainingMonitor, TrainingStage } from '@/hooks/useTrainingMonitor';
import { stopTraining } from '@/services/classificationEngineService';

interface RealTimeProgressBarProps {
  sessionId: string;
  onComplete?: () => void;
  onError?: (error: Error) => void;
  onStop?: () => void;
  showStopButton?: boolean;
  showMetrics?: boolean;
  compact?: boolean;
  className?: string;
}

export const RealTimeProgressBar: React.FC<RealTimeProgressBarProps> = ({
  sessionId,
  onComplete,
  onError,
  onStop,
  showStopButton = true,
  showMetrics = true,
  compact = false,
  className = ''
}) => {
  const [isStoppingTraining, setIsStoppingTraining] = React.useState(false);

  const {
    isConnected,
    isConnecting,
    error,
    progress,
    metrics,
    isTraining,
    isCompleted,
    isFailed,
    progressPercentage,
    estimatedTimeRemaining
  } = useTrainingMonitor(sessionId, {
    autoConnect: true,
    onError: onError,
    onProgress: (newProgress) => {
      if (newProgress.stage === TrainingStage.COMPLETED && onComplete) {
        onComplete();
      }
    }
  });

  // Format time duration
  const formatDuration = (seconds: number): string => {
    if (!seconds || seconds < 0) return '--:--';
    
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);
    
    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  };

  // Get stage color and icon
  const getStageInfo = (stage: TrainingStage) => {
    switch (stage) {
      case TrainingStage.INITIALIZATION:
      case TrainingStage.DATA_LOADING:
      case TrainingStage.PREPROCESSING:
        return { color: 'bg-blue-500', icon: <Loader2 className="h-4 w-4 animate-spin" /> };
      case TrainingStage.TRAINING:
        return { color: 'bg-green-500', icon: <Activity className="h-4 w-4" /> };
      case TrainingStage.VALIDATION:
      case TrainingStage.EVALUATION:
        return { color: 'bg-yellow-500', icon: <Activity className="h-4 w-4" /> };
      case TrainingStage.OPTIMIZATION:
        return { color: 'bg-purple-500', icon: <Activity className="h-4 w-4" /> };
      case TrainingStage.COMPLETED:
        return { color: 'bg-green-600', icon: <CheckCircle className="h-4 w-4" /> };
      case TrainingStage.FAILED:
        return { color: 'bg-red-500', icon: <XCircle className="h-4 w-4" /> };
      case TrainingStage.STOPPED:
        return { color: 'bg-gray-500', icon: <Square className="h-4 w-4" /> };
      default:
        return { color: 'bg-gray-400', icon: <Activity className="h-4 w-4" /> };
    }
  };

  // Handle stop training
  const handleStopTraining = async () => {
    try {
      setIsStoppingTraining(true);
      await stopTraining(sessionId);
      onStop?.();
    } catch (error) {
      console.error('Failed to stop training:', error);
    } finally {
      setIsStoppingTraining(false);
    }
  };

  // Connection status indicator
  const connectionStatus = isConnected ? 'connected' : isConnecting ? 'connecting' : 'disconnected';
  const connectionColor = isConnected ? 'bg-green-500' : isConnecting ? 'bg-yellow-500' : 'bg-red-500';

  if (compact) {
    return (
      <div className={`space-y-2 ${className}`}>
        {/* Error Alert */}
        {error && (
          <Alert variant="destructive" className="py-2">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription className="text-sm">{error.message}</AlertDescription>
          </Alert>
        )}

        {/* Compact Progress */}
        <div className="flex items-center gap-3">
          <div className="flex items-center gap-2">
            <div className={`w-2 h-2 rounded-full ${connectionColor}`} />
            {progress && (
              <>
                {getStageInfo(progress.stage).icon}
                <Badge variant="outline" className="text-xs">
                  {progress.stage.replace('_', ' ')}
                </Badge>
              </>
            )}
          </div>
          
          <div className="flex-1">
            <Progress value={progressPercentage} className="h-2" />
          </div>
          
          <div className="text-sm font-medium min-w-[50px] text-right">
            {progressPercentage.toFixed(1)}%
          </div>

          {showStopButton && isTraining && (
            <Button
              variant="outline"
              size="sm"
              onClick={handleStopTraining}
              disabled={isStoppingTraining}
              className="h-8 px-2"
            >
              <Square className="h-3 w-3" />
            </Button>
          )}
        </div>

        {/* Compact Metrics */}
        {showMetrics && progress && (
          <div className="flex items-center gap-4 text-xs text-gray-600">
            <span>Epoch {progress.current_epoch}/{progress.total_epochs}</span>
            <span>Step {progress.current_step}/{progress.total_steps}</span>
            {estimatedTimeRemaining && (
              <span>ETA {formatDuration(estimatedTimeRemaining)}</span>
            )}
          </div>
        )}
      </div>
    );
  }

  return (
    <Card className={className}>
      <CardContent className="p-4 space-y-4">
        {/* Error Alert */}
        {error && (
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>{error.message}</AlertDescription>
          </Alert>
        )}

        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className={`w-3 h-3 rounded-full ${connectionColor}`} />
            <span className="text-sm font-medium">Training Progress</span>
            {progress && (
              <Badge className={getStageInfo(progress.stage).color}>
                <span className="flex items-center gap-1">
                  {getStageInfo(progress.stage).icon}
                  {progress.stage.replace('_', ' ').toUpperCase()}
                </span>
              </Badge>
            )}
          </div>

          {showStopButton && isTraining && (
            <Button
              variant="destructive"
              size="sm"
              onClick={handleStopTraining}
              disabled={isStoppingTraining}
            >
              <Square className="h-4 w-4 mr-2" />
              {isStoppingTraining ? 'Stopping...' : 'Stop'}
            </Button>
          )}
        </div>

        {/* Progress Bar */}
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>Overall Progress</span>
            <span className="font-medium">{progressPercentage.toFixed(1)}%</span>
          </div>
          <Progress value={progressPercentage} className="h-3" />
        </div>

        {/* Metrics */}
        {showMetrics && progress && (
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div className="flex items-center gap-2">
              <Activity className="h-4 w-4 text-blue-500" />
              <div>
                <div className="font-medium">Epoch</div>
                <div className="text-gray-600">
                  {progress.current_epoch} / {progress.total_epochs}
                </div>
              </div>
            </div>
            
            <div className="flex items-center gap-2">
              <Activity className="h-4 w-4 text-green-500" />
              <div>
                <div className="font-medium">Step</div>
                <div className="text-gray-600">
                  {progress.current_step} / {progress.total_steps}
                </div>
              </div>
            </div>
            
            <div className="flex items-center gap-2">
              <Clock className="h-4 w-4 text-purple-500" />
              <div>
                <div className="font-medium">Elapsed</div>
                <div className="text-gray-600">
                  {formatDuration(progress.elapsed_time)}
                </div>
              </div>
            </div>
            
            <div className="flex items-center gap-2">
              <Clock className="h-4 w-4 text-orange-500" />
              <div>
                <div className="font-medium">ETA</div>
                <div className="text-gray-600">
                  {estimatedTimeRemaining ? formatDuration(estimatedTimeRemaining) : '--:--'}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Current Metrics */}
        {showMetrics && metrics && (
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 pt-2 border-t">
            {Object.entries(metrics)
              .filter(([key]) => ['loss', 'accuracy', 'validation_loss', 'validation_accuracy'].includes(key))
              .map(([key, value]) => (
                <div key={key} className="text-center">
                  <div className="text-xs text-gray-600 capitalize">
                    {key.replace('_', ' ').replace('validation', 'val')}
                  </div>
                  <div className="text-sm font-bold">
                    {typeof value === 'number' ? value.toFixed(4) : value}
                  </div>
                </div>
              ))}
          </div>
        )}

        {/* Connection Status */}
        <div className="flex items-center justify-between text-xs text-gray-500 pt-2 border-t">
          <span>
            Status: {connectionStatus === 'connected' ? 'Connected' : 
                    connectionStatus === 'connecting' ? 'Connecting...' : 'Disconnected'}
          </span>
          <span>Session: {sessionId.slice(-8)}</span>
        </div>
      </CardContent>
    </Card>
  );
};
