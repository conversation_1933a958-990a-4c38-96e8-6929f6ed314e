import requests
import json
import os
from pprint import pprint

# Test file upload
def test_file_upload():
    # Create a test CSV file
    file_path = "test_data.csv"
    with open(file_path, "w") as f:
        f.write("text\nThis is a test sentence.\nAnother test sentence for classification.")

    print(f"Created test file: {file_path}")

    # Upload the file
    try:
        with open(file_path, "rb") as f:
            files = {"file": (file_path, f, "text/csv")}
            response = requests.post("http://localhost:8000/files/upload", files=files)

            print(f"\nFile upload response status code: {response.status_code}")

            if response.status_code == 200:
                print("File upload successful!")
                print("Response:")
                result = response.json()
                pprint(result)
                return result
            else:
                print("File upload failed!")
                print("Response:")
                try:
                    pprint(response.json())
                except:
                    print(response.text)
                return None
    except Exception as e:
        print(f"Error uploading file: {e}")
        return None

# Test LLM classification endpoint
def test_llm_classification(file_id, filename):
    # Create a test request with a valid hierarchy structure
    request_data = {
        "file_id": file_id,
        "original_filename": filename,
        "text_column": "text",
        "hierarchy": {
            "themes": [
                {
                    "name": "Test Theme",
                    "categories": [
                        {
                            "name": "Test Category",
                            "segments": [
                                {
                                    "name": "Test Segment",
                                    "subsegments": [
                                        {
                                            "name": "Test Subsegment",
                                            "keywords": ["test", "example", "sample"]
                                        }
                                    ]
                                }
                            ]
                        }
                    ]
                }
            ]
        },
        "llm_config": {
            "provider": "Ollama",
            "endpoint": "http://localhost:11434",
            "model_name": "llama3"
        }
    }

    print("Sending request to /llm/classify:")
    pprint(request_data)

    # Wrap the request data in a 'request' field as expected by the backend
    wrapped_request = {"request": request_data}

    # Send the request
    try:
        response = requests.post("http://localhost:8000/llm/classify", json=wrapped_request)

        print(f"\nResponse status code: {response.status_code}")

        if response.status_code == 202:
            print("LLM classification request successful!")
            print("Response:")
            pprint(response.json())
            return response.json()
        else:
            print("LLM classification request failed!")
            print("Response:")
            try:
                pprint(response.json())
            except:
                print(response.text)
            return None
    except Exception as e:
        print(f"Error sending LLM classification request: {e}")
        return None

if __name__ == "__main__":
    # First upload a test file
    upload_result = test_file_upload()

    if upload_result:
        # Then test LLM classification with the uploaded file
        test_llm_classification(upload_result["file_id"], upload_result["filename"])
    else:
        print("\nSkipping LLM classification test because file upload failed.")
