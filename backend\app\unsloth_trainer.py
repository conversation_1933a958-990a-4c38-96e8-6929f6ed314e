"""Unsloth-based efficient training pipeline for ClassyWeb."""

import logging
import os
import json
import traceback
from typing import List, Dict, Any, Optional, Tuple
import pandas as pd
import numpy as np
from datetime import datetime

try:
    import torch
    # Check if CUDA is available before importing Unsloth
    if torch.cuda.is_available():
        try:
            # Try importing triton first (required for Unsloth on some systems)
            import triton
        except ImportError:
            logging.warning("Triton not available. Unsloth may not work optimally on Windows. Consider using Linux for full Unsloth support.")

        from unsloth import FastLanguageModel
        from unsloth.chat_templates import get_chat_template
        from datasets import Dataset
        from transformers import TrainingArguments
        from trl import SFTTrainer
        UNSLOTH_AVAILABLE = True
        logging.info(f"Unsloth available with CUDA {torch.version.cuda} on {torch.cuda.get_device_name(0)}")
    else:
        logging.warning("CUDA not available, Unsloth requires CUDA support. Install CUDA-enabled PyTorch to use Unsloth features.")
        # Provide fallback imports for type hints
        Dataset = Any
        TrainingArguments = Any
        SFTTrainer = Any
        FastLanguageModel = Any
        UNSLOTH_AVAILABLE = False
except Exception as e:
    logging.warning(f"Unsloth dependencies not available: {e}. For Windows, Unsloth may require Linux environment or WSL.")
    # Provide fallback imports for type hints
    Dataset = Any
    TrainingArguments = Any
    SFTTrainer = Any
    FastLanguageModel = Any
    UNSLOTH_AVAILABLE = False

from .config import config

logger = logging.getLogger(__name__)

class UnslothTrainingPipeline:
    """Efficient training pipeline using Unsloth for 4-bit QLoRA fine-tuning."""
    
    def __init__(self):
        self.model_cache = {}
        self.supported_models = [
            "unsloth/llama-2-7b-bnb-4bit",
            "unsloth/llama-2-13b-bnb-4bit", 
            "unsloth/mistral-7b-bnb-4bit",
            "unsloth/codellama-34b-bnb-4bit",
            "unsloth/zephyr-sft-bnb-4bit",
            "unsloth/tinyllama-bnb-4bit"
        ]
    
    def is_available(self) -> bool:
        """Check if Unsloth is available for use."""
        return UNSLOTH_AVAILABLE
    
    def get_supported_models(self) -> List[str]:
        """Get list of supported Unsloth models."""
        return self.supported_models
    
    def prepare_classification_dataset(
        self,
        texts: List[str],
        labels: List[List[str]],
        hierarchy_levels: Optional[List[str]] = None
    ) -> Dataset:
        """Prepare dataset for classification fine-tuning."""
        if not texts or not labels:
            raise ValueError("Texts and labels cannot be empty")
        
        # Create instruction-response pairs for classification
        formatted_data = []
        
        for text, label_list in zip(texts, labels):
            if hierarchy_levels:
                # Hierarchical classification format
                instruction = f"Classify the following text into the hierarchy levels {', '.join(hierarchy_levels)}:"
                response = " | ".join(label_list)
            else:
                # Non-hierarchical multi-label format
                instruction = "Classify the following text with appropriate labels:"
                response = ", ".join(label_list)
            
            formatted_data.append({
                "instruction": instruction,
                "input": text,
                "output": response
            })
        
        return Dataset.from_list(formatted_data)
    
    def load_model_and_tokenizer(
        self,
        model_name: str,
        max_seq_length: int = 2048,
        load_in_4bit: bool = True
    ) -> Tuple[Any, Any]:
        """Load model and tokenizer with Unsloth optimizations."""
        if not self.is_available():
            raise RuntimeError("Unsloth is not available. Please install required dependencies.")
        
        try:
            # Check cache first
            cache_key = f"{model_name}_{max_seq_length}_{load_in_4bit}"
            if cache_key in self.model_cache:
                logger.info(f"Loading cached model: {model_name}")
                return self.model_cache[cache_key]
            
            logger.info(f"Loading Unsloth model: {model_name}")
            
            model, tokenizer = FastLanguageModel.from_pretrained(
                model_name=model_name,
                max_seq_length=max_seq_length,
                dtype=None,  # Auto-detect
                load_in_4bit=load_in_4bit,
                use_gradient_checkpointing="unsloth"
            )
            
            # Apply LoRA with optimized settings
            model = FastLanguageModel.get_peft_model(
                model,
                r=16,  # LoRA rank
                target_modules=[
                    "q_proj", "k_proj", "v_proj", "o_proj",
                    "gate_proj", "up_proj", "down_proj"
                ],
                lora_alpha=32,
                lora_dropout=0.05,
                bias="none",
                use_gradient_checkpointing="unsloth",
                random_state=42,
                use_rslora=True,  # Rank stabilized LoRA
                loftq_config=None
            )
            
            # Cache the model
            self.model_cache[cache_key] = (model, tokenizer)
            
            logger.info(f"Successfully loaded Unsloth model: {model_name}")
            return model, tokenizer
            
        except Exception as e:
            logger.error(f"Error loading Unsloth model {model_name}: {e}")
            logger.error(traceback.format_exc())
            raise
    
    def train_efficient(
        self,
        texts: List[str],
        labels: List[List[str]],
        model_name: str = "unsloth/mistral-7b-bnb-4bit",
        num_epochs: int = 3,
        learning_rate: float = 2e-4,
        batch_size: int = 2,
        gradient_accumulation_steps: int = 4,
        max_seq_length: int = 2048,
        hierarchy_levels: Optional[List[str]] = None,
        validation_split: float = 0.15
    ) -> Tuple[Any, Any, Dict[str, Any]]:
        """Train model with Unsloth optimizations."""
        if not self.is_available():
            raise RuntimeError("Unsloth is not available. Please install required dependencies.")
        
        try:
            logger.info(f"Starting Unsloth training with {model_name}")
            
            # Load model and tokenizer
            model, tokenizer = self.load_model_and_tokenizer(
                model_name=model_name,
                max_seq_length=max_seq_length
            )
            
            # Prepare dataset
            dataset = self.prepare_classification_dataset(
                texts=texts,
                labels=labels,
                hierarchy_levels=hierarchy_levels
            )
            
            # Split dataset
            if validation_split > 0:
                split_dataset = dataset.train_test_split(test_size=validation_split, seed=42)
                train_dataset = split_dataset['train']
                eval_dataset = split_dataset['test']
            else:
                train_dataset = dataset
                eval_dataset = None
            
            # Set up training arguments
            training_args = TrainingArguments(
                per_device_train_batch_size=batch_size,
                per_device_eval_batch_size=batch_size,
                gradient_accumulation_steps=gradient_accumulation_steps,
                warmup_steps=5,
                num_train_epochs=num_epochs,
                learning_rate=learning_rate,
                fp16=not torch.cuda.is_bf16_supported(),
                bf16=torch.cuda.is_bf16_supported(),
                logging_steps=1,
                optim="adamw_8bit",
                weight_decay=0.01,
                lr_scheduler_type="linear",
                seed=42,
                output_dir="./unsloth_outputs",
                save_strategy="epoch",
                evaluation_strategy="epoch" if eval_dataset else "no",
                load_best_model_at_end=True if eval_dataset else False,
                metric_for_best_model="eval_loss" if eval_dataset else None,
                greater_is_better=False,
                report_to=None,  # Disable wandb/tensorboard
                remove_unused_columns=False,
                dataloader_pin_memory=False
            )
            
            # Format dataset for training
            def formatting_prompts_func(examples):
                instructions = examples["instruction"]
                inputs = examples["input"]
                outputs = examples["output"]
                texts = []
                for instruction, input_text, output in zip(instructions, inputs, outputs):
                    text = f"### Instruction:\n{instruction}\n\n### Input:\n{input_text}\n\n### Response:\n{output}"
                    texts.append(text)
                return {"text": texts}
            
            train_dataset = train_dataset.map(formatting_prompts_func, batched=True)
            if eval_dataset:
                eval_dataset = eval_dataset.map(formatting_prompts_func, batched=True)
            
            # Initialize trainer
            trainer = SFTTrainer(
                model=model,
                tokenizer=tokenizer,
                train_dataset=train_dataset,
                eval_dataset=eval_dataset,
                dataset_text_field="text",
                max_seq_length=max_seq_length,
                dataset_num_proc=2,
                packing=False,
                args=training_args
            )
            
            # Train the model
            logger.info("Starting Unsloth training...")
            train_result = trainer.train()
            
            # Get training metrics
            metrics = {
                "train_loss": train_result.training_loss,
                "train_runtime": train_result.metrics.get("train_runtime", 0),
                "train_samples_per_second": train_result.metrics.get("train_samples_per_second", 0),
                "train_steps_per_second": train_result.metrics.get("train_steps_per_second", 0),
                "total_flos": train_result.metrics.get("total_flos", 0),
                "num_parameters": sum(p.numel() for p in model.parameters()),
                "num_trainable_parameters": sum(p.numel() for p in model.parameters() if p.requires_grad)
            }
            
            if eval_dataset:
                eval_result = trainer.evaluate()
                metrics.update({
                    "eval_loss": eval_result.get("eval_loss", 0),
                    "eval_runtime": eval_result.get("eval_runtime", 0),
                    "eval_samples_per_second": eval_result.get("eval_samples_per_second", 0)
                })
            
            logger.info(f"Unsloth training completed. Metrics: {json.dumps(metrics, indent=2)}")
            
            # Move model to CPU for saving
            model.cpu()
            
            return model, tokenizer, metrics
            
        except Exception as e:
            logger.error(f"Error in Unsloth training: {e}")
            logger.error(traceback.format_exc())
            raise
    
    def save_model(
        self,
        model: Any,
        tokenizer: Any,
        save_path: str,
        save_method: str = "lora"
    ) -> bool:
        """Save trained model with Unsloth optimizations."""
        try:
            os.makedirs(save_path, exist_ok=True)
            
            if save_method == "lora":
                # Save only LoRA adapters (much smaller)
                model.save_pretrained(save_path)
                tokenizer.save_pretrained(save_path)
            elif save_method == "merged":
                # Save merged model (larger but standalone)
                model = FastLanguageModel.for_inference(model)
                model.save_pretrained(save_path)
                tokenizer.save_pretrained(save_path)
            else:
                raise ValueError(f"Unsupported save method: {save_method}")
            
            logger.info(f"Model saved successfully to {save_path} using {save_method} method")
            return True
            
        except Exception as e:
            logger.error(f"Error saving Unsloth model: {e}")
            return False
    
    def clear_cache(self):
        """Clear model cache to free memory."""
        self.model_cache.clear()
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        logger.info("Unsloth model cache cleared")

# Global instance
unsloth_pipeline = UnslothTrainingPipeline()
