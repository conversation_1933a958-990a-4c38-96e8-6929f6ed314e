import requests
import json
from pprint import pprint

# Test HF rules update endpoint
def test_hf_rules_update():
    # Replace with an actual model ID from your database
    model_id = 1  # Change this to a valid model ID
    
    # Create test rules
    rules_data = {
        "rules": [
            {
                "Label": "Test Label 1",
                "Keywords": "test,example,sample",
                "Confidence Threshold": 0.7
            },
            {
                "Label": "Test Label 2",
                "Keywords": "another,test,keywords",
                "Confidence Threshold": 0.5
            }
        ]
    }
    
    print(f"Sending rules update request for model ID {model_id}:")
    pprint(rules_data)
    
    # Send the request
    try:
        response = requests.put(f"http://localhost:8000/hf/rules/{model_id}", json=rules_data)
        
        print(f"\nResponse status code: {response.status_code}")
        
        if response.status_code == 200:
            print("Rules update successful!")
            print("Response:")
            pprint(response.json())
            
            # Now fetch the rules to verify they were saved
            print("\nFetching rules to verify:")
            get_response = requests.get(f"http://localhost:8000/hf/rules/{model_id}")
            
            if get_response.status_code == 200:
                print("Rules fetch successful!")
                print("Response:")
                pprint(get_response.json())
                return get_response.json()
            else:
                print("Rules fetch failed!")
                print("Response:")
                try:
                    pprint(get_response.json())
                except:
                    print(get_response.text)
        else:
            print("Rules update failed!")
            print("Response:")
            try:
                pprint(response.json())
            except:
                print(response.text)
            return None
    except Exception as e:
        print(f"Error sending rules update request: {e}")
        return None

if __name__ == "__main__":
    test_hf_rules_update()
