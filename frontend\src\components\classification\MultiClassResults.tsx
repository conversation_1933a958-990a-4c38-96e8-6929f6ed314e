/**
 * MultiClassResults.tsx
 *
 * Comprehensive results visualization for multi-class classification.
 * Features confusion matrix, per-class metrics, strategy comparison, and performance analysis.
 */

import React, { useState, useEffect, useMemo } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Progress } from "@/components/ui/progress";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";
import {
  BarChart3,
  Grid3X3,
  Target,
  TrendingUp,
  Download,
  Eye,
  AlertCircle,
  CheckCircle2,
  Info,
  Activity,
  Share2,
  FileText,
  Image,
  BarChart,
  PieChart,
  Zap,
  XCircle,
  RefreshCw
} from "lucide-react";
import {
  ResponsiveContainer,
  BarChart as RechartsBarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  PieChart as RechartsPieChart,
  Pie,
  Cell,
  RadarChart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  Radar
} from 'recharts';

export interface MultiClassResults {
  // Overall metrics
  accuracy: number;
  f1Score: number;
  precision: number;
  recall: number;
  
  // Strategy information
  strategy: 'softmax' | 'ovr' | 'ovo' | 'auto';
  actualStrategy?: string; // For 'auto' strategy
  
  // Class-specific metrics
  perClassMetrics: Record<string, {
    precision: number;
    recall: number;
    f1Score: number;
    support: number;
  }>;
  
  // Confusion matrix
  confusionMatrix: number[][];
  classNames: string[];
  
  // Strategy-specific results
  strategySpecific?: {
    softmax?: {
      classConfidences: Record<string, number>;
      temperature: number;
    };
    ovr?: {
      binaryClassifiers: Record<string, { accuracy: number; auc: number }>;
    };
    ovo?: {
      pairwiseClassifiers: Record<string, { accuracy: number; classes: [string, string] }>;
    };
  };
  
  // Training metadata
  trainingTime: number;
  modelSize: number;
  totalSamples: number;
  
  // Predictions sample
  samplePredictions?: Array<{
    text: string;
    actualClass: string;
    predictedClass: string;
    confidence: number;
    correct: boolean;
  }>;

  // Enhanced metrics for visualization
  macroAvg?: {
    precision: number;
    recall: number;
    f1Score: number;
  };
  weightedAvg?: {
    precision: number;
    recall: number;
    f1Score: number;
  };

  // Strategy comparison data (if available)
  strategyComparison?: Array<{
    strategy: string;
    accuracy: number;
    f1Score: number;
    trainingTime: number;
    modelSize: number;
  }>;
}

interface MultiClassResultsProps {
  results: MultiClassResults;
  onExport?: (format: 'csv' | 'excel' | 'json' | 'pdf' | 'png') => void;
  onRetrain?: () => void;
  onShare?: () => void;
  showExportOptions?: boolean;
  showStrategyComparison?: boolean;
  modelName?: string;
}

export const MultiClassResults: React.FC<MultiClassResultsProps> = ({
  results,
  onExport,
  onRetrain,
  onShare,
  showExportOptions = true,
  showStrategyComparison = false,
  modelName = "Multi-Class Model"
}) => {
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState('overview');
  const [exportFormat, setExportFormat] = useState<'csv' | 'excel' | 'json' | 'pdf' | 'png'>('csv');
  const [visualizationType, setVisualizationType] = useState<'bar' | 'radar' | 'pie'>('bar');

  // Enhanced metrics calculation
  const enhancedMetrics = useMemo(() => {
    const classMetrics = Object.values(results.perClassMetrics);
    const totalSupport = classMetrics.reduce((sum, metrics) => sum + metrics.support, 0);

    const macroAvg = {
      precision: classMetrics.reduce((sum, metrics) => sum + metrics.precision, 0) / classMetrics.length,
      recall: classMetrics.reduce((sum, metrics) => sum + metrics.recall, 0) / classMetrics.length,
      f1Score: classMetrics.reduce((sum, metrics) => sum + metrics.f1Score, 0) / classMetrics.length
    };

    const weightedAvg = {
      precision: classMetrics.reduce((sum, metrics) => sum + (metrics.precision * metrics.support), 0) / totalSupport,
      recall: classMetrics.reduce((sum, metrics) => sum + (metrics.recall * metrics.support), 0) / totalSupport,
      f1Score: classMetrics.reduce((sum, metrics) => sum + (metrics.f1Score * metrics.support), 0) / totalSupport
    };

    return { macroAvg, weightedAvg, totalSupport };
  }, [results.perClassMetrics]);

  // Visualization data preparation
  const chartData = useMemo(() => {
    return Object.entries(results.perClassMetrics).map(([className, metrics]) => ({
      class: className,
      precision: (metrics.precision * 100).toFixed(1),
      recall: (metrics.recall * 100).toFixed(1),
      f1Score: (metrics.f1Score * 100).toFixed(1),
      support: metrics.support
    }));
  }, [results.perClassMetrics]);

  const radarData = useMemo(() => {
    return Object.entries(results.perClassMetrics).map(([className, metrics]) => ({
      class: className,
      precision: metrics.precision * 100,
      recall: metrics.recall * 100,
      f1Score: metrics.f1Score * 100
    }));
  }, [results.perClassMetrics]);

  const pieData = useMemo(() => {
    return Object.entries(results.perClassMetrics).map(([className, metrics]) => ({
      name: className,
      value: metrics.support,
      percentage: ((metrics.support / enhancedMetrics.totalSupport) * 100).toFixed(1)
    }));
  }, [results.perClassMetrics, enhancedMetrics.totalSupport]);

  const handleExport = (format: 'csv' | 'excel' | 'json' | 'pdf' | 'png') => {
    if (onExport) {
      onExport(format);
      toast({
        title: "Export started",
        description: `Exporting results in ${format.toUpperCase()} format`
      });
    }
  };

  const handleShare = () => {
    if (onShare) {
      onShare();
    } else {
      // Default share functionality
      const shareData = {
        title: `${modelName} - Multi-Class Classification Results`,
        text: `Model achieved ${(results.accuracy * 100).toFixed(1)}% accuracy with ${results.strategy.toUpperCase()} strategy`,
        url: window.location.href
      };

      if (navigator.share) {
        navigator.share(shareData);
      } else {
        navigator.clipboard.writeText(`${shareData.title}\n${shareData.text}\n${shareData.url}`);
        toast({
          title: "Results copied",
          description: "Results summary copied to clipboard"
        });
      }
    }
  };

  // Color palette for charts
  const COLORS = ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6', '#06b6d4', '#84cc16', '#f97316'];

  const renderConfusionMatrix = () => {
    const { confusionMatrix, classNames } = results;
    
    return (
      <div className="space-y-4">
        <h4 className="font-medium">Confusion Matrix</h4>
        <div className="overflow-x-auto">
          <table className="w-full border-collapse">
            <thead>
              <tr>
                <th className="p-2 text-xs font-medium text-muted-foreground">Actual \ Predicted</th>
                {classNames.map((className) => (
                  <th key={className} className="p-2 text-xs font-medium text-center">
                    {className}
                  </th>
                ))}
              </tr>
            </thead>
            <tbody>
              {confusionMatrix.map((row, i) => (
                <tr key={i}>
                  <td className="p-2 text-xs font-medium text-muted-foreground">
                    {classNames[i]}
                  </td>
                  {row.map((value, j) => {
                    const isCorrect = i === j;
                    const total = row.reduce((sum, val) => sum + val, 0);
                    const percentage = total > 0 ? (value / total) * 100 : 0;
                    
                    return (
                      <td
                        key={j}
                        className={`p-2 text-center text-xs ${
                          isCorrect 
                            ? 'bg-green-100 text-green-800 font-medium' 
                            : value > 0 
                              ? 'bg-red-50 text-red-600' 
                              : 'bg-gray-50'
                        }`}
                      >
                        <div>{value}</div>
                        <div className="text-xs opacity-75">
                          {percentage.toFixed(1)}%
                        </div>
                      </td>
                    );
                  })}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    );
  };

  return (
    <div className="space-y-6">
      {/* Enhanced Header */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <BarChart3 className="w-5 h-5" />
              {modelName} - Results Analysis
            </h3>
            <div className="flex items-center gap-4 text-sm text-muted-foreground mt-1">
              <span>Strategy: {results.strategy.toUpperCase()}</span>
              {results.actualStrategy && results.strategy === 'auto' && (
                <span>→ {results.actualStrategy.toUpperCase()}</span>
              )}
              <span>•</span>
              <span>{results.classNames.length} classes</span>
              <span>•</span>
              <span>{results.totalSamples.toLocaleString()} samples</span>
              {results.trainingTime && (
                <>
                  <span>•</span>
                  <span>Training: {Math.round(results.trainingTime / 60)}min</span>
                </>
              )}
            </div>
          </div>
          <div className="flex gap-2">
            {showExportOptions && (
              <div className="flex items-center gap-2">
                <Select value={exportFormat} onValueChange={setExportFormat}>
                  <SelectTrigger className="w-24">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="csv">CSV</SelectItem>
                    <SelectItem value="excel">Excel</SelectItem>
                    <SelectItem value="json">JSON</SelectItem>
                    <SelectItem value="pdf">PDF</SelectItem>
                    <SelectItem value="png">PNG</SelectItem>
                  </SelectContent>
                </Select>
                <Button variant="outline" onClick={() => handleExport(exportFormat)}>
                  <Download className="w-4 h-4 mr-2" />
                  Export
                </Button>
              </div>
            )}
            <Button variant="outline" onClick={handleShare}>
              <Share2 className="w-4 h-4 mr-2" />
              Share
            </Button>
            {onRetrain && (
              <Button variant="outline" onClick={onRetrain}>
                <RefreshCw className="w-4 h-4 mr-2" />
                Retrain
              </Button>
            )}
          </div>
        </div>

        {/* Quick Performance Summary */}
        <div className="grid grid-cols-4 gap-4">
          <Card className="p-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {(results.accuracy * 100).toFixed(1)}%
              </div>
              <div className="text-sm text-muted-foreground">Accuracy</div>
            </div>
          </Card>
          <Card className="p-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {(enhancedMetrics.macroAvg.f1Score * 100).toFixed(1)}%
              </div>
              <div className="text-sm text-muted-foreground">Macro F1</div>
            </div>
          </Card>
          <Card className="p-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">
                {(enhancedMetrics.weightedAvg.f1Score * 100).toFixed(1)}%
              </div>
              <div className="text-sm text-muted-foreground">Weighted F1</div>
            </div>
          </Card>
          <Card className="p-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">
                {results.modelSize ? `${results.modelSize}MB` : 'N/A'}
              </div>
              <div className="text-sm text-muted-foreground">Model Size</div>
            </div>
          </Card>
        </div>
      </div>

      {/* Overall Performance */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="w-4 h-4" />
            Overall Performance
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {(results.accuracy * 100).toFixed(1)}%
              </div>
              <div className="text-sm text-muted-foreground">Accuracy</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {(results.f1Score * 100).toFixed(1)}%
              </div>
              <div className="text-sm text-muted-foreground">F1 Score</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">
                {(macroF1 * 100).toFixed(1)}%
              </div>
              <div className="text-sm text-muted-foreground">Macro F1</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">
                {(weightedF1 * 100).toFixed(1)}%
              </div>
              <div className="text-sm text-muted-foreground">Weighted F1</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Enhanced Results Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className={`grid w-full ${showStrategyComparison ? 'grid-cols-6' : 'grid-cols-5'}`}>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="visualizations">Charts</TabsTrigger>
          <TabsTrigger value="confusion">Confusion Matrix</TabsTrigger>
          <TabsTrigger value="classes">Per-Class Metrics</TabsTrigger>
          {showStrategyComparison && (
            <TabsTrigger value="comparison">Strategy Comparison</TabsTrigger>
          )}
          <TabsTrigger value="samples">Sample Predictions</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Training Summary</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2 text-sm">
                <div><strong>Total Samples:</strong> {results.totalSamples.toLocaleString()}</div>
                <div><strong>Training Time:</strong> {Math.round(results.trainingTime / 60)} minutes</div>
                <div><strong>Model Size:</strong> {results.modelSize}MB</div>
                <div><strong>Strategy Used:</strong> {results.actualStrategy || results.strategy}</div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Class Distribution</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                {Object.entries(results.perClassMetrics).map(([className, metrics]) => (
                  <div key={className} className="space-y-1">
                    <div className="flex justify-between text-sm">
                      <span>{className}</span>
                      <span>{metrics.support} samples</span>
                    </div>
                    <Progress 
                      value={(metrics.support / results.totalSamples) * 100} 
                      className="h-2"
                    />
                  </div>
                ))}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Enhanced Visualizations Tab */}
        <TabsContent value="visualizations" className="space-y-4">
          <div className="space-y-4">
            {/* Visualization Controls */}
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center gap-2">
                    <BarChart className="w-4 h-4" />
                    Performance Visualizations
                  </CardTitle>
                  <Select value={visualizationType} onValueChange={setVisualizationType}>
                    <SelectTrigger className="w-32">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="bar">Bar Chart</SelectItem>
                      <SelectItem value="radar">Radar Chart</SelectItem>
                      <SelectItem value="pie">Pie Chart</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </CardHeader>
              <CardContent>
                {visualizationType === 'bar' && (
                  <ResponsiveContainer width="100%" height={400}>
                    <RechartsBarChart data={chartData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="class" />
                      <YAxis domain={[0, 100]} />
                      <Tooltip formatter={(value) => `${value}%`} />
                      <Legend />
                      <Bar dataKey="precision" fill="#3b82f6" name="Precision" />
                      <Bar dataKey="recall" fill="#10b981" name="Recall" />
                      <Bar dataKey="f1Score" fill="#f59e0b" name="F1 Score" />
                    </RechartsBarChart>
                  </ResponsiveContainer>
                )}

                {visualizationType === 'radar' && (
                  <ResponsiveContainer width="100%" height={400}>
                    <RadarChart data={radarData}>
                      <PolarGrid />
                      <PolarAngleAxis dataKey="class" />
                      <PolarRadiusAxis domain={[0, 100]} />
                      <Radar name="Precision" dataKey="precision" stroke="#3b82f6" fill="#3b82f6" fillOpacity={0.3} />
                      <Radar name="Recall" dataKey="recall" stroke="#10b981" fill="#10b981" fillOpacity={0.3} />
                      <Radar name="F1 Score" dataKey="f1Score" stroke="#f59e0b" fill="#f59e0b" fillOpacity={0.3} />
                      <Legend />
                    </RadarChart>
                  </ResponsiveContainer>
                )}

                {visualizationType === 'pie' && (
                  <ResponsiveContainer width="100%" height={400}>
                    <RechartsPieChart>
                      <Pie
                        data={pieData}
                        cx="50%"
                        cy="50%"
                        outerRadius={120}
                        dataKey="value"
                        label={({ name, percentage }) => `${name}: ${percentage}%`}
                      >
                        {pieData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <Tooltip />
                    </RechartsPieChart>
                  </ResponsiveContainer>
                )}
              </CardContent>
            </Card>

            {/* Metrics Summary Cards */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">Macro Average</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>Precision:</span>
                    <span className="font-medium">{(enhancedMetrics.macroAvg.precision * 100).toFixed(2)}%</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Recall:</span>
                    <span className="font-medium">{(enhancedMetrics.macroAvg.recall * 100).toFixed(2)}%</span>
                  </div>
                  <div className="flex justify-between">
                    <span>F1 Score:</span>
                    <span className="font-medium">{(enhancedMetrics.macroAvg.f1Score * 100).toFixed(2)}%</span>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">Weighted Average</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>Precision:</span>
                    <span className="font-medium">{(enhancedMetrics.weightedAvg.precision * 100).toFixed(2)}%</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Recall:</span>
                    <span className="font-medium">{(enhancedMetrics.weightedAvg.recall * 100).toFixed(2)}%</span>
                  </div>
                  <div className="flex justify-between">
                    <span>F1 Score:</span>
                    <span className="font-medium">{(enhancedMetrics.weightedAvg.f1Score * 100).toFixed(2)}%</span>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">Model Performance</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>Overall Accuracy:</span>
                    <span className="font-medium">{(results.accuracy * 100).toFixed(2)}%</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Best Class F1:</span>
                    <span className="font-medium">
                      {Math.max(...Object.values(results.perClassMetrics).map(m => m.f1Score * 100)).toFixed(1)}%
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>Worst Class F1:</span>
                    <span className="font-medium">
                      {Math.min(...Object.values(results.perClassMetrics).map(m => m.f1Score * 100)).toFixed(1)}%
                    </span>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="confusion" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Grid3X3 className="w-4 h-4" />
                Confusion Matrix
              </CardTitle>
              <CardDescription>
                Detailed breakdown of predictions vs actual classes
              </CardDescription>
            </CardHeader>
            <CardContent>
              {renderConfusionMatrix()}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="classes" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Per-Class Performance Metrics</CardTitle>
              <CardDescription>
                Detailed metrics for each class in your multi-class model
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Class</TableHead>
                    <TableHead>Precision</TableHead>
                    <TableHead>Recall</TableHead>
                    <TableHead>F1 Score</TableHead>
                    <TableHead>Support</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {Object.entries(results.perClassMetrics).map(([className, metrics]) => (
                    <TableRow key={className}>
                      <TableCell className="font-medium">{className}</TableCell>
                      <TableCell>{(metrics.precision * 100).toFixed(2)}%</TableCell>
                      <TableCell>{(metrics.recall * 100).toFixed(2)}%</TableCell>
                      <TableCell>{(metrics.f1Score * 100).toFixed(2)}%</TableCell>
                      <TableCell>{metrics.support}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Strategy Comparison Tab */}
        {showStrategyComparison && results.strategyComparison && (
          <TabsContent value="comparison" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Activity className="w-4 h-4" />
                  Strategy Performance Comparison
                </CardTitle>
                <CardDescription>
                  Compare performance across different multi-class strategies
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {/* Comparison Chart */}
                  <ResponsiveContainer width="100%" height={300}>
                    <RechartsBarChart data={results.strategyComparison}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="strategy" />
                      <YAxis domain={[0, 100]} />
                      <Tooltip formatter={(value) => `${value}%`} />
                      <Legend />
                      <Bar dataKey="accuracy" fill="#3b82f6" name="Accuracy %" />
                      <Bar dataKey="f1Score" fill="#10b981" name="F1 Score %" />
                    </RechartsBarChart>
                  </ResponsiveContainer>

                  {/* Comparison Table */}
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Strategy</TableHead>
                        <TableHead>Accuracy</TableHead>
                        <TableHead>F1 Score</TableHead>
                        <TableHead>Training Time</TableHead>
                        <TableHead>Model Size</TableHead>
                        <TableHead>Recommendation</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {results.strategyComparison.map((strategy) => (
                        <TableRow key={strategy.strategy}>
                          <TableCell className="font-medium">
                            {strategy.strategy.toUpperCase()}
                            {strategy.strategy === results.strategy && (
                              <Badge variant="default" className="ml-2">Current</Badge>
                            )}
                          </TableCell>
                          <TableCell>{(strategy.accuracy * 100).toFixed(2)}%</TableCell>
                          <TableCell>{(strategy.f1Score * 100).toFixed(2)}%</TableCell>
                          <TableCell>{Math.round(strategy.trainingTime / 60)}min</TableCell>
                          <TableCell>{strategy.modelSize}MB</TableCell>
                          <TableCell>
                            {strategy.accuracy === Math.max(...results.strategyComparison.map(s => s.accuracy)) && (
                              <Badge variant="default">Best Accuracy</Badge>
                            )}
                            {strategy.f1Score === Math.max(...results.strategyComparison.map(s => s.f1Score)) && (
                              <Badge variant="default">Best F1</Badge>
                            )}
                            {strategy.trainingTime === Math.min(...results.strategyComparison.map(s => s.trainingTime)) && (
                              <Badge variant="outline">Fastest</Badge>
                            )}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>

                  {/* Strategy Recommendations */}
                  <Alert>
                    <Target className="h-4 w-4" />
                    <AlertDescription>
                      <div className="space-y-2">
                        <div className="font-medium">Strategy Recommendations:</div>
                        <ul className="list-disc list-inside space-y-1 text-sm">
                          <li><strong>Softmax:</strong> Best for balanced datasets with moderate number of classes</li>
                          <li><strong>One-vs-Rest (OvR):</strong> Effective for imbalanced datasets and large number of classes</li>
                          <li><strong>One-vs-One (OvO):</strong> Good for small datasets with many classes</li>
                        </ul>
                      </div>
                    </AlertDescription>
                  </Alert>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        )}

        <TabsContent value="samples" className="space-y-4">
          {results.samplePredictions && results.samplePredictions.length > 0 ? (
            <Card>
              <CardHeader>
                <CardTitle>Sample Predictions</CardTitle>
                <CardDescription>
                  Review individual predictions to understand model behavior
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {results.samplePredictions.slice(0, 10).map((prediction, index) => (
                    <div
                      key={index}
                      className={`p-3 rounded-lg border ${
                        prediction.correct 
                          ? 'border-green-200 bg-green-50' 
                          : 'border-red-200 bg-red-50'
                      }`}
                    >
                      <div className="space-y-2">
                        <div className="text-sm font-medium truncate">
                          {prediction.text}
                        </div>
                        <div className="flex items-center justify-between text-xs">
                          <div className="flex gap-4">
                            <span>
                              <strong>Actual:</strong> {prediction.actualClass}
                            </span>
                            <span>
                              <strong>Predicted:</strong> {prediction.predictedClass}
                            </span>
                          </div>
                          <div className="flex items-center gap-2">
                            <Badge variant={prediction.correct ? 'default' : 'destructive'}>
                              {prediction.correct ? 'Correct' : 'Incorrect'}
                            </Badge>
                            <span className="text-muted-foreground">
                              {(prediction.confidence * 100).toFixed(1)}%
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          ) : (
            <Alert>
              <Info className="h-4 w-4" />
              <AlertDescription>
                No sample predictions available. This data will be shown after classification.
              </AlertDescription>
            </Alert>
          )}
        </TabsContent>
      </Tabs>

      {/* Export Options */}
      {showExportOptions && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Download className="w-4 h-4" />
              Export Results
            </CardTitle>
            <CardDescription>
              Download your multi-class classification results
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex gap-2">
              <Button variant="outline" onClick={() => handleExport('csv')}>
                Export as CSV
              </Button>
              <Button variant="outline" onClick={() => handleExport('json')}>
                Export as JSON
              </Button>
              <Button variant="outline" onClick={() => handleExport('pdf')}>
                Export Report (PDF)
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
