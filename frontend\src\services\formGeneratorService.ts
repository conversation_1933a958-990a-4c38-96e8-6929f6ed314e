// frontend/src/services/formGeneratorService.ts
import React from 'react';
import { HierarchyConfig } from '../types';
import { ValidationRule } from './validationService';

export interface FormFieldConfig {
  name: string;
  label: string;
  type: 'text' | 'textarea' | 'select' | 'number' | 'email' | 'url';
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
  options?: { value: string; label: string }[];
  validation?: ValidationRule;
  gridSize?: {
    xs?: number;
    sm?: number;
    md?: number;
    lg?: number;
  };
  helperText?: string;
  multiline?: boolean;
  rows?: number;
  startAdornment?: React.ReactNode;
  endAdornment?: React.ReactNode;
}

export interface FormSectionConfig {
  title: string;
  description?: string;
  fields: FormFieldConfig[];
  collapsible?: boolean;
  defaultExpanded?: boolean;
}

export interface FormConfig {
  title: string;
  description?: string;
  sections: FormSectionConfig[];
  submitButtonText?: string;
  resetButtonText?: string;
  showResetButton?: boolean;
}

export class DynamicFormGeneratorService {
  private hierarchyConfig: HierarchyConfig;

  constructor(hierarchyConfig: HierarchyConfig) {
    this.hierarchyConfig = hierarchyConfig;
  }

  /**
   * Generate form configuration for hierarchy data entry
   */
  generateHierarchyFormConfig(): FormConfig {
    const hierarchyFields: FormFieldConfig[] = this.hierarchyConfig.hierarchy_levels.map((level, index) => {
      const displayName = this.getDisplayName(level);
      const placeholder = this.getPlaceholder(level);
      const validation = this.getValidationRules(level);

      return {
        name: level,
        label: displayName,
        type: 'text',
        placeholder,
        required: validation.required,
        validation,
        gridSize: {
          xs: 12,
          sm: 6,
          md: this.hierarchyConfig.hierarchy_levels.length <= 2 ? 6 : 4
        },
        helperText: index === 0 ? 'Most general category' : 
                   index === this.hierarchyConfig.hierarchy_levels.length - 1 ? 'Most specific category' : 
                   undefined
      };
    });

    // Add Keywords field
    const keywordsField: FormFieldConfig = {
      name: 'Keywords',
      label: 'Keywords',
      type: 'textarea',
      placeholder: 'Enter keywords separated by commas',
      required: false,
      multiline: true,
      rows: 2,
      gridSize: {
        xs: 12,
        sm: 12,
        md: 6
      },
      helperText: 'Comma-separated keywords for classification'
    };

    return {
      title: `${this.hierarchyConfig.name} Entry Form`,
      description: this.hierarchyConfig.description || undefined,
      sections: [
        {
          title: 'Hierarchy Classification',
          description: 'Define the hierarchical classification path',
          fields: hierarchyFields
        },
        {
          title: 'Additional Information',
          description: 'Optional keywords and metadata',
          fields: [keywordsField]
        }
      ],
      submitButtonText: 'Save Entry',
      resetButtonText: 'Clear Form',
      showResetButton: true
    };
  }

  /**
   * Generate form configuration for hierarchy configuration
   */
  generateConfigFormConfig(): FormConfig {
    const basicFields: FormFieldConfig[] = [
      {
        name: 'name',
        label: 'Configuration Name',
        type: 'text',
        placeholder: 'e.g., Customer Support Categories',
        required: true,
        gridSize: { xs: 12, sm: 8 },
        helperText: 'A descriptive name for this hierarchy configuration'
      },
      {
        name: 'description',
        label: 'Description',
        type: 'textarea',
        placeholder: 'Describe what this hierarchy is used for...',
        required: false,
        multiline: true,
        rows: 3,
        gridSize: { xs: 12 },
        helperText: 'Optional description of the hierarchy purpose'
      },
      {
        name: 'domain',
        label: 'Domain',
        type: 'select',
        required: false,
        options: [
          { value: 'General', label: 'General' },
          { value: 'E-commerce', label: 'E-commerce' },
          { value: 'Customer Support', label: 'Customer Support' },
          { value: 'Legal', label: 'Legal' },
          { value: 'Medical', label: 'Medical' },
          { value: 'Education', label: 'Education' },
          { value: 'Finance', label: 'Finance' },
          { value: 'Technology', label: 'Technology' },
          { value: 'Marketing', label: 'Marketing' },
          { value: 'HR', label: 'Human Resources' }
        ],
        gridSize: { xs: 12, sm: 4 },
        helperText: 'Industry or domain this hierarchy applies to'
      }
    ];

    return {
      title: 'Hierarchy Configuration',
      description: 'Configure a new hierarchy structure for classification',
      sections: [
        {
          title: 'Basic Information',
          description: 'General information about this hierarchy',
          fields: basicFields
        }
      ],
      submitButtonText: 'Save Configuration',
      resetButtonText: 'Reset Form',
      showResetButton: true
    };
  }

  /**
   * Generate form configuration for classification settings
   */
  generateClassificationFormConfig(): FormConfig {
    const thresholdFields: FormFieldConfig[] = this.hierarchyConfig.hierarchy_levels.map(level => ({
      name: `threshold_${level}`,
      label: `${this.getDisplayName(level)} Threshold`,
      type: 'number',
      placeholder: '0.5',
      required: true,
      validation: {
        required: true,
        customValidator: (value: number) => {
          if (value < 0.1 || value > 0.9) {
            return 'Threshold must be between 0.1 and 0.9';
          }
          return null;
        }
      },
      gridSize: { xs: 12, sm: 6, md: 4 },
      helperText: 'Minimum confidence for automatic classification'
    }));

    const settingsFields: FormFieldConfig[] = [
      {
        name: 'batch_size',
        label: 'Batch Size',
        type: 'number',
        placeholder: '10',
        required: false,
        gridSize: { xs: 12, sm: 6 },
        helperText: 'Number of texts to process in each batch'
      },
      {
        name: 'max_retries',
        label: 'Max Retries',
        type: 'number',
        placeholder: '3',
        required: false,
        gridSize: { xs: 12, sm: 6 },
        helperText: 'Maximum number of retry attempts for failed classifications'
      }
    ];

    return {
      title: 'Classification Settings',
      description: 'Configure classification parameters and thresholds',
      sections: [
        {
          title: 'Confidence Thresholds',
          description: 'Set minimum confidence levels for each hierarchy level',
          fields: thresholdFields
        },
        {
          title: 'Processing Settings',
          description: 'Configure batch processing and retry behavior',
          fields: settingsFields,
          collapsible: true,
          defaultExpanded: false
        }
      ],
      submitButtonText: 'Apply Settings',
      resetButtonText: 'Reset to Defaults',
      showResetButton: true
    };
  }

  /**
   * Generate dynamic search form configuration
   */
  generateSearchFormConfig(availableFields: string[]): FormConfig {
    const searchFields: FormFieldConfig[] = [
      {
        name: 'query',
        label: 'Search Query',
        type: 'text',
        placeholder: 'Enter search terms...',
        required: false,
        gridSize: { xs: 12, sm: 8 },
        helperText: 'Search across all fields'
      },
      {
        name: 'search_fields',
        label: 'Search In',
        type: 'select',
        options: [
          { value: 'all', label: 'All Fields' },
          ...availableFields.map(field => ({
            value: field,
            label: this.getDisplayName(field)
          }))
        ],
        required: false,
        gridSize: { xs: 12, sm: 4 },
        helperText: 'Limit search to specific fields'
      }
    ];

    // Add hierarchy level filters
    const filterFields: FormFieldConfig[] = this.hierarchyConfig.hierarchy_levels.map(level => ({
      name: `filter_${level}`,
      label: `Filter by ${this.getDisplayName(level)}`,
      type: 'text',
      placeholder: `Filter ${this.getDisplayName(level).toLowerCase()}...`,
      required: false,
      gridSize: { xs: 12, sm: 6, md: 4 }
    }));

    return {
      title: 'Search & Filter',
      description: 'Search and filter classification results',
      sections: [
        {
          title: 'Search',
          fields: searchFields
        },
        {
          title: 'Filters',
          description: 'Filter by specific hierarchy levels',
          fields: filterFields,
          collapsible: true,
          defaultExpanded: false
        }
      ],
      submitButtonText: 'Search',
      resetButtonText: 'Clear Filters',
      showResetButton: true
    };
  }

  /**
   * Get display name for a field
   */
  private getDisplayName(fieldName: string): string {
    const uiConfig = this.hierarchyConfig.ui_config || {};
    const displayNames = uiConfig.display_names || {};
    return displayNames[fieldName] || fieldName.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());
  }

  /**
   * Get placeholder text for a field
   */
  private getPlaceholder(fieldName: string): string {
    const uiConfig = this.hierarchyConfig.ui_config || {};
    const placeholders = uiConfig.placeholders || {};
    return placeholders[fieldName] || `Enter ${this.getDisplayName(fieldName).toLowerCase()}`;
  }

  /**
   * Get validation rules for a field
   */
  private getValidationRules(fieldName: string): ValidationRule {
    const validationRules = this.hierarchyConfig.validation_rules || {};
    const fieldRules = validationRules[fieldName] || {};

    return {
      required: fieldRules.required !== false,
      minLength: fieldRules.min_length || 1,
      maxLength: fieldRules.max_length || 100,
      pattern: fieldRules.pattern || '^[a-zA-Z0-9\\s\\-_\\.]+$'
    };
  }

  /**
   * Generate field configuration for a specific field type
   */
  generateFieldConfig(
    fieldName: string,
    fieldType: FormFieldConfig['type'] = 'text',
    overrides: Partial<FormFieldConfig> = {}
  ): FormFieldConfig {
    const baseConfig: FormFieldConfig = {
      name: fieldName,
      label: this.getDisplayName(fieldName),
      type: fieldType,
      placeholder: this.getPlaceholder(fieldName),
      required: this.getValidationRules(fieldName).required,
      validation: this.getValidationRules(fieldName),
      gridSize: { xs: 12, sm: 6 }
    };

    return { ...baseConfig, ...overrides };
  }
}

/**
 * Create a form generator service instance
 */
export const createFormGeneratorService = (hierarchyConfig: HierarchyConfig): DynamicFormGeneratorService => {
  return new DynamicFormGeneratorService(hierarchyConfig);
};

export default DynamicFormGeneratorService;
