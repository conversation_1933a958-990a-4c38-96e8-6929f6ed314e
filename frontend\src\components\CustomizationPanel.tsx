import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Switch } from "@/components/ui/switch";
import {
  Settings,
  Brain,
  Zap,
  Target,
  Database,
  Sliders,
  CheckCircle2,
  X,
  Plus,
  Loader2
} from "lucide-react";
import { SmartDataAnalysis } from "@/services/dataAnalysisApi";
import { ProviderListResponse } from "@/types/index";
import { getLLMProviders, fetchLLMModels } from "@/services/llmApi";
import { useToast } from "@/hooks/use-toast";

interface CustomizationPanelProps {
  analysis: SmartDataAnalysis;
  onSave: (settings: CustomizationSettings) => void;
  onCancel: () => void;
  initialSettings?: Partial<CustomizationSettings>;
}

export interface CustomizationSettings {
  textColumns: string[]; // Changed from textColumn to textColumns
  labelColumns: string[];
  trainingMethod: 'custom' | 'llm';
  modelType: string;
  hyperparameters: {
    learningRate?: number;
    batchSize?: number;
    epochs?: number;
    validationSplit?: number;
    earlyStoppingPatience?: number;
  };
  preprocessing: {
    removeStopwords: boolean;
    lowercase: boolean;
    removePunctuation: boolean;
    maxSequenceLength?: number;
  };
  advanced: {
    useGpu: boolean;
    enableUnsloth: boolean;
    customPrompt?: string;
  };
  llmConfig?: {
    provider: string;
    model: string;
    endpoint: string;
    temperature?: number;
    maxTokens?: number;
  };
}

export const CustomizationPanel = ({ 
  analysis, 
  onSave, 
  onCancel, 
  initialSettings 
}: CustomizationPanelProps) => {
  const { toast } = useToast();
  const [customLabelInput, setCustomLabelInput] = useState('');
  const [providers, setProviders] = useState<ProviderListResponse | null>(null);
  const [models, setModels] = useState<string[]>([]);
  const [loadingProviders, setLoadingProviders] = useState(false);
  const [loadingModels, setLoadingModels] = useState(false);
  const [settings, setSettings] = useState<CustomizationSettings>({
    textColumns: initialSettings?.textColumns || (analysis.preview.text_column ? [analysis.preview.text_column] : []),
    labelColumns: initialSettings?.labelColumns || analysis.preview.label_columns || [],
    trainingMethod: initialSettings?.trainingMethod || 'custom',
    modelType: initialSettings?.modelType || 'distilbert-base-uncased',
    hyperparameters: {
      learningRate: 2e-5,
      batchSize: 16,
      epochs: 3,
      validationSplit: 0.2,
      earlyStoppingPatience: 2,
      ...initialSettings?.hyperparameters
    },
    preprocessing: {
      removeStopwords: false,
      lowercase: true,
      removePunctuation: false,
      maxSequenceLength: 512,
      ...initialSettings?.preprocessing
    },
    advanced: {
      useGpu: true,
      enableUnsloth: true,
      customPrompt: '',
      ...initialSettings?.advanced
    },
    llmConfig: {
      provider: '',
      model: '',
      endpoint: '',
      temperature: 0.1,
      maxTokens: 100,
      ...initialSettings?.llmConfig
    }
  });

  const availableColumns = Object.keys(analysis.column_analysis);
  // Allow selection from all columns, but prioritize detected ones
  const textColumns = availableColumns; // Show all columns for text selection
  const labelColumns = availableColumns; // Show all columns for label selection

  // Helper function to get column priority (detected columns first)
  const getColumnPriority = (col: string, isText: boolean) => {
    const columnInfo = analysis.column_analysis[col];
    if (isText && columnInfo.is_potential_text) return 0;
    if (!isText && columnInfo.is_potential_label) return 0;
    return 1;
  };

  const modelOptions = [
    { value: 'distilbert-base-uncased', label: 'DistilBERT (Fast)', description: 'Good balance of speed and accuracy' },
    { value: 'bert-base-uncased', label: 'BERT Base', description: 'Standard BERT model' },
    { value: 'roberta-base', label: 'RoBERTa Base', description: 'Improved BERT variant' },
    { value: 'albert-base-v2', label: 'ALBERT Base', description: 'Lightweight BERT' },
    { value: 'electra-base-discriminator', label: 'ELECTRA Base', description: 'Efficient pre-training' }
  ];

  // Load LLM providers on component mount
  useEffect(() => {
    const loadProviders = async () => {
      try {
        setLoadingProviders(true);
        const providersData = await getLLMProviders();
        setProviders(providersData);

        // Set default provider if none selected
        if (!settings.llmConfig?.provider && providersData.providers.length > 0) {
          const defaultProvider = providersData.providers[0];
          const defaultEndpoint = providersData.default_endpoints[defaultProvider] || "";

          updateSettings('llmConfig', {
            ...settings.llmConfig,
            provider: defaultProvider,
            endpoint: defaultEndpoint
          });
        }
      } catch (error) {
        console.error('Failed to load providers:', error);
        toast({
          title: "Error loading providers",
          description: "Failed to fetch available AI providers",
          variant: "destructive"
        });
      } finally {
        setLoadingProviders(false);
      }
    };

    if (settings.trainingMethod === 'llm') {
      loadProviders();
    }
  }, [settings.trainingMethod]);

  // Load models when provider changes
  useEffect(() => {
    const loadModels = async () => {
      if (!settings.llmConfig?.provider || !settings.llmConfig?.endpoint) return;

      try {
        setLoadingModels(true);
        const modelsData = await fetchLLMModels({
          provider: settings.llmConfig.provider,
          endpoint: settings.llmConfig.endpoint
        });
        setModels(modelsData.models);

        // Set default model if none selected
        if (!settings.llmConfig.model && modelsData.models.length > 0) {
          updateSettings('llmConfig', {
            ...settings.llmConfig,
            model: modelsData.models[0]
          });
        }
      } catch (error) {
        console.error('Failed to load models:', error);
        toast({
          title: "Error loading models",
          description: `Failed to fetch models for ${settings.llmConfig?.provider}`,
          variant: "destructive"
        });
        setModels([]);
      } finally {
        setLoadingModels(false);
      }
    };

    if (settings.trainingMethod === 'llm' && settings.llmConfig?.provider) {
      loadModels();
    }
  }, [settings.llmConfig?.provider, settings.llmConfig?.endpoint]);

  const updateSettings = (key: keyof CustomizationSettings, value: any) => {
    setSettings(prev => ({ ...prev, [key]: value }));
  };

  const handleProviderChange = (provider: string) => {
    if (!providers) return;

    const defaultEndpoint = providers.default_endpoints[provider] || "";
    updateSettings('llmConfig', {
      ...settings.llmConfig,
      provider,
      endpoint: defaultEndpoint,
      model: "" // Clear model selection while loading
    });
  };

  const handleModelChange = (model: string) => {
    updateSettings('llmConfig', {
      ...settings.llmConfig,
      model
    });
  };

  const getProviderStatus = (provider: string) => {
    if (!providers) return "unknown";
    return providers.api_keys[provider] === "available" ? "available" : "missing";
  };

  const updateNestedSettings = (section: keyof CustomizationSettings, key: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [section]: { ...(prev[section] as Record<string, any> || {}), [key]: value }
    }));
  };

  // Helper functions for managing multiple column selections
  const addTextColumn = (column: string) => {
    if (!settings.textColumns.includes(column)) {
      updateSettings('textColumns', [...settings.textColumns, column]);
    }
  };

  const removeTextColumn = (column: string) => {
    updateSettings('textColumns', settings.textColumns.filter(col => col !== column));
  };

  const addLabelColumn = (column: string) => {
    if (!settings.labelColumns.includes(column)) {
      updateSettings('labelColumns', [...settings.labelColumns, column]);
    }
  };

  const removeLabelColumn = (column: string) => {
    updateSettings('labelColumns', settings.labelColumns.filter(col => col !== column));
  };

  const addCustomLabelColumn = () => {
    if (customLabelInput.trim() && !settings.labelColumns.includes(customLabelInput.trim())) {
      updateSettings('labelColumns', [...settings.labelColumns, customLabelInput.trim()]);
      setCustomLabelInput('');
    }
  };

  const handleCustomLabelKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      addCustomLabelColumn();
    }
  };

  return (
    <Card className="border-2 border-primary/20">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 rounded-lg bg-primary/10 flex items-center justify-center">
              <Settings className="w-5 h-5 text-primary" />
            </div>
            <div>
              <CardTitle>Customize Your Model</CardTitle>
              <CardDescription>
                Fine-tune all parameters to match your specific requirements
              </CardDescription>
            </div>
          </div>
          <Button variant="ghost" size="icon" onClick={onCancel}>
            <X className="w-4 h-4" />
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Data Configuration */}
        <div className="space-y-4">
          <div className="flex items-center gap-2">
            <Database className="w-4 h-4 text-primary" />
            <h4 className="font-semibold">Data Configuration</h4>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="text-columns">Text Columns</Label>

              {/* Selected text columns */}
              {settings.textColumns.length > 0 && (
                <div className="flex flex-wrap gap-2 mb-2">
                  {settings.textColumns.map(col => (
                    <Badge key={col} variant="secondary" className="flex items-center gap-1">
                      {col}
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-4 w-4 p-0 hover:bg-destructive hover:text-destructive-foreground"
                        onClick={() => removeTextColumn(col)}
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </Badge>
                  ))}
                </div>
              )}

              {/* Add text column dropdown */}
              <Select value="" onValueChange={addTextColumn}>
                <SelectTrigger>
                  <SelectValue placeholder="Add text column" />
                </SelectTrigger>
                <SelectContent>
                  {textColumns
                    .filter(col => !settings.textColumns.includes(col))
                    .sort((a, b) => getColumnPriority(a, true) - getColumnPriority(b, true))
                    .map(col => {
                      const columnInfo = analysis.column_analysis[col];
                      const isRecommended = columnInfo.is_potential_text;

                      return (
                        <SelectItem key={col} value={col}>
                          <div className="flex items-center gap-2">
                            <span>{col}</span>
                            {isRecommended && (
                              <Badge variant="default" className="text-xs bg-ml-primary/10 text-ml-primary">
                                Recommended
                              </Badge>
                            )}
                            <Badge variant="secondary" className="text-xs">
                              {columnInfo.unique_count} unique
                            </Badge>
                          </div>
                        </SelectItem>
                      );
                    })}
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="label-columns">Label Columns</Label>

              {/* Selected label columns */}
              {settings.labelColumns.length > 0 && (
                <div className="flex flex-wrap gap-2 mb-2">
                  {settings.labelColumns.map(col => (
                    <Badge key={col} variant="secondary" className="flex items-center gap-1">
                      {col}
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-4 w-4 p-0 hover:bg-destructive hover:text-destructive-foreground"
                        onClick={() => removeLabelColumn(col)}
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </Badge>
                  ))}
                </div>
              )}

              {/* Add label column dropdown */}
              <Select value="" onValueChange={addLabelColumn}>
                <SelectTrigger>
                  <SelectValue placeholder="Add label column" />
                </SelectTrigger>
                <SelectContent>
                  {labelColumns
                    .filter(col => !settings.labelColumns.includes(col))
                    .sort((a, b) => getColumnPriority(a, false) - getColumnPriority(b, false))
                    .map(col => {
                      const columnInfo = analysis.column_analysis[col];
                      const isRecommended = columnInfo.is_potential_label;

                      return (
                        <SelectItem key={col} value={col}>
                          <div className="flex items-center gap-2">
                            <span>{col}</span>
                            {isRecommended && (
                              <Badge variant="default" className="text-xs bg-ml-primary/10 text-ml-primary">
                                Recommended
                              </Badge>
                            )}
                            <Badge variant="secondary" className="text-xs">
                              {columnInfo.unique_count} classes
                            </Badge>
                          </div>
                        </SelectItem>
                      );
                    })}
                </SelectContent>
              </Select>

              {/* Custom label column input */}
              <div className="flex gap-2 mt-2">
                <Input
                  placeholder="Add custom label column"
                  value={customLabelInput}
                  onChange={(e) => setCustomLabelInput(e.target.value)}
                  onKeyPress={handleCustomLabelKeyPress}
                  className="flex-1"
                />
                <Button
                  variant="outline"
                  size="sm"
                  onClick={addCustomLabelColumn}
                  disabled={!customLabelInput.trim()}
                >
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        </div>

        <Separator />

        {/* Training Method */}
        <div className="space-y-4">
          <div className="flex items-center gap-2">
            <Brain className="w-4 h-4 text-ml-secondary" />
            <h4 className="font-semibold">Training Method</h4>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card 
              className={`cursor-pointer transition-all ${
                settings.trainingMethod === 'custom' ? 'border-2 border-primary bg-primary/5' : 'hover:border-primary/20'
              }`}
              onClick={() => updateSettings('trainingMethod', 'custom')}
            >
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <Brain className="w-6 h-6 text-ml-primary" />
                  <div>
                    <h5 className="font-medium">Custom Training</h5>
                    <p className="text-xs text-muted-foreground">Train specialized model</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card 
              className={`cursor-pointer transition-all ${
                settings.trainingMethod === 'llm' ? 'border-2 border-primary bg-primary/5' : 'hover:border-primary/20'
              }`}
              onClick={() => updateSettings('trainingMethod', 'llm')}
            >
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <Zap className="w-6 h-6 text-ml-secondary" />
                  <div>
                    <h5 className="font-medium">LLM Inference</h5>
                    <p className="text-xs text-muted-foreground">Use language models</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Model Selection (for custom training) */}
        {settings.trainingMethod === 'custom' && (
          <>
            <Separator />
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <Target className="w-4 h-4 text-ml-accent" />
                <h4 className="font-semibold">Model Selection</h4>
              </div>
              
              <Select value={settings.modelType} onValueChange={(value) => updateSettings('modelType', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select model" />
                </SelectTrigger>
                <SelectContent>
                  {modelOptions.map(model => (
                    <SelectItem key={model.value} value={model.value}>
                      <div>
                        <div className="font-medium">{model.label}</div>
                        <div className="text-xs text-muted-foreground">{model.description}</div>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <Separator />

            {/* Hyperparameters */}
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <Sliders className="w-4 h-4 text-ml-warning" />
                <h4 className="font-semibold">Hyperparameters</h4>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="learning-rate">Learning Rate</Label>
                  <Input
                    id="learning-rate"
                    type="number"
                    step="0.00001"
                    value={settings.hyperparameters.learningRate}
                    onChange={(e) => updateNestedSettings('hyperparameters', 'learningRate', parseFloat(e.target.value))}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="batch-size">Batch Size</Label>
                  <Select 
                    value={settings.hyperparameters.batchSize?.toString()} 
                    onValueChange={(value) => updateNestedSettings('hyperparameters', 'batchSize', parseInt(value))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="8">8</SelectItem>
                      <SelectItem value="16">16</SelectItem>
                      <SelectItem value="32">32</SelectItem>
                      <SelectItem value="64">64</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="epochs">Epochs</Label>
                  <Input
                    id="epochs"
                    type="number"
                    min="1"
                    max="10"
                    value={settings.hyperparameters.epochs}
                    onChange={(e) => updateNestedSettings('hyperparameters', 'epochs', parseInt(e.target.value))}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="validation-split">Validation Split</Label>
                  <Input
                    id="validation-split"
                    type="number"
                    step="0.1"
                    min="0.1"
                    max="0.5"
                    value={settings.hyperparameters.validationSplit}
                    onChange={(e) => updateNestedSettings('hyperparameters', 'validationSplit', parseFloat(e.target.value))}
                  />
                </div>
              </div>
            </div>

            <Separator />

            {/* Advanced Options */}
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <Zap className="w-4 h-4 text-ml-success" />
                <h4 className="font-semibold">Advanced Options</h4>
              </div>
              
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <Label htmlFor="use-gpu">Use GPU Acceleration</Label>
                    <p className="text-xs text-muted-foreground">Enable NVIDIA GPU for faster training</p>
                  </div>
                  <Switch
                    id="use-gpu"
                    checked={settings.advanced.useGpu}
                    onCheckedChange={(checked) => updateNestedSettings('advanced', 'useGpu', checked)}
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <Label htmlFor="enable-unsloth">Enable Unsloth</Label>
                    <p className="text-xs text-muted-foreground">Use Unsloth for 2x faster training</p>
                  </div>
                  <Switch
                    id="enable-unsloth"
                    checked={settings.advanced.enableUnsloth}
                    onCheckedChange={(checked) => updateNestedSettings('advanced', 'enableUnsloth', checked)}
                  />
                </div>
              </div>
            </div>
          </>
        )}

        {/* LLM Configuration (for LLM inference) */}
        {settings.trainingMethod === 'llm' && (
          <>
            <Separator />
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <Brain className="w-4 h-4 text-ml-secondary" />
                <h4 className="font-semibold">LLM Configuration</h4>
              </div>

              {/* Provider Selection */}
              <div className="space-y-2">
                <Label htmlFor="provider">AI Model Provider</Label>
                <Select
                  value={settings.llmConfig?.provider || ""}
                  onValueChange={handleProviderChange}
                  disabled={loadingProviders}
                >
                  <SelectTrigger>
                    <SelectValue placeholder={loadingProviders ? "Loading providers..." : "Select a provider"} />
                  </SelectTrigger>
                  <SelectContent>
                    {providers?.providers.map((provider) => (
                      <SelectItem key={provider} value={provider}>
                        <div className="flex items-center gap-2">
                          <span className="capitalize">{provider}</span>
                          <Badge
                            variant={getProviderStatus(provider) === "available" ? "default" : "destructive"}
                            className="text-xs"
                          >
                            {getProviderStatus(provider) === "available" ? "Ready" : "API Key Missing"}
                          </Badge>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {loadingProviders && (
                  <div className="flex items-center space-x-2 text-xs text-muted-foreground">
                    <Loader2 className="w-3 h-3 animate-spin" />
                    <span>Loading providers...</span>
                  </div>
                )}
              </div>

              {/* Model Selection */}
              <div className="space-y-2">
                <Label htmlFor="model">Model</Label>
                <Select
                  value={settings.llmConfig?.model || ""}
                  onValueChange={handleModelChange}
                  disabled={!settings.llmConfig?.provider || loadingModels}
                >
                  <SelectTrigger>
                    <SelectValue placeholder={loadingModels ? "Loading models..." : "Select a model"} />
                  </SelectTrigger>
                  <SelectContent>
                    {models.map((model) => (
                      <SelectItem key={model} value={model}>
                        {model}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {loadingModels && (
                  <div className="flex items-center space-x-2 text-xs text-muted-foreground">
                    <Loader2 className="w-3 h-3 animate-spin" />
                    <span>Loading available models...</span>
                  </div>
                )}
              </div>

              {/* Advanced LLM Settings */}
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="temperature">Temperature</Label>
                  <Select
                    value={settings.llmConfig?.temperature?.toString() || "0.1"}
                    onValueChange={(value) => updateSettings('llmConfig', {
                      ...settings.llmConfig,
                      temperature: parseFloat(value)
                    })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="0.0">0.0 (Deterministic)</SelectItem>
                      <SelectItem value="0.1">0.1 (Very Low)</SelectItem>
                      <SelectItem value="0.3">0.3 (Low)</SelectItem>
                      <SelectItem value="0.5">0.5 (Medium)</SelectItem>
                      <SelectItem value="0.7">0.7 (High)</SelectItem>
                      <SelectItem value="1.0">1.0 (Very High)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="max-tokens">Max Tokens</Label>
                  <Select
                    value={settings.llmConfig?.maxTokens?.toString() || "100"}
                    onValueChange={(value) => updateSettings('llmConfig', {
                      ...settings.llmConfig,
                      maxTokens: parseInt(value)
                    })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="50">50 (Short)</SelectItem>
                      <SelectItem value="100">100 (Standard)</SelectItem>
                      <SelectItem value="200">200 (Long)</SelectItem>
                      <SelectItem value="500">500 (Very Long)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="custom-prompt">Custom Prompt (Optional)</Label>
                <textarea
                  id="custom-prompt"
                  className="w-full p-3 border rounded-md resize-none"
                  rows={3}
                  placeholder="Enter custom classification prompt..."
                  value={settings.advanced.customPrompt}
                  onChange={(e) => updateNestedSettings('advanced', 'customPrompt', e.target.value)}
                />
                <p className="text-xs text-muted-foreground">
                  Leave empty to use our optimized prompts for your classification type
                </p>
              </div>
            </div>
          </>
        )}

        {/* Action Buttons */}
        <div className="flex gap-3 pt-4">
          <Button onClick={() => onSave(settings)} className="flex-1">
            <CheckCircle2 className="w-4 h-4 mr-2" />
            Apply Settings
          </Button>
          <Button variant="outline" onClick={onCancel}>
            Cancel
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};
