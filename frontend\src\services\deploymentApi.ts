/**
 * Deployment API Service for ClassyWeb ML Platform
 * 
 * Provides comprehensive deployment functionality for trained models,
 * including local export, API generation, cloud deployment, and integration management.
 */

import apiClient from './apiClient';

// --- Types ---

export interface DeploymentRequest {
  model_id: string;
  deployment_type: 'local' | 'api' | 'batch' | 'cloud' | 'edge';
  config?: Record<string, any>;
  name?: string;
  description?: string;
}

export interface ExportRequest {
  model_id: string;
  format?: 'pytorch' | 'onnx' | 'huggingface' | 'tensorflow';
  include_tokenizer?: boolean;
  include_metadata?: boolean;
  compression?: 'zip' | 'tar' | 'none';
}

export interface APIDeploymentConfig {
  name: string;
  version?: string;
  description: string;
  enable_auth?: boolean;
  enable_rate_limit?: boolean;
  max_requests_per_minute?: number;
}

export interface DeploymentResponse {
  deployment_id: string;
  model_id: string;
  deployment_type: string;
  status: string;
  endpoint?: string;
  download_url?: string;
  api_key?: string;
  created_at: string;
  config: Record<string, any>;
}

export interface DeploymentStatus {
  deployment_id: string;
  status: string;
  progress: number;
  message: string;
  endpoint?: string;
  error?: string;
}

export interface IntegrationCodeResponse {
  python_code: string;
  javascript_code: string;
  curl_code: string;
  documentation_url: string;
}

export interface DeploymentOption {
  title: string;
  description: string;
  available: boolean;
  requires_license?: string;
  estimated_time: string;
}

export interface DeploymentOptionsResponse {
  user_license: string;
  options: Record<string, DeploymentOption>;
}

// --- API Functions ---

/**
 * Deploy a trained model using the specified deployment type
 */
export const deployModel = async (request: DeploymentRequest): Promise<DeploymentResponse> => {
  try {
    const response = await apiClient.post('/api/deployment/deploy', request);
    return response.data;
  } catch (error: any) {
    console.error('Failed to deploy model:', error);
    throw new Error(error.response?.data?.detail || 'Failed to deploy model');
  }
};

/**
 * Export a trained model for download
 */
export const exportModel = async (request: ExportRequest): Promise<{
  download_url: string;
  expires_at: string;
  file_size: string;
  format: string;
}> => {
  try {
    const response = await apiClient.post('/api/deployment/export', request);
    return response.data;
  } catch (error: any) {
    console.error('Failed to export model:', error);
    throw new Error(error.response?.data?.detail || 'Failed to export model');
  }
};

/**
 * List all deployments for the current user
 */
export const listDeployments = async (): Promise<DeploymentResponse[]> => {
  try {
    const response = await apiClient.get('/api/deployment/deployments');
    return response.data;
  } catch (error: any) {
    console.error('Failed to list deployments:', error);
    throw new Error(error.response?.data?.detail || 'Failed to list deployments');
  }
};

/**
 * Get the status of a specific deployment
 */
export const getDeploymentStatus = async (deploymentId: string): Promise<DeploymentStatus> => {
  try {
    const response = await apiClient.get(`/api/deployment/deployments/${deploymentId}/status`);
    return response.data;
  } catch (error: any) {
    console.error('Failed to get deployment status:', error);
    throw new Error(error.response?.data?.detail || 'Failed to get deployment status');
  }
};

/**
 * Delete a deployment
 */
export const deleteDeployment = async (deploymentId: string): Promise<void> => {
  try {
    await apiClient.delete(`/api/deployment/deployments/${deploymentId}`);
  } catch (error: any) {
    console.error('Failed to delete deployment:', error);
    throw new Error(error.response?.data?.detail || 'Failed to delete deployment');
  }
};

/**
 * Generate integration code examples for a deployment
 */
export const getIntegrationCode = async (deploymentId: string): Promise<IntegrationCodeResponse> => {
  try {
    const response = await apiClient.get(`/api/deployment/deployments/${deploymentId}/integration-code`);
    return response.data;
  } catch (error: any) {
    console.error('Failed to get integration code:', error);
    throw new Error(error.response?.data?.detail || 'Failed to get integration code');
  }
};

/**
 * Test a deployment with sample data
 */
export const testDeployment = async (
  deploymentId: string,
  testData: Record<string, any>
): Promise<{
  deployment_id: string;
  test_status: string;
  response_time_ms: number;
  prediction: string;
  confidence: number;
  hierarchy_path: string[];
  probabilities: Record<string, number>;
}> => {
  try {
    const response = await apiClient.post(`/api/deployment/deployments/${deploymentId}/test`, testData);
    return response.data;
  } catch (error: any) {
    console.error('Failed to test deployment:', error);
    throw new Error(error.response?.data?.detail || 'Failed to test deployment');
  }
};

/**
 * Get available deployment options based on user license
 */
export const getDeploymentOptions = async (): Promise<DeploymentOptionsResponse> => {
  try {
    const response = await apiClient.get('/api/deployment/deployment-options');
    return response.data;
  } catch (error: any) {
    console.error('Failed to get deployment options:', error);
    throw new Error(error.response?.data?.detail || 'Failed to get deployment options');
  }
};

/**
 * Download model export file
 */
export const downloadModelExport = async (downloadUrl: string, filename?: string): Promise<void> => {
  try {
    const response = await fetch(downloadUrl);
    
    if (!response.ok) {
      throw new Error(`Download failed: ${response.statusText}`);
    }
    
    const blob = await response.blob();
    
    // Get filename from response headers or use provided filename
    const contentDisposition = response.headers.get('content-disposition');
    let downloadFilename = filename || 'model_export.zip';
    
    if (contentDisposition) {
      const filenameMatch = contentDisposition.match(/filename="?([^"]+)"?/);
      if (filenameMatch) {
        downloadFilename = filenameMatch[1];
      }
    }
    
    // Create blob URL and trigger download
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = downloadFilename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  } catch (error: any) {
    console.error('Failed to download model export:', error);
    throw new Error('Failed to download model export');
  }
};

/**
 * Generate API deployment configuration
 */
export const generateAPIConfig = (
  modelName: string,
  hierarchyLevels: Array<{ name: string; column: string; order: number }>
): APIDeploymentConfig => {
  return {
    name: modelName.replace(/\s+/g, '-').toLowerCase(),
    version: '1.0.0',
    description: `Hierarchical classification API for ${modelName} with ${hierarchyLevels.length} levels`,
    enable_auth: true,
    enable_rate_limit: true,
    max_requests_per_minute: 100
  };
};

/**
 * Validate deployment configuration
 */
export const validateDeploymentConfig = (
  deploymentType: string,
  config: Record<string, any>
): { valid: boolean; errors: string[] } => {
  const errors: string[] = [];
  
  if (deploymentType === 'api') {
    if (!config.name || config.name.trim() === '') {
      errors.push('API name is required');
    }
    
    if (config.name && !/^[a-z0-9-]+$/.test(config.name)) {
      errors.push('API name must contain only lowercase letters, numbers, and hyphens');
    }
    
    if (!config.description || config.description.trim() === '') {
      errors.push('API description is required');
    }
    
    if (config.max_requests_per_minute && (config.max_requests_per_minute < 1 || config.max_requests_per_minute > 10000)) {
      errors.push('Rate limit must be between 1 and 10,000 requests per minute');
    }
  }
  
  if (deploymentType === 'local') {
    if (!config.format) {
      errors.push('Export format is required');
    }
    
    if (config.format && !['pytorch', 'onnx', 'huggingface', 'tensorflow'].includes(config.format)) {
      errors.push('Invalid export format');
    }
  }
  
  return {
    valid: errors.length === 0,
    errors
  };
};

/**
 * Get deployment type display information
 */
export const getDeploymentTypeInfo = (deploymentType: string) => {
  const typeInfo = {
    local: {
      title: 'Local Model Export',
      description: 'Download trained model files for offline use',
      icon: 'Download',
      color: 'blue'
    },
    api: {
      title: 'API Endpoint',
      description: 'Generate REST API for real-time inference',
      icon: 'Server',
      color: 'green'
    },
    batch: {
      title: 'Batch Processing',
      description: 'Setup for large-scale batch classification',
      icon: 'Database',
      color: 'purple'
    },
    cloud: {
      title: 'Cloud Deployment',
      description: 'Deploy to AWS/GCP/Azure with auto-scaling',
      icon: 'Cloud',
      color: 'orange'
    },
    edge: {
      title: 'Edge Deployment',
      description: 'Optimize for mobile and edge devices',
      icon: 'Smartphone',
      color: 'pink'
    }
  };
  
  return typeInfo[deploymentType as keyof typeof typeInfo] || {
    title: 'Unknown Deployment',
    description: 'Unknown deployment type',
    icon: 'HelpCircle',
    color: 'gray'
  };
};

// Export all functions as default
export default {
  deployModel,
  exportModel,
  listDeployments,
  getDeploymentStatus,
  deleteDeployment,
  getIntegrationCode,
  testDeployment,
  getDeploymentOptions,
  downloadModelExport,
  generateAPIConfig,
  validateDeploymentConfig,
  getDeploymentTypeInfo
};
