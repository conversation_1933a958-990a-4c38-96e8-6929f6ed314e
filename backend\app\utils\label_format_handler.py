"""
Label Format Handler for Multi-Class Classification

Handles conversion between different label formats:
- Single label column (traditional format)
- Multiple binary columns (one-hot encoded format)
"""

import pandas as pd
import numpy as np
from typing import List, Tuple, Dict, Any, Optional
import logging

logger = logging.getLogger(__name__)

class LabelFormatHandler:
    """Handles conversion between different label formats for multi-class classification"""
    
    @staticmethod
    def detect_format(df: pd.DataFrame, label_columns: List[str]) -> str:
        """Auto-detect label format from data"""
        if len(label_columns) == 1:
            return 'single'
        
        # Check if columns contain binary values
        for col in label_columns:
            if col not in df.columns:
                continue
            unique_vals = df[col].dropna().unique()
            if not all(val in [0, 1, '0', '1', True, False, 'true', 'false'] for val in unique_vals):
                logger.warning(f"Column {col} doesn't contain binary values: {unique_vals}")
                return 'single'  # Fallback to single format
        
        return 'multiple'
    
    @staticmethod
    def validate_binary_format(df: pd.DataFrame, binary_columns: List[str]) -> Dict[str, Any]:
        """Validate binary format data"""
        issues = []
        stats = {}
        
        # Check column existence
        missing_cols = [col for col in binary_columns if col not in df.columns]
        if missing_cols:
            issues.append(f"Missing columns: {missing_cols}")
            return {"valid": False, "issues": issues, "stats": stats}
        
        # Check binary values
        for col in binary_columns:
            unique_vals = df[col].dropna().unique()
            valid_vals = [0, 1, '0', '1', True, False, 'true', 'false']
            invalid_vals = [val for val in unique_vals if val not in valid_vals]
            if invalid_vals:
                issues.append(f"Column {col} has invalid values: {invalid_vals}")
        
        # Check one-hot encoding (exactly one 1 per row)
        binary_df = df[binary_columns].copy()
        # Convert to numeric
        for col in binary_columns:
            binary_df[col] = pd.to_numeric(binary_df[col], errors='coerce')
        
        row_sums = binary_df.sum(axis=1)
        
        # Rows with no active class
        no_class_rows = (row_sums == 0).sum()
        # Rows with multiple active classes
        multi_class_rows = (row_sums > 1).sum()
        
        if no_class_rows > 0:
            issues.append(f"{no_class_rows} rows have no active class")
        
        if multi_class_rows > 0:
            issues.append(f"{multi_class_rows} rows have multiple active classes (multi-label detected)")
        
        # Calculate class distribution
        class_distribution = {}
        for col in binary_columns:
            class_distribution[col] = int(binary_df[col].sum())
        
        stats = {
            "num_classes": len(binary_columns),
            "class_names": binary_columns,
            "class_distribution": class_distribution,
            "no_class_rows": int(no_class_rows),
            "multi_class_rows": int(multi_class_rows),
            "total_samples": len(df)
        }
        
        return {
            "valid": len(issues) == 0,
            "issues": issues,
            "stats": stats
        }
    
    @staticmethod
    def validate_single_format(df: pd.DataFrame, label_column: str) -> Dict[str, Any]:
        """Validate single label format data"""
        issues = []
        stats = {}
        
        # Check column existence
        if label_column not in df.columns:
            issues.append(f"Label column '{label_column}' not found")
            return {"valid": False, "issues": issues, "stats": stats}
        
        # Check for empty labels
        empty_labels = df[label_column].isna().sum()
        if empty_labels > 0:
            issues.append(f"{empty_labels} rows have empty labels")
        
        # Get unique classes
        unique_classes = df[label_column].dropna().unique().tolist()
        
        # Check minimum classes
        if len(unique_classes) < 2:
            issues.append(f"Need at least 2 classes for classification, found {len(unique_classes)}")
        
        # Calculate class distribution
        class_distribution = df[label_column].value_counts().to_dict()
        
        stats = {
            "num_classes": len(unique_classes),
            "class_names": unique_classes,
            "class_distribution": class_distribution,
            "empty_labels": int(empty_labels),
            "total_samples": len(df)
        }
        
        return {
            "valid": len(issues) == 0,
            "issues": issues,
            "stats": stats
        }
    
    @staticmethod
    def convert_binary_to_single(df: pd.DataFrame, binary_columns: List[str], 
                                target_column: str = 'category') -> Tuple[pd.DataFrame, List[str]]:
        """Convert binary columns to single label column"""
        logger.info(f"Converting binary columns {binary_columns} to single column '{target_column}'")
        
        df_converted = df.copy()
        
        # Convert binary columns to numeric
        binary_df = df_converted[binary_columns].copy()
        for col in binary_columns:
            binary_df[col] = pd.to_numeric(binary_df[col], errors='coerce').fillna(0)
        
        # Find the active class for each row
        def get_active_class(row):
            active_classes = [col for col in binary_columns if row[col] == 1]
            if len(active_classes) == 1:
                return active_classes[0]
            elif len(active_classes) == 0:
                return 'no_class'
            else:
                return 'multi_class'  # Multiple active classes
        
        df_converted[target_column] = binary_df.apply(get_active_class, axis=1)
        
        # Remove original binary columns
        df_converted = df_converted.drop(columns=binary_columns)
        
        # Get unique classes (excluding error cases)
        unique_classes = [cls for cls in df_converted[target_column].unique() 
                         if cls not in ['no_class', 'multi_class']]
        
        # Filter out problematic rows
        valid_mask = ~df_converted[target_column].isin(['no_class', 'multi_class'])
        problematic_rows = (~valid_mask).sum()
        
        if problematic_rows > 0:
            logger.warning(f"Filtered out {problematic_rows} problematic rows")
        
        df_converted = df_converted[valid_mask].reset_index(drop=True)
        
        logger.info(f"Converted to single format: {len(unique_classes)} classes, {len(df_converted)} valid samples")
        
        return df_converted, unique_classes
    
    @staticmethod
    def convert_single_to_binary(df: pd.DataFrame, label_column: str, 
                               class_names: List[str] = None) -> Tuple[pd.DataFrame, List[str]]:
        """Convert single label column to binary columns"""
        df_converted = df.copy()
        
        if class_names is None:
            class_names = sorted(df[label_column].dropna().unique().tolist())
        
        logger.info(f"Converting single column '{label_column}' to binary columns: {class_names}")
        
        # Create binary columns
        for class_name in class_names:
            df_converted[class_name] = (df_converted[label_column] == class_name).astype(int)
        
        # Remove original label column
        df_converted = df_converted.drop(columns=[label_column])
        
        return df_converted, class_names
    
    @staticmethod
    def normalize_for_training(df: pd.DataFrame, text_column: str, label_columns: List[str], 
                             label_format: str = None) -> Tuple[pd.DataFrame, str, List[str], Dict[str, Any]]:
        """Normalize data to single label format for training"""
        
        # Auto-detect format if not specified
        if label_format is None:
            label_format = LabelFormatHandler.detect_format(df, label_columns)
        
        logger.info(f"Normalizing data with format: {label_format}")
        
        if label_format == 'single':
            # Already in single format
            if len(label_columns) != 1:
                raise ValueError(f"Single format requires exactly one label column, got {len(label_columns)}")
            
            label_column = label_columns[0]
            
            # Validate single format
            validation = LabelFormatHandler.validate_single_format(df, label_column)
            if not validation["valid"]:
                raise ValueError(f"Single format validation failed: {validation['issues']}")
            
            class_names = sorted(df[label_column].dropna().unique().tolist())
            
            logger.info(f"Using single format: column '{label_column}', classes: {class_names}")
            
            format_info = {
                'original_format': 'single',
                'original_columns': label_columns,
                'normalized_column': label_column,
                'class_names': class_names,
                'validation_stats': validation['stats']
            }
            
            return df, label_column, class_names, format_info
        
        elif label_format == 'multiple':
            # Convert binary to single format
            validation = LabelFormatHandler.validate_binary_format(df, label_columns)
            if not validation["valid"]:
                raise ValueError(f"Binary format validation failed: {validation['issues']}")
            
            # Convert to single format for training
            target_column = 'category'
            df_converted, class_names = LabelFormatHandler.convert_binary_to_single(
                df, label_columns, target_column
            )
            
            logger.info(f"Converted binary to single format: column '{target_column}', classes: {class_names}")
            
            format_info = {
                'original_format': 'multiple',
                'original_columns': label_columns,
                'normalized_column': target_column,
                'class_names': class_names,
                'validation_stats': validation['stats']
            }
            
            return df_converted, target_column, class_names, format_info
        
        else:
            raise ValueError(f"Unsupported label format: {label_format}")
    
    @staticmethod
    def prepare_inference_data(df: pd.DataFrame, text_column: str, 
                             format_info: Dict[str, Any]) -> pd.DataFrame:
        """Prepare data for inference based on original format"""
        
        original_format = format_info.get('original_format')
        original_columns = format_info.get('original_columns', [])
        
        if original_format == 'multiple':
            # For binary format inference, we might need to convert results back
            # This is handled in the inference pipeline
            pass
        
        # For now, just return the dataframe as-is
        # The inference pipeline will handle format-specific processing
        return df
