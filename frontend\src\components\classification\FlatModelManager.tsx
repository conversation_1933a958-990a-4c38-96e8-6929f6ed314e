/**
 * FlatModelManager.tsx
 *
 * Model management component for flat classification models.
 * Features scalability metadata, performance metrics, and optimization recommendations.
 */

import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useToast } from "@/hooks/use-toast";
import {
  Brain,
  Calendar,
  MoreVertical,
  Play,
  Trash2,
  Download,
  Eye,
  Database,
  Zap,
  BarChart3,
  AlertCircle,
  Info,
  Loader2,
  HardDrive,
  Cpu
} from "lucide-react";
import { formatDistanceToNow } from 'date-fns';

export interface FlatModel {
  id: string;
  name: string;
  baseModel: string;
  scalabilityMode: 'standard' | 'large_dataset' | 'streaming';
  
  // Performance metrics
  accuracy: number;
  f1Score: number;
  precision: number;
  recall: number;
  
  // Scalability metrics
  scalabilityMetrics: {
    maxDatasetSize: number;
    avgProcessingTime: number;
    memoryUsage: number;
    throughput: number; // samples per second
    optimizationLevel: string;
  };
  
  // Training metadata
  trainingTime: number;
  modelSize: number;
  createdAt: Date;
  lastUsed?: Date;
  
  // Configuration
  trainingConfig: {
    epochs: number;
    batchSize: number;
    learningRate: number;
    chunkSize: number;
    parallelProcessing: boolean;
    memoryOptimization: boolean;
  };
  
  status: 'training' | 'completed' | 'failed' | 'deployed';
}

interface FlatModelManagerProps {
  onModelSelect: (model: FlatModel) => void;
  onModelUse: (modelId: string) => void;
  onModelDelete: (modelId: string) => void;
  selectedModelId?: string;
  showActions?: boolean;
}

export const FlatModelManager: React.FC<FlatModelManagerProps> = ({
  onModelSelect,
  onModelUse,
  onModelDelete,
  selectedModelId,
  showActions = true
}) => {
  const { toast } = useToast();
  const [models, setModels] = useState<FlatModel[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadModels();
  }, []);

  const loadModels = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Mock data for now - in real implementation, this would call an API
      const mockModels: FlatModel[] = [
        {
          id: 'flat-model-1',
          name: 'Large Scale Text Classifier',
          baseModel: 'distilbert-base-uncased',
          scalabilityMode: 'large_dataset',
          accuracy: 0.89,
          f1Score: 0.87,
          precision: 0.88,
          recall: 0.86,
          scalabilityMetrics: {
            maxDatasetSize: 500000,
            avgProcessingTime: 0.02, // seconds per sample
            memoryUsage: 2.1, // GB
            throughput: 50, // samples per second
            optimizationLevel: 'high'
          },
          trainingTime: 3600,
          modelSize: 267,
          createdAt: new Date('2024-01-15'),
          lastUsed: new Date('2024-01-20'),
          trainingConfig: {
            epochs: 3,
            batchSize: 8,
            learningRate: 2e-5,
            chunkSize: 50000,
            parallelProcessing: true,
            memoryOptimization: true
          },
          status: 'completed'
        },
        {
          id: 'flat-model-2',
          name: 'Standard Document Classifier',
          baseModel: 'bert-base-uncased',
          scalabilityMode: 'standard',
          accuracy: 0.92,
          f1Score: 0.90,
          precision: 0.91,
          recall: 0.89,
          scalabilityMetrics: {
            maxDatasetSize: 50000,
            avgProcessingTime: 0.05,
            memoryUsage: 1.2,
            throughput: 20,
            optimizationLevel: 'medium'
          },
          trainingTime: 1800,
          modelSize: 438,
          createdAt: new Date('2024-01-10'),
          trainingConfig: {
            epochs: 5,
            batchSize: 16,
            learningRate: 1e-5,
            chunkSize: 10000,
            parallelProcessing: false,
            memoryOptimization: false
          },
          status: 'completed'
        }
      ];

      setModels(mockModels);
    } catch (err: any) {
      console.error('Failed to load models:', err);
      setError(err.message || 'Failed to load flat classification models');
      toast({
        title: "Error loading models",
        description: err.message || 'Failed to load flat classification models',
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteModel = async (modelId: string) => {
    try {
      // In real implementation, this would call an API
      setModels(prev => prev.filter(model => model.id !== modelId));
      onModelDelete(modelId);
      
      toast({
        title: "Model deleted",
        description: "Flat classification model has been deleted successfully"
      });
    } catch (error: any) {
      toast({
        title: "Delete failed",
        description: error.message || "Failed to delete model",
        variant: "destructive"
      });
    }
  };

  const handleUseModel = (modelId: string) => {
    const model = models.find(m => m.id === modelId);
    if (model) {
      // Update last used timestamp
      setModels(prev => prev.map(m => 
        m.id === modelId 
          ? { ...m, lastUsed: new Date() }
          : m
      ));
      onModelUse(modelId);
      
      toast({
        title: "Model selected",
        description: `Using ${model.name} for classification`
      });
    }
  };

  const getScalabilityBadgeColor = (mode: string) => {
    switch (mode) {
      case 'standard': return 'default';
      case 'large_dataset': return 'secondary';
      case 'streaming': return 'outline';
      default: return 'default';
    }
  };

  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case 'completed': return 'default';
      case 'training': return 'secondary';
      case 'failed': return 'destructive';
      case 'deployed': return 'outline';
      default: return 'default';
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <div className="flex items-center gap-2">
            <Loader2 className="w-4 h-4 animate-spin" />
            <span>Loading flat classification models...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          {error}
          <Button
            variant="outline"
            size="sm"
            className="ml-2"
            onClick={loadModels}
          >
            Retry
          </Button>
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold flex items-center gap-2">
            <Brain className="w-5 h-5" />
            Flat Classification Model Manager
          </h3>
          <p className="text-sm text-muted-foreground">
            Manage and reuse your trained flat classification models
          </p>
        </div>
        <Button variant="outline" onClick={loadModels}>
          Refresh
        </Button>
      </div>

      {/* Models List */}
      {models.length === 0 ? (
        <Alert>
          <Info className="h-4 w-4" />
          <AlertDescription>
            No flat classification models found. Train your first model to see it here.
          </AlertDescription>
        </Alert>
      ) : (
        <Card>
          <CardHeader>
            <CardTitle>Available Models ({models.length})</CardTitle>
            <CardDescription>
              Select a model to reuse or manage your trained models
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Model</TableHead>
                  <TableHead>Scalability</TableHead>
                  <TableHead>Performance</TableHead>
                  <TableHead>Throughput</TableHead>
                  <TableHead>Created</TableHead>
                  <TableHead>Status</TableHead>
                  {showActions && <TableHead>Actions</TableHead>}
                </TableRow>
              </TableHeader>
              <TableBody>
                {models.map((model) => (
                  <TableRow
                    key={model.id}
                    className={`cursor-pointer ${
                      selectedModelId === model.id ? 'bg-muted/50' : ''
                    }`}
                    onClick={() => onModelSelect(model)}
                  >
                    <TableCell>
                      <div className="space-y-1">
                        <div className="font-medium">{model.name}</div>
                        <div className="text-xs text-muted-foreground">
                          {model.baseModel}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant={getScalabilityBadgeColor(model.scalabilityMode)}>
                        {model.scalabilityMode.replace('_', ' ').toUpperCase()}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="space-y-1">
                        <div className="text-sm">Acc: {(model.accuracy * 100).toFixed(1)}%</div>
                        <div className="text-xs text-muted-foreground">
                          F1: {(model.f1Score * 100).toFixed(1)}%
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="space-y-1">
                        <div className="text-sm">{model.scalabilityMetrics.throughput}/s</div>
                        <div className="text-xs text-muted-foreground">
                          {model.scalabilityMetrics.memoryUsage.toFixed(1)}GB
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">
                        {formatDistanceToNow(model.createdAt, { addSuffix: true })}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant={getStatusBadgeColor(model.status)}>
                        {model.status}
                      </Badge>
                    </TableCell>
                    {showActions && (
                      <TableCell>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm">
                              <MoreVertical className="w-4 h-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => handleUseModel(model.id)}>
                              <Play className="w-4 h-4 mr-2" />
                              Use Model
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <Eye className="w-4 h-4 mr-2" />
                              View Details
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <Download className="w-4 h-4 mr-2" />
                              Export Model
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              onClick={() => handleDeleteModel(model.id)}
                              className="text-destructive"
                            >
                              <Trash2 className="w-4 h-4 mr-2" />
                              Delete
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    )}
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
