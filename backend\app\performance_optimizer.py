"""Performance Optimization Utilities for ClassyWeb ML Platform Phase 2.

This module provides performance optimization tools including memory management,
GPU optimization, batch processing optimization, and caching strategies.
"""

import logging
import time
import gc
import psutil
import torch
from typing import Dict, Any, Optional, List, Tuple
from functools import wraps
from dataclasses import dataclass
import numpy as np
from concurrent.futures import ThreadPoolExecutor
import asyncio

logger = logging.getLogger(__name__)


@dataclass
class PerformanceMetrics:
    """Performance metrics for optimization tracking."""
    execution_time: float
    memory_usage_mb: float
    gpu_memory_mb: float
    cpu_utilization: float
    throughput: float
    cache_hit_rate: Optional[float] = None


class MemoryManager:
    """Advanced memory management for training and inference."""
    
    def __init__(self):
        self.memory_threshold_mb = 1024  # 1GB threshold
        self.cleanup_enabled = True
        
    def get_memory_usage(self) -> Dict[str, float]:
        """Get current memory usage statistics."""
        process = psutil.Process()
        memory_info = process.memory_info()
        
        stats = {
            'rss_mb': memory_info.rss / 1024 / 1024,
            'vms_mb': memory_info.vms / 1024 / 1024,
            'percent': process.memory_percent(),
            'available_mb': psutil.virtual_memory().available / 1024 / 1024
        }
        
        # Add GPU memory if available
        if torch.cuda.is_available():
            stats['gpu_allocated_mb'] = torch.cuda.memory_allocated() / 1024 / 1024
            stats['gpu_reserved_mb'] = torch.cuda.memory_reserved() / 1024 / 1024
            stats['gpu_max_allocated_mb'] = torch.cuda.max_memory_allocated() / 1024 / 1024
        
        return stats
    
    def cleanup_memory(self, force_gpu_cleanup: bool = False):
        """Perform memory cleanup operations."""
        if not self.cleanup_enabled:
            return
        
        # Python garbage collection
        collected = gc.collect()
        
        # GPU memory cleanup
        if torch.cuda.is_available() and force_gpu_cleanup:
            torch.cuda.empty_cache()
            torch.cuda.synchronize()
        
        logger.debug(f"Memory cleanup: collected {collected} objects")
    
    def monitor_memory_usage(self, threshold_mb: Optional[float] = None) -> bool:
        """Monitor memory usage and trigger cleanup if needed."""
        threshold = threshold_mb or self.memory_threshold_mb
        current_usage = self.get_memory_usage()
        
        if current_usage['rss_mb'] > threshold:
            logger.warning(f"High memory usage detected: {current_usage['rss_mb']:.1f}MB")
            self.cleanup_memory(force_gpu_cleanup=True)
            return True
        
        return False
    
    def memory_efficient_batch_processing(self, data: List[Any], batch_size: int, process_func: callable) -> List[Any]:
        """Process data in memory-efficient batches."""
        results = []
        
        for i in range(0, len(data), batch_size):
            batch = data[i:i + batch_size]
            
            # Process batch
            batch_results = process_func(batch)
            results.extend(batch_results)
            
            # Monitor memory after each batch
            if self.monitor_memory_usage():
                logger.info(f"Memory cleanup triggered after batch {i // batch_size + 1}")
        
        return results


class GPUOptimizer:
    """GPU optimization utilities for training and inference."""
    
    def __init__(self):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.mixed_precision_enabled = torch.cuda.is_available()
        
    def optimize_model_for_inference(self, model: torch.nn.Module) -> torch.nn.Module:
        """Optimize model for inference performance."""
        model.eval()
        
        # Move to appropriate device
        model = model.to(self.device)
        
        # Enable inference optimizations
        if hasattr(torch, 'jit') and self.device.type == 'cuda':
            try:
                # Try to script the model for better performance
                model = torch.jit.script(model)
                logger.info("Model successfully scripted for inference optimization")
            except Exception as e:
                logger.warning(f"Failed to script model: {e}")
        
        # Set to inference mode
        if hasattr(model, 'eval'):
            model.eval()
        
        return model
    
    def get_optimal_batch_size(self, model: torch.nn.Module, input_shape: Tuple[int, ...], max_memory_mb: float = 8192) -> int:
        """Determine optimal batch size based on GPU memory."""
        if not torch.cuda.is_available():
            return 16  # Conservative default for CPU
        
        # Start with a small batch size and increase until memory limit
        batch_size = 1
        max_batch_size = 1
        
        model.eval()
        with torch.no_grad():
            while batch_size <= 128:  # Reasonable upper limit
                try:
                    # Create dummy input
                    dummy_input = torch.randn(batch_size, *input_shape[1:]).to(self.device)
                    
                    # Forward pass
                    _ = model(dummy_input)
                    
                    # Check memory usage
                    memory_used = torch.cuda.memory_allocated() / 1024 / 1024
                    if memory_used > max_memory_mb:
                        break
                    
                    max_batch_size = batch_size
                    batch_size *= 2
                    
                except RuntimeError as e:
                    if "out of memory" in str(e):
                        break
                    raise e
                finally:
                    torch.cuda.empty_cache()
        
        logger.info(f"Optimal batch size determined: {max_batch_size}")
        return max_batch_size
    
    def enable_mixed_precision(self) -> bool:
        """Enable mixed precision training if supported."""
        if torch.cuda.is_available() and hasattr(torch.cuda, 'amp'):
            self.mixed_precision_enabled = True
            logger.info("Mixed precision training enabled")
            return True
        return False


class CacheManager:
    """Intelligent caching system for model predictions and data."""
    
    def __init__(self, max_size: int = 1000):
        self.cache: Dict[str, Any] = {}
        self.access_times: Dict[str, float] = {}
        self.max_size = max_size
        self.hit_count = 0
        self.miss_count = 0
    
    def _generate_key(self, *args, **kwargs) -> str:
        """Generate cache key from arguments."""
        import hashlib
        key_data = str(args) + str(sorted(kwargs.items()))
        return hashlib.md5(key_data.encode()).hexdigest()
    
    def get(self, key: str) -> Optional[Any]:
        """Get item from cache."""
        if key in self.cache:
            self.access_times[key] = time.time()
            self.hit_count += 1
            return self.cache[key]
        
        self.miss_count += 1
        return None
    
    def put(self, key: str, value: Any):
        """Put item in cache with LRU eviction."""
        if len(self.cache) >= self.max_size:
            # Remove least recently used item
            lru_key = min(self.access_times.keys(), key=lambda k: self.access_times[k])
            del self.cache[lru_key]
            del self.access_times[lru_key]
        
        self.cache[key] = value
        self.access_times[key] = time.time()
    
    def get_hit_rate(self) -> float:
        """Get cache hit rate."""
        total = self.hit_count + self.miss_count
        return self.hit_count / total if total > 0 else 0.0
    
    def clear(self):
        """Clear cache."""
        self.cache.clear()
        self.access_times.clear()
        self.hit_count = 0
        self.miss_count = 0


class PerformanceProfiler:
    """Performance profiling and optimization recommendations."""
    
    def __init__(self):
        self.memory_manager = MemoryManager()
        self.gpu_optimizer = GPUOptimizer()
        self.cache_manager = CacheManager()
        
    def profile_function(self, func: callable):
        """Decorator to profile function performance."""
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Pre-execution metrics
            start_time = time.time()
            start_memory = self.memory_manager.get_memory_usage()
            
            try:
                # Execute function
                result = func(*args, **kwargs)
                
                # Post-execution metrics
                end_time = time.time()
                end_memory = self.memory_manager.get_memory_usage()
                
                # Calculate metrics
                execution_time = end_time - start_time
                memory_delta = end_memory['rss_mb'] - start_memory['rss_mb']
                
                metrics = PerformanceMetrics(
                    execution_time=execution_time,
                    memory_usage_mb=memory_delta,
                    gpu_memory_mb=end_memory.get('gpu_allocated_mb', 0),
                    cpu_utilization=psutil.cpu_percent(),
                    throughput=len(args[0]) / execution_time if args and hasattr(args[0], '__len__') else 0,
                    cache_hit_rate=self.cache_manager.get_hit_rate()
                )
                
                logger.info(f"Performance metrics for {func.__name__}: {metrics}")
                return result
                
            except Exception as e:
                logger.error(f"Error in profiled function {func.__name__}: {e}")
                raise
        
        return wrapper
    
    def optimize_training_config(self, model_size_mb: float, dataset_size: int, available_memory_mb: float) -> Dict[str, Any]:
        """Recommend optimal training configuration."""
        recommendations = {}
        
        # Batch size recommendation
        if available_memory_mb > 8192:  # 8GB+
            recommendations['batch_size'] = min(32, dataset_size // 100)
        elif available_memory_mb > 4096:  # 4GB+
            recommendations['batch_size'] = min(16, dataset_size // 200)
        else:
            recommendations['batch_size'] = min(8, dataset_size // 400)
        
        # Gradient accumulation
        if recommendations['batch_size'] < 16:
            recommendations['gradient_accumulation_steps'] = 16 // recommendations['batch_size']
        else:
            recommendations['gradient_accumulation_steps'] = 1
        
        # Mixed precision
        recommendations['use_fp16'] = torch.cuda.is_available() and available_memory_mb < 8192
        
        # Gradient checkpointing
        recommendations['gradient_checkpointing'] = model_size_mb > 500 or available_memory_mb < 4096
        
        # DataLoader workers
        cpu_count = psutil.cpu_count()
        recommendations['num_workers'] = min(4, cpu_count // 2) if cpu_count > 2 else 0
        
        logger.info(f"Training optimization recommendations: {recommendations}")
        return recommendations
    
    def optimize_inference_config(self, model_size_mb: float, expected_qps: float) -> Dict[str, Any]:
        """Recommend optimal inference configuration."""
        recommendations = {}
        
        # Batch size for inference
        if expected_qps > 100:
            recommendations['inference_batch_size'] = 32
        elif expected_qps > 10:
            recommendations['inference_batch_size'] = 16
        else:
            recommendations['inference_batch_size'] = 8
        
        # Caching strategy
        if expected_qps > 50:
            recommendations['enable_caching'] = True
            recommendations['cache_size'] = min(10000, int(expected_qps * 60))  # 1 minute of requests
        else:
            recommendations['enable_caching'] = False
        
        # Model optimization
        recommendations['use_torchscript'] = model_size_mb < 1000  # Only for smaller models
        recommendations['use_onnx'] = model_size_mb < 500  # ONNX for very small models
        
        logger.info(f"Inference optimization recommendations: {recommendations}")
        return recommendations


class AsyncBatchProcessor:
    """Asynchronous batch processing for improved throughput."""
    
    def __init__(self, max_workers: int = 4):
        self.max_workers = max_workers
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
    
    async def process_batches_async(self, data: List[Any], batch_size: int, process_func: callable) -> List[Any]:
        """Process batches asynchronously for improved throughput."""
        batches = [data[i:i + batch_size] for i in range(0, len(data), batch_size)]
        
        # Create async tasks
        loop = asyncio.get_event_loop()
        tasks = [
            loop.run_in_executor(self.executor, process_func, batch)
            for batch in batches
        ]
        
        # Wait for all tasks to complete
        results = await asyncio.gather(*tasks)
        
        # Flatten results
        flattened_results = []
        for batch_results in results:
            flattened_results.extend(batch_results)
        
        return flattened_results
    
    def __del__(self):
        """Cleanup executor on deletion."""
        if hasattr(self, 'executor'):
            self.executor.shutdown(wait=True)


# Global performance profiler instance
performance_profiler = PerformanceProfiler()

# Convenience decorators
def profile_performance(func):
    """Decorator for performance profiling."""
    return performance_profiler.profile_function(func)

def optimize_memory(threshold_mb: float = 1024):
    """Decorator for memory optimization."""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Monitor memory before execution
            performance_profiler.memory_manager.monitor_memory_usage(threshold_mb)
            
            try:
                result = func(*args, **kwargs)
                return result
            finally:
                # Cleanup after execution
                performance_profiler.memory_manager.cleanup_memory()
        
        return wrapper
    return decorator
