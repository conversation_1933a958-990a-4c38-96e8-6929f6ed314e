#!/usr/bin/env python3
"""
Debug script to check task status directly from the database.

This script helps debug why the frontend is not detecting completed tasks.
"""

import sys
import os
from pathlib import Path

# Add backend to path
backend_dir = Path(__file__).parent / "backend"
sys.path.insert(0, str(backend_dir))

def debug_task_status(task_id: str = None):
    """Debug task status by checking the database directly."""
    print("🔍 Debugging Task Status")
    print("=" * 40)
    
    try:
        # Set environment - use the backend database
        os.environ["DATABASE_URL"] = "sqlite:///./backend/classyweb.db"
        
        # Import after setting environment
        from app.database import SessionLocal, get_task
        
        with SessionLocal() as db:
            if task_id:
                # Check specific task
                task = get_task(db, task_id)
                if task:
                    print(f"Task ID: {task.id}")
                    print(f"Status: {task.status}")
                    print(f"Message: {task.message}")
                    print(f"Result File Path: {task.result_file_path}")
                    print(f"Created: {task.created_at}")
                    print(f"Updated: {task.updated_at}")
                    print(f"Completed: {task.completed_at}")
                    
                    # Check if result file exists
                    if task.result_file_path:
                        result_path = Path(task.result_file_path)
                        print(f"Result file exists: {result_path.exists()}")
                        if result_path.exists():
                            print(f"Result file size: {result_path.stat().st_size} bytes")
                    
                    return task
                else:
                    print(f"❌ Task {task_id} not found")
                    return None
            else:
                # List recent tasks
                from app.database import Task
                recent_tasks = db.query(Task).order_by(Task.created_at.desc()).limit(10).all()
                
                print("Recent Tasks:")
                print("-" * 80)
                for task in recent_tasks:
                    status_icon = {
                        "PENDING": "⏳",
                        "RUNNING": "🔄", 
                        "SUCCESS": "✅",
                        "FAILED": "❌"
                    }.get(task.status, "❓")
                    
                    print(f"{status_icon} {task.id[:8]}... | {task.status:8} | {task.task_type:20} | {task.created_at}")
                    if task.status == "SUCCESS" and task.result_file_path:
                        result_path = Path(task.result_file_path)
                        exists = "✓" if result_path.exists() else "✗"
                        print(f"    Result: {exists} {task.result_file_path}")
                
                return recent_tasks
                
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

def test_task_api(task_id: str):
    """Test the task API endpoint directly."""
    print(f"\n🌐 Testing Task API for {task_id}")
    print("=" * 40)
    
    try:
        import requests
        
        # Test the API endpoint
        response = requests.get(f"http://localhost:8001/tasks/{task_id}")
        
        print(f"Status Code: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            data = response.json()
            print("Response Data:")
            for key, value in data.items():
                print(f"  {key}: {value}")
        else:
            print(f"Error Response: {response.text}")
            
    except Exception as e:
        print(f"❌ API Test Error: {e}")

def main():
    """Main function."""
    print("🚀 Task Status Debug Tool")
    print("=" * 50)
    
    if len(sys.argv) > 1:
        task_id = sys.argv[1]
        print(f"Checking specific task: {task_id}")
        
        # Check database
        task = debug_task_status(task_id)
        
        # Test API if task exists
        if task:
            test_task_api(task_id)
    else:
        print("Listing recent tasks...")
        debug_task_status()
        
        print("\nUsage: python debug_task_status.py <task_id>")
        print("       python debug_task_status.py  (to list recent tasks)")

if __name__ == "__main__":
    main()
