"""Model artifact service for saving and loading trained models."""

import logging
import json
import os
import pickle
from pathlib import Path
from typing import Dict, Any, Optional
import pandas as pd
import torch

from ..config import MODEL_ARTIFACTS_DIR
from ..database import ModelArtifact

logger = logging.getLogger(__name__)

def save_model_artifact(
    artifact: ModelArtifact,
    model: Any,
    tokenizer: Any,
    label_map: Dict[str, int],
    rules_df: pd.DataFrame,
    hrm_model_data: Optional[Dict[str, Any]] = None
) -> bool:
    """Save model artifact to disk."""
    try:
        # Create artifact directory
        artifact_dir = MODEL_ARTIFACTS_DIR / artifact.id
        artifact_dir.mkdir(parents=True, exist_ok=True)
        
        # Save model state dict
        if model is not None:
            model_path = artifact_dir / "model.pt"
            if hasattr(model, 'state_dict'):
                torch.save(model.state_dict(), model_path)
            else:
                # For Unsloth models, save the entire model
                torch.save(model, model_path)
            logger.info(f"Model saved to {model_path}")
        
        # Save tokenizer
        if tokenizer is not None:
            tokenizer_path = artifact_dir / "tokenizer"
            tokenizer.save_pretrained(tokenizer_path)
            logger.info(f"Tokenizer saved to {tokenizer_path}")
        
        # Save label map
        if label_map:
            label_map_path = artifact_dir / "label_map.json"
            with open(label_map_path, 'w') as f:
                json.dump(label_map, f)
            logger.info(f"Label map saved to {label_map_path}")
        
        # Save rules DataFrame
        if rules_df is not None and not rules_df.empty:
            rules_path = artifact_dir / "rules.csv"
            rules_df.to_csv(rules_path, index=False)
            logger.info(f"Rules saved to {rules_path}")
        
        # Save HRM model data if available
        if hrm_model_data:
            hrm_path = artifact_dir / "hrm_model.json"
            with open(hrm_path, 'w') as f:
                json.dump(hrm_model_data, f)
            logger.info(f"HRM model data saved to {hrm_path}")
        
        # Save metadata
        metadata = {
            'artifact_id': artifact.id,
            'model_type': artifact.model_type,
            'has_model': model is not None,
            'has_tokenizer': tokenizer is not None,
            'has_label_map': bool(label_map),
            'has_rules': rules_df is not None and not rules_df.empty,
            'has_hrm': hrm_model_data is not None,
            'parameters': json.loads(artifact.parameters) if artifact.parameters else {},
            'metrics': json.loads(artifact.metrics) if artifact.metrics else {}
        }
        
        metadata_path = artifact_dir / "metadata.json"
        with open(metadata_path, 'w') as f:
            json.dump(metadata, f, indent=2)
        
        logger.info(f"Model artifact {artifact.id} saved successfully")
        return True
        
    except Exception as e:
        logger.error(f"Error saving model artifact {artifact.id}: {e}")
        return False

def load_model_artifact(artifact: ModelArtifact) -> Optional[Dict[str, Any]]:
    """Load model artifact from disk."""
    try:
        artifact_dir = MODEL_ARTIFACTS_DIR / artifact.id
        
        if not artifact_dir.exists():
            logger.error(f"Artifact directory not found: {artifact_dir}")
            return None
        
        # Load metadata
        metadata_path = artifact_dir / "metadata.json"
        metadata = {}
        if metadata_path.exists():
            with open(metadata_path, 'r') as f:
                metadata = json.load(f)
        
        result = {'metadata': metadata}
        
        # Load model
        model_path = artifact_dir / "model.pt"
        if model_path.exists():
            try:
                model = torch.load(model_path, map_location='cpu')
                result['model'] = model
                logger.info(f"Model loaded from {model_path}")
            except Exception as e:
                logger.error(f"Error loading model: {e}")
                result['model'] = None
        else:
            result['model'] = None
        
        # Load tokenizer
        tokenizer_path = artifact_dir / "tokenizer"
        if tokenizer_path.exists():
            try:
                from transformers import AutoTokenizer
                tokenizer = AutoTokenizer.from_pretrained(tokenizer_path)
                result['tokenizer'] = tokenizer
                logger.info(f"Tokenizer loaded from {tokenizer_path}")
            except Exception as e:
                logger.error(f"Error loading tokenizer: {e}")
                result['tokenizer'] = None
        else:
            result['tokenizer'] = None
        
        # Load label map
        label_map_path = artifact_dir / "label_map.json"
        if label_map_path.exists():
            try:
                with open(label_map_path, 'r') as f:
                    label_map = json.load(f)
                result['label_map'] = label_map
                logger.info(f"Label map loaded from {label_map_path}")
            except Exception as e:
                logger.error(f"Error loading label map: {e}")
                result['label_map'] = {}
        else:
            result['label_map'] = {}
        
        # Load rules
        rules_path = artifact_dir / "rules.csv"
        if rules_path.exists():
            try:
                rules_df = pd.read_csv(rules_path)
                result['rules'] = rules_df
                logger.info(f"Rules loaded from {rules_path}")
            except Exception as e:
                logger.error(f"Error loading rules: {e}")
                result['rules'] = pd.DataFrame()
        else:
            result['rules'] = pd.DataFrame()
        
        # Load HRM model data
        hrm_path = artifact_dir / "hrm_model.json"
        if hrm_path.exists():
            try:
                with open(hrm_path, 'r') as f:
                    hrm_model_data = json.load(f)
                result['hrm_model_data'] = hrm_model_data
                logger.info(f"HRM model data loaded from {hrm_path}")
            except Exception as e:
                logger.error(f"Error loading HRM model data: {e}")
                result['hrm_model_data'] = None
        else:
            result['hrm_model_data'] = None
        
        logger.info(f"Model artifact {artifact.id} loaded successfully")
        return result
        
    except Exception as e:
        logger.error(f"Error loading model artifact {artifact.id}: {e}")
        return None

def delete_model_artifact(artifact_id: str) -> bool:
    """Delete model artifact from disk."""
    try:
        artifact_dir = MODEL_ARTIFACTS_DIR / artifact_id
        
        if artifact_dir.exists():
            import shutil
            shutil.rmtree(artifact_dir)
            logger.info(f"Model artifact {artifact_id} deleted successfully")
            return True
        else:
            logger.warning(f"Artifact directory not found: {artifact_dir}")
            return True  # Consider it successful if already deleted
        
    except Exception as e:
        logger.error(f"Error deleting model artifact {artifact_id}: {e}")
        return False

def get_artifact_size(artifact_id: str) -> int:
    """Get the total size of a model artifact in bytes."""
    try:
        artifact_dir = MODEL_ARTIFACTS_DIR / artifact_id
        
        if not artifact_dir.exists():
            return 0
        
        total_size = 0
        for file_path in artifact_dir.rglob('*'):
            if file_path.is_file():
                total_size += file_path.stat().st_size
        
        return total_size
        
    except Exception as e:
        logger.error(f"Error calculating artifact size for {artifact_id}: {e}")
        return 0

def list_artifact_files(artifact_id: str) -> Dict[str, Any]:
    """List all files in a model artifact directory."""
    try:
        artifact_dir = MODEL_ARTIFACTS_DIR / artifact_id
        
        if not artifact_dir.exists():
            return {'exists': False, 'files': []}
        
        files = []
        for file_path in artifact_dir.rglob('*'):
            if file_path.is_file():
                relative_path = file_path.relative_to(artifact_dir)
                files.append({
                    'name': str(relative_path),
                    'size': file_path.stat().st_size,
                    'modified': file_path.stat().st_mtime
                })
        
        return {
            'exists': True,
            'files': files,
            'total_size': sum(f['size'] for f in files)
        }
        
    except Exception as e:
        logger.error(f"Error listing artifact files for {artifact_id}: {e}")
        return {'exists': False, 'files': [], 'error': str(e)}
