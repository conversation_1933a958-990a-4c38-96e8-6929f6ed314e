/**
 * ClassBalanceAnalyzer.tsx
 * 
 * Class balance analysis component with imbalance detection and handling recommendations.
 * Provides visual analysis of class distribution and suggests mitigation strategies.
 */

import React, { useState, useEffect, useMemo } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  BarChart3, 
  AlertTriangle, 
  CheckCircle2, 
  TrendingUp, 
  Scale, 
  Target,
  Info,
  Lightbulb,
  PieChart
} from "lucide-react";
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, <PERSON><PERSON><PERSON> as RechartsPieChart, Cell } from 'recharts';
import { UploadedFile } from "@/services/fileUploadApi";

interface ClassBalanceAnalyzerProps {
  data: UploadedFile;
  labelColumn: string;
  labelFormat?: 'single' | 'multiple';
  selectedLabelColumns?: string[];
  onRecommendationApply?: (recommendation: BalanceRecommendation) => void;
}

interface ClassDistribution {
  class: string;
  count: number;
  percentage: number;
  color: string;
}

interface BalanceMetrics {
  totalSamples: number;
  numClasses: number;
  majorityClass: string;
  minorityClass: string;
  imbalanceRatio: number;
  giniIndex: number;
  entropyScore: number;
  isBalanced: boolean;
  severityLevel: 'low' | 'moderate' | 'high' | 'severe';
}

interface BalanceRecommendation {
  id: string;
  title: string;
  description: string;
  technique: 'oversampling' | 'undersampling' | 'class_weights' | 'ensemble' | 'data_augmentation';
  pros: string[];
  cons: string[];
  applicability: number; // 0-100 score
  complexity: 'low' | 'medium' | 'high';
}

const COLORS = [
  '#3b82f6', '#ef4444', '#22c55e', '#f59e0b', '#8b5cf6',
  '#06b6d4', '#f97316', '#84cc16', '#ec4899', '#6366f1'
];

export const ClassBalanceAnalyzer: React.FC<ClassBalanceAnalyzerProps> = ({
  data,
  labelColumn,
  labelFormat = 'single',
  selectedLabelColumns = [],
  onRecommendationApply
}) => {
  const [selectedRecommendation, setSelectedRecommendation] = useState<string | null>(null);

  // Calculate class distribution
  const classDistribution = useMemo((): ClassDistribution[] => {
    if (!data.preview) return [];

    const counts: Record<string, number> = {};
    let total = 0;

    if (labelFormat === 'single' && labelColumn) {
      // Single column format: count occurrences of each class
      data.preview.forEach(row => {
        const label = row[labelColumn];
        if (label) {
          counts[label] = (counts[label] || 0) + 1;
          total++;
        }
      });
    } else if (labelFormat === 'multiple' && selectedLabelColumns.length > 0) {
      // Multiple column format: count positive cases for each class
      selectedLabelColumns.forEach(col => {
        const count = data.preview.filter(row =>
          row[col] === 1 || row[col] === '1' || row[col] === true || row[col] === 'true'
        ).length;
        counts[col] = count;
        total += count; // Note: total might be higher than number of rows due to multi-class nature
      });
    }

    // Convert to distribution array with colors
    return Object.entries(counts)
      .map(([className, count], index) => ({
        class: className,
        count,
        percentage: total > 0 ? (count / total) * 100 : 0,
        color: COLORS[index % COLORS.length]
      }))
      .sort((a, b) => b.count - a.count);
  }, [data.preview, labelColumn, labelFormat, selectedLabelColumns]);

  // Calculate balance metrics
  const balanceMetrics = useMemo((): BalanceMetrics => {
    if (classDistribution.length === 0) {
      return {
        totalSamples: 0,
        numClasses: 0,
        majorityClass: '',
        minorityClass: '',
        imbalanceRatio: 1,
        giniIndex: 0,
        entropyScore: 0,
        isBalanced: true,
        severityLevel: 'low'
      };
    }

    const totalSamples = classDistribution.reduce((sum, cls) => sum + cls.count, 0);
    const majorityClass = classDistribution[0];
    const minorityClass = classDistribution[classDistribution.length - 1];
    const imbalanceRatio = majorityClass.count / minorityClass.count;

    // Calculate Gini Index
    const giniIndex = 1 - classDistribution.reduce((sum, cls) => {
      const p = cls.count / totalSamples;
      return sum + (p * p);
    }, 0);

    // Calculate Entropy
    const entropyScore = -classDistribution.reduce((sum, cls) => {
      const p = cls.count / totalSamples;
      return sum + (p * Math.log2(p));
    }, 0);

    // Determine balance status
    const isBalanced = imbalanceRatio <= 2;
    let severityLevel: 'low' | 'moderate' | 'high' | 'severe' = 'low';
    
    if (imbalanceRatio > 10) severityLevel = 'severe';
    else if (imbalanceRatio > 5) severityLevel = 'high';
    else if (imbalanceRatio > 2) severityLevel = 'moderate';

    return {
      totalSamples,
      numClasses: classDistribution.length,
      majorityClass: majorityClass.class,
      minorityClass: minorityClass.class,
      imbalanceRatio,
      giniIndex,
      entropyScore,
      isBalanced,
      severityLevel
    };
  }, [classDistribution]);

  // Generate recommendations based on imbalance severity
  const recommendations = useMemo((): BalanceRecommendation[] => {
    const recs: BalanceRecommendation[] = [];

    if (balanceMetrics.isBalanced) {
      return [{
        id: 'balanced',
        title: 'Dataset is Well Balanced',
        description: 'Your dataset has a good class distribution. No special handling required.',
        technique: 'class_weights',
        pros: ['Natural distribution', 'No bias introduced', 'Standard training works well'],
        cons: [],
        applicability: 100,
        complexity: 'low'
      }];
    }

    // Class weights (always applicable)
    recs.push({
      id: 'class_weights',
      title: 'Use Class Weights',
      description: 'Assign higher weights to minority classes during training to balance the loss function.',
      technique: 'class_weights',
      pros: ['Simple to implement', 'No data modification', 'Works with any algorithm'],
      cons: ['May not work well with extreme imbalance', 'Can lead to overfitting'],
      applicability: 90,
      complexity: 'low'
    });

    // SMOTE for moderate to high imbalance
    if (balanceMetrics.severityLevel !== 'low') {
      recs.push({
        id: 'smote',
        title: 'SMOTE Oversampling',
        description: 'Generate synthetic examples for minority classes using SMOTE (Synthetic Minority Oversampling Technique).',
        technique: 'oversampling',
        pros: ['Increases minority class samples', 'Reduces overfitting risk', 'Well-established technique'],
        cons: ['May create unrealistic samples', 'Increases training time', 'Not suitable for high-dimensional data'],
        applicability: balanceMetrics.severityLevel === 'severe' ? 70 : 85,
        complexity: 'medium'
      });
    }

    // Random undersampling for high imbalance
    if (balanceMetrics.severityLevel === 'high' || balanceMetrics.severityLevel === 'severe') {
      recs.push({
        id: 'undersampling',
        title: 'Random Undersampling',
        description: 'Randomly remove samples from majority classes to balance the dataset.',
        technique: 'undersampling',
        pros: ['Reduces training time', 'Simple to implement', 'Effective for large datasets'],
        cons: ['Loss of potentially useful data', 'May hurt performance', 'Not suitable for small datasets'],
        applicability: balanceMetrics.totalSamples > 10000 ? 75 : 40,
        complexity: 'low'
      });
    }

    // Ensemble methods for severe imbalance
    if (balanceMetrics.severityLevel === 'severe') {
      recs.push({
        id: 'ensemble',
        title: 'Ensemble Methods',
        description: 'Use ensemble techniques like BalancedRandomForest or EasyEnsemble to handle extreme imbalance.',
        technique: 'ensemble',
        pros: ['Handles extreme imbalance well', 'Combines multiple strategies', 'Often achieves best performance'],
        cons: ['More complex to implement', 'Longer training time', 'Harder to interpret'],
        applicability: 95,
        complexity: 'high'
      });
    }

    return recs.sort((a, b) => b.applicability - a.applicability);
  }, [balanceMetrics]);

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'low': return 'text-green-600';
      case 'moderate': return 'text-yellow-600';
      case 'high': return 'text-orange-600';
      case 'severe': return 'text-red-600';
      default: return 'text-muted-foreground';
    }
  };

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'low': return <CheckCircle2 className="w-4 h-4" />;
      case 'moderate': return <Info className="w-4 h-4" />;
      case 'high': return <AlertTriangle className="w-4 h-4" />;
      case 'severe': return <AlertTriangle className="w-4 h-4" />;
      default: return <Info className="w-4 h-4" />;
    }
  };

  if (classDistribution.length === 0) {
    return (
      <Alert>
        <Info className="h-4 w-4" />
        <AlertDescription>
          Select a label column to analyze class balance.
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      {/* Balance Status Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Scale className="w-5 h-5" />
            Class Balance Analysis
          </CardTitle>
          <CardDescription>
            Analysis of class distribution and balance in your dataset
            {labelFormat === 'multiple' && ' (multiple binary columns)'}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Status Badge */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              {getSeverityIcon(balanceMetrics.severityLevel)}
              <span className={`font-medium ${getSeverityColor(balanceMetrics.severityLevel)}`}>
                {balanceMetrics.isBalanced ? 'Balanced Dataset' : `${balanceMetrics.severityLevel.charAt(0).toUpperCase() + balanceMetrics.severityLevel.slice(1)} Imbalance`}
              </span>
            </div>
            <Badge variant={balanceMetrics.isBalanced ? 'default' : 'destructive'}>
              Ratio: {balanceMetrics.imbalanceRatio.toFixed(1)}:1
            </Badge>
          </div>

          {/* Key Metrics */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center p-3 bg-muted/50 rounded-lg">
              <div className="text-lg font-bold">{balanceMetrics.numClasses}</div>
              <div className="text-xs text-muted-foreground">Classes</div>
            </div>
            <div className="text-center p-3 bg-muted/50 rounded-lg">
              <div className="text-lg font-bold">{balanceMetrics.totalSamples}</div>
              <div className="text-xs text-muted-foreground">Total Samples</div>
            </div>
            <div className="text-center p-3 bg-muted/50 rounded-lg">
              <div className="text-lg font-bold">{balanceMetrics.giniIndex.toFixed(3)}</div>
              <div className="text-xs text-muted-foreground">Gini Index</div>
            </div>
            <div className="text-center p-3 bg-muted/50 rounded-lg">
              <div className="text-lg font-bold">{balanceMetrics.entropyScore.toFixed(2)}</div>
              <div className="text-xs text-muted-foreground">Entropy</div>
            </div>
          </div>

          {/* Imbalance Warning */}
          {!balanceMetrics.isBalanced && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                <strong>Class imbalance detected!</strong> The majority class "{balanceMetrics.majorityClass}" 
                has {balanceMetrics.imbalanceRatio.toFixed(1)}x more samples than the minority class "{balanceMetrics.minorityClass}". 
                This may lead to biased predictions.
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* Visualizations */}
      <Card>
        <CardHeader>
          <CardTitle>Class Distribution</CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="bar" className="space-y-4">
            <TabsList>
              <TabsTrigger value="bar">Bar Chart</TabsTrigger>
              <TabsTrigger value="pie">Pie Chart</TabsTrigger>
              <TabsTrigger value="table">Table View</TabsTrigger>
            </TabsList>

            <TabsContent value="bar">
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={classDistribution}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="class" />
                  <YAxis />
                  <Tooltip />
                  <Bar dataKey="count" fill="#3b82f6" />
                </BarChart>
              </ResponsiveContainer>
            </TabsContent>

            <TabsContent value="pie">
              <ResponsiveContainer width="100%" height={300}>
                <RechartsPieChart>
                  <Tooltip />
                  <RechartsPieChart data={classDistribution} cx="50%" cy="50%" outerRadius={100}>
                    {classDistribution.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </RechartsPieChart>
                </RechartsPieChart>
              </ResponsiveContainer>
            </TabsContent>

            <TabsContent value="table">
              <div className="border rounded-lg overflow-hidden">
                <table className="w-full text-sm">
                  <thead className="bg-muted">
                    <tr>
                      <th className="text-left p-3">Class</th>
                      <th className="text-left p-3">Count</th>
                      <th className="text-left p-3">Percentage</th>
                      <th className="text-left p-3">Status</th>
                    </tr>
                  </thead>
                  <tbody>
                    {classDistribution.map((cls, index) => (
                      <tr key={cls.class} className="border-t">
                        <td className="p-3 font-medium">{cls.class}</td>
                        <td className="p-3">{cls.count.toLocaleString()}</td>
                        <td className="p-3">{cls.percentage.toFixed(1)}%</td>
                        <td className="p-3">
                          <Badge variant={index === 0 ? 'destructive' : index === classDistribution.length - 1 ? 'secondary' : 'outline'}>
                            {index === 0 ? 'Majority' : index === classDistribution.length - 1 ? 'Minority' : 'Regular'}
                          </Badge>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Recommendations */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Lightbulb className="w-5 h-5" />
            Recommendations
          </CardTitle>
          <CardDescription>
            Suggested techniques to handle class imbalance
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {recommendations.map((rec) => (
            <div 
              key={rec.id}
              className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                selectedRecommendation === rec.id ? 'border-primary bg-primary/5' : 'hover:bg-muted/50'
              }`}
              onClick={() => setSelectedRecommendation(selectedRecommendation === rec.id ? null : rec.id)}
            >
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-2">
                    <h4 className="font-medium">{rec.title}</h4>
                    <Badge variant="outline" className="text-xs">
                      {rec.complexity} complexity
                    </Badge>
                    <Badge variant="secondary" className="text-xs">
                      {rec.applicability}% match
                    </Badge>
                  </div>
                  <p className="text-sm text-muted-foreground mb-3">{rec.description}</p>
                  
                  {selectedRecommendation === rec.id && (
                    <div className="space-y-3">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <h5 className="font-medium text-green-600 mb-1">Pros</h5>
                          <ul className="text-sm text-muted-foreground space-y-1">
                            {rec.pros.map((pro, idx) => (
                              <li key={idx}>• {pro}</li>
                            ))}
                          </ul>
                        </div>
                        <div>
                          <h5 className="font-medium text-red-600 mb-1">Cons</h5>
                          <ul className="text-sm text-muted-foreground space-y-1">
                            {rec.cons.map((con, idx) => (
                              <li key={idx}>• {con}</li>
                            ))}
                          </ul>
                        </div>
                      </div>
                      
                      {onRecommendationApply && (
                        <Button 
                          onClick={(e) => {
                            e.stopPropagation();
                            onRecommendationApply(rec);
                          }}
                          size="sm"
                        >
                          Apply This Technique
                        </Button>
                      )}
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))}
        </CardContent>
      </Card>
    </div>
  );
};

export default ClassBalanceAnalyzer;
