import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Separator } from "@/components/ui/separator";
import { Progress } from "@/components/ui/progress";
import { 
  Target, 
  Brain, 
  Upload, 
  Play, 
  Settings,
  FileText,
  CheckCircle2,
  AlertCircle,
  Info,
  Loader2,
  Database,
  Zap,
  BarChart3
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { HierarchicalModelManager, HierarchicalModel } from "./HierarchicalModelManager";
import { UnifiedFileUploadZone } from "@/components/UnifiedFileUploadZone";
import { ColumnSelector } from "@/components/ColumnSelector";

export interface ClassificationConfig {
  selectedModelId: string;
  useTrainingData: boolean;
  classificationFileId?: string;
  textColumn: string;
  batchSize: number;
  confidenceThreshold: number;
  includeConfidenceScores: boolean;
  includeProbabilities: boolean;
  outputFormat: 'csv' | 'excel' | 'json';
}

interface HierarchicalModelClassificationProps {
  trainingResults?: any;
  uploadedData?: any;
  dualData?: any;
  selectedTextColumns: string[];
  selectedLabelColumns: string[];
  hierarchyLevels: any[];
  onClassificationStart: (config: ClassificationConfig) => void;
  onClassificationComplete: (results: any) => void;
  isClassifying?: boolean;
  classificationProgress?: number;
}

export const HierarchicalModelClassification: React.FC<HierarchicalModelClassificationProps> = ({
  trainingResults,
  uploadedData,
  dualData,
  selectedTextColumns,
  selectedLabelColumns,
  hierarchyLevels,
  onClassificationStart,
  onClassificationComplete,
  isClassifying = false,
  classificationProgress = 0
}) => {
  const { toast } = useToast();
  
  // State management
  const [selectedModel, setSelectedModel] = useState<HierarchicalModel | null>(null);
  const [classificationConfig, setClassificationConfig] = useState<ClassificationConfig>({
    selectedModelId: '',
    useTrainingData: false, // Default to using classification data, not training data
    textColumn: selectedTextColumns[0] || '',
    batchSize: 32,
    confidenceThreshold: 0.5,
    includeConfidenceScores: true,
    includeProbabilities: false,
    outputFormat: 'csv'
  });

  // Auto-detect and set classification data source
  useEffect(() => {
    // If we have dual data setup, automatically use classification data
    if (dualData?.classificationData?.fileInfo) {
      setClassificationConfig(prev => ({
        ...prev,
        useTrainingData: false, // Force use of classification data
        classificationFileId: dualData.classificationData.fileInfo.file_id
      }));

      // Set the classification data for the component
      setClassificationData(dualData.classificationData.fileInfo);
    }
    // If we only have uploaded data (single file workflow), use that
    else if (uploadedData && !dualData) {
      setClassificationConfig(prev => ({
        ...prev,
        useTrainingData: false,
        classificationFileId: uploadedData.file_id
      }));

      // Set the uploaded data as classification data
      setClassificationData(uploadedData);
    }
  }, [dualData, uploadedData]);
  
  // File upload state for new classification data
  const [classificationData, setClassificationData] = useState<any>(null);
  const [availableColumns, setAvailableColumns] = useState<string[]>([]);
  const [showModelManager, setShowModelManager] = useState(false);
  const [currentTab, setCurrentTab] = useState<'model' | 'data' | 'config'>('model');

  // Auto-select model if training just completed
  useEffect(() => {
    if (trainingResults?.model_id && !selectedModel) {
      // Create a model object from training results
      const trainedModel: HierarchicalModel = {
        id: trainingResults.model_id,
        name: `Recently Trained Model`,
        base_model: trainingResults.base_model || 'distilbert-base-uncased',
        created_at: new Date().toISOString(),
        status: 'completed',
        training_config: {
          num_epochs: trainingResults.epochs || 3,
          batch_size: trainingResults.batch_size || 16,
          learning_rate: trainingResults.learning_rate || 2e-5,
          validation_split: 0.2,
          use_unsloth: trainingResults.use_unsloth || false,
          base_model: trainingResults.base_model || 'distilbert-base-uncased',
          warmup_steps: 500,
          weight_decay: 0.01,
          gradient_accumulation_steps: 1,
          fp16: true,
          gradient_checkpointing: false,
          enable_early_stopping: true,
          hierarchy_weights: [],
          constraint_enforcement: false,
          level_wise_training: false
        },
        metrics: trainingResults.metrics,
        metadata: {
          hierarchy_levels: hierarchyLevels.length,
          total_samples: trainingResults.total_samples || 0,
          model_size: trainingResults.model_size || 0,
          description: 'Recently trained hierarchical model'
        }
      };
      
      setSelectedModel(trainedModel);
      setClassificationConfig(prev => ({
        ...prev,
        selectedModelId: trainingResults.model_id
      }));
    }
  }, [trainingResults, hierarchyLevels.length]);

  // Update available columns when data changes
  useEffect(() => {
    if (classificationConfig.useTrainingData) {
      // Use training data when useTrainingData is true
      const dataSource = dualData?.trainingData?.fileInfo || uploadedData;
      if (dataSource?.columns) {
        setAvailableColumns(dataSource.columns);
      }
    } else {
      // Use classification data when useTrainingData is false
      let dataSource = null;

      // Priority: explicitly uploaded classification data > dual data classification > uploaded data
      if (classificationData?.columns) {
        dataSource = classificationData;
      } else if (dualData?.classificationData?.fileInfo?.columns) {
        dataSource = dualData.classificationData.fileInfo;
      } else if (uploadedData?.columns) {
        dataSource = uploadedData;
      }

      if (dataSource?.columns) {
        setAvailableColumns(dataSource.columns);
      }
    }
  }, [classificationConfig.useTrainingData, uploadedData, dualData, classificationData]);

  const handleModelSelect = (model: HierarchicalModel) => {
    setSelectedModel(model);
    setClassificationConfig(prev => ({
      ...prev,
      selectedModelId: model.id
    }));
    
    toast({
      title: "Model Selected",
      description: `Selected "${model.name}" for classification`
    });
  };

  const handleDataSourceChange = (useTrainingData: boolean) => {
    setClassificationConfig(prev => ({
      ...prev,
      useTrainingData,
      classificationFileId: useTrainingData ? undefined : prev.classificationFileId
    }));
  };

  const handleFileUpload = (fileInfo: any) => {
    setClassificationData(fileInfo);
    setClassificationConfig(prev => ({
      ...prev,
      classificationFileId: fileInfo.file_id
    }));
    
    toast({
      title: "Classification Data Uploaded",
      description: `Uploaded ${fileInfo.filename} for classification`
    });
  };

  const handleConfigChange = (key: keyof ClassificationConfig, value: any) => {
    setClassificationConfig(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const validateConfiguration = (): { valid: boolean; errors: string[] } => {
    const errors: string[] = [];
    
    if (!selectedModel) {
      errors.push("Please select a trained model");
    }
    
    // Check if we have classification data available
    if (!classificationConfig.useTrainingData) {
      const hasClassificationData = classificationData ||
                                   dualData?.classificationData?.fileInfo ||
                                   uploadedData;
      if (!hasClassificationData) {
        errors.push("Please upload classification data or use training data");
      }
    }
    
    if (!classificationConfig.textColumn) {
      errors.push("Please select a text column for classification");
    }
    
    if (classificationConfig.batchSize < 1 || classificationConfig.batchSize > 128) {
      errors.push("Batch size must be between 1 and 128");
    }
    
    if (classificationConfig.confidenceThreshold < 0 || classificationConfig.confidenceThreshold > 1) {
      errors.push("Confidence threshold must be between 0 and 1");
    }
    
    return {
      valid: errors.length === 0,
      errors
    };
  };

  const handleStartClassification = () => {
    const validation = validateConfiguration();
    
    if (!validation.valid) {
      toast({
        title: "Configuration Error",
        description: validation.errors.join(", "),
        variant: "destructive"
      });
      return;
    }
    
    onClassificationStart(classificationConfig);
  };

  const getDataSourceInfo = () => {
    if (classificationConfig.useTrainingData) {
      // Show training data info when useTrainingData is true
      const dataSource = dualData?.trainingData?.fileInfo || uploadedData;
      return {
        filename: dataSource?.filename || 'Training data',
        rows: dataSource?.num_rows || dataSource?.rows || 0,
        columns: dataSource?.columns?.length || 0
      };
    } else {
      // Show classification data info when useTrainingData is false
      // Priority: explicitly uploaded classification data > dual data classification > uploaded data
      let dataSource = null;

      if (classificationData) {
        dataSource = classificationData;
      } else if (dualData?.classificationData?.fileInfo) {
        dataSource = dualData.classificationData.fileInfo;
      } else if (uploadedData) {
        dataSource = uploadedData;
      }

      if (dataSource) {
        return {
          filename: dataSource.filename || 'Classification data',
          rows: dataSource.num_rows || dataSource.rows || 0,
          columns: dataSource.columns?.length || 0
        };
      }
    }
    return null;
  };

  const dataInfo = getDataSourceInfo();

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card className="border-2 border-gray-200 dark:border-gray-800">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 rounded-lg bg-gray-100 dark:bg-gray-800 flex items-center justify-center">
                <Target className="w-5 h-5 text-gray-700 dark:text-gray-300" />
              </div>
              <div>
                <CardTitle>Model Classification</CardTitle>
                <CardDescription>
                  Select a trained model and configure classification parameters
                </CardDescription>
              </div>
            </div>
            {selectedModel && (
              <Badge variant="outline" className="flex items-center gap-2">
                <Brain className="w-3 h-3" />
                {selectedModel.name}
              </Badge>
            )}
          </div>
        </CardHeader>
      </Card>

      {/* Configuration Tabs */}
      <Tabs value={currentTab} onValueChange={(value) => setCurrentTab(value as any)} className="space-y-4">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="model" className="flex items-center gap-2">
            <Brain className="w-4 h-4" />
            Model Selection
          </TabsTrigger>
          <TabsTrigger value="data" className="flex items-center gap-2">
            <Database className="w-4 h-4" />
            Data Source
          </TabsTrigger>
          <TabsTrigger value="config" className="flex items-center gap-2">
            <Settings className="w-4 h-4" />
            Configuration
          </TabsTrigger>
        </TabsList>

        {/* Model Selection Tab */}
        <TabsContent value="model" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Select Classification Model</CardTitle>
              <CardDescription>
                Choose a trained hierarchical model for classification
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {selectedModel ? (
                <div className="space-y-4">
                  <Alert>
                    <CheckCircle2 className="h-4 w-4" />
                    <AlertDescription>
                      <strong>Selected Model:</strong> {selectedModel.name}
                      <br />
                      <span className="text-sm text-muted-foreground">
                        Base Model: {selectedModel.base_model} • 
                        Hierarchy Levels: {selectedModel.metadata.hierarchy_levels} • 
                        Status: {selectedModel.status}
                      </span>
                    </AlertDescription>
                  </Alert>
                  
                  <Button
                    variant="outline"
                    onClick={() => setShowModelManager(!showModelManager)}
                  >
                    {showModelManager ? 'Hide' : 'Show'} Available Models
                  </Button>
                </div>
              ) : (
                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    No model selected. Please choose a trained model below.
                  </AlertDescription>
                </Alert>
              )}

              {(showModelManager || !selectedModel) && (
                <HierarchicalModelManager
                  onModelSelect={handleModelSelect}
                  onModelUse={(modelId) => {
                    // This will be handled by the classification start
                  }}
                  onModelDelete={() => {
                    // Handle model deletion if needed
                  }}
                  selectedModelId={selectedModel?.id}
                  showActions={false}
                />
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Data Source Tab */}
        <TabsContent value="data" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Classification Data Source</CardTitle>
              <CardDescription>
                Choose which data to classify with your selected model
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <input
                    type="radio"
                    id="useClassificationData"
                    name="dataSource"
                    checked={!classificationConfig.useTrainingData}
                    onChange={() => handleDataSourceChange(false)}
                    className="w-4 h-4"
                  />
                  <Label htmlFor="useClassificationData">Use Classification Data</Label>
                  <span className="text-sm text-muted-foreground ml-2">(Recommended)</span>
                </div>

                <div className="flex items-center space-x-2">
                  <input
                    type="radio"
                    id="useTrainingData"
                    name="dataSource"
                    checked={classificationConfig.useTrainingData}
                    onChange={() => handleDataSourceChange(true)}
                    className="w-4 h-4"
                  />
                  <Label htmlFor="useTrainingData">Use Training Data</Label>
                  <span className="text-sm text-muted-foreground ml-2">(For testing/validation)</span>
                </div>
              </div>

              {!classificationConfig.useTrainingData && (
                <div className="space-y-4">
                  <UnifiedFileUploadZone
                    onFileUpload={handleFileUpload}
                    acceptedTypes={['.csv', '.xlsx', '.json']}
                    maxFileSize={100}
                    showFileReuse={true}
                    allowedPurposes={['classification']}
                    title="Upload Classification Data"
                    description="Upload the data you want to classify"
                  />
                </div>
              )}

              {dataInfo && (
                <Alert>
                  <Info className="h-4 w-4" />
                  <AlertDescription>
                    <strong>Data Source:</strong> {dataInfo.filename}
                    <br />
                    <strong>Rows:</strong> {dataInfo.rows.toLocaleString()}
                    <br />
                    <strong>Columns:</strong> {dataInfo.columns}
                  </AlertDescription>
                </Alert>
              )}

              {availableColumns.length > 0 && (
                <div className="space-y-2">
                  <Label htmlFor="textColumn">Text Column</Label>
                  <Select
                    value={classificationConfig.textColumn}
                    onValueChange={(value) => handleConfigChange('textColumn', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select text column" />
                    </SelectTrigger>
                    <SelectContent>
                      {availableColumns.map((column) => (
                        <SelectItem key={column} value={column}>
                          {column}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Configuration Tab */}
        <TabsContent value="config" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Classification Configuration</CardTitle>
              <CardDescription>
                Configure parameters for the classification process
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="batchSize">Batch Size</Label>
                  <Select
                    value={classificationConfig.batchSize.toString()}
                    onValueChange={(value) => handleConfigChange('batchSize', parseInt(value))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="8">8</SelectItem>
                      <SelectItem value="16">16</SelectItem>
                      <SelectItem value="32">32</SelectItem>
                      <SelectItem value="64">64</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="confidenceThreshold">Confidence Threshold</Label>
                  <Select
                    value={classificationConfig.confidenceThreshold.toString()}
                    onValueChange={(value) => handleConfigChange('confidenceThreshold', parseFloat(value))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="0.3">0.3</SelectItem>
                      <SelectItem value="0.5">0.5</SelectItem>
                      <SelectItem value="0.7">0.7</SelectItem>
                      <SelectItem value="0.9">0.9</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="includeConfidenceScores"
                    checked={classificationConfig.includeConfidenceScores}
                    onChange={(e) => handleConfigChange('includeConfidenceScores', e.target.checked)}
                    className="w-4 h-4"
                  />
                  <Label htmlFor="includeConfidenceScores">Include Confidence Scores</Label>
                </div>

                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="includeProbabilities"
                    checked={classificationConfig.includeProbabilities}
                    onChange={(e) => handleConfigChange('includeProbabilities', e.target.checked)}
                    className="w-4 h-4"
                  />
                  <Label htmlFor="includeProbabilities">Include Probabilities</Label>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="outputFormat">Output Format</Label>
                <Select
                  value={classificationConfig.outputFormat}
                  onValueChange={(value) => handleConfigChange('outputFormat', value as 'csv' | 'excel' | 'json')}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="csv">CSV</SelectItem>
                    <SelectItem value="excel">Excel</SelectItem>
                    <SelectItem value="json">JSON</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Action Buttons */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex justify-between items-center">
            <div className="text-sm text-muted-foreground">
              {selectedModel ? `Ready to classify with ${selectedModel.name}` : 'Please select a model first'}
            </div>

            <Button
              onClick={handleStartClassification}
              disabled={!selectedModel || isClassifying || !classificationConfig.textColumn}
              size="lg"
              className="min-w-[200px]"
            >
              {isClassifying ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Classifying... {Math.round(classificationProgress || 0)}%
                </>
              ) : (
                <>
                  <Zap className="w-4 h-4 mr-2" />
                  Start Classification
                </>
              )}
            </Button>
          </div>

          {isClassifying && (
            <div className="mt-4">
              <Progress value={classificationProgress || 0} className="w-full" />
              <p className="text-sm text-muted-foreground text-center mt-2">
                Processing {dataInfo?.rows.toLocaleString()} rows...
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
