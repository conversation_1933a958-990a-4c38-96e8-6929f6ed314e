/**
 * EnhancedGuidanceSystem.tsx
 * 
 * Enhanced guidance system for ClassyWeb Phase 4 implementation
 * Provides contextual help, tooltips, guided tours, and smart recommendations
 */

import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { 
  HelpCircle, 
  Lightbulb, 
  Info, 
  AlertTriangle, 
  CheckCircle2,
  X,
  ChevronRight,
  Play,
  Pause,
  RotateCcw
} from 'lucide-react';

export interface GuidanceStep {
  id: string;
  title: string;
  content: string;
  target?: string; // CSS selector for element to highlight
  position?: 'top' | 'bottom' | 'left' | 'right';
  action?: 'click' | 'input' | 'wait';
  optional?: boolean;
}

export interface GuidanceTour {
  id: string;
  title: string;
  description: string;
  steps: GuidanceStep[];
  category: 'beginner' | 'expert' | 'feature';
  estimatedTime: string;
}

export interface ContextualHelp {
  id: string;
  title: string;
  content: string;
  type: 'info' | 'warning' | 'tip' | 'error';
  trigger: 'hover' | 'click' | 'focus';
  position?: 'top' | 'bottom' | 'left' | 'right';
}

export interface SmartRecommendation {
  id: string;
  title: string;
  description: string;
  confidence: number; // 0-1
  category: 'data' | 'model' | 'configuration' | 'optimization';
  action?: {
    label: string;
    onClick: () => void;
  };
  learnMore?: {
    label: string;
    url: string;
  };
}

interface EnhancedGuidanceSystemProps {
  // Contextual help
  contextualHelp?: ContextualHelp[];
  showContextualHelp?: boolean;
  
  // Guided tours
  availableTours?: GuidanceTour[];
  activeTour?: string;
  onTourStart?: (tourId: string) => void;
  onTourComplete?: (tourId: string) => void;
  onTourExit?: () => void;
  
  // Smart recommendations
  recommendations?: SmartRecommendation[];
  showRecommendations?: boolean;
  onRecommendationDismiss?: (recommendationId: string) => void;
  
  // General settings
  className?: string;
  compactMode?: boolean;
}

export const EnhancedGuidanceSystem: React.FC<EnhancedGuidanceSystemProps> = ({
  contextualHelp = [],
  showContextualHelp = true,
  availableTours = [],
  activeTour,
  onTourStart,
  onTourComplete,
  onTourExit,
  recommendations = [],
  showRecommendations = true,
  onRecommendationDismiss,
  className = "",
  compactMode = false
}) => {
  const [currentTourStep, setCurrentTourStep] = useState(0);
  const [tourPaused, setTourPaused] = useState(false);
  const [dismissedRecommendations, setDismissedRecommendations] = useState<Set<string>>(new Set());

  const currentTour = availableTours.find(tour => tour.id === activeTour);
  const visibleRecommendations = recommendations.filter(rec => !dismissedRecommendations.has(rec.id));

  // Tour navigation
  const handleTourNext = () => {
    if (!currentTour) return;
    
    if (currentTourStep < currentTour.steps.length - 1) {
      setCurrentTourStep(currentTourStep + 1);
    } else {
      // Tour completed
      onTourComplete?.(currentTour.id);
      setCurrentTourStep(0);
    }
  };

  const handleTourPrevious = () => {
    if (currentTourStep > 0) {
      setCurrentTourStep(currentTourStep - 1);
    }
  };

  const handleTourExit = () => {
    onTourExit?.();
    setCurrentTourStep(0);
    setTourPaused(false);
  };

  const handleRecommendationDismiss = (recommendationId: string) => {
    setDismissedRecommendations(prev => new Set([...prev, recommendationId]));
    onRecommendationDismiss?.(recommendationId);
  };

  const getRecommendationIcon = (type: SmartRecommendation['category']) => {
    switch (type) {
      case 'data':
        return <Info className="w-4 h-4" />;
      case 'model':
        return <Lightbulb className="w-4 h-4" />;
      case 'configuration':
        return <HelpCircle className="w-4 h-4" />;
      case 'optimization':
        return <CheckCircle2 className="w-4 h-4" />;
      default:
        return <Info className="w-4 h-4" />;
    }
  };

  const getRecommendationColor = (confidence: number) => {
    if (confidence >= 0.8) return 'text-green-600 bg-green-50 border-green-200';
    if (confidence >= 0.6) return 'text-blue-600 bg-blue-50 border-blue-200';
    return 'text-yellow-600 bg-yellow-50 border-yellow-200';
  };

  return (
    <TooltipProvider>
      <div className={`space-y-4 ${className}`}>
        {/* Active Tour Overlay */}
        {currentTour && !tourPaused && (
          <Card className="border-2 border-blue-500 shadow-lg">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-lg flex items-center gap-2">
                    <Play className="w-5 h-5 text-blue-600" />
                    {currentTour.title}
                  </CardTitle>
                  <p className="text-sm text-muted-foreground mt-1">
                    Step {currentTourStep + 1} of {currentTour.steps.length}
                  </p>
                </div>
                <div className="flex items-center gap-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setTourPaused(true)}
                  >
                    <Pause className="w-4 h-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleTourExit}
                  >
                    <X className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              {currentTour.steps[currentTourStep] && (
                <div className="space-y-4">
                  <div>
                    <h4 className="font-semibold mb-2">
                      {currentTour.steps[currentTourStep].title}
                    </h4>
                    <p className="text-sm text-muted-foreground">
                      {currentTour.steps[currentTourStep].content}
                    </p>
                  </div>
                  
                  <div className="flex justify-between items-center">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleTourPrevious}
                      disabled={currentTourStep === 0}
                    >
                      Previous
                    </Button>
                    
                    <div className="flex items-center gap-1">
                      {currentTour.steps.map((_, index) => (
                        <div
                          key={index}
                          className={`w-2 h-2 rounded-full ${
                            index === currentTourStep ? 'bg-blue-600' : 'bg-gray-300'
                          }`}
                        />
                      ))}
                    </div>
                    
                    <Button
                      size="sm"
                      onClick={handleTourNext}
                    >
                      {currentTourStep === currentTour.steps.length - 1 ? 'Complete' : 'Next'}
                      <ChevronRight className="w-4 h-4 ml-1" />
                    </Button>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {/* Tour Paused State */}
        {currentTour && tourPaused && (
          <Alert>
            <Pause className="w-4 h-4" />
            <AlertDescription className="flex items-center justify-between">
              <span>Tour paused: {currentTour.title}</span>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setTourPaused(false)}
                >
                  <Play className="w-4 h-4 mr-1" />
                  Resume
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleTourExit}
                >
                  Exit Tour
                </Button>
              </div>
            </AlertDescription>
          </Alert>
        )}

        {/* Available Tours */}
        {!currentTour && availableTours.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <Play className="w-5 h-5" />
                Guided Tours
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-3">
                {availableTours.map((tour) => (
                  <div
                    key={tour.id}
                    className="flex items-center justify-between p-3 border rounded-lg hover:bg-muted/50"
                  >
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <h4 className="font-medium">{tour.title}</h4>
                        <Badge variant="outline" className="text-xs">
                          {tour.category}
                        </Badge>
                        <span className="text-xs text-muted-foreground">
                          {tour.estimatedTime}
                        </span>
                      </div>
                      <p className="text-sm text-muted-foreground">
                        {tour.description}
                      </p>
                    </div>
                    <Button
                      size="sm"
                      onClick={() => onTourStart?.(tour.id)}
                    >
                      Start Tour
                    </Button>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Smart Recommendations */}
        {showRecommendations && visibleRecommendations.length > 0 && (
          <div className="space-y-3">
            <h3 className="font-semibold flex items-center gap-2">
              <Lightbulb className="w-5 h-5" />
              Smart Recommendations
            </h3>
            {visibleRecommendations.map((recommendation) => (
              <Alert
                key={recommendation.id}
                className={getRecommendationColor(recommendation.confidence)}
              >
                <div className="flex items-start gap-3">
                  {getRecommendationIcon(recommendation.category)}
                  <div className="flex-1">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium">{recommendation.title}</h4>
                      <div className="flex items-center gap-2">
                        <Badge variant="outline" className="text-xs">
                          {Math.round(recommendation.confidence * 100)}% confidence
                        </Badge>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleRecommendationDismiss(recommendation.id)}
                        >
                          <X className="w-3 h-3" />
                        </Button>
                      </div>
                    </div>
                    <p className="text-sm mb-3">{recommendation.description}</p>
                    <div className="flex gap-2">
                      {recommendation.action && (
                        <Button
                          size="sm"
                          onClick={recommendation.action.onClick}
                        >
                          {recommendation.action.label}
                        </Button>
                      )}
                      {recommendation.learnMore && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => window.open(recommendation.learnMore!.url, '_blank')}
                        >
                          {recommendation.learnMore.label}
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              </Alert>
            ))}
          </div>
        )}

        {/* Contextual Help Tooltips */}
        {showContextualHelp && contextualHelp.length > 0 && (
          <div className="space-y-2">
            {contextualHelp.map((help) => (
              <Tooltip key={help.id}>
                <TooltipTrigger asChild>
                  <Button variant="ghost" size="sm" className="h-auto p-2">
                    <HelpCircle className="w-4 h-4" />
                    <span className="ml-2 text-sm">{help.title}</span>
                  </Button>
                </TooltipTrigger>
                <TooltipContent side={help.position || 'top'} className="max-w-xs">
                  <div className="space-y-2">
                    <p className="font-medium">{help.title}</p>
                    <p className="text-sm">{help.content}</p>
                  </div>
                </TooltipContent>
              </Tooltip>
            ))}
          </div>
        )}
      </div>
    </TooltipProvider>
  );
};
