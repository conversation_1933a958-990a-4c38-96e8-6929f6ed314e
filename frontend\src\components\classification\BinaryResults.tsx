/**
 * BinaryResults.tsx
 * 
 * Comprehensive results visualization component for binary classification.
 * Includes ROC curves, threshold optimization, performance metrics dashboard,
 * and binary-specific analysis.
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Progress } from "@/components/ui/progress";
import { useToast } from "@/hooks/use-toast";
import {
  BarChart3,
  Download,
  Share2,
  Target,
  TrendingUp,
  Scale,
  Activity,
  Zap,
  Eye,
  FileText,
  CheckCircle2,
  AlertCircle,
  Info,
  PieChart,
  LineChart
} from "lucide-react";
import { ResultsDataTable } from "@/components/ResultsDataTable";
import { ROCCurveChart } from "./ROCCurveChart";
import { ThresholdOptimizer } from "./ThresholdOptimizer";
import { ClassBalanceAnalyzer } from "./ClassBalanceAnalyzer";
import { downloadResultsCSV, downloadResultsExcel } from "@/services/exportApi";

interface BinaryMetrics {
  // Basic metrics
  accuracy: number;
  precision: number;
  recall: number;
  f1_score: number;
  specificity: number;
  
  // ROC metrics
  roc_auc: number;
  pr_auc: number;
  
  // Threshold metrics
  optimal_threshold: number;
  threshold_strategy: string;
  
  // Confusion matrix
  true_positives: number;
  true_negatives: number;
  false_positives: number;
  false_negatives: number;
  
  // Additional metrics
  balanced_accuracy: number;
  matthews_correlation: number;
  log_loss: number;
  brier_score: number;
}

interface ROCData {
  fpr: number[];
  tpr: number[];
  thresholds: number[];
}

interface PRData {
  precision: number[];
  recall: number[];
  thresholds: number[];
}

interface BinaryResultsProps {
  results: any[];
  metrics: BinaryMetrics;
  rocData: ROCData;
  prData: PRData;
  thresholdData: any[];
  modelName: string;
  positiveClass: string;
  negativeClass: string;
  trainingTime?: number;
  onExport?: (format: 'csv' | 'excel') => void;
  onShare?: () => void;
  onThresholdChange?: (threshold: number) => void;
}

export const BinaryResults: React.FC<BinaryResultsProps> = ({
  results,
  metrics,
  rocData,
  prData,
  thresholdData,
  modelName,
  positiveClass,
  negativeClass,
  trainingTime,
  onExport,
  onShare,
  onThresholdChange
}) => {
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState('overview');
  const [selectedThreshold, setSelectedThreshold] = useState(metrics.optimal_threshold);

  const handleExport = async (format: 'csv' | 'excel') => {
    try {
      if (onExport) {
        onExport(format);
      } else {
        // Default export functionality
        if (format === 'csv') {
          await downloadResultsCSV(results, `binary-results-${Date.now()}`);
        } else {
          await downloadResultsExcel(results, `binary-results-${Date.now()}`);
        }
      }
      
      toast({
        title: "Export successful",
        description: `Results exported as ${format.toUpperCase()}`
      });
    } catch (error) {
      toast({
        title: "Export failed",
        description: "Failed to export results",
        variant: "destructive"
      });
    }
  };

  const handleThresholdChange = (threshold: number) => {
    setSelectedThreshold(threshold);
    if (onThresholdChange) {
      onThresholdChange(threshold);
    }
  };

  const getMetricColor = (value: number, isLoss: boolean = false) => {
    if (isLoss) {
      // For loss metrics, lower is better
      if (value < 0.1) return 'text-green-600';
      if (value < 0.3) return 'text-yellow-600';
      return 'text-red-600';
    } else {
      // For performance metrics, higher is better
      if (value > 0.8) return 'text-green-600';
      if (value > 0.6) return 'text-yellow-600';
      return 'text-red-600';
    }
  };

  const getPerformanceLevel = (value: number, isLoss: boolean = false) => {
    if (isLoss) {
      if (value < 0.1) return 'Excellent';
      if (value < 0.3) return 'Good';
      if (value < 0.5) return 'Fair';
      return 'Poor';
    } else {
      if (value > 0.9) return 'Excellent';
      if (value > 0.8) return 'Good';
      if (value > 0.6) return 'Fair';
      return 'Poor';
    }
  };

  // Prepare ROC data for visualization
  const rocChartData = [{
    modelId: 'binary-model',
    modelName: modelName,
    points: rocData.fpr.map((fpr, idx) => ({
      fpr,
      tpr: rocData.tpr[idx],
      threshold: rocData.thresholds[idx]
    })),
    auc: metrics.roc_auc,
    color: '#3b82f6',
    optimalThreshold: metrics.optimal_threshold
  }];

  // Prepare PR data for visualization
  const prChartData = [{
    modelId: 'binary-model',
    modelName: modelName,
    points: prData.precision.map((precision, idx) => ({
      precision,
      recall: prData.recall[idx],
      threshold: prData.thresholds[idx]
    })),
    auc: metrics.pr_auc,
    color: '#22c55e'
  }];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Binary Classification Results</h2>
          <p className="text-muted-foreground">
            Performance analysis of {modelName} ({positiveClass} vs {negativeClass})
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={() => handleExport('csv')}>
            <Download className="w-4 h-4 mr-2" />
            Export CSV
          </Button>
          <Button variant="outline" onClick={() => handleExport('excel')}>
            <Download className="w-4 h-4 mr-2" />
            Export Excel
          </Button>
          {onShare && (
            <Button variant="outline" onClick={onShare}>
              <Share2 className="w-4 h-4 mr-2" />
              Share
            </Button>
          )}
        </div>
      </div>

      {/* Results Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="roc">ROC Analysis</TabsTrigger>
          <TabsTrigger value="threshold">Threshold</TabsTrigger>
          <TabsTrigger value="confusion">Confusion Matrix</TabsTrigger>
          <TabsTrigger value="data">Data</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          {/* Key Metrics Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Accuracy</CardTitle>
              </CardHeader>
              <CardContent>
                <div className={`text-2xl font-bold ${getMetricColor(metrics.accuracy)}`}>
                  {(metrics.accuracy * 100).toFixed(1)}%
                </div>
                <p className="text-xs text-muted-foreground">
                  {getPerformanceLevel(metrics.accuracy)}
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">F1 Score</CardTitle>
              </CardHeader>
              <CardContent>
                <div className={`text-2xl font-bold ${getMetricColor(metrics.f1_score)}`}>
                  {metrics.f1_score.toFixed(3)}
                </div>
                <p className="text-xs text-muted-foreground">
                  {getPerformanceLevel(metrics.f1_score)}
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">ROC AUC</CardTitle>
              </CardHeader>
              <CardContent>
                <div className={`text-2xl font-bold ${getMetricColor(metrics.roc_auc)}`}>
                  {metrics.roc_auc.toFixed(3)}
                </div>
                <p className="text-xs text-muted-foreground">
                  {getPerformanceLevel(metrics.roc_auc)}
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Precision</CardTitle>
              </CardHeader>
              <CardContent>
                <div className={`text-2xl font-bold ${getMetricColor(metrics.precision)}`}>
                  {(metrics.precision * 100).toFixed(1)}%
                </div>
                <p className="text-xs text-muted-foreground">
                  {getPerformanceLevel(metrics.precision)}
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Performance Summary */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="w-5 h-5" />
                Performance Summary
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Recall (Sensitivity)</span>
                    <span className="text-sm">{(metrics.recall * 100).toFixed(1)}%</span>
                  </div>
                  <Progress value={metrics.recall * 100} />
                  
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Specificity</span>
                    <span className="text-sm">{(metrics.specificity * 100).toFixed(1)}%</span>
                  </div>
                  <Progress value={metrics.specificity * 100} />
                </div>
                
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Balanced Accuracy</span>
                    <span className="text-sm">{(metrics.balanced_accuracy * 100).toFixed(1)}%</span>
                  </div>
                  <Progress value={metrics.balanced_accuracy * 100} />
                  
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Matthews Correlation</span>
                    <span className="text-sm">{metrics.matthews_correlation.toFixed(3)}</span>
                  </div>
                  <Progress value={(metrics.matthews_correlation + 1) * 50} />
                </div>
              </div>

              {trainingTime && (
                <Alert>
                  <Activity className="h-4 w-4" />
                  <AlertDescription>
                    <strong>Training completed in {Math.round(trainingTime / 60)} minutes</strong>
                    <br />
                    Model processed {results.length} samples with optimal threshold: {metrics.optimal_threshold.toFixed(3)}
                  </AlertDescription>
                </Alert>
              )}
            </CardContent>
          </Card>

          {/* Threshold Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Scale className="w-5 h-5" />
                Threshold Analysis
              </CardTitle>
              <CardDescription>
                Current threshold configuration and optimization results
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="text-center p-4 border rounded-lg">
                  <div className="text-2xl font-bold text-blue-600">
                    {metrics.optimal_threshold.toFixed(3)}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    Optimal Threshold
                  </div>
                  <Badge variant="outline" className="mt-2">
                    {metrics.threshold_strategy}
                  </Badge>
                </div>
                
                <div className="text-center p-4 border rounded-lg">
                  <div className="text-2xl font-bold text-green-600">
                    {metrics.true_positives}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    True Positives
                  </div>
                </div>
                
                <div className="text-center p-4 border rounded-lg">
                  <div className="text-2xl font-bold text-red-600">
                    {metrics.false_positives}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    False Positives
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* ROC Analysis Tab */}
        <TabsContent value="roc" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="w-5 h-5" />
                ROC Curve Analysis
              </CardTitle>
              <CardDescription>
                Receiver Operating Characteristic and Precision-Recall curves
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ROCCurveChart
                rocData={rocChartData}
                prData={prChartData}
                selectedThreshold={selectedThreshold}
                onThresholdChange={handleThresholdChange}
                showPRCurve={true}
                showOptimalPoint={true}
                interactive={true}
                height={400}
              />
            </CardContent>
          </Card>
        </TabsContent>

        {/* Threshold Optimization Tab */}
        <TabsContent value="threshold" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Scale className="w-5 h-5" />
                Threshold Optimization
              </CardTitle>
              <CardDescription>
                Interactive threshold optimization and strategy comparison
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ThresholdOptimizer
                thresholdData={thresholdData}
                selectedThreshold={selectedThreshold}
                onThresholdChange={handleThresholdChange}
                positiveClass={positiveClass}
                negativeClass={negativeClass}
                showStrategyComparison={true}
                interactive={true}
              />
            </CardContent>
          </Card>
        </TabsContent>

        {/* Confusion Matrix Tab */}
        <TabsContent value="confusion" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <PieChart className="w-5 h-5" />
                Confusion Matrix
              </CardTitle>
              <CardDescription>
                Detailed breakdown of classification results
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Confusion Matrix Visualization */}
              <div className="flex justify-center">
                <div className="grid grid-cols-3 gap-2 text-center">
                  {/* Header row */}
                  <div></div>
                  <div className="font-medium text-sm">Predicted {negativeClass}</div>
                  <div className="font-medium text-sm">Predicted {positiveClass}</div>

                  {/* Actual Negative row */}
                  <div className="font-medium text-sm">Actual {negativeClass}</div>
                  <div className="w-24 h-24 bg-green-100 border-2 border-green-300 rounded-lg flex flex-col items-center justify-center">
                    <div className="text-2xl font-bold text-green-700">{metrics.true_negatives}</div>
                    <div className="text-xs text-green-600">TN</div>
                  </div>
                  <div className="w-24 h-24 bg-red-100 border-2 border-red-300 rounded-lg flex flex-col items-center justify-center">
                    <div className="text-2xl font-bold text-red-700">{metrics.false_positives}</div>
                    <div className="text-xs text-red-600">FP</div>
                  </div>

                  {/* Actual Positive row */}
                  <div className="font-medium text-sm">Actual {positiveClass}</div>
                  <div className="w-24 h-24 bg-red-100 border-2 border-red-300 rounded-lg flex flex-col items-center justify-center">
                    <div className="text-2xl font-bold text-red-700">{metrics.false_negatives}</div>
                    <div className="text-xs text-red-600">FN</div>
                  </div>
                  <div className="w-24 h-24 bg-green-100 border-2 border-green-300 rounded-lg flex flex-col items-center justify-center">
                    <div className="text-2xl font-bold text-green-700">{metrics.true_positives}</div>
                    <div className="text-xs text-green-600">TP</div>
                  </div>
                </div>
              </div>

              {/* Metrics derived from confusion matrix */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm">Classification Metrics</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-sm">Sensitivity (Recall)</span>
                      <span className="font-medium">{(metrics.recall * 100).toFixed(1)}%</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm">Specificity</span>
                      <span className="font-medium">{(metrics.specificity * 100).toFixed(1)}%</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm">Precision (PPV)</span>
                      <span className="font-medium">{(metrics.precision * 100).toFixed(1)}%</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm">Negative Predictive Value</span>
                      <span className="font-medium">
                        {((metrics.true_negatives / (metrics.true_negatives + metrics.false_negatives)) * 100).toFixed(1)}%
                      </span>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm">Error Analysis</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-sm">False Positive Rate</span>
                      <span className="font-medium">
                        {((metrics.false_positives / (metrics.false_positives + metrics.true_negatives)) * 100).toFixed(1)}%
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm">False Negative Rate</span>
                      <span className="font-medium">
                        {((metrics.false_negatives / (metrics.false_negatives + metrics.true_positives)) * 100).toFixed(1)}%
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm">False Discovery Rate</span>
                      <span className="font-medium">
                        {((metrics.false_positives / (metrics.false_positives + metrics.true_positives)) * 100).toFixed(1)}%
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm">False Omission Rate</span>
                      <span className="font-medium">
                        {((metrics.false_negatives / (metrics.false_negatives + metrics.true_negatives)) * 100).toFixed(1)}%
                      </span>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Additional Insights */}
              <Alert>
                <Info className="h-4 w-4" />
                <AlertDescription>
                  <strong>Model Performance Summary:</strong>
                  <br />
                  • Total samples: {metrics.true_positives + metrics.true_negatives + metrics.false_positives + metrics.false_negatives}
                  <br />
                  • Correct predictions: {metrics.true_positives + metrics.true_negatives} ({((metrics.true_positives + metrics.true_negatives) / (metrics.true_positives + metrics.true_negatives + metrics.false_positives + metrics.false_negatives) * 100).toFixed(1)}%)
                  <br />
                  • Incorrect predictions: {metrics.false_positives + metrics.false_negatives} ({((metrics.false_positives + metrics.false_negatives) / (metrics.true_positives + metrics.true_negatives + metrics.false_positives + metrics.false_negatives) * 100).toFixed(1)}%)
                </AlertDescription>
              </Alert>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Raw Data Tab */}
        <TabsContent value="data" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="w-5 h-5" />
                Classification Results
              </CardTitle>
              <CardDescription>
                Raw classification results with predictions and confidence scores
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ResultsDataTable 
                data={results}
                onExport={handleExport}
              />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};
