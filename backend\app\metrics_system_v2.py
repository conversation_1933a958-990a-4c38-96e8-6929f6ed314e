"""Comprehensive Metrics System v2 for ClassyWeb ML Platform Phase 2.

This module implements advanced metrics collection, storage, and real-time monitoring
with WebSocket support, GPU utilization tracking, and performance benchmarking.
"""

import logging
import time
import asyncio
import json
from typing import Dict, Any, List, Optional, Callable
from datetime import datetime, timezone
from dataclasses import dataclass, asdict
from sqlalchemy.orm import Session
import psutil
import GPUtil
import numpy as np

from .database import get_db, TrainingSession, ModelPerformance, TrainingStatusEnum

logger = logging.getLogger(__name__)


@dataclass
class SystemMetrics:
    """System resource metrics."""
    timestamp: float
    cpu_percent: float
    memory_percent: float
    memory_used_gb: float
    memory_total_gb: float
    disk_usage_percent: float
    gpu_metrics: List[Dict[str, Any]]


@dataclass
class TrainingMetrics:
    """Training progress metrics."""
    timestamp: float
    epoch: int
    step: int
    loss: float
    learning_rate: float
    accuracy: Optional[float] = None
    f1_score: Optional[float] = None
    validation_loss: Optional[float] = None
    validation_accuracy: Optional[float] = None
    gradient_norm: Optional[float] = None


@dataclass
class PerformanceBenchmark:
    """Performance benchmark data."""
    model_id: str
    classification_type: str
    dataset_size: int
    training_time: float
    inference_time_ms: float
    memory_peak_gb: float
    gpu_utilization_avg: float
    throughput_samples_per_sec: float
    model_size_mb: float


class SystemMonitor:
    """System resource monitoring with GPU support."""
    
    def __init__(self):
        self.monitoring = False
        self.metrics_history = []
        self.gpu_available = GPUtil.getGPUs() is not None
        
    def get_current_metrics(self) -> SystemMetrics:
        """Get current system metrics."""
        # CPU and Memory
        cpu_percent = psutil.cpu_percent(interval=0.1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        
        # GPU metrics
        gpu_metrics = []
        try:
            if self.gpu_available:
                gpus = GPUtil.getGPUs()
                for gpu in gpus:
                    gpu_metrics.append({
                        'id': gpu.id,
                        'name': gpu.name,
                        'utilization': gpu.load * 100,
                        'memory_used': gpu.memoryUsed,
                        'memory_total': gpu.memoryTotal,
                        'memory_percent': gpu.memoryUtil * 100,
                        'temperature': gpu.temperature,
                        'power_draw': getattr(gpu, 'powerDraw', None),
                        'power_limit': getattr(gpu, 'powerLimit', None)
                    })
        except Exception as e:
            logger.warning(f"Failed to get GPU metrics: {e}")
        
        return SystemMetrics(
            timestamp=time.time(),
            cpu_percent=cpu_percent,
            memory_percent=memory.percent,
            memory_used_gb=memory.used / (1024**3),
            memory_total_gb=memory.total / (1024**3),
            disk_usage_percent=disk.percent,
            gpu_metrics=gpu_metrics
        )
    
    def start_monitoring(self, interval: float = 1.0):
        """Start system monitoring."""
        self.monitoring = True
        self.metrics_history = []
        
        async def monitor_loop():
            while self.monitoring:
                try:
                    metrics = self.get_current_metrics()
                    self.metrics_history.append(metrics)
                    
                    # Keep only last 1000 metrics to prevent memory issues
                    if len(self.metrics_history) > 1000:
                        self.metrics_history = self.metrics_history[-1000:]
                    
                    await asyncio.sleep(interval)
                except Exception as e:
                    logger.error(f"Error in monitoring loop: {e}")
                    await asyncio.sleep(interval)
        
        # Start monitoring in background
        asyncio.create_task(monitor_loop())
    
    def stop_monitoring(self):
        """Stop system monitoring."""
        self.monitoring = False
    
    def get_monitoring_summary(self) -> Dict[str, Any]:
        """Get summary of monitoring data."""
        if not self.metrics_history:
            return {'monitored': False}
        
        # Calculate statistics
        cpu_values = [m.cpu_percent for m in self.metrics_history]
        memory_values = [m.memory_percent for m in self.metrics_history]
        
        gpu_stats = {}
        if self.metrics_history[0].gpu_metrics:
            gpu_utilization = []
            gpu_memory = []
            gpu_temperature = []
            
            for metrics in self.metrics_history:
                for gpu in metrics.gpu_metrics:
                    gpu_utilization.append(gpu['utilization'])
                    gpu_memory.append(gpu['memory_percent'])
                    gpu_temperature.append(gpu['temperature'])
            
            gpu_stats = {
                'utilization': {
                    'avg': np.mean(gpu_utilization),
                    'max': np.max(gpu_utilization),
                    'min': np.min(gpu_utilization)
                },
                'memory': {
                    'avg': np.mean(gpu_memory),
                    'max': np.max(gpu_memory),
                    'min': np.min(gpu_memory)
                },
                'temperature': {
                    'avg': np.mean(gpu_temperature),
                    'max': np.max(gpu_temperature),
                    'min': np.min(gpu_temperature)
                }
            }
        
        return {
            'monitored': True,
            'duration': self.metrics_history[-1].timestamp - self.metrics_history[0].timestamp,
            'samples': len(self.metrics_history),
            'cpu': {
                'avg': np.mean(cpu_values),
                'max': np.max(cpu_values),
                'min': np.min(cpu_values)
            },
            'memory': {
                'avg': np.mean(memory_values),
                'max': np.max(memory_values),
                'min': np.min(memory_values),
                'peak_gb': max(m.memory_used_gb for m in self.metrics_history)
            },
            'gpu': gpu_stats
        }


class TrainingProgressTracker:
    """Track training progress with detailed metrics."""
    
    def __init__(self, session_id: str):
        self.session_id = session_id
        self.training_metrics = []
        self.start_time = time.time()
        self.current_epoch = 0
        self.total_epochs = 0
        
    def log_training_step(
        self,
        epoch: int,
        step: int,
        loss: float,
        learning_rate: float,
        **kwargs
    ):
        """Log a training step."""
        metrics = TrainingMetrics(
            timestamp=time.time(),
            epoch=epoch,
            step=step,
            loss=loss,
            learning_rate=learning_rate,
            **kwargs
        )
        
        self.training_metrics.append(metrics)
        self.current_epoch = epoch
        
        # Update database
        self._update_session_progress(metrics)
    
    def _update_session_progress(self, metrics: TrainingMetrics):
        """Update training session progress in database."""
        try:
            with Session(bind=get_db().bind) as db:
                session = db.query(TrainingSession).filter(
                    TrainingSession.id == self.session_id
                ).first()
                
                if session:
                    # Calculate progress percentage
                    if self.total_epochs > 0:
                        progress = (metrics.epoch / self.total_epochs) * 100
                    else:
                        progress = 0
                    
                    session.progress_percentage = min(progress, 99.0)  # Cap at 99% until complete
                    session.current_stage = f"epoch_{metrics.epoch}_step_{metrics.step}"
                    
                    # Store latest metrics
                    session.progress_data = {
                        'current_epoch': metrics.epoch,
                        'current_step': metrics.step,
                        'current_loss': metrics.loss,
                        'learning_rate': metrics.learning_rate,
                        'timestamp': metrics.timestamp
                    }
                    
                    db.commit()
        except Exception as e:
            logger.error(f"Failed to update session progress: {e}")
    
    def get_training_summary(self) -> Dict[str, Any]:
        """Get training summary statistics."""
        if not self.training_metrics:
            return {'tracked': False}
        
        losses = [m.loss for m in self.training_metrics]
        learning_rates = [m.learning_rate for m in self.training_metrics]
        
        summary = {
            'tracked': True,
            'duration': time.time() - self.start_time,
            'total_steps': len(self.training_metrics),
            'current_epoch': self.current_epoch,
            'loss': {
                'current': losses[-1],
                'min': min(losses),
                'max': max(losses),
                'avg': np.mean(losses)
            },
            'learning_rate': {
                'current': learning_rates[-1],
                'min': min(learning_rates),
                'max': max(learning_rates)
            }
        }
        
        # Add accuracy metrics if available
        accuracies = [m.accuracy for m in self.training_metrics if m.accuracy is not None]
        if accuracies:
            summary['accuracy'] = {
                'current': accuracies[-1],
                'max': max(accuracies),
                'avg': np.mean(accuracies)
            }
        
        return summary


class PerformanceBenchmarker:
    """Benchmark model performance across different metrics."""
    
    def __init__(self):
        self.benchmarks = []
    
    def benchmark_training(
        self,
        model_id: str,
        classification_type: str,
        dataset_size: int,
        training_time: float,
        system_summary: Dict[str, Any],
        model_size_mb: float
    ) -> PerformanceBenchmark:
        """Benchmark training performance."""
        benchmark = PerformanceBenchmark(
            model_id=model_id,
            classification_type=classification_type,
            dataset_size=dataset_size,
            training_time=training_time,
            inference_time_ms=0.0,  # To be measured separately
            memory_peak_gb=system_summary.get('memory', {}).get('peak_gb', 0.0),
            gpu_utilization_avg=system_summary.get('gpu', {}).get('utilization', {}).get('avg', 0.0),
            throughput_samples_per_sec=dataset_size / training_time if training_time > 0 else 0.0,
            model_size_mb=model_size_mb
        )
        
        self.benchmarks.append(benchmark)
        return benchmark
    
    def benchmark_inference(
        self,
        model_id: str,
        texts: List[str],
        inference_times: List[float]
    ) -> Dict[str, Any]:
        """Benchmark inference performance."""
        if not inference_times:
            return {'benchmarked': False}
        
        avg_time_ms = np.mean(inference_times) * 1000
        throughput = len(texts) / sum(inference_times) if sum(inference_times) > 0 else 0
        
        # Update existing benchmark if found
        for benchmark in self.benchmarks:
            if benchmark.model_id == model_id:
                benchmark.inference_time_ms = avg_time_ms
                benchmark.throughput_samples_per_sec = max(
                    benchmark.throughput_samples_per_sec, throughput
                )
                break
        
        return {
            'benchmarked': True,
            'avg_inference_time_ms': avg_time_ms,
            'min_inference_time_ms': min(inference_times) * 1000,
            'max_inference_time_ms': max(inference_times) * 1000,
            'throughput_samples_per_sec': throughput,
            'total_samples': len(texts)
        }
    
    def get_comparative_analysis(self, classification_type: str) -> Dict[str, Any]:
        """Get comparative analysis for a classification type."""
        relevant_benchmarks = [
            b for b in self.benchmarks 
            if b.classification_type == classification_type
        ]
        
        if not relevant_benchmarks:
            return {'available': False}
        
        # Calculate statistics
        training_times = [b.training_time for b in relevant_benchmarks]
        inference_times = [b.inference_time_ms for b in relevant_benchmarks if b.inference_time_ms > 0]
        throughputs = [b.throughput_samples_per_sec for b in relevant_benchmarks]
        
        analysis = {
            'available': True,
            'total_models': len(relevant_benchmarks),
            'training_time': {
                'avg': np.mean(training_times),
                'min': min(training_times),
                'max': max(training_times)
            },
            'throughput': {
                'avg': np.mean(throughputs),
                'min': min(throughputs),
                'max': max(throughputs)
            }
        }
        
        if inference_times:
            analysis['inference_time'] = {
                'avg': np.mean(inference_times),
                'min': min(inference_times),
                'max': max(inference_times)
            }
        
        return analysis


class ComprehensiveMetricsSystem:
    """Comprehensive metrics system coordinating all monitoring components."""
    
    def __init__(self):
        self.system_monitor = SystemMonitor()
        self.performance_benchmarker = PerformanceBenchmarker()
        self.active_trackers = {}  # session_id -> TrainingProgressTracker
        
    def start_training_monitoring(self, session_id: str, total_epochs: int = 0) -> TrainingProgressTracker:
        """Start monitoring for a training session."""
        tracker = TrainingProgressTracker(session_id)
        tracker.total_epochs = total_epochs
        self.active_trackers[session_id] = tracker
        
        # Start system monitoring if not already running
        if not self.system_monitor.monitoring:
            self.system_monitor.start_monitoring()
        
        return tracker
    
    def stop_training_monitoring(self, session_id: str) -> Dict[str, Any]:
        """Stop monitoring for a training session and return summary."""
        if session_id not in self.active_trackers:
            return {'tracked': False}
        
        tracker = self.active_trackers[session_id]
        training_summary = tracker.get_training_summary()
        system_summary = self.system_monitor.get_monitoring_summary()
        
        # Remove tracker
        del self.active_trackers[session_id]
        
        # Stop system monitoring if no active trackers
        if not self.active_trackers:
            self.system_monitor.stop_monitoring()
        
        return {
            'training_summary': training_summary,
            'system_summary': system_summary
        }
    
    def get_session_metrics(self, session_id: str) -> Dict[str, Any]:
        """Get current metrics for a training session."""
        if session_id not in self.active_trackers:
            return {'active': False}
        
        tracker = self.active_trackers[session_id]
        current_system = self.system_monitor.get_current_metrics()
        
        return {
            'active': True,
            'training_summary': tracker.get_training_summary(),
            'current_system': asdict(current_system),
            'session_id': session_id
        }


# Global metrics system instance
metrics_system = ComprehensiveMetricsSystem()
