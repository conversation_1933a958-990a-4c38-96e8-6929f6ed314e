/**
 * Comprehensive test for the BeginnerWorkflow component.
 * 
 * This test validates the frontend workflow components and their interactions.
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import BeginnerWorkflow from '../pages/BeginnerWorkflow';

// Mock the services
vi.mock('../services/fileUploadApi', () => ({
  uploadFile: vi.fn(),
}));

vi.mock('../services/dataAnalysisApi', () => ({
  analyzeFileStructure: vi.fn(),
  getWorkflowRecommendations: vi.fn(),
}));

vi.mock('../services/universalApi', () => ({
  startLLMClassificationOnFile: vi.fn(),
  startCustomTrainingOnFile: vi.fn(),
  getUniversalTaskStatus: vi.fn(),
}));

// Mock the toast hook
vi.mock('../hooks/use-toast', () => ({
  useToast: () => ({
    toast: vi.fn(),
  }),
}));

// Mock the navigation hook
const mockNavigate = vi.fn();
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useNavigate: () => mockNavigate,
  };
});

// Test wrapper component
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <BrowserRouter>
    {children}
  </BrowserRouter>
);

describe('BeginnerWorkflow', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders the initial upload step', () => {
    render(
      <TestWrapper>
        <BeginnerWorkflow />
      </TestWrapper>
    );

    // Check if the upload step is visible
    expect(screen.getByText('Upload Classification Data')).toBeInTheDocument();
    expect(screen.getByText('Upload new data you want to classify')).toBeInTheDocument();
    
    // Check if the progress indicator shows step 1
    expect(screen.getByText('Step 1 of 6')).toBeInTheDocument();
  });

  it('shows the correct step titles in the sidebar', () => {
    render(
      <TestWrapper>
        <BeginnerWorkflow />
      </TestWrapper>
    );

    // Check if all step titles are present
    expect(screen.getByText('Upload Sample Data')).toBeInTheDocument();
    expect(screen.getByText('Smart Detection')).toBeInTheDocument();
    expect(screen.getByText('Configure & Recommend')).toBeInTheDocument();
    expect(screen.getByText('Upload Training Data')).toBeInTheDocument();
    expect(screen.getByText('Train & Classify')).toBeInTheDocument();
    expect(screen.getByText('Results & Deploy')).toBeInTheDocument();
  });

  it('handles file upload correctly', async () => {
    const { uploadFile } = await import('../services/fileUploadApi');
    const mockUploadFile = uploadFile as any;
    
    // Mock successful file upload
    mockUploadFile.mockResolvedValue({
      file_id: 'test-file-id',
      filename: 'test.csv',
      columns: ['text', 'label'],
      num_rows: 100,
      preview: [
        { text: 'Sample text', label: 'positive' }
      ]
    });

    render(
      <TestWrapper>
        <BeginnerWorkflow />
      </TestWrapper>
    );

    // Create a mock file
    const file = new File(['test content'], 'test.csv', { type: 'text/csv' });
    
    // Find the file input (it might be hidden)
    const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement;
    expect(fileInput).toBeInTheDocument();

    // Simulate file selection
    Object.defineProperty(fileInput, 'files', {
      value: [file],
      writable: false,
    });

    fireEvent.change(fileInput);

    // Wait for the upload to complete
    await waitFor(() => {
      expect(mockUploadFile).toHaveBeenCalledWith(file);
    });
  });

  it('progresses through workflow steps correctly', async () => {
    const { uploadFile } = await import('../services/fileUploadApi');
    const { analyzeFileStructure } = await import('../services/dataAnalysisApi');
    
    const mockUploadFile = uploadFile as any;
    const mockAnalyzeFile = analyzeFileStructure as any;
    
    // Mock successful responses
    mockUploadFile.mockResolvedValue({
      file_id: 'test-file-id',
      filename: 'test.csv',
      columns: ['text', 'label'],
      num_rows: 100,
      preview: []
    });

    mockAnalyzeFile.mockResolvedValue({
      detected_structure: 'flat',
      confidence: 0.9,
      suggestions: [{
        structure_type: 'flat',
        confidence: 0.9,
        reasoning: 'Test reasoning',
        suggested_config: {
          workflow_type: 'flat',
          classification_type: 'binary',
          text_column: 'text',
          label_columns: ['label']
        },
        preview_data: {}
      }],
      preview: {
        sample_rows: [],
        text_column: 'text',
        label_columns: ['label'],
        total_rows: 100,
        total_columns: 2
      },
      column_analysis: {},
      data_quality: {
        total_rows: 100,
        total_columns: 2,
        missing_data_percentage: 0,
        duplicate_rows: 0,
        empty_rows: 0,
        data_types: {}
      }
    });

    render(
      <TestWrapper>
        <BeginnerWorkflow />
      </TestWrapper>
    );

    // Step 1: Upload file
    const file = new File(['test content'], 'test.csv', { type: 'text/csv' });
    const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement;
    
    Object.defineProperty(fileInput, 'files', {
      value: [file],
      writable: false,
    });

    fireEvent.change(fileInput);

    // Wait for upload and move to next step
    await waitFor(() => {
      expect(mockUploadFile).toHaveBeenCalled();
    });

    // Find and click continue button
    const continueButton = screen.getByText('Continue to Analysis');
    fireEvent.click(continueButton);

    // Should now be on step 2
    await waitFor(() => {
      expect(screen.getByText('Step 2 of 6')).toBeInTheDocument();
    });
  });

  it('handles errors gracefully', async () => {
    const { uploadFile } = await import('../services/fileUploadApi');
    const mockUploadFile = uploadFile as any;
    
    // Mock failed file upload
    mockUploadFile.mockRejectedValue(new Error('Upload failed'));

    render(
      <TestWrapper>
        <BeginnerWorkflow />
      </TestWrapper>
    );

    const file = new File(['test content'], 'test.csv', { type: 'text/csv' });
    const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement;
    
    Object.defineProperty(fileInput, 'files', {
      value: [file],
      writable: false,
    });

    fireEvent.change(fileInput);

    // Wait for error handling
    await waitFor(() => {
      expect(mockUploadFile).toHaveBeenCalled();
    });

    // Should show error state (exact implementation depends on error handling)
    // This test ensures the component doesn't crash on errors
  });

  it('validates required fields before proceeding', () => {
    render(
      <TestWrapper>
        <BeginnerWorkflow />
      </TestWrapper>
    );

    // Try to find a continue button (should be disabled initially)
    const continueButton = screen.queryByText('Continue to Analysis');
    
    if (continueButton) {
      // If button exists, it should be disabled when no file is uploaded
      expect(continueButton).toBeDisabled();
    }
  });

  it('shows appropriate loading states', async () => {
    const { uploadFile } = await import('../services/fileUploadApi');
    const mockUploadFile = uploadFile as any;
    
    // Mock slow upload
    mockUploadFile.mockImplementation(() => 
      new Promise(resolve => setTimeout(() => resolve({
        file_id: 'test-file-id',
        filename: 'test.csv',
        columns: ['text', 'label'],
        num_rows: 100,
        preview: []
      }), 100))
    );

    render(
      <TestWrapper>
        <BeginnerWorkflow />
      </TestWrapper>
    );

    const file = new File(['test content'], 'test.csv', { type: 'text/csv' });
    const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement;
    
    Object.defineProperty(fileInput, 'files', {
      value: [file],
      writable: false,
    });

    fireEvent.change(fileInput);

    // Should show loading state
    await waitFor(() => {
      // Look for loading indicators (spinner, progress bar, etc.)
      const loadingElements = screen.queryAllByText(/uploading|loading/i);
      // This test ensures loading states are shown
    });
  });
});
