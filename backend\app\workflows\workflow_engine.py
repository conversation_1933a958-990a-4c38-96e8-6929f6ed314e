"""Dynamic Workflow Engine for ClassyWeb Universal Platform."""

import logging
import json
import uuid
from typing import Dict, List, Any, Optional, Union
from datetime import datetime, timezone
from dataclasses import dataclass, field
from enum import Enum

logger = logging.getLogger(__name__)


class WorkflowStepType(Enum):
    """Types of workflow steps."""
    DATA_LOAD = "data_load"
    MODEL_TRAINING = "model_training"
    MODEL_EVALUATION = "model_evaluation"


class WorkflowStepStatus(Enum):
    """Status of workflow steps."""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    SKIPPED = "skipped"


@dataclass
class WorkflowStepConfig:
    """Configuration for a workflow step."""
    step_type: WorkflowStepType
    name: str
    description: str
    required: bool = True
    depends_on: List[str] = field(default_factory=list)
    parameters: Dict[str, Any] = field(default_factory=dict)
    timeout_seconds: int = 300
    retry_count: int = 3
    plugin_name: Optional[str] = None


@dataclass
class WorkflowStep:
    """Individual step in a workflow."""
    id: str
    config: WorkflowStepConfig
    status: WorkflowStepStatus = WorkflowStepStatus.PENDING
    result: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    execution_time_ms: Optional[float] = None
    retry_attempts: int = 0


@dataclass
class WorkflowTemplate:
    """Template for creating workflows."""
    id: str
    name: str
    description: str
    category: str
    steps: List[WorkflowStepConfig]
    metadata: Dict[str, Any] = field(default_factory=dict)
    created_by: Optional[str] = None
    created_at: Optional[datetime] = None
    is_public: bool = False
    tags: List[str] = field(default_factory=list)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert template to dictionary."""
        return {
            "id": self.id,
            "name": self.name,
            "description": self.description,
            "category": self.category,
            "steps": [
                {
                    "step_type": step.step_type.value,
                    "name": step.name,
                    "description": step.description,
                    "required": step.required,
                    "depends_on": step.depends_on,
                    "parameters": step.parameters,
                    "timeout_seconds": step.timeout_seconds,
                    "retry_count": step.retry_count,
                    "plugin_name": step.plugin_name
                }
                for step in self.steps
            ],
            "metadata": self.metadata,
            "created_by": self.created_by,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "is_public": self.is_public,
            "tags": self.tags
        }


class WorkflowEngine:
    """Core workflow engine for managing dynamic workflows."""
    
    def __init__(self):
        self.active_workflows: Dict[str, Dict[str, Any]] = {}
        self.workflow_templates: Dict[str, WorkflowTemplate] = {}
        self.step_executors: Dict[str, Any] = {}
        
        # Initialize built-in step executors
        self._initialize_builtin_executors()
        
        logger.info("Workflow Engine initialized")
    
    def create_workflow(
        self,
        user_id: int,
        template_id: Optional[str] = None,
        custom_steps: Optional[List[WorkflowStepConfig]] = None,
        workflow_name: str = None
    ) -> str:
        """Create a new workflow instance."""
        try:
            workflow_id = str(uuid.uuid4())
            
            # Get steps from template or use custom steps
            if template_id and template_id in self.workflow_templates:
                template = self.workflow_templates[template_id]
                steps_config = template.steps
                workflow_name = workflow_name or f"{template.name} - {datetime.now().strftime('%Y%m%d_%H%M%S')}"
            elif custom_steps:
                steps_config = custom_steps
                workflow_name = workflow_name or f"Custom Workflow - {datetime.now().strftime('%Y%m%d_%H%M%S')}"
            else:
                raise ValueError("Either template_id or custom_steps must be provided")
            
            # Create workflow steps
            steps = []
            for i, step_config in enumerate(steps_config):
                step = WorkflowStep(
                    id=f"{workflow_id}_step_{i}",
                    config=step_config
                )
                steps.append(step)
            
            # Create workflow
            workflow = {
                "id": workflow_id,
                "name": workflow_name,
                "user_id": user_id,
                "template_id": template_id,
                "steps": steps,
                "status": "created",
                "created_at": datetime.now(timezone.utc),
                "updated_at": datetime.now(timezone.utc),
                "context": {},
                "results": {}
            }
            
            self.active_workflows[workflow_id] = workflow
            
            logger.info(f"Created workflow {workflow_id} for user {user_id}")
            return workflow_id
            
        except Exception as e:
            logger.error(f"Error creating workflow: {str(e)}")
            raise
    
    async def execute_workflow(
        self,
        workflow_id: str,
        input_data: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Execute a workflow step by step."""
        try:
            if workflow_id not in self.active_workflows:
                raise ValueError(f"Workflow {workflow_id} not found")
            
            workflow = self.active_workflows[workflow_id]
            workflow["status"] = "running"
            workflow["started_at"] = datetime.now(timezone.utc)
            
            if input_data:
                workflow["context"].update(input_data)
            
            logger.info(f"Starting execution of workflow {workflow_id}")
            
            # Execute steps in order
            for step in workflow["steps"]:
                if step.status == WorkflowStepStatus.COMPLETED:
                    continue  # Skip already completed steps
                
                # Check dependencies
                if not await self._check_step_dependencies(step, workflow):
                    step.status = WorkflowStepStatus.FAILED
                    step.error_message = "Dependencies not satisfied"
                    continue
                
                # Execute step
                success = await self._execute_step(step, workflow)
                if not success and step.config.required:
                    workflow["status"] = "failed"
                    break
            
            # Update workflow status
            if workflow["status"] == "running":
                all_required_completed = all(
                    step.status == WorkflowStepStatus.COMPLETED
                    for step in workflow["steps"]
                    if step.config.required
                )
                workflow["status"] = "completed" if all_required_completed else "partial"
            
            workflow["completed_at"] = datetime.now(timezone.utc)
            workflow["updated_at"] = datetime.now(timezone.utc)
            
            logger.info(f"Workflow {workflow_id} execution completed with status: {workflow['status']}")
            
            return {
                "workflow_id": workflow_id,
                "status": workflow["status"],
                "steps": [
                    {
                        "id": step.id,
                        "name": step.config.name,
                        "status": step.status.value,
                        "result": step.result,
                        "error_message": step.error_message,
                        "execution_time_ms": step.execution_time_ms
                    }
                    for step in workflow["steps"]
                ],
                "results": workflow["results"]
            }
            
        except Exception as e:
            logger.error(f"Error executing workflow {workflow_id}: {str(e)}")
            if workflow_id in self.active_workflows:
                self.active_workflows[workflow_id]["status"] = "failed"
            raise
    
    async def get_workflow_status(self, workflow_id: str) -> Dict[str, Any]:
        """Get current status of a workflow."""
        try:
            if workflow_id not in self.active_workflows:
                raise ValueError(f"Workflow {workflow_id} not found")
            
            workflow = self.active_workflows[workflow_id]
            
            return {
                "workflow_id": workflow_id,
                "name": workflow["name"],
                "status": workflow["status"],
                "created_at": workflow["created_at"].isoformat(),
                "updated_at": workflow["updated_at"].isoformat(),
                "steps": [
                    {
                        "id": step.id,
                        "name": step.config.name,
                        "type": step.config.step_type.value,
                        "status": step.status.value,
                        "required": step.config.required,
                        "execution_time_ms": step.execution_time_ms,
                        "error_message": step.error_message
                    }
                    for step in workflow["steps"]
                ],
                "progress_percentage": self._calculate_workflow_progress(workflow)
            }
            
        except Exception as e:
            logger.error(f"Error getting workflow status {workflow_id}: {str(e)}")
            raise
    
    def register_template(self, template: WorkflowTemplate) -> bool:
        """Register a new workflow template."""
        try:
            self.workflow_templates[template.id] = template
            logger.info(f"Registered workflow template: {template.name}")
            return True
            
        except Exception as e:
            logger.error(f"Error registering template {template.id}: {str(e)}")
            return False
    
    def get_available_templates(
        self,
        category: Optional[str] = None,
        user_id: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """Get available workflow templates."""
        try:
            templates = []
            
            for template in self.workflow_templates.values():
                # Filter by category if specified
                if category and template.category != category:
                    continue
                
                # Filter by access permissions
                if not template.is_public and template.created_by != str(user_id):
                    continue
                
                templates.append(template.to_dict())
            
            return templates
            
        except Exception as e:
            logger.error(f"Error getting available templates: {str(e)}")
            return []
    
    async def _execute_step(self, step: WorkflowStep, workflow: Dict[str, Any]) -> bool:
        """Execute a single workflow step."""
        try:
            step.status = WorkflowStepStatus.IN_PROGRESS
            step.started_at = datetime.now(timezone.utc)
            
            logger.info(f"Executing step {step.id}: {step.config.name}")
            
            # Get step executor
            executor = self.step_executors.get(step.config.step_type)
            if not executor:
                step.status = WorkflowStepStatus.FAILED
                step.error_message = f"No executor found for step type: {step.config.step_type}"
                return False
            
            # Execute step
            result = await executor.execute(step.config, workflow["context"])
            
            # Update step
            step.completed_at = datetime.now(timezone.utc)
            step.execution_time_ms = (step.completed_at - step.started_at).total_seconds() * 1000
            step.result = result
            step.status = WorkflowStepStatus.COMPLETED
            
            # Update workflow context with step results
            if result:
                workflow["context"][f"step_{step.id}_result"] = result
                workflow["results"][step.config.name] = result
            
            logger.info(f"Step {step.id} completed successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error executing step {step.id}: {str(e)}")
            step.status = WorkflowStepStatus.FAILED
            step.error_message = str(e)
            step.completed_at = datetime.now(timezone.utc)
            if step.started_at:
                step.execution_time_ms = (step.completed_at - step.started_at).total_seconds() * 1000
            return False
    
    async def _check_step_dependencies(self, step: WorkflowStep, workflow: Dict[str, Any]) -> bool:
        """Check if step dependencies are satisfied."""
        try:
            if not step.config.depends_on:
                return True
            
            for dependency_id in step.config.depends_on:
                # Find dependency step
                dependency_step = None
                for workflow_step in workflow["steps"]:
                    if workflow_step.id == dependency_id:
                        dependency_step = workflow_step
                        break
                
                if not dependency_step:
                    logger.error(f"Dependency step {dependency_id} not found")
                    return False
                
                if dependency_step.status != WorkflowStepStatus.COMPLETED:
                    logger.info(f"Dependency {dependency_id} not completed yet")
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error checking step dependencies: {str(e)}")
            return False
    
    def _calculate_workflow_progress(self, workflow: Dict[str, Any]) -> float:
        """Calculate workflow progress percentage."""
        try:
            total_steps = len(workflow["steps"])
            if total_steps == 0:
                return 100.0
            
            completed_steps = sum(
                1 for step in workflow["steps"]
                if step.status == WorkflowStepStatus.COMPLETED
            )
            
            return (completed_steps / total_steps) * 100.0
            
        except Exception as e:
            logger.error(f"Error calculating workflow progress: {str(e)}")
            return 0.0
    
    def _initialize_builtin_executors(self):
        """Initialize built-in step executors."""
        try:
            # Import basic executors
            from .step_executors import (
                DataLoadStepExecutor,
                ModelTrainingStepExecutor,
                ModelEvaluationStepExecutor
            )

            # Register basic executors
            self.step_executors = {
                'data_load': DataLoadStepExecutor(),
                'model_training': ModelTrainingStepExecutor(),
                'model_evaluation': ModelEvaluationStepExecutor()
            }
            
            logger.info("Built-in step executors initialized")
            
        except ImportError as e:
            logger.warning(f"Some step executors not available: {str(e)}")
            # Initialize with minimal executors
            self.step_executors = {}
        except Exception as e:
            logger.error(f"Error initializing built-in executors: {str(e)}")
            self.step_executors = {}


# Predefined workflow templates
BUILTIN_TEMPLATES = [
    WorkflowTemplate(
        id="universal_classification",
        name="Universal Classification Workflow",
        description="Simplified universal workflow for all classification types",
        category="classification",
        steps=[
            WorkflowStepConfig(
                step_type=WorkflowStepType.DATA_LOAD,
                name="Load Data",
                description="Load and validate training data"
            ),
            WorkflowStepConfig(
                step_type=WorkflowStepType.MODEL_TRAINING,
                name="Train Model",
                description="Train classification model",
                depends_on=["data_load"]
            ),
            WorkflowStepConfig(
                step_type=WorkflowStepType.MODEL_EVALUATION,
                name="Evaluate Model",
                description="Evaluate model performance",
                depends_on=["model_training"]
            )
        ],
        is_public=True,
        tags=["universal", "classification"]
    )

]
