/**
 * HierarchyConstraintDebugger.tsx
 * 
 * Debug component to test hierarchy constraint detection
 */

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { HierarchyTreeBuilder } from './HierarchyTreeBuilder';

const sampleData = [
  { category: 'Electronics', subcategory: 'Smartphones', product: 'iPhone 14', text: 'Latest iPhone model' },
  { category: 'Electronics', subcategory: 'Smartphones', product: 'Samsung Galaxy', text: 'Android smartphone' },
  { category: 'Electronics', subcategory: 'Laptops', product: 'MacBook Pro', text: 'Professional laptop' },
  { category: 'Electronics', subcategory: 'Laptops', product: 'Dell XPS', text: 'Windows laptop' },
  { category: 'Clothing', subcategory: 'Shirts', product: 'T-Shirt', text: 'Cotton t-shirt' },
  { category: 'Clothing', subcategory: 'Shirts', product: 'Dress Shirt', text: 'Formal shirt' },
  { category: 'Clothing', subcategory: 'Pants', product: 'Jeans', text: 'Denim jeans' },
];

const sampleHierarchyLevels = [
  { name: 'Category', column: 'category', order: 0 },
  { name: 'Subcategory', column: 'subcategory', order: 1 },
  { name: 'Product', column: 'product', order: 2 }
];

export const HierarchyConstraintDebugger: React.FC = () => {
  const [detectedConstraints, setDetectedConstraints] = useState<Record<string, string[]>>({});
  const [constraintInfo, setConstraintInfo] = useState<any[]>([]);
  const [tree, setTree] = useState<any[]>([]);

  const handleConstraintsChange = (constraints: Record<string, string[]>, info?: any[]) => {
    console.log('Debugger: Constraints detected:', constraints);
    console.log('Debugger: Constraint info:', info);
    setDetectedConstraints(constraints);
    if (info) {
      setConstraintInfo(info);
    }
  };

  const handleTreeChange = (newTree: any[]) => {
    console.log('Debugger: Tree changed:', newTree);
    setTree(newTree);
  };

  const resetTest = () => {
    setDetectedConstraints({});
    setConstraintInfo([]);
    setTree([]);
  };

  return (
    <div className="space-y-6 p-6">
      <Card>
        <CardHeader>
          <CardTitle>Hierarchy Constraint Detection Debugger</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-2">
            <Button onClick={resetTest} variant="outline">
              Reset Test
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h3 className="font-medium mb-2">Sample Data ({sampleData.length} rows)</h3>
              <pre className="text-xs bg-muted p-2 rounded overflow-auto max-h-40">
                {JSON.stringify(sampleData, null, 2)}
              </pre>
            </div>

            <div>
              <h3 className="font-medium mb-2">Hierarchy Levels</h3>
              <pre className="text-xs bg-muted p-2 rounded overflow-auto max-h-40">
                {JSON.stringify(sampleHierarchyLevels, null, 2)}
              </pre>
            </div>
          </div>

          <div>
            <h3 className="font-medium mb-2">Tree Builder Component</h3>
            <div className="border rounded p-4">
              <HierarchyTreeBuilder
                data={sampleData}
                hierarchyLevels={sampleHierarchyLevels}
                onTreeChange={handleTreeChange}
                onConstraintsChange={handleConstraintsChange}
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h3 className="font-medium mb-2">Detected Constraints ({Object.keys(detectedConstraints).length})</h3>
              <pre className="text-xs bg-muted p-2 rounded overflow-auto max-h-40">
                {JSON.stringify(detectedConstraints, null, 2)}
              </pre>
            </div>

            <div>
              <h3 className="font-medium mb-2">Constraint Info ({constraintInfo.length})</h3>
              <pre className="text-xs bg-muted p-2 rounded overflow-auto max-h-40">
                {JSON.stringify(constraintInfo, null, 2)}
              </pre>
            </div>
          </div>

          <div>
            <h3 className="font-medium mb-2">Built Tree ({tree.length} root nodes)</h3>
            <pre className="text-xs bg-muted p-2 rounded overflow-auto max-h-60">
              {JSON.stringify(tree, null, 2)}
            </pre>
          </div>

          <div className="space-y-2">
            <h3 className="font-medium">Test Results</h3>
            <div className="grid grid-cols-3 gap-4 text-center">
              <div className={`p-3 rounded ${tree.length > 0 ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                <div className="font-bold">{tree.length > 0 ? '✓' : '✗'}</div>
                <div className="text-sm">Tree Built</div>
              </div>
              <div className={`p-3 rounded ${Object.keys(detectedConstraints).length > 0 ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                <div className="font-bold">{Object.keys(detectedConstraints).length > 0 ? '✓' : '✗'}</div>
                <div className="text-sm">Constraints Detected</div>
              </div>
              <div className={`p-3 rounded ${constraintInfo.length > 0 ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                <div className="font-bold">{constraintInfo.length > 0 ? '✓' : '✗'}</div>
                <div className="text-sm">Constraint Info</div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
