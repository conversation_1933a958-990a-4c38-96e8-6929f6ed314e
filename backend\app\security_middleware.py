"""
Security middleware for ClassyWeb ML Platform.

This module provides security enhancements including:
1. Rate limiting
2. Input validation
3. Security headers
4. Request sanitization
"""
import time
import logging
import re
from typing import Dict, Any, Optional
from collections import defaultdict, deque
from datetime import datetime, timedelta

from fastapi import Request, HTTPException, status
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware

logger = logging.getLogger(__name__)

# Rate limiting storage (in production, use Redis)
rate_limit_storage: Dict[str, deque] = defaultdict(deque)

class SecurityMiddleware(BaseHTTPMiddleware):
    """Security middleware for request validation and rate limiting."""
    
    def __init__(self, app, rate_limit_requests: int = 100, rate_limit_window: int = 60):
        super().__init__(app)
        self.rate_limit_requests = rate_limit_requests
        self.rate_limit_window = rate_limit_window
    
    async def dispatch(self, request: Request, call_next):
        """Process request through security checks."""
        try:
            # Apply rate limiting
            if not self._check_rate_limit(request):
                return JSONResponse(
                    status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                    content={"detail": "Rate limit exceeded. Please try again later."}
                )
            
            # Validate request headers
            self._validate_headers(request)
            
            # Sanitize request data
            await self._sanitize_request(request)
            
            # Process request
            response = await call_next(request)
            
            # Add security headers
            self._add_security_headers(response)
            
            return response
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Security middleware error: {e}", exc_info=True)
            return JSONResponse(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                content={"detail": "Internal server error"}
            )
    
    def _check_rate_limit(self, request: Request) -> bool:
        """Check if request is within rate limits."""
        client_ip = request.client.host
        current_time = time.time()

        # More lenient rate limiting for auth endpoints
        is_auth_endpoint = request.url.path.startswith('/auth/')
        effective_limit = self.rate_limit_requests * 2 if is_auth_endpoint else self.rate_limit_requests

        # Clean old entries
        while (rate_limit_storage[client_ip] and
               rate_limit_storage[client_ip][0] < current_time - self.rate_limit_window):
            rate_limit_storage[client_ip].popleft()

        # Check if limit exceeded
        if len(rate_limit_storage[client_ip]) >= effective_limit:
            logger.warning(f"Rate limit exceeded for IP: {client_ip} (endpoint: {request.url.path})")
            return False

        # Add current request
        rate_limit_storage[client_ip].append(current_time)
        return True
    
    def _validate_headers(self, request: Request) -> None:
        """Validate request headers for security issues."""
        # Check for suspicious headers
        suspicious_patterns = [
            r'<script',
            r'javascript:',
            r'vbscript:',
            r'onload=',
            r'onerror=',
            r'eval\(',
            r'expression\('
        ]
        
        for header_name, header_value in request.headers.items():
            if isinstance(header_value, str):
                for pattern in suspicious_patterns:
                    if re.search(pattern, header_value, re.IGNORECASE):
                        logger.warning(f"Suspicious header detected: {header_name}={header_value}")
                        raise HTTPException(
                            status_code=status.HTTP_400_BAD_REQUEST,
                            detail="Invalid request headers"
                        )
    
    async def _sanitize_request(self, request: Request) -> None:
        """Sanitize request data."""
        # For now, just log suspicious patterns in URL
        url_path = str(request.url.path)
        
        # Check for path traversal attempts
        if '..' in url_path or '//' in url_path:
            logger.warning(f"Path traversal attempt detected: {url_path}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid request path"
            )
        
        # Check for SQL injection patterns in query parameters
        query_params = str(request.url.query)
        sql_injection_patterns = [
            r'union\s+select',
            r'drop\s+table',
            r'delete\s+from',
            r'insert\s+into',
            r'update\s+set',
            r'exec\s*\(',
            r'script\s*>',
            r'<\s*script'
        ]
        
        for pattern in sql_injection_patterns:
            if re.search(pattern, query_params, re.IGNORECASE):
                logger.warning(f"SQL injection attempt detected: {query_params}")
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Invalid request parameters"
                )
    
    def _add_security_headers(self, response) -> None:
        """Add security headers to response."""
        security_headers = {
            "X-Content-Type-Options": "nosniff",
            "X-Frame-Options": "DENY",
            "X-XSS-Protection": "1; mode=block",
            "Strict-Transport-Security": "max-age=31536000; includeSubDomains",
            "Content-Security-Policy": "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'",
            "Referrer-Policy": "strict-origin-when-cross-origin",
            "Permissions-Policy": "geolocation=(), microphone=(), camera=()"
        }
        
        for header_name, header_value in security_headers.items():
            response.headers[header_name] = header_value


def sanitize_input(data: Any) -> Any:
    """Sanitize input data to prevent XSS and injection attacks."""
    if isinstance(data, str):
        # Remove potentially dangerous characters
        dangerous_patterns = [
            r'<script[^>]*>.*?</script>',
            r'javascript:',
            r'vbscript:',
            r'onload\s*=',
            r'onerror\s*=',
            r'eval\s*\(',
            r'expression\s*\('
        ]
        
        for pattern in dangerous_patterns:
            data = re.sub(pattern, '', data, flags=re.IGNORECASE | re.DOTALL)
        
        # Escape HTML entities
        data = data.replace('<', '&lt;').replace('>', '&gt;')
        data = data.replace('"', '&quot;').replace("'", '&#x27;')
        
        return data
    
    elif isinstance(data, dict):
        return {key: sanitize_input(value) for key, value in data.items()}
    
    elif isinstance(data, list):
        return [sanitize_input(item) for item in data]
    
    return data


def validate_json_input(data: Dict[str, Any], max_depth: int = 10, max_keys: int = 1000) -> None:
    """Validate JSON input for security issues."""
    def check_depth(obj, current_depth=0):
        if current_depth > max_depth:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="JSON structure too deep"
            )
        
        if isinstance(obj, dict):
            if len(obj) > max_keys:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Too many keys in JSON object"
                )
            for value in obj.values():
                check_depth(value, current_depth + 1)
        elif isinstance(obj, list):
            if len(obj) > max_keys:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Too many items in JSON array"
                )
            for item in obj:
                check_depth(item, current_depth + 1)
    
    check_depth(data)


def validate_file_path(file_path: str) -> str:
    """Validate and sanitize file paths."""
    # Remove any path traversal attempts
    if '..' in file_path or file_path.startswith('/'):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid file path"
        )
    
    # Sanitize filename
    import os
    file_path = os.path.basename(file_path)
    
    # Remove dangerous characters
    dangerous_chars = '<>:"/\\|?*'
    for char in dangerous_chars:
        file_path = file_path.replace(char, '_')
    
    return file_path
