#!/usr/bin/env python3
"""
Fix task result file paths in the database.

This script updates task records that have relative paths to use absolute paths.
"""

import sys
import os
from pathlib import Path

# Add backend to path
backend_dir = Path(__file__).parent / "backend"
sys.path.insert(0, str(backend_dir))

def fix_task_paths():
    """Fix task result file paths to use absolute paths."""
    print("🔧 Fixing Task Result File Paths")
    print("=" * 40)
    
    try:
        # Set environment
        os.environ["DATABASE_URL"] = "sqlite:///./backend/classyweb.db"
        
        # Import after setting environment
        from app.database import SessionLocal, Task
        
        with SessionLocal() as db:
            # Get all tasks with result file paths
            tasks_with_results = db.query(Task).filter(
                Task.result_file_path.isnot(None),
                Task.status == "SUCCESS"
            ).all()
            
            print(f"Found {len(tasks_with_results)} tasks with result files")
            
            fixed_count = 0
            for task in tasks_with_results:
                result_path = Path(task.result_file_path)
                
                # Check if path is relative and file doesn't exist at that path
                if not result_path.is_absolute() and not result_path.exists():
                    # Try to find the file in common locations
                    possible_paths = [
                        Path("backend") / task.result_file_path,
                        Path("backend") / "model_artifacts" / Path(task.result_file_path).name,
                        Path("backend") / "temp_uploads" / Path(task.result_file_path).name,
                        Path("model_artifacts") / Path(task.result_file_path).name,
                        Path("temp_uploads") / Path(task.result_file_path).name,
                    ]
                    
                    found_path = None
                    for possible_path in possible_paths:
                        if possible_path.exists():
                            found_path = possible_path.resolve()
                            break
                    
                    if found_path:
                        print(f"✅ Fixing task {task.id[:8]}...")
                        print(f"   Old path: {task.result_file_path}")
                        print(f"   New path: {found_path}")
                        
                        task.result_file_path = str(found_path)
                        fixed_count += 1
                    else:
                        print(f"❌ Could not find file for task {task.id[:8]}: {task.result_file_path}")
                
                elif result_path.is_absolute() and result_path.exists():
                    print(f"✓ Task {task.id[:8]} already has correct absolute path")
                
                elif not result_path.exists():
                    print(f"⚠️  Task {task.id[:8]} file missing: {task.result_file_path}")
            
            if fixed_count > 0:
                db.commit()
                print(f"\n🎉 Fixed {fixed_count} task paths!")
            else:
                print("\n✓ No paths needed fixing")
                
    except Exception as e:
        print(f"❌ Error: {e}")
        return False
    
    return True

def main():
    """Main function."""
    print("🚀 Task Path Fixer")
    print("=" * 30)
    
    success = fix_task_paths()
    
    if success:
        print("\n✅ Task path fixing completed!")
    else:
        print("\n❌ Task path fixing failed!")

if __name__ == "__main__":
    main()
