"""Multi-Tenant Management for ClassyWeb Universal Platform."""

import logging
import uuid
from typing import Dict, List, Any, Optional
from datetime import datetime, timezone
from dataclasses import dataclass
from enum import Enum

from ..database import SessionLocal
from ..models.tenant import Tenant as Tenant<PERSON><PERSON><PERSON>, TenantUser, TenantResource

logger = logging.getLogger(__name__)


class TenantTier(Enum):
    """Tenant subscription tiers."""
    FREE = "free"
    PROFESSIONAL = "professional"
    ENTERPRISE = "enterprise"
    CUSTOM = "custom"


class ResourceType(Enum):
    """Types of tenant resources."""
    STORAGE = "storage"
    COMPUTE = "compute"
    API_CALLS = "api_calls"
    MODELS = "models"
    USERS = "users"
    PLUGINS = "plugins"


@dataclass
class ResourceQuota:
    """Resource quota for a tenant."""
    resource_type: ResourceType
    limit: int
    used: int = 0
    unit: str = "count"
    
    @property
    def usage_percentage(self) -> float:
        """Calculate usage percentage."""
        if self.limit == 0:
            return 0.0
        return (self.used / self.limit) * 100.0
    
    @property
    def is_exceeded(self) -> bool:
        """Check if quota is exceeded."""
        return self.used > self.limit


@dataclass
class Tenant:
    """Tenant data structure."""
    id: str
    name: str
    tier: TenantTier
    quotas: Dict[ResourceType, ResourceQuota]
    settings: Dict[str, Any]
    created_at: datetime
    is_active: bool = True
    admin_user_id: Optional[int] = None


class TenantManager:
    """Manages multi-tenant operations and resource isolation."""
    
    def __init__(self):
        self.tenants: Dict[str, Tenant] = {}
        self.user_tenant_mapping: Dict[int, str] = {}
        
        # Default quotas by tier
        self.tier_quotas = {
            TenantTier.FREE: {
                ResourceType.STORAGE: ResourceQuota(ResourceType.STORAGE, 100, unit="MB"),
                ResourceType.API_CALLS: ResourceQuota(ResourceType.API_CALLS, 1000, unit="calls/month"),
                ResourceType.MODELS: ResourceQuota(ResourceType.MODELS, 3, unit="models"),
                ResourceType.USERS: ResourceQuota(ResourceType.USERS, 1, unit="users"),
                ResourceType.PLUGINS: ResourceQuota(ResourceType.PLUGINS, 5, unit="plugins")
            },
            TenantTier.PROFESSIONAL: {
                ResourceType.STORAGE: ResourceQuota(ResourceType.STORAGE, 1000, unit="MB"),
                ResourceType.API_CALLS: ResourceQuota(ResourceType.API_CALLS, 10000, unit="calls/month"),
                ResourceType.MODELS: ResourceQuota(ResourceType.MODELS, 10, unit="models"),
                ResourceType.USERS: ResourceQuota(ResourceType.USERS, 5, unit="users"),
                ResourceType.PLUGINS: ResourceQuota(ResourceType.PLUGINS, 20, unit="plugins")
            },
            TenantTier.ENTERPRISE: {
                ResourceType.STORAGE: ResourceQuota(ResourceType.STORAGE, 10000, unit="MB"),
                ResourceType.API_CALLS: ResourceQuota(ResourceType.API_CALLS, 100000, unit="calls/month"),
                ResourceType.MODELS: ResourceQuota(ResourceType.MODELS, 50, unit="models"),
                ResourceType.USERS: ResourceQuota(ResourceType.USERS, 100, unit="users"),
                ResourceType.PLUGINS: ResourceQuota(ResourceType.PLUGINS, 100, unit="plugins")
            }
        }
        
        logger.info("Tenant Manager initialized")
    
    async def create_tenant(
        self,
        name: str,
        tier: TenantTier,
        admin_user_id: int,
        settings: Dict[str, Any] = None
    ) -> str:
        """Create a new tenant."""
        try:
            tenant_id = str(uuid.uuid4())
            
            # Get default quotas for tier
            quotas = self.tier_quotas.get(tier, self.tier_quotas[TenantTier.FREE]).copy()
            
            # Create tenant
            tenant = Tenant(
                id=tenant_id,
                name=name,
                tier=tier,
                quotas=quotas,
                settings=settings or {},
                created_at=datetime.now(timezone.utc),
                admin_user_id=admin_user_id
            )
            
            # Save to memory and database
            self.tenants[tenant_id] = tenant
            self.user_tenant_mapping[admin_user_id] = tenant_id
            
            await self._save_tenant_to_db(tenant)
            
            logger.info(f"Created tenant {tenant_id} ({name}) with tier {tier.value}")
            return tenant_id
            
        except Exception as e:
            logger.error(f"Error creating tenant: {str(e)}")
            raise
    
    async def get_tenant(self, tenant_id: str) -> Optional[Tenant]:
        """Get tenant by ID."""
        try:
            if tenant_id in self.tenants:
                return self.tenants[tenant_id]
            
            # Load from database if not in memory
            tenant = await self._load_tenant_from_db(tenant_id)
            if tenant:
                self.tenants[tenant_id] = tenant
            
            return tenant
            
        except Exception as e:
            logger.error(f"Error getting tenant {tenant_id}: {str(e)}")
            return None
    
    async def get_user_tenant(self, user_id: int) -> Optional[Tenant]:
        """Get tenant for a specific user."""
        try:
            if user_id in self.user_tenant_mapping:
                tenant_id = self.user_tenant_mapping[user_id]
                return await self.get_tenant(tenant_id)
            
            # Load from database
            tenant_id = await self._get_user_tenant_from_db(user_id)
            if tenant_id:
                self.user_tenant_mapping[user_id] = tenant_id
                return await self.get_tenant(tenant_id)
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting tenant for user {user_id}: {str(e)}")
            return None
    
    async def add_user_to_tenant(self, tenant_id: str, user_id: int, role: str = "member") -> bool:
        """Add a user to a tenant."""
        try:
            tenant = await self.get_tenant(tenant_id)
            if not tenant:
                logger.error(f"Tenant {tenant_id} not found")
                return False
            
            # Check user quota
            users_quota = tenant.quotas.get(ResourceType.USERS)
            if users_quota and users_quota.is_exceeded:
                logger.error(f"User quota exceeded for tenant {tenant_id}")
                return False
            
            # Add user to tenant
            self.user_tenant_mapping[user_id] = tenant_id
            
            # Update quota
            if users_quota:
                users_quota.used += 1
            
            # Save to database
            await self._save_tenant_user_to_db(tenant_id, user_id, role)
            
            logger.info(f"Added user {user_id} to tenant {tenant_id} with role {role}")
            return True
            
        except Exception as e:
            logger.error(f"Error adding user {user_id} to tenant {tenant_id}: {str(e)}")
            return False
    
    async def check_resource_quota(
        self,
        tenant_id: str,
        resource_type: ResourceType,
        requested_amount: int = 1
    ) -> bool:
        """Check if tenant has sufficient quota for a resource."""
        try:
            tenant = await self.get_tenant(tenant_id)
            if not tenant:
                return False
            
            quota = tenant.quotas.get(resource_type)
            if not quota:
                return True  # No quota limit
            
            return quota.used + requested_amount <= quota.limit
            
        except Exception as e:
            logger.error(f"Error checking resource quota: {str(e)}")
            return False
    
    async def consume_resource(
        self,
        tenant_id: str,
        resource_type: ResourceType,
        amount: int = 1
    ) -> bool:
        """Consume tenant resources and update quotas."""
        try:
            tenant = await self.get_tenant(tenant_id)
            if not tenant:
                return False
            
            quota = tenant.quotas.get(resource_type)
            if not quota:
                return True  # No quota limit
            
            if quota.used + amount > quota.limit:
                logger.warning(f"Resource quota exceeded for tenant {tenant_id}, resource {resource_type}")
                return False
            
            # Update quota
            quota.used += amount
            
            # Save to database
            await self._update_tenant_quota_in_db(tenant_id, resource_type, quota.used)
            
            return True
            
        except Exception as e:
            logger.error(f"Error consuming resource: {str(e)}")
            return False
    
    async def get_tenant_analytics(self, tenant_id: str) -> Dict[str, Any]:
        """Get analytics for a tenant."""
        try:
            tenant = await self.get_tenant(tenant_id)
            if not tenant:
                return {}
            
            # Calculate quota usage
            quota_usage = {}
            for resource_type, quota in tenant.quotas.items():
                quota_usage[resource_type.value] = {
                    "used": quota.used,
                    "limit": quota.limit,
                    "usage_percentage": quota.usage_percentage,
                    "is_exceeded": quota.is_exceeded,
                    "unit": quota.unit
                }
            
            # Get user count
            user_count = await self._get_tenant_user_count(tenant_id)
            
            # Get resource usage trends
            usage_trends = await self._get_usage_trends(tenant_id)
            
            return {
                "tenant_id": tenant_id,
                "name": tenant.name,
                "tier": tenant.tier.value,
                "quota_usage": quota_usage,
                "user_count": user_count,
                "usage_trends": usage_trends,
                "is_active": tenant.is_active,
                "created_at": tenant.created_at.isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error getting tenant analytics for {tenant_id}: {str(e)}")
            return {}
    
    async def _save_tenant_to_db(self, tenant: Tenant):
        """Save tenant to database."""
        try:
            with SessionLocal() as db:
                tenant_model = TenantModel(
                    id=tenant.id,
                    name=tenant.name,
                    tier=tenant.tier.value,
                    settings=tenant.settings,
                    is_active=tenant.is_active,
                    admin_user_id=tenant.admin_user_id,
                    created_at=tenant.created_at
                )
                
                db.add(tenant_model)
                
                # Save quotas
                for resource_type, quota in tenant.quotas.items():
                    resource_model = TenantResource(
                        tenant_id=tenant.id,
                        resource_type=resource_type.value,
                        limit_amount=quota.limit,
                        used_amount=quota.used,
                        unit=quota.unit
                    )
                    db.add(resource_model)
                
                db.commit()
                logger.info(f"Saved tenant {tenant.id} to database")
                
        except Exception as e:
            logger.error(f"Error saving tenant to database: {str(e)}")
            raise
    
    async def _load_tenant_from_db(self, tenant_id: str) -> Optional[Tenant]:
        """Load tenant from database."""
        try:
            with SessionLocal() as db:
                tenant_model = db.query(TenantModel).filter(TenantModel.id == tenant_id).first()
                if not tenant_model:
                    return None
                
                # Load quotas
                quotas = {}
                resources = db.query(TenantResource).filter(TenantResource.tenant_id == tenant_id).all()
                
                for resource in resources:
                    resource_type = ResourceType(resource.resource_type)
                    quotas[resource_type] = ResourceQuota(
                        resource_type=resource_type,
                        limit=resource.limit_amount,
                        used=resource.used_amount,
                        unit=resource.unit
                    )
                
                return Tenant(
                    id=tenant_model.id,
                    name=tenant_model.name,
                    tier=TenantTier(tenant_model.tier),
                    quotas=quotas,
                    settings=tenant_model.settings or {},
                    created_at=tenant_model.created_at,
                    is_active=tenant_model.is_active,
                    admin_user_id=tenant_model.admin_user_id
                )
                
        except Exception as e:
            logger.error(f"Error loading tenant from database: {str(e)}")
            return None
    
    async def _save_tenant_user_to_db(self, tenant_id: str, user_id: int, role: str):
        """Save tenant user relationship to database."""
        try:
            with SessionLocal() as db:
                tenant_user = TenantUser(
                    tenant_id=tenant_id,
                    user_id=user_id,
                    role=role,
                    joined_at=datetime.now(timezone.utc)
                )
                db.add(tenant_user)
                db.commit()
                
        except Exception as e:
            logger.error(f"Error saving tenant user to database: {str(e)}")
            raise
    
    async def _get_user_tenant_from_db(self, user_id: int) -> Optional[str]:
        """Get user's tenant ID from database."""
        try:
            with SessionLocal() as db:
                tenant_user = db.query(TenantUser).filter(TenantUser.user_id == user_id).first()
                return tenant_user.tenant_id if tenant_user else None
                
        except Exception as e:
            logger.error(f"Error getting user tenant from database: {str(e)}")
            return None
    
    async def _update_tenant_quota_in_db(self, tenant_id: str, resource_type: ResourceType, used_amount: int):
        """Update tenant quota in database."""
        try:
            with SessionLocal() as db:
                resource = db.query(TenantResource).filter(
                    TenantResource.tenant_id == tenant_id,
                    TenantResource.resource_type == resource_type.value
                ).first()
                
                if resource:
                    resource.used_amount = used_amount
                    resource.updated_at = datetime.now(timezone.utc)
                    db.commit()
                
        except Exception as e:
            logger.error(f"Error updating tenant quota in database: {str(e)}")
    
    async def _get_tenant_user_count(self, tenant_id: str) -> int:
        """Get number of users in a tenant."""
        try:
            with SessionLocal() as db:
                count = db.query(TenantUser).filter(TenantUser.tenant_id == tenant_id).count()
                return count
                
        except Exception as e:
            logger.error(f"Error getting tenant user count: {str(e)}")
            return 0
    
    async def _get_usage_trends(self, tenant_id: str) -> Dict[str, Any]:
        """Get usage trends for a tenant."""
        try:
            # TODO: Implement usage trends analysis
            # This would involve querying historical usage data
            return {
                "daily_api_calls": [],
                "storage_growth": [],
                "user_activity": [],
                "model_usage": []
            }
            
        except Exception as e:
            logger.error(f"Error getting usage trends: {str(e)}")
            return {}


# Global tenant manager instance
tenant_manager = TenantManager()
