#!/usr/bin/env python3
"""
Test script for universal training endpoint to check for parameter mismatches.
"""

import requests
import json
import tempfile
import pandas as pd
import os
import sys

# Add the app directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

def create_test_data():
    """Create a test CSV file."""
    data = {
        'text': [
            'I love this product',
            'This is terrible',
            'Great quality',
            'Poor service',
            'Amazing experience',
            'Worst purchase ever',
            'Highly recommend',
            'Not worth it',
            'Excellent value',
            'Complete waste of money'
        ],
        'sentiment': [
            'positive',
            'negative', 
            'positive',
            'negative',
            'positive',
            'negative',
            'positive',
            'negative',
            'positive',
            'negative'
        ],
        'category': [
            'product',
            'product',
            'product',
            'service',
            'experience',
            'product',
            'product',
            'product',
            'product',
            'product'
        ]
    }
    
    df = pd.DataFrame(data)
    
    # Create temporary file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
        df.to_csv(f.name, index=False)
        return f.name

def test_universal_training():
    """Test the universal training endpoint."""
    base_url = "http://localhost:8000"
    
    # Create test data
    test_file = create_test_data()
    
    try:
        # Step 1: Register/Login user
        print("Step 1: Registering test user...")
        register_data = {
            "email": "<EMAIL>",
            "password": "testpassword123",
            "username": "testuser"
        }
        
        register_response = requests.post(f"{base_url}/auth/register", json=register_data)
        if register_response.status_code not in [201, 400]:  # 400 if user already exists
            print(f"Registration failed: {register_response.status_code} - {register_response.text}")
            return False
        
        # Login
        login_data = {
            "email": "<EMAIL>",
            "password": "testpassword123"
        }
        
        login_response = requests.post(f"{base_url}/auth/login", json=login_data)
        if login_response.status_code != 200:
            print(f"Login failed: {login_response.status_code} - {login_response.text}")
            return False
        
        token = login_response.json()["access_token"]
        headers = {"Authorization": f"Bearer {token}"}
        
        # Step 2: Upload file
        print("Step 2: Uploading test file...")
        with open(test_file, 'rb') as f:
            files = {"file": ("test_data.csv", f, "text/csv")}
            upload_response = requests.post(f"{base_url}/files/upload", files=files, headers=headers)
        
        if upload_response.status_code != 200:
            print(f"File upload failed: {upload_response.status_code} - {upload_response.text}")
            return False
        
        file_id = upload_response.json()["file_id"]
        print(f"File uploaded successfully: {file_id}")
        
        # Step 3: Test universal training
        print("Step 3: Testing universal training...")
        training_config = {
            "file_id": file_id,
            "classification_type": "multiclass",  # Use the correct format
            "model_type": "custom",
            "text_column": "text",
            "label_columns": ["sentiment"],
            "training_params": {
                "epochs": 1,
                "batch_size": 2,
                "learning_rate": 0.001
            }
        }
        
        training_response = requests.post(
            f"{base_url}/api/v2/classification/universal/train",
            json=training_config,
            headers=headers
        )
        
        print(f"Training response status: {training_response.status_code}")
        print(f"Training response: {training_response.text}")
        
        if training_response.status_code == 200:
            print("✅ Universal training endpoint works correctly!")
            return True
        else:
            print(f"❌ Universal training failed: {training_response.status_code}")
            print(f"Error details: {training_response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Test failed with exception: {e}")
        return False
    finally:
        # Clean up
        if os.path.exists(test_file):
            os.unlink(test_file)

if __name__ == "__main__":
    print("Testing Universal Training Endpoint...")
    print("=" * 50)
    
    success = test_universal_training()
    
    if success:
        print("\n✅ All tests passed!")
        sys.exit(0)
    else:
        print("\n❌ Tests failed!")
        sys.exit(1)
