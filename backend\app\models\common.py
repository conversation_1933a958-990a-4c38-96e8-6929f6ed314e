"""
Common Pydantic models used across different parts of the application.
"""
from typing import List, Dict, Any, Optional
from pydantic import BaseModel, Field
from enum import Enum

class FileInfo(BaseModel):
    """Model representing basic information about an uploaded file."""
    file_id: str
    filename: str
    columns: List[str]
    num_rows: int
    preview: List[Dict[str, Any]] = Field(..., description="List of dicts representing head(5) of the DataFrame")

class TaskStatusEnum(str, Enum):
    """Enum for task status values."""
    PENDING = "PENDING"
    RUNNING = "RUNNING"
    SUCCESS = "SUCCESS"
    FAILED = "FAILED"

class TaskStatus(BaseModel):
    """Model representing the status of a background task."""
    task_id: str
    status: TaskStatusEnum = Field(..., description="Current status of the task")
    message: Optional[str] = Field(None, description="Status message or error details")
    result_data_url: Optional[str] = Field(None, description="URL to fetch result data as JSON when SUCCESS")
    progress: Optional[float] = Field(None, description="Progress percentage (0.0 to 100.0)")
    result: Optional[Dict[str, Any]] = Field(None, description="Task result data")
    error: Optional[str] = Field(None, description="Error details if task failed")
