"""Enhanced Multi-label Classification Engine for ClassyWeb ML Platform Phase 3.

This module implements production-ready multi-label classification with advanced features:
- Advanced label correlation analysis with mutual information and co-occurrence patterns
- Per-label threshold optimization with multiple strategies
- Comprehensive multi-label metrics (Hamming loss, subset accuracy, micro/macro averages)
- Label ranking metrics and exact match ratio
- Real-time performance monitoring and optimization
"""

import logging
import time
import pandas as pd
import numpy as np
from typing import List, Dict, Any, Optional, Tuple, Union
from sklearn.metrics import (
    accuracy_score, precision_score, recall_score, f1_score, hamming_loss,
    multilabel_confusion_matrix, classification_report, precision_recall_curve,
    average_precision_score, label_ranking_average_precision_score,
    label_ranking_loss, coverage_error
)
from sklearn.feature_selection import mutual_info_classif
from sklearn.model_selection import StratifiedKFold
from scipy.stats import chi2_contingency
import torch
from transformers import AutoTokenizer, AutoModelForSequenceClassification

from .base_engine import (
    BaseClassificationEngine, 
    ClassificationType, 
    TrainingMethod,
    ClassificationResult,
    TrainingConfig,
    TrainingResult
)

logger = logging.getLogger(__name__)


class LabelCorrelationAnalyzer:
    """Advanced label correlation analysis for multi-label classification."""

    def __init__(self):
        self.correlation_methods = ['pearson', 'spearman', 'mutual_info', 'chi2', 'cooccurrence']

    def analyze_correlations(self, data: pd.DataFrame, label_columns: List[str]) -> Dict[str, Any]:
        """Comprehensive label correlation analysis."""
        results = {
            'correlation_matrices': {},
            'mutual_information': {},
            'chi2_statistics': {},
            'cooccurrence_patterns': {},
            'high_correlations': [],
            'label_dependencies': {},
            'summary_statistics': {}
        }

        try:
            # Convert to binary matrix
            binary_data = self._prepare_binary_data(data, label_columns)

            if binary_data.shape[1] < 2:
                return {'error': 'Need at least 2 labels for correlation analysis'}

            # Pearson correlation
            pearson_corr = binary_data.corr(method='pearson')
            results['correlation_matrices']['pearson'] = pearson_corr.to_dict()

            # Spearman correlation
            spearman_corr = binary_data.corr(method='spearman')
            results['correlation_matrices']['spearman'] = spearman_corr.to_dict()

            # Mutual information
            mi_matrix = self._calculate_mutual_information(binary_data)
            results['mutual_information'] = mi_matrix

            # Chi-square test for independence
            chi2_results = self._calculate_chi2_statistics(binary_data)
            results['chi2_statistics'] = chi2_results

            # Co-occurrence patterns
            cooccurrence = self._analyze_cooccurrence_patterns(binary_data)
            results['cooccurrence_patterns'] = cooccurrence

            # Find high correlations
            high_corr = self._find_high_correlations(pearson_corr, threshold=0.7)
            results['high_correlations'] = high_corr

            # Label dependencies
            dependencies = self._identify_label_dependencies(binary_data, pearson_corr)
            results['label_dependencies'] = dependencies

            # Summary statistics
            summary = self._calculate_summary_statistics(binary_data, pearson_corr)
            results['summary_statistics'] = summary

        except Exception as e:
            logger.error(f"Correlation analysis failed: {e}")
            results['error'] = str(e)

        return results

    def _prepare_binary_data(self, data: pd.DataFrame, label_columns: List[str]) -> pd.DataFrame:
        """Convert label columns to binary format."""
        binary_data = pd.DataFrame()

        for col in label_columns:
            if data[col].dtype in ['int64', 'float64', 'bool']:
                binary_data[col] = data[col].astype(int)
            else:
                # Handle string representations
                binary_data[col] = data[col].map({
                    'True': 1, 'False': 0, 'true': 1, 'false': 0,
                    '1': 1, '0': 0, 'yes': 1, 'no': 0,
                    'Yes': 1, 'No': 0, 'Y': 1, 'N': 0
                }).fillna(0).astype(int)

        return binary_data

    def _calculate_mutual_information(self, binary_data: pd.DataFrame) -> Dict[str, Dict[str, float]]:
        """Calculate mutual information between all label pairs."""
        mi_matrix = {}
        labels = binary_data.columns.tolist()

        for i, label1 in enumerate(labels):
            mi_matrix[label1] = {}
            for j, label2 in enumerate(labels):
                if i == j:
                    mi_matrix[label1][label2] = 1.0
                else:
                    try:
                        # Calculate mutual information
                        mi_score = mutual_info_classif(
                            binary_data[[label1]],
                            binary_data[label2],
                            discrete_features=True
                        )[0]
                        mi_matrix[label1][label2] = float(mi_score)
                    except:
                        mi_matrix[label1][label2] = 0.0

        return mi_matrix

    def _calculate_chi2_statistics(self, binary_data: pd.DataFrame) -> Dict[str, Dict[str, Dict[str, float]]]:
        """Calculate chi-square statistics for label independence."""
        chi2_results = {}
        labels = binary_data.columns.tolist()

        for i, label1 in enumerate(labels):
            chi2_results[label1] = {}
            for j, label2 in enumerate(labels[i+1:], i+1):
                try:
                    # Create contingency table
                    contingency = pd.crosstab(binary_data[label1], binary_data[label2])
                    chi2_stat, p_value, dof, expected = chi2_contingency(contingency)

                    chi2_results[label1][label2] = {
                        'chi2_statistic': float(chi2_stat),
                        'p_value': float(p_value),
                        'degrees_of_freedom': int(dof),
                        'is_independent': p_value > 0.05
                    }
                except Exception as e:
                    chi2_results[label1][label2] = {'error': str(e)}

        return chi2_results

    def _analyze_cooccurrence_patterns(self, binary_data: pd.DataFrame) -> Dict[str, Any]:
        """Analyze label co-occurrence patterns."""
        labels = binary_data.columns.tolist()
        n_samples = len(binary_data)

        # Calculate co-occurrence frequencies
        cooccurrence_freq = {}
        for i, label1 in enumerate(labels):
            cooccurrence_freq[label1] = {}
            for j, label2 in enumerate(labels):
                if i != j:
                    # Count samples where both labels are 1
                    both_present = ((binary_data[label1] == 1) & (binary_data[label2] == 1)).sum()
                    cooccurrence_freq[label1][label2] = {
                        'count': int(both_present),
                        'frequency': float(both_present / n_samples),
                        'conditional_prob': float(both_present / max(binary_data[label1].sum(), 1))
                    }

        # Find most common label combinations
        label_combinations = []
        for _, row in binary_data.iterrows():
            active_labels = [label for label in labels if row[label] == 1]
            if len(active_labels) > 1:
                label_combinations.append(tuple(sorted(active_labels)))

        from collections import Counter
        combination_counts = Counter(label_combinations)

        return {
            'cooccurrence_frequencies': cooccurrence_freq,
            'common_combinations': dict(combination_counts.most_common(10)),
            'avg_labels_per_sample': float(binary_data.sum(axis=1).mean()),
            'max_labels_per_sample': int(binary_data.sum(axis=1).max()),
            'samples_with_multiple_labels': int((binary_data.sum(axis=1) > 1).sum())
        }

    def _find_high_correlations(self, corr_matrix: pd.DataFrame, threshold: float = 0.7) -> List[Dict[str, Any]]:
        """Find highly correlated label pairs."""
        high_correlations = []

        for i in range(len(corr_matrix.columns)):
            for j in range(i+1, len(corr_matrix.columns)):
                corr_val = corr_matrix.iloc[i, j]
                if abs(corr_val) > threshold:
                    high_correlations.append({
                        'label1': corr_matrix.columns[i],
                        'label2': corr_matrix.columns[j],
                        'correlation': float(corr_val),
                        'strength': 'strong' if abs(corr_val) > 0.8 else 'moderate',
                        'direction': 'positive' if corr_val > 0 else 'negative'
                    })

        return sorted(high_correlations, key=lambda x: abs(x['correlation']), reverse=True)

    def _identify_label_dependencies(self, binary_data: pd.DataFrame, corr_matrix: pd.DataFrame) -> Dict[str, List[str]]:
        """Identify potential label dependencies."""
        dependencies = {}
        labels = binary_data.columns.tolist()

        for label in labels:
            dependent_labels = []
            for other_label in labels:
                if label != other_label:
                    corr = abs(corr_matrix.loc[label, other_label])
                    if corr > 0.6:  # Strong correlation threshold
                        dependent_labels.append(other_label)

            if dependent_labels:
                dependencies[label] = dependent_labels

        return dependencies

    def _calculate_summary_statistics(self, binary_data: pd.DataFrame, corr_matrix: pd.DataFrame) -> Dict[str, float]:
        """Calculate summary statistics for label correlations."""
        # Get upper triangle of correlation matrix (excluding diagonal)
        upper_triangle = corr_matrix.values[np.triu_indices_from(corr_matrix.values, k=1)]

        return {
            'avg_correlation': float(np.mean(upper_triangle)),
            'max_correlation': float(np.max(np.abs(upper_triangle))),
            'min_correlation': float(np.min(upper_triangle)),
            'std_correlation': float(np.std(upper_triangle)),
            'num_high_correlations': int(np.sum(np.abs(upper_triangle) > 0.7)),
            'num_moderate_correlations': int(np.sum((np.abs(upper_triangle) > 0.4) & (np.abs(upper_triangle) <= 0.7))),
            'label_imbalance_ratio': float(binary_data.mean().std() / binary_data.mean().mean()) if binary_data.mean().mean() > 0 else 0.0
        }


class MultiLabelThresholdOptimizer:
    """Advanced threshold optimization for multi-label classification."""

    def __init__(self):
        self.optimization_strategies = [
            'f1_optimal', 'precision_recall_balance', 'youden_index',
            'cost_sensitive', 'roc_optimal', 'pr_auc_optimal'
        ]

    def optimize_thresholds(
        self,
        y_true: np.ndarray,
        y_scores: np.ndarray,
        label_names: List[str],
        strategy: str = 'f1_optimal',
        class_weights: Optional[Dict[str, float]] = None
    ) -> Dict[str, Any]:
        """Optimize thresholds for each label using specified strategy."""

        if y_true.shape[1] != y_scores.shape[1]:
            raise ValueError("Mismatch between true labels and scores dimensions")

        optimal_thresholds = {}
        threshold_analysis = {}

        for i, label in enumerate(label_names):
            if i < y_scores.shape[1]:
                label_true = y_true[:, i]
                label_scores = y_scores[:, i]

                # Skip if all labels are the same
                if len(np.unique(label_true)) < 2:
                    optimal_thresholds[label] = 0.5
                    threshold_analysis[label] = {'error': 'All labels are the same'}
                    continue

                try:
                    threshold_result = self._optimize_single_label_threshold(
                        label_true, label_scores, strategy,
                        class_weights.get(label, 1.0) if class_weights else 1.0
                    )

                    optimal_thresholds[label] = threshold_result['optimal_threshold']
                    threshold_analysis[label] = threshold_result

                except Exception as e:
                    logger.warning(f"Threshold optimization failed for label {label}: {e}")
                    optimal_thresholds[label] = 0.5
                    threshold_analysis[label] = {'error': str(e)}
            else:
                optimal_thresholds[label] = 0.5
                threshold_analysis[label] = {'error': 'No scores available'}

        return {
            'optimal_thresholds': optimal_thresholds,
            'threshold_analysis': threshold_analysis,
            'strategy_used': strategy,
            'global_metrics': self._calculate_global_threshold_metrics(
                y_true, y_scores, optimal_thresholds, label_names
            )
        }

    def _optimize_single_label_threshold(
        self,
        y_true: np.ndarray,
        y_scores: np.ndarray,
        strategy: str,
        class_weight: float = 1.0
    ) -> Dict[str, Any]:
        """Optimize threshold for a single label."""

        # Calculate precision-recall curve
        precision, recall, thresholds = precision_recall_curve(y_true, y_scores)

        # Ensure we have valid thresholds
        if len(thresholds) == 0:
            return {
                'optimal_threshold': 0.5,
                'precision': 0.0,
                'recall': 0.0,
                'f1_score': 0.0,
                'error': 'No valid thresholds found'
            }

        # Add threshold for the last precision/recall point
        thresholds = np.append(thresholds, 1.0)

        if strategy == 'f1_optimal':
            # Find threshold that maximizes F1 score
            f1_scores = 2 * (precision * recall) / (precision + recall + 1e-8)
            best_idx = np.argmax(f1_scores)

        elif strategy == 'precision_recall_balance':
            # Find threshold where precision ≈ recall
            pr_diff = np.abs(precision - recall)
            best_idx = np.argmin(pr_diff)

        elif strategy == 'youden_index':
            # Maximize Youden's J statistic (sensitivity + specificity - 1)
            # Need to calculate specificity for each threshold
            youden_scores = []
            for i, thresh in enumerate(thresholds):
                y_pred = (y_scores >= thresh).astype(int)
                tn = np.sum((y_true == 0) & (y_pred == 0))
                fp = np.sum((y_true == 0) & (y_pred == 1))
                specificity = tn / (tn + fp + 1e-8)
                sensitivity = recall[i] if i < len(recall) else recall[-1]
                youden_scores.append(sensitivity + specificity - 1)

            best_idx = np.argmax(youden_scores)

        elif strategy == 'cost_sensitive':
            # Minimize cost considering class weights
            cost_scores = []
            for i, thresh in enumerate(thresholds):
                y_pred = (y_scores >= thresh).astype(int)
                fp = np.sum((y_true == 0) & (y_pred == 1))
                fn = np.sum((y_true == 1) & (y_pred == 0))
                # Cost = FP + class_weight * FN
                cost = fp + class_weight * fn
                cost_scores.append(cost)

            best_idx = np.argmin(cost_scores)

        elif strategy == 'roc_optimal':
            # Find threshold closest to top-left corner of ROC curve
            from sklearn.metrics import roc_curve
            fpr, tpr, roc_thresholds = roc_curve(y_true, y_scores)

            # Calculate distance to (0,1) point
            distances = np.sqrt(fpr**2 + (1 - tpr)**2)
            roc_best_idx = np.argmin(distances)

            # Map back to precision-recall thresholds
            if roc_best_idx < len(roc_thresholds):
                optimal_threshold = roc_thresholds[roc_best_idx]
                # Find closest threshold in precision-recall curve
                best_idx = np.argmin(np.abs(thresholds - optimal_threshold))
            else:
                best_idx = len(thresholds) // 2

        elif strategy == 'pr_auc_optimal':
            # Find threshold that maximizes area under precision-recall curve
            # This is approximated by finding the threshold with highest F1
            f1_scores = 2 * (precision * recall) / (precision + recall + 1e-8)
            best_idx = np.argmax(f1_scores)

        else:
            # Default to F1 optimal
            f1_scores = 2 * (precision * recall) / (precision + recall + 1e-8)
            best_idx = np.argmax(f1_scores)

        # Ensure index is valid
        best_idx = min(best_idx, len(thresholds) - 1)
        optimal_threshold = float(thresholds[best_idx])

        # Calculate metrics at optimal threshold
        best_precision = float(precision[min(best_idx, len(precision) - 1)])
        best_recall = float(recall[min(best_idx, len(recall) - 1)])
        best_f1 = 2 * (best_precision * best_recall) / (best_precision + best_recall + 1e-8)

        return {
            'optimal_threshold': optimal_threshold,
            'precision': best_precision,
            'recall': best_recall,
            'f1_score': float(best_f1),
            'strategy': strategy,
            'threshold_index': int(best_idx),
            'total_thresholds_evaluated': len(thresholds)
        }

    def _calculate_global_threshold_metrics(
        self,
        y_true: np.ndarray,
        y_scores: np.ndarray,
        thresholds: Dict[str, float],
        label_names: List[str]
    ) -> Dict[str, float]:
        """Calculate global metrics using optimized thresholds."""

        # Apply thresholds to get predictions
        y_pred = np.zeros_like(y_scores)
        for i, label in enumerate(label_names):
            if i < y_scores.shape[1]:
                threshold = thresholds.get(label, 0.5)
                y_pred[:, i] = (y_scores[:, i] >= threshold).astype(int)

        # Calculate comprehensive metrics
        try:
            return {
                'hamming_loss': float(hamming_loss(y_true, y_pred)),
                'subset_accuracy': float(accuracy_score(y_true, y_pred)),
                'micro_f1': float(f1_score(y_true, y_pred, average='micro')),
                'macro_f1': float(f1_score(y_true, y_pred, average='macro')),
                'weighted_f1': float(f1_score(y_true, y_pred, average='weighted')),
                'samples_f1': float(f1_score(y_true, y_pred, average='samples')),
                'micro_precision': float(precision_score(y_true, y_pred, average='micro')),
                'macro_precision': float(precision_score(y_true, y_pred, average='macro')),
                'micro_recall': float(recall_score(y_true, y_pred, average='micro')),
                'macro_recall': float(recall_score(y_true, y_pred, average='macro'))
            }
        except Exception as e:
            logger.error(f"Global metrics calculation failed: {e}")
            return {'error': str(e)}


class EnhancedMultiLabelEngine(BaseClassificationEngine):
    """Enhanced multi-label classification engine with advanced features for Phase 3."""

    def __init__(self, classification_type: ClassificationType, **kwargs):
        """Initialize enhanced multi-label classification engine."""
        super().__init__(classification_type)
        self.label_names = []
        self.num_labels = 0
        self.label_thresholds = {}  # Per-label optimized thresholds
        self.correlation_analyzer = LabelCorrelationAnalyzer()
        self.threshold_optimizer = MultiLabelThresholdOptimizer()

        # Enhanced attributes
        self.label_correlations = {}
        self.threshold_analysis = {}
        self.performance_history = []
        self.optimization_results = {}

    @property
    def supported_training_methods(self) -> List[TrainingMethod]:
        """Return supported training methods."""
        return [TrainingMethod.CUSTOM, TrainingMethod.LLM]

    @property
    def default_metrics(self) -> List[str]:
        """Return comprehensive default metrics for multi-label classification."""
        return [
            'hamming_loss', 'subset_accuracy', 'exact_match_ratio',
            'micro_f1', 'macro_f1', 'weighted_f1', 'samples_f1',
            'micro_precision', 'macro_precision', 'weighted_precision',
            'micro_recall', 'macro_recall', 'weighted_recall',
            'label_ranking_loss', 'label_ranking_average_precision',
            'coverage_error', 'average_precision_score'
        ]

    def calculate_comprehensive_metrics(
        self,
        y_true: np.ndarray,
        y_pred: np.ndarray,
        y_scores: Optional[np.ndarray] = None
    ) -> Dict[str, float]:
        """Calculate comprehensive multi-label classification metrics."""
        metrics = {}

        try:
            # Basic multi-label metrics
            metrics['hamming_loss'] = float(hamming_loss(y_true, y_pred))
            metrics['subset_accuracy'] = float(accuracy_score(y_true, y_pred))
            metrics['exact_match_ratio'] = float(accuracy_score(y_true, y_pred))  # Same as subset accuracy

            # F1 scores
            metrics['micro_f1'] = float(f1_score(y_true, y_pred, average='micro'))
            metrics['macro_f1'] = float(f1_score(y_true, y_pred, average='macro'))
            metrics['weighted_f1'] = float(f1_score(y_true, y_pred, average='weighted'))
            metrics['samples_f1'] = float(f1_score(y_true, y_pred, average='samples'))

            # Precision scores
            metrics['micro_precision'] = float(precision_score(y_true, y_pred, average='micro'))
            metrics['macro_precision'] = float(precision_score(y_true, y_pred, average='macro'))
            metrics['weighted_precision'] = float(precision_score(y_true, y_pred, average='weighted'))

            # Recall scores
            metrics['micro_recall'] = float(recall_score(y_true, y_pred, average='micro'))
            metrics['macro_recall'] = float(recall_score(y_true, y_pred, average='macro'))
            metrics['weighted_recall'] = float(recall_score(y_true, y_pred, average='weighted'))

            # Ranking-based metrics (require probability scores)
            if y_scores is not None:
                try:
                    metrics['label_ranking_loss'] = float(label_ranking_loss(y_true, y_scores))
                    metrics['label_ranking_average_precision'] = float(
                        label_ranking_average_precision_score(y_true, y_scores)
                    )
                    metrics['coverage_error'] = float(coverage_error(y_true, y_scores))

                    # Average precision score per label
                    ap_scores = []
                    for i in range(y_true.shape[1]):
                        if len(np.unique(y_true[:, i])) > 1:  # Skip if all labels are the same
                            ap = average_precision_score(y_true[:, i], y_scores[:, i])
                            ap_scores.append(ap)

                    if ap_scores:
                        metrics['average_precision_score'] = float(np.mean(ap_scores))
                        metrics['per_label_average_precision'] = ap_scores

                except Exception as e:
                    logger.warning(f"Ranking metrics calculation failed: {e}")
                    metrics['ranking_metrics_error'] = str(e)

            # Per-label metrics
            per_label_metrics = {}
            for i, label_name in enumerate(self.label_names):
                if i < y_true.shape[1]:
                    try:
                        label_true = y_true[:, i]
                        label_pred = y_pred[:, i]

                        if len(np.unique(label_true)) > 1:  # Skip if all labels are the same
                            per_label_metrics[label_name] = {
                                'precision': float(precision_score(label_true, label_pred)),
                                'recall': float(recall_score(label_true, label_pred)),
                                'f1_score': float(f1_score(label_true, label_pred)),
                                'support': int(np.sum(label_true))
                            }
                        else:
                            per_label_metrics[label_name] = {
                                'precision': 0.0,
                                'recall': 0.0,
                                'f1_score': 0.0,
                                'support': int(np.sum(label_true)),
                                'note': 'All labels are the same'
                            }
                    except Exception as e:
                        per_label_metrics[label_name] = {'error': str(e)}

            metrics['per_label_metrics'] = per_label_metrics

            # Additional statistics
            metrics['avg_labels_per_sample'] = float(np.mean(np.sum(y_true, axis=1)))
            metrics['max_labels_per_sample'] = int(np.max(np.sum(y_true, axis=1)))
            metrics['min_labels_per_sample'] = int(np.min(np.sum(y_true, axis=1)))
            metrics['samples_with_no_labels'] = int(np.sum(np.sum(y_true, axis=1) == 0))
            metrics['samples_with_multiple_labels'] = int(np.sum(np.sum(y_true, axis=1) > 1))

            # Label frequency statistics
            label_frequencies = np.sum(y_true, axis=0)
            metrics['label_frequencies'] = {
                label_name: int(freq) for label_name, freq in zip(self.label_names, label_frequencies)
            }
            metrics['most_frequent_label'] = self.label_names[np.argmax(label_frequencies)] if len(self.label_names) > 0 else None
            metrics['least_frequent_label'] = self.label_names[np.argmin(label_frequencies)] if len(self.label_names) > 0 else None

        except Exception as e:
            logger.error(f"Comprehensive metrics calculation failed: {e}")
            metrics['error'] = str(e)

        return metrics

    def analyze_label_correlations_enhanced(self, data: pd.DataFrame, label_columns: List[str]) -> Dict[str, Any]:
        """Enhanced label correlation analysis using the advanced analyzer."""
        return self.correlation_analyzer.analyze_correlations(data, label_columns)

    def optimize_thresholds_enhanced(
        self,
        y_true: np.ndarray,
        y_scores: np.ndarray,
        strategy: str = 'f1_optimal',
        class_weights: Optional[Dict[str, float]] = None
    ) -> Dict[str, Any]:
        """Enhanced threshold optimization with multiple strategies."""
        return self.threshold_optimizer.optimize_thresholds(
            y_true, y_scores, self.label_names, strategy, class_weights
        )
    
    def validate_config(self, config: TrainingConfig) -> Tuple[bool, List[str]]:
        """Validate configuration for multi-label classification."""
        errors = []
        
        # Check classification type
        if config.classification_type != ClassificationType.MULTILABEL:
            errors.append(f"Expected multilabel classification, got {config.classification_type}")
        
        # Check text columns
        if not config.text_columns:
            errors.append("At least one text column must be specified")
        
        # Check label columns
        if not config.label_columns:
            errors.append("At least one label column must be specified for multi-label classification")
        
        # Check training method specific requirements
        if config.training_method == TrainingMethod.LLM:
            if not config.llm_provider:
                errors.append("LLM provider must be specified for LLM training method")
            if not config.prompt_template:
                errors.append("Prompt template must be specified for LLM training method")
        
        return len(errors) == 0, errors
    
    def get_recommended_config(
        self,
        data: pd.DataFrame,
        training_method: TrainingMethod
    ) -> TrainingConfig:
        """Get enhanced recommended configuration for multi-label classification."""
        config = super().get_recommended_config(data, training_method)

        # Detect text columns
        text_cols = [col for col in data.columns if data[col].dtype == 'object' and 'text' in col.lower()]
        if not text_cols:
            text_cols = [col for col in data.columns if data[col].dtype == 'object'][:1]

        # Detect label columns (binary columns or columns with lists/arrays)
        label_cols = []
        for col in data.columns:
            if col in text_cols:
                continue

            # Check if column contains binary values (0/1 or True/False)
            unique_vals = data[col].dropna().unique()
            if len(unique_vals) == 2 and set(unique_vals).issubset({0, 1, True, False}):
                label_cols.append(col)
            # Check if column contains lists or comma-separated values
            elif data[col].dtype == 'object':
                sample_val = data[col].dropna().iloc[0] if len(data[col].dropna()) > 0 else None
                if isinstance(sample_val, (list, tuple)) or (isinstance(sample_val, str) and ',' in sample_val):
                    label_cols.append(col)

        config.text_columns = text_cols
        config.label_columns = label_cols

        # Enhanced analysis for multi-label data
        if label_cols:
            self.label_names = label_cols
            self.num_labels = len(label_cols)

            # Perform comprehensive correlation analysis
            try:
                correlation_results = self.analyze_label_correlations_enhanced(data, label_cols)
                self.label_correlations = correlation_results

                # Log insights from correlation analysis
                if 'summary_statistics' in correlation_results:
                    stats = correlation_results['summary_statistics']
                    logger.info(f"Label correlation analysis: avg_correlation={stats.get('avg_correlation', 0):.3f}, "
                              f"high_correlations={stats.get('num_high_correlations', 0)}")

                # Adjust configuration based on correlation insights
                if stats.get('num_high_correlations', 0) > 0:
                    # High correlations detected - may need special handling
                    config.gradient_accumulation_steps = max(config.gradient_accumulation_steps, 2)
                    logger.info("High label correlations detected - adjusting training parameters")

            except Exception as e:
                logger.warning(f"Enhanced correlation analysis failed: {e}")
                self.label_correlations = {'error': str(e)}

            # Initialize default thresholds (will be optimized during training)
            self.label_thresholds = {label: 0.5 for label in label_cols}

            # Analyze label distribution for imbalance
            label_stats = self._analyze_label_distribution(data, label_cols)
            if label_stats.get('imbalance_detected', False):
                # Adjust for label imbalance
                config.class_weight = 'balanced'
                logger.info("Label imbalance detected - enabling balanced class weights")

        # Enhanced LLM configuration
        if training_method == TrainingMethod.LLM:
            config.prompt_template = self._get_enhanced_prompt_template()
            config.temperature = 0.1
            config.max_tokens = 100  # Increased for multi-label responses

            # Add few-shot examples if available
            if hasattr(config, 'few_shot_examples'):
                config.few_shot_examples = self._generate_few_shot_examples(data, label_cols)

        return config

    def _analyze_label_distribution(self, data: pd.DataFrame, label_columns: List[str]) -> Dict[str, Any]:
        """Analyze label distribution for imbalance detection."""
        stats = {
            'label_frequencies': {},
            'imbalance_detected': False,
            'imbalance_ratio': 0.0,
            'recommendations': []
        }

        try:
            # Convert to binary data for analysis
            binary_data = self.correlation_analyzer._prepare_binary_data(data, label_columns)

            # Calculate label frequencies
            for col in label_columns:
                if col in binary_data.columns:
                    freq = binary_data[col].sum()
                    total = len(binary_data)
                    stats['label_frequencies'][col] = {
                        'count': int(freq),
                        'percentage': float(freq / total * 100),
                        'imbalance_ratio': float(min(freq, total - freq) / max(freq, total - freq, 1))
                    }

            # Detect overall imbalance
            imbalance_ratios = [info['imbalance_ratio'] for info in stats['label_frequencies'].values()]
            if imbalance_ratios:
                avg_imbalance = np.mean(imbalance_ratios)
                stats['imbalance_ratio'] = float(avg_imbalance)

                if avg_imbalance < 0.3:  # Significant imbalance threshold
                    stats['imbalance_detected'] = True
                    stats['recommendations'].append('Consider using balanced class weights')
                    stats['recommendations'].append('Consider oversampling minority labels')

                    # Find most imbalanced labels
                    most_imbalanced = min(stats['label_frequencies'].items(),
                                        key=lambda x: x[1]['imbalance_ratio'])
                    stats['most_imbalanced_label'] = most_imbalanced[0]

        except Exception as e:
            logger.warning(f"Label distribution analysis failed: {e}")
            stats['error'] = str(e)

        return stats

    def _get_enhanced_prompt_template(self) -> str:
        """Get enhanced prompt template for multi-label classification."""
        if self.label_names:
            labels_str = ", ".join(self.label_names)
            return f"""You are an expert text classifier. Analyze the following text and identify which labels apply.

Available labels: {labels_str}

Instructions:
1. Multiple labels can apply to the same text
2. If no labels apply, respond with "none"
3. List all applicable labels separated by commas
4. Be precise and confident in your classifications

Text to classify: {{text}}

Applicable labels:"""
        else:
            return """You are an expert text classifier. Analyze the following text and identify all applicable labels or categories.

Instructions:
1. Multiple labels/categories can apply to the same text
2. If no labels apply, respond with "none"
3. List all applicable labels separated by commas
4. Be precise and confident in your classifications

Text to classify: {text}

Applicable labels:"""

    def _generate_few_shot_examples(self, data: pd.DataFrame, label_columns: List[str]) -> List[Dict[str, str]]:
        """Generate few-shot examples for LLM prompting."""
        examples = []

        try:
            # Sample a few diverse examples
            sample_size = min(5, len(data))
            sample_data = data.sample(n=sample_size, random_state=42)

            text_col = self.label_names[0] if self.label_names else data.columns[0]

            for _, row in sample_data.iterrows():
                text = str(row[text_col])

                # Get applicable labels
                applicable_labels = []
                for label_col in label_columns:
                    if row[label_col] in [1, True, '1', 'true', 'True', 'yes', 'Yes']:
                        applicable_labels.append(label_col)

                if applicable_labels:
                    labels_str = ", ".join(applicable_labels)
                else:
                    labels_str = "none"

                examples.append({
                    'text': text,
                    'labels': labels_str
                })

        except Exception as e:
            logger.warning(f"Few-shot example generation failed: {e}")

        return examples
    
    def _get_default_prompt_template(self) -> str:
        """Get default prompt template for multi-label classification."""
        if self.label_names:
            labels_str = ", ".join(self.label_names)
            return f"""Analyze the following text and identify which of these labels apply (multiple labels can apply): {labels_str}

Text: {{text}}

Applicable labels (list all that apply, separated by commas):"""
        else:
            return """Analyze the following text and identify all applicable labels or categories.

Text: {text}

Applicable labels:"""
    
    def analyze_label_correlations(self, data: pd.DataFrame, label_columns: List[str]) -> Dict[str, Any]:
        """Analyze correlations between labels."""
        correlations = {}
        
        try:
            # Convert label columns to binary if needed
            binary_data = pd.DataFrame()
            for col in label_columns:
                if data[col].dtype in ['int64', 'float64', 'bool']:
                    binary_data[col] = data[col].astype(int)
                else:
                    # Try to parse as binary
                    binary_data[col] = data[col].map({'True': 1, 'False': 0, 'true': 1, 'false': 0, '1': 1, '0': 0}).fillna(0)
            
            # Calculate correlation matrix
            if len(binary_data.columns) > 1:
                corr_matrix = binary_data.corr()
                
                # Find highly correlated pairs
                high_correlations = []
                for i in range(len(corr_matrix.columns)):
                    for j in range(i+1, len(corr_matrix.columns)):
                        corr_val = corr_matrix.iloc[i, j]
                        if abs(corr_val) > 0.7:  # High correlation threshold
                            high_correlations.append({
                                'label1': corr_matrix.columns[i],
                                'label2': corr_matrix.columns[j],
                                'correlation': corr_val
                            })
                
                correlations = {
                    'correlation_matrix': corr_matrix.to_dict(),
                    'high_correlations': high_correlations,
                    'avg_correlation': corr_matrix.values[np.triu_indices_from(corr_matrix.values, k=1)].mean()
                }
            
        except Exception as e:
            logger.warning(f"Failed to analyze label correlations: {e}")
            correlations = {'error': str(e)}
        
        return correlations
    
    def optimize_thresholds(self, y_true: np.ndarray, y_scores: np.ndarray, label_names: List[str]) -> Dict[str, float]:
        """Optimize classification thresholds for each label."""
        optimal_thresholds = {}
        
        try:
            from sklearn.metrics import precision_recall_curve
            
            for i, label in enumerate(label_names):
                if i < y_scores.shape[1]:
                    # Calculate precision-recall curve
                    precision, recall, thresholds = precision_recall_curve(y_true[:, i], y_scores[:, i])
                    
                    # Find threshold that maximizes F1 score
                    f1_scores = 2 * (precision * recall) / (precision + recall + 1e-8)
                    best_threshold_idx = np.argmax(f1_scores)
                    
                    if best_threshold_idx < len(thresholds):
                        optimal_thresholds[label] = float(thresholds[best_threshold_idx])
                    else:
                        optimal_thresholds[label] = 0.5
                else:
                    optimal_thresholds[label] = 0.5
                    
        except Exception as e:
            logger.warning(f"Failed to optimize thresholds: {e}")
            # Fall back to default thresholds
            optimal_thresholds = {label: 0.5 for label in label_names}
        
        return optimal_thresholds
    
    async def train_custom_model(
        self,
        data: pd.DataFrame,
        config: TrainingConfig,
        progress_callback: Optional[callable] = None
    ) -> TrainingResult:
        """Train an enhanced custom multi-label classification model with advanced features."""
        start_time = time.time()

        try:
            # Validate configuration
            is_valid, errors = self.validate_config(config)
            if not is_valid:
                return TrainingResult(
                    model_id="",
                    training_time=0,
                    final_metrics={},
                    training_history=[],
                    error=f"Configuration validation failed: {'; '.join(errors)}"
                )

            if progress_callback:
                progress_callback({"stage": "preparation", "progress": 0.05})

            # Enhanced data preparation with correlation analysis
            text_col = config.text_columns[0]
            label_cols = config.label_columns

            texts = data[text_col].astype(str).tolist()

            # Perform comprehensive label correlation analysis
            logger.info("Performing enhanced label correlation analysis...")
            correlation_results = self.analyze_label_correlations_enhanced(data, label_cols)
            self.label_correlations = correlation_results

            if progress_callback:
                progress_callback({"stage": "correlation_analysis", "progress": 0.1})
            
            # Prepare multi-label targets
            labels_matrix = []
            for _, row in data.iterrows():
                label_vector = []
                for label_col in label_cols:
                    val = row[label_col]
                    if isinstance(val, (list, tuple)):
                        # Handle list of labels
                        label_vector.append(1 if len(val) > 0 else 0)
                    elif isinstance(val, str) and ',' in val:
                        # Handle comma-separated labels
                        label_vector.append(1 if val.strip() else 0)
                    else:
                        # Handle binary values
                        label_vector.append(1 if val else 0)
                labels_matrix.append(label_vector)
            
            labels_matrix = np.array(labels_matrix)
            self.num_labels = labels_matrix.shape[1]
            self.label_names = label_cols
            
            if progress_callback:
                progress_callback({"stage": "model_setup", "progress": 0.2})
            
            # Initialize tokenizer and model
            tokenizer = AutoTokenizer.from_pretrained(config.base_model)
            model = AutoModelForSequenceClassification.from_pretrained(
                config.base_model,
                num_labels=self.num_labels,
                problem_type="multi_label_classification"
            )
            
            # Tokenize data
            encodings = tokenizer(
                texts,
                truncation=True,
                padding=True,
                max_length=config.max_length,
                return_tensors="pt"
            )
            
            if progress_callback:
                progress_callback({"stage": "training", "progress": 0.3})
            
            # Create dataset
            class MultiLabelDataset(torch.utils.data.Dataset):
                def __init__(self, encodings, labels):
                    self.encodings = encodings
                    self.labels = labels
                
                def __getitem__(self, idx):
                    item = {key: torch.tensor(val[idx]) for key, val in self.encodings.items()}
                    item['labels'] = torch.tensor(self.labels[idx], dtype=torch.float)
                    return item
                
                def __len__(self):
                    return len(self.labels)
            
            # Split data
            from sklearn.model_selection import train_test_split
            train_texts, val_texts, train_labels, val_labels = train_test_split(
                texts, labels_matrix, test_size=config.validation_split, random_state=42
            )
            
            train_encodings = tokenizer(train_texts, truncation=True, padding=True, max_length=config.max_length, return_tensors="pt")
            val_encodings = tokenizer(val_texts, truncation=True, padding=True, max_length=config.max_length, return_tensors="pt")
            
            train_dataset = MultiLabelDataset(train_encodings, train_labels)
            val_dataset = MultiLabelDataset(val_encodings, val_labels)
            
            # Training setup
            from transformers import Trainer, TrainingArguments
            
            training_args = TrainingArguments(
                output_dir='./results',
                num_train_epochs=config.num_epochs,
                per_device_train_batch_size=config.batch_size,
                per_device_eval_batch_size=config.batch_size,
                warmup_steps=config.warmup_steps,
                weight_decay=config.weight_decay,
                logging_dir='./logs',
                evaluation_strategy=config.evaluation_strategy,
                save_strategy=config.save_strategy,
                fp16=config.fp16,
                gradient_accumulation_steps=config.gradient_accumulation_steps,
                gradient_checkpointing=config.gradient_checkpointing,
                learning_rate=config.learning_rate,
            )

            def compute_metrics(eval_pred):
                predictions, labels = eval_pred
                # Apply sigmoid to get probabilities
                sigmoid = torch.nn.Sigmoid()
                probs = sigmoid(torch.Tensor(predictions)).numpy()

                # Apply thresholds
                y_pred = (probs > 0.5).astype(int)
                y_true = labels.astype(int)

                return {
                    'hamming_loss': hamming_loss(y_true, y_pred),
                    'subset_accuracy': accuracy_score(y_true, y_pred),
                    'micro_f1': f1_score(y_true, y_pred, average='micro'),
                    'macro_f1': f1_score(y_true, y_pred, average='macro'),
                    'samples_f1': f1_score(y_true, y_pred, average='samples')
                }

            trainer = Trainer(
                model=model,
                args=training_args,
                train_dataset=train_dataset,
                eval_dataset=val_dataset,
                compute_metrics=compute_metrics,
            )

            # Train model
            trainer.train()

            if progress_callback:
                progress_callback({"stage": "evaluation", "progress": 0.8})

            # Enhanced evaluation with multiple threshold optimization strategies
            logger.info("Starting enhanced model evaluation and threshold optimization...")
            val_predictions = trainer.predict(val_dataset)
            sigmoid = torch.nn.Sigmoid()
            val_probs = sigmoid(torch.Tensor(val_predictions.predictions)).numpy()

            if progress_callback:
                progress_callback({"stage": "threshold_optimization", "progress": 0.85})

            # Try multiple threshold optimization strategies
            optimization_strategies = ['f1_optimal', 'precision_recall_balance', 'youden_index']
            best_strategy = 'f1_optimal'
            best_score = 0.0
            all_optimization_results = {}

            for strategy in optimization_strategies:
                try:
                    optimization_result = self.optimize_thresholds_enhanced(
                        val_labels, val_probs, strategy
                    )
                    all_optimization_results[strategy] = optimization_result

                    # Evaluate this strategy
                    strategy_score = optimization_result['global_metrics'].get('macro_f1', 0.0)
                    if strategy_score > best_score:
                        best_score = strategy_score
                        best_strategy = strategy

                    logger.info(f"Strategy {strategy}: macro_f1={strategy_score:.4f}")

                except Exception as e:
                    logger.warning(f"Threshold optimization strategy {strategy} failed: {e}")

            # Use the best strategy results
            best_optimization = all_optimization_results.get(best_strategy, {})
            optimal_thresholds = best_optimization.get('optimal_thresholds', {label: 0.5 for label in self.label_names})
            self.label_thresholds = optimal_thresholds
            self.threshold_analysis = best_optimization.get('threshold_analysis', {})

            logger.info(f"Best threshold optimization strategy: {best_strategy} (macro_f1={best_score:.4f})")

            # Apply optimized thresholds
            y_pred_optimized = np.zeros_like(val_probs)
            for i, label in enumerate(self.label_names):
                threshold = optimal_thresholds.get(label, 0.5)
                y_pred_optimized[:, i] = (val_probs[:, i] >= threshold).astype(int)

            # Calculate comprehensive final metrics
            final_metrics = self.calculate_comprehensive_metrics(
                val_labels, y_pred_optimized, val_probs
            )

            # Add enhanced analysis results to final metrics
            final_metrics.update({
                'optimal_thresholds': optimal_thresholds,
                'threshold_optimization_strategy': best_strategy,
                'threshold_analysis': self.threshold_analysis,
                'all_optimization_results': all_optimization_results,
                'label_correlations': self.label_correlations,
                'training_strategy': 'enhanced_multilabel_v3'
            })

            if progress_callback:
                progress_callback({"stage": "model_saving", "progress": 0.95})

            # Save model artifacts with enhanced metadata
            model_id = f"enhanced_multilabel_model_{int(time.time())}"
            model_path = f"./model_artifacts/{model_id}"

            # Create directory if it doesn't exist
            import os
            os.makedirs(model_path, exist_ok=True)

            trainer.save_model(model_path)
            tokenizer.save_pretrained(model_path)

            # Save enhanced configuration and analysis results
            import json
            config_data = {
                'model_id': model_id,
                'classification_type': 'multilabel',
                'label_names': self.label_names,
                'num_labels': self.num_labels,
                'optimal_thresholds': optimal_thresholds,
                'threshold_analysis': self.threshold_analysis,
                'label_correlations': self.label_correlations,
                'training_config': {
                    'base_model': config.base_model,
                    'max_length': config.max_length,
                    'batch_size': config.batch_size,
                    'learning_rate': config.learning_rate,
                    'num_epochs': config.num_epochs
                },
                'performance_summary': {
                    'best_threshold_strategy': best_strategy,
                    'final_macro_f1': final_metrics.get('macro_f1', 0.0),
                    'final_micro_f1': final_metrics.get('micro_f1', 0.0),
                    'hamming_loss': final_metrics.get('hamming_loss', 1.0),
                    'subset_accuracy': final_metrics.get('subset_accuracy', 0.0)
                }
            }

            with open(f"{model_path}/enhanced_config.json", 'w') as f:
                json.dump(config_data, f, indent=2, default=str)

            # Store model info
            self.model = model
            self.tokenizer = tokenizer
            self.is_trained = True
            self.optimization_results = all_optimization_results

            training_time = time.time() - start_time
            logger.info(f"Enhanced multi-label training completed in {training_time:.2f}s")

            if progress_callback:
                progress_callback({"stage": "complete", "progress": 1.0})

            return TrainingResult(
                model_id=model_id,
                training_time=training_time,
                final_metrics=final_metrics,
                training_history=[],  # TODO: Extract from trainer logs
                model_path=model_path,
                tokenizer_path=model_path,
                config_path=f"{model_path}/enhanced_config.json"
            )

        except Exception as e:
            logger.error(f"Training failed: {e}")
            return TrainingResult(
                model_id="",
                training_time=time.time() - start_time,
                final_metrics={},
                training_history=[],
                error=str(e)
            )

    async def llm_inference(
        self,
        texts: List[str],
        config: TrainingConfig,
        progress_callback: Optional[callable] = None
    ) -> List[ClassificationResult]:
        """Perform multi-label classification using LLM inference."""
        results = []

        try:
            # Use sequential thinking LLM classifier for enhanced results
            from .sequential_llm_classifier import SequentialLLMClassifier

            if progress_callback:
                progress_callback({"stage": "llm_setup", "progress": 0.1})

            # Initialize sequential classifier
            sequential_classifier = SequentialLLMClassifier()

            # Use sequential thinking approach for enhanced classification
            results = await sequential_classifier.classify_with_sequential_thinking(
                texts=texts,
                classification_type="multi-label",
                llm_provider=config.llm_provider,
                llm_model=config.llm_model,
                llm_endpoint=getattr(config, 'llm_endpoint', ''),
                api_key=getattr(config, 'api_key', None),
                custom_prompt=config.prompt_template,
                temperature=config.temperature,
                max_tokens=config.max_tokens,
                progress_callback=progress_callback
            )

            return results

        except Exception as e:
            logger.error(f"LLM inference failed: {e}")
            # Return error results for all texts
            return [
                ClassificationResult(
                    text=text,
                    predictions=["error"],
                    confidence=0.0,
                    processing_time=0.0,
                    method_used="llm_inference",
                    reasoning=f"Error: {str(e)}",
                    metadata={"error": str(e)}
                )
                for text in texts
            ]

    async def predict(
        self,
        texts: List[str],
        model_id: Optional[str] = None
    ) -> List[ClassificationResult]:
        """Make enhanced predictions using a trained multi-label classification model."""
        if not self.is_trained or self.model is None or self.tokenizer is None:
            raise ValueError("Model not trained or loaded. Train a model first or load an existing one.")

        results = []

        try:
            # Tokenize texts in batches for efficiency
            batch_size = 32
            all_predictions = []

            for i in range(0, len(texts), batch_size):
                batch_texts = texts[i:i + batch_size]

                encodings = self.tokenizer(
                    batch_texts,
                    truncation=True,
                    padding=True,
                    max_length=512,
                    return_tensors="pt"
                )

                # Make predictions
                with torch.no_grad():
                    outputs = self.model(**encodings)
                    # Apply sigmoid to get probabilities
                    sigmoid = torch.nn.Sigmoid()
                    batch_predictions = sigmoid(outputs.logits)
                    all_predictions.extend(batch_predictions.numpy())

            # Process results with enhanced analysis
            for i, text in enumerate(texts):
                start_time = time.time()

                probs = all_predictions[i]

                # Apply optimized per-label thresholds
                predicted_labels = []
                prob_dict = {}
                threshold_info = {}

                for j, label_name in enumerate(self.label_names):
                    if j < len(probs):
                        prob = float(probs[j])
                        threshold = self.label_thresholds.get(label_name, 0.5)

                        prob_dict[label_name] = prob
                        threshold_info[label_name] = {
                            'threshold': threshold,
                            'probability': prob,
                            'above_threshold': prob >= threshold
                        }

                        if prob >= threshold:
                            predicted_labels.append(label_name)

                # Enhanced confidence calculation
                if predicted_labels:
                    # Confidence based on how far above threshold the predictions are
                    confidence_scores = []
                    for label in predicted_labels:
                        prob = prob_dict[label]
                        threshold = self.label_thresholds.get(label, 0.5)
                        # Normalize confidence based on distance from threshold
                        confidence_scores.append(min(1.0, (prob - threshold) / (1.0 - threshold + 1e-8)))

                    confidence = float(np.mean(confidence_scores))
                else:
                    # If no labels predicted, confidence based on how close we were
                    max_prob = max(prob_dict.values()) if prob_dict else 0.0
                    confidence = float(max_prob * 0.5)  # Lower confidence for no predictions
                    predicted_labels = ["none"]

                # Additional analysis for enhanced reasoning
                reasoning_parts = []
                if predicted_labels and predicted_labels != ["none"]:
                    reasoning_parts.append(f"Predicted {len(predicted_labels)} labels")

                    # Mention high-confidence predictions
                    high_conf_labels = [label for label in predicted_labels
                                      if prob_dict[label] > 0.8]
                    if high_conf_labels:
                        reasoning_parts.append(f"High confidence: {', '.join(high_conf_labels)}")

                    # Check for label correlations in predictions
                    if hasattr(self, 'label_correlations') and 'high_correlations' in self.label_correlations:
                        correlated_pairs = []
                        for corr_info in self.label_correlations['high_correlations']:
                            if (corr_info['label1'] in predicted_labels and
                                corr_info['label2'] in predicted_labels):
                                correlated_pairs.append(f"{corr_info['label1']}-{corr_info['label2']}")

                        if correlated_pairs:
                            reasoning_parts.append(f"Correlated labels detected: {', '.join(correlated_pairs)}")
                else:
                    reasoning_parts.append("No labels exceeded their optimized thresholds")

                reasoning = "; ".join(reasoning_parts) if reasoning_parts else "Standard multi-label prediction"

                processing_time = time.time() - start_time

                results.append(ClassificationResult(
                    text=text,
                    predictions=predicted_labels,
                    confidence=confidence,
                    probabilities=prob_dict,
                    processing_time=processing_time,
                    method_used="enhanced_multilabel_custom",
                    reasoning=reasoning,
                    metadata={
                        "model_id": model_id,
                        "num_labels": self.num_labels,
                        "thresholds_used": self.label_thresholds,
                        "threshold_details": threshold_info,
                        "optimization_strategy": getattr(self, 'threshold_analysis', {}).get('strategy', 'f1_optimal'),
                        "label_correlations_available": bool(hasattr(self, 'label_correlations') and self.label_correlations)
                    }
                ))
            return results

        except Exception as e:
            logger.error(f"Enhanced prediction failed: {e}")
            return [
                ClassificationResult(
                    text=text,
                    predictions=["error"],
                    confidence=0.0,
                    processing_time=0.0,
                    method_used="enhanced_multilabel_custom",
                    reasoning=f"Enhanced prediction error: {str(e)}",
                    metadata={"error": str(e)}
                )
                for text in texts
            ]


# Backward compatibility alias
MultiLabelEngine = EnhancedMultiLabelEngine
