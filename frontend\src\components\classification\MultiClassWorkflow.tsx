/**
 * MultiClassWorkflow.tsx
 *
 * Complete multi-class classification workflow component following the hierarchical workflow pattern.
 * Features strategy selection (softmax vs one-vs-rest vs one-vs-one) and advanced multi-class metrics.
 * Implements comprehensive step management, validation, and dual data upload support.
 */

import React, { useState, useEffect, useMemo, useCallback, useRef } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Layers,
  Upload,
  Settings,
  Brain,
  Zap,
  BarChart3,
  Download,
  CheckCircle2,
  ArrowRight,
  ArrowLeft,
  Play,
  AlertCircle,
  Grid3X3,
  Target,
  Activity,
  Shield,
  Rocket,
  Info
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { debounce } from 'lodash';

// Import services
import { uploadFile, UploadedFile } from "@/services/fileUploadApi";
import {
  startUniversalTraining,
  startUniversalInference,
  getUniversalTaskStatus,
  UniversalTrainingRequest,
  UniversalInferenceRequest
} from "@/services/universalApi";
import {
  startMultiClassTraining,
  startMultiClassInference,
  getTaskStatus,
  validateMultiClassData,
  getStrategyRecommendations,
  MultiClassTrainingRequest,
  MultiClassInferenceRequest
} from '@/services/multiClassApi';
import { getUserLicense, getLicenseFeatures } from "@/services/licenseApi";

// Import classification components
import { ClassBalanceAnalyzer } from "./ClassBalanceAnalyzer";
import { TrainingProgressMonitor } from "./TrainingProgressMonitor";
import { EnhancedModelComparisonDashboard } from "./EnhancedModelComparisonDashboard";
import { UnifiedFileUploadZone } from "../UnifiedFileUploadZone";
import { MultiClassTrainingConfig, MultiClassTrainingConfig as MultiClassTrainingConfigType } from "./MultiClassTrainingConfig";
import { MultiClassModelManager, MultiClassModel } from "./MultiClassModelManager";
import { MultiClassResults } from "./MultiClassResults";
import { DeployStep } from "./DeployStep";
import { LLMConfigurationPanel } from "../LLMConfigurationPanel";

// Import unified data management
import { unifiedDataManager, DualDataUpload } from "@/services/unifiedDataManager";

interface MultiClassWorkflowProps {
  initialData?: any;
  onComplete: (results: any) => void;
}

interface DualDataUpload {
  trainingData: {
    fileId: string;
    fileInfo: any;
    purpose: string;
  };
  classificationData: {
    fileId: string;
    fileInfo: any;
    purpose: string;
  };
  dualUpload: boolean;
}

interface MultiClassTrainingResults {
  task_id: string;
  model_id: string;
  metrics: {
    accuracy: number;
    precision: number;
    recall: number;
    f1_score: number;
    macro_avg: {
      precision: number;
      recall: number;
      f1_score: number;
    };
    weighted_avg: {
      precision: number;
      recall: number;
      f1_score: number;
    };
    confusion_matrix: number[][];
    classification_report: any;
    per_class_metrics: Array<{
      class: string;
      precision: number;
      recall: number;
      f1_score: number;
      support: number;
    }>;
  };
  strategy: 'softmax' | 'ovr' | 'ovo';
  training_time: number;
  model_size: number;
}

type ClassificationStrategy = 'softmax' | 'ovr' | 'ovo';

const STRATEGY_INFO = {
  softmax: {
    name: 'Softmax (Native Multi-class)',
    description: 'Direct multi-class classification with softmax output layer. Most efficient for balanced datasets.',
    pros: ['Fastest training', 'Most memory efficient', 'Natural probability distribution'],
    cons: ['May struggle with imbalanced data', 'Less flexible than ensemble methods'],
    complexity: 'Low',
    color: '#3b82f6'
  },
  ovr: {
    name: 'One-vs-Rest (OvR)',
    description: 'Trains one binary classifier per class against all other classes. Good for imbalanced datasets.',
    pros: ['Handles imbalanced data well', 'Interpretable', 'Scales well with classes'],
    cons: ['Slower training', 'More memory usage', 'Potential calibration issues'],
    complexity: 'Medium',
    color: '#22c55e'
  },
  ovo: {
    name: 'One-vs-One (OvO)',
    description: 'Trains one binary classifier for each pair of classes. Best for small datasets with many classes.',
    pros: ['Good for small datasets', 'Robust to outliers', 'High accuracy potential'],
    cons: ['Very slow for many classes', 'High memory usage', 'Complex interpretation'],
    complexity: 'High',
    color: '#f59e0b'
  }
};

export const MultiClassWorkflow: React.FC<MultiClassWorkflowProps> = ({
  initialData,
  onComplete
}) => {
  const { toast } = useToast();

  // Navigation hooks
  const navigate = useNavigate();
  const location = useLocation();

  // Check if we came from expert workflow
  const fromExpertWorkflow = location.state?.fromExpertWorkflow ||
                            sessionStorage.getItem('expertWorkflowDualData');

  // Core workflow state
  const [currentStep, setCurrentStep] = useState(1);
  const [uploadedData, setUploadedData] = useState<any>(null);
  const [dualData, setDualData] = useState<DualDataUpload | null>(null);
  const [selectedTextColumn, setSelectedTextColumn] = useState<string>('');
  const [selectedLabelColumn, setSelectedLabelColumn] = useState<string>('');
  const [selectedLabelColumns, setSelectedLabelColumns] = useState<string[]>([]);
  const [labelFormat, setLabelFormat] = useState<'single' | 'multiple'>('single');
  const [detectedClasses, setDetectedClasses] = useState<string[]>([]);
  const [classDistribution, setClassDistribution] = useState<Record<string, number>>({});

  // Strategy selection
  const [selectedStrategy, setSelectedStrategy] = useState<ClassificationStrategy>('softmax');

  // Configuration and method selection
  const [trainingMethod, setTrainingMethod] = useState<'custom' | 'llm' | 'model_classification'>('custom');
  const [llmProvider, setLlmProvider] = useState<string>('openai');
  const [llmModel, setLlmModel] = useState<string>('gpt-3.5-turbo');
  const [customPrompt, setCustomPrompt] = useState<string>('');
  const [trainingConfig, setTrainingConfig] = useState<MultiClassTrainingConfigType | null>(null);

  // Workflow type management
  const [workflowType, setWorkflowType] = useState<'training' | 'inference' | 'model_classification'>('training');

  // Model management
  const [selectedModel, setSelectedModel] = useState<MultiClassModel | null>(null);
  const [showModelManager, setShowModelManager] = useState(false);
  const [showTrainingConfig, setShowTrainingConfig] = useState(false);

  // Training state
  const [isTraining, setIsTraining] = useState(false);
  const [trainingProgress, setTrainingProgress] = useState(0);
  const [trainingTaskId, setTrainingTaskId] = useState<string | null>(null);
  const [trainingResults, setTrainingResults] = useState<MultiClassTrainingResults | null>(null);
  const [trainingStatus, setTrainingStatus] = useState<{
    currentEpoch?: number;
    totalEpochs?: number;
    currentLoss?: number;
    currentAccuracy?: number;
    estimatedTimeRemaining?: number;
    stage?: string;
  }>({});

  // Classification state
  const [isClassifying, setIsClassifying] = useState(false);
  const [classificationProgress, setClassificationProgress] = useState(0);
  const [classificationTaskId, setClassificationTaskId] = useState<string | null>(null);
  const [classificationResults, setClassificationResults] = useState<any>(null);
  const [classificationStatus, setClassificationStatus] = useState<{
    processedSamples?: number;
    totalSamples?: number;
    currentBatch?: number;
    totalBatches?: number;
    averageConfidence?: number;
    stage?: string;
  }>({});

  // Enhanced error handling and validation
  const [error, setError] = useState<string | null>(null);
  const [errors, setErrors] = useState<string[]>([]);
  const [isValidating, setIsValidating] = useState(false);
  const [errorHistory, setErrorHistory] = useState<Array<{
    timestamp: Date;
    step: string;
    error: string;
    context?: any;
  }>>([]);
  const [retryCount, setRetryCount] = useState(0);
  const [isRecovering, setIsRecovering] = useState(false);

  // License management
  const [userLicense, setUserLicense] = useState<{
    type: 'personal' | 'professional' | 'enterprise';
    features: string[];
    limits: {
      max_deployments?: number;
      max_api_calls_per_month?: number;
      cloud_deployment?: boolean;
      enterprise_features?: boolean;
    };
  }>({
    type: 'personal',
    features: [],
    limits: {}
  });

  // Centralized step management following hierarchical pattern
  const getWorkflowSteps = () => {
    const baseSteps = [
      { id: 1, title: "Data Upload", icon: Upload, key: "upload" },
      { id: 2, title: "Configuration", icon: Settings, key: "config" },
      { id: 3, title: "Strategy Selection", icon: Grid3X3, key: "strategy" },
      { id: 4, title: "Method Selection", icon: Brain, key: "method" },
    ];

    switch (workflowType) {
      case 'inference':
        return [
          ...baseSteps,
          { id: 5, title: "Classification", icon: Zap, key: "classification" },
          { id: 6, title: "Results", icon: BarChart3, key: "results" },
          { id: 7, title: "Deploy", icon: Rocket, key: "deploy" }
        ];
      case 'training':
        return [
          ...baseSteps,
          { id: 5, title: "Training", icon: Zap, key: "training" },
          { id: 6, title: "Classification", icon: Target, key: "classification" },
          { id: 7, title: "Results", icon: BarChart3, key: "results" },
          { id: 8, title: "Deploy", icon: Rocket, key: "deploy" }
        ];
      case 'model_classification':
        return [
          ...baseSteps,
          { id: 5, title: "Model Selection", icon: Shield, key: "model_selection" },
          { id: 6, title: "Classification", icon: Target, key: "classification" },
          { id: 7, title: "Results", icon: BarChart3, key: "results" },
          { id: 8, title: "Deploy", icon: Rocket, key: "deploy" }
        ];
      default:
        return baseSteps;
    }
  };

  const isCurrentStep = (stepKey: string) => {
    const steps = getWorkflowSteps();
    const step = steps.find(s => s.key === stepKey);
    return step ? currentStep === step.id : false;
  };

  const goToStep = (stepKey: string) => {
    const steps = getWorkflowSteps();
    const step = steps.find(s => s.key === stepKey);
    if (step) {
      setCurrentStep(step.id);
    }
  };

  const steps = getWorkflowSteps().map(step => ({
    ...step,
    status: currentStep === step.id ? "current" : currentStep > step.id ? "complete" : "pending"
  }));

  // Step validation system following hierarchical pattern
  const validateStep = (step: number): boolean => {
    const newErrors: string[] = [];

    switch (step) {
      case 1: // Upload
        if (!uploadedData && !dualData) {
          newErrors.push("Please upload data to continue");
        }
        break;
      case 2: // Configuration
        if (!selectedTextColumn) {
          newErrors.push("Please select a text column");
        }
        if (labelFormat === 'single' && !selectedLabelColumn) {
          newErrors.push("Please select a label column");
        }
        if (labelFormat === 'multiple' && selectedLabelColumns.length === 0) {
          newErrors.push("Please select label columns");
        }
        if (detectedClasses.length === 0) {
          newErrors.push("No classes detected in the data");
        }
        break;
      case 3: // Strategy Selection
        if (!selectedStrategy) {
          newErrors.push("Please select a classification strategy");
        }
        break;
      case 4: // Method Selection
        if (!trainingMethod) {
          newErrors.push("Please select a training method");
        }
        if (trainingMethod === 'custom' && !trainingConfig) {
          newErrors.push("Please configure training parameters");
        }
        if (trainingMethod === 'llm' && (!llmProvider || !llmModel)) {
          newErrors.push("Please configure LLM settings");
        }
        if (trainingMethod === 'model_classification' && !selectedModel) {
          newErrors.push("Please select a trained model");
        }
        break;
    }

    setErrors(newErrors);
    return newErrors.length === 0;
  };

  const handleNext = () => {
    if (validateStep(currentStep)) {
      if (currentStep < steps.length) {
        setCurrentStep(currentStep + 1);
      }
    } else {
      toast({
        title: "Validation Error",
        description: "Please fix the errors before proceeding",
        variant: "destructive"
      });
    }
  };

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  // Initialize dual data from session storage (from expert workflow)
  useEffect(() => {
    const checkForDualData = () => {
      const dualDataString = sessionStorage.getItem('expertWorkflowDualData');
      if (dualDataString) {
        try {
          const parsedDualData = JSON.parse(dualDataString);
          if (parsedDualData && parsedDualData.trainingData && parsedDualData.classificationData) {
            console.log('Loading dual data from expert workflow:', parsedDualData);

            // Set dual data state
            setDualData(parsedDualData);
            setUploadedData(parsedDualData.trainingData.fileInfo);

            // Auto-select columns from training data
            const trainingColumns = parsedDualData.trainingData.fileInfo.columns || [];
            const textColumn = trainingColumns.find(col =>
              col.toLowerCase().includes('text') ||
              col.toLowerCase().includes('content') ||
              col.toLowerCase().includes('message')
            ) || trainingColumns[0];
            setSelectedTextColumn(textColumn);

            const labelColumn = trainingColumns.find(col =>
              col.toLowerCase().includes('label') ||
              col.toLowerCase().includes('category') ||
              col.toLowerCase().includes('class')
            );
            if (labelColumn) {
              setSelectedLabelColumn(labelColumn);
              setLabelFormat('single');
            } else {
              // Check if we have multiple binary columns (potential multi-class setup)
              const binaryColumns = trainingColumns.filter(col =>
                col !== textColumn &&
                parsedDualData.trainingData.fileInfo.preview?.every(row =>
                  row[col] === 0 || row[col] === 1 || row[col] === '0' || row[col] === '1' ||
                  row[col] === true || row[col] === false || row[col] === 'true' || row[col] === 'false'
                )
              );
              if (binaryColumns.length >= 3) {
                setSelectedLabelColumns(binaryColumns);
                setLabelFormat('multiple');
              }
            }

            // Skip to step 2 since we have data
            setCurrentStep(2);
            return;
          }
        } catch (error) {
          console.error('Error parsing dual data:', error);
        }
      }
    };

    checkForDualData();

    // Only run fallback initialization if no dual data was found
    if (!sessionStorage.getItem('expertWorkflowDualData') && initialData) {
      setUploadedData(initialData);
    }
  }, [initialData]);

  // Load user license
  useEffect(() => {
    const loadLicense = async () => {
      try {
        const license = await getUserLicense();
        setUserLicense(license);
      } catch (error) {
        console.error('Failed to load user license:', error);
        // Set default license on error
        setUserLicense({
          type: 'personal',
          features: [],
          limits: {}
        });
      }
    };

    loadLicense();
  }, []);

  // Handle navigation back to expert workflow
  const handleBackToExpert = () => {
    // Clear any stored dual data
    sessionStorage.removeItem('expertWorkflowDualData');

    // Navigate back to expert workflow
    navigate('/expert', {
      state: {
        returnedFromMultiClass: true,
        multiClassData: {
          currentStep,
          selectedStrategy,
          trainingResults
        }
      }
    });
  };

  const handleComplete = () => {
    onComplete({
      type: 'multi-class',
      taskId: trainingTaskId,
      results: trainingResults,
      classificationResults: classificationResults,
      strategy: selectedStrategy
    });
  };

  // Centralized error handling with recovery options
  const handleError = (error: any, step: string, context?: any) => {
    const errorMessage = error?.message || error?.toString() || 'An unknown error occurred';

    // Log error to history
    setErrorHistory(prev => [...prev, {
      timestamp: new Date(),
      step,
      error: errorMessage,
      context
    }]);

    // Set current error
    setError(errorMessage);

    // Determine if error is recoverable
    const isRecoverable = isRecoverableError(error, step);

    if (isRecoverable && retryCount < 3) {
      setIsRecovering(true);
      toast({
        title: "Temporary issue detected",
        description: `Attempting to recover... (Attempt ${retryCount + 1}/3)`,
        variant: "default"
      });

      // Attempt recovery after a delay
      setTimeout(() => {
        attemptRecovery(step, context);
      }, 2000);
    } else {
      // Show error with guidance
      toast({
        title: `Error in ${step}`,
        description: getErrorGuidance(error, step),
        variant: "destructive"
      });
    }
  };

  // Determine if an error is recoverable
  const isRecoverableError = (error: any, step: string): boolean => {
    const errorMessage = error?.message?.toLowerCase() || '';

    // Network/timeout errors are usually recoverable
    if (errorMessage.includes('network') ||
        errorMessage.includes('timeout') ||
        errorMessage.includes('connection') ||
        errorMessage.includes('fetch')) {
      return true;
    }

    // API rate limiting is recoverable
    if (errorMessage.includes('rate limit') ||
        errorMessage.includes('too many requests')) {
      return true;
    }

    // Temporary server errors are recoverable
    if (errorMessage.includes('server error') ||
        errorMessage.includes('internal error')) {
      return true;
    }

    return false;
  };

  // Provide user guidance based on error type
  const getErrorGuidance = (error: any, step: string): string => {
    const errorMessage = error?.message?.toLowerCase() || '';

    if (errorMessage.includes('validation')) {
      return 'Please check your data configuration and try again.';
    }

    if (errorMessage.includes('insufficient data')) {
      return 'Your dataset may be too small. Try adding more training data.';
    }

    if (errorMessage.includes('memory') || errorMessage.includes('out of memory')) {
      return 'Try reducing the batch size or using a smaller model.';
    }

    if (errorMessage.includes('permission') || errorMessage.includes('unauthorized')) {
      return 'Please check your account permissions and try again.';
    }

    if (errorMessage.includes('quota') || errorMessage.includes('limit')) {
      return 'You have reached your usage limit. Please upgrade your plan or try again later.';
    }

    return 'Please try again or contact support if the issue persists.';
  };

  // Attempt to recover from an error
  const attemptRecovery = async (step: string, context?: any) => {
    try {
      setRetryCount(prev => prev + 1);

      switch (step) {
        case 'training':
          if (context?.trainingRequest) {
            await startTraining();
          }
          break;
        case 'classification':
          if (context?.classificationRequest) {
            await startClassification();
          }
          break;
        case 'llm_classification':
          if (context?.llmRequest) {
            await startLLMClassification();
          }
          break;
        default:
          // Generic recovery - just clear the error
          setError(null);
          break;
      }

      setIsRecovering(false);
      toast({
        title: "Recovery successful",
        description: "The operation has been resumed.",
        variant: "default"
      });
    } catch (recoveryError) {
      setIsRecovering(false);
      handleError(recoveryError, `${step}_recovery`);
    }
  };

  // Clear errors and reset retry count
  const clearErrors = () => {
    setError(null);
    setErrors([]);
    setRetryCount(0);
    setIsRecovering(false);
  };

  // Detect classes from uploaded data (handles both single and dual data)
  useEffect(() => {
    // Use training data if available (dual data setup), otherwise use uploaded data
    const dataToAnalyze = dualData?.trainingData?.fileInfo || uploadedData;
    if (!dataToAnalyze) return;

    if (labelFormat === 'single' && selectedLabelColumn) {
      // Single column format: extract unique values
      const uniqueClasses = [...new Set(
        dataToAnalyze.preview?.map(row => row[selectedLabelColumn]).filter(Boolean)
      )];
      setDetectedClasses(uniqueClasses);

      // Calculate class distribution
      const distribution: Record<string, number> = {};
      dataToAnalyze.preview?.forEach(row => {
        const label = row[selectedLabelColumn];
        if (label) {
          distribution[label] = (distribution[label] || 0) + 1;
        }
      });
      setClassDistribution(distribution);
    } else if (labelFormat === 'multiple' && selectedLabelColumns.length > 0) {
      // Multiple column format: use column names as classes
      setDetectedClasses(selectedLabelColumns);

      // Calculate class distribution from binary columns
      const distribution: Record<string, number> = {};
      selectedLabelColumns.forEach(col => {
        const count = dataToAnalyze.preview?.filter(row =>
          row[col] === 1 || row[col] === '1' || row[col] === true || row[col] === 'true'
        ).length || 0;
        distribution[col] = count;
      });
      setClassDistribution(distribution);
    }
  }, [uploadedData, dualData, selectedLabelColumn, selectedLabelColumns, labelFormat]);

  // Enhanced progress monitoring with detailed status updates
  useEffect(() => {
    let interval: NodeJS.Timeout;

    if (trainingTaskId && isTraining) {
      interval = setInterval(async () => {
        try {
          const status = await getTaskStatus(trainingTaskId);

          if (status.status === 'SUCCESS') {
            setIsTraining(false);
            setTrainingResults(status.result);
            clearErrors(); // Clear any previous errors

            // Determine next step based on workflow type
            if (workflowType === 'training') {
              goToStep('classification');
            } else {
              goToStep('results');
            }

            toast({
              title: "Training completed successfully",
              description: `Model achieved ${((status.result?.metrics?.accuracy || 0) * 100).toFixed(1)}% accuracy`
            });
          } else if (status.status === 'FAILURE') {
            setIsTraining(false);
            handleError(new Error(status.message || 'Training failed'), 'training');
          } else if (status.status === 'RUNNING') {
            // Update progress with detailed information
            setTrainingProgress(status.progress || 0);

            // Update detailed training status
            setTrainingStatus({
              currentEpoch: status.current_epoch,
              totalEpochs: status.total_epochs,
              currentLoss: status.current_loss,
              currentAccuracy: status.current_accuracy,
              estimatedTimeRemaining: status.estimated_time_remaining,
              stage: status.stage || 'training'
            });

            // Log detailed progress for debugging
            if (status.current_epoch && status.total_epochs) {
              const epochProgress = `Epoch ${status.current_epoch}/${status.total_epochs}`;
              const lossInfo = status.current_loss ? ` - Loss: ${status.current_loss.toFixed(4)}` : '';
              const accInfo = status.current_accuracy ? ` - Acc: ${(status.current_accuracy * 100).toFixed(1)}%` : '';

              console.log(`Training progress: ${epochProgress}${lossInfo}${accInfo}`);
            }
          }
        } catch (error) {
          console.error('Error checking training status:', error);
          // Don't immediately fail on monitoring errors, but log them
          if (retryCount < 3) {
            console.log(`Retrying status check... (${retryCount + 1}/3)`);
          } else {
            handleError(error, 'training_monitoring');
          }
        }
      }, 2000);
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [trainingTaskId, isTraining, workflowType, retryCount]);

  // Production classification progress monitoring
  useEffect(() => {
    let interval: NodeJS.Timeout;

    if (isClassifying && classificationTaskId) {
      interval = setInterval(async () => {
        try {
          const status = await getTaskStatus(classificationTaskId);

          if (status.status === 'SUCCESS') {
            setIsClassifying(false);
            setClassificationResults(status.result);
            clearErrors();
            goToStep('results');

            toast({
              title: "Classification completed",
              description: `Processed ${status.result?.total_samples || 0} samples successfully`
            });
          } else if (status.status === 'FAILURE') {
            setIsClassifying(false);
            handleError(new Error(status.message || 'Classification failed'), 'classification');
          } else if (status.status === 'RUNNING') {
            setClassificationProgress(status.progress || 0);

            // Update detailed classification status from API
            setClassificationStatus({
              processedSamples: status.processed_samples,
              totalSamples: status.total_samples,
              currentBatch: status.current_batch,
              totalBatches: status.total_batches,
              averageConfidence: status.average_confidence,
              stage: status.stage || 'processing'
            });
          }
        } catch (error) {
          console.error('Error monitoring classification:', error);
          if (retryCount < 3) {
            console.log(`Retrying classification status check... (${retryCount + 1}/3)`);
          } else {
            handleError(error, 'classification_monitoring');
          }
        }
      }, 2000);
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [isClassifying, classificationTaskId, retryCount]);

  // Recommend strategy based on data characteristics (handles dual data)
  const recommendedStrategy = useMemo((): ClassificationStrategy => {
    const dataToAnalyze = dualData?.trainingData?.fileInfo || uploadedData;
    if (!dataToAnalyze || detectedClasses.length === 0) return 'softmax';

    const numClasses = detectedClasses.length;
    const numSamples = dataToAnalyze.num_rows;

    // Calculate class balance
    const classCounts = detectedClasses.map(cls =>
      dataToAnalyze.preview?.filter(row => row[selectedLabelColumn] === cls).length || 0
    );
    const maxCount = Math.max(...classCounts);
    const minCount = Math.min(...classCounts);
    const imbalanceRatio = maxCount / minCount;

    if (numClasses <= 3 && imbalanceRatio <= 2) {
      return 'softmax'; // Balanced, few classes
    } else if (imbalanceRatio > 5) {
      return 'ovr'; // Imbalanced data
    } else if (numClasses > 10 && numSamples < 1000) {
      return 'ovo'; // Many classes, small dataset
    } else {
      return 'softmax'; // Default
    }
  }, [uploadedData, dualData, detectedClasses, selectedLabelColumn]);

  // Dual data upload handler
  const handleDualDataUpload = (dualDataUpload: DualDataUpload) => {
    setDualData(dualDataUpload);
    setUploadedData(dualDataUpload.trainingData.fileInfo);

    // Auto-select columns from training data
    const trainingColumns = dualDataUpload.trainingData.fileInfo.columns || [];
    const textColumn = trainingColumns.find(col =>
      col.toLowerCase().includes('text') ||
      col.toLowerCase().includes('content') ||
      col.toLowerCase().includes('message')
    ) || trainingColumns[0];
    setSelectedTextColumn(textColumn);

    // Enhanced label column detection for both formats
    const labelColumn = trainingColumns.find(col =>
      col.toLowerCase().includes('label') ||
      col.toLowerCase().includes('category') ||
      col.toLowerCase().includes('class')
    );

    if (labelColumn) {
      // Traditional single label column format
      setSelectedLabelColumn(labelColumn);
      setLabelFormat('single');

      // Detect classes from the single label column
      const uniqueLabels = new Set();
      dualDataUpload.trainingData.fileInfo.preview?.forEach(row => {
        if (row[labelColumn]) {
          uniqueLabels.add(row[labelColumn]);
        }
      });
      setDetectedClasses(Array.from(uniqueLabels) as string[]);
    } else {
      // Check for multiple binary columns (one-hot encoded format)
      const binaryColumns = trainingColumns.filter(col => {
        if (col === textColumn) return false; // Skip text column

        // Check if column contains only binary values (0/1, true/false)
        const columnValues = dualDataUpload.trainingData.fileInfo.preview?.map(row => row[col]) || [];
        const uniqueValues = new Set(columnValues.filter(val => val !== null && val !== undefined));

        return uniqueValues.size <= 2 &&
               Array.from(uniqueValues).every(val =>
                 val === 0 || val === 1 || val === '0' || val === '1' ||
                 val === true || val === false || val === 'true' || val === 'false'
               );
      });

      if (binaryColumns.length >= 3) {
        // Multiple binary columns detected (one-hot encoded)
        setSelectedLabelColumns(binaryColumns);
        setLabelFormat('multiple');
        setDetectedClasses(binaryColumns); // Class names are column names

        toast({
          title: "One-hot encoded format detected",
          description: `Found ${binaryColumns.length} binary class columns`
        });
      } else {
        // Fallback: use first non-text column as single label
        const fallbackColumn = trainingColumns.find(col => col !== textColumn);
        if (fallbackColumn) {
          setSelectedLabelColumn(fallbackColumn);
          setLabelFormat('single');
        }
      }
    }

    toast({
      title: "Dual data loaded",
      description: "Training and classification data configured"
    });
  };

  const handleDataUpload = async (file: File) => {
    try {
      const uploadedFile = await uploadFile(file);
      setUploadedData(uploadedFile);
      
      // Auto-select columns
      if (uploadedFile.columns) {
        const textColumn = uploadedFile.columns.find(col => 
          col.toLowerCase().includes('text') || 
          col.toLowerCase().includes('content') ||
          col.toLowerCase().includes('message')
        ) || uploadedFile.columns[0];
        setSelectedTextColumn(textColumn);
        
        const labelColumn = uploadedFile.columns.find(col =>
          col.toLowerCase().includes('label') ||
          col.toLowerCase().includes('category') ||
          col.toLowerCase().includes('class')
        );
        if (labelColumn) {
          // Traditional single label column format
          setSelectedLabelColumn(labelColumn);
          setLabelFormat('single');

          // Detect classes from the single label column
          const uniqueLabels = new Set();
          uploadedFile.preview?.forEach(row => {
            if (row[labelColumn]) {
              uniqueLabels.add(row[labelColumn]);
            }
          });
          setDetectedClasses(Array.from(uniqueLabels) as string[]);
        } else {
          // Check for multiple binary columns (one-hot encoded format)
          const binaryColumns = uploadedFile.columns.filter(col => {
            if (col === textColumn) return false; // Skip text column

            // Check if column contains only binary values (0/1, true/false)
            const columnValues = uploadedFile.preview?.map(row => row[col]) || [];
            const uniqueValues = new Set(columnValues.filter(val => val !== null && val !== undefined));

            return uniqueValues.size <= 2 &&
                   Array.from(uniqueValues).every(val =>
                     val === 0 || val === 1 || val === '0' || val === '1' ||
                     val === true || val === false || val === 'true' || val === 'false'
                   );
          });

          if (binaryColumns.length >= 3) {
            // Multiple binary columns detected (one-hot encoded)
            setSelectedLabelColumns(binaryColumns);
            setLabelFormat('multiple');
            setDetectedClasses(binaryColumns); // Class names are column names

            toast({
              title: "One-hot encoded format detected",
              description: `Found ${binaryColumns.length} binary class columns`
            });
          } else {
            // Fallback: use first non-text column as single label
            const fallbackColumn = uploadedFile.columns.find(col => col !== textColumn);
            if (fallbackColumn) {
              setSelectedLabelColumn(fallbackColumn);
              setLabelFormat('single');
            }
          }
        }
      }
      
      toast({
        title: "Data uploaded successfully",
        description: `${uploadedFile.filename} with ${uploadedFile.num_rows} rows`
      });
    } catch (error) {
      setError(`Upload failed: ${error}`);
      toast({
        title: "Upload failed",
        description: "Please try again with a valid CSV file",
        variant: "destructive"
      });
    }
  };

  // Training configuration handler
  const handleTrainingConfigSave = (config: MultiClassTrainingConfigType) => {
    setTrainingConfig(config);
    toast({
      title: "Configuration saved",
      description: "Training configuration has been saved"
    });
  };

  // Model management handlers
  const handleModelSelect = (model: MultiClassModel) => {
    setSelectedModel(model);
  };

  const handleModelUse = (modelId: string) => {
    // Set up for using existing model
    setShowModelManager(false);
    toast({
      title: "Model selected",
      description: "Using existing model for classification"
    });
  };

  const handleModelDelete = (modelId: string) => {
    // Model deletion handled in MultiClassModelManager
  };

  // Training handler following hierarchical pattern
  const startTraining = async () => {
    if (!validateStep(4)) {
      return;
    }

    setIsTraining(true);
    setError(null);
    setTrainingProgress(0);

    try {
      const trainingFileId = dualData?.trainingData?.fileInfo?.file_id || uploadedData?.file_id;

      // Handle both label formats for multi-class classification
      let labelColumns: string[];
      let labelConfig: any = {};

      if (labelFormat === 'single') {
        // Traditional single label column format
        labelColumns = [selectedLabelColumn];
        labelConfig = {
          label_format: 'single',
          label_column: selectedLabelColumn,
          classes: detectedClasses
        };
      } else {
        // Multiple binary columns (one-hot encoded) format
        // For binary format, we need to tell the backend about the binary columns
        // but the label_columns should still be the column names, not class names
        labelColumns = selectedLabelColumns;
        labelConfig = {
          label_format: 'multiple',
          binary_columns: selectedLabelColumns, // The actual binary column names
          classes: selectedLabelColumns, // Class names are the column names
          binary_encoding: true
        };
      }

      const trainingRequest: UniversalTrainingRequest = {
        file_id: trainingFileId,
        classification_type: 'multi-class',
        model_type: 'custom',
        text_column: selectedTextColumn,
        label_columns: labelColumns,
        model_name: trainingConfig?.modelName || `multiclass-${Date.now()}`,
        training_params: {
          max_epochs: trainingConfig?.numEpochs || 3,
          batch_size: trainingConfig?.batchSize || 16,
          learning_rate: trainingConfig?.learningRate || 2e-5,
          strategy: selectedStrategy,

          // Label format configuration
          ...labelConfig,

          // Multi-class specific parameters
          classification_strategy: selectedStrategy,
          num_classes: labelFormat === 'single' ? detectedClasses.length : selectedLabelColumns.length,
          class_names: labelFormat === 'single' ? detectedClasses : selectedLabelColumns,

          // Enhanced multi-class parameters
          ...(trainingConfig && {
            base_model: trainingConfig.baseModel,
            max_length: trainingConfig.maxLength,
            validation_split: trainingConfig.validationSplit,
            warmup_steps: trainingConfig.warmupSteps,
            weight_decay: trainingConfig.weightDecay,
            gradient_accumulation_steps: trainingConfig.gradientAccumulationSteps,
            use_unsloth: trainingConfig.useUnsloth,
            fp16: trainingConfig.fp16,
            gradient_checkpointing: trainingConfig.gradientCheckpointing,
            enable_early_stopping: trainingConfig.enableEarlyStopping,
            patience: trainingConfig.patience,
            min_delta: trainingConfig.minDelta,

            // Multi-class specific configuration
            multiclass_config: {
              strategy: trainingConfig.strategy,
              class_weight_strategy: trainingConfig.classWeightStrategy,
              loss_function: trainingConfig.multiClassLoss,
              ovr_config: trainingConfig.ovrConfig,
              ovo_config: trainingConfig.ovoConfig,
              softmax_config: trainingConfig.softmaxConfig,
              class_balance_handling: trainingConfig.classBalanceHandling,
              custom_class_weights: trainingConfig.customClassWeights,
              confidence_threshold: trainingConfig.confidenceThreshold
            }
          })
        },
        // Add dual data information if available
        ...(dualData && {
          dual_data_setup: true,
          classification_file_id: dualData.classificationData.fileInfo.file_id,
          training_file_id: dualData.trainingData.fileInfo.file_id
        })
      };

      const response = await startUniversalTraining(trainingRequest);
      setTrainingTaskId(response.task_id);

      toast({
        title: "Training started",
        description: `Training multi-class model with ${selectedStrategy} strategy`
      });
    } catch (error) {
      setIsTraining(false);
      handleError(error, 'training', { trainingRequest: true });
    }
  };

  // Classification handler following hierarchical pattern
  const startClassification = async () => {
    if (!trainingResults || !dualData) {
      toast({
        title: "Missing requirements",
        description: "Please complete training and ensure classification data is uploaded",
        variant: "destructive"
      });
      return;
    }

    setIsClassifying(true);
    setError(null);
    setClassificationProgress(0);
    setClassificationTaskId(null);

    try {
      const inferenceRequest: UniversalInferenceRequest = {
        model_id: trainingResults.model_id,
        file_id: dualData.classificationData.fileInfo.file_id,
        text_column: selectedTextColumn,
        classification_type: 'multi-class'
      };

      const response = await startUniversalInference(inferenceRequest);

      // If response includes a task_id, monitor progress
      if (response.task_id) {
        setClassificationTaskId(response.task_id);
        toast({
          title: "Classification started",
          description: "Processing your data, please wait..."
        });
      } else {
        // Direct response (for small datasets)
        setClassificationResults(response);
        setClassificationProgress(100);
        setIsClassifying(false);
        goToStep('results');

        toast({
          title: "Classification completed",
          description: `Classified ${response.summary?.total_predictions || 0} texts successfully`
        });
      }
    } catch (error) {
      setIsClassifying(false);
      handleError(error, 'classification', { classificationRequest: true });
    }
  };

  // LLM Classification handler following hierarchical pattern
  const startLLMClassification = async () => {
    if (!validateStep(4)) {
      return;
    }

    // Determine which file to use for classification
    const classificationFileId = dualData
      ? dualData.classificationData.fileInfo.file_id
      : uploadedData?.file_id;

    if (!classificationFileId) {
      toast({
        title: "No data available",
        description: "Please upload data for classification",
        variant: "destructive"
      });
      return;
    }

    setIsClassifying(true);
    setClassificationProgress(0);
    setClassificationTaskId(null);
    setError(null);

    try {
      const labelColumns = labelFormat === 'single' ? [selectedLabelColumn] : selectedLabelColumns;

      const trainingRequest: UniversalTrainingRequest = {
        file_id: classificationFileId,
        classification_type: 'multi-class',
        model_type: 'llm',
        text_column: selectedTextColumn,
        label_columns: labelColumns,
        llm_provider: llmProvider,
        llm_model: llmModel,
        custom_prompt: customPrompt || undefined,
        training_params: {
          strategy: selectedStrategy,
          classes: detectedClasses,
          label_format: labelFormat
        },
        // Add dual data information if available
        ...(dualData && {
          dual_data_setup: true,
          classification_file_id: dualData.classificationData.fileInfo.file_id,
          training_file_id: dualData.trainingData.fileInfo.file_id
        })
      };

      const response = await startUniversalTraining(trainingRequest);

      // If response includes a task_id, monitor progress
      if (response.task_id) {
        setClassificationTaskId(response.task_id);
        toast({
          title: "LLM classification started",
          description: "Processing your data with LLM, please wait..."
        });
      } else {
        // Direct response (for small datasets)
        setClassificationResults(response);
        setClassificationProgress(100);
        setIsClassifying(false);
        goToStep('results');

        toast({
          title: "LLM classification completed",
          description: `Classified ${response.summary?.total_predictions || 0} texts successfully`
        });
      }
    } catch (error) {
      setIsClassifying(false);
      handleError(error, 'llm_classification', { llmRequest: true });
    }
  };

  return (
    <div className="max-w-7xl mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center">
            <Layers className="w-6 h-6 text-primary" />
          </div>
          <div>
            <h1 className="text-3xl font-bold">Multi-Class Classification Workflow</h1>
            <p className="text-muted-foreground">Multiple mutually exclusive classes with strategy optimization</p>
          </div>
        </div>
        <Badge variant="outline" className="px-3 py-1">
          Step {currentStep} of {steps.length}
        </Badge>
      </div>

      {/* Progress Bar */}
      <div className="space-y-2">
        <div className="flex justify-between text-sm text-muted-foreground">
          <span>Progress</span>
          <span>{Math.round(((currentStep - 1) / steps.length) * 100)}% Complete</span>
        </div>
        <Progress value={((currentStep - 1) / steps.length) * 100} className="h-2" />
      </div>

      {/* Error Alert */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Enhanced Error Display with Recovery Options */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            <div className="space-y-3">
              <div>
                <strong>Error:</strong> {error}
              </div>

              {isRecovering && (
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                  <span className="text-sm">Attempting to recover...</span>
                </div>
              )}

              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={clearErrors}
                >
                  Dismiss
                </Button>

                {retryCount < 3 && !isRecovering && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      const lastError = errorHistory[errorHistory.length - 1];
                      if (lastError) {
                        attemptRecovery(lastError.step, lastError.context);
                      }
                    }}
                  >
                    Retry ({3 - retryCount} attempts left)
                  </Button>
                )}

                {errorHistory.length > 1 && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      // Show error history modal or expand details
                      console.log('Error history:', errorHistory);
                    }}
                  >
                    View Details
                  </Button>
                )}
              </div>
            </div>
          </AlertDescription>
        </Alert>
      )}

      {/* Validation Errors */}
      {errors.length > 0 && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            <ul className="list-disc list-inside space-y-1">
              {errors.map((error, index) => (
                <li key={index}>{error}</li>
              ))}
            </ul>
          </AlertDescription>
        </Alert>
      )}

      {/* Back to Expert Workflow Button */}
      {fromExpertWorkflow && (
        <div className="flex justify-start">
          <Button variant="outline" onClick={handleBackToExpert}>
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Expert Workflow
          </Button>
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
        {/* Sidebar - Steps */}
        <div className="lg:col-span-1">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Workflow Steps</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {steps.map((step) => {
                  const Icon = step.icon;
                  const isActive = currentStep === step.id;
                  const isComplete = step.status === 'complete';

                  return (
                    <div
                      key={step.id}
                      className={`flex items-center gap-3 p-3 rounded-lg transition-colors cursor-pointer ${
                        isActive ? 'bg-primary/10 border border-primary/20' :
                        isComplete ? 'bg-green-50 border border-green-200' : 'bg-muted/30'
                      }`}
                      onClick={() => {
                        if (isComplete || step.id <= currentStep) {
                          setCurrentStep(step.id);
                        }
                      }}
                    >
                      <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                        isActive ? 'bg-primary text-white' :
                        isComplete ? 'bg-green-500 text-white' : 'bg-muted'
                      }`}>
                        {isComplete ? (
                          <CheckCircle2 className="w-4 h-4" />
                        ) : (
                          <Icon className="w-4 h-4" />
                        )}
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className={`font-medium text-sm ${
                          isActive ? 'text-primary' :
                          isComplete ? 'text-green-600' : 'text-muted-foreground'
                        }`}>
                          {step.title}
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Content */}
        <div className="lg:col-span-3">
          {/* Step 1: Data Upload */}
          {isCurrentStep('upload') && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Upload className="w-5 h-5" />
                  Data Upload
                </CardTitle>
                <CardDescription>
                  Upload your training data and optionally separate classification data for multi-class classification.
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <UnifiedFileUploadZone
                  onFileSelected={(fileId, fileInfo, purposes) => {
                    if (purposes.includes('training') && purposes.includes('classification')) {
                      // Single file for both purposes
                      setUploadedData(fileInfo);
                    } else {
                      // Check if we have dual data setup
                      const dualDataUpload = unifiedDataManager.getDualDataUpload();
                      if (dualDataUpload) {
                        handleDualDataUpload(dualDataUpload);
                      } else {
                        setUploadedData(fileInfo);
                      }
                    }
                  }}
                  requiredPurposes={['training']}
                  suggestedPurposes={['classification']}
                  allowMultiplePurposes={true}
                  showFileReuse={true}
                  title="Upload Multi-Class Data"
                  description="Upload your training data and optionally separate classification data"
                />

                {(uploadedData || dualData) && (
                  <div className="space-y-4">
                    {dualData ? (
                      <div className="space-y-3">
                        <div className="flex items-center justify-between p-4 bg-blue-50 dark:bg-blue-950 rounded-lg">
                          <div className="flex items-center gap-3">
                            <CheckCircle2 className="w-5 h-5 text-blue-500" />
                            <div>
                              <div className="font-medium">Training Data: {dualData.trainingData.fileInfo.filename}</div>
                              <div className="text-sm text-muted-foreground">
                                {dualData.trainingData.fileInfo.num_rows} rows, {dualData.trainingData.fileInfo.columns?.length} columns
                              </div>
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center justify-between p-4 bg-green-50 dark:bg-green-950 rounded-lg">
                          <div className="flex items-center gap-3">
                            <CheckCircle2 className="w-5 h-5 text-green-500" />
                            <div>
                              <div className="font-medium">Classification Data: {dualData.classificationData.fileInfo.filename}</div>
                              <div className="text-sm text-muted-foreground">
                                {dualData.classificationData.fileInfo.num_rows} rows, {dualData.classificationData.fileInfo.columns?.length} columns
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    ) : (
                      <div className="flex items-center justify-between p-4 bg-green-50 dark:bg-green-950 rounded-lg">
                        <div className="flex items-center gap-3">
                          <CheckCircle2 className="w-5 h-5 text-green-500" />
                          <div>
                            <div className="font-medium">{uploadedData?.filename}</div>
                            <div className="text-sm text-muted-foreground">
                              {uploadedData?.num_rows} rows, {uploadedData?.columns?.length} columns
                            </div>
                          </div>
                        </div>
                        <Button variant="outline" size="sm" onClick={() => setUploadedData(null)}>
                          Remove
                        </Button>
                      </div>
                    )}

                    {(uploadedData?.preview || dualData?.trainingData.fileInfo.preview) && (
                      <div className="border rounded-lg overflow-hidden">
                        <div className="bg-muted p-3 border-b">
                          <h4 className="font-medium">Training Data Preview</h4>
                        </div>
                        <div className="overflow-x-auto">
                          <table className="w-full text-sm">
                            <thead className="bg-muted/50">
                              <tr>
                                {(uploadedData?.columns || dualData?.trainingData.fileInfo.columns)?.map((col) => (
                                  <th key={col} className="text-left p-2 font-medium">
                                    {col}
                                  </th>
                                ))}
                              </tr>
                            </thead>
                            <tbody>
                              {(uploadedData?.preview || dualData?.trainingData.fileInfo.preview)?.slice(0, 5).map((row, idx) => (
                                <tr key={idx} className="border-t">
                                  {(uploadedData?.columns || dualData?.trainingData.fileInfo.columns)?.map((col) => (
                                    <td key={col} className="p-2 max-w-xs truncate">
                                      {row[col]}
                                    </td>
                                  ))}
                                </tr>
                              ))}
                            </tbody>
                          </table>
                        </div>
                      </div>
                    )}
                  </div>
                )}

                <div className="flex justify-end">
                  <Button
                    onClick={handleNext}
                    disabled={!uploadedData && !dualData}
                    className="flex items-center gap-2"
                  >
                    Next Step
                    <ArrowRight className="w-4 h-4" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Step 2: Configuration */}
          {isCurrentStep('config') && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Layers className="w-5 h-5" />
                  Multi-Class Setup
                </CardTitle>
                <CardDescription>
                  Configure classes and analyze class distribution for multi-class classification.
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Data Source Information */}
                {dualData && (
                  <div className="p-4 bg-blue-50 rounded-lg">
                    <div className="font-medium text-blue-900">Dual Data Configuration</div>
                    <div className="text-sm text-blue-700 mt-1">
                      Configuring columns from training data: {dualData.trainingData.fileInfo.filename}
                    </div>
                    <div className="text-sm text-blue-700">
                      Classification will be performed on: {dualData.classificationData.fileInfo.filename}
                    </div>
                  </div>
                )}

                {/* Text Column Selection */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">Text Column</label>
                  <select
                    value={selectedTextColumn}
                    onChange={(e) => setSelectedTextColumn(e.target.value)}
                    className="w-full p-2 border rounded-md"
                  >
                    <option value="">Select text column...</option>
                    {(dualData?.trainingData?.fileInfo?.columns || uploadedData?.columns)?.map((col) => (
                      <option key={col} value={col}>{col}</option>
                    ))}
                  </select>
                  <div className="text-xs text-muted-foreground">
                    {dualData ?
                      "Select the column containing text data from your training file" :
                      "Select the column containing text data"
                    }
                  </div>
                </div>

                {/* Label Format Selection */}
                <div className="space-y-4">
                  {/* Format Information */}
                  <Alert>
                    <Info className="h-4 w-4" />
                    <AlertDescription>
                      <div className="space-y-2">
                        <div className="font-medium">Multi-Class Label Formats Supported:</div>
                        <div className="text-sm space-y-1">
                          <div><strong>Traditional:</strong> One column with class names (e.g., "Sports", "Tech", "Politics")</div>
                          <div><strong>One-Hot Encoded:</strong> Multiple binary columns, one per class (e.g., Sports=1, Tech=0, Politics=0)</div>
                        </div>
                      </div>
                    </AlertDescription>
                  </Alert>

                  <div className="space-y-2">
                    <label className="text-sm font-medium">Label Format</label>
                    <div className="flex gap-4">
                      <label className="flex items-center gap-2">
                        <input
                          type="radio"
                          value="single"
                          checked={labelFormat === 'single'}
                          onChange={(e) => {
                            setLabelFormat('single');
                            setSelectedLabelColumns([]);
                          }}
                          className="w-4 h-4"
                        />
                        <span className="text-sm">Single Label Column (Traditional)</span>
                      </label>
                      <label className="flex items-center gap-2">
                        <input
                          type="radio"
                          value="multiple"
                          checked={labelFormat === 'multiple'}
                          onChange={(e) => {
                            setLabelFormat('multiple');
                            setSelectedLabelColumn('');
                          }}
                          className="w-4 h-4"
                        />
                        <span className="text-sm">Multiple Binary Columns (One-Hot Encoded)</span>
                      </label>
                    </div>
                  </div>

                  {labelFormat === 'single' ? (
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Label Column</label>
                      <select
                        value={selectedLabelColumn}
                        onChange={(e) => setSelectedLabelColumn(e.target.value)}
                        className="w-full p-2 border rounded-md"
                      >
                        <option value="">Select label column...</option>
                        {(dualData?.trainingData?.fileInfo?.columns || uploadedData?.columns)
                          ?.filter(col => col !== selectedTextColumn)
                          .map((col) => (
                            <option key={col} value={col}>{col}</option>
                          ))}
                      </select>
                      <p className="text-xs text-muted-foreground">
                        <strong>Traditional Format:</strong> Select a column containing class names (e.g., "Sports", "Technology", "Politics")
                        {dualData && " from your training data"}
                      </p>
                    </div>
                  ) : (
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Class Columns</label>
                      <div className="border rounded-md p-3 max-h-40 overflow-y-auto">
                        {(dualData?.trainingData?.fileInfo?.columns || uploadedData?.columns)
                          ?.filter(col => col !== selectedTextColumn)
                          .map((col) => (
                            <label key={col} className="flex items-center gap-2 py-1">
                              <input
                                type="checkbox"
                                checked={selectedLabelColumns.includes(col)}
                                onChange={(e) => {
                                  if (e.target.checked) {
                                    setSelectedLabelColumns([...selectedLabelColumns, col]);
                                  } else {
                                    setSelectedLabelColumns(selectedLabelColumns.filter(c => c !== col));
                                  }
                                }}
                                className="w-4 h-4"
                              />
                              <span className="text-sm">{col}</span>
                            </label>
                          ))}
                      </div>
                      <p className="text-xs text-muted-foreground">
                        <strong>One-Hot Encoded Format:</strong> Select columns where each represents a class with binary values (1/0, true/false).
                        Each row should have exactly one column set to 1 (indicating the class) and others set to 0.
                        {dualData && " from your training data"}
                      </p>
                    </div>
                  )}
                </div>

                {/* Detected Classes Display */}
                {detectedClasses.length > 0 && (
                  <div className="space-y-3">
                    <h4 className="font-medium">Detected Classes ({detectedClasses.length})</h4>
                    <div className="flex flex-wrap gap-2">
                      {detectedClasses.map((cls, index) => (
                        <Badge key={index} variant="secondary" className="px-3 py-1">
                          {cls}
                        </Badge>
                      ))}
                    </div>

                    {detectedClasses.length < 3 && (
                      <Alert variant="destructive">
                        <AlertCircle className="h-4 w-4" />
                        <AlertDescription>
                          Multi-class classification requires at least 3 classes. Found {detectedClasses.length} classes.
                          Please check your data or use binary classification instead.
                        </AlertDescription>
                      </Alert>
                    )}

                    {detectedClasses.length > 20 && (
                      <Alert>
                        <AlertCircle className="h-4 w-4" />
                        <AlertDescription>
                          You have {detectedClasses.length} classes. Consider using hierarchical classification
                          or grouping similar classes for better performance.
                        </AlertDescription>
                      </Alert>
                    )}
                  </div>
                )}

                {/* Class Balance Analysis */}
                {(uploadedData || dualData) && detectedClasses.length >= 3 && (
                  <ClassBalanceAnalyzer
                    data={dualData?.trainingData?.fileInfo || uploadedData}
                    labelColumn={labelFormat === 'single' ? selectedLabelColumn : selectedLabelColumns[0]}
                    labelFormat={labelFormat}
                    selectedLabelColumns={labelFormat === 'multiple' ? selectedLabelColumns : undefined}
                  />
                )}

                <div className="flex justify-between">
                  <Button variant="outline" onClick={handlePrevious}>
                    <ArrowLeft className="w-4 h-4 mr-2" />
                    Previous
                  </Button>
                  <Button
                    onClick={handleNext}
                    disabled={
                      !selectedTextColumn ||
                      (labelFormat === 'single' && !selectedLabelColumn) ||
                      (labelFormat === 'multiple' && selectedLabelColumns.length < 3) ||
                      detectedClasses.length < 3
                    }
                  >
                    Next Step
                    <ArrowRight className="w-4 h-4 ml-2" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Step 3: Strategy Selection */}
          {isCurrentStep('strategy') && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Grid3X3 className="w-5 h-5" />
                  Classification Strategy
                </CardTitle>
                <CardDescription>
                  Choose the best multi-class classification strategy for your data.
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Strategy Recommendation */}
                <Alert>
                  <Target className="h-4 w-4" />
                  <AlertDescription>
                    <strong>Recommended Strategy:</strong> {STRATEGY_INFO[recommendedStrategy].name} -
                    Based on your data characteristics ({detectedClasses.length} classes, {uploadedData?.num_rows} samples).
                  </AlertDescription>
                </Alert>

                {/* Strategy Options */}
                <div className="space-y-4">
                  <h4 className="font-medium">Available Strategies</h4>
                  <div className="grid grid-cols-1 gap-4">
                    {Object.entries(STRATEGY_INFO).map(([key, info]) => {
                      const strategy = key as ClassificationStrategy;
                      const isSelected = selectedStrategy === strategy;
                      const isRecommended = strategy === recommendedStrategy;

                      return (
                        <div
                          key={strategy}
                          className={`p-4 border rounded-lg cursor-pointer transition-all ${
                            isSelected ? 'border-primary bg-primary/5' : 'hover:bg-muted/50'
                          }`}
                          onClick={() => setSelectedStrategy(strategy)}
                        >
                          <div className="flex items-start justify-between mb-3">
                            <div className="flex items-center gap-3">
                              <div
                                className="w-4 h-4 rounded-full"
                                style={{ backgroundColor: info.color }}
                              />
                              <div>
                                <h5 className="font-medium flex items-center gap-2">
                                  {info.name}
                                  {isRecommended && (
                                    <Badge variant="default" className="text-xs">Recommended</Badge>
                                  )}
                                </h5>
                                <Badge variant="outline" className="text-xs mt-1">
                                  {info.complexity} Complexity
                                </Badge>
                              </div>
                            </div>
                            {isSelected && <CheckCircle2 className="w-5 h-5 text-primary" />}
                          </div>

                          <p className="text-sm text-muted-foreground mb-3">
                            {info.description}
                          </p>

                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                            <div>
                              <h6 className="font-medium text-green-600 mb-1">Pros</h6>
                              <ul className="text-muted-foreground space-y-1">
                                {info.pros.map((pro, idx) => (
                                  <li key={idx}>• {pro}</li>
                                ))}
                              </ul>
                            </div>
                            <div>
                              <h6 className="font-medium text-red-600 mb-1">Cons</h6>
                              <ul className="text-muted-foreground space-y-1">
                                {info.cons.map((con, idx) => (
                                  <li key={idx}>• {con}</li>
                                ))}
                              </ul>
                            </div>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>

                <div className="flex justify-between">
                  <Button variant="outline" onClick={handlePrevious}>
                    <ArrowLeft className="w-4 h-4 mr-2" />
                    Previous
                  </Button>
                  <Button onClick={handleNext}>
                    Configure Training
                    <ArrowRight className="w-4 h-4 ml-2" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Step 4: Method Selection */}
          {isCurrentStep('method') && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Brain className="w-5 h-5" />
                  Method Selection
                </CardTitle>
                <CardDescription>
                  Choose how you want to perform multi-class classification
                </CardDescription>
              </CardHeader>
              <CardContent>
                {/* Dual Data Summary */}
                {dualData && (
                  <div className="mb-6 p-4 bg-blue-50 rounded-lg">
                    <div className="font-medium text-blue-900 mb-2">Dual Data Configuration</div>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <div className="font-medium text-blue-800">Training Data</div>
                        <div className="text-blue-700">{dualData.trainingData.fileInfo.filename}</div>
                        <div className="text-blue-600">{dualData.trainingData.fileInfo.num_rows} rows</div>
                      </div>
                      <div>
                        <div className="font-medium text-blue-800">Classification Data</div>
                        <div className="text-blue-700">{dualData.classificationData.fileInfo.filename}</div>
                        <div className="text-blue-600">{dualData.classificationData.fileInfo.num_rows} rows</div>
                      </div>
                    </div>
                    <div className="mt-2 text-sm text-blue-700">
                      Strategy: {selectedStrategy} • Classes: {detectedClasses.length} • Text Column: {selectedTextColumn}
                    </div>
                  </div>
                )}

                <Tabs value={trainingMethod} onValueChange={(value) => {
                  setTrainingMethod(value as 'llm' | 'custom' | 'model_classification');
                  if (value === 'llm') {
                    setWorkflowType('inference');
                  } else if (value === 'custom') {
                    setWorkflowType('training');
                  } else if (value === 'model_classification') {
                    setWorkflowType('model_classification');
                  }
                }}>
                  <TabsList className="grid w-full grid-cols-3">
                    <TabsTrigger value="custom">Custom Training</TabsTrigger>
                    <TabsTrigger value="model_classification">Model Classification</TabsTrigger>
                    <TabsTrigger value="llm">LLM Classification</TabsTrigger>
                  </TabsList>

                  <TabsContent value="custom" className="space-y-4">
                    <Alert>
                      <Target className="h-4 w-4" />
                      <AlertDescription>
                        Train a custom multi-class classification model using your data.
                        This provides the best accuracy for your specific use case.
                      </AlertDescription>
                    </Alert>

                    {/* Model Management Section */}
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <h4 className="text-lg font-semibold">Model Options</h4>
                        <div className="flex gap-2">
                          <Button
                            variant="outline"
                            onClick={() => setShowModelManager(!showModelManager)}
                          >
                            <Brain className="w-4 h-4 mr-2" />
                            {showModelManager ? 'Hide' : 'Show'} Existing Models
                          </Button>
                          <Button
                            variant="outline"
                            onClick={() => setShowTrainingConfig(!showTrainingConfig)}
                          >
                            <Settings className="w-4 h-4 mr-2" />
                            {showTrainingConfig ? 'Hide' : 'Show'} Training Config
                          </Button>
                        </div>
                      </div>

                      {/* Model Manager */}
                      {showModelManager && (
                        <MultiClassModelManager
                          onModelSelect={handleModelSelect}
                          onModelUse={handleModelUse}
                          onModelDelete={handleModelDelete}
                          selectedModelId={selectedModel?.id}
                          showActions={true}
                        />
                      )}

                      {/* Training Configuration */}
                      {showTrainingConfig && (
                        <MultiClassTrainingConfig
                          onConfigChange={setTrainingConfig}
                          onSave={handleTrainingConfigSave}
                          initialConfig={trainingConfig || undefined}
                          userJourney="expert"
                          detectedClasses={detectedClasses}
                          classDistribution={classDistribution}
                          estimatedTrainingTime="15-30 minutes"
                        />
                      )}
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="p-4 border rounded">
                        <h4 className="font-semibold mb-2">Model Architecture</h4>
                        <p className="text-sm text-muted-foreground">
                          {trainingConfig?.baseModel || 'Multi-class BERT-based'} classifier with {selectedStrategy} strategy
                        </p>
                      </div>
                      <div className="p-4 border rounded">
                        <h4 className="font-semibold mb-2">Training Time</h4>
                        <p className="text-sm text-muted-foreground">
                          {trainingConfig ?
                            `Estimated ${Math.round(trainingConfig.numEpochs * 10)} minutes` :
                            'Estimated 15-30 minutes depending on data size'
                          }
                        </p>
                      </div>
                      <div className="p-4 border rounded">
                        <h4 className="font-semibold mb-2">Accuracy</h4>
                        <p className="text-sm text-muted-foreground">
                          Typically 85-95% F1 score for {detectedClasses.length} classes
                        </p>
                      </div>
                    </div>
                  </TabsContent>

                  <TabsContent value="model_classification" className="space-y-4">
                    <Alert>
                      <Target className="h-4 w-4" />
                      <AlertDescription>
                        Use a pre-trained multi-class model to classify your data.
                        Select your model, configure settings, and run classification directly from this step.
                        You'll be taken to the results step once classification is complete.
                      </AlertDescription>
                    </Alert>

                    {/* Model Classification Component would go here */}
                    <div className="p-8 border-2 border-dashed border-muted-foreground/25 rounded-lg text-center">
                      <Brain className="w-12 h-12 mx-auto mb-4 text-muted-foreground" />
                      <h3 className="text-lg font-semibold mb-2">Model Classification</h3>
                      <p className="text-muted-foreground mb-4">
                        Select from your trained multi-class models and run classification on your data.
                      </p>
                      <Button variant="outline" disabled>
                        <Settings className="w-4 h-4 mr-2" />
                        Configure Model Classification
                      </Button>
                      <p className="text-xs text-muted-foreground mt-2">
                        This feature will be available once you have trained models.
                      </p>
                    </div>
                  </TabsContent>

                  <TabsContent value="llm" className="space-y-4">
                    <Alert>
                      <Zap className="h-4 w-4" />
                      <AlertDescription>
                        Use a large language model for multi-class classification.
                        Faster setup but may require prompt engineering.
                      </AlertDescription>
                    </Alert>

                    <LLMConfigurationPanel
                      initialConfig={{
                        provider: llmProvider,
                        model: llmModel,
                        endpoint: '',
                        customPrompt: customPrompt,
                        temperature: 0.1,
                        maxTokens: 100
                      }}
                      onConfigurationChange={(config) => {
                        // Update state when configuration changes
                        setLlmProvider(config.provider);
                        setLlmModel(config.model);
                        setCustomPrompt(config.customPrompt || '');
                      }}
                      onApply={(config) => {
                        setLlmProvider(config.provider);
                        setLlmModel(config.model);
                        setCustomPrompt(config.customPrompt || '');
                      }}
                      onCancel={() => {}}
                      classificationType="multi-class"
                    />
                  </TabsContent>
                </Tabs>

                <div className="flex justify-between">
                  <Button variant="outline" onClick={handlePrevious}>
                    <ArrowLeft className="w-4 h-4 mr-2" />
                    Previous
                  </Button>
                  <Button
                    onClick={handleNext}
                    disabled={trainingMethod === 'custom' && !trainingConfig}
                  >
                    {workflowType === 'training' ? 'Continue to Training' :
                     workflowType === 'inference' ? 'Continue to Classification' :
                     'Continue to Model Selection'}
                    <ArrowRight className="w-4 h-4 ml-2" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Training Step */}
          {isCurrentStep('training') && (
            <div className="space-y-6">
              {/* Training Step for Custom Training */}
              {workflowType === 'training' && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Zap className="w-5 h-5" />
                      {isTraining ? 'Training in Progress' : 'Start Training'}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    {!isTraining && !trainingResults && (
                      <div className="space-y-4">
                        <Alert>
                          <Shield className="h-4 w-4" />
                          <AlertDescription>
                            Ready to start multi-class classification training with {detectedClasses.length} classes using {selectedStrategy} strategy.
                          </AlertDescription>
                        </Alert>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div className="p-4 border rounded">
                            <h4 className="font-semibold mb-2">Configuration Summary</h4>
                            <ul className="text-sm space-y-1">
                              <li>• Method: Custom Training</li>
                              <li>• Text Column: {selectedTextColumn || 'None selected'}</li>
                              <li>• Classes: {detectedClasses.length}</li>
                              <li>• Strategy: {selectedStrategy}</li>
                              <li>• Label Format: {labelFormat}</li>
                            </ul>
                          </div>
                          <div className="p-4 border rounded">
                            <h4 className="font-semibold mb-2">Training Details</h4>
                            <ul className="text-sm space-y-1">
                              <li>• Model: {trainingConfig?.baseModel || 'Default BERT'}</li>
                              <li>• Epochs: {trainingConfig?.numEpochs || 3}</li>
                              <li>• Batch Size: {trainingConfig?.batchSize || 16}</li>
                              <li>• Learning Rate: {trainingConfig?.learningRate || 2e-5}</li>
                            </ul>
                          </div>
                        </div>

                        <div className="text-center">
                          <Button onClick={startTraining} size="lg">
                            <Play className="w-4 h-4 mr-2" />
                            Start Training
                          </Button>
                        </div>
                      </div>
                    )}

                    {isTraining && (
                      <TrainingProgressMonitor
                        progress={trainingProgress}
                        taskId={trainingTaskId}
                        currentEpoch={trainingStatus.currentEpoch}
                        totalEpochs={trainingStatus.totalEpochs}
                        currentLoss={trainingStatus.currentLoss}
                        currentAccuracy={trainingStatus.currentAccuracy}
                        estimatedTimeRemaining={trainingStatus.estimatedTimeRemaining}
                        stage={trainingStatus.stage}
                        onComplete={(results) => {
                          setTrainingResults(results);
                          setIsTraining(false);
                          handleStepComplete(5);
                        }}
                        onError={(error) => {
                          handleError(new Error(error), 'training');
                        }}
                      />
                    )}

                    {trainingResults && (
                      <div className="space-y-4">
                        <Alert>
                          <CheckCircle2 className="h-4 w-4" />
                          <AlertDescription>
                            Training completed successfully! Your multi-class model achieved {(trainingResults.metrics.accuracy * 100).toFixed(1)}% accuracy.
                          </AlertDescription>
                        </Alert>

                        {dualData && (
                          <div className="space-y-4">
                            <h4 className="font-medium">Run Classification</h4>
                            <p className="text-sm text-muted-foreground">
                              Classify your uploaded data using the trained model.
                            </p>
                            <Button
                              onClick={startClassification}
                              disabled={isClassifying}
                              className="flex items-center gap-2"
                            >
                              {isClassifying ? (
                                <>
                                  <div className="w-4 h-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                                  Classifying...
                                </>
                              ) : (
                                <>
                                  <Target className="w-4 h-4" />
                                  Start Classification
                                </>
                              )}
                            </Button>
                          </div>
                        )}
                      </div>
                    )}
                  </CardContent>
                </Card>
              )}

              {/* Classification Step for LLM Inference */}
              {workflowType === 'inference' && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Zap className="w-5 h-5" />
                      {isClassifying ? 'Classification in Progress' : 'Start LLM Classification'}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    {!isClassifying && !classificationResults && (
                      <div className="space-y-4">
                        <Alert>
                          <Zap className="h-4 w-4" />
                          <AlertDescription>
                            Ready to classify your data using {llmProvider} {llmModel} with {selectedStrategy} strategy.
                          </AlertDescription>
                        </Alert>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div className="p-4 border rounded">
                            <h4 className="font-semibold mb-2">Classification Setup</h4>
                            <ul className="text-sm space-y-1">
                              <li>• Method: LLM Classification</li>
                              <li>• Text Column: {selectedTextColumn}</li>
                              <li>• Classes: {detectedClasses.length}</li>
                              <li>• Strategy: {selectedStrategy}</li>
                              <li>• Label Format: {labelFormat}</li>
                            </ul>
                          </div>
                          <div className="p-4 border rounded">
                            <h4 className="font-semibold mb-2">LLM Configuration</h4>
                            <ul className="text-sm space-y-1">
                              <li>• Provider: {llmProvider}</li>
                              <li>• Model: {llmModel}</li>
                              <li>• Custom Prompt: {customPrompt ? 'Yes' : 'No'}</li>
                              <li>• Data: {dualData ? 'Dual setup' : 'Single file'}</li>
                            </ul>
                          </div>
                        </div>

                        <div className="text-center">
                          <Button onClick={startLLMClassification} size="lg">
                            <Play className="w-4 h-4 mr-2" />
                            Start LLM Classification
                          </Button>
                        </div>
                      </div>
                    )}

                    {isClassifying && (
                      <div className="text-center space-y-4">
                        <div className="w-16 h-16 rounded-full bg-primary/10 flex items-center justify-center mx-auto">
                          <div className="w-8 h-8 animate-spin rounded-full border-2 border-primary border-t-transparent" />
                        </div>
                        <div>
                          <h3 className="text-lg font-semibold">Classifying with LLM</h3>
                          <p className="text-muted-foreground">
                            Processing your data with {llmProvider} {llmModel}...
                          </p>
                        </div>
                        <Progress value={classificationProgress} className="max-w-md mx-auto" />
                      </div>
                    )}

                    {classificationResults && (
                      <div className="space-y-4">
                        <Alert>
                          <CheckCircle2 className="h-4 w-4" />
                          <AlertDescription>
                            LLM classification completed! Processed {classificationResults.summary?.total_predictions || 0} texts.
                          </AlertDescription>
                        </Alert>
                      </div>
                    )}
                  </CardContent>
                </Card>
              )}

              {/* Model Classification Step */}
              {workflowType === 'model_classification' && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Brain className="w-5 h-5" />
                      Model Classification
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="p-8 border-2 border-dashed border-muted-foreground/25 rounded-lg text-center">
                      <Brain className="w-12 h-12 mx-auto mb-4 text-muted-foreground" />
                      <h3 className="text-lg font-semibold mb-2">Model Classification</h3>
                      <p className="text-muted-foreground mb-4">
                        This feature will allow you to use pre-trained multi-class models for classification.
                      </p>
                      <Button variant="outline" disabled>
                        <Settings className="w-4 h-4 mr-2" />
                        Configure Model Classification
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          )}

          {/* Navigation for Step 5 */}
          {currentStep === 5 && (
            <div className="flex justify-between">
              <Button variant="outline" onClick={handlePrevious}>
                <ArrowLeft className="w-4 h-4 mr-2" />
                Previous
              </Button>
              <Button
                onClick={handleNext}
                disabled={
                  (workflowType === 'training' && (!trainingResults || (dualData && !classificationResults))) ||
                  (workflowType === 'inference' && !classificationResults) ||
                  (workflowType === 'model_classification')
                }
              >
                View Results
                <ArrowRight className="w-4 h-4 ml-2" />
              </Button>
            </div>
          )}

          {/* Model Selection Step */}
          {isCurrentStep('model_selection') && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Shield className="w-5 h-5" />
                  Model Selection
                </CardTitle>
                <CardDescription>
                  Select a trained multi-class model for classification
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <MultiClassModelManager
                  onModelUse={(modelId) => {
                    // Handle model selection
                    const model = selectedModel;
                    if (model) {
                      setSelectedModel(model);
                      toast({
                        title: "Model selected",
                        description: `Using ${model.name} for classification`
                      });
                    }
                  }}
                  onModelDelete={(modelId) => {
                    toast({
                      title: "Model deleted",
                      description: "Model has been removed"
                    });
                  }}
                />

                <div className="flex justify-between">
                  <Button variant="outline" onClick={handlePrevious}>
                    <ArrowLeft className="w-4 h-4 mr-2" />
                    Previous
                  </Button>
                  <Button
                    onClick={handleNext}
                    disabled={!selectedModel}
                  >
                    Next Step
                    <ArrowRight className="w-4 h-4 ml-2" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Classification Step */}
          {isCurrentStep('classification') && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Target className="w-5 h-5" />
                  Classification
                </CardTitle>
                <CardDescription>
                  Run classification on your data using the trained model
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {isClassifying ? (
                  <div className="space-y-4">
                    <div className="text-center">
                      <div className="text-lg font-medium">Classifying Data...</div>
                      <div className="text-sm text-muted-foreground">
                        Processing {dualData?.classificationData?.fileInfo?.num_rows || uploadedData?.num_rows || 0} texts
                      </div>
                    </div>
                    <Progress value={classificationProgress} className="h-2" />
                    <div className="text-center text-sm text-muted-foreground">
                      {classificationProgress}% Complete
                    </div>
                  </div>
                ) : (
                  <div className="space-y-4">
                    <div className="p-4 bg-blue-50 rounded-lg">
                      <div className="font-medium">Ready to classify</div>
                      <div className="text-sm text-muted-foreground">
                        {dualData ?
                          `Classification data: ${dualData.classificationData.fileInfo.filename} (${dualData.classificationData.fileInfo.num_rows} rows)` :
                          `Data: ${uploadedData?.filename} (${uploadedData?.num_rows} rows)`
                        }
                      </div>
                    </div>

                    <div className="flex justify-between">
                      <Button variant="outline" onClick={handlePrevious}>
                        <ArrowLeft className="w-4 h-4 mr-2" />
                        Previous
                      </Button>
                      <Button onClick={startClassification}>
                        <Play className="w-4 h-4 mr-2" />
                        Start Classification
                      </Button>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Results Step */}
          {isCurrentStep('results') && (trainingResults || classificationResults) && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="w-5 h-5" />
                  Results Analysis
                </CardTitle>
                <CardDescription>
                  Analyze your multi-class classification results and performance metrics
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {trainingResults && (
                  <MultiClassResults
                    results={{
                      accuracy: trainingResults.metrics.accuracy,
                      f1Score: trainingResults.metrics.f1_score,
                      precision: trainingResults.metrics.precision,
                      recall: trainingResults.metrics.recall,
                      strategy: trainingResults.strategy,
                      perClassMetrics: trainingResults.metrics.per_class_metrics.reduce((acc, metric) => ({
                        ...acc,
                        [metric.class]: {
                          precision: metric.precision,
                          recall: metric.recall,
                          f1Score: metric.f1_score,
                          support: metric.support
                        }
                      }), {}),
                      confusionMatrix: trainingResults.metrics.confusion_matrix,
                      classNames: detectedClasses,
                      trainingTime: trainingResults.training_time,
                      modelSize: trainingResults.model_size,
                      totalSamples: uploadedData?.num_rows || 0,
                      samplePredictions: classificationResults?.predictions?.slice(0, 10).map((pred: any) => ({
                        text: pred.text,
                        actualClass: pred.actual_class,
                        predictedClass: pred.predicted_class,
                        confidence: pred.confidence,
                        correct: pred.actual_class === pred.predicted_class
                      }))
                    }}
                    onExport={(format) => {
                      toast({
                        title: "Export started",
                        description: `Exporting results in ${format} format`
                      });
                    }}
                    onRetrain={() => {
                      setCurrentStep(4);
                      setTrainingResults(null);
                      setClassificationResults(null);
                    }}
                  />
                )}

                <div className="flex justify-between">
                  <Button variant="outline" onClick={handlePrevious}>
                    <ArrowLeft className="w-4 h-4 mr-2" />
                    Previous
                  </Button>
                  <Button onClick={handleNext}>
                    Deploy Model
                    <ArrowRight className="w-4 h-4 ml-2" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Deploy Step */}
          {isCurrentStep('deploy') && trainingResults && (
            <DeployStep
              modelId={trainingResults.model_id}
              modelName={trainingConfig?.modelName || `Multi-Class Model ${trainingResults.task_id.slice(-8)}`}
              classificationType="multi-class"
              trainingMetrics={{
                accuracy: trainingResults.metrics.accuracy,
                f1_score: trainingResults.metrics.f1_score,
                precision: trainingResults.metrics.precision,
                recall: trainingResults.metrics.recall,
                training_time: trainingResults.training_time,
                model_size_mb: trainingResults.model_size,
                strategy: selectedStrategy,
                num_classes: detectedClasses.length,
                class_names: detectedClasses
              }}
              labels={detectedClasses}
              userLicense={userLicense}
              onComplete={(deploymentInfo) => {
                console.log('Multi-class deployment completed:', deploymentInfo);
                if (onComplete) {
                  onComplete({
                    type: 'multi-class',
                    trainingResults,
                    classificationResults,
                    deploymentInfo
                  });
                }
              }}
              onBack={() => setCurrentStep(6)}
            />
          )}
        </div>
      </div>
    </div>
  );
};

export default MultiClassWorkflow;
