import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  Target, 
  BarChart3, 
  Tags, 
  TreePine, 
  LayoutGrid,
  ArrowRight,
  CheckCircle2,
  Info,
  Lightbulb
} from "lucide-react";
import { cn } from "@/lib/utils";

export interface ClassificationType {
  id: string;
  title: string;
  description: string;
  icon: any;
  color: string;
  bgColor: string;
  examples: string[];
  useCases: string[];
  complexity: 'Beginner' | 'Intermediate' | 'Advanced';
  recommendedFor: string[];
}

const classificationTypes: ClassificationType[] = [
  {
    id: "binary",
    title: "Binary Classification",
    description: "Two-class problems with clear yes/no decisions",
    icon: Target,
    color: "text-ml-primary",
    bgColor: "bg-ml-primary/10",
    examples: ["Spam vs. Not Spam", "Positive vs. Negative", "Fraud Detection"],
    useCases: ["Email filtering", "Sentiment analysis", "Medical diagnosis", "Quality control"],
    complexity: "Beginner",
    recommendedFor: ["Simple decision problems", "Clear binary outcomes", "High accuracy requirements"]
  },
  {
    id: "multiclass",
    title: "Multi-class Classification",
    description: "Multiple mutually exclusive classes",
    icon: BarChart3,
    color: "text-ml-secondary",
    bgColor: "bg-ml-secondary/10",
    examples: ["Sentiment Analysis", "Image Recognition", "Document Classification"],
    useCases: ["Topic classification", "Product categorization", "Language detection", "Priority levels"],
    complexity: "Intermediate",
    recommendedFor: ["Multiple distinct categories", "Mutually exclusive labels", "Standard classification"]
  },
  {
    id: "multilabel",
    title: "Multi-label Classification",
    description: "Multiple non-exclusive labels per instance",
    icon: Tags,
    color: "text-ml-accent",
    bgColor: "bg-ml-accent/10",
    examples: ["Topic Tagging", "Movie Genres", "Medical Diagnosis"],
    useCases: ["Content tagging", "Skill assessment", "Feature detection", "Multi-attribute classification"],
    complexity: "Advanced",
    recommendedFor: ["Multiple simultaneous labels", "Complex feature detection", "Flexible categorization"]
  },
  {
    id: "hierarchical",
    title: "Hierarchical Classification",
    description: "Tree-structured class relationships",
    icon: TreePine,
    color: "text-ml-success",
    bgColor: "bg-ml-success/10",
    examples: ["Product Categories", "Taxonomies", "Organizational Structure"],
    useCases: ["E-commerce categorization", "Scientific classification", "Content organization", "Knowledge management"],
    complexity: "Advanced",
    recommendedFor: ["Nested categories", "Taxonomic structures", "Hierarchical relationships"]
  },
  {
    id: "flat",
    title: "Flat Classification",
    description: "Standard single-level classification",
    icon: LayoutGrid,
    color: "text-ml-warning",
    bgColor: "bg-ml-warning/10",
    examples: ["Standard Categories", "Simple Grouping", "Basic Sorting"],
    useCases: ["Simple categorization", "Basic grouping", "Standard sorting", "General classification"],
    complexity: "Beginner",
    recommendedFor: ["Simple categorization", "Non-hierarchical data", "Standard classification tasks"]
  }
];

interface ClassificationTypeSelectorProps {
  onTypeSelect: (type: ClassificationType) => void;
  selectedType?: string;
  showRecommendation?: boolean;
  recommendedType?: string;
}

export const ClassificationTypeSelector = ({ 
  onTypeSelect, 
  selectedType, 
  showRecommendation = false,
  recommendedType 
}: ClassificationTypeSelectorProps) => {
  const [hoveredType, setHoveredType] = useState<string | null>(null);

  const getComplexityColor = (complexity: string) => {
    switch (complexity) {
      case 'Beginner': return 'text-ml-success';
      case 'Intermediate': return 'text-ml-warning';
      case 'Advanced': return 'text-ml-error';
      default: return 'text-muted-foreground';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 rounded-lg bg-primary/10 flex items-center justify-center">
              <LayoutGrid className="w-5 h-5 text-primary" />
            </div>
            <div>
              <CardTitle>Select Classification Type</CardTitle>
              <CardDescription>
                Choose the classification approach that best fits your data and problem
              </CardDescription>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Recommendation Banner */}
      {showRecommendation && recommendedType && (
        <Card className="border-2 border-ml-success/20 bg-ml-success/5">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 rounded-lg bg-ml-success/10 flex items-center justify-center">
                <Lightbulb className="w-4 h-4 text-ml-success" />
              </div>
              <div>
                <h4 className="font-semibold text-ml-success">AI Recommendation</h4>
                <p className="text-sm text-muted-foreground">
                  Based on your data analysis, we recommend <strong>{classificationTypes.find(t => t.id === recommendedType)?.title}</strong>
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Classification Types Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {classificationTypes.map((type) => {
          const Icon = type.icon;
          const isSelected = selectedType === type.id;
          const isRecommended = recommendedType === type.id;
          const isHovered = hoveredType === type.id;

          return (
            <Card
              key={type.id}
              className={cn(
                "relative cursor-pointer transition-all duration-300 hover:shadow-card",
                isSelected && "border-2 border-primary bg-primary/5",
                isRecommended && "border-2 border-ml-success/50",
                "hover:scale-105"
              )}
              onMouseEnter={() => setHoveredType(type.id)}
              onMouseLeave={() => setHoveredType(null)}
              onClick={() => onTypeSelect(type)}
            >
              {/* Recommendation Badge */}
              {isRecommended && (
                <div className="absolute -top-2 -right-2 z-10">
                  <Badge className="bg-ml-success text-white">
                    <CheckCircle2 className="w-3 h-3 mr-1" />
                    Recommended
                  </Badge>
                </div>
              )}

              {/* Selection Indicator */}
              {isSelected && (
                <div className="absolute top-3 right-3">
                  <div className="w-6 h-6 rounded-full bg-primary flex items-center justify-center">
                    <CheckCircle2 className="w-4 h-4 text-white" />
                  </div>
                </div>
              )}

              <CardHeader>
                <div className="flex items-center gap-3 mb-3">
                  <div className={cn("w-12 h-12 rounded-lg flex items-center justify-center", type.bgColor)}>
                    <Icon className={cn("w-6 h-6", type.color)} />
                  </div>
                  <div className="flex-1">
                    <CardTitle className="text-lg">{type.title}</CardTitle>
                    <div className="flex items-center gap-2 mt-1">
                      <Badge variant="outline" className={getComplexityColor(type.complexity)}>
                        {type.complexity}
                      </Badge>
                    </div>
                  </div>
                </div>
                <CardDescription className="text-sm">
                  {type.description}
                </CardDescription>
              </CardHeader>

              <CardContent>
                <div className="space-y-4">
                  {/* Examples */}
                  <div>
                    <h4 className="font-semibold text-sm mb-2">Examples:</h4>
                    <ul className="text-xs text-muted-foreground space-y-1">
                      {type.examples.map((example, index) => (
                        <li key={index}>• {example}</li>
                      ))}
                    </ul>
                  </div>

                  {/* Use Cases - Show on hover or selection */}
                  {(isHovered || isSelected) && (
                    <div className="animate-in slide-in-from-top-2 duration-200">
                      <h4 className="font-semibold text-sm mb-2">Use Cases:</h4>
                      <ul className="text-xs text-muted-foreground space-y-1">
                        {type.useCases.slice(0, 3).map((useCase, index) => (
                          <li key={index}>• {useCase}</li>
                        ))}
                      </ul>
                    </div>
                  )}

                  {/* Recommended For */}
                  {(isHovered || isSelected) && (
                    <div className="animate-in slide-in-from-top-2 duration-300">
                      <h4 className="font-semibold text-sm mb-2 flex items-center gap-1">
                        <Info className="w-3 h-3" />
                        Best For:
                      </h4>
                      <ul className="text-xs text-muted-foreground space-y-1">
                        {type.recommendedFor.slice(0, 2).map((rec, index) => (
                          <li key={index}>• {rec}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Selection Action */}
      {selectedType && (
        <div className="flex justify-center pt-6">
          <Button 
            size="lg" 
            className="bg-primary hover:bg-primary/90"
            onClick={() => {
              const selected = classificationTypes.find(t => t.id === selectedType);
              if (selected) onTypeSelect(selected);
            }}
          >
            Continue with {classificationTypes.find(t => t.id === selectedType)?.title}
            <ArrowRight className="w-4 h-4 ml-2" />
          </Button>
        </div>
      )}

      {/* Help Section */}
      <Card className="bg-muted/30">
        <CardContent className="p-4">
          <div className="flex items-start gap-3">
            <div className="w-8 h-8 rounded-lg bg-primary/10 flex items-center justify-center flex-shrink-0">
              <Info className="w-4 h-4 text-primary" />
            </div>
            <div>
              <h4 className="font-semibold text-sm mb-1">Need help choosing?</h4>
              <p className="text-xs text-muted-foreground mb-3">
                Consider your data structure and the type of predictions you need. Binary is simplest, 
                while hierarchical handles complex nested categories.
              </p>
              <div className="flex gap-2">
                <Button variant="outline" size="sm">
                  <Info className="w-3 h-3 mr-1" />
                  Learn More
                </Button>
                <Button variant="outline" size="sm">
                  <Lightbulb className="w-3 h-3 mr-1" />
                  Get Recommendation
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
