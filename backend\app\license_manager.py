"""License Management System for ClassyWeb ML Platform.

This module provides foundational license management with hardware fingerprinting,
activation limits, and marketplace model infrastructure for monetization.
"""

import logging
import hashlib
import platform
import uuid
import json
from datetime import datetime, timedelta, timezone
from typing import Dict, Any, Optional, List
from sqlalchemy.orm import Session
import psutil

from .database import get_db, License, LicenseTypeEnum, LicenseStatusEnum

logger = logging.getLogger(__name__)


class LicenseManager:
    """License management with hardware fingerprinting and activation control."""
    
    def __init__(self):
        self.feature_flags = {
            LicenseTypeEnum.PERSONAL: {
                'max_models': 10,
                'max_monthly_inferences': 10000,
                'gpu_acceleration': True,
                'custom_training': True,
                'llm_inference': True,
                'marketplace_access': True,
                'premium_models': False,
                'commercial_use': False,
                'team_sharing': False,
                'priority_support': False
            },
            LicenseTypeEnum.PROFESSIONAL: {
                'max_models': 100,
                'max_monthly_inferences': 100000,
                'gpu_acceleration': True,
                'custom_training': True,
                'llm_inference': True,
                'marketplace_access': True,
                'premium_models': True,
                'commercial_use': True,
                'team_sharing': True,
                'priority_support': True
            },
            LicenseTypeEnum.ENTERPRISE: {
                'max_models': -1,  # Unlimited
                'max_monthly_inferences': -1,  # Unlimited
                'gpu_acceleration': True,
                'custom_training': True,
                'llm_inference': True,
                'marketplace_access': True,
                'premium_models': True,
                'commercial_use': True,
                'team_sharing': True,
                'priority_support': True,
                'custom_branding': True,
                'on_premise_deployment': True,
                'professional_services': True
            }
        }
    
    def generate_hardware_fingerprint(self) -> str:
        """Generate a unique hardware fingerprint for license binding."""
        try:
            # Collect hardware information
            system_info = {
                'platform': platform.platform(),
                'processor': platform.processor(),
                'machine': platform.machine(),
                'node': platform.node()
            }
            
            # Add CPU information
            try:
                system_info['cpu_count'] = psutil.cpu_count()
                system_info['cpu_freq'] = psutil.cpu_freq().max if psutil.cpu_freq() else 0
            except:
                pass
            
            # Add memory information
            try:
                memory = psutil.virtual_memory()
                system_info['memory_total'] = memory.total
            except:
                pass
            
            # Create hash of system information
            system_str = json.dumps(system_info, sort_keys=True)
            fingerprint = hashlib.sha256(system_str.encode()).hexdigest()[:32]
            
            return fingerprint
            
        except Exception as e:
            logger.warning(f"Failed to generate hardware fingerprint: {e}")
            # Fallback to a random UUID if hardware detection fails
            return str(uuid.uuid4()).replace('-', '')[:32]
    
    def generate_license_key(self, license_type: LicenseTypeEnum, user_id: int) -> str:
        """Generate a unique license key."""
        # Create a unique identifier combining type, user, and timestamp
        identifier = f"{license_type.value}-{user_id}-{int(datetime.now().timestamp())}"
        key_hash = hashlib.sha256(identifier.encode()).hexdigest()[:16].upper()
        
        # Format as license key (XXXX-XXXX-XXXX-XXXX)
        formatted_key = '-'.join([key_hash[i:i+4] for i in range(0, 16, 4)])
        
        return formatted_key
    
    def create_license(
        self,
        user_id: int,
        license_type: LicenseTypeEnum,
        billing_cycle: str = 'yearly',
        price_paid_cents: Optional[int] = None
    ) -> License:
        """Create a new license for a user."""
        try:
            with Session(bind=get_db().bind) as db:
                # Generate license key
                license_key = self.generate_license_key(license_type, user_id)
                
                # Set expiry based on billing cycle
                expires_at = None
                if billing_cycle == 'monthly':
                    expires_at = datetime.now(timezone.utc) + timedelta(days=30)
                elif billing_cycle == 'yearly':
                    expires_at = datetime.now(timezone.utc) + timedelta(days=365)
                # lifetime licenses don't expire
                
                # Get feature flags for this license type
                features = self.feature_flags.get(license_type, {})
                
                # Create license
                license = License(
                    user_id=user_id,
                    license_type=license_type,
                    license_key=license_key,
                    max_models=features.get('max_models'),
                    max_monthly_inferences=features.get('max_monthly_inferences'),
                    feature_flags=features,
                    price_paid_cents=price_paid_cents,
                    billing_cycle=billing_cycle,
                    expires_at=expires_at,
                    status=LicenseStatusEnum.ACTIVE
                )
                
                db.add(license)
                db.commit()
                db.refresh(license)
                
                logger.info(f"Created {license_type.value} license for user {user_id}")
                return license
                
        except Exception as e:
            logger.error(f"Failed to create license: {e}")
            raise
    
    def activate_license(self, license_key: str, hardware_fingerprint: Optional[str] = None) -> Dict[str, Any]:
        """Activate a license on the current hardware."""
        try:
            with Session(bind=get_db().bind) as db:
                license = db.query(License).filter(
                    License.license_key == license_key
                ).first()
                
                if not license:
                    return {'success': False, 'error': 'Invalid license key'}
                
                if license.status != LicenseStatusEnum.ACTIVE:
                    return {'success': False, 'error': f'License is {license.status.value}'}
                
                if license.expires_at and license.expires_at < datetime.now(timezone.utc):
                    license.status = LicenseStatusEnum.EXPIRED
                    db.commit()
                    return {'success': False, 'error': 'License has expired'}
                
                # Generate hardware fingerprint if not provided
                if not hardware_fingerprint:
                    hardware_fingerprint = self.generate_hardware_fingerprint()
                
                # Check activation limits
                if license.activation_count >= license.max_activations:
                    # Check if this hardware is already activated
                    if license.hardware_fingerprint != hardware_fingerprint:
                        return {'success': False, 'error': 'Maximum activations reached'}
                
                # Activate license
                license.hardware_fingerprint = hardware_fingerprint
                license.activation_count = max(license.activation_count, 1)
                license.last_used_at = datetime.now(timezone.utc)
                
                db.commit()
                
                return {
                    'success': True,
                    'license_type': license.license_type.value,
                    'features': license.feature_flags,
                    'expires_at': license.expires_at.isoformat() if license.expires_at else None
                }
                
        except Exception as e:
            logger.error(f"Failed to activate license: {e}")
            return {'success': False, 'error': 'Activation failed'}
    
    def validate_license(self, user_id: int, feature: str = None) -> Dict[str, Any]:
        """Validate a user's license and check feature access."""
        try:
            with Session(bind=get_db().bind) as db:
                license = db.query(License).filter(
                    License.user_id == user_id,
                    License.status == LicenseStatusEnum.ACTIVE
                ).first()
                
                if not license:
                    return {'valid': False, 'error': 'No active license found'}
                
                # Check expiry
                if license.expires_at and license.expires_at < datetime.now(timezone.utc):
                    license.status = LicenseStatusEnum.EXPIRED
                    db.commit()
                    return {'valid': False, 'error': 'License has expired'}
                
                # Check feature access if specified
                if feature:
                    features = license.feature_flags or {}
                    if not features.get(feature, False):
                        return {'valid': False, 'error': f'Feature {feature} not available in your license'}
                
                # Update last used
                license.last_used_at = datetime.now(timezone.utc)
                db.commit()
                
                return {
                    'valid': True,
                    'license_type': license.license_type.value,
                    'features': license.feature_flags,
                    'usage': {
                        'models_trained': license.total_models_trained,
                        'monthly_inferences': license.current_month_inferences,
                        'max_models': license.max_models,
                        'max_monthly_inferences': license.max_monthly_inferences
                    }
                }
                
        except Exception as e:
            logger.error(f"Failed to validate license: {e}")
            return {'valid': False, 'error': 'License validation failed'}
    
    def check_usage_limits(self, user_id: int, operation: str) -> Dict[str, Any]:
        """Check if user can perform an operation based on license limits."""
        try:
            license_info = self.validate_license(user_id)
            if not license_info['valid']:
                return license_info
            
            usage = license_info['usage']
            
            if operation == 'train_model':
                max_models = usage['max_models']
                if max_models != -1 and usage['models_trained'] >= max_models:
                    return {'allowed': False, 'error': 'Model training limit reached'}
            
            elif operation == 'inference':
                max_inferences = usage['max_monthly_inferences']
                if max_inferences != -1 and usage['monthly_inferences'] >= max_inferences:
                    return {'allowed': False, 'error': 'Monthly inference limit reached'}
            
            return {'allowed': True}
            
        except Exception as e:
            logger.error(f"Failed to check usage limits: {e}")
            return {'allowed': False, 'error': 'Usage check failed'}
    
    def update_usage(self, user_id: int, operation: str, count: int = 1) -> bool:
        """Update usage statistics for a user."""
        try:
            with Session(bind=get_db().bind) as db:
                license = db.query(License).filter(
                    License.user_id == user_id,
                    License.status == LicenseStatusEnum.ACTIVE
                ).first()
                
                if not license:
                    return False
                
                if operation == 'train_model':
                    license.total_models_trained += count
                elif operation == 'inference':
                    license.current_month_inferences += count
                
                db.commit()
                return True
                
        except Exception as e:
            logger.error(f"Failed to update usage: {e}")
            return False
    
    def get_user_license_info(self, user_id: int) -> Optional[Dict[str, Any]]:
        """Get comprehensive license information for a user."""
        try:
            with Session(bind=get_db().bind) as db:
                license = db.query(License).filter(
                    License.user_id == user_id
                ).first()
                
                if not license:
                    return None
                
                return {
                    'license_key': license.license_key,
                    'license_type': license.license_type.value,
                    'status': license.status.value,
                    'features': license.feature_flags,
                    'expires_at': license.expires_at.isoformat() if license.expires_at else None,
                    'usage': {
                        'models_trained': license.total_models_trained,
                        'monthly_inferences': license.current_month_inferences,
                        'max_models': license.max_models,
                        'max_monthly_inferences': license.max_monthly_inferences
                    },
                    'activation_count': license.activation_count,
                    'max_activations': license.max_activations,
                    'last_used_at': license.last_used_at.isoformat() if license.last_used_at else None
                }
                
        except Exception as e:
            logger.error(f"Failed to get license info: {e}")
            return None


# Global license manager instance
license_manager = LicenseManager()


# Convenience functions for external use

def create_user_license(
    user_id: int,
    license_type: str,
    billing_cycle: str = 'yearly',
    price_paid_cents: Optional[int] = None
) -> License:
    """Create a license for a user."""
    license_type_enum = LicenseTypeEnum(license_type)
    return license_manager.create_license(user_id, license_type_enum, billing_cycle, price_paid_cents)


def validate_user_license(user_id: int, feature: str = None) -> Dict[str, Any]:
    """Validate a user's license."""
    return license_manager.validate_license(user_id, feature)


def check_user_limits(user_id: int, operation: str) -> Dict[str, Any]:
    """Check if user can perform an operation."""
    return license_manager.check_usage_limits(user_id, operation)


def update_user_usage(user_id: int, operation: str, count: int = 1) -> bool:
    """Update user usage statistics."""
    return license_manager.update_usage(user_id, operation, count)
