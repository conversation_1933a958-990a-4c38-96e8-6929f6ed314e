"""
Performance optimization service for ClassyWeb ML Platform.

This module provides:
1. Query optimization utilities
2. Caching strategies
3. Large dataset handling
4. Memory management
"""
import logging
import time
import hashlib
import pickle
from typing import Any, Dict, List, Optional, Callable
from functools import wraps
from datetime import datetime, timedelta

from sqlalchemy.orm import Session, joinedload, selectinload
from sqlalchemy import text

logger = logging.getLogger(__name__)

# In-memory cache (in production, use Redis)
_cache: Dict[str, Dict[str, Any]] = {}
_cache_stats = {"hits": 0, "misses": 0, "sets": 0}

class PerformanceOptimizer:
    """Performance optimization utilities."""
    
    @staticmethod
    def cache_key(*args, **kwargs) -> str:
        """Generate a cache key from arguments."""
        key_data = str(args) + str(sorted(kwargs.items()))
        return hashlib.md5(key_data.encode()).hexdigest()
    
    @staticmethod
    def cached_query(ttl_seconds: int = 300):
        """Decorator to cache query results."""
        def decorator(func: Callable) -> Callable:
            @wraps(func)
            def wrapper(*args, **kwargs):
                # Generate cache key
                cache_key = PerformanceOptimizer.cache_key(func.__name__, *args, **kwargs)
                
                # Check cache
                if cache_key in _cache:
                    cache_entry = _cache[cache_key]
                    if datetime.now() < cache_entry["expires"]:
                        _cache_stats["hits"] += 1
                        logger.debug(f"Cache hit for {func.__name__}")
                        return cache_entry["data"]
                    else:
                        # Expired entry
                        del _cache[cache_key]
                
                # Cache miss - execute function
                _cache_stats["misses"] += 1
                logger.debug(f"Cache miss for {func.__name__}")
                result = func(*args, **kwargs)
                
                # Store in cache
                _cache[cache_key] = {
                    "data": result,
                    "expires": datetime.now() + timedelta(seconds=ttl_seconds),
                    "created": datetime.now()
                }
                _cache_stats["sets"] += 1
                
                return result
            return wrapper
        return decorator
    
    @staticmethod
    def optimize_query(db: Session, query, use_eager_loading: bool = True):
        """Optimize database query with eager loading."""
        if use_eager_loading:
            # Add common joins to reduce N+1 queries
            if hasattr(query, 'options'):
                query = query.options(
                    joinedload('*'),  # Load all relationships
                    selectinload('*')  # Use select in loading for collections
                )
        return query
    
    @staticmethod
    def paginate_large_dataset(query, page: int = 1, page_size: int = 100, max_page_size: int = 1000):
        """Paginate large datasets efficiently."""
        if page_size > max_page_size:
            page_size = max_page_size
        
        offset = (page - 1) * page_size
        return query.offset(offset).limit(page_size)
    
    @staticmethod
    def batch_process(items: List[Any], batch_size: int = 100, processor: Callable = None):
        """Process large datasets in batches."""
        if not processor:
            return items
        
        results = []
        for i in range(0, len(items), batch_size):
            batch = items[i:i + batch_size]
            batch_results = processor(batch)
            results.extend(batch_results)
            
            # Allow other operations to run
            time.sleep(0.001)
        
        return results
    
    @staticmethod
    def get_cache_stats() -> Dict[str, Any]:
        """Get cache performance statistics."""
        total_requests = _cache_stats["hits"] + _cache_stats["misses"]
        hit_rate = _cache_stats["hits"] / total_requests if total_requests > 0 else 0
        
        return {
            "hits": _cache_stats["hits"],
            "misses": _cache_stats["misses"],
            "sets": _cache_stats["sets"],
            "hit_rate": hit_rate,
            "cache_size": len(_cache)
        }
    
    @staticmethod
    def clear_cache():
        """Clear all cached data."""
        global _cache, _cache_stats
        _cache.clear()
        _cache_stats = {"hits": 0, "misses": 0, "sets": 0}
        logger.info("Cache cleared")
    
    @staticmethod
    def cleanup_expired_cache():
        """Remove expired cache entries."""
        now = datetime.now()
        expired_keys = [
            key for key, entry in _cache.items()
            if now >= entry["expires"]
        ]
        
        for key in expired_keys:
            del _cache[key]
        
        if expired_keys:
            logger.info(f"Cleaned up {len(expired_keys)} expired cache entries")


class DatabaseOptimizer:
    """Database-specific performance optimizations."""
    
    @staticmethod
    def analyze_query_performance(db: Session, query_sql: str) -> Dict[str, Any]:
        """Analyze query performance (SQLite specific)."""
        try:
            # Use EXPLAIN QUERY PLAN for SQLite
            explain_result = db.execute(text(f"EXPLAIN QUERY PLAN {query_sql}")).fetchall()
            
            analysis = {
                "query": query_sql,
                "plan": [dict(row) for row in explain_result],
                "timestamp": datetime.now().isoformat()
            }
            
            return analysis
        except Exception as e:
            logger.error(f"Query analysis failed: {e}")
            return {"error": str(e)}
    
    @staticmethod
    def optimize_database_settings(db: Session):
        """Apply database optimization settings."""
        try:
            # SQLite optimizations
            optimizations = [
                "PRAGMA journal_mode = WAL",  # Write-Ahead Logging
                "PRAGMA synchronous = NORMAL",  # Balance safety and speed
                "PRAGMA cache_size = 10000",  # Increase cache size
                "PRAGMA temp_store = MEMORY",  # Store temp tables in memory
                "PRAGMA mmap_size = 268435456",  # Use memory mapping (256MB)
            ]
            
            for pragma in optimizations:
                db.execute(text(pragma))
                logger.debug(f"Applied optimization: {pragma}")
            
            db.commit()
            logger.info("Database optimizations applied")
            
        except Exception as e:
            logger.error(f"Failed to apply database optimizations: {e}")
    
    @staticmethod
    def create_performance_indexes(db: Session):
        """Create additional performance indexes."""
        try:
            indexes = [
                "CREATE INDEX IF NOT EXISTS idx_tasks_status_created ON tasks(status, created_at)",
                "CREATE INDEX IF NOT EXISTS idx_files_user_size ON files(user_id, file_size)",
                "CREATE INDEX IF NOT EXISTS idx_hierarchy_configs_user_active ON hierarchy_configs(user_id, is_active)",
            ]
            
            for index_sql in indexes:
                db.execute(text(index_sql))
                logger.debug(f"Created index: {index_sql}")
            
            db.commit()
            logger.info("Performance indexes created")
            
        except Exception as e:
            logger.error(f"Failed to create performance indexes: {e}")


class MemoryOptimizer:
    """Memory usage optimization utilities."""
    
    @staticmethod
    def optimize_dataframe_memory(df):
        """Optimize pandas DataFrame memory usage."""
        try:
            import pandas as pd
            
            # Optimize numeric columns
            for col in df.select_dtypes(include=['int64']).columns:
                df[col] = pd.to_numeric(df[col], downcast='integer')
            
            for col in df.select_dtypes(include=['float64']).columns:
                df[col] = pd.to_numeric(df[col], downcast='float')
            
            # Optimize string columns
            for col in df.select_dtypes(include=['object']).columns:
                if df[col].nunique() / len(df) < 0.5:  # If less than 50% unique values
                    df[col] = df[col].astype('category')
            
            logger.debug(f"DataFrame memory optimized: {df.memory_usage(deep=True).sum() / 1024**2:.2f} MB")
            return df
            
        except Exception as e:
            logger.error(f"DataFrame memory optimization failed: {e}")
            return df
    
    @staticmethod
    def chunk_large_file(file_path: str, chunk_size: int = 10000):
        """Process large files in chunks."""
        try:
            import pandas as pd
            
            for chunk in pd.read_csv(file_path, chunksize=chunk_size):
                yield MemoryOptimizer.optimize_dataframe_memory(chunk)
                
        except Exception as e:
            logger.error(f"File chunking failed: {e}")
            yield None


# Global performance optimizer instance
performance_optimizer = PerformanceOptimizer()
database_optimizer = DatabaseOptimizer()
memory_optimizer = MemoryOptimizer()

# Convenience decorators
cached_query = performance_optimizer.cached_query
