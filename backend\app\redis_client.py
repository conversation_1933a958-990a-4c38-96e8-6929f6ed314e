"""
Redis client for token blacklisting and session management.
"""
import logging
import redis
from datetime import datetime, timedelta

from . import config

# Set up logging
logger = logging.getLogger(__name__)

# Create Redis client
try:
    # Check if Redis URL is configured
    if not config.REDIS_URL or config.REDIS_URL.strip() == "":
        logger.info("Redis URL not configured, using in-memory fallback")
        raise Exception("Redis URL not configured")

    redis_client = redis.from_url(config.REDIS_URL)
    # Test the connection
    redis_client.ping()
    logger.info(f"Connected to Redis at {config.REDIS_URL}")
except Exception as e:
    logger.warning(f"Redis not available ({e}), using in-memory fallback")
    # Create a dummy client for development without Redis
    class DummyRedisClient:
        """Dummy Redis client for development without Redis."""
        def __init__(self):
            self._storage = {}
            logger.warning("Using in-memory dummy Redis client. This is not suitable for production.")

        def setex(self, key, time, value):
            """Set key with expiry time."""
            expiry = datetime.now() + timedelta(seconds=time)
            self._storage[key] = (value, expiry)
            return True

        def get(self, key):
            """Get value for key."""
            if key not in self._storage:
                return None
            value, expiry = self._storage[key]
            if datetime.now() > expiry:
                del self._storage[key]
                return None
            return value

        def exists(self, key):
            """Check if key exists."""
            if key not in self._storage:
                return False
            value, expiry = self._storage[key]
            if datetime.now() > expiry:
                del self._storage[key]
                return False
            return True

        def delete(self, key):
            """Delete key."""
            if key in self._storage:
                del self._storage[key]
            return True

        def flushall(self):
            """Clear all keys."""
            self._storage = {}
            return True

    redis_client = DummyRedisClient()

def blacklist_token(token: str, expires_delta: int) -> bool:
    """
    Add a token to the blacklist.

    Args:
        token: JWT token to blacklist
        expires_delta: Time in seconds until the token expires

    Returns:
        True if successful, False otherwise
    """
    try:
        key = f"{config.REDIS_TOKEN_BLACKLIST_PREFIX}{token}"
        redis_client.setex(key, expires_delta, "1")
        logger.info(f"Token blacklisted successfully")
        return True
    except Exception as e:
        logger.error(f"Failed to blacklist token: {e}")
        return False

def is_token_blacklisted(token: str) -> bool:
    """
    Check if a token is blacklisted.

    Args:
        token: JWT token to check

    Returns:
        True if blacklisted, False otherwise
    """
    try:
        key = f"{config.REDIS_TOKEN_BLACKLIST_PREFIX}{token}"
        return redis_client.exists(key)
    except Exception as e:
        logger.error(f"Failed to check if token is blacklisted: {e}")
        return False

def store_refresh_token(user_id: int, token: str, expires_delta: int, metadata: dict = None) -> bool:
    """
    Store a refresh token with optional metadata.

    Args:
        user_id: User ID
        token: Refresh token
        expires_delta: Time in seconds until the token expires
        metadata: Optional metadata about the token (user agent, IP, etc.)

    Returns:
        True if successful, False otherwise
    """
    try:
        # Store the token itself
        token_key = f"refresh_token:{user_id}:{token}"

        # If we have metadata, store it as JSON
        if metadata:
            import json
            value = json.dumps({
                'user_id': str(user_id),
                'created_at': datetime.now().isoformat(),
                'metadata': metadata
            })
        else:
            value = str(user_id)

        redis_client.setex(token_key, expires_delta, value)

        # Also store a reference to this token in a user's tokens set
        user_tokens_key = f"user:{user_id}:tokens"

        # For the dummy client, we need to handle this differently
        if isinstance(redis_client, redis.Redis):
            # Add to a set with expiry
            redis_client.sadd(user_tokens_key, token)
            # Make sure the set expires eventually (1 month longer than the token)
            redis_client.expire(user_tokens_key, expires_delta + 2592000)  # 30 days in seconds

        logger.info(f"Refresh token stored for user {user_id}")
        return True
    except Exception as e:
        logger.error(f"Failed to store refresh token: {e}")
        return False

def validate_refresh_token(user_id: int, token: str) -> bool:
    """
    Validate a refresh token.

    Args:
        user_id: User ID
        token: Refresh token

    Returns:
        True if valid, False otherwise
    """
    try:
        key = f"refresh_token:{user_id}:{token}"
        stored_value = redis_client.get(key)

        if not stored_value:
            logger.warning(f"No refresh token found for user {user_id}")
            return False

        # Try to decode as JSON first (for tokens with metadata)
        try:
            import json
            data = json.loads(stored_value.decode())
            stored_user_id = data.get('user_id')
        except (json.JSONDecodeError, AttributeError):
            # If not JSON, it's just the user ID as a string
            try:
                stored_user_id = stored_value.decode()
            except AttributeError:
                # For dummy client that might not return bytes
                stored_user_id = stored_value

        if stored_user_id == str(user_id):
            return True

        logger.warning(f"Invalid refresh token for user {user_id}")
        return False
    except Exception as e:
        logger.error(f"Failed to validate refresh token: {e}")
        return False

def find_user_by_refresh_token(token: str) -> int | None:
    """
    Find user ID by refresh token by scanning Redis.

    Args:
        token: Refresh token to search for

    Returns:
        User ID if found, None otherwise
    """
    try:
        pattern = f"refresh_token:*:{token}"

        if hasattr(redis_client, 'scan_iter'):
            # Real Redis client
            for key in redis_client.scan_iter(match=pattern):
                key_str = key.decode('utf-8') if isinstance(key, bytes) else key
                # Extract user_id from key: refresh_token:user_id:token
                parts = key_str.split(':')
                if len(parts) >= 3:
                    try:
                        return int(parts[2])
                    except ValueError:
                        continue
        else:
            # Dummy Redis client - search in memory storage
            for key in redis_client._storage.keys():
                if key.startswith('refresh_token:') and key.endswith(f':{token}'):
                    parts = key.split(':')
                    if len(parts) >= 3:
                        try:
                            return int(parts[2])
                        except ValueError:
                            continue

        return None
    except Exception as e:
        logger.error(f"Error finding user by refresh token: {e}")
        return None

def invalidate_refresh_token(user_id: int, token: str) -> bool:
    """
    Invalidate a refresh token.

    Args:
        user_id: User ID
        token: Refresh token

    Returns:
        True if successful, False otherwise
    """
    try:
        key = f"refresh_token:{user_id}:{token}"
        redis_client.delete(key)
        logger.info(f"Refresh token invalidated for user {user_id}")
        return True
    except Exception as e:
        logger.error(f"Failed to invalidate refresh token: {e}")
        return False

def invalidate_all_user_tokens(user_id: int) -> bool:
    """
    Invalidate all refresh tokens for a user.

    Args:
        user_id: User ID

    Returns:
        True if successful, False otherwise
    """
    try:
        pattern = f"refresh_token:{user_id}:*"

        # Check if we're using the real Redis client or the dummy client
        if isinstance(redis_client, redis.Redis):
            # Real Redis implementation
            keys_to_delete = []
            for key in redis_client.scan_iter(pattern):
                keys_to_delete.append(key)

            if keys_to_delete:
                redis_client.delete(*keys_to_delete)
                logger.info(f"Invalidated {len(keys_to_delete)} refresh tokens for user {user_id}")
            else:
                logger.info(f"No refresh tokens found for user {user_id}")
        else:
            # Dummy client implementation
            # Find all keys matching the pattern
            keys_to_delete = []
            for key in list(redis_client._storage.keys()):
                if key.startswith(f"refresh_token:{user_id}:"):
                    keys_to_delete.append(key)

            # Delete all matching keys
            for key in keys_to_delete:
                redis_client.delete(key)

            logger.info(f"Invalidated {len(keys_to_delete)} refresh tokens for user {user_id}")

        return True
    except Exception as e:
        logger.error(f"Failed to invalidate all user tokens: {e}")
        return False
