/**
 * WebSocket Hook for Real-time Training Monitoring
 * 
 * This hook provides WebSocket functionality for real-time communication
 * with the backend during training sessions and system monitoring.
 */

import { useState, useEffect, useRef, useCallback } from 'react';

export interface WebSocketMessage {
  type: string;
  timestamp: number;
  [key: string]: any;
}

export interface TrainingProgress {
  session_id: string;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
  progress_percentage: number;
  current_stage: string;
  progress_data?: any;
  system_metrics?: any;
}

export interface SystemMetrics {
  cpu_percent: number;
  memory_percent: number;
  memory_used_gb: number;
  memory_total_gb: number;
  gpu_metrics: Array<{
    id: number;
    name: string;
    utilization: number;
    memory_used: number;
    memory_total: number;
    memory_percent: number;
    temperature: number;
  }>;
}

export interface UseWebSocketOptions {
  autoReconnect?: boolean;
  reconnectInterval?: number;
  maxReconnectAttempts?: number;
  heartbeatInterval?: number;
}

export interface UseWebSocketReturn {
  isConnected: boolean;
  isConnecting: boolean;
  error: string | null;
  lastMessage: WebSocketMessage | null;
  sendMessage: (message: any) => void;
  connect: () => void;
  disconnect: () => void;
  reconnectAttempts: number;
}

export const useWebSocket = (
  url: string,
  options: UseWebSocketOptions = {}
): UseWebSocketReturn => {
  const {
    autoReconnect = true,
    reconnectInterval = 3000,
    maxReconnectAttempts = 5,
    heartbeatInterval = 30000
  } = options;

  const [isConnected, setIsConnected] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastMessage, setLastMessage] = useState<WebSocketMessage | null>(null);
  const [reconnectAttempts, setReconnectAttempts] = useState(0);

  const wsRef = useRef<WebSocket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const heartbeatIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const mountedRef = useRef(true);

  // Get auth token
  const getAuthToken = useCallback(() => {
    return localStorage.getItem('token');
  }, []);

  // Build WebSocket URL with auth token
  const buildWebSocketUrl = useCallback(() => {
    const token = getAuthToken();
    const wsUrl = url.replace('http://', 'ws://').replace('https://', 'wss://');
    return token ? `${wsUrl}?token=${encodeURIComponent(token)}` : wsUrl;
  }, [url, getAuthToken]);

  // Send heartbeat ping
  const sendHeartbeat = useCallback(() => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      wsRef.current.send(JSON.stringify({
        type: 'ping',
        timestamp: Date.now()
      }));
    }
  }, []);

  // Send message
  const sendMessage = useCallback((message: any) => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      wsRef.current.send(JSON.stringify(message));
    } else {
      console.warn('WebSocket is not connected. Message not sent:', message);
    }
  }, []);

  // Connect to WebSocket
  const connect = useCallback(() => {
    if (wsRef.current?.readyState === WebSocket.OPEN || isConnecting) {
      return;
    }

    setIsConnecting(true);
    setError(null);

    try {
      const wsUrl = buildWebSocketUrl();
      wsRef.current = new WebSocket(wsUrl);

      wsRef.current.onopen = () => {
        if (!mountedRef.current) return;
        
        setIsConnected(true);
        setIsConnecting(false);
        setError(null);
        setReconnectAttempts(0);

        // Start heartbeat
        if (heartbeatIntervalRef.current) {
          clearInterval(heartbeatIntervalRef.current);
        }
        heartbeatIntervalRef.current = setInterval(sendHeartbeat, heartbeatInterval);

        console.log('WebSocket connected:', wsUrl);
      };

      wsRef.current.onmessage = (event) => {
        if (!mountedRef.current) return;

        try {
          const message: WebSocketMessage = JSON.parse(event.data);
          setLastMessage(message);
          
          // Handle pong responses
          if (message.type === 'pong') {
            console.log('WebSocket heartbeat received');
          }
        } catch (err) {
          console.error('Failed to parse WebSocket message:', err);
        }
      };

      wsRef.current.onclose = (event) => {
        if (!mountedRef.current) return;

        setIsConnected(false);
        setIsConnecting(false);

        // Clear heartbeat
        if (heartbeatIntervalRef.current) {
          clearInterval(heartbeatIntervalRef.current);
          heartbeatIntervalRef.current = null;
        }

        console.log('WebSocket closed:', event.code, event.reason);

        // Handle reconnection
        if (autoReconnect && reconnectAttempts < maxReconnectAttempts && event.code !== 1000) {
          setReconnectAttempts(prev => prev + 1);
          reconnectTimeoutRef.current = setTimeout(() => {
            if (mountedRef.current) {
              connect();
            }
          }, reconnectInterval);
        }
      };

      wsRef.current.onerror = (event) => {
        if (!mountedRef.current) return;

        setError('WebSocket connection error');
        setIsConnecting(false);
        console.error('WebSocket error:', event);
      };

    } catch (err) {
      setError('Failed to create WebSocket connection');
      setIsConnecting(false);
      console.error('WebSocket creation error:', err);
    }
  }, [
    buildWebSocketUrl,
    isConnecting,
    autoReconnect,
    reconnectAttempts,
    maxReconnectAttempts,
    reconnectInterval,
    sendHeartbeat,
    heartbeatInterval
  ]);

  // Disconnect from WebSocket
  const disconnect = useCallback(() => {
    // Clear timeouts
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }
    if (heartbeatIntervalRef.current) {
      clearInterval(heartbeatIntervalRef.current);
      heartbeatIntervalRef.current = null;
    }

    // Close WebSocket
    if (wsRef.current) {
      wsRef.current.close(1000, 'Manual disconnect');
      wsRef.current = null;
    }

    setIsConnected(false);
    setIsConnecting(false);
    setError(null);
    setReconnectAttempts(0);
  }, []);

  // Auto-connect on mount
  useEffect(() => {
    connect();

    return () => {
      mountedRef.current = false;
      disconnect();
    };
  }, [connect, disconnect]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      mountedRef.current = false;
      disconnect();
    };
  }, [disconnect]);

  return {
    isConnected,
    isConnecting,
    error,
    lastMessage,
    sendMessage,
    connect,
    disconnect,
    reconnectAttempts
  };
};

// Specialized hook for training session monitoring
export const useTrainingWebSocket = (sessionId: string) => {
  const [trainingProgress, setTrainingProgress] = useState<TrainingProgress | null>(null);
  const [isTrainingComplete, setIsTrainingComplete] = useState(false);

  const wsUrl = `${window.location.protocol === 'https:' ? 'wss:' : 'ws:'}//${window.location.host}/ws/training/${sessionId}`;
  
  const {
    isConnected,
    isConnecting,
    error,
    lastMessage,
    sendMessage,
    connect,
    disconnect,
    reconnectAttempts
  } = useWebSocket(wsUrl);

  // Process training-specific messages
  useEffect(() => {
    if (!lastMessage) return;

    switch (lastMessage.type) {
      case 'training_progress':
        setTrainingProgress({
          session_id: lastMessage.session_id,
          status: lastMessage.status,
          progress_percentage: lastMessage.progress_percentage,
          current_stage: lastMessage.current_stage,
          progress_data: lastMessage.progress_data,
          system_metrics: lastMessage.system_metrics
        });
        break;

      case 'training_complete':
        setTrainingProgress(prev => prev ? {
          ...prev,
          status: lastMessage.status,
          progress_percentage: 100
        } : null);
        setIsTrainingComplete(true);
        break;

      case 'connection_established':
        // Request current status
        sendMessage({ type: 'request_status' });
        break;
    }
  }, [lastMessage, sendMessage]);

  return {
    isConnected,
    isConnecting,
    error,
    trainingProgress,
    isTrainingComplete,
    sendMessage,
    connect,
    disconnect,
    reconnectAttempts
  };
};

// Specialized hook for system monitoring
export const useSystemWebSocket = () => {
  const [systemMetrics, setSystemMetrics] = useState<SystemMetrics | null>(null);

  const wsUrl = `${window.location.protocol === 'https:' ? 'wss:' : 'ws:'}//${window.location.host}/ws/system`;
  
  const {
    isConnected,
    isConnecting,
    error,
    lastMessage,
    sendMessage,
    connect,
    disconnect,
    reconnectAttempts
  } = useWebSocket(wsUrl);

  // Process system-specific messages
  useEffect(() => {
    if (!lastMessage) return;

    switch (lastMessage.type) {
      case 'system_metrics':
        setSystemMetrics(lastMessage.metrics);
        break;

      case 'connection_established':
        // Request current metrics
        sendMessage({ type: 'request_metrics' });
        break;
    }
  }, [lastMessage, sendMessage]);

  return {
    isConnected,
    isConnecting,
    error,
    systemMetrics,
    sendMessage,
    connect,
    disconnect,
    reconnectAttempts
  };
};
