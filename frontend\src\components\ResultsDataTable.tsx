import React, { useState, useMemo } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { 
  Download, 
  FileSpreadsheet, 
  Database, 
  ChevronLeft, 
  ChevronRight,
  Search,
  Filter
} from "lucide-react";
import { Input } from "@/components/ui/input";
import { ClassificationResultRow } from "@/types";

interface ResultsDataTableProps {
  data: ClassificationResultRow[];
  taskId: string;
  filename?: string;
  onExportCSV: () => void;
  onExportExcel: () => void;
  className?: string;
}

const ROWS_PER_PAGE = 50;

export const ResultsDataTable = ({ 
  data, 
  taskId, 
  filename, 
  onExportCSV, 
  onExportExcel, 
  className 
}: ResultsDataTableProps) => {
  const [currentPage, setCurrentPage] = useState(1);
  const [searchTerm, setSearchTerm] = useState("");

  // Get all unique column names from the data
  const columns = useMemo(() => {
    if (!data || !Array.isArray(data) || data.length === 0) return [];

    const allColumns = new Set<string>();
    data.forEach(row => {
      Object.keys(row).forEach(key => allColumns.add(key));
    });

    return Array.from(allColumns);
  }, [data]);

  // Separate original columns from classification result columns
  const { originalColumns, resultColumns } = useMemo(() => {
    const resultKeywords = ['prediction', 'predicted', 'classification', 'confidence', 'probability', 'label'];
    
    const original: string[] = [];
    const result: string[] = [];
    
    columns.forEach(col => {
      const colLower = col.toLowerCase();
      const isResultColumn = resultKeywords.some(keyword => colLower.includes(keyword));
      
      if (isResultColumn) {
        result.push(col);
      } else {
        original.push(col);
      }
    });
    
    return { originalColumns: original, resultColumns: result };
  }, [columns]);

  // Filter data based on search term
  const filteredData = useMemo(() => {
    if (!Array.isArray(data)) return [];
    if (!searchTerm.trim()) return data;

    const searchLower = searchTerm.toLowerCase();
    return data.filter(row =>
      Object.values(row).some(value =>
        String(value).toLowerCase().includes(searchLower)
      )
    );
  }, [data, searchTerm]);

  // Paginate filtered data
  const paginatedData = useMemo(() => {
    const startIndex = (currentPage - 1) * ROWS_PER_PAGE;
    const endIndex = startIndex + ROWS_PER_PAGE;
    return filteredData.slice(startIndex, endIndex);
  }, [filteredData, currentPage]);

  const totalPages = Math.ceil(filteredData.length / ROWS_PER_PAGE);

  // Helper function to format cell values
  const formatCellValue = (value: any): string => {
    if (value === null || value === undefined) {
      return "";
    }
    if (typeof value === "object") {
      return JSON.stringify(value);
    }
    if (typeof value === "string" && value.length > 100) {
      return value.substring(0, 100) + "...";
    }
    return String(value);
  };

  // Helper function to determine if a column contains classification results
  const isResultColumn = (columnName: string): boolean => {
    return resultColumns.includes(columnName);
  };

  if (!data || data.length === 0) {
    return (
      <Card className={className}>
        <CardContent className="p-8 text-center">
          <div className="w-16 h-16 rounded-full bg-muted flex items-center justify-center mx-auto mb-4">
            <Database className="w-8 h-8 text-muted-foreground" />
          </div>
          <h3 className="text-lg font-semibold mb-2">No Results Data</h3>
          <p className="text-muted-foreground">
            Classification results will appear here once processing is complete.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 rounded-lg bg-muted flex items-center justify-center">
              <Database className="w-5 h-5 text-foreground" />
            </div>
            <div>
              <CardTitle>Classification Results</CardTitle>
              <CardDescription>
                {filename ? `Results from ${filename}` : "Classified data with predictions"}
              </CardDescription>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Badge variant="outline">
              {filteredData.length.toLocaleString()} rows
            </Badge>
            <Badge variant="outline">
              {columns.length} columns
            </Badge>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Search and Export Controls */}
        <div className="flex items-center justify-between gap-4">
          <div className="flex items-center gap-2 flex-1 max-w-md">
            <Search className="w-4 h-4 text-muted-foreground" />
            <Input
              placeholder="Search results..."
              value={searchTerm}
              onChange={(e) => {
                setSearchTerm(e.target.value);
                setCurrentPage(1); // Reset to first page when searching
              }}
              className="flex-1"
            />
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" onClick={onExportCSV}>
              <Download className="w-4 h-4 mr-2" />
              Export CSV
            </Button>
            <Button variant="outline" size="sm" onClick={onExportExcel}>
              <FileSpreadsheet className="w-4 h-4 mr-2" />
              Export Excel
            </Button>
          </div>
        </div>

        {/* Data Table */}
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                {originalColumns.map((column) => (
                  <TableHead key={column} className="font-semibold">
                    {column}
                  </TableHead>
                ))}
                {resultColumns.length > 0 && (
                  <>
                    {resultColumns.map((column) => (
                      <TableHead key={column} className="font-semibold bg-muted/30">
                        <div className="flex items-center gap-1">
                          {column}
                          <Badge variant="secondary" className="text-xs">
                            Result
                          </Badge>
                        </div>
                      </TableHead>
                    ))}
                  </>
                )}
              </TableRow>
            </TableHeader>
            <TableBody>
              {paginatedData.map((row, rowIndex) => (
                <TableRow key={rowIndex}>
                  {originalColumns.map((column) => (
                    <TableCell key={column} className="max-w-xs">
                      <div className="truncate" title={String(row[column] || "")}>
                        {formatCellValue(row[column])}
                      </div>
                    </TableCell>
                  ))}
                  {resultColumns.map((column) => (
                    <TableCell key={column} className="max-w-xs bg-muted/30">
                      <div className="truncate font-medium" title={String(row[column] || "")}>
                        {formatCellValue(row[column])}
                      </div>
                    </TableCell>
                  ))}
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="flex items-center justify-between">
            <div className="text-sm text-muted-foreground">
              Showing {((currentPage - 1) * ROWS_PER_PAGE) + 1} to {Math.min(currentPage * ROWS_PER_PAGE, filteredData.length)} of {filteredData.length.toLocaleString()} results
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                disabled={currentPage === 1}
              >
                <ChevronLeft className="w-4 h-4" />
                Previous
              </Button>
              <div className="flex items-center gap-1">
                <span className="text-sm">
                  Page {currentPage} of {totalPages}
                </span>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                disabled={currentPage === totalPages}
              >
                Next
                <ChevronRight className="w-4 h-4" />
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
