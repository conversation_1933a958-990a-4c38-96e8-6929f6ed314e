@import "tailwindcss";

/* Light theme - Blue, Black, White color scheme */
:root {
  --color-background: oklch(100% 0 0);
  --color-foreground: oklch(10% 0 0);

  --color-card: oklch(100% 0 0);
  --color-card-foreground: oklch(10% 0 0);

  --color-popover: oklch(100% 0 0);
  --color-popover-foreground: oklch(10% 0 0);

  --color-primary: oklch(55% 0.25 250);
  --color-primary-foreground: oklch(100% 0 0);

  /* Light neutrals slightly darkened for contrast on white backgrounds */
  --color-secondary: oklch(93% 0.01 250);
  --color-secondary-foreground: oklch(20% 0.02 250);

  --color-muted: oklch(93% 0.01 250);
  --color-muted-foreground: oklch(35% 0.02 250);

  --color-accent: oklch(94% 0.01 250);
  --color-accent-foreground: oklch(20% 0.02 250);

  --color-destructive: oklch(60% 0.84 0);
  --color-destructive-foreground: oklch(100% 0 0);

  /* Slightly darker borders/inputs to avoid white-on-white blending */
  --color-border: oklch(86% 0.02 250);
  --color-input: oklch(88% 0.01 250);
  --color-ring: oklch(55% 0.25 250);

  /* ML Platform Colors - Blue theme */
  --color-ml-primary: oklch(55% 0.25 250);
  --color-ml-primary-dark: oklch(45% 0.25 250);
  --color-ml-secondary: oklch(60% 0.2 220);
  --color-ml-secondary-dark: oklch(50% 0.2 220);
  --color-ml-accent: oklch(65% 0.15 280);
  --color-ml-success: oklch(50% 0.15 140);
  --color-ml-warning: oklch(60% 0.15 60);
  --color-ml-error: oklch(60% 0.2 20);

  --radius: 0.5rem;
}

/* Dark theme - Applied when system prefers dark */
@media (prefers-color-scheme: dark) {
  :root {
    /* Dark theme - Blue, Black, White color scheme */
    --color-background: oklch(8% 0.01 250);
    --color-foreground: oklch(95% 0.01 250);

    --color-card: oklch(12% 0.01 250);
    --color-card-foreground: oklch(95% 0.01 250);

    --color-popover: oklch(12% 0.01 250);
    --color-popover-foreground: oklch(95% 0.01 250);

    --color-primary: oklch(65% 0.25 250);
    --color-primary-foreground: oklch(100% 0 0);

    --color-secondary: oklch(18% 0.02 250);
    --color-secondary-foreground: oklch(95% 0.01 250);

    --color-muted: oklch(18% 0.02 250);
    --color-muted-foreground: oklch(75% 0.02 250);

    --color-accent: oklch(18% 0.02 250);
    --color-accent-foreground: oklch(95% 0.01 250);

    --color-destructive: oklch(60% 0.84 0);
    --color-destructive-foreground: oklch(100% 0 0);

    --color-border: oklch(22% 0.02 250);
    --color-input: oklch(15% 0.02 250);
    --color-ring: oklch(65% 0.25 250);
  }
}

/* Dark theme - Applied when .dark class is present */
.dark {
  --color-background: oklch(8% 0.01 250);
  --color-foreground: oklch(95% 0.01 250);

  --color-card: oklch(12% 0.01 250);
  --color-card-foreground: oklch(95% 0.01 250);

  --color-popover: oklch(12% 0.01 250);
  --color-popover-foreground: oklch(95% 0.01 250);

  --color-primary: oklch(65% 0.25 250);
  --color-primary-foreground: oklch(100% 0 0);

  --color-secondary: oklch(18% 0.02 250);
  --color-secondary-foreground: oklch(95% 0.01 250);

  --color-muted: oklch(18% 0.02 250);
  --color-muted-foreground: oklch(75% 0.02 250);

  --color-accent: oklch(18% 0.02 250);
  --color-accent-foreground: oklch(95% 0.01 250);

  --color-destructive: oklch(60% 0.84 0);
  --color-destructive-foreground: oklch(100% 0 0);

  --color-border: oklch(22% 0.02 250);
  --color-input: oklch(15% 0.02 250);
  --color-ring: oklch(65% 0.25 250);
}

  /* Dark theme gradient overrides */
  .bg-gradient-hero {
    background-color: oklch(8% 0.01 250) !important;
    color: oklch(95% 0.01 250) !important;
  }

  .bg-gradient-card {
    background-color: oklch(12% 0.01 250) !important;
    color: oklch(95% 0.01 250) !important;
  }

  /* Dark theme ML color overrides - lighter for dark backgrounds */
  .text-ml-primary {
    color: oklch(75% 0.25 250) !important;
  }

  .text-ml-secondary {
    color: oklch(75% 0.2 220) !important;
  }

  .text-ml-accent {
    color: oklch(80% 0.15 280) !important;
  }


/* Dark theme overrides for .dark class */
.dark {
  /* Dark theme gradient overrides */
  .bg-gradient-hero {
    background-color: oklch(8% 0.01 250) !important;
    color: oklch(95% 0.01 250) !important;
  }

  .bg-gradient-card {
    background-color: oklch(12% 0.01 250) !important;
    color: oklch(95% 0.01 250) !important;
  }

  /* Dark theme ML color overrides - lighter for dark backgrounds */
  .text-ml-primary {
    color: oklch(75% 0.25 250) !important;
  }

  .text-ml-secondary {
    color: oklch(75% 0.2 220) !important;
  }

  .text-ml-accent {
    color: oklch(80% 0.15 280) !important;
  }
}

/* Custom utility classes with blue theme */
@layer utilities {
  .bg-gradient-primary {
    background-color: oklch(55% 0.25 250);
    color: oklch(100% 0 0);
  }

  .bg-gradient-hero {
    background-color: oklch(100% 0 0);
    color: oklch(10% 0 0);
  }

  .bg-gradient-card {
    background-color: oklch(98% 0.01 250);
    color: oklch(10% 0 0);
  }

  .shadow-ml {
    box-shadow: 0 10px 40px -10px oklch(55% 0.25 250 / 0.3);
  }

  .shadow-card {
    box-shadow: 0 4px 20px -4px oklch(55% 0.25 250 / 0.15);
  }

  .shadow-glow {
    box-shadow: 0 0 40px oklch(55% 0.25 250 / 0.4);
  }

  /* ML Colors with blue theme */
  .text-ml-primary {
    color: oklch(40% 0.25 250);
  }

  .text-ml-secondary {
    color: oklch(45% 0.2 220);
  }

  .text-ml-accent {
    color: oklch(50% 0.15 280);
  }

  .text-ml-success {
    color: oklch(40% 0.15 140);
  }

  .text-ml-warning {
    color: oklch(50% 0.15 60);
  }

  .text-ml-error {
    color: oklch(50% 0.2 20);
  }

  .bg-ml-primary {
    background-color: oklch(55% 0.25 250);
    color: oklch(100% 0 0);
  }

  .bg-ml-secondary {
    background-color: oklch(60% 0.2 220);
    color: oklch(100% 0 0);
  }

  .bg-ml-primary\/10 {
    background-color: oklch(55% 0.25 250 / 0.12);
  }

  .bg-ml-secondary\/10 {
    background-color: oklch(60% 0.2 220 / 0.12);
  }

  .bg-ml-success\/10 {
    background-color: oklch(50% 0.15 140 / 0.12);
  }

  .hover\:bg-ml-primary-dark:hover {
    background-color: oklch(45% 0.25 250);
  }

  .hover\:bg-ml-secondary-dark:hover {
    background-color: oklch(50% 0.2 220);
  }

  /* Border utilities for better definition */
  .border-ml-primary\/20 {
    border-color: oklch(55% 0.25 250 / 0.2);
  }

  .border-ml-secondary\/20 {
    border-color: oklch(60% 0.2 220 / 0.2);
  }

  /* Enhanced button styles */
  .btn-ml-primary {
    background-color: oklch(55% 0.25 250);
    color: oklch(100% 0 0);
    border: 1px solid oklch(55% 0.25 250);
  }

  .btn-ml-primary:hover {
    background-color: oklch(45% 0.25 250);
    border-color: oklch(45% 0.25 250);
  }

  .btn-ml-secondary {
    background-color: oklch(60% 0.2 220);
    color: oklch(100% 0 0);
    border: 1px solid oklch(60% 0.2 220);
  }

  .btn-ml-secondary:hover {
    background-color: oklch(50% 0.2 220);
    border-color: oklch(50% 0.2 220);
  }

  /* Force visibility for critical elements */
  .force-visible {
    color: oklch(15% 0.026 285.75) !important;
    background-color: oklch(100% 0 0) !important;
    border-color: oklch(88% 0.13 220) !important;
  }

  /* Ensure all text is readable */
  h1, h2, h3, h4, h5, h6, p, span, div, a, button {
    color: inherit;
  }

  /* High contrast mode for accessibility */
  @media (prefers-contrast: high) {
    :root {
      --color-background: oklch(100% 0 0);
      --color-foreground: oklch(0% 0 0);
      --color-border: oklch(50% 0 0);
    }

    .text-ml-primary {
      color: oklch(30% 0.25 250) !important;
    }

    .text-ml-secondary {
      color: oklch(35% 0.2 220) !important;
    }

    .text-ml-accent {
      color: oklch(40% 0.15 280) !important;
    }
  }

  /* Additional contrast fixes for light backgrounds */
  .bg-muted\/30 .text-ml-primary,
  .bg-muted\/30 .text-ml-secondary,
  .bg-muted\/30 .text-ml-accent {
    font-weight: 600;
  }

  /* Fix for Select dropdown transparency issues */
  [data-radix-select-content] {
    background-color: var(--color-popover) !important;
    color: var(--color-popover-foreground) !important;
    border: 1px solid var(--color-border) !important;
  }

  /* Ensure Select items have proper background */
  [data-radix-select-item] {
    background-color: transparent;
    color: var(--color-popover-foreground);
  }

  [data-radix-select-item]:hover,
  [data-radix-select-item][data-highlighted] {
    background-color: var(--color-accent) !important;
    color: var(--color-accent-foreground) !important;
  }

  /* Force solid background for all dropdown menus */
  [role="listbox"],
  [data-radix-popper-content-wrapper] > div {
    background-color: var(--color-popover) !important;
    backdrop-filter: none !important;
  }

  /* Additional specific fixes for Select component */
  .bg-popover {
    background-color: var(--color-popover) !important;
  }

  .text-popover-foreground {
    color: var(--color-popover-foreground) !important;
  }

  /* Ensure all Select dropdowns have solid backgrounds */
  [data-radix-select-content],
  [data-radix-select-viewport] {
    background-color: white !important;
    color: black !important;
  }

  /* Dark mode overrides for Select */
  @media (prefers-color-scheme: dark) {
    [data-radix-select-content],
    [data-radix-select-viewport] {
      background-color: var(--color-popover) !important;
      color: var(--color-popover-foreground) !important;
    }
  }

  .dark [data-radix-select-content],
  .dark [data-radix-select-viewport] {
    background-color: var(--color-popover) !important;
    color: var(--color-popover-foreground) !important;
  }
}
