"""Enhanced Flat Classification Engine for ClassyWeb ML Platform.

This module implements flat classification (standard single-level classification
without hierarchy) with advanced features for large-scale multi-class scenarios,
performance optimization, and comprehensive metrics.
"""

import logging
import time
import asyncio
import pandas as pd
import numpy as np
from typing import List, Dict, Any, Optional, Tuple, Union, Callable
from sklearn.metrics import (
    accuracy_score, precision_score, recall_score, f1_score,
    classification_report, confusion_matrix, roc_auc_score,
    precision_recall_curve, roc_curve, average_precision_score
)
from sklearn.preprocessing import LabelEncoder, StandardScaler
from sklearn.model_selection import StratifiedKFold
from sklearn.utils.class_weight import compute_class_weight
import torch
from transformers import AutoTokenizer, AutoModelForSequenceClassification
import gc
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
from dataclasses import dataclass

from .base_engine import (
    BaseClassificationEngine,
    ClassificationType,
    TrainingMethod,
    ClassificationResult,
    TrainingConfig,
    TrainingResult
)

logger = logging.getLogger(__name__)


@dataclass
class FlatClassificationMetrics:
    """Comprehensive metrics for flat classification."""
    accuracy: float
    weighted_f1: float
    macro_f1: float
    micro_f1: float
    per_class_precision: Dict[str, float]
    per_class_recall: Dict[str, float]
    per_class_f1: Dict[str, float]
    confusion_matrix: np.ndarray
    classification_report: Dict[str, Any]
    class_distribution: Dict[str, int]
    prediction_confidence_stats: Dict[str, float]
    processing_time_ms: float
    memory_usage_mb: float


@dataclass
class LargeDatasetConfig:
    """Configuration for large dataset processing."""
    batch_size: int = 1000
    chunk_size: int = 10000
    use_multiprocessing: bool = True
    max_workers: int = 4
    memory_limit_mb: float = 8192
    enable_streaming: bool = True
    cache_predictions: bool = False


class EnhancedFlatClassificationEngine(BaseClassificationEngine):
    """Enhanced flat classification engine with advanced features for large-scale scenarios."""

    def __init__(self, classification_type: ClassificationType, **kwargs):
        """Initialize enhanced flat classification engine."""
        super().__init__(classification_type)
        self.label_encoding = kwargs.get('label_encoding', 'label')  # 'label', 'ordinal', 'target'
        self.class_weight = kwargs.get('class_weight', 'balanced')
        self.num_classes = 0
        self.class_names = []
        self.label_encoder = LabelEncoder()
        self.scaler = StandardScaler()

        # Large dataset configuration
        self.large_dataset_config = LargeDatasetConfig(**kwargs.get('large_dataset_config', {}))

        # Performance optimization
        self.enable_gpu_acceleration = kwargs.get('enable_gpu_acceleration', True)
        self.mixed_precision = kwargs.get('mixed_precision', True)
        self.gradient_checkpointing = kwargs.get('gradient_checkpointing', True)

        # Advanced features
        self.enable_ensemble = kwargs.get('enable_ensemble', False)
        self.ensemble_models = []
        self.confidence_threshold = kwargs.get('confidence_threshold', 0.5)

        # Caching and optimization
        self.prediction_cache = {}
        self.feature_cache = {}
        self.enable_caching = kwargs.get('enable_caching', True)

        # Monitoring
        self.performance_metrics = []
        self.memory_usage_history = []

    @property
    def supported_training_methods(self) -> List[TrainingMethod]:
        """Return supported training methods."""
        return [TrainingMethod.CUSTOM, TrainingMethod.LLM]

    @property
    def default_metrics(self) -> List[str]:
        """Return default metrics for flat classification."""
        return [
            'accuracy', 'weighted_f1', 'macro_f1', 'micro_f1',
            'per_class_precision', 'per_class_recall', 'per_class_f1',
            'confusion_matrix', 'classification_report', 'class_distribution'
        ]
    
    def validate_config(self, config: TrainingConfig) -> Tuple[bool, List[str]]:
        """Validate configuration for flat classification."""
        errors = []

        # Validate basic configuration
        if not config.texts or len(config.texts) == 0:
            errors.append("Training texts cannot be empty")

        if not config.labels or len(config.labels) == 0:
            errors.append("Training labels cannot be empty")

        if len(config.texts) != len(config.labels):
            errors.append("Number of texts must match number of labels")

        # Validate large dataset configuration
        if len(config.texts) > 100000:  # Large dataset
            if self.large_dataset_config.batch_size > 10000:
                errors.append("Batch size too large for dataset size")

            if not self.large_dataset_config.use_multiprocessing:
                errors.append("Multiprocessing recommended for large datasets")

        # Validate class distribution
        unique_labels = set(config.labels)
        if len(unique_labels) < 2:
            errors.append("At least 2 classes required for classification")

        if len(unique_labels) > 1000:
            errors.append("Too many classes (>1000) may cause performance issues")

        # Check for class imbalance
        label_counts = pd.Series(config.labels).value_counts()
        min_count = label_counts.min()
        max_count = label_counts.max()

        if max_count / min_count > 100:  # Severe imbalance
            logger.warning(f"Severe class imbalance detected: {max_count}/{min_count} ratio")

        return len(errors) == 0, errors

    def optimize_for_large_dataset(self, dataset_size: int) -> LargeDatasetConfig:
        """Optimize configuration for large datasets."""
        config = LargeDatasetConfig()

        if dataset_size > 1000000:  # 1M+ samples
            config.batch_size = 500
            config.chunk_size = 5000
            config.max_workers = min(8, self.large_dataset_config.max_workers)
            config.enable_streaming = True
            config.cache_predictions = False

        elif dataset_size > 100000:  # 100K+ samples
            config.batch_size = 1000
            config.chunk_size = 10000
            config.max_workers = min(6, self.large_dataset_config.max_workers)
            config.enable_streaming = True
            config.cache_predictions = True

        elif dataset_size > 10000:  # 10K+ samples
            config.batch_size = 2000
            config.chunk_size = 20000
            config.max_workers = min(4, self.large_dataset_config.max_workers)
            config.enable_streaming = False
            config.cache_predictions = True

        else:  # Small datasets
            config.batch_size = min(dataset_size, 5000)
            config.chunk_size = dataset_size
            config.max_workers = 2
            config.enable_streaming = False
            config.cache_predictions = True

        return config

    async def train_custom_model_advanced(
        self,
        config: TrainingConfig,
        progress_callback: Optional[Callable] = None,
        early_stopping_patience: int = 3
    ) -> TrainingResult:
        """Advanced custom model training with performance optimization."""
        start_time = time.time()

        try:
            # Validate configuration
            is_valid, errors = self.validate_config(config)
            if not is_valid:
                return TrainingResult(
                    success=False,
                    model_path="",
                    metrics={},
                    training_time=0,
                    error_message=f"Configuration validation failed: {'; '.join(errors)}"
                )

            # Optimize for dataset size
            dataset_size = len(config.texts)
            optimized_config = self.optimize_for_large_dataset(dataset_size)
            self.large_dataset_config = optimized_config

            logger.info(f"Training flat classification model on {dataset_size} samples")
            logger.info(f"Optimized config: batch_size={optimized_config.batch_size}, "
                       f"chunk_size={optimized_config.chunk_size}, "
                       f"workers={optimized_config.max_workers}")

            # Prepare data with optimization
            if progress_callback:
                await progress_callback({"stage": "data_preparation", "progress": 0.1})

            prepared_data = await self._prepare_data_optimized(config.texts, config.labels)

            if progress_callback:
                await progress_callback({"stage": "data_preparation", "progress": 0.2})

            # Train model with monitoring
            if progress_callback:
                await progress_callback({"stage": "model_training", "progress": 0.3})

            training_result = await self._train_with_monitoring(
                prepared_data,
                config,
                progress_callback,
                early_stopping_patience
            )

            if progress_callback:
                await progress_callback({"stage": "model_training", "progress": 0.9})

            # Calculate comprehensive metrics
            metrics = await self._calculate_comprehensive_metrics(
                prepared_data['X_test'],
                prepared_data['y_test']
            )

            training_time = time.time() - start_time

            if progress_callback:
                await progress_callback({"stage": "completed", "progress": 1.0})

            return TrainingResult(
                success=True,
                model_path=training_result.get('model_path', ''),
                metrics=metrics,
                training_time=training_time,
                additional_info={
                    'dataset_size': dataset_size,
                    'optimization_config': optimized_config.__dict__,
                    'memory_usage_peak_mb': max(self.memory_usage_history) if self.memory_usage_history else 0,
                    'gpu_utilization': training_result.get('gpu_utilization', {}),
                    'early_stopping_epoch': training_result.get('early_stopping_epoch', -1)
                }
            )

        except Exception as e:
            logger.error(f"Error in advanced training: {e}")
            return TrainingResult(
                success=False,
                model_path="",
                metrics={},
                training_time=time.time() - start_time,
                error_message=str(e)
            )

    async def _prepare_data_optimized(self, texts: List[str], labels: List[str]) -> Dict[str, Any]:
        """Prepare data with memory optimization for large datasets."""
        import psutil

        # Monitor memory usage
        process = psutil.Process()
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        self.memory_usage_history.append(initial_memory)

        # Encode labels
        self.label_encoder.fit(labels)
        encoded_labels = self.label_encoder.transform(labels)
        self.class_names = self.label_encoder.classes_.tolist()
        self.num_classes = len(self.class_names)

        # Split data efficiently for large datasets
        from sklearn.model_selection import train_test_split

        if len(texts) > self.large_dataset_config.chunk_size:
            # Use stratified sampling for large datasets
            X_train, X_test, y_train, y_test = train_test_split(
                texts, encoded_labels,
                test_size=0.2,
                stratify=encoded_labels,
                random_state=42
            )
        else:
            # Standard split for smaller datasets
            X_train, X_test, y_train, y_test = train_test_split(
                texts, encoded_labels,
                test_size=0.2,
                random_state=42
            )

        # Calculate class weights for imbalanced datasets
        if self.class_weight == 'balanced':
            class_weights = compute_class_weight(
                'balanced',
                classes=np.unique(encoded_labels),
                y=encoded_labels
            )
            class_weight_dict = dict(zip(np.unique(encoded_labels), class_weights))
        else:
            class_weight_dict = None

        # Monitor memory after preparation
        current_memory = process.memory_info().rss / 1024 / 1024  # MB
        self.memory_usage_history.append(current_memory)

        logger.info(f"Data preparation completed. Memory usage: {current_memory:.1f}MB "
                   f"(+{current_memory - initial_memory:.1f}MB)")

        return {
            'X_train': X_train,
            'X_test': X_test,
            'y_train': y_train,
            'y_test': y_test,
            'class_weights': class_weight_dict,
            'num_classes': self.num_classes,
            'class_names': self.class_names
        }

    async def _train_with_monitoring(
        self,
        prepared_data: Dict[str, Any],
        config: TrainingConfig,
        progress_callback: Optional[Callable] = None,
        early_stopping_patience: int = 3
    ) -> Dict[str, Any]:
        """Train model with comprehensive monitoring."""
        import psutil
        from transformers import (
            AutoTokenizer, AutoModelForSequenceClassification,
            TrainingArguments, Trainer, EarlyStoppingCallback
        )
        from torch.utils.data import Dataset

        # Initialize monitoring
        process = psutil.Process()
        training_start_time = time.time()

        # Load tokenizer and model
        model_name = config.model_config.get('model_name', 'distilbert-base-uncased')
        tokenizer = AutoTokenizer.from_pretrained(model_name)
        model = AutoModelForSequenceClassification.from_pretrained(
            model_name,
            num_labels=prepared_data['num_classes']
        )

        # Enable optimizations
        if self.enable_gpu_acceleration and torch.cuda.is_available():
            model = model.cuda()
            if self.mixed_precision:
                model = model.half()

        # Create dataset
        class TextDataset(Dataset):
            def __init__(self, texts, labels, tokenizer, max_length=512):
                self.texts = texts
                self.labels = labels
                self.tokenizer = tokenizer
                self.max_length = max_length

            def __len__(self):
                return len(self.texts)

            def __getitem__(self, idx):
                text = str(self.texts[idx])
                encoding = self.tokenizer(
                    text,
                    truncation=True,
                    padding='max_length',
                    max_length=self.max_length,
                    return_tensors='pt'
                )

                return {
                    'input_ids': encoding['input_ids'].flatten(),
                    'attention_mask': encoding['attention_mask'].flatten(),
                    'labels': torch.tensor(self.labels[idx], dtype=torch.long)
                }

        # Create datasets
        train_dataset = TextDataset(
            prepared_data['X_train'],
            prepared_data['y_train'],
            tokenizer
        )

        eval_dataset = TextDataset(
            prepared_data['X_test'],
            prepared_data['y_test'],
            tokenizer
        )

        # Training arguments with optimization
        training_args = TrainingArguments(
            output_dir='./results',
            num_train_epochs=config.model_config.get('epochs', 3),
            per_device_train_batch_size=min(
                config.model_config.get('batch_size', 16),
                self.large_dataset_config.batch_size
            ),
            per_device_eval_batch_size=min(
                config.model_config.get('batch_size', 16),
                self.large_dataset_config.batch_size
            ),
            warmup_steps=500,
            weight_decay=0.01,
            logging_dir='./logs',
            logging_steps=10,
            evaluation_strategy="steps",
            eval_steps=100,
            save_strategy="steps",
            save_steps=500,
            load_best_model_at_end=True,
            metric_for_best_model="eval_loss",
            greater_is_better=False,
            fp16=self.mixed_precision and torch.cuda.is_available(),
            gradient_checkpointing=self.gradient_checkpointing,
            dataloader_num_workers=min(4, self.large_dataset_config.max_workers),
            remove_unused_columns=False
        )

        # Custom trainer with monitoring
        class MonitoringTrainer(Trainer):
            def __init__(self, *args, progress_callback=None, memory_tracker=None, **kwargs):
                super().__init__(*args, **kwargs)
                self.progress_callback = progress_callback
                self.memory_tracker = memory_tracker
                self.training_metrics = []

            def log(self, logs):
                super().log(logs)

                # Track memory usage
                if self.memory_tracker:
                    current_memory = psutil.Process().memory_info().rss / 1024 / 1024
                    self.memory_tracker.append(current_memory)

                # Report progress
                if self.progress_callback and 'epoch' in logs:
                    asyncio.create_task(self.progress_callback({
                        "stage": "training",
                        "epoch": logs.get('epoch', 0),
                        "loss": logs.get('train_loss', 0),
                        "eval_loss": logs.get('eval_loss', 0),
                        "memory_mb": current_memory if self.memory_tracker else 0
                    }))

                self.training_metrics.append(logs)

        # Initialize trainer
        trainer = MonitoringTrainer(
            model=model,
            args=training_args,
            train_dataset=train_dataset,
            eval_dataset=eval_dataset,
            callbacks=[EarlyStoppingCallback(early_stopping_patience=early_stopping_patience)],
            progress_callback=progress_callback,
            memory_tracker=self.memory_usage_history
        )

        # Train model
        training_result = trainer.train()

        # Save model
        model_path = f"./models/flat_classification_{int(time.time())}"
        trainer.save_model(model_path)

        # Calculate training time and final metrics
        training_time = time.time() - training_start_time
        final_memory = process.memory_info().rss / 1024 / 1024

        return {
            'model_path': model_path,
            'training_time': training_time,
            'final_memory_mb': final_memory,
            'training_metrics': trainer.training_metrics,
            'early_stopping_epoch': getattr(trainer.state, 'early_stopping_epoch', -1),
            'gpu_utilization': self._get_gpu_utilization() if torch.cuda.is_available() else {}
        }

    async def _calculate_comprehensive_metrics(
        self,
        X_test: List[str],
        y_test: np.ndarray
    ) -> Dict[str, Any]:
        """Calculate comprehensive metrics for flat classification."""
        import psutil

        start_time = time.time()
        initial_memory = psutil.Process().memory_info().rss / 1024 / 1024

        # Make predictions
        predictions = await self._predict_batch_optimized(X_test)
        y_pred = np.array([pred['predicted_class_index'] for pred in predictions])
        y_pred_proba = np.array([pred['confidence'] for pred in predictions])

        # Basic metrics
        accuracy = accuracy_score(y_test, y_pred)
        weighted_f1 = f1_score(y_test, y_pred, average='weighted')
        macro_f1 = f1_score(y_test, y_pred, average='macro')
        micro_f1 = f1_score(y_test, y_pred, average='micro')

        # Per-class metrics
        per_class_precision = precision_score(y_test, y_pred, average=None)
        per_class_recall = recall_score(y_test, y_pred, average=None)
        per_class_f1 = f1_score(y_test, y_pred, average=None)

        # Create per-class dictionaries
        per_class_precision_dict = {
            self.class_names[i]: float(per_class_precision[i])
            for i in range(len(self.class_names))
        }
        per_class_recall_dict = {
            self.class_names[i]: float(per_class_recall[i])
            for i in range(len(self.class_names))
        }
        per_class_f1_dict = {
            self.class_names[i]: float(per_class_f1[i])
            for i in range(len(self.class_names))
        }

        # Confusion matrix
        cm = confusion_matrix(y_test, y_pred)

        # Classification report
        report = classification_report(y_test, y_pred, target_names=self.class_names, output_dict=True)

        # Class distribution
        unique, counts = np.unique(y_test, return_counts=True)
        class_distribution = {
            self.class_names[i]: int(counts[np.where(unique == i)[0][0]])
            for i in range(len(self.class_names)) if i in unique
        }

        # Prediction confidence statistics
        confidence_stats = {
            'mean_confidence': float(np.mean(y_pred_proba)),
            'std_confidence': float(np.std(y_pred_proba)),
            'min_confidence': float(np.min(y_pred_proba)),
            'max_confidence': float(np.max(y_pred_proba)),
            'median_confidence': float(np.median(y_pred_proba))
        }

        # Processing time and memory
        processing_time = (time.time() - start_time) * 1000  # ms
        final_memory = psutil.Process().memory_info().rss / 1024 / 1024
        memory_usage = final_memory - initial_memory

        return {
            'accuracy': float(accuracy),
            'weighted_f1': float(weighted_f1),
            'macro_f1': float(macro_f1),
            'micro_f1': float(micro_f1),
            'per_class_precision': per_class_precision_dict,
            'per_class_recall': per_class_recall_dict,
            'per_class_f1': per_class_f1_dict,
            'confusion_matrix': cm.tolist(),
            'classification_report': report,
            'class_distribution': class_distribution,
            'prediction_confidence_stats': confidence_stats,
            'processing_time_ms': processing_time,
            'memory_usage_mb': memory_usage
        }

    async def _predict_batch_optimized(self, texts: List[str]) -> List[Dict[str, Any]]:
        """Optimized batch prediction for large datasets."""
        if not self.model or not self.tokenizer:
            raise ValueError("Model not trained or loaded")

        results = []
        batch_size = self.large_dataset_config.batch_size

        # Process in batches to manage memory
        for i in range(0, len(texts), batch_size):
            batch_texts = texts[i:i + batch_size]

            # Check cache first
            if self.enable_caching:
                cached_results = []
                uncached_texts = []
                uncached_indices = []

                for j, text in enumerate(batch_texts):
                    text_hash = hash(text)
                    if text_hash in self.prediction_cache:
                        cached_results.append((i + j, self.prediction_cache[text_hash]))
                    else:
                        uncached_texts.append(text)
                        uncached_indices.append(i + j)

                # Process uncached texts
                if uncached_texts:
                    batch_predictions = await self._predict_batch_raw(uncached_texts)

                    # Cache results
                    for k, text in enumerate(uncached_texts):
                        text_hash = hash(text)
                        self.prediction_cache[text_hash] = batch_predictions[k]
                        cached_results.append((uncached_indices[k], batch_predictions[k]))

                # Sort by original index and add to results
                cached_results.sort(key=lambda x: x[0])
                results.extend([result[1] for result in cached_results])
            else:
                # Direct prediction without caching
                batch_predictions = await self._predict_batch_raw(batch_texts)
                results.extend(batch_predictions)

            # Memory cleanup
            if i % (batch_size * 10) == 0:  # Every 10 batches
                gc.collect()
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()

        return results

    async def _predict_batch_raw(self, texts: List[str]) -> List[Dict[str, Any]]:
        """Raw batch prediction without caching."""
        # Tokenize batch
        encodings = self.tokenizer(
            texts,
            truncation=True,
            padding=True,
            max_length=512,
            return_tensors="pt"
        )

        # Move to GPU if available
        if torch.cuda.is_available() and self.enable_gpu_acceleration:
            encodings = {k: v.cuda() for k, v in encodings.items()}

        # Make predictions
        with torch.no_grad():
            outputs = self.model(**encodings)
            predictions = torch.nn.functional.softmax(outputs.logits, dim=-1)

        # Process results
        results = []
        for i, text in enumerate(texts):
            probs = predictions[i].cpu().numpy()
            predicted_class = int(np.argmax(probs))
            confidence = float(probs[predicted_class])

            # Map back to original labels
            prediction_label = self.class_names[predicted_class]

            # Create probability dictionary
            prob_dict = {
                self.class_names[j]: float(probs[j])
                for j in range(len(self.class_names))
            }

            results.append({
                'text': text,
                'predicted_class': prediction_label,
                'predicted_class_index': predicted_class,
                'confidence': confidence,
                'probabilities': prob_dict
            })

        return results

    def _get_gpu_utilization(self) -> Dict[str, Any]:
        """Get GPU utilization statistics."""
        try:
            import pynvml
            pynvml.nvmlInit()

            gpu_count = pynvml.nvmlDeviceGetCount()
            gpu_info = {}

            for i in range(gpu_count):
                handle = pynvml.nvmlDeviceGetHandleByIndex(i)

                # Memory info
                memory_info = pynvml.nvmlDeviceGetMemoryInfo(handle)
                memory_used_mb = memory_info.used / 1024 / 1024
                memory_total_mb = memory_info.total / 1024 / 1024
                memory_utilization = (memory_info.used / memory_info.total) * 100

                # GPU utilization
                utilization = pynvml.nvmlDeviceGetUtilizationRates(handle)

                gpu_info[f'gpu_{i}'] = {
                    'memory_used_mb': memory_used_mb,
                    'memory_total_mb': memory_total_mb,
                    'memory_utilization_percent': memory_utilization,
                    'gpu_utilization_percent': utilization.gpu,
                    'memory_clock_mhz': pynvml.nvmlDeviceGetClockInfo(handle, pynvml.NVML_CLOCK_MEM),
                    'graphics_clock_mhz': pynvml.nvmlDeviceGetClockInfo(handle, pynvml.NVML_CLOCK_GRAPHICS)
                }

            return gpu_info

        except Exception as e:
            logger.warning(f"Could not get GPU utilization: {e}")
            return {'error': str(e)}


# Alias for backward compatibility
FlatClassificationEngine = EnhancedFlatClassificationEngine
