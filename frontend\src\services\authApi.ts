// frontend/src/services/authApi.ts
import apiClient from './apiClient';
import {
  UserLoginRequest,
  UserRegisterRequest,
  TokenResponse,
  UserResponse,
  PasswordResetRequest,
  PasswordResetConfirmRequest,
  EmailVerificationRequest,
  UserUpdateRequest,
  PasswordChangeRequest,
  GoogleAuthRequest,
  MessageResponse,
  UserActivityResponse,
  UserPreferencesUpdateRequest
} from '../types/auth';

// Register a new user
export const registerUser = async (userData: UserRegisterRequest): Promise<UserResponse> => {
  const response = await apiClient.post<UserResponse>('/auth/register', userData);
  return response.data;
};

// Login with email and password
export const loginUser = async (credentials: UserLoginRequest): Promise<TokenResponse> => {
  const response = await apiClient.post<TokenResponse>('/auth/login', credentials);
  return response.data;
};

// Get current user information
export const getCurrentUser = async (): Promise<UserResponse> => {
  const response = await apiClient.get<UserResponse>('/auth/me');
  return response.data;
};

// Update user profile
export const updateUserProfile = async (userData: UserUpdateRequest): Promise<UserResponse> => {
  const response = await apiClient.put<UserResponse>('/auth/me', userData);
  return response.data;
};

// Change password
export const changePassword = async (passwordData: PasswordChangeRequest): Promise<MessageResponse> => {
  const response = await apiClient.post<MessageResponse>('/auth/change-password', passwordData);
  return response.data;
};

// Request password reset
export const requestPasswordReset = async (email: PasswordResetRequest): Promise<MessageResponse> => {
  const response = await apiClient.post<MessageResponse>('/auth/reset-password', email);
  return response.data;
};

// Confirm password reset
export const confirmPasswordReset = async (resetData: PasswordResetConfirmRequest): Promise<MessageResponse> => {
  const response = await apiClient.post<MessageResponse>('/auth/reset-password-confirm', resetData);
  return response.data;
};

// Verify email
export const verifyEmail = async (verificationData: EmailVerificationRequest): Promise<MessageResponse> => {
  const response = await apiClient.post<MessageResponse>('/auth/verify-email', verificationData);
  return response.data;
};

// Google OAuth authentication
export const googleAuth = async (authData: GoogleAuthRequest): Promise<TokenResponse> => {
  const response = await apiClient.post<TokenResponse>('/auth/google', authData);
  return response.data;
};

// Get user activity
export const getUserActivity = async (): Promise<UserActivityResponse> => {
  const response = await apiClient.get<UserActivityResponse>('/auth/activity');
  return response.data;
};

// Update user preferences
export const updateUserPreferences = async (preferences: UserPreferencesUpdateRequest): Promise<UserResponse> => {
  const response = await apiClient.put<UserResponse>('/auth/preferences', preferences);
  return response.data;
};

// Logout user
export const logoutUser = async (allDevices: boolean = false): Promise<MessageResponse> => {
  const response = await apiClient.post<MessageResponse>('/auth/logout', { all_devices: allDevices });
  return response.data;
};

// Refresh access token
export const refreshToken = async (refreshToken: string): Promise<TokenResponse> => {
  const response = await apiClient.post<TokenResponse>('/auth/refresh', { refresh_token: refreshToken });
  return response.data;
};
