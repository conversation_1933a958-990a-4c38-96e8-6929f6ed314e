import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { 
  Upload, 
  Brain, 
  Lightbulb, 
  Settings, 
  Zap, 

  Database,
  Sliders,
  Code,
  Activity,
  ArrowRight,
  User,
  GraduationCap
} from "lucide-react";

const beginnerSteps = [
  {
    icon: Upload,
    title: "Upload Data",
    description: "Simply drag and drop your dataset",
    detail: "Support for CSV, JSON, and other formats"
  },
  {
    icon: Brain,
    title: "Smart Detection",
    description: "AI analyzes your data automatically",
    detail: "Intelligent pattern recognition"
  },
  {
    icon: Lightbulb,
    title: "Get Recommendations",
    description: "Platform suggests optimal classification type",
    detail: "ML-powered suggestions"
  },
  {
    icon: Zap,
    title: "One-Click Training",
    description: "Start training with optimized defaults",
    detail: "No configuration needed"
  }
];

const expertSteps = [
  {
    icon: Database,
    title: "Data Engineering",
    description: "Advanced preprocessing and feature engineering",
    detail: "Custom transformations"
  },
  {
    icon: Slide<PERSON>,
    title: "Hyperparameter Tuning",
    description: "Fine-tune model parameters for optimal performance",
    detail: "Grid search & Bayesian optimization"
  },
  {
    icon: Code,
    title: "Custom Models",
    description: "Implement custom architectures and training loops",
    detail: "Full programmatic control"
  },
  {
    icon: Activity,
    title: "Advanced Monitoring",
    description: "Detailed metrics, logging, and experiment tracking",
    detail: "MLOps integration"
  }
];

export const UserJourneys = () => {
  return (
    <section className="py-20 bg-background">
      <div className="max-w-7xl mx-auto px-6">
        <div className="text-center mb-16">
          <Badge variant="outline" className="mb-4">
            <User className="w-4 h-4 mr-2" />
            User Journeys
          </Badge>
          <h2 className="text-4xl md:text-5xl font-bold mb-6 text-foreground">
            Choose Your
            <span className="text-primary font-bold"> Learning Path</span>
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Whether you're just starting with ML or you're a seasoned expert, 
            ClassyWeb adapts to your skill level and workflow preferences.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          {/* Beginner Journey */}
          <Card className="relative overflow-hidden border-2 border-ml-secondary/30 bg-card">
            <div className="absolute top-0 right-0 w-32 h-32 bg-secondary/10 rounded-full -mr-16 -mt-16" />

            <CardHeader className="relative z-10">
              <div className="flex items-center gap-3 mb-4">
                <div className="w-12 h-12 rounded-full bg-secondary/10 flex items-center justify-center">
                  <GraduationCap className="w-6 h-6 text-secondary-foreground" />
                </div>
                <div>
                  <CardTitle className="text-2xl">Beginner Journey</CardTitle>
                  <CardDescription>Perfect for ML newcomers</CardDescription>
                </div>
              </div>
            </CardHeader>

            <CardContent className="space-y-6">
              {beginnerSteps.map((step, index) => {
                const Icon = step.icon;
                return (
                  <div key={index} className="flex items-start gap-4">
                    <div className="w-10 h-10 rounded-lg bg-secondary/10 flex items-center justify-center flex-shrink-0">
                      <Icon className="w-5 h-5 text-secondary-foreground" />
                    </div>
                    <div className="flex-1">
                      <h4 className="font-semibold mb-1">{step.title}</h4>
                      <p className="text-sm text-muted-foreground mb-1">{step.description}</p>
                      <p className="text-xs text-secondary-foreground font-medium">{step.detail}</p>
                    </div>
                  </div>
                );
              })}
              
              <div className="pt-4">
                <Button className="w-full bg-ml-secondary hover:bg-ml-secondary-dark" asChild>
                  <a href="/beginner">
                    Start Beginner Journey
                    <ArrowRight className="w-4 h-4 ml-2" />
                  </a>
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Expert Journey */}
          <Card className="relative overflow-hidden border-2 border-ml-primary/30 bg-card">
            <div className="absolute top-0 right-0 w-32 h-32 bg-primary/10 rounded-full -mr-16 -mt-16" />

            <CardHeader className="relative z-10">
              <div className="flex items-center gap-3 mb-4">
                <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center">
                  <Settings className="w-6 h-6 text-primary" />
                </div>
                <div>
                  <CardTitle className="text-2xl">Expert Journey</CardTitle>
                  <CardDescription>Advanced control and customization</CardDescription>
                </div>
              </div>
            </CardHeader>

            <CardContent className="space-y-6">
              {expertSteps.map((step, index) => {
                const Icon = step.icon;
                return (
                  <div key={index} className="flex items-start gap-4">
                    <div className="w-10 h-10 rounded-lg bg-primary/10 flex items-center justify-center flex-shrink-0">
                      <Icon className="w-5 h-5 text-primary" />
                    </div>
                    <div className="flex-1">
                      <h4 className="font-semibold mb-1">{step.title}</h4>
                      <p className="text-sm text-muted-foreground mb-1">{step.description}</p>
                      <p className="text-xs text-primary font-medium">{step.detail}</p>
                    </div>
                  </div>
                );
              })}
              
              <div className="pt-4">
                <Button className="w-full bg-ml-primary hover:bg-ml-primary-dark" asChild>
                  <a href="/expert">
                    Start Expert Journey
                    <ArrowRight className="w-4 h-4 ml-2" />
                  </a>
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  );
};
