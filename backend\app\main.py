"""
Main FastAPI application definition for the AI Text Classifier API.

This module sets up the FastAPI application instance, configures middleware,
includes API routers, and defines the application lifespan management.
"""

import logging
from contextlib import asynccontextmanager

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

# Import configuration variables (adjust path if needed)
from .config import (
    CORS_ORIGINS, CORS_ALLOW_CREDENTIALS, CORS_ALLOW_METHODS, CORS_ALLOW_HEADERS,
    UPLOAD_DIR, HF_MODELS_DIR, MODEL_ARTIFACTS_DIR # Add MODEL_ARTIFACTS_DIR
)

# Import database initialization and setup utilities
from .database import init_db, engine, Base
from . import setup_db # Use relative import as setup_db is now in the same directory

# Import API routers from the 'api' directory
from .api import files, tasks, auth, enterprise, plugins, workflows, classification_v2, websocket_endpoints, analysis, hierarchical_models, deployment, license

# Import and configure logging
from .logging_config import configure_logging
configure_logging()
logger = logging.getLogger(__name__)

# Import security middleware
from .security_middleware import SecurityMiddleware

# Import error handlers
from .error_handlers import (
    ClassyWebException, ValidationException, DatabaseException,
    classyweb_exception_handler, validation_exception_handler,
    database_exception_handler, http_exception_handler, general_exception_handler
)
from pydantic import ValidationError
from sqlalchemy.exc import SQLAlchemyError
from fastapi import HTTPException

# Import universal platform
from .universal_platform import UniversalClassificationPlatform

logger.info("Starting ClassyWeb ML Platform API with enhanced logging")

# Initialize universal platform
universal_platform = UniversalClassificationPlatform()

# --- FastAPI Lifespan ---

@asynccontextmanager
async def lifespan(app_instance: FastAPI): # Use app_instance if needed later
    """Manages application startup and shutdown events."""
    # Startup: Initialize the database connection and setup tables
    logger.info("Application startup: Initializing database...")
    try:
        init_db() # Ensure SessionLocal factory is ready
        logger.info("Database session factory initialized.")

        # Attempt structured setup first (from the original main.py logic)
        try:
            setup_db.setup_database()
            logger.info("Database setup via setup_db.py completed successfully.")
        except Exception as e_setup:
            logger.error(f"Error running setup_db.setup_database: {e_setup}", exc_info=True)
            logger.warning("Attempting direct table creation via SQLAlchemy metadata as fallback.")
            # Fallback: Create tables directly using SQLAlchemy metadata
            try:
                Base.metadata.create_all(bind=engine)
                logger.info("Direct table creation via SQLAlchemy metadata successful.")
            except Exception as e_direct_create:
                logger.error(f"Fallback table creation failed: {e_direct_create}", exc_info=True)
                # Decide if the application can proceed or should halt.
                # For now, we log a severe warning but allow startup to continue.
                logger.critical("DATABASE SETUP FAILED. Application might not function correctly.")

    except Exception as e_init:
        logger.error(f"Database initialization (init_db) failed: {e_init}", exc_info=True)
        logger.critical("CORE DATABASE INITIALIZATION FAILED. Application might not function correctly.")

    # Ensure necessary directories exist
    try:
        UPLOAD_DIR.mkdir(parents=True, exist_ok=True)
        HF_MODELS_DIR.mkdir(parents=True, exist_ok=True) # Ensure HF models dir exists
        MODEL_ARTIFACTS_DIR.mkdir(parents=True, exist_ok=True) # Ensure model artifacts dir exists
        logger.info(f"Upload directory ensured: {UPLOAD_DIR.resolve()}")
        logger.info(f"Saved HF models directory ensured: {HF_MODELS_DIR.resolve()}")
        logger.info(f"Model artifacts directory ensured: {MODEL_ARTIFACTS_DIR.resolve()}")
    except Exception as dir_e:
        logger.error(f"Failed to create necessary directories: {dir_e}", exc_info=True)
        # Depending on severity, might want to raise an error or exit

    # Initialize universal platform
    try:
        logger.info("Starting universal platform initialization...")
        await universal_platform.initialize()
        logger.info("Universal platform initialization complete")
    except Exception as e:
        logger.error(f"Error initializing universal platform: {str(e)}")
        # Don't fail startup, but log the error

    yield

    # Shutdown: Clean up resources if needed
    logger.info("Shutting down application")
    try:
        logger.info("Shutting down universal platform...")
        if universal_platform.plugin_manager:
            await universal_platform.plugin_manager.registry.cleanup_all_plugins()
        logger.info("Universal platform shutdown complete")
    except Exception as e:
        logger.error(f"Error shutting down universal platform: {str(e)}")

# Create FastAPI app instance
app = FastAPI(
    title="AI Text Classifier API (New Structure)",
    description="API backend for the AI Text Classifier application using the modular structure.",
    version="1.0.0", # Or update version as needed
    lifespan=lifespan
)

# Add security middleware (should be added before CORS)
app.add_middleware(SecurityMiddleware, rate_limit_requests=100, rate_limit_window=60)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=CORS_ORIGINS,
    allow_credentials=CORS_ALLOW_CREDENTIALS,
    allow_methods=CORS_ALLOW_METHODS,
    allow_headers=CORS_ALLOW_HEADERS,
)

# Include API routers
logger.info("Including API routers...")
app.include_router(auth.router) # Use the new auth router (prefix/tags defined within)
app.include_router(files.router) # Prefix is defined in files.py
app.include_router(tasks.tasks_router) # Includes /tasks/{task_id} and /tasks
app.include_router(tasks.results_router) # Includes /results/{task_id}/...
app.include_router(analysis.router) # Data analysis and workflow recommendations
app.include_router(enterprise.router) # Enterprise features
app.include_router(plugins.router) # Plugin management
app.include_router(workflows.router) # Workflow management
app.include_router(classification_v2.router) # Unified classification API with LLM endpoints
app.include_router(hierarchical_models.router) # Hierarchical model management
app.include_router(websocket_endpoints.router) # Real-time WebSocket monitoring
app.include_router(deployment.router) # Model deployment and export
app.include_router(license.router) # License management and validation
logger.info("API routers included.")

# Add exception handlers
app.add_exception_handler(ClassyWebException, classyweb_exception_handler)
app.add_exception_handler(ValidationError, validation_exception_handler)
app.add_exception_handler(SQLAlchemyError, database_exception_handler)
app.add_exception_handler(HTTPException, http_exception_handler)
app.add_exception_handler(Exception, general_exception_handler)
logger.info("Exception handlers configured.")

# --- Root & Health Check Endpoints ---

@app.get("/", tags=["Status"])
async def read_root():
    """Root endpoint providing basic API information."""
    logger.info("Root endpoint accessed.")
    return {"message": "AI Text Classifier API is running (New Structure)!"}

@app.get("/health", tags=["Status"])
async def health_check():
    """Health check endpoint to verify API status."""
    # Add more checks here if needed (e.g., database connectivity)
    logger.debug("Health check endpoint accessed.")
    return {"status": "healthy"}


