#!/usr/bin/env python3
"""
Test script to verify the hierarchical classification inference fixes.
This script tests the model loading and prediction processing fixes.
"""

import sys
import os
import logging
import asyncio
from pathlib import Path

# Add the backend directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_model_loading_fix():
    """Test the model loading fix for session ID lookup."""
    logger.info("Testing model loading fix...")
    
    try:
        from app.classification_engines.hierarchical_engine import HierarchicalEngine
        from app.classification_engines.base_engine import ClassificationType
        
        # Create engine
        engine = HierarchicalEngine(ClassificationType.HIERARCHICAL)
        
        # Test the _find_model_directory method with the actual session ID
        model_id = "fac0665b-2fa2-4735-ae32-649343be3ba3"
        model_dir = engine._find_model_directory(model_id)
        
        if model_dir:
            logger.info(f"✅ Model directory found: {model_dir}")
            
            # Verify the directory exists
            if os.path.exists(model_dir):
                logger.info(f"✅ Model directory exists on disk: {model_dir}")
                
                # List contents
                contents = os.listdir(model_dir)
                logger.info(f"📁 Model directory contents: {contents}")
                
                # Check for required files
                required_files = ['config.json', 'model.safetensors', 'tokenizer.json']
                missing_files = [f for f in required_files if f not in contents]
                
                if not missing_files:
                    logger.info("✅ All required model files are present")
                    return True
                else:
                    logger.warning(f"⚠️ Missing model files: {missing_files}")
                    return False
            else:
                logger.error(f"❌ Model directory does not exist: {model_dir}")
                return False
        else:
            logger.error(f"❌ Model directory not found for ID: {model_id}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Model loading test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_prediction_processing():
    """Test the prediction processing fix for hierarchical results."""
    logger.info("Testing prediction processing fix...")
    
    try:
        from app.classification_engines.hierarchical_engine import HierarchicalEngine
        from app.classification_engines.base_engine import ClassificationType, ClassificationResult
        
        # Create engine
        engine = HierarchicalEngine(ClassificationType.HIERARCHICAL)
        
        # Create mock hierarchical prediction results (like what the engine returns)
        mock_results = [
            ClassificationResult(
                text="Test text 1",
                predictions=["Business", "Finance", "Banking"],  # List of strings (hierarchy path)
                confidence=0.85,
                probabilities={"level_0": 0.9, "level_1": 0.8, "level_2": 0.85},
                processing_time=0.1,
                method_used="custom_model",
                reasoning="Hierarchical prediction",
                metadata={}
            ),
            ClassificationResult(
                text="Test text 2", 
                predictions=["Technology", "Software", "AI"],  # List of strings (hierarchy path)
                confidence=0.92,
                probabilities={"level_0": 0.95, "level_1": 0.9, "level_2": 0.92},
                processing_time=0.12,
                method_used="custom_model",
                reasoning="Hierarchical prediction",
                metadata={}
            )
        ]
        
        # Test the processing logic from classification_v2.py
        texts = ["Test text 1", "Test text 2"]
        results = []
        
        for i, text in enumerate(texts):
            if i < len(mock_results):
                result = mock_results[i]
                
                # This is the fixed logic from classification_v2.py
                if hasattr(result, 'predictions') and result.predictions:
                    # For hierarchical classification, predictions is a list of strings (hierarchy path)
                    if isinstance(result.predictions, list) and all(isinstance(p, str) for p in result.predictions):
                        hierarchy_path = result.predictions
                        predictions = [
                            {
                                "label": label,
                                "confidence": result.confidence / len(result.predictions) if len(result.predictions) > 0 else result.confidence
                            }
                            for label in hierarchy_path
                        ]
                    else:
                        # Standard classification result format with prediction objects
                        predictions = [
                            {
                                "label": pred.label if hasattr(pred, 'label') else str(pred),
                                "confidence": pred.confidence if hasattr(pred, 'confidence') else result.confidence
                            }
                            for pred in result.predictions
                        ]
                        hierarchy_path = [pred.label if hasattr(pred, 'label') else str(pred) for pred in result.predictions]
                else:
                    # Fallback
                    hierarchy_path = ["unknown"]
                    predictions = [{"label": "unknown", "confidence": 0.0}]
                
                results.append({
                    "text": text,
                    "predictions": predictions,
                    "hierarchy_path": hierarchy_path,
                    "confidence": predictions[0]["confidence"] if predictions else 0.0
                })
        
        # Verify the results
        logger.info("📊 Processed results:")
        for i, result in enumerate(results):
            logger.info(f"  Result {i+1}:")
            logger.info(f"    Text: {result['text']}")
            logger.info(f"    Hierarchy: {result['hierarchy_path']}")
            logger.info(f"    Predictions: {result['predictions']}")
            logger.info(f"    Confidence: {result['confidence']}")
            
            # Verify structure
            assert isinstance(result['predictions'], list), "Predictions should be a list"
            assert all('label' in pred and 'confidence' in pred for pred in result['predictions']), "Each prediction should have label and confidence"
            assert isinstance(result['hierarchy_path'], list), "Hierarchy path should be a list"
            assert all(isinstance(label, str) for label in result['hierarchy_path']), "Hierarchy path should contain strings"
        
        logger.info("✅ Prediction processing test passed!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Prediction processing test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_fallback_predictions():
    """Test the fallback prediction format."""
    logger.info("Testing fallback prediction format...")
    
    try:
        from app.classification_engines.hierarchical_engine import HierarchicalEngine
        from app.classification_engines.base_engine import ClassificationType
        
        # Create engine
        engine = HierarchicalEngine(ClassificationType.HIERARCHICAL)
        
        # Test the fallback prediction logic
        texts = ["This is about sports and games", "Business and finance topics"]
        
        # Simulate the fallback prediction logic from the engine
        fallback_predictions = []
        for text in texts:
            text_lower = text.lower()
            
            # Basic theme detection (from the engine code)
            if any(word in text_lower for word in ['sport', 'game', 'match', 'team', 'player']):
                theme = 'Sports'
                category = 'Recreation'
            elif any(word in text_lower for word in ['business', 'company', 'market', 'finance']):
                theme = 'Business'
                category = 'Economy'
            else:
                theme = 'General'
                category = 'Miscellaneous'
            
            fallback_predictions.append({
                "hierarchy_path": [theme, category],
                "confidence": 0.6,
                "probabilities": {
                    "level_0": 0.6,
                    "level_1": 0.6
                }
            })
        
        # Test processing these fallback predictions
        results = []
        for i, text in enumerate(texts):
            if i < len(fallback_predictions):
                result = fallback_predictions[i]
                
                # Handle direct hierarchical prediction format (from classification_v2.py)
                if isinstance(result, dict) and 'hierarchy_path' in result:
                    hierarchy_path = result['hierarchy_path']
                    predictions = [
                        {
                            "label": label,
                            "confidence": result.get('confidence', 0.5)
                        }
                        for label in hierarchy_path
                    ]
                else:
                    # Fallback
                    hierarchy_path = ["unknown"]
                    predictions = [{"label": "unknown", "confidence": 0.0}]
                
                results.append({
                    "text": text,
                    "predictions": predictions,
                    "hierarchy_path": hierarchy_path,
                    "confidence": predictions[0]["confidence"] if predictions else 0.0
                })
        
        # Verify the results
        logger.info("📊 Fallback prediction results:")
        for i, result in enumerate(results):
            logger.info(f"  Result {i+1}:")
            logger.info(f"    Text: {result['text']}")
            logger.info(f"    Hierarchy: {result['hierarchy_path']}")
            logger.info(f"    Predictions: {result['predictions']}")
            
            # Verify structure
            assert isinstance(result['predictions'], list), "Predictions should be a list"
            assert all('label' in pred and 'confidence' in pred for pred in result['predictions']), "Each prediction should have label and confidence"
        
        logger.info("✅ Fallback prediction test passed!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Fallback prediction test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Run all tests."""
    logger.info("Starting hierarchical classification inference fix tests...")
    
    tests_passed = 0
    total_tests = 3
    
    # Test 1: Model loading fix
    if await test_model_loading_fix():
        tests_passed += 1
    
    # Test 2: Prediction processing fix
    if await test_prediction_processing():
        tests_passed += 1
    
    # Test 3: Fallback prediction format
    if await test_fallback_predictions():
        tests_passed += 1
    
    # Summary
    logger.info(f"\n{'='*50}")
    logger.info(f"Test Results: {tests_passed}/{total_tests} tests passed")
    
    if tests_passed == total_tests:
        logger.info("🎉 All tests passed! The hierarchical classification fixes are working.")
        return True
    else:
        logger.error(f"❌ {total_tests - tests_passed} tests failed. Please check the errors above.")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
