/**
 * MultiClassWorkflow.tsx
 *
 * Multi-class classification workflow page - streamlined unified implementation
 */

import { MultiClassWorkflow as MultiClassWorkflowComponent } from "@/components/classification/MultiClassWorkflow";

const MultiClassWorkflow = () => {
  return (
    <MultiClassWorkflowComponent
      onComplete={(results) => {
        console.log('Multi-class classification workflow completed:', results);
        // Handle completion - could navigate to results page or show success message
      }}
    />
  );
};

export default MultiClassWorkflow;
