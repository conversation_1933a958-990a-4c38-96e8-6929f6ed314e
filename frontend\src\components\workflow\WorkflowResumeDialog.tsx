/**
 * WorkflowResumeDialog.tsx
 * 
 * Dialog component for resuming incomplete workflows
 * Part of Phase 4 progress persistence implementation
 */

import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Play, 
  Trash2, 
  Clock, 
  FileText, 
  Settings, 
  AlertCircle,
  CheckCircle2,
  Download,
  Upload
} from 'lucide-react';
import { 
  workflowProgressManager, 
  WorkflowResumeInfo 
} from '@/services/workflowProgressManager';
import { workflowRouter } from '@/services/workflowRouter';
import { formatDistanceToNow } from 'date-fns';

interface WorkflowResumeDialogProps {
  trigger?: React.ReactNode;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  autoShow?: boolean; // Show automatically if resumable workflows exist
}

export const WorkflowResumeDialog: React.FC<WorkflowResumeDialogProps> = ({
  trigger,
  open,
  onOpenChange,
  autoShow = false
}) => {
  const [isOpen, setIsOpen] = useState(open || false);
  const [resumableWorkflows, setResumableWorkflows] = useState<WorkflowResumeInfo[]>([]);
  const [selectedWorkflow, setSelectedWorkflow] = useState<string | null>(null);
  const [isDeleting, setIsDeleting] = useState<string | null>(null);
  const navigate = useNavigate();

  useEffect(() => {
    loadResumableWorkflows();
  }, []);

  useEffect(() => {
    if (autoShow && resumableWorkflows.length > 0 && !isOpen) {
      setIsOpen(true);
    }
  }, [resumableWorkflows, autoShow, isOpen]);

  useEffect(() => {
    if (open !== undefined) {
      setIsOpen(open);
    }
  }, [open]);

  const loadResumableWorkflows = () => {
    const workflows = workflowProgressManager.getResumableWorkflows();
    setResumableWorkflows(workflows.filter(w => w.canResume));
  };

  const handleOpenChange = (newOpen: boolean) => {
    setIsOpen(newOpen);
    onOpenChange?.(newOpen);
  };

  const handleResumeWorkflow = async (workflowId: string) => {
    const state = workflowProgressManager.resumeWorkflow(workflowId);
    if (!state) {
      console.error('Failed to resume workflow');
      return;
    }

    // Navigate to appropriate workflow page
    const route = getWorkflowRoute(state.workflowType);
    if (route) {
      navigate(`${route}?resume=${workflowId}&step=${state.currentStep}`);
      handleOpenChange(false);
    }
  };

  const handleDeleteWorkflow = async (workflowId: string) => {
    setIsDeleting(workflowId);
    
    try {
      const success = workflowProgressManager.deleteWorkflow(workflowId);
      if (success) {
        setResumableWorkflows(prev => prev.filter(w => w.workflowId !== workflowId));
      }
    } catch (error) {
      console.error('Failed to delete workflow:', error);
    } finally {
      setIsDeleting(null);
    }
  };

  const handleExportWorkflow = (workflowId: string) => {
    const data = workflowProgressManager.exportWorkflow(workflowId);
    if (data) {
      const blob = new Blob([data], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `classyweb-workflow-${workflowId}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    }
  };

  const getWorkflowRoute = (workflowType: string): string | null => {
    const routes: Record<string, string> = {
      'binary': '/classification/binary',
      'multiclass': '/classification/multiclass',
      'multilabel': '/classification/multilabel',
      'hierarchical': '/classification/hierarchical',
      'flat': '/classification/flat',
      'beginner': '/beginner',
      'expert': '/expert'
    };
    return routes[workflowType] || null;
  };

  const getWorkflowIcon = (workflowType: string) => {
    switch (workflowType) {
      case 'binary':
      case 'multiclass':
      case 'multilabel':
      case 'hierarchical':
      case 'flat':
        return <Settings className="w-4 h-4" />;
      case 'beginner':
        return <Play className="w-4 h-4" />;
      case 'expert':
        return <Settings className="w-4 h-4" />;
      default:
        return <FileText className="w-4 h-4" />;
    }
  };

  const getStatusColor = (workflow: WorkflowResumeInfo) => {
    if (workflow.progressPercentage >= 75) return 'text-green-600 bg-green-50';
    if (workflow.progressPercentage >= 50) return 'text-blue-600 bg-blue-50';
    if (workflow.progressPercentage >= 25) return 'text-yellow-600 bg-yellow-50';
    return 'text-gray-600 bg-gray-50';
  };

  if (resumableWorkflows.length === 0) {
    return null;
  }

  const dialogContent = (
    <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
      <DialogHeader>
        <DialogTitle className="flex items-center gap-2">
          <Play className="w-5 h-5" />
          Resume Workflow
        </DialogTitle>
        <DialogDescription>
          You have {resumableWorkflows.length} incomplete workflow{resumableWorkflows.length !== 1 ? 's' : ''}. 
          Resume where you left off or start fresh.
        </DialogDescription>
      </DialogHeader>

      <div className="space-y-4">
        {resumableWorkflows.map((workflow) => (
          <Card 
            key={workflow.workflowId}
            className={`cursor-pointer transition-colors hover:bg-muted/50 ${
              selectedWorkflow === workflow.workflowId ? 'ring-2 ring-blue-500' : ''
            }`}
            onClick={() => setSelectedWorkflow(workflow.workflowId)}
          >
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  {getWorkflowIcon(workflow.workflowType)}
                  <div>
                    <CardTitle className="text-lg capitalize">
                      {workflow.workflowType} Classification
                    </CardTitle>
                    <p className="text-sm text-muted-foreground">
                      Last updated {formatDistanceToNow(new Date(workflow.lastUpdated), { addSuffix: true })}
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant="outline" className={getStatusColor(workflow)}>
                    Step {workflow.currentStep}
                  </Badge>
                  {workflow.estimatedTimeRemaining && (
                    <div className="flex items-center gap-1 text-sm text-muted-foreground">
                      <Clock className="w-3 h-3" />
                      {workflow.estimatedTimeRemaining}
                    </div>
                  )}
                </div>
              </div>
            </CardHeader>
            
            <CardContent>
              <div className="space-y-4">
                {/* Progress */}
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Progress</span>
                    <span>{Math.round(workflow.progressPercentage)}%</span>
                  </div>
                  <Progress value={workflow.progressPercentage} className="h-2" />
                </div>

                {/* Status indicators */}
                <div className="flex items-center gap-4 text-sm">
                  <div className="flex items-center gap-1">
                    {workflow.hasFiles ? (
                      <CheckCircle2 className="w-4 h-4 text-green-600" />
                    ) : (
                      <AlertCircle className="w-4 h-4 text-yellow-600" />
                    )}
                    <span>Files {workflow.hasFiles ? 'uploaded' : 'missing'}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    {workflow.hasConfiguration ? (
                      <CheckCircle2 className="w-4 h-4 text-green-600" />
                    ) : (
                      <AlertCircle className="w-4 h-4 text-yellow-600" />
                    )}
                    <span>Configuration {workflow.hasConfiguration ? 'complete' : 'incomplete'}</span>
                  </div>
                </div>

                {/* Actions */}
                <div className="flex justify-between items-center pt-2 border-t">
                  <div className="flex gap-2">
                    <Button
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleResumeWorkflow(workflow.workflowId);
                      }}
                    >
                      <Play className="w-4 h-4 mr-1" />
                      Resume
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleExportWorkflow(workflow.workflowId);
                      }}
                    >
                      <Download className="w-4 h-4 mr-1" />
                      Export
                    </Button>
                  </div>
                  
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleDeleteWorkflow(workflow.workflowId);
                    }}
                    disabled={isDeleting === workflow.workflowId}
                  >
                    <Trash2 className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}

        {/* Import workflow option */}
        <Card className="border-dashed">
          <CardContent className="p-6 text-center">
            <Upload className="w-8 h-8 mx-auto mb-2 text-muted-foreground" />
            <h3 className="font-medium mb-1">Import Workflow</h3>
            <p className="text-sm text-muted-foreground mb-3">
              Import a previously exported workflow file
            </p>
            <Button
              variant="outline"
              onClick={() => {
                const input = document.createElement('input');
                input.type = 'file';
                input.accept = '.json';
                input.onchange = (e) => {
                  const file = (e.target as HTMLInputElement).files?.[0];
                  if (file) {
                    const reader = new FileReader();
                    reader.onload = (e) => {
                      const data = e.target?.result as string;
                      const workflowId = workflowProgressManager.importWorkflow(data);
                      if (workflowId) {
                        loadResumableWorkflows();
                      }
                    };
                    reader.readAsText(file);
                  }
                };
                input.click();
              }}
            >
              <Upload className="w-4 h-4 mr-1" />
              Import File
            </Button>
          </CardContent>
        </Card>
      </div>

      <div className="flex justify-end gap-2 pt-4 border-t">
        <Button variant="outline" onClick={() => handleOpenChange(false)}>
          Start Fresh
        </Button>
      </div>
    </DialogContent>
  );

  if (trigger) {
    return (
      <Dialog open={isOpen} onOpenChange={handleOpenChange}>
        <DialogTrigger asChild>
          {trigger}
        </DialogTrigger>
        {dialogContent}
      </Dialog>
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleOpenChange}>
      {dialogContent}
    </Dialog>
  );
};
