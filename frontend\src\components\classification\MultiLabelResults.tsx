/**
 * MultiLabelResults.tsx
 * 
 * Comprehensive results visualization component for multi-label classification.
 * Includes label correlation heatmaps, per-label metrics, multi-label specific charts,
 * and detailed performance analysis.
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Progress } from "@/components/ui/progress";
import { useToast } from "@/hooks/use-toast";
import {
  BarChart3,
  Download,
  Share2,
  Target,
  Network,
  TrendingUp,
  Activity,
  Zap,
  Eye,
  FileText,
  CheckCircle2,
  AlertCircle,
  Info,
  Grid3X3,
  PieC<PERSON>,
  LineChart
} from "lucide-react";
import { ResultsDataTable } from "@/components/ResultsDataTable";
import { downloadResultsCSV, downloadResultsExcel } from "@/services/exportApi";

interface MultiLabelMetrics {
  // Overall metrics
  hamming_loss: number;
  jaccard_score: number;
  f1_macro: number;
  f1_micro: number;
  f1_weighted: number;
  subset_accuracy: number;
  label_ranking_loss: number;
  coverage_error: number;
  
  // Per-label metrics
  per_label_precision: Record<string, number>;
  per_label_recall: Record<string, number>;
  per_label_f1: Record<string, number>;
  per_label_support: Record<string, number>;
  
  // Label statistics
  label_frequencies: Record<string, number>;
  label_correlations: Record<string, Record<string, number>>;
  co_occurrence_matrix: number[][];
  
  // Threshold analysis
  optimal_thresholds: Record<string, number>;
  threshold_analysis: Record<string, {
    precision_curve: number[];
    recall_curve: number[];
    f1_curve: number[];
    thresholds: number[];
  }>;
}

interface MultiLabelResultsProps {
  results: any[];
  metrics: MultiLabelMetrics;
  labels: string[];
  modelName: string;
  trainingTime?: number;
  onExport?: (format: 'csv' | 'excel') => void;
  onShare?: () => void;
}

export const MultiLabelResults: React.FC<MultiLabelResultsProps> = ({
  results,
  metrics,
  labels,
  modelName,
  trainingTime,
  onExport,
  onShare
}) => {
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState('overview');
  const [selectedLabel, setSelectedLabel] = useState<string>(labels[0] || '');

  const handleExport = async (format: 'csv' | 'excel') => {
    try {
      if (onExport) {
        onExport(format);
      } else {
        // Default export functionality
        if (format === 'csv') {
          await downloadResultsCSV(results, `multi-label-results-${Date.now()}`);
        } else {
          await downloadResultsExcel(results, `multi-label-results-${Date.now()}`);
        }
      }
      
      toast({
        title: "Export successful",
        description: `Results exported as ${format.toUpperCase()}`
      });
    } catch (error) {
      toast({
        title: "Export failed",
        description: "Failed to export results",
        variant: "destructive"
      });
    }
  };

  const getMetricColor = (value: number, isLoss: boolean = false) => {
    if (isLoss) {
      // For loss metrics, lower is better
      if (value < 0.1) return 'text-green-600';
      if (value < 0.2) return 'text-yellow-600';
      return 'text-red-600';
    } else {
      // For performance metrics, higher is better
      if (value > 0.8) return 'text-green-600';
      if (value > 0.6) return 'text-yellow-600';
      return 'text-red-600';
    }
  };

  const getPerformanceLevel = (value: number, isLoss: boolean = false) => {
    if (isLoss) {
      if (value < 0.1) return 'Excellent';
      if (value < 0.2) return 'Good';
      if (value < 0.3) return 'Fair';
      return 'Poor';
    } else {
      if (value > 0.8) return 'Excellent';
      if (value > 0.6) return 'Good';
      if (value > 0.4) return 'Fair';
      return 'Poor';
    }
  };

  // Calculate label distribution for visualization
  const labelDistribution = labels.map(label => ({
    label,
    frequency: metrics.label_frequencies[label] || 0,
    precision: metrics.per_label_precision[label] || 0,
    recall: metrics.per_label_recall[label] || 0,
    f1: metrics.per_label_f1[label] || 0
  })).sort((a, b) => b.frequency - a.frequency);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Multi-Label Classification Results</h2>
          <p className="text-muted-foreground">
            Comprehensive analysis of {modelName} performance
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={() => handleExport('csv')}>
            <Download className="w-4 h-4 mr-2" />
            Export CSV
          </Button>
          <Button variant="outline" onClick={() => handleExport('excel')}>
            <Download className="w-4 h-4 mr-2" />
            Export Excel
          </Button>
          {onShare && (
            <Button variant="outline" onClick={onShare}>
              <Share2 className="w-4 h-4 mr-2" />
              Share
            </Button>
          )}
        </div>
      </div>

      {/* Results Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="metrics">Metrics</TabsTrigger>
          <TabsTrigger value="labels">Per-Label</TabsTrigger>
          <TabsTrigger value="correlations">Correlations</TabsTrigger>
          <TabsTrigger value="data">Data</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          {/* Key Metrics Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">F1 Macro</CardTitle>
              </CardHeader>
              <CardContent>
                <div className={`text-2xl font-bold ${getMetricColor(metrics.f1_macro)}`}>
                  {metrics.f1_macro.toFixed(3)}
                </div>
                <p className="text-xs text-muted-foreground">
                  {getPerformanceLevel(metrics.f1_macro)}
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">F1 Micro</CardTitle>
              </CardHeader>
              <CardContent>
                <div className={`text-2xl font-bold ${getMetricColor(metrics.f1_micro)}`}>
                  {metrics.f1_micro.toFixed(3)}
                </div>
                <p className="text-xs text-muted-foreground">
                  {getPerformanceLevel(metrics.f1_micro)}
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Jaccard Score</CardTitle>
              </CardHeader>
              <CardContent>
                <div className={`text-2xl font-bold ${getMetricColor(metrics.jaccard_score)}`}>
                  {metrics.jaccard_score.toFixed(3)}
                </div>
                <p className="text-xs text-muted-foreground">
                  {getPerformanceLevel(metrics.jaccard_score)}
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Hamming Loss</CardTitle>
              </CardHeader>
              <CardContent>
                <div className={`text-2xl font-bold ${getMetricColor(metrics.hamming_loss, true)}`}>
                  {metrics.hamming_loss.toFixed(3)}
                </div>
                <p className="text-xs text-muted-foreground">
                  {getPerformanceLevel(metrics.hamming_loss, true)}
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Performance Summary */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="w-5 h-5" />
                Performance Summary
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Subset Accuracy</span>
                    <span className="text-sm">{(metrics.subset_accuracy * 100).toFixed(1)}%</span>
                  </div>
                  <Progress value={metrics.subset_accuracy * 100} />
                  
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">F1 Weighted</span>
                    <span className="text-sm">{(metrics.f1_weighted * 100).toFixed(1)}%</span>
                  </div>
                  <Progress value={metrics.f1_weighted * 100} />
                </div>
                
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Label Ranking Loss</span>
                    <span className="text-sm">{metrics.label_ranking_loss.toFixed(3)}</span>
                  </div>
                  <Progress value={(1 - metrics.label_ranking_loss) * 100} />
                  
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Coverage Error</span>
                    <span className="text-sm">{metrics.coverage_error.toFixed(2)}</span>
                  </div>
                  <Progress value={Math.max(0, (10 - metrics.coverage_error) * 10)} />
                </div>
              </div>

              {trainingTime && (
                <Alert>
                  <Activity className="h-4 w-4" />
                  <AlertDescription>
                    <strong>Training completed in {Math.round(trainingTime / 60)} minutes</strong>
                    <br />
                    Model processed {results.length} samples across {labels.length} labels
                  </AlertDescription>
                </Alert>
              )}
            </CardContent>
          </Card>

          {/* Label Distribution */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <PieChart className="w-5 h-5" />
                Label Distribution
              </CardTitle>
              <CardDescription>
                Frequency and performance of each label
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3 max-h-60 overflow-y-auto">
                {labelDistribution.map(({ label, frequency, f1 }) => (
                  <div key={label} className="flex items-center justify-between p-2 border rounded">
                    <div className="flex items-center gap-3">
                      <Badge variant="outline">{label}</Badge>
                      <span className="text-sm text-muted-foreground">
                        {frequency} occurrences
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className={`text-sm font-medium ${getMetricColor(f1)}`}>
                        F1: {f1.toFixed(3)}
                      </span>
                      <div className="w-16">
                        <Progress value={f1 * 100} className="h-2" />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Detailed Metrics Tab */}
        <TabsContent value="metrics" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Multi-label Specific Metrics */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Target className="w-5 h-5" />
                  Multi-Label Metrics
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="font-medium">Hamming Loss</span>
                    <span className={getMetricColor(metrics.hamming_loss, true)}>
                      {metrics.hamming_loss.toFixed(4)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="font-medium">Jaccard Score</span>
                    <span className={getMetricColor(metrics.jaccard_score)}>
                      {metrics.jaccard_score.toFixed(4)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="font-medium">Subset Accuracy</span>
                    <span className={getMetricColor(metrics.subset_accuracy)}>
                      {metrics.subset_accuracy.toFixed(4)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="font-medium">Label Ranking Loss</span>
                    <span className={getMetricColor(metrics.label_ranking_loss, true)}>
                      {metrics.label_ranking_loss.toFixed(4)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="font-medium">Coverage Error</span>
                    <span className={getMetricColor(metrics.coverage_error, true)}>
                      {metrics.coverage_error.toFixed(4)}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* F1 Score Variants */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="w-5 h-5" />
                  F1 Score Analysis
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="font-medium">F1 Macro</span>
                    <span className={getMetricColor(metrics.f1_macro)}>
                      {metrics.f1_macro.toFixed(4)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="font-medium">F1 Micro</span>
                    <span className={getMetricColor(metrics.f1_micro)}>
                      {metrics.f1_micro.toFixed(4)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="font-medium">F1 Weighted</span>
                    <span className={getMetricColor(metrics.f1_weighted)}>
                      {metrics.f1_weighted.toFixed(4)}
                    </span>
                  </div>
                </div>

                <Alert>
                  <Info className="h-4 w-4" />
                  <AlertDescription className="text-xs">
                    <strong>Macro:</strong> Unweighted mean of per-label F1 scores<br/>
                    <strong>Micro:</strong> Global F1 calculated from total TP, FP, FN<br/>
                    <strong>Weighted:</strong> Weighted mean by label support
                  </AlertDescription>
                </Alert>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Per-Label Analysis Tab */}
        <TabsContent value="labels" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Grid3X3 className="w-5 h-5" />
                Per-Label Performance
              </CardTitle>
              <CardDescription>
                Detailed metrics for each individual label
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="w-full text-sm">
                  <thead>
                    <tr className="border-b">
                      <th className="text-left p-2">Label</th>
                      <th className="text-right p-2">Support</th>
                      <th className="text-right p-2">Precision</th>
                      <th className="text-right p-2">Recall</th>
                      <th className="text-right p-2">F1-Score</th>
                      <th className="text-right p-2">Threshold</th>
                    </tr>
                  </thead>
                  <tbody>
                    {labels.map(label => (
                      <tr key={label} className="border-b hover:bg-muted/30">
                        <td className="p-2">
                          <Badge variant="outline">{label}</Badge>
                        </td>
                        <td className="text-right p-2">
                          {metrics.per_label_support[label] || 0}
                        </td>
                        <td className="text-right p-2">
                          <span className={getMetricColor(metrics.per_label_precision[label] || 0)}>
                            {(metrics.per_label_precision[label] || 0).toFixed(3)}
                          </span>
                        </td>
                        <td className="text-right p-2">
                          <span className={getMetricColor(metrics.per_label_recall[label] || 0)}>
                            {(metrics.per_label_recall[label] || 0).toFixed(3)}
                          </span>
                        </td>
                        <td className="text-right p-2">
                          <span className={getMetricColor(metrics.per_label_f1[label] || 0)}>
                            {(metrics.per_label_f1[label] || 0).toFixed(3)}
                          </span>
                        </td>
                        <td className="text-right p-2">
                          {(metrics.optimal_thresholds[label] || 0.5).toFixed(2)}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Label Correlations Tab */}
        <TabsContent value="correlations" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Network className="w-5 h-5" />
                Label Correlations
              </CardTitle>
              <CardDescription>
                Analysis of label co-occurrence patterns and relationships
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Alert>
                <Info className="h-4 w-4" />
                <AlertDescription>
                  Label correlation heatmap and co-occurrence analysis will be implemented here.
                  This includes visualization of which labels frequently appear together.
                </AlertDescription>
              </Alert>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Raw Data Tab */}
        <TabsContent value="data" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="w-5 h-5" />
                Classification Results
              </CardTitle>
              <CardDescription>
                Raw classification results with predictions and confidence scores
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ResultsDataTable 
                data={results}
                onExport={handleExport}
              />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};
