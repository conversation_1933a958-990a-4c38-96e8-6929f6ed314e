"""
Test script for hierarchical classification functionality.
"""
import sys
import os
import logging
from pathlib import Path

# Add the parent directory to sys.path to import app modules
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from app.hf_classifier import _ensure_hierarchical_consistency
import numpy as np

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_hierarchical_consistency():
    """Test the hierarchical consistency function."""
    # Test data
    labels = {
        "Segment: Product Features",
        "Subsegment: Mobile App"
    }
    
    # Mock probabilities and reverse_label_map
    probabilities = np.array([0.8, 0.7, 0.6, 0.5, 0.4, 0.3, 0.2, 0.1])
    reverse_label_map = {
        0: "Theme: User Experience",
        1: "Theme: Technical Issues",
        2: "Category: Interface",
        3: "Category: Performance",
        4: "Segment: Product Features",
        5: "Segment: Navigation",
        6: "Subsegment: Mobile App",
        7: "Subsegment: Desktop"
    }
    
    # Define hierarchy levels
    hierarchy_levels = ["Theme", "Category", "Segment", "Subsegment"]
    
    # Test the function
    consistent_labels = _ensure_hierarchical_consistency(
        labels, probabilities, reverse_label_map, hierarchy_levels
    )
    
    # Print results
    print("Original labels:", labels)
    print("Consistent labels:", consistent_labels)
    
    # Verify that higher levels were added
    assert "Theme: User Experience" in consistent_labels, "Theme level was not added"
    assert "Category: Interface" in consistent_labels, "Category level was not added"
    assert "Segment: Product Features" in consistent_labels, "Segment level was preserved"
    assert "Subsegment: Mobile App" in consistent_labels, "Subsegment level was preserved"
    
    print("All tests passed!")

if __name__ == "__main__":
    test_hierarchical_consistency()
