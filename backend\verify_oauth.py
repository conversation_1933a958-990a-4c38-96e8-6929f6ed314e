"""
<PERSON><PERSON>t to verify Google OAuth configuration.
"""
import os
import sys
import logging
import requests
import urllib.parse
from datetime import datetime, timezone

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Try to load environment variables from .env file
try:
    from dotenv import load_dotenv
    load_dotenv()
    logger.info("Loaded environment variables from .env file")
except ImportError:
    logger.warning("python-dotenv not installed, skipping .env loading")

# Get OAuth configuration from environment variables
client_id = os.getenv("GOOGLE_CLIENT_ID", "")
client_secret = os.getenv("GOOGLE_CLIENT_SECRET", "")
redirect_uri = os.getenv("GOOGLE_REDIRECT_URI", "http://localhost:5173/auth/google/callback")

def mask_secret(secret):
    """Mask a secret for logging."""
    if not secret:
        return "Not set"
    return f"{secret[:5]}...{secret[-5:]}" if len(secret) > 10 else "***"

def verify_oauth_config():
    """Verify OAuth configuration."""
    logger.info("Verifying Google OAuth configuration...")
    
    # Check if client ID and secret are set
    if not client_id:
        logger.error("GOOGLE_CLIENT_ID is not set")
        return False
    
    if not client_secret:
        logger.error("GOOGLE_CLIENT_SECRET is not set")
        return False
    
    logger.info(f"Client ID: {client_id[:8]}...{client_id[-8:]}")
    logger.info(f"Client Secret: {mask_secret(client_secret)}")
    logger.info(f"Redirect URI: {redirect_uri}")
    
    # Test connectivity to Google OAuth endpoints
    try:
        response = requests.head("https://accounts.google.com", timeout=5)
        logger.info(f"Google Accounts connectivity: OK ({response.status_code})")
    except Exception as e:
        logger.error(f"Failed to connect to Google Accounts: {e}")
        return False
    
    try:
        response = requests.head("https://oauth2.googleapis.com/token", timeout=5)
        logger.info(f"OAuth endpoint connectivity: OK ({response.status_code})")
    except Exception as e:
        logger.error(f"Failed to connect to OAuth endpoint: {e}")
        return False
    
    # Test client credentials with a minimal request
    token_url = "https://oauth2.googleapis.com/token"
    test_data = {
        "client_id": client_id,
        "client_secret": client_secret,
        "grant_type": "client_credentials"  # This will fail, but we can check the error type
    }
    
    headers = {
        'Content-Type': 'application/x-www-form-urlencoded'
    }
    
    try:
        response = requests.post(token_url, data=urllib.parse.urlencode(test_data), headers=headers)
        logger.info(f"Test request status code: {response.status_code}")
        
        try:
            response_json = response.json()
            logger.info(f"Test request response: {response_json}")
            
            # Check for specific error types
            if response_json.get("error") == "unsupported_grant_type":
                logger.info("Got expected 'unsupported_grant_type' error - this is normal for this test")
                return True
            elif response_json.get("error") == "invalid_client":
                logger.error("Client credentials are invalid. Check your client ID and client secret.")
                return False
            else:
                logger.warning(f"Unexpected error type: {response_json.get('error')}")
                return False
        except Exception as e:
            logger.error(f"Failed to parse response JSON: {e}")
            logger.error(f"Response text: {response.text}")
            return False
    except Exception as e:
        logger.error(f"Failed to make test request: {e}")
        return False

def check_time_sync():
    """Check time synchronization with Google servers."""
    logger.info("Checking time synchronization...")
    
    # Get local time
    local_time = datetime.now()
    utc_time = datetime.now(timezone.utc)
    
    logger.info(f"Local time: {local_time}")
    logger.info(f"UTC time: {utc_time}")
    
    # Get Google's server time
    try:
        response = requests.head("https://www.google.com")
        if "date" in response.headers:
            server_time_str = response.headers["date"]
            from email.utils import parsedate_to_datetime
            server_time = parsedate_to_datetime(server_time_str)
            
            logger.info(f"Google server time: {server_time}")
            
            # Calculate time difference
            time_diff = (utc_time - server_time).total_seconds()
            logger.info(f"Time difference: {time_diff:.2f} seconds")
            
            if abs(time_diff) > 300:  # More than 5 minutes
                logger.warning("Time difference is significant and may cause OAuth issues")
                return False
            else:
                logger.info("Time synchronization looks good")
                return True
        else:
            logger.warning("Could not get date header from Google")
            return False
    except Exception as e:
        logger.error(f"Failed to check time synchronization: {e}")
        return False

if __name__ == "__main__":
    logger.info("=== Google OAuth Configuration Verification ===")
    
    # Verify OAuth configuration
    oauth_config_ok = verify_oauth_config()
    logger.info(f"OAuth configuration verification: {'PASSED' if oauth_config_ok else 'FAILED'}")
    
    # Check time synchronization
    time_sync_ok = check_time_sync()
    logger.info(f"Time synchronization check: {'PASSED' if time_sync_ok else 'FAILED'}")
    
    # Overall result
    if oauth_config_ok and time_sync_ok:
        logger.info("All checks PASSED")
        sys.exit(0)
    else:
        logger.error("Some checks FAILED")
        sys.exit(1)
