"""
Centralized error handling for ClassyWeb ML Platform.

This module provides:
1. Custom exception classes
2. Standardized error responses
3. Error logging utilities
4. User-friendly error messages
"""
import logging
import traceback
from typing import Dict, Any, Optional
from enum import Enum

from fastapi import HTTPException, Request, status
from fastapi.responses import J<PERSON>NResponse
from sqlalchemy.exc import SQLAlchemyError
from pydantic import ValidationError

logger = logging.getLogger(__name__)

class ErrorCode(str, Enum):
    """Standardized error codes."""
    # Authentication errors
    INVALID_CREDENTIALS = "INVALID_CREDENTIALS"
    TOKEN_EXPIRED = "TOKEN_EXPIRED"
    INSUFFICIENT_PERMISSIONS = "INSUFFICIENT_PERMISSIONS"
    
    # Validation errors
    INVALID_INPUT = "INVALID_INPUT"
    MISSING_REQUIRED_FIELD = "MISSING_REQUIRED_FIELD"
    INVALID_FILE_FORMAT = "INVALID_FILE_FORMAT"
    FILE_TOO_LARGE = "FILE_TOO_LARGE"
    
    # Database errors
    DATABASE_ERROR = "DATABASE_ERROR"
    RECORD_NOT_FOUND = "RECORD_NOT_FOUND"
    DUPLICATE_RECORD = "DUPLICATE_RECORD"
    
    # Processing errors
    PROCESSING_FAILED = "PROCESSING_FAILED"
    MODEL_TRAINING_FAILED = "MODEL_TRAINING_FAILED"
    CLASSIFICATION_FAILED = "CLASSIFICATION_FAILED"
    
    # System errors
    INTERNAL_SERVER_ERROR = "INTERNAL_SERVER_ERROR"
    SERVICE_UNAVAILABLE = "SERVICE_UNAVAILABLE"
    RATE_LIMIT_EXCEEDED = "RATE_LIMIT_EXCEEDED"


class ClassyWebException(Exception):
    """Base exception class for ClassyWeb."""
    
    def __init__(
        self,
        message: str,
        error_code: ErrorCode,
        status_code: int = status.HTTP_500_INTERNAL_SERVER_ERROR,
        details: Optional[Dict[str, Any]] = None
    ):
        self.message = message
        self.error_code = error_code
        self.status_code = status_code
        self.details = details or {}
        super().__init__(self.message)


class ValidationException(ClassyWebException):
    """Exception for validation errors."""
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=message,
            error_code=ErrorCode.INVALID_INPUT,
            status_code=status.HTTP_400_BAD_REQUEST,
            details=details
        )


class AuthenticationException(ClassyWebException):
    """Exception for authentication errors."""
    
    def __init__(self, message: str = "Authentication failed"):
        super().__init__(
            message=message,
            error_code=ErrorCode.INVALID_CREDENTIALS,
            status_code=status.HTTP_401_UNAUTHORIZED
        )


class AuthorizationException(ClassyWebException):
    """Exception for authorization errors."""
    
    def __init__(self, message: str = "Insufficient permissions"):
        super().__init__(
            message=message,
            error_code=ErrorCode.INSUFFICIENT_PERMISSIONS,
            status_code=status.HTTP_403_FORBIDDEN
        )


class DatabaseException(ClassyWebException):
    """Exception for database errors."""
    
    def __init__(self, message: str = "Database operation failed", details: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=message,
            error_code=ErrorCode.DATABASE_ERROR,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            details=details
        )


class ProcessingException(ClassyWebException):
    """Exception for processing errors."""
    
    def __init__(self, message: str, error_code: ErrorCode = ErrorCode.PROCESSING_FAILED):
        super().__init__(
            message=message,
            error_code=error_code,
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY
        )


class ErrorHandler:
    """Centralized error handling utilities."""
    
    @staticmethod
    def create_error_response(
        error_code: ErrorCode,
        message: str,
        status_code: int = status.HTTP_500_INTERNAL_SERVER_ERROR,
        details: Optional[Dict[str, Any]] = None,
        request_id: Optional[str] = None
    ) -> JSONResponse:
        """Create a standardized error response."""
        error_response = {
            "error": {
                "code": error_code.value,
                "message": message,
                "timestamp": logger.handlers[0].formatter.formatTime(logging.LogRecord(
                    name="", level=0, pathname="", lineno=0, msg="", args=(), exc_info=None
                )) if logger.handlers else None,
                "request_id": request_id
            }
        }
        
        if details:
            error_response["error"]["details"] = details
        
        return JSONResponse(
            status_code=status_code,
            content=error_response
        )
    
    @staticmethod
    def log_error(
        error: Exception,
        request: Optional[Request] = None,
        user_id: Optional[int] = None,
        additional_context: Optional[Dict[str, Any]] = None
    ) -> str:
        """Log error with context and return error ID."""
        import uuid
        error_id = str(uuid.uuid4())
        
        context = {
            "error_id": error_id,
            "error_type": type(error).__name__,
            "error_message": str(error),
            "user_id": user_id,
        }
        
        if request:
            context.update({
                "method": request.method,
                "url": str(request.url),
                "client_ip": request.client.host,
                "user_agent": request.headers.get("user-agent", "Unknown")
            })
        
        if additional_context:
            context.update(additional_context)
        
        # Log the error with full context
        logger.error(
            f"Error {error_id}: {str(error)}",
            extra=context,
            exc_info=True
        )
        
        return error_id
    
    @staticmethod
    def handle_validation_error(error: ValidationError) -> JSONResponse:
        """Handle Pydantic validation errors."""
        details = []
        for err in error.errors():
            details.append({
                "field": ".".join(str(x) for x in err["loc"]),
                "message": err["msg"],
                "type": err["type"]
            })
        
        return ErrorHandler.create_error_response(
            error_code=ErrorCode.INVALID_INPUT,
            message="Validation failed",
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            details={"validation_errors": details}
        )
    
    @staticmethod
    def handle_database_error(error: SQLAlchemyError) -> JSONResponse:
        """Handle SQLAlchemy database errors."""
        error_message = "Database operation failed"
        
        # Don't expose internal database details in production
        if logger.level == logging.DEBUG:
            error_message = str(error)
        
        return ErrorHandler.create_error_response(
            error_code=ErrorCode.DATABASE_ERROR,
            message=error_message,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )
    
    @staticmethod
    def handle_http_exception(error: HTTPException) -> JSONResponse:
        """Handle FastAPI HTTP exceptions."""
        # Map HTTP status codes to error codes
        error_code_mapping = {
            400: ErrorCode.INVALID_INPUT,
            401: ErrorCode.INVALID_CREDENTIALS,
            403: ErrorCode.INSUFFICIENT_PERMISSIONS,
            404: ErrorCode.RECORD_NOT_FOUND,
            422: ErrorCode.INVALID_INPUT,
            429: ErrorCode.RATE_LIMIT_EXCEEDED,
            500: ErrorCode.INTERNAL_SERVER_ERROR,
            503: ErrorCode.SERVICE_UNAVAILABLE
        }
        
        error_code = error_code_mapping.get(error.status_code, ErrorCode.INTERNAL_SERVER_ERROR)
        
        return ErrorHandler.create_error_response(
            error_code=error_code,
            message=error.detail,
            status_code=error.status_code
        )


# Global error handler instance
error_handler = ErrorHandler()

# Exception handlers for FastAPI
async def classyweb_exception_handler(request: Request, exc: ClassyWebException) -> JSONResponse:
    """Handle ClassyWeb custom exceptions."""
    error_id = error_handler.log_error(exc, request)
    
    return error_handler.create_error_response(
        error_code=exc.error_code,
        message=exc.message,
        status_code=exc.status_code,
        details=exc.details,
        request_id=error_id
    )


async def validation_exception_handler(request: Request, exc: ValidationError) -> JSONResponse:
    """Handle Pydantic validation exceptions."""
    error_handler.log_error(exc, request)
    return error_handler.handle_validation_error(exc)


async def database_exception_handler(request: Request, exc: SQLAlchemyError) -> JSONResponse:
    """Handle database exceptions."""
    error_handler.log_error(exc, request)
    return error_handler.handle_database_error(exc)


async def http_exception_handler(request: Request, exc: HTTPException) -> JSONResponse:
    """Handle HTTP exceptions."""
    error_handler.log_error(exc, request)
    return error_handler.handle_http_exception(exc)


async def general_exception_handler(request: Request, exc: Exception) -> JSONResponse:
    """Handle all other exceptions."""
    error_id = error_handler.log_error(exc, request)
    
    return error_handler.create_error_response(
        error_code=ErrorCode.INTERNAL_SERVER_ERROR,
        message="An unexpected error occurred",
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        request_id=error_id
    )
