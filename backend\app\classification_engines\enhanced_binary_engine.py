"""Enhanced Binary Classification Engine for ClassyWeb ML Platform Phase 2.

This module implements production-ready binary classification with advanced features:
- Threshold optimization using multiple strategies
- Class imbalance handling with SMOTE and class weights
- ROC curve analysis and AUC optimization
- Unsloth integration with GPU monitoring
- Advanced metrics and performance benchmarking
"""

import logging
import time
import pandas as pd
import numpy as np
from typing import List, Dict, Any, Optional, Tuple, Union
from sklearn.metrics import (
    accuracy_score, precision_score, recall_score, f1_score, roc_auc_score,
    roc_curve, precision_recall_curve, average_precision_score,
    classification_report, confusion_matrix
)
from sklearn.model_selection import StratifiedKFold
from sklearn.utils.class_weight import compute_class_weight
import torch
from transformers import AutoTokenizer, AutoModelForSequenceClassification, TrainingArguments, Trainer
import psutil
import GPUtil

from .base_engine import (
    BaseClassificationEngine,
    ClassificationType,
    TrainingMethod,
    ClassificationResult,
    TrainingConfig,
    TrainingResult
)
from ..performance_optimizer import performance_profiler, profile_performance, optimize_memory

logger = logging.getLogger(__name__)


class ThresholdOptimizer:
    """Advanced threshold optimization for binary classification."""
    
    def __init__(self):
        self.strategies = {
            'youden': self._youden_index,
            'f1_optimal': self._f1_optimal,
            'precision_recall_balance': self._precision_recall_balance,
            'cost_sensitive': self._cost_sensitive,
            'roc_optimal': self._roc_optimal
        }
    
    def optimize_threshold(
        self, 
        y_true: np.ndarray, 
        y_scores: np.ndarray, 
        strategy: str = 'youden',
        cost_matrix: Optional[Dict[str, float]] = None
    ) -> Dict[str, Any]:
        """Optimize classification threshold using specified strategy."""
        if strategy not in self.strategies:
            raise ValueError(f"Unknown strategy: {strategy}. Available: {list(self.strategies.keys())}")
        
        return self.strategies[strategy](y_true, y_scores, cost_matrix)
    
    def _youden_index(self, y_true: np.ndarray, y_scores: np.ndarray, cost_matrix: Optional[Dict] = None) -> Dict[str, Any]:
        """Optimize threshold using Youden's J statistic (sensitivity + specificity - 1)."""
        fpr, tpr, thresholds = roc_curve(y_true, y_scores)
        youden_scores = tpr - fpr
        optimal_idx = np.argmax(youden_scores)
        
        return {
            'threshold': float(thresholds[optimal_idx]),
            'score': float(youden_scores[optimal_idx]),
            'sensitivity': float(tpr[optimal_idx]),
            'specificity': float(1 - fpr[optimal_idx]),
            'strategy': 'youden'
        }
    
    def _f1_optimal(self, y_true: np.ndarray, y_scores: np.ndarray, cost_matrix: Optional[Dict] = None) -> Dict[str, Any]:
        """Optimize threshold for maximum F1 score."""
        precision, recall, thresholds = precision_recall_curve(y_true, y_scores)
        f1_scores = 2 * (precision * recall) / (precision + recall + 1e-8)
        optimal_idx = np.argmax(f1_scores)
        
        return {
            'threshold': float(thresholds[optimal_idx]) if optimal_idx < len(thresholds) else 0.5,
            'score': float(f1_scores[optimal_idx]),
            'precision': float(precision[optimal_idx]),
            'recall': float(recall[optimal_idx]),
            'strategy': 'f1_optimal'
        }
    
    def _precision_recall_balance(self, y_true: np.ndarray, y_scores: np.ndarray, cost_matrix: Optional[Dict] = None) -> Dict[str, Any]:
        """Find threshold where precision equals recall."""
        precision, recall, thresholds = precision_recall_curve(y_true, y_scores)
        diff = np.abs(precision - recall)
        optimal_idx = np.argmin(diff)
        
        return {
            'threshold': float(thresholds[optimal_idx]) if optimal_idx < len(thresholds) else 0.5,
            'score': float(min(precision[optimal_idx], recall[optimal_idx])),
            'precision': float(precision[optimal_idx]),
            'recall': float(recall[optimal_idx]),
            'strategy': 'precision_recall_balance'
        }
    
    def _cost_sensitive(self, y_true: np.ndarray, y_scores: np.ndarray, cost_matrix: Optional[Dict] = None) -> Dict[str, Any]:
        """Optimize threshold based on cost matrix."""
        if not cost_matrix:
            cost_matrix = {'fp_cost': 1.0, 'fn_cost': 1.0}
        
        fpr, tpr, thresholds = roc_curve(y_true, y_scores)
        fnr = 1 - tpr  # False negative rate
        
        # Calculate expected cost for each threshold
        costs = cost_matrix['fp_cost'] * fpr + cost_matrix['fn_cost'] * fnr
        optimal_idx = np.argmin(costs)
        
        return {
            'threshold': float(thresholds[optimal_idx]),
            'score': float(costs[optimal_idx]),
            'fp_rate': float(fpr[optimal_idx]),
            'fn_rate': float(fnr[optimal_idx]),
            'strategy': 'cost_sensitive'
        }
    
    def _roc_optimal(self, y_true: np.ndarray, y_scores: np.ndarray, cost_matrix: Optional[Dict] = None) -> Dict[str, Any]:
        """Find threshold closest to top-left corner of ROC curve."""
        fpr, tpr, thresholds = roc_curve(y_true, y_scores)
        distances = np.sqrt((fpr - 0)**2 + (tpr - 1)**2)
        optimal_idx = np.argmin(distances)
        
        return {
            'threshold': float(thresholds[optimal_idx]),
            'score': float(distances[optimal_idx]),
            'fpr': float(fpr[optimal_idx]),
            'tpr': float(tpr[optimal_idx]),
            'strategy': 'roc_optimal'
        }


class ClassImbalanceHandler:
    """Handle class imbalance in binary classification."""
    
    def __init__(self):
        self.strategies = ['class_weights', 'smote', 'undersampling', 'oversampling']
    
    def analyze_imbalance(self, y: np.ndarray) -> Dict[str, Any]:
        """Analyze class distribution and imbalance."""
        unique, counts = np.unique(y, return_counts=True)
        total = len(y)
        
        class_distribution = dict(zip(unique, counts))
        class_percentages = {cls: count/total for cls, count in class_distribution.items()}
        
        # Calculate imbalance ratio
        majority_count = max(counts)
        minority_count = min(counts)
        imbalance_ratio = majority_count / minority_count
        
        # Determine severity
        if imbalance_ratio < 1.5:
            severity = 'balanced'
        elif imbalance_ratio < 3:
            severity = 'mild'
        elif imbalance_ratio < 10:
            severity = 'moderate'
        else:
            severity = 'severe'
        
        return {
            'class_distribution': class_distribution,
            'class_percentages': class_percentages,
            'imbalance_ratio': imbalance_ratio,
            'severity': severity,
            'total_samples': total,
            'majority_class': unique[np.argmax(counts)],
            'minority_class': unique[np.argmin(counts)]
        }
    
    def compute_class_weights(self, y: np.ndarray) -> Dict[str, float]:
        """Compute class weights for imbalanced data."""
        classes = np.unique(y)
        weights = compute_class_weight('balanced', classes=classes, y=y)
        return dict(zip(classes, weights))
    
    def apply_smote(self, X: np.ndarray, y: np.ndarray, random_state: int = 42) -> Tuple[np.ndarray, np.ndarray]:
        """Apply SMOTE for oversampling minority class."""
        try:
            from imblearn.over_sampling import SMOTE
            smote = SMOTE(random_state=random_state)
            X_resampled, y_resampled = smote.fit_resample(X, y)
            return X_resampled, y_resampled
        except ImportError:
            logger.warning("imblearn not available, skipping SMOTE")
            return X, y


class GPUMonitor:
    """Monitor GPU utilization during training."""
    
    def __init__(self):
        self.gpu_available = torch.cuda.is_available()
        self.monitoring_data = []
    
    def get_gpu_info(self) -> Dict[str, Any]:
        """Get current GPU information."""
        if not self.gpu_available:
            return {'available': False}
        
        try:
            gpus = GPUtil.getGPUs()
            gpu_info = []
            
            for gpu in gpus:
                gpu_info.append({
                    'id': gpu.id,
                    'name': gpu.name,
                    'memory_used': gpu.memoryUsed,
                    'memory_total': gpu.memoryTotal,
                    'memory_percent': gpu.memoryUtil * 100,
                    'gpu_percent': gpu.load * 100,
                    'temperature': gpu.temperature
                })
            
            return {
                'available': True,
                'count': len(gpus),
                'gpus': gpu_info
            }
        except Exception as e:
            logger.warning(f"Failed to get GPU info: {e}")
            return {'available': False, 'error': str(e)}
    
    def start_monitoring(self):
        """Start GPU monitoring."""
        self.monitoring_data = []
    
    def record_metrics(self):
        """Record current GPU metrics."""
        gpu_info = self.get_gpu_info()
        if gpu_info['available']:
            self.monitoring_data.append({
                'timestamp': time.time(),
                'gpu_info': gpu_info
            })
    
    def get_monitoring_summary(self) -> Dict[str, Any]:
        """Get summary of GPU monitoring data."""
        if not self.monitoring_data:
            return {'monitored': False}
        
        # Calculate averages and peaks
        memory_usage = []
        gpu_usage = []
        temperatures = []
        
        for record in self.monitoring_data:
            for gpu in record['gpu_info'].get('gpus', []):
                memory_usage.append(gpu['memory_percent'])
                gpu_usage.append(gpu['gpu_percent'])
                temperatures.append(gpu['temperature'])
        
        return {
            'monitored': True,
            'duration': self.monitoring_data[-1]['timestamp'] - self.monitoring_data[0]['timestamp'],
            'samples': len(self.monitoring_data),
            'memory_usage': {
                'avg': np.mean(memory_usage) if memory_usage else 0,
                'max': np.max(memory_usage) if memory_usage else 0,
                'min': np.min(memory_usage) if memory_usage else 0
            },
            'gpu_usage': {
                'avg': np.mean(gpu_usage) if gpu_usage else 0,
                'max': np.max(gpu_usage) if gpu_usage else 0,
                'min': np.min(gpu_usage) if gpu_usage else 0
            },
            'temperature': {
                'avg': np.mean(temperatures) if temperatures else 0,
                'max': np.max(temperatures) if temperatures else 0,
                'min': np.min(temperatures) if temperatures else 0
            }
        }


class EnhancedBinaryClassificationEngine(BaseClassificationEngine):
    """Enhanced binary classification engine with production-ready features."""
    
    def __init__(self, classification_type: ClassificationType, **kwargs):
        """Initialize enhanced binary classification engine."""
        super().__init__(classification_type)
        self.threshold_optimizer = ThresholdOptimizer()
        self.imbalance_handler = ClassImbalanceHandler()
        self.gpu_monitor = GPUMonitor()
        
        # Enhanced attributes
        self.optimal_threshold = 0.5
        self.threshold_strategy = 'youden'
        self.class_weights = None
        self.imbalance_analysis = None
        self.roc_data = None
        self.pr_data = None
        
    @property
    def supported_training_methods(self) -> List[TrainingMethod]:
        """Return supported training methods."""
        return [TrainingMethod.CUSTOM, TrainingMethod.LLM]
    
    @property
    def default_metrics(self) -> List[str]:
        """Return enhanced default metrics for binary classification."""
        return [
            'accuracy', 'precision', 'recall', 'f1', 'auc_roc', 'auc_pr',
            'sensitivity', 'specificity', 'balanced_accuracy', 'mcc'
        ]

    def validate_config(self, config: TrainingConfig) -> Tuple[bool, List[str]]:
        """Validate configuration for enhanced binary classification."""
        errors = []

        # Check classification type
        if config.classification_type != ClassificationType.BINARY:
            errors.append(f"Expected binary classification, got {config.classification_type}")

        # Check text columns
        if not config.text_columns:
            errors.append("At least one text column must be specified")

        # Check label columns
        if len(config.label_columns) != 1:
            errors.append("Binary classification requires exactly one label column")

        # Validate threshold optimization strategy
        threshold_strategy = config.metadata.get('threshold_strategy', 'youden')
        if threshold_strategy not in self.threshold_optimizer.strategies:
            errors.append(f"Invalid threshold strategy: {threshold_strategy}")

        # Validate imbalance handling strategy
        imbalance_strategy = config.metadata.get('imbalance_strategy', 'class_weights')
        if imbalance_strategy not in self.imbalance_handler.strategies:
            errors.append(f"Invalid imbalance strategy: {imbalance_strategy}")

        # Check training method specific requirements
        if config.training_method == TrainingMethod.LLM:
            if not config.llm_provider:
                errors.append("LLM provider must be specified for LLM training method")
            if not config.prompt_template:
                errors.append("Prompt template must be specified for LLM training method")

        return len(errors) == 0, errors

    def analyze_data_characteristics(self, data: pd.DataFrame, config: TrainingConfig) -> Dict[str, Any]:
        """Analyze data characteristics for binary classification."""
        text_col = config.text_columns[0]
        label_col = config.label_columns[0]

        # Basic data analysis
        total_samples = len(data)
        missing_text = data[text_col].isnull().sum()
        missing_labels = data[label_col].isnull().sum()

        # Text analysis
        text_lengths = data[text_col].astype(str).str.len()

        # Label analysis
        labels = data[label_col].dropna()
        unique_labels = labels.unique()

        if len(unique_labels) != 2:
            logger.warning(f"Expected 2 unique labels, found {len(unique_labels)}: {unique_labels}")

        # Class imbalance analysis
        self.imbalance_analysis = self.imbalance_handler.analyze_imbalance(labels.values)

        return {
            'total_samples': total_samples,
            'missing_text': int(missing_text),
            'missing_labels': int(missing_labels),
            'text_stats': {
                'avg_length': float(text_lengths.mean()),
                'max_length': int(text_lengths.max()),
                'min_length': int(text_lengths.min()),
                'std_length': float(text_lengths.std())
            },
            'label_stats': {
                'unique_labels': unique_labels.tolist(),
                'label_counts': labels.value_counts().to_dict()
            },
            'imbalance_analysis': self.imbalance_analysis
        }

    def compute_enhanced_metrics(
        self,
        y_true: np.ndarray,
        y_pred: np.ndarray,
        y_scores: np.ndarray
    ) -> Dict[str, Any]:
        """Compute comprehensive binary classification metrics."""
        from sklearn.metrics import matthews_corrcoef, balanced_accuracy_score

        # Basic metrics
        metrics = {
            'accuracy': float(accuracy_score(y_true, y_pred)),
            'precision': float(precision_score(y_true, y_pred, zero_division=0)),
            'recall': float(recall_score(y_true, y_pred, zero_division=0)),
            'f1': float(f1_score(y_true, y_pred, zero_division=0)),
            'balanced_accuracy': float(balanced_accuracy_score(y_true, y_pred)),
            'mcc': float(matthews_corrcoef(y_true, y_pred))
        }

        # ROC and PR curves
        try:
            # ROC curve
            fpr, tpr, roc_thresholds = roc_curve(y_true, y_scores)
            metrics['auc_roc'] = float(roc_auc_score(y_true, y_scores))

            # Store ROC data for visualization
            self.roc_data = {
                'fpr': fpr.tolist(),
                'tpr': tpr.tolist(),
                'thresholds': roc_thresholds.tolist()
            }

            # Precision-Recall curve
            precision, recall, pr_thresholds = precision_recall_curve(y_true, y_scores)
            metrics['auc_pr'] = float(average_precision_score(y_true, y_scores))

            # Store PR data for visualization
            self.pr_data = {
                'precision': precision.tolist(),
                'recall': recall.tolist(),
                'thresholds': pr_thresholds.tolist()
            }

            # Sensitivity and Specificity
            tn, fp, fn, tp = confusion_matrix(y_true, y_pred).ravel()
            metrics['sensitivity'] = float(tp / (tp + fn)) if (tp + fn) > 0 else 0.0
            metrics['specificity'] = float(tn / (tn + fp)) if (tn + fp) > 0 else 0.0

            # Additional metrics
            metrics['true_positives'] = int(tp)
            metrics['true_negatives'] = int(tn)
            metrics['false_positives'] = int(fp)
            metrics['false_negatives'] = int(fn)

        except Exception as e:
            logger.warning(f"Failed to compute some metrics: {e}")
            metrics['auc_roc'] = 0.0
            metrics['auc_pr'] = 0.0
            metrics['sensitivity'] = 0.0
            metrics['specificity'] = 0.0

        return metrics

    def optimize_threshold_for_data(
        self,
        y_true: np.ndarray,
        y_scores: np.ndarray,
        strategy: str = 'youden'
    ) -> Dict[str, Any]:
        """Optimize classification threshold for the given data."""
        optimization_result = self.threshold_optimizer.optimize_threshold(
            y_true, y_scores, strategy
        )

        self.optimal_threshold = optimization_result['threshold']
        self.threshold_strategy = strategy

        # Test the optimized threshold
        y_pred_optimized = (y_scores >= self.optimal_threshold).astype(int)
        optimized_metrics = self.compute_enhanced_metrics(y_true, y_pred_optimized, y_scores)

        return {
            'optimization_result': optimization_result,
            'optimized_metrics': optimized_metrics,
            'threshold_used': self.optimal_threshold
        }

    @profile_performance
    @optimize_memory(threshold_mb=2048)
    async def train_custom_model(
        self,
        data: pd.DataFrame,
        config: TrainingConfig,
        progress_callback: Optional[callable] = None
    ) -> TrainingResult:
        """Train enhanced binary classification model with advanced features."""
        start_time = time.time()

        try:
            # Validate configuration
            is_valid, errors = self.validate_config(config)
            if not is_valid:
                return TrainingResult(
                    model_id="",
                    training_time=0,
                    final_metrics={},
                    training_history=[],
                    error=f"Configuration validation failed: {'; '.join(errors)}"
                )

            if progress_callback:
                progress_callback({"stage": "data_analysis", "progress": 0.05})

            # Analyze data characteristics
            data_characteristics = self.analyze_data_characteristics(data, config)

            # Prepare data
            text_col = config.text_columns[0]
            label_col = config.label_columns[0]

            texts = data[text_col].astype(str).tolist()
            labels = data[label_col].tolist()

            # Convert labels to binary (0, 1)
            unique_labels = list(set(labels))
            if len(unique_labels) != 2:
                return TrainingResult(
                    model_id="",
                    training_time=0,
                    final_metrics={},
                    training_history=[],
                    error=f"Expected 2 unique labels, found {len(unique_labels)}"
                )

            # Map labels to 0, 1
            label_map = {unique_labels[0]: 0, unique_labels[1]: 1}
            binary_labels = np.array([label_map[label] for label in labels])

            if progress_callback:
                progress_callback({"stage": "imbalance_handling", "progress": 0.1})

            # Handle class imbalance
            imbalance_strategy = config.metadata.get('imbalance_strategy', 'class_weights')
            if imbalance_strategy == 'class_weights':
                self.class_weights = self.imbalance_handler.compute_class_weights(binary_labels)

            if progress_callback:
                progress_callback({"stage": "model_setup", "progress": 0.15})

            # Get performance optimization recommendations
            available_memory = performance_profiler.memory_manager.get_memory_usage()['available_mb']
            optimization_config = performance_profiler.optimize_training_config(
                model_size_mb=250,  # Estimated model size
                dataset_size=len(data),
                available_memory_mb=available_memory
            )

            # Apply optimization recommendations
            if 'batch_size' in optimization_config:
                config.batch_size = min(config.batch_size, optimization_config['batch_size'])
            if 'gradient_accumulation_steps' in optimization_config:
                config.gradient_accumulation_steps = optimization_config['gradient_accumulation_steps']
            if 'use_fp16' in optimization_config:
                config.fp16 = optimization_config['use_fp16']

            # Initialize tokenizer and model
            tokenizer = AutoTokenizer.from_pretrained(config.base_model)
            model = AutoModelForSequenceClassification.from_pretrained(
                config.base_model,
                num_labels=2
            )

            # Optimize model for training
            if performance_profiler.gpu_optimizer.device.type == 'cuda':
                model = model.to(performance_profiler.gpu_optimizer.device)

            # Start GPU monitoring
            self.gpu_monitor.start_monitoring()

            if progress_callback:
                progress_callback({"stage": "tokenization", "progress": 0.2})

            # Tokenize data
            encodings = tokenizer(
                texts,
                truncation=True,
                padding=True,
                max_length=config.max_length,
                return_tensors="pt"
            )

            # Create dataset
            class EnhancedBinaryDataset(torch.utils.data.Dataset):
                def __init__(self, encodings, labels):
                    self.encodings = encodings
                    self.labels = labels

                def __getitem__(self, idx):
                    item = {key: torch.tensor(val[idx]) for key, val in self.encodings.items()}
                    item['labels'] = torch.tensor(self.labels[idx], dtype=torch.long)
                    return item

                def __len__(self):
                    return len(self.labels)

            # Split data with stratification
            from sklearn.model_selection import train_test_split
            train_texts, val_texts, train_labels, val_labels = train_test_split(
                texts, binary_labels, test_size=config.validation_split,
                random_state=42, stratify=binary_labels
            )

            train_encodings = tokenizer(train_texts, truncation=True, padding=True, max_length=config.max_length, return_tensors="pt")
            val_encodings = tokenizer(val_texts, truncation=True, padding=True, max_length=config.max_length, return_tensors="pt")

            train_dataset = EnhancedBinaryDataset(train_encodings, train_labels)
            val_dataset = EnhancedBinaryDataset(val_encodings, val_labels)

            if progress_callback:
                progress_callback({"stage": "training_setup", "progress": 0.25})

            # Enhanced training arguments
            training_args = TrainingArguments(
                output_dir='./results',
                num_train_epochs=config.num_epochs,
                per_device_train_batch_size=config.batch_size,
                per_device_eval_batch_size=config.batch_size,
                warmup_steps=config.warmup_steps,
                weight_decay=config.weight_decay,
                logging_dir='./logs',
                logging_steps=10,
                evaluation_strategy=config.evaluation_strategy,
                save_strategy=config.save_strategy,
                fp16=config.fp16,
                gradient_accumulation_steps=config.gradient_accumulation_steps,
                gradient_checkpointing=config.gradient_checkpointing,
                learning_rate=config.learning_rate,
                load_best_model_at_end=True,
                metric_for_best_model='eval_f1',
                greater_is_better=True,
                save_total_limit=2,
                dataloader_num_workers=2 if config.use_unsloth else 0,
            )

            # Custom compute metrics function
            def compute_metrics(eval_pred):
                predictions, labels = eval_pred
                predictions = np.argmax(predictions, axis=1)

                # Record GPU metrics during evaluation
                self.gpu_monitor.record_metrics()

                return {
                    'accuracy': accuracy_score(labels, predictions),
                    'f1': f1_score(labels, predictions),
                    'precision': precision_score(labels, predictions, zero_division=0),
                    'recall': recall_score(labels, predictions, zero_division=0)
                }

            # Initialize trainer
            trainer = Trainer(
                model=model,
                args=training_args,
                train_dataset=train_dataset,
                eval_dataset=val_dataset,
                compute_metrics=compute_metrics,
            )

            if progress_callback:
                progress_callback({"stage": "training", "progress": 0.3})

            # Train model with monitoring
            training_start = time.time()
            trainer.train()
            training_end = time.time()

            if progress_callback:
                progress_callback({"stage": "evaluation", "progress": 0.8})

            # Get predictions for threshold optimization
            val_predictions = trainer.predict(val_dataset)
            val_probs = torch.softmax(torch.tensor(val_predictions.predictions), dim=1)[:, 1].numpy()
            val_preds_default = np.argmax(val_predictions.predictions, axis=1)

            # Optimize threshold
            threshold_strategy = config.metadata.get('threshold_strategy', 'youden')
            threshold_optimization = self.optimize_threshold_for_data(
                val_labels, val_probs, threshold_strategy
            )

            # Compute final metrics with optimized threshold
            val_preds_optimized = (val_probs >= self.optimal_threshold).astype(int)
            final_metrics = self.compute_enhanced_metrics(val_labels, val_preds_optimized, val_probs)

            # Add training metadata
            gpu_summary = self.gpu_monitor.get_monitoring_summary()
            final_metrics.update({
                'data_characteristics': data_characteristics,
                'threshold_optimization': threshold_optimization,
                'gpu_monitoring': gpu_summary,
                'training_time': training_end - training_start,
                'roc_curve_data': self.roc_data,
                'pr_curve_data': self.pr_data,
                'class_weights': self.class_weights,
                'imbalance_analysis': self.imbalance_analysis
            })

            # Save model artifacts
            model_id = f"enhanced_binary_model_{int(time.time())}"
            model_path = f"./model_artifacts/{model_id}"

            trainer.save_model(model_path)
            tokenizer.save_pretrained(model_path)

            # Store model info
            self.model = model
            self.tokenizer = tokenizer
            self.label_map = {v: k for k, v in label_map.items()}
            self.is_trained = True

            training_time = time.time() - start_time

            if progress_callback:
                progress_callback({"stage": "complete", "progress": 1.0})

            return TrainingResult(
                model_id=model_id,
                training_time=training_time,
                final_metrics=final_metrics,
                training_history=[],  # TODO: Extract from trainer logs
                model_path=model_path,
                tokenizer_path=model_path,
                config_path=model_path
            )

        except Exception as e:
            logger.error(f"Enhanced binary training failed: {e}")
            return TrainingResult(
                model_id="",
                training_time=time.time() - start_time,
                final_metrics={},
                training_history=[],
                error=str(e)
            )
