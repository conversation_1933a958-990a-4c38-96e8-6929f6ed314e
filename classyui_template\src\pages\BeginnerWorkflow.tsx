import { useState } from "react";
import { Navigation } from "@/components/Navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/hooks/use-toast";
import { 
  Upload, 
  Brain, 
  Lightbulb, 
  Settings, 
  Zap, 
  Activity, 
  BarChart3, 
  Rocket,
  CheckCircle2,
  ArrowRight,
  FileText,
  Loader2,
  GraduationCap
} from "lucide-react";

const steps = [
  { id: 1, title: "Upload Data", icon: Upload, status: "current" },
  { id: 2, title: "Smart Detection", icon: Brain, status: "upcoming" },
  { id: 3, title: "Recommendations", icon: Lightbulb, status: "upcoming" },
  { id: 4, title: "Guided Setup", icon: Settings, status: "upcoming" },
  { id: 5, title: "Method Selection", icon: Zap, status: "upcoming" },
  { id: 6, title: "Training", icon: Activity, status: "upcoming" },
  { id: 7, title: "Results", icon: BarChart3, status: "upcoming" },
  { id: 8, title: "Deploy", icon: Rocket, status: "upcoming" }
];

const BeginnerWorkflow = () => {
  const [currentStep, setCurrentStep] = useState(1);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysisComplete, setAnalysisComplete] = useState(false);
  const [recommendation, setRecommendation] = useState<string | null>(null);
  const { toast } = useToast();

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      toast({
        title: "File uploaded successfully",
        description: `${file.name} is ready for analysis`,
      });
      
      // Simulate analysis
      setIsAnalyzing(true);
      setTimeout(() => {
        setIsAnalyzing(false);
        setAnalysisComplete(true);
        setRecommendation("Multi-class Classification");
        setCurrentStep(3);
        toast({
          title: "Analysis complete",
          description: "We've detected your data is perfect for multi-class classification",
        });
      }, 2000);
    }
  };

  const handleNextStep = () => {
    if (currentStep < steps.length) {
      setCurrentStep(currentStep + 1);
      toast({
        title: "Step completed",
        description: `Moving to ${steps[currentStep]?.title}`,
      });
    }
  };

  const currentStepData = steps.find(step => step.id === currentStep);
  const Icon = currentStepData?.icon || Upload;

  return (
    <div className="min-h-screen bg-background">
      <Navigation />
      
      <main className="pt-20">
        {/* Header */}
        <section className="py-8 bg-ml-success/5">
          <div className="max-w-7xl mx-auto px-6">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-8 h-8 rounded-lg bg-ml-success/20 flex items-center justify-center">
                <GraduationCap className="w-5 h-5 text-ml-success" />
              </div>
              <Badge variant="outline" className="border-ml-success/30 text-ml-success">
                Beginner Journey
              </Badge>
            </div>
            <h1 className="text-3xl font-bold mb-2">Guided ML Classification Workflow</h1>
            <p className="text-muted-foreground">Follow our step-by-step process to train your classification model</p>
          </div>
        </section>

        <div className="max-w-7xl mx-auto px-6 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            
            {/* Progress Sidebar */}
            <div className="lg:col-span-1">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Progress</CardTitle>
                  <Progress value={(currentStep / steps.length) * 100} className="mt-2" />
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {steps.map((step) => {
                      const StepIcon = step.icon;
                      const isCompleted = step.id < currentStep;
                      const isCurrent = step.id === currentStep;
                      
                      return (
                        <div 
                          key={step.id}
                          className={`flex items-center gap-3 p-2 rounded-lg transition-colors ${
                            isCurrent ? 'bg-ml-success/10 border border-ml-success/20' :
                            isCompleted ? 'bg-muted/50' : 'opacity-50'
                          }`}
                        >
                          <div className={`w-6 h-6 rounded-full flex items-center justify-center ${
                            isCompleted ? 'bg-ml-success text-white' :
                            isCurrent ? 'bg-ml-success/20 text-ml-success' : 'bg-muted'
                          }`}>
                            {isCompleted ? (
                              <CheckCircle2 className="w-3 h-3" />
                            ) : (
                              <StepIcon className="w-3 h-3" />
                            )}
                          </div>
                          <span className={`text-sm ${isCurrent ? 'font-medium text-ml-success' : ''}`}>
                            {step.title}
                          </span>
                        </div>
                      );
                    })}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Main Content */}
            <div className="lg:col-span-3">
              <Card>
                <CardHeader>
                  <div className="flex items-center gap-3 mb-2">
                    <div className="w-8 h-8 rounded-lg bg-ml-success/10 flex items-center justify-center">
                      <Icon className="w-5 h-5 text-ml-success" />
                    </div>
                    <div>
                      <CardTitle>Step {currentStep}: {currentStepData?.title}</CardTitle>
                      <CardDescription>
                        {currentStep === 1 && "Upload your dataset to get started"}
                        {currentStep === 2 && "Our AI is analyzing your data"}
                        {currentStep === 3 && "Based on analysis, here's our recommendation"}
                      </CardDescription>
                    </div>
                  </div>
                </CardHeader>

                <CardContent>
                  {/* Step 1: Upload Data */}
                  {currentStep === 1 && (
                    <div className="space-y-6">
                      <div className="border-2 border-dashed border-border rounded-lg p-8 text-center">
                        <Upload className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                        <h3 className="text-lg font-semibold mb-2">Upload Your Dataset</h3>
                        <p className="text-muted-foreground mb-4">
                          Drag and drop your CSV, JSON, or Excel file here, or click to browse
                        </p>
                        <Input
                          type="file"
                          accept=".csv,.json,.xlsx,.xls"
                          onChange={handleFileUpload}
                          className="hidden"
                          id="file-upload"
                        />
                        <Label htmlFor="file-upload">
                          <Button variant="outline" className="cursor-pointer">
                            <FileText className="w-4 h-4 mr-2" />
                            Choose File
                          </Button>
                        </Label>
                      </div>
                      
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
                        <div className="p-4 bg-muted/50 rounded-lg">
                          <h4 className="font-medium mb-1">CSV Files</h4>
                          <p className="text-xs text-muted-foreground">Comma-separated values</p>
                        </div>
                        <div className="p-4 bg-muted/50 rounded-lg">
                          <h4 className="font-medium mb-1">JSON Data</h4>
                          <p className="text-xs text-muted-foreground">Structured JSON format</p>
                        </div>
                        <div className="p-4 bg-muted/50 rounded-lg">
                          <h4 className="font-medium mb-1">Excel Sheets</h4>
                          <p className="text-xs text-muted-foreground">.xlsx and .xls files</p>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Step 2: Analysis */}
                  {currentStep === 2 && (
                    <div className="space-y-6 text-center">
                      <div className="py-12">
                        {isAnalyzing ? (
                          <>
                            <Loader2 className="w-16 h-16 text-ml-success animate-spin mx-auto mb-4" />
                            <h3 className="text-lg font-semibold mb-2">Analyzing Your Data</h3>
                            <p className="text-muted-foreground">
                              Our AI is examining patterns, relationships, and data structure...
                            </p>
                          </>
                        ) : (
                          <>
                            <CheckCircle2 className="w-16 h-16 text-ml-success mx-auto mb-4" />
                            <h3 className="text-lg font-semibold mb-2">Analysis Complete!</h3>
                            <p className="text-muted-foreground">
                              We've identified key patterns in your dataset
                            </p>
                          </>
                        )}
                      </div>
                    </div>
                  )}

                  {/* Step 3: Recommendations */}
                  {currentStep === 3 && recommendation && (
                    <div className="space-y-6">
                      <div className="bg-ml-success/5 border border-ml-success/20 rounded-lg p-6">
                        <div className="flex items-center gap-3 mb-4">
                          <Lightbulb className="w-6 h-6 text-ml-success" />
                          <h3 className="text-lg font-semibold text-ml-success">Recommended Classification Type</h3>
                        </div>
                        <div className="bg-white rounded-lg p-4 border">
                          <h4 className="font-semibold mb-2">{recommendation}</h4>
                          <p className="text-sm text-muted-foreground mb-3">
                            Based on your data structure, this is the optimal classification approach for your dataset.
                          </p>
                          <div className="flex gap-2">
                            <Badge variant="secondary">95% Confidence</Badge>
                            <Badge variant="outline">3 Classes Detected</Badge>
                          </div>
                        </div>
                      </div>

                      <div className="space-y-4">
                        <h4 className="font-semibold">Data Insights:</h4>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div className="p-4 bg-muted/50 rounded-lg">
                            <h5 className="font-medium mb-1">Dataset Size</h5>
                            <p className="text-2xl font-bold text-ml-success">1,247 rows</p>
                          </div>
                          <div className="p-4 bg-muted/50 rounded-lg">
                            <h5 className="font-medium mb-1">Features</h5>
                            <p className="text-2xl font-bold text-ml-success">8 columns</p>
                          </div>
                        </div>
                      </div>

                      <Button onClick={handleNextStep} className="w-full bg-ml-success hover:bg-ml-success/90">
                        Accept Recommendation
                        <ArrowRight className="w-4 h-4 ml-2" />
                      </Button>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default BeginnerWorkflow;