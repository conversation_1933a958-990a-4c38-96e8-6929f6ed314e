"""Intelligent Assistance System for ClassyWeb ML Platform.

This module provides AI-powered assistance for model selection, hyperparameter tuning,
and performance optimization based on data characteristics and historical performance.
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple, Union
from dataclasses import dataclass
from enum import Enum
import json
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
import re

logger = logging.getLogger(__name__)


class RecommendationType(Enum):
    """Types of recommendations the assistant can provide."""
    MODEL_SELECTION = "model_selection"
    HYPERPARAMETER_TUNING = "hyperparameter_tuning"
    PERFORMANCE_OPTIMIZATION = "performance_optimization"
    DATA_PREPROCESSING = "data_preprocessing"
    TRAINING_STRATEGY = "training_strategy"


@dataclass
class DataCharacteristics:
    """Characteristics of the dataset for analysis."""
    num_samples: int
    num_features: int
    num_classes: int
    class_distribution: Dict[str, int]
    avg_text_length: float
    text_complexity_score: float
    language_detected: str
    has_class_imbalance: bool
    imbalance_ratio: float
    vocabulary_size: int
    unique_tokens_ratio: float
    domain_indicators: List[str]


@dataclass
class Recommendation:
    """A recommendation from the intelligent assistant."""
    type: RecommendationType
    title: str
    description: str
    confidence: float
    reasoning: str
    parameters: Dict[str, Any]
    expected_improvement: Optional[float] = None
    implementation_difficulty: str = "medium"  # easy, medium, hard
    estimated_time_minutes: Optional[int] = None


class IntelligentAssistant:
    """AI-powered assistant for ML optimization recommendations."""
    
    def __init__(self):
        self.knowledge_base = self._load_knowledge_base()
        self.performance_history = []
        self.vectorizer = TfidfVectorizer(max_features=1000, stop_words='english')
        
    def _load_knowledge_base(self) -> Dict[str, Any]:
        """Load the knowledge base with best practices and patterns."""
        return {
            "model_recommendations": {
                "small_dataset": {
                    "models": ["distilbert-base-uncased", "albert-base-v2"],
                    "reasoning": "Smaller models work better with limited data to avoid overfitting"
                },
                "large_dataset": {
                    "models": ["bert-base-uncased", "roberta-base", "deberta-base"],
                    "reasoning": "Larger models can leverage more data for better performance"
                },
                "imbalanced_classes": {
                    "strategies": ["class_weights", "focal_loss", "oversampling"],
                    "reasoning": "Address class imbalance to improve minority class performance"
                },
                "domain_specific": {
                    "scientific": ["scibert", "biobert"],
                    "legal": ["legal-bert"],
                    "financial": ["finbert"],
                    "clinical": ["clinicalbert"]
                }
            },
            "hyperparameter_patterns": {
                "learning_rate": {
                    "small_dataset": {"min": 1e-5, "max": 5e-4, "default": 2e-5},
                    "large_dataset": {"min": 1e-6, "max": 1e-4, "default": 1e-5},
                    "fine_tuning": {"min": 1e-6, "max": 5e-5, "default": 2e-5}
                },
                "batch_size": {
                    "small_dataset": {"values": [8, 16], "default": 8},
                    "medium_dataset": {"values": [16, 32], "default": 16},
                    "large_dataset": {"values": [32, 64], "default": 32}
                },
                "epochs": {
                    "small_dataset": {"min": 5, "max": 20, "default": 10},
                    "large_dataset": {"min": 2, "max": 10, "default": 3}
                }
            },
            "optimization_strategies": {
                "memory_optimization": {
                    "gradient_checkpointing": "Reduces memory usage at cost of computation",
                    "mixed_precision": "Uses FP16 to reduce memory and increase speed",
                    "gradient_accumulation": "Simulates larger batch sizes with limited memory"
                },
                "speed_optimization": {
                    "dynamic_padding": "Reduces computation on padding tokens",
                    "dataloader_workers": "Parallel data loading",
                    "pin_memory": "Faster GPU transfer"
                }
            }
        }
    
    def analyze_data_characteristics(self, data: pd.DataFrame, text_column: str, label_column: str) -> DataCharacteristics:
        """Analyze dataset characteristics for intelligent recommendations."""
        texts = data[text_column].astype(str).tolist()
        labels = data[label_column].tolist()
        
        # Basic statistics
        num_samples = len(data)
        num_classes = len(set(labels))
        class_distribution = dict(pd.Series(labels).value_counts())
        
        # Text analysis
        text_lengths = [len(text.split()) for text in texts]
        avg_text_length = np.mean(text_lengths)
        
        # Calculate text complexity (vocabulary diversity)
        all_text = " ".join(texts)
        words = re.findall(r'\b\w+\b', all_text.lower())
        vocabulary_size = len(set(words))
        unique_tokens_ratio = vocabulary_size / len(words) if words else 0
        
        # Text complexity score (based on vocabulary diversity and average length)
        text_complexity_score = min(1.0, (unique_tokens_ratio * 2 + avg_text_length / 100) / 3)
        
        # Class imbalance analysis
        class_counts = list(class_distribution.values())
        imbalance_ratio = max(class_counts) / min(class_counts) if class_counts else 1.0
        has_class_imbalance = imbalance_ratio > 2.0
        
        # Language detection (simple heuristic)
        language_detected = self._detect_language(texts[:100])  # Sample first 100 texts
        
        # Domain detection
        domain_indicators = self._detect_domain(texts[:100])
        
        return DataCharacteristics(
            num_samples=num_samples,
            num_features=len(data.columns),
            num_classes=num_classes,
            class_distribution=class_distribution,
            avg_text_length=avg_text_length,
            text_complexity_score=text_complexity_score,
            language_detected=language_detected,
            has_class_imbalance=has_class_imbalance,
            imbalance_ratio=imbalance_ratio,
            vocabulary_size=vocabulary_size,
            unique_tokens_ratio=unique_tokens_ratio,
            domain_indicators=domain_indicators
        )
    
    def get_model_recommendations(self, characteristics: DataCharacteristics) -> List[Recommendation]:
        """Get intelligent model selection recommendations."""
        recommendations = []
        
        # Dataset size-based recommendations
        if characteristics.num_samples < 1000:
            recommendations.append(Recommendation(
                type=RecommendationType.MODEL_SELECTION,
                title="Use Lightweight Model for Small Dataset",
                description="DistilBERT or ALBERT recommended for datasets under 1000 samples",
                confidence=0.9,
                reasoning="Small datasets benefit from lighter models to prevent overfitting",
                parameters={"model_name": "distilbert-base-uncased", "freeze_layers": 6},
                expected_improvement=0.15,
                implementation_difficulty="easy",
                estimated_time_minutes=30
            ))
        elif characteristics.num_samples > 10000:
            recommendations.append(Recommendation(
                type=RecommendationType.MODEL_SELECTION,
                title="Use Full-Size Model for Large Dataset",
                description="BERT or RoBERTa recommended for datasets over 10,000 samples",
                confidence=0.85,
                reasoning="Large datasets can support bigger models for better performance",
                parameters={"model_name": "bert-base-uncased", "freeze_layers": 0},
                expected_improvement=0.12,
                implementation_difficulty="medium",
                estimated_time_minutes=120
            ))
        
        # Domain-specific recommendations
        for domain in characteristics.domain_indicators:
            if domain in self.knowledge_base["model_recommendations"]["domain_specific"]:
                domain_models = self.knowledge_base["model_recommendations"]["domain_specific"][domain]
                recommendations.append(Recommendation(
                    type=RecommendationType.MODEL_SELECTION,
                    title=f"Use {domain.title()}-Specific Model",
                    description=f"Specialized model for {domain} domain detected",
                    confidence=0.8,
                    reasoning=f"Domain-specific models often outperform general models in {domain}",
                    parameters={"model_name": domain_models[0] if domain_models else "bert-base-uncased"},
                    expected_improvement=0.20,
                    implementation_difficulty="medium",
                    estimated_time_minutes=90
                ))
        
        # Class imbalance recommendations
        if characteristics.has_class_imbalance:
            recommendations.append(Recommendation(
                type=RecommendationType.TRAINING_STRATEGY,
                title="Address Class Imbalance",
                description="Use class weights or focal loss for imbalanced dataset",
                confidence=0.95,
                reasoning=f"Imbalance ratio of {characteristics.imbalance_ratio:.1f} requires special handling",
                parameters={
                    "class_weight": "balanced",
                    "focal_loss": True,
                    "alpha": 0.25,
                    "gamma": 2.0
                },
                expected_improvement=0.25,
                implementation_difficulty="medium",
                estimated_time_minutes=45
            ))
        
        return recommendations
    
    def get_hyperparameter_recommendations(self, characteristics: DataCharacteristics) -> List[Recommendation]:
        """Get intelligent hyperparameter tuning recommendations."""
        recommendations = []
        
        # Learning rate recommendations
        if characteristics.num_samples < 1000:
            lr_config = self.knowledge_base["hyperparameter_patterns"]["learning_rate"]["small_dataset"]
        elif characteristics.num_samples > 10000:
            lr_config = self.knowledge_base["hyperparameter_patterns"]["learning_rate"]["large_dataset"]
        else:
            lr_config = self.knowledge_base["hyperparameter_patterns"]["learning_rate"]["fine_tuning"]
        
        recommendations.append(Recommendation(
            type=RecommendationType.HYPERPARAMETER_TUNING,
            title="Optimize Learning Rate",
            description=f"Recommended learning rate range: {lr_config['min']:.0e} - {lr_config['max']:.0e}",
            confidence=0.8,
            reasoning="Learning rate optimization based on dataset size and complexity",
            parameters={
                "learning_rate": lr_config["default"],
                "lr_scheduler": "linear",
                "warmup_steps": max(100, characteristics.num_samples // 100)
            },
            expected_improvement=0.10,
            implementation_difficulty="easy",
            estimated_time_minutes=15
        ))
        
        # Batch size recommendations
        if characteristics.num_samples < 1000:
            batch_config = self.knowledge_base["hyperparameter_patterns"]["batch_size"]["small_dataset"]
        elif characteristics.num_samples > 10000:
            batch_config = self.knowledge_base["hyperparameter_patterns"]["batch_size"]["large_dataset"]
        else:
            batch_config = self.knowledge_base["hyperparameter_patterns"]["batch_size"]["medium_dataset"]
        
        recommendations.append(Recommendation(
            type=RecommendationType.HYPERPARAMETER_TUNING,
            title="Optimize Batch Size",
            description=f"Recommended batch size: {batch_config['default']}",
            confidence=0.75,
            reasoning="Batch size optimization based on dataset size and memory constraints",
            parameters={
                "batch_size": batch_config["default"],
                "gradient_accumulation_steps": 1 if characteristics.num_samples > 5000 else 2
            },
            expected_improvement=0.08,
            implementation_difficulty="easy",
            estimated_time_minutes=10
        ))
        
        return recommendations
    
    def get_performance_optimization_recommendations(self, characteristics: DataCharacteristics) -> List[Recommendation]:
        """Get performance optimization recommendations."""
        recommendations = []
        
        # Memory optimization for large datasets
        if characteristics.num_samples > 5000 or characteristics.avg_text_length > 200:
            recommendations.append(Recommendation(
                type=RecommendationType.PERFORMANCE_OPTIMIZATION,
                title="Enable Memory Optimizations",
                description="Use gradient checkpointing and mixed precision for large datasets",
                confidence=0.9,
                reasoning="Large datasets or long texts require memory optimization",
                parameters={
                    "gradient_checkpointing": True,
                    "fp16": True,
                    "dataloader_num_workers": 4
                },
                expected_improvement=0.0,  # Speed improvement, not accuracy
                implementation_difficulty="easy",
                estimated_time_minutes=5
            ))
        
        # Speed optimization
        if characteristics.num_samples > 1000:
            recommendations.append(Recommendation(
                type=RecommendationType.PERFORMANCE_OPTIMIZATION,
                title="Enable Speed Optimizations",
                description="Use dynamic padding and parallel data loading",
                confidence=0.85,
                reasoning="Speed optimizations for medium to large datasets",
                parameters={
                    "dynamic_padding": True,
                    "pin_memory": True,
                    "persistent_workers": True
                },
                expected_improvement=0.0,  # Speed improvement
                implementation_difficulty="easy",
                estimated_time_minutes=10
            ))
        
        return recommendations
    
    def _detect_language(self, texts: List[str]) -> str:
        """Simple language detection based on character patterns."""
        # This is a simplified implementation
        # In production, you might use a proper language detection library
        sample_text = " ".join(texts[:10]).lower()
        
        if any(char in sample_text for char in "àáâãäåæçèéêëìíîïðñòóôõöøùúûüýþÿ"):
            return "european"
        elif any(char in sample_text for char in "αβγδεζηθικλμνξοπρστυφχψω"):
            return "greek"
        elif any(char in sample_text for char in "абвгдеёжзийклмнопрстуфхцчшщъыьэюя"):
            return "cyrillic"
        else:
            return "english"
    
    def _detect_domain(self, texts: List[str]) -> List[str]:
        """Detect domain indicators in the text."""
        domains = []
        sample_text = " ".join(texts).lower()
        
        # Domain keywords
        domain_keywords = {
            "medical": ["patient", "diagnosis", "treatment", "medical", "clinical", "hospital", "doctor"],
            "legal": ["court", "law", "legal", "contract", "attorney", "judge", "case"],
            "financial": ["bank", "investment", "financial", "money", "credit", "loan", "market"],
            "scientific": ["research", "study", "analysis", "experiment", "hypothesis", "data", "results"],
            "technical": ["software", "system", "algorithm", "code", "programming", "technical", "computer"]
        }
        
        for domain, keywords in domain_keywords.items():
            if sum(1 for keyword in keywords if keyword in sample_text) >= 2:
                domains.append(domain)
        
        return domains
    
    def get_comprehensive_recommendations(self, characteristics: DataCharacteristics) -> Dict[str, List[Recommendation]]:
        """Get comprehensive recommendations across all categories."""
        return {
            "model_selection": self.get_model_recommendations(characteristics),
            "hyperparameter_tuning": self.get_hyperparameter_recommendations(characteristics),
            "performance_optimization": self.get_performance_optimization_recommendations(characteristics)
        }
