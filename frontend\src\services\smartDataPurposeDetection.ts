/**
 * smartDataPurposeDetection.ts
 * 
 * Enhanced data purpose detection service with sophisticated analysis
 * and recommendations for optimal data usage patterns.
 */

import { UploadedFile } from "./fileUploadApi";
import { DataPurpose, DataPurposeSuggestion } from "./unifiedDataManager";

interface ColumnAnalysis {
  name: string;
  type: 'text' | 'categorical' | 'numeric' | 'datetime' | 'boolean';
  uniqueValues: number;
  nullCount: number;
  sampleValues: any[];
  textLength?: {
    min: number;
    max: number;
    avg: number;
  };
  isTextColumn: boolean;
  isLabelColumn: boolean;
  isIdColumn: boolean;
  confidence: number;
}

interface DataQualityMetrics {
  completeness: number;
  consistency: number;
  validity: number;
  uniqueness: number;
  overall: number;
}

interface ClassificationTypeDetection {
  type: 'binary' | 'multiclass' | 'multilabel' | 'hierarchical' | 'flat';
  confidence: number;
  evidence: string[];
  classCount?: number;
  classDistribution?: Record<string, number>;
  imbalanceRatio?: number;
}

export class SmartDataPurposeDetection {
  
  /**
   * Analyze file and provide enhanced purpose suggestions
   */
  static analyzeFile(fileInfo: UploadedFile): DataPurposeSuggestion & {
    columnAnalysis: ColumnAnalysis[];
    qualityMetrics: DataQualityMetrics;
    classificationTypeDetection: ClassificationTypeDetection | null;
    optimizationRecommendations: string[];
  } {
    const columnAnalysis = this.analyzeColumns(fileInfo);
    const qualityMetrics = this.calculateQualityMetrics(fileInfo, columnAnalysis);
    const classificationTypeDetection = this.detectClassificationType(fileInfo, columnAnalysis);
    
    const baseSuggestion = this.generateBaseSuggestions(fileInfo, columnAnalysis, qualityMetrics);
    const optimizationRecommendations = this.generateOptimizationRecommendations(
      fileInfo, 
      columnAnalysis, 
      qualityMetrics, 
      classificationTypeDetection
    );

    return {
      ...baseSuggestion,
      columnAnalysis,
      qualityMetrics,
      classificationTypeDetection,
      optimizationRecommendations
    };
  }

  /**
   * Analyze individual columns with advanced heuristics
   */
  private static analyzeColumns(fileInfo: UploadedFile): ColumnAnalysis[] {
    if (!fileInfo.columns || !fileInfo.preview) {
      return [];
    }

    return fileInfo.columns.map(columnName => {
      const values = fileInfo.preview.map(row => row[columnName]).filter(v => v != null);
      const uniqueValues = new Set(values).size;
      const nullCount = fileInfo.preview.length - values.length;
      const sampleValues = Array.from(new Set(values)).slice(0, 10);

      // Analyze text characteristics
      const textLengths = values
        .filter(v => typeof v === 'string')
        .map(v => v.length);
      
      const textLength = textLengths.length > 0 ? {
        min: Math.min(...textLengths),
        max: Math.max(...textLengths),
        avg: textLengths.reduce((a, b) => a + b, 0) / textLengths.length
      } : undefined;

      // Column type detection
      const type = this.detectColumnType(values, textLength);
      
      // Purpose detection
      const { isTextColumn, isLabelColumn, isIdColumn, confidence } = this.detectColumnPurpose(
        columnName, 
        values, 
        uniqueValues, 
        fileInfo.num_rows, 
        type,
        textLength
      );

      return {
        name: columnName,
        type,
        uniqueValues,
        nullCount,
        sampleValues,
        textLength,
        isTextColumn,
        isLabelColumn,
        isIdColumn,
        confidence
      };
    });
  }

  /**
   * Detect column data type
   */
  private static detectColumnType(values: any[], textLength?: { min: number; max: number; avg: number }): ColumnAnalysis['type'] {
    if (values.length === 0) return 'text';

    // Check for numeric
    const numericValues = values.filter(v => !isNaN(Number(v)) && v !== '');
    if (numericValues.length / values.length > 0.8) {
      return 'numeric';
    }

    // Check for boolean
    const booleanValues = values.filter(v => 
      typeof v === 'boolean' || 
      (typeof v === 'string' && ['true', 'false', '1', '0', 'yes', 'no'].includes(v.toLowerCase()))
    );
    if (booleanValues.length / values.length > 0.8) {
      return 'boolean';
    }

    // Check for datetime
    const dateValues = values.filter(v => {
      if (typeof v !== 'string') return false;
      const date = new Date(v);
      return !isNaN(date.getTime()) && v.length > 8;
    });
    if (dateValues.length / values.length > 0.8) {
      return 'datetime';
    }

    // Check for text vs categorical
    if (textLength && textLength.avg > 50) {
      return 'text';
    }

    const uniqueRatio = new Set(values).size / values.length;
    if (uniqueRatio < 0.1) {
      return 'categorical';
    }

    return 'text';
  }

  /**
   * Detect column purpose with advanced heuristics
   */
  private static detectColumnPurpose(
    columnName: string, 
    values: any[], 
    uniqueValues: number, 
    totalRows: number,
    type: ColumnAnalysis['type'],
    textLength?: { min: number; max: number; avg: number }
  ): { isTextColumn: boolean; isLabelColumn: boolean; isIdColumn: boolean; confidence: number } {
    const name = columnName.toLowerCase();
    const uniqueRatio = uniqueValues / totalRows;

    // ID column detection
    const isIdColumn = (
      uniqueRatio > 0.95 || 
      name.includes('id') || 
      name.includes('index') ||
      (type === 'numeric' && uniqueValues === totalRows)
    );

    // Text column detection
    const textKeywords = ['text', 'content', 'message', 'description', 'comment', 'review', 'body', 'summary'];
    const nameBasedTextScore = textKeywords.some(keyword => name.includes(keyword)) ? 0.8 : 0;
    const lengthBasedTextScore = textLength && textLength.avg > 100 ? 0.7 : textLength && textLength.avg > 20 ? 0.4 : 0;
    const typeBasedTextScore = type === 'text' ? 0.5 : 0;
    
    const textScore = Math.max(nameBasedTextScore, lengthBasedTextScore + typeBasedTextScore);
    const isTextColumn = textScore > 0.5 && !isIdColumn;

    // Label column detection
    const labelKeywords = ['label', 'category', 'class', 'type', 'sentiment', 'tag', 'target', 'outcome'];
    const nameBasedLabelScore = labelKeywords.some(keyword => name.includes(keyword)) ? 0.8 : 0;
    const categoricalScore = type === 'categorical' && uniqueRatio < 0.2 ? 0.6 : 0;
    const uniquenessScore = uniqueValues >= 2 && uniqueValues <= 50 ? 0.4 : 0;
    
    const labelScore = Math.max(nameBasedLabelScore, categoricalScore + uniquenessScore);
    const isLabelColumn = labelScore > 0.5 && !isIdColumn && !isTextColumn;

    // Calculate overall confidence
    const confidence = Math.max(
      isTextColumn ? textScore : 0,
      isLabelColumn ? labelScore : 0,
      isIdColumn ? 0.9 : 0
    );

    return { isTextColumn, isLabelColumn, isIdColumn, confidence };
  }

  /**
   * Calculate data quality metrics
   */
  private static calculateQualityMetrics(fileInfo: UploadedFile, columnAnalysis: ColumnAnalysis[]): DataQualityMetrics {
    if (columnAnalysis.length === 0) {
      return { completeness: 0, consistency: 0, validity: 0, uniqueness: 0, overall: 0 };
    }

    // Completeness: percentage of non-null values
    const totalCells = fileInfo.num_rows * columnAnalysis.length;
    const nullCells = columnAnalysis.reduce((sum, col) => sum + col.nullCount, 0);
    const completeness = ((totalCells - nullCells) / totalCells) * 100;

    // Consistency: how well column types are detected
    const consistencyScores = columnAnalysis.map(col => col.confidence);
    const consistency = (consistencyScores.reduce((a, b) => a + b, 0) / consistencyScores.length) * 100;

    // Validity: percentage of valid data based on type detection
    const validity = columnAnalysis.reduce((sum, col) => {
      const validRatio = (col.uniqueValues + col.nullCount <= fileInfo.num_rows) ? 1 : 0.5;
      return sum + validRatio;
    }, 0) / columnAnalysis.length * 100;

    // Uniqueness: appropriate level of uniqueness for each column type
    const uniqueness = columnAnalysis.reduce((sum, col) => {
      const uniqueRatio = col.uniqueValues / fileInfo.num_rows;
      let score = 1;
      
      if (col.isIdColumn && uniqueRatio < 0.95) score = 0.3;
      else if (col.isLabelColumn && (uniqueRatio > 0.5 || uniqueRatio < 0.01)) score = 0.5;
      else if (col.isTextColumn && uniqueRatio < 0.1) score = 0.7;
      
      return sum + score;
    }, 0) / columnAnalysis.length * 100;

    const overall = (completeness + consistency + validity + uniqueness) / 4;

    return { completeness, consistency, validity, uniqueness, overall };
  }

  /**
   * Detect classification type from data
   */
  private static detectClassificationType(fileInfo: UploadedFile, columnAnalysis: ColumnAnalysis[]): ClassificationTypeDetection | null {
    const labelColumns = columnAnalysis.filter(col => col.isLabelColumn);
    
    if (labelColumns.length === 0) {
      return null;
    }

    const primaryLabelColumn = labelColumns[0];
    const classCount = primaryLabelColumn.uniqueValues;
    
    // Get class distribution from preview data
    const classDistribution: Record<string, number> = {};
    if (fileInfo.preview) {
      fileInfo.preview.forEach(row => {
        const label = row[primaryLabelColumn.name];
        if (label) {
          classDistribution[label] = (classDistribution[label] || 0) + 1;
        }
      });
    }

    const counts = Object.values(classDistribution);
    const imbalanceRatio = counts.length > 0 ? Math.max(...counts) / Math.min(...counts) : 1;

    // Determine classification type
    let type: ClassificationTypeDetection['type'];
    let confidence = 0.8;
    const evidence: string[] = [];

    if (classCount === 2) {
      type = 'binary';
      evidence.push(`Detected 2 classes: ${Object.keys(classDistribution).join(', ')}`);
    } else if (classCount <= 10) {
      type = 'multiclass';
      evidence.push(`Detected ${classCount} classes for multi-class classification`);
    } else if (classCount <= 50) {
      type = 'flat';
      evidence.push(`Detected ${classCount} classes suitable for flat classification`);
    } else {
      type = 'hierarchical';
      evidence.push(`Large number of classes (${classCount}) suggests hierarchical structure`);
      confidence = 0.6;
    }

    // Check for multi-label indicators
    if (labelColumns.length > 1) {
      type = 'multilabel';
      evidence.push(`Multiple label columns detected: ${labelColumns.map(c => c.name).join(', ')}`);
    }

    // Add quality indicators
    if (imbalanceRatio > 5) {
      evidence.push(`Class imbalance detected (ratio: ${imbalanceRatio.toFixed(1)}:1)`);
      confidence *= 0.9;
    }

    return {
      type,
      confidence,
      evidence,
      classCount,
      classDistribution,
      imbalanceRatio
    };
  }

  /**
   * Generate base purpose suggestions
   */
  private static generateBaseSuggestions(
    fileInfo: UploadedFile, 
    columnAnalysis: ColumnAnalysis[], 
    qualityMetrics: DataQualityMetrics
  ): DataPurposeSuggestion {
    const textColumns = columnAnalysis.filter(col => col.isTextColumn);
    const labelColumns = columnAnalysis.filter(col => col.isLabelColumn);
    
    const hasLabels = labelColumns.length > 0;
    const hasText = textColumns.length > 0;
    const canUseForTraining = hasLabels && hasText && fileInfo.num_rows >= 50;
    const canUseForClassification = hasText;

    const suggestedPurposes: DataPurpose[] = ['analysis'];
    const recommendations: string[] = [];
    const warnings: string[] = [];

    if (canUseForTraining) {
      suggestedPurposes.push('training');
      recommendations.push(
        `Suitable for training with ${textColumns.length} text column(s) and ${labelColumns.length} label column(s)`
      );
    }

    if (canUseForClassification) {
      suggestedPurposes.push('classification');
      recommendations.push(`Can be used for classification with ${textColumns.length} text column(s)`);
    }

    // Quality-based warnings
    if (qualityMetrics.overall < 60) {
      warnings.push(`Data quality score is ${qualityMetrics.overall.toFixed(0)}% - consider data cleaning`);
    }

    if (fileInfo.num_rows < 100) {
      warnings.push('Small dataset (< 100 rows) may not be suitable for training');
    }

    if (fileInfo.num_rows < 1000 && canUseForTraining) {
      warnings.push('Consider using LLM inference for better results with small datasets');
    }

    return {
      canUseForTraining,
      canUseForClassification,
      hasLabels,
      suggestedPurposes,
      recommendations,
      warnings
    };
  }

  /**
   * Generate optimization recommendations
   */
  private static generateOptimizationRecommendations(
    fileInfo: UploadedFile,
    columnAnalysis: ColumnAnalysis[],
    qualityMetrics: DataQualityMetrics,
    classificationTypeDetection: ClassificationTypeDetection | null
  ): string[] {
    const recommendations: string[] = [];

    // Data size recommendations
    if (fileInfo.num_rows < 500) {
      recommendations.push('Consider collecting more data for better model performance');
    } else if (fileInfo.num_rows > 10000) {
      recommendations.push('Large dataset detected - consider data sampling for faster training');
    }

    // Column recommendations
    const textColumns = columnAnalysis.filter(col => col.isTextColumn);
    const labelColumns = columnAnalysis.filter(col => col.isLabelColumn);
    
    if (textColumns.length === 0) {
      recommendations.push('No clear text columns detected - verify your data contains text for classification');
    } else if (textColumns.length > 3) {
      recommendations.push('Multiple text columns detected - consider combining or selecting the most relevant one');
    }

    if (labelColumns.length === 0 && fileInfo.num_rows > 50) {
      recommendations.push('No label columns detected - consider using LLM inference for unlabeled data');
    }

    // Quality recommendations
    if (qualityMetrics.completeness < 90) {
      recommendations.push('Consider handling missing values to improve data completeness');
    }

    // Classification type recommendations
    if (classificationTypeDetection) {
      if (classificationTypeDetection.imbalanceRatio && classificationTypeDetection.imbalanceRatio > 3) {
        recommendations.push('Class imbalance detected - consider balancing techniques or stratified sampling');
      }

      if (classificationTypeDetection.classCount && classificationTypeDetection.classCount > 20) {
        recommendations.push('Many classes detected - consider hierarchical classification or class grouping');
      }
    }

    return recommendations;
  }
}
