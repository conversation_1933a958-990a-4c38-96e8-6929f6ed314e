# Multi-Class Classification Guide

## Overview

The Multi-Class Classification workflow in ClassyWeb enables you to build models that can classify text into one of multiple mutually exclusive categories. This guide covers the complete workflow from data preparation to model deployment.

## Table of Contents

1. [Getting Started](#getting-started)
2. [Data Requirements](#data-requirements)
3. [Workflow Steps](#workflow-steps)
4. [Training Configuration](#training-configuration)
5. [Strategy Selection](#strategy-selection)
6. [Results Analysis](#results-analysis)
7. [Deployment Options](#deployment-options)
8. [Best Practices](#best-practices)
9. [Troubleshooting](#troubleshooting)
10. [API Reference](#api-reference)

## Getting Started

### Prerequisites

- ClassyWeb account with appropriate license
- Training data in CSV format
- Text data with corresponding class labels

### Quick Start

1. **Upload Data**: Upload your CSV file with text and label columns
2. **Configure**: Select text and label columns, choose strategy
3. **Train**: Configure training parameters and start training
4. **Classify**: Use the trained model to classify new data
5. **Deploy**: Export or deploy your model for production use

## Data Requirements

### File Format
- **Supported formats**: CSV, Excel, JSON
- **Required columns**: 
  - Text column (containing the text to classify)
  - Label column (containing the class labels)

### Data Quality Guidelines

#### Minimum Requirements
- **Classes**: At least 3 distinct classes
- **Samples per class**: Minimum 10 samples per class
- **Total samples**: Recommended 100+ samples for basic models

#### Recommended Guidelines
- **Classes**: 3-50 classes for optimal performance
- **Samples per class**: 50+ samples per class
- **Total samples**: 1000+ samples for production models
- **Class balance**: Avoid extreme imbalances (>10:1 ratio)

### Example Data Structure

```csv
text,category
"This movie was absolutely fantastic!",positive
"The product quality is poor",negative
"Average experience, nothing special",neutral
"Outstanding customer service",positive
"Terrible delivery experience",negative
```

## Workflow Steps

### 1. Data Upload and Validation

The workflow begins with uploading your dataset:

- **Single File Upload**: Upload one file containing both text and labels
- **Dual File Upload**: Upload separate files for training and classification
- **Automatic Validation**: System validates data format and quality
- **Class Detection**: Automatically detects unique classes in your data

### 2. Data Configuration

Configure your data settings:

- **Text Column**: Select the column containing text data
- **Label Column**: Select the column containing class labels
- **Label Format**: Choose between single column or multi-column labels
- **Data Split**: Configure train/validation split (default: 80/20)

### 3. Method Selection

Choose your approach:

- **Custom Training**: Train a new model from scratch
- **LLM Inference**: Use pre-trained language models for classification

### 4. Training Configuration (Custom Training)

Configure training parameters:

#### Basic Settings
- **Model Name**: Give your model a descriptive name
- **Base Model**: Choose from available transformer models
- **Strategy**: Select classification strategy (softmax, OvR, OvO)

#### Advanced Settings
- **Epochs**: Number of training iterations (1-20)
- **Batch Size**: Training batch size (8-128)
- **Learning Rate**: Optimizer learning rate (1e-6 to 1e-2)
- **Validation Split**: Percentage for validation (10-50%)

#### Optimization
- **Unsloth**: Enable GPU acceleration (recommended)
- **Mixed Precision**: Use FP16 for faster training
- **Gradient Checkpointing**: Save memory during training
- **Early Stopping**: Stop training when performance plateaus

### 5. Model Training

Monitor training progress:

- **Real-time Progress**: Live updates on training progress
- **Epoch Information**: Current epoch, loss, and accuracy
- **Time Estimation**: Estimated time remaining
- **Error Recovery**: Automatic retry on temporary failures

### 6. Classification

Use your trained model:

- **Batch Classification**: Classify multiple texts at once
- **Real-time Classification**: Single text classification
- **Confidence Scores**: Get prediction confidence levels
- **Strategy Metadata**: Access strategy-specific information

### 7. Results Analysis

Comprehensive results visualization:

- **Performance Metrics**: Accuracy, precision, recall, F1-score
- **Confusion Matrix**: Detailed prediction breakdown
- **Per-Class Metrics**: Individual class performance
- **Visualization Charts**: Interactive charts and graphs

### 8. Model Deployment

Deploy your model:

- **Local Export**: Download model files
- **API Deployment**: Create REST API endpoints
- **Batch Processing**: Setup for large-scale processing
- **Cloud Deployment**: Deploy to cloud platforms
- **Edge Deployment**: Optimize for mobile/edge devices

## Training Configuration

### Model Selection

#### Available Models
- **DistilBERT**: Fast and efficient, good for most tasks
- **BERT Base**: Balanced performance and speed
- **RoBERTa**: Enhanced BERT with better training
- **ELECTRA**: Fast training with good performance
- **Custom Models**: Upload your own pre-trained models

#### Model Comparison
| Model | Speed | Accuracy | Memory | Best For |
|-------|-------|----------|---------|----------|
| DistilBERT | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ | Quick prototyping |
| BERT Base | ⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐ | Balanced tasks |
| RoBERTa | ⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐ | High accuracy needs |
| ELECTRA | ⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐ | Fast training |

### Hyperparameter Tuning

#### Learning Rate Guidelines
- **Small datasets** (< 1K samples): 2e-5 to 5e-5
- **Medium datasets** (1K-10K samples): 1e-5 to 3e-5
- **Large datasets** (> 10K samples): 5e-6 to 2e-5

#### Batch Size Guidelines
- **GPU Memory < 8GB**: Batch size 8-16
- **GPU Memory 8-16GB**: Batch size 16-32
- **GPU Memory > 16GB**: Batch size 32-64

#### Epoch Guidelines
- **Well-balanced data**: 3-5 epochs
- **Imbalanced data**: 5-10 epochs
- **Small datasets**: 5-15 epochs
- **Large datasets**: 2-5 epochs

## Strategy Selection

### Available Strategies

#### 1. Softmax (Default)
- **Best for**: Balanced datasets, moderate number of classes
- **Pros**: Fast training, interpretable probabilities
- **Cons**: Struggles with imbalanced data
- **Recommended when**: Classes are balanced, < 20 classes

#### 2. One-vs-Rest (OvR)
- **Best for**: Imbalanced datasets, many classes
- **Pros**: Handles imbalance well, scales to many classes
- **Cons**: Slower training, less calibrated probabilities
- **Recommended when**: Class imbalance > 3:1, > 10 classes

#### 3. One-vs-One (OvO)
- **Best for**: Small datasets, many classes
- **Pros**: Good for small datasets, robust to outliers
- **Cons**: Quadratic complexity, slower inference
- **Recommended when**: < 1K samples, > 20 classes

### Strategy Comparison

| Strategy | Training Speed | Inference Speed | Memory Usage | Imbalance Handling |
|----------|---------------|-----------------|--------------|-------------------|
| Softmax | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐ |
| OvR | ⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| OvO | ⭐⭐ | ⭐⭐ | ⭐⭐ | ⭐⭐⭐⭐ |

### Automatic Strategy Recommendation

The system automatically recommends strategies based on:

- **Number of classes**: More classes favor OvR or OvO
- **Class balance**: Imbalanced data favors OvR
- **Dataset size**: Small datasets favor OvO
- **Performance requirements**: Speed requirements favor Softmax

## Results Analysis

### Performance Metrics

#### Overall Metrics
- **Accuracy**: Percentage of correct predictions
- **Macro F1**: Unweighted average F1 across classes
- **Weighted F1**: Sample-weighted average F1 across classes
- **Training Time**: Total time spent training
- **Model Size**: Size of the trained model

#### Per-Class Metrics
- **Precision**: True positives / (True positives + False positives)
- **Recall**: True positives / (True positives + False negatives)
- **F1-Score**: Harmonic mean of precision and recall
- **Support**: Number of samples in each class

### Visualization Options

#### 1. Bar Charts
- Compare metrics across classes
- Identify best and worst performing classes
- Visualize class distribution

#### 2. Confusion Matrix
- Detailed prediction breakdown
- Identify common misclassifications
- Understand model behavior

#### 3. Radar Charts
- Multi-dimensional performance view
- Compare multiple metrics simultaneously
- Identify performance patterns

#### 4. Strategy Comparison
- Compare different strategies side-by-side
- Evaluate trade-offs between approaches
- Make informed strategy decisions

### Export Options

#### Supported Formats
- **CSV**: Tabular data export
- **Excel**: Formatted spreadsheet
- **JSON**: Structured data format
- **PDF**: Professional report format
- **PNG**: Chart and visualization export

## Deployment Options

### Local Export

Download your trained model for offline use:

#### Export Formats
- **PyTorch**: Native PyTorch format (.pt)
- **ONNX**: Cross-platform format (.onnx)
- **HuggingFace**: HuggingFace Transformers format
- **TensorFlow**: TensorFlow SavedModel format

#### Export Configuration
- **Include Tokenizer**: Bundle tokenizer with model
- **Include Metadata**: Add training configuration and metrics
- **Optimization**: Optimize for inference speed
- **Documentation**: Generate usage documentation

### API Deployment

Create REST API endpoints for real-time inference:

#### API Features
- **Authentication**: Secure API access with keys
- **Rate Limiting**: Control usage and costs
- **Auto-scaling**: Handle variable load
- **Monitoring**: Track usage and performance

#### API Configuration
- **Confidence Threshold**: Minimum prediction confidence
- **Max Predictions**: Number of top predictions to return
- **Return Probabilities**: Include class probabilities
- **Strategy Metadata**: Include strategy information

### Batch Processing

Setup for large-scale batch classification:

#### Batch Features
- **Parallel Processing**: Multi-worker processing
- **Progress Tracking**: Monitor batch progress
- **Error Handling**: Robust error recovery
- **Output Formats**: Multiple output formats

#### Batch Configuration
- **Batch Size**: Number of samples per batch
- **Parallel Workers**: Number of concurrent workers
- **Output Format**: CSV, JSON, or Parquet
- **Error Handling**: Skip errors or fail fast

### Cloud Deployment

Deploy to major cloud platforms:

#### Supported Platforms
- **AWS**: Amazon Web Services
- **GCP**: Google Cloud Platform
- **Azure**: Microsoft Azure
- **Custom**: Private cloud deployments

#### Cloud Features
- **Auto-scaling**: Automatic resource scaling
- **Load Balancing**: Distribute traffic efficiently
- **Monitoring**: Comprehensive monitoring and logging
- **Security**: Enterprise-grade security

### Edge Deployment

Optimize for mobile and edge devices:

#### Edge Features
- **Model Quantization**: Reduce model size
- **Hardware Optimization**: Optimize for specific hardware
- **Offline Capability**: Run without internet connection
- **Low Latency**: Minimize inference time

## Best Practices

### Data Preparation

1. **Clean Your Data**
   - Remove duplicates and inconsistencies
   - Handle missing values appropriately
   - Ensure consistent labeling

2. **Balance Your Classes**
   - Aim for balanced class distribution
   - Use data augmentation for minority classes
   - Consider class weighting for imbalanced data

3. **Validate Your Labels**
   - Review label consistency
   - Use multiple annotators for quality
   - Implement inter-annotator agreement checks

### Model Training

1. **Start Simple**
   - Begin with default settings
   - Use recommended strategies
   - Iterate based on results

2. **Monitor Training**
   - Watch for overfitting
   - Use early stopping
   - Validate on held-out data

3. **Experiment Systematically**
   - Change one parameter at a time
   - Document your experiments
   - Use version control for models

### Performance Optimization

1. **Choose the Right Strategy**
   - Consider your data characteristics
   - Test multiple strategies
   - Use automatic recommendations

2. **Optimize Hyperparameters**
   - Start with recommended values
   - Use systematic search methods
   - Consider computational constraints

3. **Handle Class Imbalance**
   - Use appropriate strategies (OvR)
   - Apply class weighting
   - Consider data augmentation

### Production Deployment

1. **Test Thoroughly**
   - Validate on diverse test data
   - Test edge cases and errors
   - Monitor performance metrics

2. **Plan for Scale**
   - Consider expected load
   - Plan for peak usage
   - Implement proper monitoring

3. **Maintain Your Models**
   - Monitor model drift
   - Retrain periodically
   - Update with new data

## Troubleshooting

### Common Issues

#### Training Issues

**Problem**: Training fails with out-of-memory error
**Solution**: 
- Reduce batch size
- Enable gradient checkpointing
- Use a smaller model
- Reduce sequence length

**Problem**: Training is very slow
**Solution**:
- Enable Unsloth acceleration
- Use mixed precision (FP16)
- Increase batch size if memory allows
- Use a faster model (DistilBERT)

**Problem**: Model overfits quickly
**Solution**:
- Reduce number of epochs
- Enable early stopping
- Increase validation split
- Add regularization

#### Data Issues

**Problem**: Poor performance on imbalanced data
**Solution**:
- Use One-vs-Rest strategy
- Apply class weighting
- Augment minority classes
- Collect more balanced data

**Problem**: Low accuracy across all classes
**Solution**:
- Check data quality and labeling
- Increase training data
- Try different models
- Adjust hyperparameters

**Problem**: Good training accuracy, poor validation accuracy
**Solution**:
- Reduce overfitting (early stopping, regularization)
- Increase training data
- Improve data quality
- Check for data leakage

#### Deployment Issues

**Problem**: API responses are slow
**Solution**:
- Optimize model for inference
- Use model quantization
- Implement caching
- Scale horizontally

**Problem**: High memory usage in production
**Solution**:
- Use ONNX format
- Enable model quantization
- Implement model sharing
- Use edge deployment

### Error Recovery

The system includes automatic error recovery for:

- **Network timeouts**: Automatic retry with backoff
- **Rate limiting**: Intelligent retry scheduling
- **Temporary server errors**: Automatic recovery attempts
- **Memory issues**: Graceful degradation and suggestions

### Getting Help

1. **Documentation**: Check this guide and API documentation
2. **Examples**: Review provided examples and tutorials
3. **Community**: Join the ClassyWeb community forum
4. **Support**: Contact technical support for assistance

## API Reference

### Training API

```python
# Start training
response = start_universal_training({
    "file_id": "your-file-id",
    "text_column": "text",
    "label_column": "category",
    "classification_type": "multi-class",
    "config": {
        "model_name": "distilbert-base-uncased",
        "num_epochs": 3,
        "batch_size": 16,
        "learning_rate": 2e-5,
        "strategy": "softmax"
    }
})
```

### Classification API

```python
# Classify text
response = start_universal_inference({
    "model_id": "your-model-id",
    "file_id": "your-data-file-id",
    "text_column": "text",
    "classification_type": "multi-class"
})
```

### Deployment API

```python
# Deploy model
response = deploy_model({
    "model_id": "your-model-id",
    "deployment_type": "api",
    "config": {
        "name": "My Multi-Class API",
        "enable_auth": True,
        "confidence_threshold": 0.5
    }
})
```

---

For more detailed examples and advanced usage, see the [Examples Documentation](./examples/multiclass-examples.md).
