"""User authentication migration

Revision ID: 002
Revises: 001
Create Date: 2023-07-02

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import sqlite


# revision identifiers, used by Alembic.
revision = '002'
down_revision = '001'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Check if users table exists
    from sqlalchemy.engine.reflection import Inspector
    from sqlalchemy import create_engine
    from app import config

    engine = create_engine(config.DATABASE_URL)
    inspector = Inspector.from_engine(engine)

    # Only proceed if users table exists
    if 'users' in inspector.get_table_names():
        # Get existing columns
        columns = [col['name'] for col in inspector.get_columns('users')]

        with op.batch_alter_table('users', schema=None) as batch_op:
            # Make username and hashed_password nullable
            batch_op.alter_column('username',
                   existing_type=sa.VARCHAR(length=50),
                   nullable=True)
            batch_op.alter_column('hashed_password',
                   existing_type=sa.VARCHAR(length=100),
                   nullable=True)

            # Add new columns if they don't exist
            if 'oauth_provider' not in columns:
                batch_op.add_column(sa.Column('oauth_provider', sa.String(length=20), nullable=True))
            if 'oauth_user_id' not in columns:
                batch_op.add_column(sa.Column('oauth_user_id', sa.String(length=100), nullable=True))
            if 'first_name' not in columns:
                batch_op.add_column(sa.Column('first_name', sa.String(length=50), nullable=True))
            if 'last_name' not in columns:
                batch_op.add_column(sa.Column('last_name', sa.String(length=50), nullable=True))
            if 'profile_picture' not in columns:
                batch_op.add_column(sa.Column('profile_picture', sa.String(length=255), nullable=True))
            if 'is_verified' not in columns:
                batch_op.add_column(sa.Column('is_verified', sa.Boolean(), nullable=True, server_default='0'))
            if 'verification_token' not in columns:
                batch_op.add_column(sa.Column('verification_token', sa.String(length=100), nullable=True))
            if 'verification_token_expires' not in columns:
                batch_op.add_column(sa.Column('verification_token_expires', sa.DateTime(), nullable=True))
            if 'reset_token' not in columns:
                batch_op.add_column(sa.Column('reset_token', sa.String(length=100), nullable=True))
            if 'reset_token_expires' not in columns:
                batch_op.add_column(sa.Column('reset_token_expires', sa.DateTime(), nullable=True))
            if 'last_login' not in columns:
                batch_op.add_column(sa.Column('last_login', sa.DateTime(), nullable=True))

            # Create index if it doesn't exist
            indexes = [idx['name'] for idx in inspector.get_indexes('users')]
            if 'ix_users_oauth_user_id' not in indexes:
                batch_op.create_index(op.f('ix_users_oauth_user_id'), ['oauth_user_id'], unique=False)


def downgrade() -> None:
    # Check if users table exists
    from sqlalchemy.engine.reflection import Inspector
    from sqlalchemy import create_engine
    from app import config

    engine = create_engine(config.DATABASE_URL)
    inspector = Inspector.from_engine(engine)

    # Only proceed if users table exists
    if 'users' in inspector.get_table_names():
        # Get existing columns
        columns = [col['name'] for col in inspector.get_columns('users')]

        with op.batch_alter_table('users', schema=None) as batch_op:
            # Drop index if it exists
            indexes = [idx['name'] for idx in inspector.get_indexes('users')]
            if 'ix_users_oauth_user_id' in indexes:
                batch_op.drop_index(op.f('ix_users_oauth_user_id'))

            # Drop columns if they exist
            if 'last_login' in columns:
                batch_op.drop_column('last_login')
            if 'reset_token_expires' in columns:
                batch_op.drop_column('reset_token_expires')
            if 'reset_token' in columns:
                batch_op.drop_column('reset_token')
            if 'verification_token_expires' in columns:
                batch_op.drop_column('verification_token_expires')
            if 'verification_token' in columns:
                batch_op.drop_column('verification_token')
            if 'is_verified' in columns:
                batch_op.drop_column('is_verified')
            if 'profile_picture' in columns:
                batch_op.drop_column('profile_picture')
            if 'last_name' in columns:
                batch_op.drop_column('last_name')
            if 'first_name' in columns:
                batch_op.drop_column('first_name')
            if 'oauth_user_id' in columns:
                batch_op.drop_column('oauth_user_id')
            if 'oauth_provider' in columns:
                batch_op.drop_column('oauth_provider')

            # Make username and hashed_password non-nullable
            batch_op.alter_column('hashed_password',
                   existing_type=sa.VARCHAR(length=100),
                   nullable=False)
            batch_op.alter_column('username',
                   existing_type=sa.VARCHAR(length=50),
                   nullable=False)
