/**
 * FlatTrainingConfig.tsx
 *
 * Training configuration component for flat classification with scalability optimization.
 * Features large dataset handling, memory optimization, and performance tuning options.
 */

import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Slider } from "@/components/ui/slider";
import { Switch } from "@/components/ui/switch";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Tabs, TabsContent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { useToast } from "@/hooks/use-toast";
import {
  Settings,
  Database,
  Zap,
  Brain,
  BarChart3,
  Info,
  AlertCircle,
  CheckCircle2,
  Sliders,
  Activity,
  HardDrive,
  Cpu,
  MemoryStick
} from "lucide-react";

export interface FlatTrainingConfig {
  // Base training parameters
  modelName: string;
  baseModel: string;
  maxLength: number;
  
  // Training parameters
  numEpochs: number;
  batchSize: number;
  learningRate: number;
  validationSplit: number;
  
  // Advanced parameters
  warmupSteps: number;
  weightDecay: number;
  gradientAccumulationSteps: number;
  
  // Hardware optimization
  useUnsloth: boolean;
  fp16: boolean;
  gradientCheckpointing: boolean;
  
  // Early stopping
  enableEarlyStopping: boolean;
  patience: number;
  minDelta: number;
  
  // Flat classification specific parameters
  scalabilityMode: 'standard' | 'large_dataset' | 'streaming';
  memoryOptimization: boolean;
  batchProcessing: boolean;
  
  // Large dataset handling
  chunkSize: number;
  parallelProcessing: boolean;
  diskCaching: boolean;
  
  // Performance optimization
  modelCompression: boolean;
  quantization: '8bit' | '16bit' | 'none';
  pruning: boolean;
  
  // Advanced flat features
  maxWorkers: number;
  cacheSize: number;
  streamingThreshold: number;
}

interface FlatTrainingConfigProps {
  onConfigChange: (config: FlatTrainingConfig) => void;
  onSave: (config: FlatTrainingConfig) => void;
  initialConfig?: Partial<FlatTrainingConfig>;
  userJourney: 'beginner' | 'expert';
  datasetSize: number;
  estimatedTrainingTime?: string;
}

const DEFAULT_CONFIG: FlatTrainingConfig = {
  // Base parameters
  modelName: "flat-classifier",
  baseModel: "distilbert-base-uncased",
  maxLength: 512,
  
  // Training parameters
  numEpochs: 3,
  batchSize: 16,
  learningRate: 2e-5,
  validationSplit: 0.2,
  
  // Advanced parameters
  warmupSteps: 500,
  weightDecay: 0.01,
  gradientAccumulationSteps: 1,
  
  // Hardware optimization
  useUnsloth: true,
  fp16: true,
  gradientCheckpointing: true,
  
  // Early stopping
  enableEarlyStopping: true,
  patience: 2,
  minDelta: 0.001,
  
  // Flat specific
  scalabilityMode: 'standard',
  memoryOptimization: true,
  batchProcessing: false,
  
  // Large dataset handling
  chunkSize: 10000,
  parallelProcessing: true,
  diskCaching: false,
  
  // Performance optimization
  modelCompression: false,
  quantization: 'none',
  pruning: false,
  
  // Advanced features
  maxWorkers: 4,
  cacheSize: 1000,
  streamingThreshold: 100000
};

const TRANSFORMER_MODELS = [
  { value: "distilbert-base-uncased", label: "DistilBERT Base", description: "Fast and efficient" },
  { value: "bert-base-uncased", label: "BERT Base", description: "Balanced performance" },
  { value: "roberta-base", label: "RoBERTa Base", description: "High accuracy" },
  { value: "albert-base-v2", label: "ALBERT Base", description: "Memory efficient" },
  { value: "electra-base-discriminator", label: "ELECTRA Base", description: "Fast training" }
];

export const FlatTrainingConfig: React.FC<FlatTrainingConfigProps> = ({
  onConfigChange,
  onSave,
  initialConfig = {},
  userJourney,
  datasetSize,
  estimatedTrainingTime
}) => {
  const { toast } = useToast();
  const [config, setConfig] = useState<FlatTrainingConfig>({
    ...DEFAULT_CONFIG,
    ...initialConfig
  });
  const [activeTab, setActiveTab] = useState('basic');
  const [isValidating, setIsValidating] = useState(false);
  const [validationErrors, setValidationErrors] = useState<string[]>([]);

  // Auto-configure based on dataset size
  useEffect(() => {
    if (datasetSize > 0) {
      let updates: Partial<FlatTrainingConfig> = {};
      
      if (datasetSize > 100000) {
        // Large dataset optimizations
        updates = {
          scalabilityMode: 'large_dataset',
          batchProcessing: true,
          chunkSize: 50000,
          memoryOptimization: true,
          diskCaching: true,
          batchSize: 8, // Smaller batch size for memory
          gradientAccumulationSteps: 4 // Compensate with accumulation
        };
      } else if (datasetSize > 50000) {
        // Medium dataset optimizations
        updates = {
          scalabilityMode: 'standard',
          batchProcessing: true,
          chunkSize: 20000,
          memoryOptimization: true,
          batchSize: 16
        };
      }
      
      if (Object.keys(updates).length > 0) {
        setConfig(prev => ({ ...prev, ...updates }));
      }
    }
  }, [datasetSize]);

  useEffect(() => {
    onConfigChange(config);
  }, [config, onConfigChange]);

  const updateConfig = (updates: Partial<FlatTrainingConfig>) => {
    setConfig(prev => ({ ...prev, ...updates }));
  };

  const validateConfig = () => {
    const errors: string[] = [];
    
    if (!config.modelName.trim()) {
      errors.push("Model name is required");
    }
    
    if (config.numEpochs < 1 || config.numEpochs > 100) {
      errors.push("Number of epochs must be between 1 and 100");
    }
    
    if (config.batchSize < 1 || config.batchSize > 128) {
      errors.push("Batch size must be between 1 and 128");
    }
    
    if (config.learningRate <= 0 || config.learningRate > 1) {
      errors.push("Learning rate must be between 0 and 1");
    }
    
    if (config.chunkSize < 1000 || config.chunkSize > 1000000) {
      errors.push("Chunk size must be between 1,000 and 1,000,000");
    }
    
    setValidationErrors(errors);
    return errors.length === 0;
  };

  const handleSave = () => {
    if (validateConfig()) {
      onSave(config);
      toast({
        title: "Configuration saved",
        description: "Flat classification training configuration has been saved successfully"
      });
    } else {
      toast({
        title: "Validation failed",
        description: "Please fix the configuration errors before saving",
        variant: "destructive"
      });
    }
  };

  const getScalabilityDescription = (mode: string) => {
    switch (mode) {
      case 'standard':
        return "Standard processing for datasets up to 50K samples. Good balance of speed and memory usage.";
      case 'large_dataset':
        return "Optimized for large datasets (50K+ samples). Uses chunking and memory optimization.";
      case 'streaming':
        return "Streaming mode for very large datasets (1M+ samples). Processes data in real-time.";
      default:
        return "";
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold flex items-center gap-2">
            <Settings className="w-5 h-5" />
            Flat Classification Training Configuration
          </h3>
          <p className="text-sm text-muted-foreground">
            Configure scalability and performance optimization for flat classification
          </p>
        </div>
        {estimatedTrainingTime && (
          <Badge variant="outline" className="flex items-center gap-1">
            <Activity className="w-3 h-3" />
            Est. {estimatedTrainingTime}
          </Badge>
        )}
      </div>

      {/* Dataset Analysis */}
      <Alert>
        <Database className="h-4 w-4" />
        <AlertDescription>
          <div className="space-y-2">
            <div>
              <strong>Dataset Size:</strong> {datasetSize.toLocaleString()} samples
              {datasetSize > 100000 && (
                <Badge variant="secondary" className="ml-2">
                  Large Dataset
                </Badge>
              )}
            </div>
            <div className="text-sm">
              <strong>Recommended Mode:</strong> {config.scalabilityMode.replace('_', ' ').toUpperCase()}
            </div>
          </div>
        </AlertDescription>
      </Alert>

      {/* Validation Errors */}
      {validationErrors.length > 0 && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            <ul className="list-disc list-inside space-y-1">
              {validationErrors.map((error, index) => (
                <li key={index}>{error}</li>
              ))}
            </ul>
          </AlertDescription>
        </Alert>
      )}

      {/* Configuration Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="basic">Basic</TabsTrigger>
          <TabsTrigger value="scalability">Scalability</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="optimization">Optimization</TabsTrigger>
        </TabsList>

        {/* Basic Configuration */}
        <TabsContent value="basic" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Brain className="w-4 h-4" />
                Model Configuration
              </CardTitle>
              <CardDescription>
                Basic model and training parameters
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="modelName">Model Name</Label>
                  <Input
                    id="modelName"
                    value={config.modelName}
                    onChange={(e) => updateConfig({ modelName: e.target.value })}
                    placeholder="Enter model name"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="baseModel">Base Model</Label>
                  <Select
                    value={config.baseModel}
                    onValueChange={(value) => updateConfig({ baseModel: value })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {TRANSFORMER_MODELS.map((model) => (
                        <SelectItem key={model.value} value={model.value}>
                          <div className="flex flex-col">
                            <span>{model.label}</span>
                            <span className="text-xs text-muted-foreground">{model.description}</span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="grid grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="numEpochs">Epochs</Label>
                  <Input
                    id="numEpochs"
                    type="number"
                    min="1"
                    max="100"
                    value={config.numEpochs}
                    onChange={(e) => updateConfig({ numEpochs: parseInt(e.target.value) || 1 })}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="batchSize">Batch Size</Label>
                  <Input
                    id="batchSize"
                    type="number"
                    min="1"
                    max="128"
                    value={config.batchSize}
                    onChange={(e) => updateConfig({ batchSize: parseInt(e.target.value) || 1 })}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="learningRate">Learning Rate</Label>
                  <Input
                    id="learningRate"
                    type="number"
                    step="0.00001"
                    min="0.00001"
                    max="1"
                    value={config.learningRate}
                    onChange={(e) => updateConfig({ learningRate: parseFloat(e.target.value) || 0.00001 })}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label>Validation Split: {(config.validationSplit * 100).toFixed(0)}%</Label>
                <Slider
                  value={[config.validationSplit]}
                  onValueChange={([value]) => updateConfig({ validationSplit: value })}
                  min={0.1}
                  max={0.4}
                  step={0.05}
                  className="w-full"
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Scalability Configuration */}
        <TabsContent value="scalability" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Database className="w-4 h-4" />
                Scalability Configuration
              </CardTitle>
              <CardDescription>
                Configure handling for large datasets and performance optimization
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-4">
                {(['standard', 'large_dataset', 'streaming'] as const).map((mode) => (
                  <Card
                    key={mode}
                    className={`cursor-pointer transition-all ${
                      config.scalabilityMode === mode
                        ? 'ring-2 ring-primary bg-primary/5'
                        : 'hover:bg-muted/50'
                    }`}
                    onClick={() => updateConfig({ scalabilityMode: mode })}
                  >
                    <CardContent className="p-4">
                      <div className="flex items-start gap-3">
                        <div className={`p-2 rounded-lg ${
                          config.scalabilityMode === mode
                            ? 'bg-primary text-white'
                            : 'bg-muted'
                        }`}>
                          <Database className="w-4 h-4" />
                        </div>
                        <div className="flex-1">
                          <h4 className="font-medium">{mode.replace('_', ' ').toUpperCase()}</h4>
                          <p className="text-sm text-muted-foreground mt-1">
                            {getScalabilityDescription(mode)}
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="chunkSize">Chunk Size</Label>
                  <Input
                    id="chunkSize"
                    type="number"
                    min="1000"
                    max="1000000"
                    value={config.chunkSize}
                    onChange={(e) => updateConfig({ chunkSize: parseInt(e.target.value) || 10000 })}
                  />
                  <p className="text-xs text-muted-foreground">
                    Number of samples processed per chunk
                  </p>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="maxWorkers">Max Workers</Label>
                  <Input
                    id="maxWorkers"
                    type="number"
                    min="1"
                    max="16"
                    value={config.maxWorkers}
                    onChange={(e) => updateConfig({ maxWorkers: parseInt(e.target.value) || 1 })}
                  />
                  <p className="text-xs text-muted-foreground">
                    Number of parallel processing workers
                  </p>
                </div>
              </div>

              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="parallelProcessing">Parallel Processing</Label>
                    <p className="text-xs text-muted-foreground">Enable multi-core processing</p>
                  </div>
                  <Switch
                    id="parallelProcessing"
                    checked={config.parallelProcessing}
                    onCheckedChange={(checked) => updateConfig({ parallelProcessing: checked })}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="diskCaching">Disk Caching</Label>
                    <p className="text-xs text-muted-foreground">Cache processed data to disk</p>
                  </div>
                  <Switch
                    id="diskCaching"
                    checked={config.diskCaching}
                    onCheckedChange={(checked) => updateConfig({ diskCaching: checked })}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="batchProcessing">Batch Processing</Label>
                    <p className="text-xs text-muted-foreground">Process data in batches for efficiency</p>
                  </div>
                  <Switch
                    id="batchProcessing"
                    checked={config.batchProcessing}
                    onCheckedChange={(checked) => updateConfig({ batchProcessing: checked })}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Save Button */}
      <div className="flex justify-end">
        <Button onClick={handleSave} className="flex items-center gap-2">
          <CheckCircle2 className="w-4 h-4" />
          Save Configuration
        </Button>
      </div>
    </div>
  );
};
