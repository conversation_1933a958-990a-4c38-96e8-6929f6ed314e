// frontend/src/types/auth.ts

// User registration request
export interface UserRegisterRequest {
  email: string;
  password: string;
  username?: string;
  first_name?: string;
  last_name?: string;
}

// User login request
export interface UserLoginRequest {
  email: string;
  password: string;
}

// Token response
export interface TokenResponse {
  access_token: string;
  refresh_token?: string;
  token_type: string;
  expires_in?: number;
}

// Refresh token request
export interface RefreshTokenRequest {
  refresh_token: string;
}

// User response
export interface UserResponse {
  id: number;
  email: string;
  username?: string;
  first_name?: string;
  last_name?: string;
  profile_picture?: string;
  is_active: boolean;
  is_verified: boolean;
  oauth_provider?: string;
  created_at: string;
  last_login?: string;
  bio?: string;
  job_title?: string;
  company?: string;
  website?: string;
  location?: string;
  theme_preference?: string;
}

// Password reset request
export interface PasswordResetRequest {
  email: string;
}

// Password reset confirmation request
export interface PasswordResetConfirmRequest {
  token: string;
  new_password: string;
}

// Email verification request
export interface EmailVerificationRequest {
  token: string;
}

// User update request
export interface UserUpdateRequest {
  username?: string;
  first_name?: string;
  last_name?: string;
  bio?: string;
  job_title?: string;
  company?: string;
  website?: string;
  location?: string;
  theme_preference?: string;
}

// Password change request
export interface PasswordChangeRequest {
  current_password: string;
  new_password: string;
}

// Timezone information
export interface TimezoneInfo {
  timestamp: string;
  localTime: string;
  timezone: string;
  offset: number;
  offsetStr: string;
  isDST: boolean;
  codeLength?: number;
}

// Google auth request
export interface GoogleAuthRequest {
  code: string;
  tzInfo?: TimezoneInfo;
}

// Message response
export interface MessageResponse {
  message: string;
}

// File activity item
export interface FileActivityItem {
  id: string;
  filename: string;
  created_at: string;
  file_size: number;
  num_rows: number;
  columns?: string[];
}

// Task activity item
export interface TaskActivityItem {
  id: string;
  task_type: string;
  status: string;
  created_at: string;
  completed_at?: string;
  input_file_id: string;
  result_file_path?: string;
  message?: string;
}

// User activity response
export interface UserActivityResponse {
  files_count: number;
  tasks_count: number;
  recent_files: FileActivityItem[];
  recent_tasks: TaskActivityItem[];
}

// File update request
export interface FileUpdateRequest {
  filename: string;
}

// User preferences update request
export interface UserPreferencesUpdateRequest {
  theme_preference?: string;
  notification_preferences?: Record<string, boolean>;
}
