import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { X, Plus, FileText, Tag } from "lucide-react";

interface ColumnInfo {
  type: string;
  unique_count: number;
  total_count: number;
  uniqueness_ratio: number;
  sample_values: any[];
  is_potential_text: boolean;
  is_potential_label: boolean;
}

interface ColumnSelectorProps {
  columns: Record<string, ColumnInfo>;
  selectedTextColumns: string[];
  selectedLabelColumns: string[];
  onTextColumnsChange: (columns: string[]) => void;
  onLabelColumnsChange: (columns: string[]) => void;
  onContinue?: () => void;
  onSkip?: () => void;
  showContinueButton?: boolean;
  showSkipButton?: boolean;
}

export const ColumnSelector = ({
  columns,
  selectedTextColumns = [],
  selectedLabelColumns = [],
  onTextColumnsChange,
  onLabelColumnsChange,
  onContinue,
  onSkip,
  showContinueButton = true,
  showSkipButton = false
}: ColumnSelectorProps) => {
  const availableColumns = Object.keys(columns || {});

  const addTextColumn = (column: string) => {
    if (!selectedTextColumns?.includes(column)) {
      onTextColumnsChange([...(selectedTextColumns || []), column]);
    }
  };

  const removeTextColumn = (column: string) => {
    onTextColumnsChange((selectedTextColumns || []).filter(col => col !== column));
  };

  const addLabelColumn = (column: string) => {
    if (!selectedLabelColumns?.includes(column)) {
      onLabelColumnsChange([...(selectedLabelColumns || []), column]);
    }
  };

  const removeLabelColumn = (column: string) => {
    onLabelColumnsChange((selectedLabelColumns || []).filter(col => col !== column));
  };

  const getColumnPriority = (col: string, isText: boolean) => {
    const columnInfo = columns?.[col];
    if (!columnInfo) return 2; // Lowest priority if no column info
    if (isText && columnInfo.is_potential_text) return 0;
    if (!isText && columnInfo.is_potential_label) return 0;
    return 1;
  };

  const canContinue = (selectedTextColumns?.length || 0) > 0 && (selectedLabelColumns?.length || 0) > 0;

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 rounded-lg bg-primary/10 flex items-center justify-center">
            <FileText className="w-5 h-5 text-primary" />
          </div>
          <div>
            <CardTitle>Select Columns</CardTitle>
            <CardDescription>
              Choose which columns contain the text to classify and the labels
            </CardDescription>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Text Columns Selection */}
        <div className="space-y-3">
          <div className="flex items-center gap-2">
            <FileText className="w-4 h-4 text-primary" />
            <Label className="text-sm font-medium">Text Columns</Label>
            <Badge variant="outline" className="text-xs">Required</Badge>
          </div>
          
          <div className="flex flex-wrap gap-2 mb-2">
            {(selectedTextColumns || []).map(col => (
              <Badge key={col} variant="secondary" className="flex items-center gap-1">
                {col}
                <X
                  className="w-3 h-3 cursor-pointer hover:text-destructive"
                  onClick={() => removeTextColumn(col)}
                />
              </Badge>
            ))}
          </div>

          <Select value="" onValueChange={addTextColumn}>
            <SelectTrigger>
              <SelectValue placeholder="Add text column" />
            </SelectTrigger>
            <SelectContent>
              {availableColumns
                .filter(col => !(selectedTextColumns || []).includes(col))
                .sort((a, b) => getColumnPriority(a, true) - getColumnPriority(b, true))
                .map(col => {
                  const columnInfo = columns?.[col];
                  const isRecommended = columnInfo?.is_potential_text || false;
                  return (
                    <SelectItem key={col} value={col}>
                      <div className="flex items-center justify-between w-full">
                        <span>{col}</span>
                        {isRecommended && (
                          <Badge variant="outline" className="ml-2 text-xs">Recommended</Badge>
                        )}
                      </div>
                    </SelectItem>
                  );
                })}
            </SelectContent>
          </Select>
        </div>

        {/* Label Columns Selection */}
        <div className="space-y-3">
          <div className="flex items-center gap-2">
            <Tag className="w-4 h-4 text-primary" />
            <Label className="text-sm font-medium">Label Columns</Label>
            <Badge variant="outline" className="text-xs">Required</Badge>
          </div>
          
          <div className="flex flex-wrap gap-2 mb-2">
            {(selectedLabelColumns || []).map(col => (
              <Badge key={col} variant="secondary" className="flex items-center gap-1">
                {col}
                <X
                  className="w-3 h-3 cursor-pointer hover:text-destructive"
                  onClick={() => removeLabelColumn(col)}
                />
              </Badge>
            ))}
          </div>

          <Select value="" onValueChange={addLabelColumn}>
            <SelectTrigger>
              <SelectValue placeholder="Add label column" />
            </SelectTrigger>
            <SelectContent>
              {availableColumns
                .filter(col => !(selectedLabelColumns || []).includes(col))
                .sort((a, b) => getColumnPriority(a, false) - getColumnPriority(b, false))
                .map(col => {
                  const columnInfo = columns?.[col];
                  const isRecommended = columnInfo?.is_potential_label || false;
                  return (
                    <SelectItem key={col} value={col}>
                      <div className="flex items-center justify-between w-full">
                        <span>{col}</span>
                        {isRecommended && (
                          <Badge variant="outline" className="ml-2 text-xs">Recommended</Badge>
                        )}
                      </div>
                    </SelectItem>
                  );
                })}
            </SelectContent>
          </Select>
        </div>

        {/* Column Preview */}
        <div className="p-3 bg-muted/50 rounded-lg">
          <h4 className="text-sm font-medium mb-2">Selection Summary</h4>
          <div className="text-xs text-muted-foreground space-y-1">
            <div>Text Columns: {(selectedTextColumns?.length || 0) > 0 ? (selectedTextColumns || []).join(', ') : 'None selected'}</div>
            <div>Label Columns: {(selectedLabelColumns?.length || 0) > 0 ? (selectedLabelColumns || []).join(', ') : 'None selected'}</div>
          </div>
        </div>

        {(showContinueButton || showSkipButton) && (
          <div className="flex gap-3">
            {showSkipButton && (
              <Button
                variant="outline"
                onClick={onSkip}
                className="flex-1"
              >
                Skip Column Selection
              </Button>
            )}
            {showContinueButton && (
              <Button
                onClick={onContinue}
                disabled={!canContinue}
                className="flex-1"
              >
                Continue to Analysis
              </Button>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
};
