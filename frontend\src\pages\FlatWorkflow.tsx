/**
 * FlatWorkflow.tsx
 *
 * Flat classification workflow page - Phase 4 enhanced implementation
 * Demonstrates integration of unified workflow system, progress persistence, and enhanced guidance
 */

import React, { useState, useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';
import { FlatClassificationWorkflow } from "@/components/classification/FlatClassificationWorkflow";
import { UnifiedWorkflowNavigation, WorkflowStep } from "@/components/workflow/UnifiedWorkflowNavigation";
import { EnhancedGuidanceSystem, GuidanceTour, SmartRecommendation } from "@/components/guidance/EnhancedGuidanceSystem";
import { WorkflowResumeDialog } from "@/components/workflow/WorkflowResumeDialog";
import { useUnifiedWorkflow } from "@/contexts/UnifiedWorkflowContext";
import { useToast } from "@/hooks/use-toast";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { Card, CardContent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import {
  Upload,
  LayoutGrid,
  Settings,
  Brain,
  Zap,
  BarChart3,
  Download,
  Info,
  Play,
  RotateCcw
} from 'lucide-react';

const FlatWorkflow = () => {
  const [searchParams] = useSearchParams();
  const { toast } = useToast();
  const {
    currentWorkflowId,
    currentStep,
    totalSteps,
    completedSteps,
    canNavigateToStep,
    navigateToStep,
    nextStep,
    previousStep,
    startNewWorkflow,
    resumeWorkflow,
    saveProgress,
    handleError,
    canResume
  } = useUnifiedWorkflow();

  const [showGuidance, setShowGuidance] = useState(true);
  const [activeTour, setActiveTour] = useState<string | null>(null);
  const [useEnhancedWorkflow, setUseEnhancedWorkflow] = useState(false);

  // Define workflow steps for Phase 4 navigation
  const workflowSteps: WorkflowStep[] = [
    {
      id: 1,
      title: "Data Upload",
      description: "Upload your dataset for classification",
      icon: Upload,
      status: currentStep === 1 ? 'current' : completedSteps.includes(1) ? 'complete' : 'pending',
      estimatedTime: '2-3 min',
      helpText: "Upload a CSV file with text data and labels for training or classification"
    },
    {
      id: 2,
      title: "Category Setup",
      description: "Configure categories and data structure",
      icon: LayoutGrid,
      status: currentStep === 2 ? 'current' : completedSteps.includes(2) ? 'complete' : 'pending',
      estimatedTime: '3-5 min',
      helpText: "Select text and label columns, review data structure and categories"
    },
    {
      id: 3,
      title: "Configuration",
      description: "Set training parameters and options",
      icon: Settings,
      status: currentStep === 3 ? 'current' : completedSteps.includes(3) ? 'complete' : 'pending',
      estimatedTime: '2-4 min',
      helpText: "Configure training method, model parameters, and advanced options"
    },
    {
      id: 4,
      title: "Method Selection",
      description: "Choose training approach and model",
      icon: Brain,
      status: currentStep === 4 ? 'current' : completedSteps.includes(4) ? 'complete' : 'pending',
      estimatedTime: '1-2 min',
      helpText: "Select between custom training or LLM inference based on your needs"
    },
    {
      id: 5,
      title: "Training/Inference",
      description: "Train model or run classification",
      icon: Zap,
      status: currentStep === 5 ? 'current' : completedSteps.includes(5) ? 'complete' : 'pending',
      estimatedTime: '5-15 min',
      helpText: "Monitor training progress or classification results in real-time"
    },
    {
      id: 6,
      title: "Results",
      description: "View results and performance metrics",
      icon: BarChart3,
      status: currentStep === 6 ? 'current' : completedSteps.includes(6) ? 'complete' : 'pending',
      estimatedTime: '3-5 min',
      helpText: "Analyze model performance, view metrics, and validate results"
    },
    {
      id: 7,
      title: "Deploy",
      description: "Export and deploy your model",
      icon: Download,
      status: currentStep === 7 ? 'current' : completedSteps.includes(7) ? 'complete' : 'pending',
      estimatedTime: '2-3 min',
      helpText: "Download trained model, export results, and get deployment instructions"
    }
  ];

  // Define guided tours for enhanced guidance
  const availableTours: GuidanceTour[] = [
    {
      id: 'flat-workflow-basics',
      title: 'Flat Classification Basics',
      description: 'Learn the fundamentals of flat classification workflow',
      category: 'beginner',
      estimatedTime: '5 minutes',
      steps: [
        {
          id: 'welcome',
          title: 'Welcome to Flat Classification',
          content: 'This tour will guide you through the flat classification workflow, perfect for general-purpose text classification tasks.',
          target: '.workflow-container',
          position: 'bottom'
        },
        {
          id: 'data-upload',
          title: 'Upload Your Data',
          content: 'Start by uploading a CSV file with your text data and labels. The system will automatically detect the structure.',
          target: '.file-upload-zone',
          position: 'bottom',
          action: 'click'
        },
        {
          id: 'smart-detection',
          title: 'Smart Detection',
          content: 'Our AI will analyze your data and recommend the best classification approach based on your data characteristics.',
          target: '.analysis-section',
          position: 'top'
        }
      ]
    }
  ];

  // Define smart recommendations
  const recommendations: SmartRecommendation[] = [
    {
      id: 'use-enhanced-workflow',
      title: 'Try Enhanced Workflow',
      description: 'The enhanced flat classification workflow includes advanced features like real-time monitoring, class balance analysis, and comprehensive model comparison.',
      confidence: 0.9,
      category: 'optimization',
      action: {
        label: 'Use Enhanced Workflow',
        onClick: () => setUseEnhancedWorkflow(true)
      }
    }
  ];

  // Initialize workflow on mount
  useEffect(() => {
    const resumeId = searchParams.get('resume');
    const stepParam = searchParams.get('step');

    if (resumeId) {
      // Resume existing workflow
      resumeWorkflow(resumeId);
      toast({
        title: "Workflow Resumed",
        description: "Continuing from where you left off.",
      });
    } else if (!currentWorkflowId) {
      // Start new workflow
      const workflowId = startNewWorkflow('flat', 'beginner', workflowSteps);
      toast({
        title: "New Workflow Started",
        description: "Your flat classification workflow has been initialized.",
      });
    }

    if (stepParam) {
      const step = parseInt(stepParam, 10);
      if (!isNaN(step) && canNavigateToStep(step)) {
        navigateToStep(step);
      }
    }
  }, []);

  const handleWorkflowComplete = (results: any) => {
    console.log('Flat classification workflow completed:', results);

    // Save final results
    saveProgress({
      classificationResults: results,
      completedSteps: workflowSteps.map(step => step.id)
    });

    toast({
      title: "Workflow Complete!",
      description: "Your flat classification workflow has been completed successfully.",
    });
  };

  const handleStepChange = (step: number) => {
    navigateToStep(step);

    // Save progress
    saveProgress({
      currentStep: step,
      lastUpdated: new Date().toISOString()
    });
  };

  const handleTourStart = (tourId: string) => {
    setActiveTour(tourId);
  };

  const handleTourComplete = (tourId: string) => {
    setActiveTour(null);
    toast({
      title: "Tour Complete",
      description: "You've completed the guided tour!",
    });
  };

  const handleTourExit = () => {
    setActiveTour(null);
  };

  // Show enhanced workflow if requested
  if (useEnhancedWorkflow) {
    return (
      <div className="min-h-screen bg-background">
        <div className="max-w-7xl mx-auto px-6 py-8">
          {/* Enhanced Guidance System */}
          {showGuidance && (
            <div className="mb-6">
              <EnhancedGuidanceSystem
                availableTours={availableTours}
                activeTour={activeTour}
                onTourStart={handleTourStart}
                onTourComplete={handleTourComplete}
                onTourExit={handleTourExit}
                recommendations={recommendations}
                showRecommendations={true}
                onRecommendationDismiss={(id) => {
                  // Handle recommendation dismissal
                }}
              />
            </div>
          )}

          {/* Unified Navigation */}
          <div className="mb-8">
            <UnifiedWorkflowNavigation
              steps={workflowSteps}
              currentStep={currentStep}
              onStepChange={handleStepChange}
              onNext={nextStep}
              onPrevious={previousStep}
              canNavigateToStep={canNavigateToStep}
              showProgress={true}
              showStepList={true}
              showKeyboardHints={true}
              showEstimatedTime={true}
              showHelpTooltips={true}
            />
          </div>

          {/* Main Workflow Component */}
          <FlatClassificationWorkflow
            onComplete={handleWorkflowComplete}
          />
        </div>
      </div>
    );
  }

  // Original workflow with Phase 4 enhancements
  return (
    <div className="min-h-screen bg-background">
      <div className="max-w-7xl mx-auto px-6 py-8">
        {/* Resume Dialog */}
        {canResume && (
          <WorkflowResumeDialog autoShow={true} />
        )}

        {/* Phase 4 Enhancement Alert */}
        <Alert className="mb-6">
          <Info className="h-4 w-4" />
          <AlertDescription className="flex items-center justify-between">
            <div>
              <strong>Phase 4 Implementation Complete!</strong> This workflow now includes unified navigation,
              progress persistence, enhanced guidance, and error handling.
            </div>
            <div className="flex gap-2">
              <Button
                size="sm"
                onClick={() => setUseEnhancedWorkflow(true)}
              >
                <Play className="w-4 h-4 mr-1" />
                Try Enhanced Version
              </Button>
              {canResume && (
                <WorkflowResumeDialog
                  trigger={
                    <Button variant="outline" size="sm">
                      <RotateCcw className="w-4 h-4 mr-1" />
                      Resume Previous
                    </Button>
                  }
                />
              )}
            </div>
          </AlertDescription>
        </Alert>

        {/* Original Workflow */}
        <FlatClassificationWorkflow
          onComplete={handleWorkflowComplete}
        />
      </div>
    </div>
  );
};

export default FlatWorkflow;
