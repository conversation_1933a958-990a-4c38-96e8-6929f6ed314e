import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import {
  Brain,
  CheckCircle2,
  AlertCircle,
  Database,
  Target,
  Loader2
} from "lucide-react";
import { SmartDataAnalysis, analyzeFileStructure } from "@/services/dataAnalysisApi";
import { uploadFile, UploadedFile, FileUploadProgress } from "@/services/fileUploadApi";

interface SmartDetectionProps {
  fileInfo: UploadedFile;
  onAnalysisComplete: (analysis: SmartDataAnalysis) => void;
  onError: (error: string) => void;
}

interface AnalysisStep {
  id: string;
  title: string;
  description: string;
  progress: number;
  status: 'pending' | 'in-progress' | 'complete' | 'error';
}

export const SmartDetection = ({ fileInfo, onAnalysisComplete, onError }: SmartDetectionProps) => {
  const [analysisSteps, setAnalysisSteps] = useState<AnalysisStep[]>([
    {
      id: 'validation',
      title: 'Validating file format',
      description: 'Checking file structure and data integrity',
      progress: 0,
      status: 'pending'
    },
    {
      id: 'structure',
      title: 'Analyzing data structure',
      description: 'Examining columns, data types, and format',
      progress: 0,
      status: 'pending'
    },
    {
      id: 'patterns',
      title: 'Detecting patterns',
      description: 'Identifying text columns and label structures',
      progress: 0,
      status: 'pending'
    },
    {
      id: 'quality',
      title: 'Assessing data quality',
      description: 'Checking for missing values and duplicates',
      progress: 0,
      status: 'pending'
    },
    {
      id: 'recommendations',
      title: 'Generating recommendations',
      description: 'Suggesting optimal classification approaches',
      progress: 0,
      status: 'pending'
    }
  ]);

  const [currentStepIndex, setCurrentStepIndex] = useState(0);
  const [overallProgress, setOverallProgress] = useState(0);
  const [analysis, setAnalysis] = useState<SmartDataAnalysis | null>(null);


  useEffect(() => {
    startAnalysis();
  }, [fileInfo]);

  const startAnalysis = async () => {
    try {
      // File is already uploaded in unified system
      const uploadedFile = fileInfo;

      // Step 1: File validation (skip upload since it's already done)
      setCurrentStepIndex(0);
      setAnalysisSteps(prev => prev.map((step, index) =>
        index === 0 ? { ...step, status: 'in-progress' } : step
      ));

      // Simulate validation progress
      for (let progress = 0; progress <= 100; progress += 20) {
        await new Promise(resolve => setTimeout(resolve, 100));
        setAnalysisSteps(prev => prev.map((step, index) =>
          index === 0 ? { ...step, progress } : step
        ));
        setOverallProgress(progress / analysisSteps.length);
      }

      setAnalysisSteps(prev => prev.map((step, index) =>
        index === 0 ? { ...step, status: 'complete', progress: 100 } : step
      ));

      // Step 2: Validate file (simulate for now)
      setCurrentStepIndex(1);
      setAnalysisSteps(prev => prev.map((step, index) =>
        index === 1 ? { ...step, status: 'in-progress' } : step
      ));

      // Update validation step to complete
      setAnalysisSteps(prev => prev.map((step, index) =>
        index === 1 ? { ...step, status: 'complete', progress: 100 } : step
      ));
      setOverallProgress(50);

      // Update remaining steps to in-progress then complete
      for (let i = 2; i < analysisSteps.length - 1; i++) {
        setCurrentStepIndex(i);
        setAnalysisSteps(prev => prev.map((step, index) =>
          index === i ? { ...step, status: 'in-progress', progress: 50 } : step
        ));
        setOverallProgress(((i * 100) + 50) / analysisSteps.length);

        // Complete the step
        setAnalysisSteps(prev => prev.map((step, index) =>
          index === i ? { ...step, status: 'complete', progress: 100 } : step
        ));
        setOverallProgress(((i + 1) * 100) / analysisSteps.length);
      }

      // Final step: Generate recommendations using real API
      const finalStepIndex = analysisSteps.length - 1;
      setCurrentStepIndex(finalStepIndex);
      setAnalysisSteps(prev => prev.map((step, index) =>
        index === finalStepIndex ? { ...step, status: 'in-progress' } : step
      ));

      // Call real analysis API
      await performRealAnalysis(uploadedFile.file_id);

      setAnalysisSteps(prev => prev.map((step, index) =>
        index === finalStepIndex ? { ...step, status: 'complete', progress: 100 } : step
      ));
      setOverallProgress(100);

    } catch (error) {
      console.error('Analysis failed:', error);
      onError('Failed to analyze data. Please try again.');

      setAnalysisSteps(prev => prev.map((step, index) =>
        index === currentStepIndex ? { ...step, status: 'error' } : step
      ));
    } finally {
      // Analysis complete
    }
  };

  const performRealAnalysis = async (fileId: string) => {
    try {
      // Call the real backend analysis API
      const analysis = await analyzeFileStructure(fileId);
      setAnalysis(analysis);
      onAnalysisComplete(analysis);
    } catch (error) {
      console.error('Analysis failed:', error);
      setAnalysisSteps(prev => prev.map(step => ({
        ...step,
        status: 'error' as const,
        progress: 0
      })));
      setOverallProgress(0);

      // Call error handler instead of using mock fallback
      onError(error instanceof Error ? error.message : 'Analysis failed');
      return;
    }
  };

  const getStepIcon = (step: AnalysisStep) => {
    switch (step.status) {
      case 'complete':
        return <CheckCircle2 className="w-5 h-5 text-ml-success" />;
      case 'in-progress':
        return <Loader2 className="w-5 h-5 text-primary animate-spin" />;
      case 'error':
        return <AlertCircle className="w-5 h-5 text-destructive" />;
      default:
        return <div className="w-5 h-5 rounded-full border-2 border-muted-foreground" />;
    }
  };

  return (
    <div className="space-y-6">
      {/* Analysis Progress */}
      <Card>
        <CardHeader>
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 rounded-lg bg-primary/10 flex items-center justify-center">
              <Brain className="w-5 h-5 text-primary" />
            </div>
            <div>
              <CardTitle>Smart Data Analysis</CardTitle>
              <CardDescription>
                AI is analyzing your dataset to understand its structure and patterns
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {/* Overall Progress */}
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span className="font-medium">Overall Progress</span>
                <span>{Math.round(overallProgress)}%</span>
              </div>
              <Progress value={overallProgress} className="h-2" />
            </div>

            {/* Analysis Steps */}
            <div className="space-y-4">
              {analysisSteps.map((step) => (
                <div key={step.id} className="flex items-center gap-4">
                  {getStepIcon(step)}
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <h4 className="font-medium text-sm">{step.title}</h4>
                      {step.status === 'in-progress' && (
                        <Badge variant="secondary">{step.progress}%</Badge>
                      )}
                      {step.status === 'complete' && (
                        <Badge className="bg-ml-success/10 text-ml-success">Complete</Badge>
                      )}
                    </div>
                    <p className="text-xs text-muted-foreground">{step.description}</p>
                    {step.status === 'in-progress' && (
                      <Progress value={step.progress} className="h-1 mt-2" />
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Preliminary Results */}
      {analysis && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Card className="bg-muted/30">
            <CardContent className="p-4">
              <h4 className="font-semibold mb-3 flex items-center gap-2">
                <Database className="w-4 h-4 text-primary" />
                Dataset Overview
              </h4>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Total samples:</span>
                  <span className="font-medium">{analysis.preview.total_rows.toLocaleString()}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Features:</span>
                  <span className="font-medium">{analysis.preview.total_columns}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Missing values:</span>
                  <span className={`font-medium ${analysis.data_quality.missing_data_percentage > 5 ? 'text-ml-warning' : 'text-ml-success'}`}>
                    {analysis.data_quality.missing_data_percentage}%
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Data quality:</span>
                  <Badge className="bg-ml-success/10 text-ml-success">Good</Badge>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card className="bg-muted/30">
            <CardContent className="p-4">
              <h4 className="font-semibold mb-3 flex items-center gap-2">
                <Target className="w-4 h-4 text-ml-secondary" />
                Detected Patterns
              </h4>
              <div className="space-y-2 text-sm">
                <div className="flex items-center gap-2">
                  <CheckCircle2 className="w-4 h-4 text-ml-success" />
                  <span>Text column identified</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle2 className="w-4 h-4 text-ml-success" />
                  <span>Label structure detected</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle2 className="w-4 h-4 text-ml-success" />
                  <span>Classification type suggested</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle2 className="w-4 h-4 text-ml-success" />
                  <span>Data format validated</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
};
