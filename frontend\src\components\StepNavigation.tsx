import React from 'react';
import { Button } from "@/components/ui/button";
import { ChevronLeft, ChevronRight } from "lucide-react";

export interface StepNavigationProps {
  showPrevious?: boolean;
  showNext?: boolean;
  nextLabel?: string;
  previousLabel?: string;
  onNext?: () => void;
  onPrevious?: () => void;
  nextDisabled?: boolean;
  previousDisabled?: boolean;
  currentStep?: number;
  totalSteps?: number;
  className?: string;
}

/**
 * Reusable step navigation component for workflow pages
 */
export const StepNavigation: React.FC<StepNavigationProps> = ({
  showPrevious = true,
  showNext = true,
  nextLabel = "Next Step",
  previousLabel = "Previous Step",
  onNext,
  onPrevious,
  nextDisabled = false,
  previousDisabled = false,
  currentStep,
  totalSteps,
  className = ""
}) => {
  return (
    <div className={`flex justify-between items-center pt-6 border-t ${className}`}>
      <div>
        {showPrevious && (
          <Button
            variant="outline"
            onClick={onPrevious}
            disabled={previousDisabled}
            className="flex items-center gap-2"
          >
            <ChevronLeft className="w-4 h-4" />
            {previousLabel}
          </Button>
        )}
      </div>
      
      {currentStep && totalSteps && (
        <div className="text-sm text-muted-foreground">
          Step {currentStep} of {totalSteps}
        </div>
      )}
      
      <div>
        {showNext && (
          <Button
            onClick={onNext}
            disabled={nextDisabled}
            className="flex items-center gap-2"
          >
            {nextLabel}
            <ChevronRight className="w-4 h-4" />
          </Button>
        )}
      </div>
    </div>
  );
};

export default StepNavigation;
