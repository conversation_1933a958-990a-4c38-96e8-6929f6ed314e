"""Universal Classification Platform for ClassyWeb.

This module provides the main platform interface that coordinates:
- Plugin-based classification engines
- Dynamic workflow management
- Multi-tenant resource management
- Advanced analytics and monitoring
"""

import logging
import asyncio
from typing import Dict, List, Any, Optional, Union
from datetime import datetime, timezone
from dataclasses import dataclass

from .plugins.plugin_manager import PluginManager
from .plugins.base_plugin import PluginType, ClassificationPlugin
from .hierarchy_manager import DynamicHierarchyManager
from .services.data_analysis_service import DataStructureDetector

logger = logging.getLogger(__name__)


@dataclass
class ClassificationRequest:
    """Request for classification with universal platform."""
    texts: List[str]
    user_id: int
    tenant_id: Optional[str] = None
    hierarchy_config: Optional[Dict[str, Any]] = None
    classification_type: str = "auto"  # auto, hierarchical, flat
    preferred_engine: Optional[str] = None
    confidence_threshold: float = 0.5
    use_ensemble: bool = False
    max_processing_time_ms: int = 30000
    metadata: Dict[str, Any] = None


@dataclass
class ClassificationResponse:
    """Response from universal classification platform."""
    request_id: str
    results: List[Dict[str, Any]]
    engine_used: str
    processing_time_ms: float
    confidence_scores: List[float]
    metadata: Dict[str, Any]
    recommendations: List[str] = None
    warnings: List[str] = None


class UniversalClassificationPlatform:
    """Main platform class that orchestrates all classification activities."""
    
    def __init__(self):
        self.plugin_manager = PluginManager()
        self.hierarchy_manager = DynamicHierarchyManager()
        self.data_analysis_service = DataStructureDetector()

        # Built-in engines
        self.builtin_engines = {
            "llm_ensemble": None,  # Will be initialized later
            "huggingface": None    # Will be initialized later
        }
        
        # Performance tracking
        self.performance_metrics = {}
        self.usage_analytics = {}
        
        logger.info("Universal Classification Platform initialized")
    
    async def initialize(self):
        """Initialize the platform and all components."""
        logger.info("Initializing Universal Classification Platform...")
        
        # Initialize plugin manager
        await self.plugin_manager.initialize()
        
        # Initialize built-in engines
        await self._initialize_builtin_engines()
        
        # Start performance monitoring
        await self._start_performance_monitoring()
        
        logger.info("Universal Classification Platform ready")
    
    async def classify(self, request: ClassificationRequest) -> ClassificationResponse:
        """Main classification method that handles all types of requests."""
        start_time = datetime.now(timezone.utc)
        request_id = f"req_{int(start_time.timestamp() * 1000)}"
        
        try:
            logger.info(f"Processing classification request {request_id} for user {request.user_id}")
            
            # Analyze data characteristics if not provided
            if not request.metadata:
                request.metadata = await self._analyze_request_data(request)
            
            # Select optimal engine
            engine_name = await self._select_optimal_engine(request)
            
            # Execute classification
            if request.use_ensemble:
                results = await self._execute_ensemble_classification(request, engine_name)
            else:
                results = await self._execute_single_engine_classification(request, engine_name)
            
            # Calculate processing time
            end_time = datetime.now(timezone.utc)
            processing_time_ms = (end_time - start_time).total_seconds() * 1000
            
            # Extract confidence scores
            confidence_scores = [
                result.get('confidence', 0.0) for result in results
            ]
            
            # Generate recommendations
            recommendations = await self._generate_recommendations(request, results, engine_name)
            
            # Track usage analytics
            await self._track_usage_analytics(request, engine_name, processing_time_ms)
            
            response = ClassificationResponse(
                request_id=request_id,
                results=results,
                engine_used=engine_name,
                processing_time_ms=processing_time_ms,
                confidence_scores=confidence_scores,
                metadata=request.metadata,
                recommendations=recommendations
            )
            
            logger.info(f"Classification request {request_id} completed in {processing_time_ms:.2f}ms")
            return response
            
        except Exception as e:
            logger.error(f"Error processing classification request {request_id}: {str(e)}")
            raise
    
    async def get_available_engines(
        self,
        user_id: int,
        tenant_id: Optional[str] = None,
        classification_type: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """Get list of available classification engines for a user."""
        try:
            engines = []
            
            # Add built-in engines
            for engine_name, engine in self.builtin_engines.items():
                if engine:
                    engines.append({
                        "name": engine_name,
                        "type": "builtin",
                        "description": f"Built-in {engine_name} engine",
                        "capabilities": getattr(engine, 'get_capabilities', lambda: {})(),
                        "performance": self.performance_metrics.get(engine_name, {})
                    })
            
            # Add plugin engines
            plugin_engines = await self.plugin_manager.get_available_plugins(
                plugin_type=PluginType.CLASSIFICATION_ENGINE
            )
            
            for plugin_info in plugin_engines:
                engines.append({
                    "name": plugin_info["name"],
                    "type": "plugin",
                    "description": plugin_info["metadata"]["description"],
                    "capabilities": plugin_info["capabilities"],
                    "health": plugin_info["health"],
                    "performance": self.performance_metrics.get(plugin_info["name"], {})
                })
            
            # Filter by classification type if specified
            if classification_type:
                filtered_engines = []
                for engine in engines:
                    capabilities = engine.get("capabilities", {})
                    if classification_type == "hierarchical" and capabilities.get("supports_hierarchical"):
                        filtered_engines.append(engine)
                    elif classification_type == "flat" and capabilities.get("supports_flat"):
                        filtered_engines.append(engine)
                    elif classification_type == "auto":
                        filtered_engines.append(engine)
                engines = filtered_engines
            
            return engines
            
        except Exception as e:
            logger.error(f"Error getting available engines: {str(e)}")
            return []
    
    async def _select_optimal_engine(self, request: ClassificationRequest) -> str:
        """Select the optimal classification engine for a request."""
        try:
            # If user specified preferred engine, use it
            if request.preferred_engine:
                available_engines = await self.get_available_engines(request.user_id)
                engine_names = [e["name"] for e in available_engines]
                if request.preferred_engine in engine_names:
                    return request.preferred_engine
                else:
                    logger.warning(f"Preferred engine {request.preferred_engine} not available")
            
            # Auto-select based on data characteristics
            data_size = len(request.texts)
            has_hierarchy = request.hierarchy_config is not None
            
            # For small datasets with hierarchy, use LLM ensemble
            if data_size < 1000 and has_hierarchy:
                return "llm_ensemble"

            # For large datasets, prefer streaming-capable engines
            if data_size > 10000:
                streaming_engines = await self.plugin_manager.get_available_plugins(
                    plugin_type=PluginType.CLASSIFICATION_ENGINE
                )
                for engine in streaming_engines:
                    if engine["capabilities"].get("supports_streaming", False):
                        return engine["name"]

            # Default to LLM ensemble for most cases
            return "llm_ensemble"
            
        except Exception as e:
            logger.error(f"Error selecting optimal engine: {str(e)}")
            return "llm_ensemble"  # Fallback
    
    async def _execute_single_engine_classification(
        self,
        request: ClassificationRequest,
        engine_name: str
    ) -> List[Dict[str, Any]]:
        """Execute classification using a single engine."""
        try:
            # Check if it's a built-in engine
            if engine_name in self.builtin_engines:
                engine = self.builtin_engines[engine_name]
                if engine_name == "llm_ensemble":
                    # Use LLM ensemble
                    results = []
                    for text in request.texts:
                        result = await engine.classify_text(
                            text,
                            use_all_processors=True
                        )
                        results.append({
                            "text": text,
                            "labels": result.get("slow", {}).get("predictions", []),
                            "confidence": result.get("slow", {}).get("confidence", 0.0),
                            "processing_details": result
                        })
                    return results
            else:
                # Use plugin engine
                return await self.plugin_manager.execute_classification(
                    engine_name,
                    request.texts,
                    request.hierarchy_config,
                    request.confidence_threshold
                )
            
        except Exception as e:
            logger.error(f"Error executing classification with engine {engine_name}: {str(e)}")
            raise
    
    async def _execute_ensemble_classification(
        self,
        request: ClassificationRequest,
        primary_engine: str
    ) -> List[Dict[str, Any]]:
        """Execute ensemble classification using multiple engines."""
        try:
            # Get available engines
            available_engines = await self.get_available_engines(request.user_id)
            
            # Select ensemble engines (primary + 2 others)
            ensemble_engines = [primary_engine]
            for engine in available_engines:
                if (engine["name"] != primary_engine and 
                    len(ensemble_engines) < 3 and
                    engine["health"].get("status") == "healthy"):
                    ensemble_engines.append(engine["name"])
            
            # Execute classification with all engines
            engine_results = {}
            for engine_name in ensemble_engines:
                try:
                    engine_results[engine_name] = await self._execute_single_engine_classification(
                        request, engine_name
                    )
                except Exception as e:
                    logger.error(f"Engine {engine_name} failed in ensemble: {str(e)}")
            
            # Combine results using voting
            return await self._combine_ensemble_results(engine_results, request.confidence_threshold)
            
        except Exception as e:
            logger.error(f"Error executing ensemble classification: {str(e)}")
            # Fallback to single engine
            return await self._execute_single_engine_classification(request, primary_engine)
    
    async def _combine_ensemble_results(
        self,
        engine_results: Dict[str, List[Dict[str, Any]]],
        confidence_threshold: float
    ) -> List[Dict[str, Any]]:
        """Combine results from multiple engines using voting."""
        try:
            if not engine_results:
                return []
            
            # Get the number of texts
            first_engine_results = list(engine_results.values())[0]
            num_texts = len(first_engine_results)
            
            combined_results = []
            
            for text_idx in range(num_texts):
                # Collect predictions from all engines for this text
                all_predictions = {}
                text_content = None
                
                for engine_name, results in engine_results.items():
                    if text_idx < len(results):
                        result = results[text_idx]
                        text_content = result.get("text", "")
                        labels = result.get("labels", [])
                        confidence = result.get("confidence", 0.0)
                        
                        for label in labels:
                            if label not in all_predictions:
                                all_predictions[label] = []
                            all_predictions[label].append(confidence)
                
                # Vote on final predictions
                final_labels = []
                final_confidences = []
                
                for label, confidences in all_predictions.items():
                    # Use average confidence
                    avg_confidence = sum(confidences) / len(confidences)
                    
                    # Require majority vote (more than half of engines)
                    vote_ratio = len(confidences) / len(engine_results)
                    
                    if vote_ratio > 0.5 and avg_confidence >= confidence_threshold:
                        final_labels.append(label)
                        final_confidences.append(avg_confidence)
                
                combined_results.append({
                    "text": text_content,
                    "labels": final_labels,
                    "confidence": max(final_confidences) if final_confidences else 0.0,
                    "ensemble_details": {
                        "engines_used": list(engine_results.keys()),
                        "vote_details": all_predictions
                    }
                })
            
            return combined_results
            
        except Exception as e:
            logger.error(f"Error combining ensemble results: {str(e)}")
            return []
    
    async def _analyze_request_data(self, request: ClassificationRequest) -> Dict[str, Any]:
        """Analyze request data characteristics."""
        try:
            # Basic analysis
            analysis = {
                "text_count": len(request.texts),
                "avg_text_length": sum(len(text) for text in request.texts) / len(request.texts),
                "has_hierarchy": request.hierarchy_config is not None,
                "classification_type": request.classification_type,
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
            
            # Advanced analysis for larger datasets
            if len(request.texts) > 100:
                # Sample analysis for performance
                sample_texts = request.texts[:100]
                # Add more sophisticated analysis here
                analysis["sample_analysis"] = {
                    "complexity_score": self._calculate_text_complexity(sample_texts),
                    "domain_hints": self._detect_domain_hints(sample_texts)
                }
            
            return analysis
            
        except Exception as e:
            logger.error(f"Error analyzing request data: {str(e)}")
            return {"error": str(e)}
    
    def _calculate_text_complexity(self, texts: List[str]) -> float:
        """Calculate text complexity score."""
        try:
            total_score = 0.0
            for text in texts:
                # Simple complexity metrics
                word_count = len(text.split())
                char_count = len(text)
                unique_words = len(set(text.lower().split()))
                
                # Complexity score based on length and vocabulary diversity
                complexity = (word_count * 0.3 + 
                             char_count * 0.001 + 
                             unique_words * 0.5)
                total_score += complexity
            
            return total_score / len(texts) if texts else 0.0
            
        except Exception as e:
            logger.error(f"Error calculating text complexity: {str(e)}")
            return 0.0
    
    def _detect_domain_hints(self, texts: List[str]) -> List[str]:
        """Detect domain hints from text content."""
        try:
            # Simple keyword-based domain detection
            domain_keywords = {
                "medical": ["patient", "diagnosis", "treatment", "symptom", "medical"],
                "legal": ["contract", "law", "legal", "court", "attorney"],
                "technical": ["software", "code", "bug", "feature", "system"],
                "business": ["customer", "sales", "revenue", "market", "business"],
                "academic": ["research", "study", "analysis", "paper", "academic"]
            }
            
            detected_domains = []
            text_content = " ".join(texts).lower()
            
            for domain, keywords in domain_keywords.items():
                keyword_count = sum(1 for keyword in keywords if keyword in text_content)
                if keyword_count >= 2:  # At least 2 keywords found
                    detected_domains.append(domain)
            
            return detected_domains
            
        except Exception as e:
            logger.error(f"Error detecting domain hints: {str(e)}")
            return []
    
    async def _generate_recommendations(
        self,
        request: ClassificationRequest,
        results: List[Dict[str, Any]],
        engine_used: str
    ) -> List[str]:
        """Generate recommendations for improving classification."""
        recommendations = []
        
        try:
            # Analyze confidence scores
            confidences = [result.get('confidence', 0.0) for result in results]
            avg_confidence = sum(confidences) / len(confidences) if confidences else 0.0
            
            if avg_confidence < 0.6:
                recommendations.append("Consider using ensemble classification for better accuracy")
                recommendations.append("Review training data quality and add more examples")
            
            # Check for empty results
            empty_results = sum(1 for result in results if not result.get('labels'))
            if empty_results > len(results) * 0.2:  # More than 20% empty
                recommendations.append("Many texts received no classifications - consider lowering confidence threshold")
            
            # Engine-specific recommendations
            if engine_used == "llm_ensemble":
                recommendations.append("LLM ensemble provides robust classification - results improve with better prompts")
            
            return recommendations
            
        except Exception as e:
            logger.error(f"Error generating recommendations: {str(e)}")
            return []
    
    async def _initialize_builtin_engines(self):
        """Initialize built-in classification engines."""
        try:
            # Dynamic HRM is already initialized
            logger.info("Built-in engines initialized")
            
        except Exception as e:
            logger.error(f"Error initializing built-in engines: {str(e)}")
    
    async def _start_performance_monitoring(self):
        """Start background performance monitoring."""
        logger.info("Starting performance monitoring...")
        
        async def monitor_performance():
            while True:
                try:
                    # Update performance metrics for all engines
                    await self._update_performance_metrics()
                    
                    # Sleep for 1 minute between updates
                    await asyncio.sleep(60)
                    
                except Exception as e:
                    logger.error(f"Error in performance monitoring: {str(e)}")
                    await asyncio.sleep(30)  # Shorter sleep on error
        
        # Start monitoring task in background
        asyncio.create_task(monitor_performance())
    
    async def _update_performance_metrics(self):
        """Update performance metrics for all engines."""
        try:
            # Update metrics for built-in engines
            for engine_name in self.builtin_engines.keys():
                self.performance_metrics[engine_name] = {
                    "avg_response_time_ms": 150,  # Placeholder
                    "success_rate": 0.95,
                    "last_updated": datetime.now(timezone.utc).isoformat()
                }
            
            # Update metrics for plugin engines
            plugin_engines = await self.plugin_manager.get_available_plugins(
                plugin_type=PluginType.CLASSIFICATION_ENGINE
            )
            
            for plugin_info in plugin_engines:
                plugin_name = plugin_info["name"]
                health = plugin_info.get("health", {})
                
                self.performance_metrics[plugin_name] = {
                    "avg_response_time_ms": health.get("avg_response_time", 1000),
                    "success_rate": health.get("success_rate", 0.8),
                    "last_updated": datetime.now(timezone.utc).isoformat()
                }
            
        except Exception as e:
            logger.error(f"Error updating performance metrics: {str(e)}")
    
    async def _track_usage_analytics(
        self,
        request: ClassificationRequest,
        engine_used: str,
        processing_time_ms: float
    ):
        """Track usage analytics for the platform."""
        try:
            # Update usage analytics
            if engine_used not in self.usage_analytics:
                self.usage_analytics[engine_used] = {
                    "total_requests": 0,
                    "total_texts_processed": 0,
                    "total_processing_time_ms": 0.0,
                    "avg_processing_time_ms": 0.0
                }
            
            analytics = self.usage_analytics[engine_used]
            analytics["total_requests"] += 1
            analytics["total_texts_processed"] += len(request.texts)
            analytics["total_processing_time_ms"] += processing_time_ms
            analytics["avg_processing_time_ms"] = (
                analytics["total_processing_time_ms"] / analytics["total_requests"]
            )
            
        except Exception as e:
            logger.error(f"Error tracking usage analytics: {str(e)}")


# Global platform instance
universal_platform = UniversalClassificationPlatform()
