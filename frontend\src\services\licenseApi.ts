/**
 * License API Service for ClassyWeb ML Platform
 * 
 * Provides license validation, feature checking, and usage tracking functionality
 */

import apiClient from './apiClient';

// --- Types ---

export interface UserLicense {
  id: string;
  user_id: number;
  license_type: 'personal' | 'professional' | 'enterprise';
  license_key: string;
  status: 'active' | 'expired' | 'suspended';
  max_models?: number;
  max_monthly_inferences?: number;
  feature_flags: Record<string, any>;
  price_paid_cents?: number;
  billing_cycle: string;
  created_at: string;
  expires_at?: string;
  total_models_trained: number;
  current_month_inferences: number;
}

export interface LicenseFeatures {
  model_export: boolean;
  api_deployment: boolean;
  batch_processing: boolean;
  cloud_deployment: boolean;
  edge_deployment: boolean;
  enterprise_features: boolean;
  max_models: number;
  max_monthly_inferences: number;
  priority_support: boolean;
  custom_branding: boolean;
  advanced_analytics: boolean;
}

export interface UsageStats {
  models_trained: number;
  monthly_inferences: number;
  deployments_active: number;
  api_calls_today: number;
  storage_used_mb: number;
}

export interface LicenseValidationResult {
  valid: boolean;
  feature_available: boolean;
  usage_allowed: boolean;
  error_message?: string;
  upgrade_required?: boolean;
  limits_exceeded?: string[];
}

// --- API Functions ---

/**
 * Get current user's license information
 */
export const getUserLicense = async (): Promise<UserLicense> => {
  try {
    const response = await apiClient.get('/api/license/current');
    return response.data;
  } catch (error: any) {
    console.error('Failed to get user license:', error);

    // Fallback to default license for development/production environments
    // where license service might not be available
    const fallbackLicense: UserLicense = {
      type: 'professional',
      features: [
        'multi_class_classification',
        'custom_training',
        'llm_inference',
        'model_export',
        'api_deployment',
        'batch_processing'
      ],
      limits: {
        max_models: 100,
        max_training_time: 3600,
        max_file_size: 100 * 1024 * 1024, // 100MB
        max_classifications_per_day: 10000
      },
      expires_at: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString() // 1 year from now
    };

    console.warn('Using fallback license due to API error:', fallbackLicense);
    return fallbackLicense;
  }
};

/**
 * Get license features for current user
 */
export const getLicenseFeatures = async (): Promise<LicenseFeatures> => {
  try {
    const response = await apiClient.get('/api/license/features');
    return response.data;
  } catch (error: any) {
    console.error('Failed to get license features:', error);
    throw new Error(error.response?.data?.detail || 'Failed to get license features');
  }
};

/**
 * Get usage statistics for current user
 */
export const getUsageStats = async (): Promise<UsageStats> => {
  try {
    const response = await apiClient.get('/api/license/usage');
    return response.data;
  } catch (error: any) {
    console.error('Failed to get usage stats:', error);
    throw new Error(error.response?.data?.detail || 'Failed to get usage statistics');
  }
};

/**
 * Validate if a specific feature is available for the user
 */
export const validateFeature = async (
  feature: string,
  operation?: string
): Promise<LicenseValidationResult> => {
  try {
    const response = await apiClient.post('/api/license/validate', {
      feature,
      operation
    });
    return response.data;
  } catch (error: any) {
    console.error('Failed to validate feature:', error);
    throw new Error(error.response?.data?.detail || 'Failed to validate feature');
  }
};

/**
 * Update usage for a specific operation
 */
export const updateUsage = async (
  operation: string,
  count: number = 1
): Promise<void> => {
  try {
    await apiClient.post('/api/license/usage/update', {
      operation,
      count
    });
  } catch (error: any) {
    console.error('Failed to update usage:', error);
    throw new Error(error.response?.data?.detail || 'Failed to update usage');
  }
};

/**
 * Get available license tiers and pricing
 */
export const getLicenseTiers = async (): Promise<{
  tiers: Array<{
    name: string;
    price_monthly: number;
    price_yearly: number;
    features: LicenseFeatures;
    description: string;
    popular?: boolean;
  }>;
}> => {
  try {
    const response = await apiClient.get('/api/license/tiers');
    return response.data;
  } catch (error: any) {
    console.error('Failed to get license tiers:', error);
    throw new Error(error.response?.data?.detail || 'Failed to get license tiers');
  }
};

/**
 * Check if user can perform a deployment operation
 */
export const canDeploy = async (deploymentType: string): Promise<LicenseValidationResult> => {
  try {
    const response = await apiClient.post('/api/license/validate-deployment', {
      deployment_type: deploymentType
    });
    return response.data;
  } catch (error: any) {
    console.error('Failed to validate deployment:', error);
    return {
      valid: false,
      feature_available: false,
      usage_allowed: false,
      error_message: error.response?.data?.detail || 'Failed to validate deployment',
      upgrade_required: true
    };
  }
};

/**
 * Get deployment limits for current license
 */
export const getDeploymentLimits = async (): Promise<{
  max_deployments: number;
  current_deployments: number;
  available_types: string[];
  restrictions: Record<string, any>;
}> => {
  try {
    const response = await apiClient.get('/api/license/deployment-limits');
    return response.data;
  } catch (error: any) {
    console.error('Failed to get deployment limits:', error);
    throw new Error(error.response?.data?.detail || 'Failed to get deployment limits');
  }
};

// --- Utility Functions ---

/**
 * Check if a feature is available based on license type
 */
export const isFeatureAvailable = (
  licenseType: string,
  feature: string
): boolean => {
  const featureMatrix = {
    personal: [
      'model_export',
      'batch_processing'
    ],
    professional: [
      'model_export',
      'batch_processing',
      'api_deployment',
      'priority_support'
    ],
    enterprise: [
      'model_export',
      'batch_processing',
      'api_deployment',
      'cloud_deployment',
      'edge_deployment',
      'enterprise_features',
      'priority_support',
      'custom_branding',
      'advanced_analytics'
    ]
  };

  return featureMatrix[licenseType as keyof typeof featureMatrix]?.includes(feature) || false;
};

/**
 * Get feature display information
 */
export const getFeatureInfo = (feature: string) => {
  const featureInfo = {
    model_export: {
      title: 'Model Export',
      description: 'Download trained models for offline use',
      icon: 'Download'
    },
    api_deployment: {
      title: 'API Deployment',
      description: 'Deploy models as REST APIs',
      icon: 'Server'
    },
    batch_processing: {
      title: 'Batch Processing',
      description: 'Process large datasets in batches',
      icon: 'Database'
    },
    cloud_deployment: {
      title: 'Cloud Deployment',
      description: 'Deploy to cloud platforms with auto-scaling',
      icon: 'Cloud'
    },
    edge_deployment: {
      title: 'Edge Deployment',
      description: 'Optimize for mobile and edge devices',
      icon: 'Smartphone'
    },
    enterprise_features: {
      title: 'Enterprise Features',
      description: 'Advanced security and compliance features',
      icon: 'Shield'
    },
    priority_support: {
      title: 'Priority Support',
      description: '24/7 priority customer support',
      icon: 'Headphones'
    },
    custom_branding: {
      title: 'Custom Branding',
      description: 'White-label the platform with your branding',
      icon: 'Palette'
    },
    advanced_analytics: {
      title: 'Advanced Analytics',
      description: 'Detailed usage and performance analytics',
      icon: 'BarChart3'
    }
  };

  return featureInfo[feature as keyof typeof featureInfo] || {
    title: 'Unknown Feature',
    description: 'Feature information not available',
    icon: 'HelpCircle'
  };
};

/**
 * Format license type for display
 */
export const formatLicenseType = (licenseType: string): string => {
  const typeMap = {
    personal: 'Personal',
    professional: 'Professional',
    enterprise: 'Enterprise'
  };

  return typeMap[licenseType as keyof typeof typeMap] || licenseType;
};

/**
 * Get license color for UI display
 */
export const getLicenseColor = (licenseType: string): string => {
  const colorMap = {
    personal: 'blue',
    professional: 'green',
    enterprise: 'purple'
  };

  return colorMap[licenseType as keyof typeof colorMap] || 'gray';
};

/**
 * Check if license is expired
 */
export const isLicenseExpired = (license: UserLicense): boolean => {
  if (!license.expires_at) return false;
  return new Date(license.expires_at) < new Date();
};

/**
 * Get days until license expires
 */
export const getDaysUntilExpiry = (license: UserLicense): number | null => {
  if (!license.expires_at) return null;
  const expiryDate = new Date(license.expires_at);
  const today = new Date();
  const diffTime = expiryDate.getTime() - today.getTime();
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
};

// Export all functions as default
export default {
  getUserLicense,
  getLicenseFeatures,
  getUsageStats,
  validateFeature,
  updateUsage,
  getLicenseTiers,
  canDeploy,
  getDeploymentLimits,
  isFeatureAvailable,
  getFeatureInfo,
  formatLicenseType,
  getLicenseColor,
  isLicenseExpired,
  getDaysUntilExpiry
};
