import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Slider } from "@/components/ui/slider";
import { Switch } from "@/components/ui/switch";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Separator } from "@/components/ui/separator";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { 
  Setting<PERSON>, 
  Brain, 
  Zap, 
  Clock, 
  Target, 
  Cpu, 
  Info,
  ChevronDown,
  ChevronUp,
  Save,
  RotateCcw
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";

export interface HierarchicalTrainingConfig {
  // Model Configuration
  modelName: string;
  baseModel: string;
  maxLength: number;
  
  // Training Parameters
  numEpochs: number;
  batchSize: number;
  learningRate: number;
  validationSplit: number;
  
  // Advanced Parameters
  warmupSteps: number;
  weightDecay: number;
  gradientAccumulationSteps: number;
  
  // Hardware Optimization
  useUnsloth: boolean;
  fp16: boolean;
  gradientCheckpointing: boolean;
  
  // Early Stopping
  enableEarlyStopping: boolean;
  patience: number;
  minDelta: number;
  
  // Hierarchical Specific
  hierarchyWeights: number[];
  constraintEnforcement: boolean;
  levelWiseTraining: boolean;
}

interface HierarchicalTrainingConfigProps {
  onConfigChange: (config: HierarchicalTrainingConfig) => void;
  onSave: (config: HierarchicalTrainingConfig) => void;
  initialConfig?: Partial<HierarchicalTrainingConfig>;
  userJourney: 'beginner' | 'expert';
  hierarchyLevels: number;
  estimatedTrainingTime?: number;
}

const BASE_MODELS = [
  { value: 'distilbert-base-uncased', label: 'DistilBERT Base (Fast)', description: 'Lightweight, good for beginners' },
  { value: 'bert-base-uncased', label: 'BERT Base (Balanced)', description: 'Standard choice, good performance' },
  { value: 'roberta-base', label: 'RoBERTa Base (Robust)', description: 'Better performance, slower training' },
  { value: 'albert-base-v2', label: 'ALBERT Base (Efficient)', description: 'Parameter efficient, good for large datasets' },
  { value: 'deberta-v3-base', label: 'DeBERTa v3 Base (Advanced)', description: 'State-of-the-art performance' }
];

const DEFAULT_CONFIG: HierarchicalTrainingConfig = {
  modelName: '',
  baseModel: 'distilbert-base-uncased',
  maxLength: 512,
  numEpochs: 3,
  batchSize: 16,
  learningRate: 2e-5,
  validationSplit: 0.2,
  warmupSteps: 500,
  weightDecay: 0.01,
  gradientAccumulationSteps: 1,
  useUnsloth: true,
  fp16: true,
  gradientCheckpointing: false,
  enableEarlyStopping: true,
  patience: 3,
  minDelta: 0.001,
  hierarchyWeights: [],
  constraintEnforcement: true,
  levelWiseTraining: false
};

export const HierarchicalTrainingConfig: React.FC<HierarchicalTrainingConfigProps> = ({
  onConfigChange,
  onSave,
  initialConfig,
  userJourney,
  hierarchyLevels,
  estimatedTrainingTime
}) => {
  const { toast } = useToast();
  const [config, setConfig] = useState<HierarchicalTrainingConfig>({
    ...DEFAULT_CONFIG,
    ...initialConfig,
    hierarchyWeights: initialConfig?.hierarchyWeights || Array(hierarchyLevels).fill(1.0)
  });
  const [showAdvanced, setShowAdvanced] = useState(userJourney === 'expert');
  const [estimatedTime, setEstimatedTime] = useState<string>('');

  useEffect(() => {
    // Generate default model name if not provided
    if (!config.modelName) {
      const timestamp = new Date().toISOString().slice(0, 16).replace(/[-:]/g, '');
      setConfig(prev => ({
        ...prev,
        modelName: `hierarchical_model_${timestamp}`
      }));
    }
  }, []);

  useEffect(() => {
    onConfigChange(config);
    calculateEstimatedTime();
  }, [config, onConfigChange]);

  const calculateEstimatedTime = () => {
    // Rough estimation based on epochs, batch size, and model complexity
    const baseTime = config.numEpochs * (config.batchSize / 16) * 2; // minutes
    const modelMultiplier = config.baseModel.includes('distilbert') ? 0.7 : 
                           config.baseModel.includes('albert') ? 0.8 : 1.0;
    const unslothMultiplier = config.useUnsloth ? 0.6 : 1.0;
    
    const totalMinutes = Math.round(baseTime * modelMultiplier * unslothMultiplier);
    setEstimatedTime(`${Math.floor(totalMinutes / 60)}h ${totalMinutes % 60}m`);
  };

  const updateConfig = (updates: Partial<HierarchicalTrainingConfig>) => {
    setConfig(prev => ({ ...prev, ...updates }));
  };

  const resetToDefaults = () => {
    setConfig({
      ...DEFAULT_CONFIG,
      hierarchyWeights: Array(hierarchyLevels).fill(1.0)
    });
    toast({
      title: "Configuration Reset",
      description: "All settings have been reset to default values."
    });
  };

  const handleSave = () => {
    if (!config.modelName.trim()) {
      toast({
        title: "Model Name Required",
        description: "Please provide a name for your model.",
        variant: "destructive"
      });
      return;
    }
    onSave(config);
  };

  return (
    <Card className="border-2 border-gray-200 dark:border-gray-800">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 rounded-lg bg-gray-100 dark:bg-gray-800 flex items-center justify-center">
              <Settings className="w-5 h-5 text-gray-700 dark:text-gray-300" />
            </div>
            <div>
              <CardTitle className="flex items-center gap-2">
                Training Configuration
                <Badge variant="outline">{userJourney}</Badge>
              </CardTitle>
              <CardDescription>
                Configure your hierarchical classification model training parameters
              </CardDescription>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" onClick={resetToDefaults}>
              <RotateCcw className="w-4 h-4 mr-2" />
              Reset
            </Button>
            <Button onClick={handleSave}>
              <Save className="w-4 h-4 mr-2" />
              Save Config
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Model Identity */}
        <div className="space-y-4">
          <div className="flex items-center gap-2">
            <Brain className="w-4 h-4 text-primary" />
            <Label className="text-base font-semibold">Model Identity</Label>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="modelName">Model Name</Label>
              <Input
                id="modelName"
                value={config.modelName}
                onChange={(e) => updateConfig({ modelName: e.target.value })}
                placeholder="Enter a unique model name"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="baseModel">Base Transformer Model</Label>
              <Select value={config.baseModel} onValueChange={(value) => updateConfig({ baseModel: value })}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {BASE_MODELS.map((model) => (
                    <SelectItem key={model.value} value={model.value}>
                      <div className="flex flex-col">
                        <span>{model.label}</span>
                        <span className="text-xs text-muted-foreground">{model.description}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        <Separator />

        {/* Core Training Parameters */}
        <div className="space-y-4">
          <div className="flex items-center gap-2">
            <Target className="w-4 h-4 text-primary" />
            <Label className="text-base font-semibold">Core Training Parameters</Label>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="epochs">Training Epochs</Label>
              <div className="space-y-2">
                <Slider
                  value={[config.numEpochs]}
                  onValueChange={([value]) => updateConfig({ numEpochs: value })}
                  min={1}
                  max={20}
                  step={1}
                  className="w-full"
                />
                <div className="flex justify-between text-xs text-muted-foreground">
                  <span>1</span>
                  <span className="font-medium">{config.numEpochs} epochs</span>
                  <span>20</span>
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="batchSize">Batch Size</Label>
              <Select value={config.batchSize.toString()} onValueChange={(value) => updateConfig({ batchSize: parseInt(value) })}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="8">8 (Low Memory)</SelectItem>
                  <SelectItem value="16">16 (Recommended)</SelectItem>
                  <SelectItem value="32">32 (High Memory)</SelectItem>
                  <SelectItem value="64">64 (Very High Memory)</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="validationSplit">Validation Split</Label>
              <div className="space-y-2">
                <Slider
                  value={[config.validationSplit * 100]}
                  onValueChange={([value]) => updateConfig({ validationSplit: value / 100 })}
                  min={10}
                  max={40}
                  step={5}
                  className="w-full"
                />
                <div className="flex justify-between text-xs text-muted-foreground">
                  <span>10%</span>
                  <span className="font-medium">{Math.round(config.validationSplit * 100)}%</span>
                  <span>40%</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Estimated Training Time */}
        {estimatedTime && (
          <Alert>
            <Clock className="h-4 w-4" />
            <AlertDescription>
              <strong>Estimated Training Time:</strong> {estimatedTime}
              {config.useUnsloth && " (with Unsloth acceleration)"}
            </AlertDescription>
          </Alert>
        )}

        {/* Advanced Configuration */}
        {(userJourney === 'expert' || showAdvanced) && (
          <>
            <Separator />
            <div className="space-y-4">
              <Button
                variant="ghost"
                onClick={() => setShowAdvanced(!showAdvanced)}
                className="flex items-center gap-2 p-0 h-auto"
              >
                {showAdvanced ? <ChevronUp className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />}
                <Label className="text-base font-semibold cursor-pointer">Advanced Configuration</Label>
              </Button>

              {showAdvanced && (
                <Tabs defaultValue="hyperparameters" className="space-y-4">
                  <TabsList className="grid w-full grid-cols-4">
                    <TabsTrigger value="hyperparameters">Hyperparameters</TabsTrigger>
                    <TabsTrigger value="optimization">Optimization</TabsTrigger>
                    <TabsTrigger value="hierarchy">Hierarchy</TabsTrigger>
                    <TabsTrigger value="stopping">Early Stopping</TabsTrigger>
                  </TabsList>

                  <TabsContent value="hyperparameters" className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <div className="flex items-center gap-2">
                          <Label htmlFor="learningRate">Learning Rate</Label>
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Info className="h-4 w-4 text-muted-foreground cursor-help" />
                              </TooltipTrigger>
                              <TooltipContent className="max-w-xs">
                                <p>Controls how much the model weights are updated during training. Lower values (1e-5) are safer but slower, higher values (5e-5) train faster but may be unstable.</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </div>
                        <Select
                          value={config.learningRate.toString()}
                          onValueChange={(value) => updateConfig({ learningRate: parseFloat(value) })}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select learning rate">
                              {config.learningRate === 1e-5 && "1e-5 (Conservative)"}
                              {config.learningRate === 2e-5 && "2e-5 (Recommended)"}
                              {config.learningRate === 3e-5 && "3e-5 (Aggressive)"}
                              {config.learningRate === 5e-5 && "5e-5 (Very Aggressive)"}
                              {![1e-5, 2e-5, 3e-5, 5e-5].includes(config.learningRate) && `${config.learningRate} (Custom)`}
                            </SelectValue>
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="1e-5">1e-5 (Conservative)</SelectItem>
                            <SelectItem value="2e-5">2e-5 (Recommended)</SelectItem>
                            <SelectItem value="3e-5">3e-5 (Aggressive)</SelectItem>
                            <SelectItem value="5e-5">5e-5 (Very Aggressive)</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="warmupSteps">Warmup Steps</Label>
                        <Input
                          id="warmupSteps"
                          type="number"
                          value={config.warmupSteps}
                          onChange={(e) => updateConfig({ warmupSteps: parseInt(e.target.value) || 0 })}
                          min={0}
                          max={2000}
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="weightDecay">Weight Decay</Label>
                        <Select
                          value={config.weightDecay.toString()}
                          onValueChange={(value) => updateConfig({ weightDecay: parseFloat(value) })}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="0">0 (No Regularization)</SelectItem>
                            <SelectItem value="0.01">0.01 (Light)</SelectItem>
                            <SelectItem value="0.1">0.1 (Standard)</SelectItem>
                            <SelectItem value="0.2">0.2 (Heavy)</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="maxLength">Max Sequence Length</Label>
                        <Select
                          value={config.maxLength.toString()}
                          onValueChange={(value) => updateConfig({ maxLength: parseInt(value) })}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="128">128 (Short Texts)</SelectItem>
                            <SelectItem value="256">256 (Medium Texts)</SelectItem>
                            <SelectItem value="512">512 (Long Texts)</SelectItem>
                            <SelectItem value="1024">1024 (Very Long Texts)</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </TabsContent>

                  <TabsContent value="optimization" className="space-y-4">
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <div className="space-y-1">
                          <Label htmlFor="useUnsloth">Enable Unsloth Acceleration</Label>
                          <p className="text-sm text-muted-foreground">
                            GPU acceleration for faster training (recommended)
                          </p>
                        </div>
                        <Switch
                          id="useUnsloth"
                          checked={config.useUnsloth}
                          onCheckedChange={(checked) => updateConfig({ useUnsloth: checked })}
                        />
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="space-y-1">
                          <Label htmlFor="fp16">Mixed Precision (FP16)</Label>
                          <p className="text-sm text-muted-foreground">
                            Reduces memory usage and speeds up training
                          </p>
                        </div>
                        <Switch
                          id="fp16"
                          checked={config.fp16}
                          onCheckedChange={(checked) => updateConfig({ fp16: checked })}
                        />
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="space-y-1">
                          <Label htmlFor="gradientCheckpointing">Gradient Checkpointing</Label>
                          <p className="text-sm text-muted-foreground">
                            Saves memory at the cost of slightly slower training
                          </p>
                        </div>
                        <Switch
                          id="gradientCheckpointing"
                          checked={config.gradientCheckpointing}
                          onCheckedChange={(checked) => updateConfig({ gradientCheckpointing: checked })}
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="gradientAccumulation">Gradient Accumulation Steps</Label>
                        <Select
                          value={config.gradientAccumulationSteps.toString()}
                          onValueChange={(value) => updateConfig({ gradientAccumulationSteps: parseInt(value) })}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="1">1 (No Accumulation)</SelectItem>
                            <SelectItem value="2">2 (Effective Batch Size x2)</SelectItem>
                            <SelectItem value="4">4 (Effective Batch Size x4)</SelectItem>
                            <SelectItem value="8">8 (Effective Batch Size x8)</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </TabsContent>

                  <TabsContent value="hierarchy" className="space-y-4">
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <div className="space-y-1">
                          <Label htmlFor="constraintEnforcement">Constraint Enforcement</Label>
                          <p className="text-sm text-muted-foreground">
                            Enforce hierarchical constraints during training
                          </p>
                        </div>
                        <Switch
                          id="constraintEnforcement"
                          checked={config.constraintEnforcement}
                          onCheckedChange={(checked) => updateConfig({ constraintEnforcement: checked })}
                        />
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="space-y-1">
                          <Label htmlFor="levelWiseTraining">Level-wise Training</Label>
                          <p className="text-sm text-muted-foreground">
                            Train hierarchy levels progressively
                          </p>
                        </div>
                        <Switch
                          id="levelWiseTraining"
                          checked={config.levelWiseTraining}
                          onCheckedChange={(checked) => updateConfig({ levelWiseTraining: checked })}
                        />
                      </div>

                      <div className="space-y-3">
                        <Label>Hierarchy Level Weights</Label>
                        <p className="text-sm text-muted-foreground">
                          Adjust the importance of each hierarchy level during training
                        </p>
                        {config.hierarchyWeights.map((weight, index) => (
                          <div key={index} className="space-y-2">
                            <div className="flex justify-between">
                              <Label>Level {index + 1}</Label>
                              <span className="text-sm text-muted-foreground">{weight.toFixed(1)}</span>
                            </div>
                            <Slider
                              value={[weight]}
                              onValueChange={([value]) => {
                                const newWeights = [...config.hierarchyWeights];
                                newWeights[index] = value;
                                updateConfig({ hierarchyWeights: newWeights });
                              }}
                              min={0.1}
                              max={2.0}
                              step={0.1}
                              className="w-full"
                            />
                          </div>
                        ))}
                      </div>
                    </div>
                  </TabsContent>

                  <TabsContent value="stopping" className="space-y-4">
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <div className="space-y-1">
                          <Label htmlFor="enableEarlyStopping">Enable Early Stopping</Label>
                          <p className="text-sm text-muted-foreground">
                            Stop training when validation performance stops improving
                          </p>
                        </div>
                        <Switch
                          id="enableEarlyStopping"
                          checked={config.enableEarlyStopping}
                          onCheckedChange={(checked) => updateConfig({ enableEarlyStopping: checked })}
                        />
                      </div>

                      {config.enableEarlyStopping && (
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div className="space-y-2">
                            <Label htmlFor="patience">Patience (Epochs)</Label>
                            <Input
                              id="patience"
                              type="number"
                              value={config.patience}
                              onChange={(e) => updateConfig({ patience: parseInt(e.target.value) || 1 })}
                              min={1}
                              max={10}
                            />
                          </div>

                          <div className="space-y-2">
                            <Label htmlFor="minDelta">Minimum Delta</Label>
                            <Input
                              id="minDelta"
                              type="number"
                              step="0.001"
                              value={config.minDelta}
                              onChange={(e) => updateConfig({ minDelta: parseFloat(e.target.value) || 0 })}
                              min={0}
                              max={0.1}
                            />
                          </div>
                        </div>
                      )}
                    </div>
                  </TabsContent>
                </Tabs>
              )}
            </div>
          </>
        )}

        {/* Beginner Mode Advanced Toggle */}
        {userJourney === 'beginner' && !showAdvanced && (
          <div className="text-center">
            <Button
              variant="outline"
              onClick={() => setShowAdvanced(true)}
              className="flex items-center gap-2"
            >
              <Settings className="w-4 h-4" />
              Show Advanced Options
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
