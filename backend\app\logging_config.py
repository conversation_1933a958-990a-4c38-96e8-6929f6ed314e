"""
Logging configuration for the application.
"""
import os
import logging
from logging.handlers import RotatingFileHandler
import sys


class SafeUnicodeFormatter(logging.Formatter):
    """Custom formatter that safely handles Unicode characters."""

    def format(self, record):
        try:
            # Try normal formatting first
            return super().format(record)
        except UnicodeEncodeError:
            # If Unicode encoding fails, replace problematic characters
            try:
                # Get the original message
                msg = str(record.getMessage())
                # Replace problematic Unicode characters with safe alternatives
                safe_msg = msg.encode('ascii', 'replace').decode('ascii')

                # Create a new record with the safe message
                safe_record = logging.LogRecord(
                    record.name, record.levelno, record.pathname, record.lineno,
                    safe_msg, record.args, record.exc_info, record.funcName
                )
                safe_record.created = record.created
                safe_record.msecs = record.msecs
                safe_record.relativeCreated = record.relativeCreated
                safe_record.thread = record.thread
                safe_record.threadName = record.threadName
                safe_record.processName = record.processName
                safe_record.process = record.process

                return super().format(safe_record)
            except Exception:
                # Last resort - return a basic error message
                return f"{record.levelname}: [Unicode encoding error in log message]"

# Create logs directory if it doesn't exist
logs_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'logs')
os.makedirs(logs_dir, exist_ok=True)

# Configure logging
def configure_logging():
    """Configure logging for the application."""
    # Create a safe Unicode formatter
    formatter = SafeUnicodeFormatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    
    # Create a file handler for all logs with UTF-8 encoding
    file_handler = RotatingFileHandler(
        os.path.join(logs_dir, 'app.log'),
        maxBytes=10*1024*1024,  # 10MB
        backupCount=5,
        encoding='utf-8'  # Explicitly set UTF-8 encoding
    )
    file_handler.setLevel(logging.DEBUG)
    file_handler.setFormatter(formatter)

    # Create a file handler for errors only with UTF-8 encoding
    error_handler = RotatingFileHandler(
        os.path.join(logs_dir, 'error.log'),
        maxBytes=10*1024*1024,  # 10MB
        backupCount=5,
        encoding='utf-8'  # Explicitly set UTF-8 encoding
    )
    error_handler.setLevel(logging.ERROR)
    error_handler.setFormatter(formatter)
    
    # Create a console handler with UTF-8 encoding to handle Unicode characters
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(formatter)

    # Set encoding to UTF-8 if possible to handle Unicode characters like emojis
    if hasattr(console_handler.stream, 'reconfigure'):
        try:
            console_handler.stream.reconfigure(encoding='utf-8')
        except Exception:
            # If reconfigure fails, we'll handle Unicode in the log messages themselves
            pass
    
    # Configure the root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.DEBUG)
    
    # Remove existing handlers to avoid duplicates
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # Add the handlers
    root_logger.addHandler(file_handler)
    root_logger.addHandler(error_handler)
    root_logger.addHandler(console_handler)
    
    # Set specific logger levels
    logging.getLogger('sqlalchemy.engine').setLevel(logging.WARNING)
    logging.getLogger('uvicorn').setLevel(logging.INFO)
    logging.getLogger('fastapi').setLevel(logging.INFO)
    
    # Our app loggers - set to DEBUG for detailed logging
    logging.getLogger('app').setLevel(logging.DEBUG)
    logging.getLogger('app.database').setLevel(logging.DEBUG)
    logging.getLogger('app.api').setLevel(logging.DEBUG)
    logging.getLogger('app.cache').setLevel(logging.DEBUG)
    
    logging.info("Logging configured")
