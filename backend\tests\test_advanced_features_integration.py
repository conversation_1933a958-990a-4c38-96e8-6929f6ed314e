"""Comprehensive Integration Tests for ClassyWeb ML Platform Advanced Features.

This module tests advanced classification features:
- Enhanced Flat Classification Engine
- Advanced Training Monitoring System
- Intelligent Assistance System
- Performance Optimization for Large Datasets
"""

import pytest
import asyncio
import numpy as np
import pandas as pd
from typing import Dict, List, Any
import json
import time
import tempfile
import os
from unittest.mock import Mock, patch, AsyncMock

# Import advanced classification components
from app.classification_engines.flat_engine import (
    EnhancedFlatClassificationEngine,
    FlatClassificationMetrics,
    LargeDatasetConfig
)
from app.advanced_training_monitor import (
    AdvancedTrainingMonitor,
    EarlyStoppingConfig,
    TrainingMetrics,
    TrainingStage
)
from app.intelligent_assistant import (
    IntelligentAssistant,
    DataCharacteristics,
    RecommendationType
)
from app.large_dataset_optimizer import (
    LargeDatasetOptimizer,
    StreamingDataLoader,
    BatchProcessor,
    MemoryMonitor
)
from app.classification_engines.base_engine import TrainingConfig, ClassificationType


class TestEnhancedFlatClassificationEngine:
    """Test suite for Enhanced Flat Classification Engine."""
    
    @pytest.fixture
    def sample_data(self):
        """Create sample dataset for testing."""
        np.random.seed(42)
        texts = [
            f"This is sample text {i} for classification testing"
            for i in range(1000)
        ]
        labels = np.random.choice(['positive', 'negative', 'neutral'], 1000)
        return pd.DataFrame({'text': texts, 'label': labels})
    
    @pytest.fixture
    def large_dataset_config(self):
        """Create large dataset configuration."""
        return LargeDatasetConfig(
            chunk_size=100,
            batch_size=32,
            max_workers=2,
            memory_limit_gb=1.0,
            enable_streaming=True
        )
    
    @pytest.fixture
    def engine(self, large_dataset_config):
        """Create enhanced flat classification engine."""
        return EnhancedFlatClassificationEngine(
            classification_type=ClassificationType.FLAT,
            large_dataset_config=large_dataset_config.__dict__,
            enable_gpu_acceleration=False,  # Disable for testing
            mixed_precision=False
        )
    
    def test_engine_initialization(self, engine):
        """Test engine initialization with advanced features."""
        assert engine.classification_type == ClassificationType.FLAT
        assert engine.large_dataset_config is not None
        assert hasattr(engine, 'label_encoder')
        assert hasattr(engine, 'performance_metrics')
        assert hasattr(engine, 'memory_usage_history')
    
    def test_dataset_optimization(self, engine):
        """Test dataset size optimization."""
        # Test small dataset
        small_config = engine.optimize_for_large_dataset(500)
        assert small_config.batch_size <= 5000
        assert not small_config.enable_streaming
        
        # Test large dataset
        large_config = engine.optimize_for_large_dataset(100000)
        assert large_config.batch_size <= 1000
        assert large_config.enable_streaming
        assert large_config.max_workers >= 4
    
    def test_configuration_validation(self, engine, sample_data):
        """Test configuration validation."""
        # Valid configuration
        config = TrainingConfig(
            texts=sample_data['text'].tolist()[:10],
            labels=sample_data['label'].tolist()[:10]
        )
        is_valid, errors = engine.validate_config(config)
        assert is_valid
        assert len(errors) == 0
        
        # Invalid configuration - empty texts
        invalid_config = TrainingConfig(texts=[], labels=[])
        is_valid, errors = engine.validate_config(invalid_config)
        assert not is_valid
        assert len(errors) > 0
    
    @pytest.mark.asyncio
    async def test_data_preparation(self, engine, sample_data):
        """Test optimized data preparation."""
        texts = sample_data['text'].tolist()[:100]
        labels = sample_data['label'].tolist()[:100]
        
        prepared_data = await engine._prepare_data_optimized(texts, labels)
        
        assert 'X_train' in prepared_data
        assert 'X_test' in prepared_data
        assert 'y_train' in prepared_data
        assert 'y_test' in prepared_data
        assert 'class_weights' in prepared_data
        assert 'num_classes' in prepared_data
        assert prepared_data['num_classes'] == 3  # positive, negative, neutral
    
    @pytest.mark.asyncio
    async def test_comprehensive_metrics_calculation(self, engine, sample_data):
        """Test comprehensive metrics calculation."""
        # Setup mock model and tokenizer
        engine.model = Mock()
        engine.tokenizer = Mock()
        engine.class_names = ['positive', 'negative', 'neutral']
        
        # Mock prediction results
        mock_predictions = [
            {
                'predicted_class_index': 0,
                'confidence': 0.8,
                'probabilities': {'positive': 0.8, 'negative': 0.1, 'neutral': 0.1}
            }
        ] * 10
        
        with patch.object(engine, '_predict_batch_optimized', return_value=mock_predictions):
            texts = sample_data['text'].tolist()[:10]
            labels = np.array([0, 1, 2, 0, 1, 2, 0, 1, 2, 0])  # Mock encoded labels
            
            metrics = await engine._calculate_comprehensive_metrics(texts, labels)
            
            assert 'accuracy' in metrics
            assert 'weighted_f1' in metrics
            assert 'macro_f1' in metrics
            assert 'per_class_precision' in metrics
            assert 'confusion_matrix' in metrics
            assert 'processing_time_ms' in metrics


class TestAdvancedTrainingMonitor:
    """Test suite for Advanced Training Monitor."""
    
    @pytest.fixture
    def early_stopping_config(self):
        """Create early stopping configuration."""
        return EarlyStoppingConfig(
            patience=2,
            min_delta=0.001,
            monitor='val_loss',
            mode='min'
        )
    
    @pytest.fixture
    def monitor(self, early_stopping_config):
        """Create training monitor."""
        return AdvancedTrainingMonitor("test_session", early_stopping_config)
    
    def test_monitor_initialization(self, monitor):
        """Test monitor initialization."""
        assert monitor.session_id == "test_session"
        assert monitor.early_stopping_config is not None
        assert monitor.progress.session_id == "test_session"
        assert not monitor.is_monitoring
        assert not monitor.should_stop
    
    def test_start_stop_monitoring(self, monitor):
        """Test starting and stopping monitoring."""
        monitor.start_monitoring(total_epochs=5, total_steps=100)
        
        assert monitor.is_monitoring
        assert monitor.progress.total_epochs == 5
        assert monitor.progress.total_steps == 100
        assert monitor.monitoring_thread is not None
        
        monitor.stop_monitoring()
        assert not monitor.is_monitoring
    
    def test_stage_updates(self, monitor):
        """Test training stage updates."""
        callback_called = False
        
        def test_callback(progress):
            nonlocal callback_called
            callback_called = True
            assert progress.stage == TrainingStage.TRAINING
        
        monitor.add_callback(test_callback)
        monitor.update_stage(TrainingStage.TRAINING, 50.0)
        
        assert monitor.progress.stage == TrainingStage.TRAINING
        assert monitor.progress.progress_percentage == 50.0
        assert callback_called
    
    def test_metrics_update(self, monitor):
        """Test metrics updates and early stopping."""
        monitor.start_monitoring(total_epochs=5, total_steps=100)
        
        # First metrics update
        metrics1 = TrainingMetrics(
            epoch=1,
            step=20,
            train_loss=1.0,
            val_loss=0.8,
            train_accuracy=0.6,
            learning_rate=1e-4
        )
        monitor.update_metrics(metrics1)
        
        assert len(monitor.metrics_history) == 1
        assert monitor.best_metric == 0.8
        assert monitor.patience_counter == 0
        
        # Second metrics update (worse)
        metrics2 = TrainingMetrics(
            epoch=2,
            step=40,
            train_loss=0.9,
            val_loss=0.9,  # Worse than previous
            train_accuracy=0.65,
            learning_rate=1e-4
        )
        monitor.update_metrics(metrics2)
        
        assert monitor.patience_counter == 1
        assert not monitor.should_stop
        
        # Third metrics update (still worse - should trigger early stopping)
        metrics3 = TrainingMetrics(
            epoch=3,
            step=60,
            train_loss=0.8,
            val_loss=0.95,  # Even worse
            train_accuracy=0.7,
            learning_rate=1e-4
        )
        monitor.update_metrics(metrics3)
        
        assert monitor.patience_counter == 2
        assert monitor.should_stop  # Should trigger early stopping
        
        monitor.stop_monitoring()
    
    def test_metrics_export(self, monitor):
        """Test metrics export functionality."""
        monitor.start_monitoring(total_epochs=3, total_steps=60)
        
        # Add some metrics
        for i in range(3):
            metrics = TrainingMetrics(
                epoch=i+1,
                step=(i+1)*20,
                train_loss=1.0 - i*0.1,
                val_loss=0.9 - i*0.05,
                train_accuracy=0.5 + i*0.1,
                learning_rate=1e-4
            )
            monitor.update_metrics(metrics)
        
        # Export as JSON
        exported_data = monitor.export_metrics("json")
        assert isinstance(exported_data, str)
        
        # Export as dict
        exported_dict = monitor.export_metrics("dict")
        assert isinstance(exported_dict, dict)
        assert 'session_id' in exported_dict
        assert 'progress' in exported_dict
        assert 'metrics_history' in exported_dict
        
        monitor.stop_monitoring()


class TestIntelligentAssistant:
    """Test suite for Intelligent Assistant."""
    
    @pytest.fixture
    def sample_data(self):
        """Create sample dataset for analysis."""
        np.random.seed(42)
        texts = [
            "This is a medical diagnosis report for patient treatment",
            "Legal contract analysis for court proceedings",
            "Financial investment analysis for market research",
            "Scientific research study on data analysis",
            "Technical software documentation for programming"
        ] * 20
        labels = ['medical', 'legal', 'financial', 'scientific', 'technical'] * 20
        return pd.DataFrame({'text': texts, 'category': labels})
    
    @pytest.fixture
    def assistant(self):
        """Create intelligent assistant."""
        return IntelligentAssistant()
    
    def test_assistant_initialization(self, assistant):
        """Test assistant initialization."""
        assert assistant.knowledge_base is not None
        assert 'model_recommendations' in assistant.knowledge_base
        assert 'hyperparameter_patterns' in assistant.knowledge_base
        assert assistant.performance_history == []
    
    def test_data_characteristics_analysis(self, assistant, sample_data):
        """Test dataset characteristics analysis."""
        characteristics = assistant.analyze_data_characteristics(
            sample_data, 'text', 'category'
        )
        
        assert isinstance(characteristics, DataCharacteristics)
        assert characteristics.num_samples == 100
        assert characteristics.num_classes == 5
        assert characteristics.avg_text_length > 0
        assert characteristics.vocabulary_size > 0
        assert characteristics.unique_tokens_ratio > 0
        assert len(characteristics.domain_indicators) > 0
    
    def test_model_recommendations(self, assistant, sample_data):
        """Test model selection recommendations."""
        characteristics = assistant.analyze_data_characteristics(
            sample_data, 'text', 'category'
        )
        
        recommendations = assistant.get_model_recommendations(characteristics)
        
        assert len(recommendations) > 0
        for rec in recommendations:
            assert rec.type in [RecommendationType.MODEL_SELECTION, RecommendationType.TRAINING_STRATEGY]
            assert rec.confidence > 0
            assert rec.title is not None
            assert rec.description is not None
    
    def test_hyperparameter_recommendations(self, assistant, sample_data):
        """Test hyperparameter tuning recommendations."""
        characteristics = assistant.analyze_data_characteristics(
            sample_data, 'text', 'category'
        )
        
        recommendations = assistant.get_hyperparameter_recommendations(characteristics)
        
        assert len(recommendations) > 0
        for rec in recommendations:
            assert rec.type == RecommendationType.HYPERPARAMETER_TUNING
            assert 'learning_rate' in rec.parameters or 'batch_size' in rec.parameters
    
    def test_performance_optimization_recommendations(self, assistant, sample_data):
        """Test performance optimization recommendations."""
        characteristics = assistant.analyze_data_characteristics(
            sample_data, 'text', 'category'
        )
        
        recommendations = assistant.get_performance_optimization_recommendations(characteristics)
        
        assert len(recommendations) >= 0  # May be empty for small datasets
        for rec in recommendations:
            assert rec.type == RecommendationType.PERFORMANCE_OPTIMIZATION
    
    def test_comprehensive_recommendations(self, assistant, sample_data):
        """Test comprehensive recommendations across all categories."""
        characteristics = assistant.analyze_data_characteristics(
            sample_data, 'text', 'category'
        )
        
        all_recommendations = assistant.get_comprehensive_recommendations(characteristics)
        
        assert isinstance(all_recommendations, dict)
        assert 'model_selection' in all_recommendations
        assert 'hyperparameter_tuning' in all_recommendations
        assert 'performance_optimization' in all_recommendations


class TestLargeDatasetOptimizer:
    """Test suite for Large Dataset Optimizer."""
    
    @pytest.fixture
    def large_dataset_config(self):
        """Create large dataset configuration."""
        return LargeDatasetConfig(
            chunk_size=100,
            batch_size=32,
            max_workers=2,
            memory_limit_gb=1.0,
            enable_streaming=True,
            cache_dir=tempfile.mkdtemp()
        )
    
    @pytest.fixture
    def sample_large_data(self):
        """Create large sample dataset."""
        np.random.seed(42)
        texts = [f"Sample text {i}" for i in range(1000)]
        labels = np.random.choice(['A', 'B', 'C'], 1000)
        return pd.DataFrame({'text': texts, 'label': labels})
    
    @pytest.fixture
    def optimizer(self, large_dataset_config):
        """Create large dataset optimizer."""
        return LargeDatasetOptimizer(large_dataset_config)
    
    def test_optimizer_initialization(self, optimizer):
        """Test optimizer initialization."""
        assert optimizer.config is not None
        assert optimizer.memory_monitor is not None
        assert optimizer.performance_metrics == []
    
    def test_streaming_data_loader(self, optimizer, sample_large_data):
        """Test streaming data loader."""
        # Save data to temporary file
        temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False)
        sample_large_data.to_csv(temp_file.name, index=False)
        temp_file.close()
        
        try:
            data_loader = optimizer.create_data_loader(temp_file.name)
            
            assert data_loader.total_chunks > 0
            
            # Test iteration
            chunk_count = 0
            for chunk in data_loader:
                assert isinstance(chunk, pd.DataFrame)
                assert len(chunk) <= optimizer.config.chunk_size
                chunk_count += 1
                if chunk_count >= 3:  # Test first 3 chunks
                    break
            
            assert chunk_count > 0
            
        finally:
            os.unlink(temp_file.name)
    
    def test_batch_processor(self, optimizer, sample_large_data):
        """Test batch processor."""
        data_loader = optimizer.create_data_loader(sample_large_data)
        
        def mock_process_func(batch):
            return len(batch)  # Return batch size
        
        with optimizer.create_batch_processor() as processor:
            assert processor is not None
            # Test would require async execution in real scenario
    
    def test_memory_monitor(self, large_dataset_config):
        """Test memory monitoring."""
        monitor = MemoryMonitor(large_dataset_config.memory_limit_gb)
        
        usage = monitor.get_current_usage()
        assert 'process_memory_mb' in usage
        assert 'system_memory_percent' in usage
        assert 'available_memory_mb' in usage
        
        # Test cleanup decision
        should_cleanup = monitor.should_cleanup()
        assert isinstance(should_cleanup, bool)
    
    def test_optimization_report(self, optimizer):
        """Test optimization report generation."""
        # Add mock performance metrics
        from app.large_dataset_optimizer import PerformanceMetrics
        
        mock_metrics = PerformanceMetrics(
            processing_time=10.0,
            memory_usage_mb=512.0,
            gpu_memory_mb=1024.0,
            throughput_samples_per_sec=100.0,
            cpu_utilization=75.0,
            gpu_utilization=80.0,
            cache_hit_ratio=0.8,
            io_wait_time=1.0,
            disk_io_mb_per_sec=50.0
        )
        optimizer.performance_metrics.append(mock_metrics)
        
        report = optimizer.get_optimization_report()
        
        assert 'configuration' in report
        assert 'performance' in report
        assert 'recommendations' in report
        assert isinstance(report['recommendations'], list)


@pytest.mark.asyncio
async def test_advanced_features_integration():
    """Integration test for all advanced classification components working together."""
    # Create sample data
    np.random.seed(42)
    texts = [f"Integration test sample {i}" for i in range(100)]
    labels = np.random.choice(['positive', 'negative'], 100)
    df = pd.DataFrame({'text': texts, 'label': labels})
    
    # Test intelligent assistant analysis
    assistant = IntelligentAssistant()
    characteristics = assistant.analyze_data_characteristics(df, 'text', 'label')
    recommendations = assistant.get_comprehensive_recommendations(characteristics)
    
    assert len(recommendations) > 0
    
    # Test large dataset optimization
    large_config = LargeDatasetConfig(chunk_size=50, batch_size=16)
    optimizer = LargeDatasetOptimizer(large_config)
    
    # Test enhanced flat classification engine
    engine = EnhancedFlatClassificationEngine(
        classification_type=ClassificationType.FLAT,
        large_dataset_config=large_config.__dict__
    )
    
    # Test training monitor
    monitor = AdvancedTrainingMonitor("integration_test")
    monitor.start_monitoring(total_epochs=2, total_steps=20)
    
    # Simulate training progress
    for epoch in range(2):
        metrics = TrainingMetrics(
            epoch=epoch + 1,
            step=(epoch + 1) * 10,
            train_loss=1.0 - epoch * 0.2,
            val_loss=0.9 - epoch * 0.1,
            train_accuracy=0.6 + epoch * 0.1,
            learning_rate=1e-4
        )
        monitor.update_metrics(metrics)
    
    monitor.stop_monitoring()
    
    # Verify integration
    assert monitor.progress.current_epoch == 2
    assert len(monitor.metrics_history) == 2
    
    print("Advanced features integration test completed successfully!")


if __name__ == "__main__":
    # Run integration test
    asyncio.run(test_advanced_features_integration())
