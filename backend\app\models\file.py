"""
File-related Pydantic models.
"""
from typing import List
from pydantic import BaseModel

# Import FileInfo from common models to avoid duplication
from .common import FileInfo

class FileListResponse(BaseModel):
    """Response model for listing uploaded files."""
    files: List[FileInfo]


class FileUpdateRequest(BaseModel):
    """Request model for updating file information."""
    filename: str = None
