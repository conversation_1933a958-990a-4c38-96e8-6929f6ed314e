// frontend/src/store/authStore.ts
import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { UserResponse, TokenResponse } from '../types/auth';
import { setAuthHeader } from '../services/apiClient';

interface AuthState {
  // State
  token: string | null;
  refreshToken: string | null;
  tokenExpiry: number | null;
  user: UserResponse | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  isRefreshing: boolean;

  // Actions
  setToken: (token: string | null, refreshToken?: string | null, expiresIn?: number | null) => void;
  setUser: (user: UserResponse | null) => void;
  setError: (error: string | null) => void;
  setLoading: (isLoading: boolean) => void;
  login: (tokenResponse: TokenResponse) => void;
  logout: () => void;
  refreshAccessToken: () => Promise<boolean>;
}

import { refreshToken as refreshTokenApi, logoutUser } from '../services/authApi';

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      // Initial state
      token: null,
      refreshToken: null,
      tokenExpiry: null,
      user: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,
      isRefreshing: false,

      // Actions
      setToken: (token, refreshToken = null, expiresIn = null) => {
        const tokenExpiry = expiresIn ? Date.now() + expiresIn * 1000 : null;
        set({ token, refreshToken, tokenExpiry });
        // Update axios headers using the exported function
        setAuthHeader(token);
      },

      setUser: (user) => set({ user }),

      setError: (error) => set({ error }),

      setLoading: (isLoading) => set({ isLoading }),

      login: (tokenResponse) => {
        const { access_token, refresh_token, expires_in } = tokenResponse;
        console.log('Login successful, setting token and auth state');
        set({
          token: access_token,
          refreshToken: refresh_token || null,
          tokenExpiry: expires_in ? Date.now() + expires_in * 1000 : null,
          isAuthenticated: true,
          error: null
        });
        // Update axios headers using the exported function
        setAuthHeader(access_token);
      },

      logout: async () => {
        try {
          // Call the backend logout endpoint
          if (get().isAuthenticated) {
            await logoutUser();
          }
        } catch (error) {
          console.error('Error during logout:', error);
        } finally {
          console.log('Logging out, clearing auth state');
          // Clear auth state regardless of API call success
          set({
            token: null,
            refreshToken: null,
            tokenExpiry: null,
            user: null,
            isAuthenticated: false
          });
          // Update axios headers using the exported function
          setAuthHeader(null);
        }
      },

      refreshAccessToken: async () => {
        const state = get();

        // Prevent concurrent refresh attempts
        if (state.isRefreshing) {
          console.log('Token refresh already in progress, waiting...');
          // Wait for the current refresh to complete
          while (get().isRefreshing) {
            await new Promise(resolve => setTimeout(resolve, 100));
          }
          return get().isAuthenticated;
        }

        const currentRefreshToken = state.refreshToken;
        if (!currentRefreshToken) {
          console.log('No refresh token available');
          return false;
        }

        try {
          console.log('Attempting to refresh token...');
          set({ isLoading: true, isRefreshing: true });
          const response = await refreshTokenApi(currentRefreshToken);
          const { access_token, refresh_token, expires_in } = response;
          console.log('Token refresh successful');

          set({
            token: access_token,
            refreshToken: refresh_token || null,
            tokenExpiry: expires_in ? Date.now() + expires_in * 1000 : null,
            isAuthenticated: true,
            isLoading: false,
            isRefreshing: false
          });

          // Update axios headers using the exported function
          setAuthHeader(access_token);
          return true;
        } catch (error) {
          console.error('Failed to refresh token:', error);
          set({ isRefreshing: false });
          // If refresh fails, log the user out
          get().logout();
          return false;
        } finally {
          set({ isLoading: false, isRefreshing: false });
        }
      },
    }),
    {
      name: 'auth-storage', // name of the item in localStorage
      partialize: (state) => ({
        token: state.token,
        refreshToken: state.refreshToken,
        tokenExpiry: state.tokenExpiry,
        user: state.user,
        isAuthenticated: state.isAuthenticated
      }), // persist tokens, user info, and authentication state
    }
  )
);

// Removed problematic initialization block.
// AuthProvider is responsible for checking token validity on mount.
// Axios headers are set within setToken/login actions.
