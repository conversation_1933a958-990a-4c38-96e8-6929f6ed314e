"""Advanced Training Monitoring System for ClassyWeb ML Platform.

This module provides comprehensive real-time training progress monitoring with enhanced
metrics visualization, performance tracking, and early stopping capabilities.
"""

import asyncio
import logging
import time
import json
from typing import Dict, List, Any, Optional, Callable, Union
from dataclasses import dataclass, asdict
from datetime import datetime, timezone
import numpy as np
import psutil
import threading
from collections import deque
from enum import Enum

logger = logging.getLogger(__name__)


class TrainingStage(Enum):
    """Training stages for monitoring."""
    INITIALIZATION = "initialization"
    DATA_PREPARATION = "data_preparation"
    MODEL_SETUP = "model_setup"
    TRAINING = "training"
    VALIDATION = "validation"
    EVALUATION = "evaluation"
    SAVING = "saving"
    COMPLETED = "completed"
    FAILED = "failed"


@dataclass
class TrainingMetrics:
    """Comprehensive training metrics."""
    epoch: int
    step: int
    train_loss: float
    val_loss: Optional[float] = None
    train_accuracy: Optional[float] = None
    val_accuracy: Optional[float] = None
    learning_rate: float = 0.0
    gpu_memory_used: float = 0.0
    gpu_utilization: float = 0.0
    cpu_usage: float = 0.0
    memory_usage: float = 0.0
    processing_speed: float = 0.0  # samples per second
    estimated_time_remaining: Optional[float] = None
    timestamp: str = ""
    
    def __post_init__(self):
        if not self.timestamp:
            self.timestamp = datetime.now(timezone.utc).isoformat()


@dataclass
class EarlyStoppingConfig:
    """Configuration for early stopping."""
    patience: int = 3
    min_delta: float = 0.001
    monitor: str = "val_loss"
    mode: str = "min"  # 'min' or 'max'
    restore_best_weights: bool = True
    baseline: Optional[float] = None


@dataclass
class TrainingProgress:
    """Overall training progress information."""
    session_id: str
    stage: TrainingStage
    progress_percentage: float
    current_epoch: int
    total_epochs: int
    current_step: int
    total_steps: int
    start_time: str
    elapsed_time: float
    estimated_total_time: Optional[float] = None
    metrics_history: List[TrainingMetrics] = None
    early_stopping_triggered: bool = False
    best_metric_value: Optional[float] = None
    best_epoch: Optional[int] = None
    
    def __post_init__(self):
        if self.metrics_history is None:
            self.metrics_history = []


class AdvancedTrainingMonitor:
    """Advanced training monitor with real-time tracking and early stopping."""
    
    def __init__(self, session_id: str, early_stopping_config: Optional[EarlyStoppingConfig] = None):
        self.session_id = session_id
        self.early_stopping_config = early_stopping_config or EarlyStoppingConfig()
        
        # Progress tracking
        self.progress = TrainingProgress(
            session_id=session_id,
            stage=TrainingStage.INITIALIZATION,
            progress_percentage=0.0,
            current_epoch=0,
            total_epochs=0,
            current_step=0,
            total_steps=0,
            start_time=datetime.now(timezone.utc).isoformat(),
            elapsed_time=0.0
        )
        
        # Metrics tracking
        self.metrics_history = deque(maxlen=1000)  # Keep last 1000 metrics
        self.performance_history = deque(maxlen=100)  # System performance
        
        # Early stopping state
        self.best_metric = None
        self.patience_counter = 0
        self.should_stop = False
        
        # Monitoring state
        self.is_monitoring = False
        self.monitoring_thread = None
        self.callbacks = []
        
        # System monitoring
        self.process = psutil.Process()
        self.start_time = time.time()
        
    def add_callback(self, callback: Callable[[TrainingProgress], None]):
        """Add a callback function to be called on progress updates."""
        self.callbacks.append(callback)
    
    def start_monitoring(self, total_epochs: int, total_steps: int):
        """Start the monitoring process."""
        self.progress.total_epochs = total_epochs
        self.progress.total_steps = total_steps
        self.progress.stage = TrainingStage.INITIALIZATION
        self.is_monitoring = True
        
        # Start background monitoring thread
        self.monitoring_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
        self.monitoring_thread.start()
        
        logger.info(f"Started training monitoring for session {self.session_id}")
    
    def stop_monitoring(self):
        """Stop the monitoring process."""
        self.is_monitoring = False
        if self.monitoring_thread:
            self.monitoring_thread.join(timeout=1.0)
        
        logger.info(f"Stopped training monitoring for session {self.session_id}")
    
    def update_stage(self, stage: TrainingStage, progress_percentage: float = None):
        """Update the current training stage."""
        self.progress.stage = stage
        if progress_percentage is not None:
            self.progress.progress_percentage = progress_percentage
        
        self._notify_callbacks()
    
    def update_metrics(self, metrics: TrainingMetrics):
        """Update training metrics and check early stopping."""
        # Add system metrics
        metrics.cpu_usage = self.process.cpu_percent()
        metrics.memory_usage = self.process.memory_info().rss / 1024 / 1024  # MB
        
        # Add GPU metrics if available
        try:
            gpu_metrics = self._get_gpu_metrics()
            metrics.gpu_memory_used = gpu_metrics.get('memory_used', 0.0)
            metrics.gpu_utilization = gpu_metrics.get('utilization', 0.0)
        except Exception as e:
            logger.debug(f"Could not get GPU metrics: {e}")
        
        # Calculate processing speed
        if len(self.metrics_history) > 0:
            prev_metrics = self.metrics_history[-1]
            time_diff = time.time() - time.mktime(time.strptime(prev_metrics.timestamp[:19], "%Y-%m-%dT%H:%M:%S"))
            if time_diff > 0:
                step_diff = metrics.step - prev_metrics.step
                metrics.processing_speed = step_diff / time_diff
        
        # Update progress
        self.progress.current_epoch = metrics.epoch
        self.progress.current_step = metrics.step
        self.progress.elapsed_time = time.time() - self.start_time
        
        # Calculate progress percentage
        if self.progress.total_steps > 0:
            self.progress.progress_percentage = (metrics.step / self.progress.total_steps) * 100
        elif self.progress.total_epochs > 0:
            self.progress.progress_percentage = (metrics.epoch / self.progress.total_epochs) * 100
        
        # Estimate remaining time
        if self.progress.progress_percentage > 0:
            estimated_total = self.progress.elapsed_time / (self.progress.progress_percentage / 100)
            self.progress.estimated_total_time = estimated_total
            metrics.estimated_time_remaining = estimated_total - self.progress.elapsed_time
        
        # Add to history
        self.metrics_history.append(metrics)
        self.progress.metrics_history = list(self.metrics_history)[-10:]  # Keep last 10 for progress
        
        # Check early stopping
        self._check_early_stopping(metrics)
        
        # Notify callbacks
        self._notify_callbacks()
    
    def _check_early_stopping(self, metrics: TrainingMetrics):
        """Check if early stopping should be triggered."""
        if not self.early_stopping_config:
            return
        
        # Get the metric to monitor
        monitor_value = None
        if self.early_stopping_config.monitor == "val_loss":
            monitor_value = metrics.val_loss
        elif self.early_stopping_config.monitor == "val_accuracy":
            monitor_value = metrics.val_accuracy
        elif self.early_stopping_config.monitor == "train_loss":
            monitor_value = metrics.train_loss
        elif self.early_stopping_config.monitor == "train_accuracy":
            monitor_value = metrics.train_accuracy
        
        if monitor_value is None:
            return
        
        # Check if this is the best metric so far
        is_better = False
        if self.best_metric is None:
            is_better = True
        else:
            if self.early_stopping_config.mode == "min":
                is_better = monitor_value < (self.best_metric - self.early_stopping_config.min_delta)
            else:  # mode == "max"
                is_better = monitor_value > (self.best_metric + self.early_stopping_config.min_delta)
        
        if is_better:
            self.best_metric = monitor_value
            self.progress.best_metric_value = monitor_value
            self.progress.best_epoch = metrics.epoch
            self.patience_counter = 0
        else:
            self.patience_counter += 1
        
        # Check if we should stop
        if self.patience_counter >= self.early_stopping_config.patience:
            self.should_stop = True
            self.progress.early_stopping_triggered = True
            logger.info(f"Early stopping triggered at epoch {metrics.epoch}")
    
    def _monitoring_loop(self):
        """Background monitoring loop for system metrics."""
        while self.is_monitoring:
            try:
                # Collect system performance metrics
                cpu_percent = self.process.cpu_percent()
                memory_info = self.process.memory_info()
                memory_mb = memory_info.rss / 1024 / 1024
                
                performance_metric = {
                    'timestamp': datetime.now(timezone.utc).isoformat(),
                    'cpu_percent': cpu_percent,
                    'memory_mb': memory_mb,
                    'memory_percent': self.process.memory_percent()
                }
                
                self.performance_history.append(performance_metric)
                
                # Sleep for 1 second
                time.sleep(1.0)
                
            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
                time.sleep(1.0)
    
    def _get_gpu_metrics(self) -> Dict[str, float]:
        """Get GPU utilization metrics."""
        try:
            import pynvml
            pynvml.nvmlInit()
            
            handle = pynvml.nvmlDeviceGetHandleByIndex(0)  # First GPU
            memory_info = pynvml.nvmlDeviceGetMemoryInfo(handle)
            utilization = pynvml.nvmlDeviceGetUtilizationRates(handle)
            
            return {
                'memory_used': memory_info.used / 1024 / 1024,  # MB
                'memory_total': memory_info.total / 1024 / 1024,  # MB
                'utilization': utilization.gpu
            }
        except Exception:
            return {}
    
    def _notify_callbacks(self):
        """Notify all registered callbacks of progress updates."""
        for callback in self.callbacks:
            try:
                callback(self.progress)
            except Exception as e:
                logger.error(f"Error in callback: {e}")
    
    def get_current_progress(self) -> TrainingProgress:
        """Get the current training progress."""
        return self.progress
    
    def get_metrics_summary(self) -> Dict[str, Any]:
        """Get a summary of training metrics."""
        if not self.metrics_history:
            return {}
        
        recent_metrics = list(self.metrics_history)[-10:]  # Last 10 metrics
        
        return {
            'total_metrics': len(self.metrics_history),
            'current_epoch': self.progress.current_epoch,
            'current_step': self.progress.current_step,
            'progress_percentage': self.progress.progress_percentage,
            'elapsed_time': self.progress.elapsed_time,
            'estimated_total_time': self.progress.estimated_total_time,
            'early_stopping_triggered': self.progress.early_stopping_triggered,
            'best_metric_value': self.progress.best_metric_value,
            'best_epoch': self.progress.best_epoch,
            'recent_train_loss': [m.train_loss for m in recent_metrics if m.train_loss is not None],
            'recent_val_loss': [m.val_loss for m in recent_metrics if m.val_loss is not None],
            'recent_accuracy': [m.train_accuracy for m in recent_metrics if m.train_accuracy is not None],
            'avg_processing_speed': np.mean([m.processing_speed for m in recent_metrics if m.processing_speed > 0]) if recent_metrics else 0,
            'avg_gpu_utilization': np.mean([m.gpu_utilization for m in recent_metrics if m.gpu_utilization > 0]) if recent_metrics else 0,
            'avg_memory_usage': np.mean([m.memory_usage for m in recent_metrics if m.memory_usage > 0]) if recent_metrics else 0
        }
    
    def export_metrics(self, format: str = "json") -> Union[str, Dict[str, Any]]:
        """Export training metrics in specified format."""
        data = {
            'session_id': self.session_id,
            'progress': asdict(self.progress),
            'metrics_history': [asdict(m) for m in self.metrics_history],
            'performance_history': list(self.performance_history),
            'early_stopping_config': asdict(self.early_stopping_config) if self.early_stopping_config else None
        }
        
        if format == "json":
            return json.dumps(data, indent=2)
        else:
            return data
