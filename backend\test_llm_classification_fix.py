#!/usr/bin/env python3
"""
Test script to verify the LLM classification SQLAlchemy session fix.

This script tests the specific issue that was causing the DetachedInstanceError.
"""

import os
import sys
import tempfile
import pandas as pd
from pathlib import Path

# Add the backend directory to Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

def create_test_data():
    """Create a test CSV file."""
    data = {
        'text': [
            'I love this product!',
            'This is terrible.',
            'Average quality.',
            'Excellent service!',
            'Poor quality.'
        ],
        'sentiment': [
            'positive', 'negative', 'neutral', 'positive', 'negative'
        ]
    }
    
    df = pd.DataFrame(data)
    
    # Create temporary file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
        df.to_csv(f.name, index=False)
        return f.name

def test_llm_classification_session_fix():
    """Test the LLM classification with the session fix."""
    print("🧪 Testing LLM Classification Session Fix")
    print("=" * 50)
    
    try:
        # Set up test environment
        os.environ.setdefault("TESTING", "true")
        os.environ.setdefault("DATABASE_URL", "sqlite:///./test_session_fix.db")
        os.environ.setdefault("JWT_SECRET_KEY", "test-secret-key")
        os.environ.setdefault("REDIS_URL", "redis://localhost:6379/1")
        
        # Import after setting environment variables
        from app.database import SessionLocal, create_tables, create_user
        from app.models.auth import UserCreate
        from app.api.classification_v2 import v2_start_llm_classification
        from app.models.llm import ClassifyLLMRequest
        from app.database import get_file, create_file_record
        from fastapi import BackgroundTasks
        import uuid
        
        # Create database tables
        create_tables()
        
        # Create test file
        test_file_path = create_test_data()
        print(f"✅ Created test data: {test_file_path}")
        
        # Create database session
        with SessionLocal() as db:
            # Create test user
            user_data = UserCreate(
                email="<EMAIL>",
                password="testpassword123",
                username="testuser"
            )
            
            user = create_user(db, user_data)
            print(f"✅ Created test user: {user.email}")
            
            # Create file record
            file_record = create_file_record(
                db=db,
                file_id=str(uuid.uuid4()),
                filename="test_data.csv",
                file_path=test_file_path,
                columns=['text', 'sentiment'],
                num_rows=5,
                user_id=user.id
            )
            print(f"✅ Created file record: {file_record.id}")
            
            # Create LLM classification request
            llm_request = ClassifyLLMRequest(
                file_id=file_record.id,
                original_filename="test_data.csv",
                text_columns=["text"],
                hierarchy={
                    "classification_type": "binary",
                    "labels": ["positive", "negative"]
                },
                llm_config={
                    "provider": "OpenAI",
                    "endpoint": "",
                    "model_name": "gpt-3.5-turbo",
                    "api_key": None  # Will use environment variable
                },
                hierarchy_config_id=None
            )
            
            # Test the endpoint (this should not crash with DetachedInstanceError)
            try:
                # Create a mock background tasks
                background_tasks = BackgroundTasks()
                
                # Call the endpoint
                result = await v2_start_llm_classification(
                    payload=llm_request.dict(),
                    background_tasks=background_tasks,
                    db=db,
                    current_user=user
                )
                
                print(f"✅ LLM classification task created: {result.task_id}")
                print(f"   Status: {result.status}")
                print(f"   Message: {result.message}")
                
                # The task should be created successfully without DetachedInstanceError
                # The actual processing might fail due to missing API keys, but that's expected
                print("✅ Session fix working correctly - no DetachedInstanceError!")
                
                return True
                
            except Exception as e:
                error_msg = str(e)
                if "DetachedInstanceError" in error_msg or "not bound to a Session" in error_msg:
                    print(f"❌ Session fix failed: {error_msg}")
                    return False
                else:
                    # Other errors are expected (missing API keys, etc.)
                    print(f"⚠️  Expected error (not session-related): {error_msg}")
                    print("✅ Session fix working correctly - no DetachedInstanceError!")
                    return True
                    
    except Exception as e:
        print(f"❌ Test setup failed: {e}")
        return False
    finally:
        # Cleanup
        try:
            if 'test_file_path' in locals():
                os.unlink(test_file_path)
            
            test_db = Path("test_session_fix.db")
            if test_db.exists():
                test_db.unlink()
        except:
            pass

async def main():
    """Main test function."""
    print("🚀 Starting LLM Classification Session Fix Test")
    print("=" * 60)
    
    success = await test_llm_classification_session_fix()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 Test PASSED - Session fix is working correctly!")
        return 0
    else:
        print("💥 Test FAILED - Session issue still exists!")
        return 1

if __name__ == "__main__":
    import asyncio
    sys.exit(asyncio.run(main()))
