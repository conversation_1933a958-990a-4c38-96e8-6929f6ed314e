# AI Text Classifier API - App Module

This directory contains the refactored application structure for the AI Text Classifier API.

## Directory Structure

```
app/
├── __init__.py
├── main.py               # FastAPI application
├── config.py             # Application configuration
├── retry.py              # Retry utilities
├── models/               # Pydantic models
│   ├── __init__.py
│   ├── auth.py           # Auth-related models
│   ├── file.py           # File-related models
│   ├── task.py           # Task-related models
│   ├── llm.py            # LLM-related models
│   └── hf.py             # HF-related models
├── api/                  # API routes
│   ├── __init__.py
│   ├── auth.py           # Auth routes
│   ├── files.py          # File management routes
│   ├── tasks.py          # Task management routes
│   ├── llm.py            # LLM classification routes
│   └── hf.py             # HF model routes
├── tasks/                # Background tasks
│   ├── __init__.py
│   ├── llm.py            # LLM classification tasks
│   └── hf.py             # HF training and classification tasks
└── utils/                # Utility functions
    ├── __init__.py
    └── helpers.py        # Common helper functions
```

## Module Descriptions

### main.py

The main FastAPI application. It sets up the application, middleware, and includes the API routers.

### config.py

Application configuration. It imports settings from the legacy `config.py` file and adds new settings.

### retry.py

Utilities for retrying operations that might fail transiently.

### models/

Pydantic models for request/response validation.

### api/

API route handlers organized by functionality.

### tasks/

Background tasks for long-running operations.

### utils/

Helper functions for common operations.

## How to Use

### Environment Variables

The application uses environment variables for configuration. Create a `.env` file in the `backend` directory with your API keys and configuration. See `.env.example` for a template.

### Running the Application

The application is designed to be imported and run from the `server.py` file in the backend directory:

```python
# server.py
import os
import sys
import argparse
import logging
import uvicorn

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Run the ClassyWeb application server")
    parser.add_argument("--host", default="127.0.0.1", help="Host to bind to (default: 127.0.0.1)")
    parser.add_argument("--port", type=int, default=8000, help="Port to bind to (default: 8000)")
    parser.add_argument("--reload", action="store_true", help="Enable auto-reload on code changes")
    parser.add_argument("--workers", type=int, default=1, help="Number of worker processes (default: 1)")
    parser.add_argument("--log-level", default="info", help="Logging level (default: info)")
    return parser.parse_args()

def main():
    """Main entry point for the server."""
    args = parse_args()

    # Run the server
    uvicorn.run(
        "app.main:app",
        host=args.host,
        port=args.port,
        reload=args.reload,
        workers=args.workers,
        log_level=args.log_level
    )

if __name__ == "__main__":
    main()
```

## Adding New Features

To add a new feature:

1. Add Pydantic models to the appropriate file in `models/`
2. Add API routes to the appropriate file in `api/`
3. Add background tasks to the appropriate file in `tasks/`
4. Add helper functions to `utils/helpers.py`

## Testing

The application can be tested using pytest. Tests should be placed in a `tests/` directory at the same level as the `app/` directory.
