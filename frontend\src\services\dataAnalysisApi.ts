// frontend/src/services/dataAnalysisApi.ts
// API service for smart data structure detection and analysis

import apiClient from './apiClient';

export interface DataStructureSuggestion {
  structure_type: 'hierarchical' | 'flat' | 'mixed' | 'unclear';
  confidence: number;
  reasoning: string;
  suggested_config: {
    workflow_type: string;
    classification_type?: string;
    hierarchy_levels?: string[];
    text_column?: string;
    label_columns?: string[];
    estimated_labels?: string[];
    total_unique_labels?: number;
    recommended_approach?: 'LLM' | 'HF';
  };
  preview_data: {
    sample_hierarchy?: Record<string, string[]>;
    sample_labels?: string[];
    multi_label_examples?: string[];
    label_distribution?: Record<string, number>;
    estimated_complexity?: number;
    data_size?: number;
    note?: string;
  };
}

export interface SmartDataAnalysis {
  detected_structure: 'hierarchical' | 'flat' | 'mixed' | 'unclear';
  confidence: number;
  suggestions: DataStructureSuggestion[];
  preview: {
    sample_rows: Record<string, any>[];
    text_column?: string;
    label_columns: string[];
    total_rows: number;
    total_columns: number;
    text_samples?: string[];
  };
  column_analysis: Record<string, {
    type: string;
    unique_count: number;
    total_count: number;
    uniqueness_ratio: number;
    sample_values: any[];
    is_potential_text: boolean;
    is_potential_label: boolean;
  }>;
  data_quality: {
    total_rows: number;
    total_columns: number;
    missing_data_percentage: number;
    duplicate_rows: number;
    empty_rows: number;
    data_types: Record<string, string>;
  };
}

/**
 * Analyze the structure of an uploaded file to detect optimal classification approach
 */
export const analyzeFileStructure = async (
  fileId: string,
  textColumn?: string
): Promise<SmartDataAnalysis> => {
  try {
    const data = textColumn ? { text_column: textColumn } : {};
    const response = await apiClient.post(`/files/${fileId}/analyze`, data);
    return response.data;
  } catch (error: any) {
    console.error('Error analyzing file structure:', error);

    // Handle specific error cases
    if (error.response?.status === 404) {
      throw new Error('File not found. Please upload the file again.');
    } else if (error.response?.status === 429) {
      throw new Error('Too many requests. Please wait a moment and try again.');
    } else if (error.response?.status >= 500) {
      throw new Error('Server error. Please try again later.');
    }

    throw new Error(error.response?.data?.detail || 'Failed to analyze file structure');
  }
};

/**
 * Get recommendations for workflow configuration based on analysis
 */
export const getWorkflowRecommendations = async (
  analysis: SmartDataAnalysis
): Promise<{
  recommended_workflow: 'LLM' | 'HF' | 'NonHierarchical';
  reasoning: string;
  configuration_hints: string[];
}> => {
  try {
    const response = await apiClient.post('/analysis/workflow-recommendations', {
      analysis
    });
    return response.data;
  } catch (error: any) {
    console.error('Error getting workflow recommendations:', error);
    throw new Error(error.response?.data?.detail || 'Failed to get workflow recommendations');
  }
};

/**
 * Validate data structure configuration
 */
export const validateDataConfiguration = async (
  fileId: string,
  configuration: {
    workflow_type: 'hierarchical' | 'flat';
    text_column: string;
    label_columns: string[];
    hierarchy_levels?: string[];
  }
): Promise<{
  is_valid: boolean;
  warnings: string[];
  suggestions: string[];
  estimated_performance: {
    accuracy_estimate: number;
    training_time_estimate: string;
    complexity_score: number;
  };
}> => {
  try {
    const response = await apiClient.post(`/files/${fileId}/validate-configuration`, configuration);
    return response.data;
  } catch (error: any) {
    console.error('Error validating data configuration:', error);
    throw new Error(error.response?.data?.detail || 'Failed to validate configuration');
  }
};

/**
 * Get smart column mapping suggestions
 */
export const getColumnMappingSuggestions = async (
  fileId: string,
  targetStructure: 'hierarchical' | 'flat'
): Promise<{
  text_column_suggestions: Array<{
    column: string;
    confidence: number;
    reasoning: string;
  }>;
  label_column_suggestions: Array<{
    column: string;
    suggested_role: string;
    confidence: number;
    reasoning: string;
  }>;
  hierarchy_suggestions?: Array<{
    level_name: string;
    suggested_columns: string[];
    confidence: number;
  }>;
}> => {
  try {
    const response = await apiClient.post(`/files/${fileId}/column-mapping-suggestions`, {
      target_structure: targetStructure
    });
    return response.data;
  } catch (error: any) {
    console.error('Error getting column mapping suggestions:', error);
    throw new Error(error.response?.data?.detail || 'Failed to get column mapping suggestions');
  }
};

/**
 * Preview classification results with current configuration
 */
export const previewClassificationResults = async (
  fileId: string,
  configuration: {
    workflow_type: 'hierarchical' | 'flat';
    text_column: string;
    label_columns: string[];
    hierarchy_levels?: string[];
  },
  sampleSize: number = 10
): Promise<{
  preview_results: Array<{
    text: string;
    predicted_labels: string[] | Record<string, string>;
    confidence_scores: Record<string, number>;
  }>;
  performance_estimate: {
    estimated_accuracy: number;
    potential_issues: string[];
    recommendations: string[];
  };
}> => {
  try {
    const response = await apiClient.post(`/files/${fileId}/preview-classification`, {
      configuration,
      sample_size: sampleSize
    });
    return response.data;
  } catch (error: any) {
    console.error('Error previewing classification results:', error);
    throw new Error(error.response?.data?.detail || 'Failed to preview classification results');
  }
};
